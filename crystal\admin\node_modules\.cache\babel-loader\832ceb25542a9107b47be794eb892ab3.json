{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\Category\\list.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\Category\\list.vue", "mtime": 1753666157756}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\"use strict\";\n\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar categoryApi = _interopRequireWildcard(require(\"@/api/categoryApi.js\"));\nvar _info = _interopRequireDefault(require(\"./info\"));\nvar _edit = _interopRequireDefault(require(\"./edit\"));\nvar selfUtil = _interopRequireWildcard(require(\"@/utils/ZBKJIutil.js\"));\nvar _permission = require(\"@/utils/permission\");\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != _typeof(e) && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  // name: \"list\"\n  components: {\n    info: _info.default,\n    edit: _edit.default\n  },\n  props: {\n    biztype: {\n      // 类型，1 产品分类，2 附件分类，3 文章分类， 4 设置分类， 5 菜单分类， 6 配置分类， 7 秒杀配置， 8 手串分类\n      type: Object,\n      default: {\n        value: -1\n      },\n      validator: function validator(obj) {\n        return obj.value > 0;\n      }\n    },\n    pid: {\n      type: Number,\n      default: 0,\n      validator: function validator(value) {\n        return value >= 0;\n      }\n    },\n    selectModel: {\n      // 是否选择模式\n      type: Boolean,\n      default: false\n    },\n    selectModelKeys: {\n      type: Array\n    },\n    rowSelect: {}\n  },\n  data: function data() {\n    return {\n      selectModelKeysNew: this.selectModelKeys,\n      loading: false,\n      constants: this.$constants,\n      treeProps: {\n        label: 'name',\n        children: 'child'\n        // expandTrigger: 'hover',\n        // checkStrictly: false,\n        // emitPath: false\n      },\n      // treeCheckedKeys:[],// 选择模式下的属性结构默认选中\n      multipleSelection: [],\n      editDialogConfig: {\n        visible: false,\n        isCreate: 0,\n        // 0=创建，1=编辑\n        prent: {},\n        // 父级对象\n        data: {},\n        biztype: this.biztype // 统一主业务中的目录类型\n      },\n      dataList: [],\n      treeList: [],\n      listPram: {\n        pid: this.pid,\n        type: this.biztype.value,\n        status: -1,\n        name: '',\n        page: this.$constants.page.page,\n        limit: this.$constants.page.limit[0]\n      },\n      viewInfoConfig: {\n        data: null,\n        visible: false\n      },\n      defaultImg: require('@/assets/imgs/moren.jpg')\n    };\n  },\n  mounted: function mounted() {\n    /* if(this.biztype.value === 3){\r\n       this.listPram.pageSize = constants.page.pageSize[4]\r\n       this.handlerGetList()\r\n     }else{*/\n    this.handlerGetTreeList();\n    // }\n  },\n  methods: {\n    checkPermi: _permission.checkPermi,\n    //权限控制\n    onchangeIsShow: function onchangeIsShow(row) {\n      var _this = this;\n      categoryApi.categroyUpdateStatus(row.id).then(function () {\n        _this.$message.success('修改成功');\n        _this.handlerGetTreeList();\n      }).catch(function () {\n        row.status = !row.status;\n      });\n    },\n    handleEditMenu: function handleEditMenu(rowData) {\n      this.editDialogConfig.isCreate = 1;\n      this.editDialogConfig.data = rowData;\n      this.editDialogConfig.prent = rowData;\n      this.editDialogConfig.visible = true;\n    },\n    handleAddMenu: function handleAddMenu(rowData) {\n      this.editDialogConfig.isCreate = 0;\n      this.editDialogConfig.prent = rowData;\n      this.editDialogConfig.data = {};\n      this.editDialogConfig.biztype = this.biztype;\n      this.editDialogConfig.visible = true;\n    },\n    getCurrentNode: function getCurrentNode(data) {\n      var node = this.$refs.tree.getNode(data);\n      this.childNodes(node);\n      // this.parentNodes(node);\n      //是否编辑的表示\n      // this.ruleForm.isEditorFlag = true;\n      //编辑时候使用\n      this.$emit('rulesSelect', this.$refs.tree.getCheckedKeys());\n      // this.selectModelKeys = this.$refs.tree.getCheckedKeys();\n      //无论编辑和新增点击了就传到后台这个值\n      // this.$emit('rulesSelect', this.$refs.tree.getCheckedKeys().concat(this.$refs.tree.getHalfCheckedKeys()));\n      // this.ruleForm.menuIdsisEditor = this.$refs.tree.getCheckedKeys().concat(this.$refs.tree.getHalfCheckedKeys());\n    },\n    //具体方法可以看element官网api\n    childNodes: function childNodes(node) {\n      var len = node.childNodes.length;\n      for (var i = 0; i < len; i++) {\n        node.childNodes[i].checked = node.checked;\n        this.childNodes(node.childNodes[i]);\n      }\n    },\n    parentNodes: function parentNodes(node) {\n      if (node.parent) {\n        for (var key in node) {\n          if (key == \"parent\") {\n            node[key].checked = true;\n            this.parentNodes(node[key]);\n          }\n        }\n      }\n    },\n    handleDelMenu: function handleDelMenu(rowData) {\n      var _this2 = this;\n      this.$confirm('确定删除当前数据?').then(function () {\n        categoryApi.deleteCategroy(rowData).then(function (data) {\n          _this2.handlerGetTreeList();\n          _this2.$message.success('删除成功');\n        });\n      });\n    },\n    handlerGetList: function handlerGetList() {\n      this.handlerGetTreeList();\n      // categoryApi.listCategroy({status:this.listPram.status, type: 1 }).then(data => {\n      //   this.treeList = data.list\n      // })\n    },\n    handlerGetTreeList: function handlerGetTreeList() {\n      var _this3 = this;\n      // this.biztype.value === 5 && !this.selectModel) ?  -1 : 1\n      // const _pram = { type: this.biztype.value, status: !this.selectModel ? -1 : (this.biztype.value === 5 ? -1 : 1) }\n      var _pram = {\n        type: this.biztype.value,\n        status: this.listPram.status,\n        name: this.listPram.name\n      };\n      this.loading = true;\n      this.biztype.value !== 3 ? categoryApi.treeCategroy(_pram).then(function (data) {\n        _this3.treeList = _this3.handleAddArrt(data);\n        _this3.loading = false;\n      }).catch(function () {\n        _this3.loading = false;\n      }) : categoryApi.listCategroy({\n        type: 3,\n        status: this.listPram.status,\n        pid: this.listPram.pid,\n        name: this.listPram.name\n      }).then(function (data) {\n        _this3.treeList = data.list;\n      });\n    },\n    handlerGetInfo: function handlerGetInfo(id) {\n      this.viewInfoConfig.data = id;\n      this.viewInfoConfig.visible = true;\n    },\n    handleNodeClick: function handleNodeClick(data) {\n      console.log('data:', data);\n    },\n    handleAddArrt: function handleAddArrt(treeData) {\n      var _result = selfUtil.addTreeListLabel(treeData);\n      return _result;\n    },\n    hideEditDialog: function hideEditDialog() {\n      var _this4 = this;\n      setTimeout(function () {\n        _this4.editDialogConfig.prent = {};\n        _this4.editDialogConfig.type = 0;\n        _this4.editDialogConfig.visible = false;\n        _this4.handlerGetTreeList();\n      }, 200);\n    },\n    handleSelectionChange: function handleSelectionChange(d1, _ref) {\n      var checkedNodes = _ref.checkedNodes,\n        checkedKeys = _ref.checkedKeys,\n        halfCheckedNodes = _ref.halfCheckedNodes,\n        halfCheckedKeys = _ref.halfCheckedKeys;\n      // this.multipleSelection =  checkedKeys.concat(halfCheckedKeys)\n      this.multipleSelection = checkedKeys;\n      this.$emit('rulesSelect', this.multipleSelection);\n    }\n  }\n};", null]}