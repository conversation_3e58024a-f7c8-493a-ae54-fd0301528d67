(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/bracelets/axureIndex"],{

/***/ 128:
/*!*******************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/main.js?{"page":"pages%2Fbracelets%2FaxureIndex"} ***!
  \*******************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _axureIndex = _interopRequireDefault(__webpack_require__(/*! ./pages/bracelets/axureIndex.vue */ 129));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_axureIndex.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 129:
/*!************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/bracelets/axureIndex.vue ***!
  \************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _axureIndex_vue_vue_type_template_id_a9834dc4___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./axureIndex.vue?vue&type=template&id=a9834dc4& */ 130);
/* harmony import */ var _axureIndex_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./axureIndex.vue?vue&type=script&lang=js& */ 132);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _axureIndex_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _axureIndex_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _axureIndex_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./axureIndex.vue?vue&type=style&index=0&lang=scss& */ 136);
/* harmony import */ var _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 51);

var renderjs





/* normalize component */

var component = Object(_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _axureIndex_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _axureIndex_vue_vue_type_template_id_a9834dc4___WEBPACK_IMPORTED_MODULE_0__["render"],
  _axureIndex_vue_vue_type_template_id_a9834dc4___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  null,
  null,
  false,
  _axureIndex_vue_vue_type_template_id_a9834dc4___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/bracelets/axureIndex.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 130:
/*!*******************************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/bracelets/axureIndex.vue?vue&type=template&id=a9834dc4& ***!
  \*******************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_axureIndex_vue_vue_type_template_id_a9834dc4___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./axureIndex.vue?vue&type=template&id=a9834dc4& */ 131);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_axureIndex_vue_vue_type_template_id_a9834dc4___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_axureIndex_vue_vue_type_template_id_a9834dc4___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_axureIndex_vue_vue_type_template_id_a9834dc4___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_axureIndex_vue_vue_type_template_id_a9834dc4___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 131:
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/bracelets/axureIndex.vue?vue&type=template&id=a9834dc4& ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    mpHtml: function () {
      return Promise.all(/*! import() | uni_modules/mp-html/components/mp-html/mp-html */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/mp-html/components/mp-html/mp-html")]).then(__webpack_require__.bind(null, /*! @/uni_modules/mp-html/components/mp-html/mp-html.vue */ 778))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var g0 = _vm.steps.length
  var l0 =
    _vm.currentStep === 0
      ? _vm.__map(_vm.colorOptions, function (color, index) {
          var $orig = _vm.__get_orig(color)
          var g1 = _vm.formData.colorPreference.includes(color.value)
          return {
            $orig: $orig,
            g1: g1,
          }
        })
      : null
  var l1 =
    _vm.currentStep === 0
      ? _vm.__map(_vm.materialOptions, function (item, index) {
          var $orig = _vm.__get_orig(item)
          var g2 = _vm.formData.materials.includes(item.value)
          return {
            $orig: $orig,
            g2: g2,
          }
        })
      : null
  var l2 =
    _vm.currentStep === 0
      ? _vm.__map(_vm.chakraOptions, function (chakra, index) {
          var $orig = _vm.__get_orig(chakra)
          var g3 = _vm.formData.chakraNeeds.includes(chakra.value)
          return {
            $orig: $orig,
            g3: g3,
          }
        })
      : null
  var l3 =
    _vm.currentStep === 2
      ? _vm.__map(_vm.beadsList, function (item, index) {
          var $orig = _vm.__get_orig(item)
          var s0 = _vm.__get_style([_vm.getMarbleStyle(index)])
          return {
            $orig: $orig,
            s0: s0,
          }
        })
      : null
  var g4 = _vm.currentStep === 3 ? _vm.beadsList.length : null
  var l4 =
    _vm.currentStep === 3
      ? _vm.__map(_vm.beadsList, function (item, index) {
          var $orig = _vm.__get_orig(item)
          var s1 = _vm.__get_style([_vm.getMarbleStyle(index)])
          return {
            $orig: $orig,
            s1: s1,
          }
        })
      : null
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        g0: g0,
        l0: l0,
        l1: l1,
        l2: l2,
        l3: l3,
        g4: g4,
        l4: l4,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 132:
/*!*************************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/bracelets/axureIndex.vue?vue&type=script&lang=js& ***!
  \*************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_axureIndex_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./axureIndex.vue?vue&type=script&lang=js& */ 133);
/* harmony import */ var _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_axureIndex_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_axureIndex_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_axureIndex_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_axureIndex_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_axureIndex_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 133:
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/bracelets/axureIndex.vue?vue&type=script&lang=js& ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _login = __webpack_require__(/*! @/libs/login.js */ 33);
var _requirement = __webpack_require__(/*! @/api/requirement.js */ 134);
var _marked = __webpack_require__(/*! @/utils/marked.js */ 135);
var _vuex = __webpack_require__(/*! vuex */ 35);
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var _default = {
  data: function data() {
    return {
      currentStep: 0,
      steps: ['需求收集', 'AI生成', '结果展示', '确认下单'],
      formData: {
        age: 25,
        gender: 'female',
        occupation: '上班族',
        purpose: '健康',
        budget: 1000,
        hand: 16.5,
        chakraNeeds: [],
        specialNeeds: '',
        colorPreference: [],
        materials: []
      },
      occupationOptions: ['学生', '上班族', '自由职业', '企业家', '艺术家', '医疗工作者', '教育工作者', '其他'],
      purposeOptions: ['健康', '事业', '爱情', '财运', '学业', '人际关系', '平衡能量', '冥想辅助'],
      colorOptions: [{
        label: '紫色',
        value: 'purple'
      }, {
        label: '白色',
        value: 'white'
      }, {
        label: '黑色',
        value: 'black'
      }, {
        label: '粉色',
        value: 'pink'
      }, {
        label: '绿色',
        value: 'green'
      }, {
        label: '蓝色',
        value: 'blue'
      }, {
        label: '黄色',
        value: 'yellow'
      }, {
        label: '红色',
        value: 'red'
      }],
      materialOptions: [{
        label: '紫水晶',
        value: 'amethyst'
      }, {
        label: '白水晶',
        value: 'clear_quartz'
      }, {
        label: '黑曜石',
        value: 'obsidian'
      }, {
        label: '月光石',
        value: 'moonstone'
      }, {
        label: '虎眼石',
        value: 'tiger_eye'
      }, {
        label: '粉晶',
        value: 'rose_quartz'
      }],
      chakraOptions: [{
        label: '海底轮',
        value: 'root',
        color: '#FF0000'
      }, {
        label: '脐轮',
        value: 'sacral',
        color: '#FF7F00'
      }, {
        label: '太阳轮',
        value: 'solar',
        color: '#FFFF00'
      }, {
        label: '心轮',
        value: 'heart',
        color: '#00FF00'
      }, {
        label: '喉轮',
        value: 'throat',
        color: '#0000FF'
      }, {
        label: '眉心轮',
        value: 'third_eye',
        color: '#4B0082'
      }, {
        label: '顶轮',
        value: 'crown',
        color: '#9400D3'
      }],
      // 模拟数据 - 推荐结果
      radius: 150,
      beadsList: [],
      aiTaskId: null,
      aiProgress: 0,
      generatingStatusText: "正在分析您的需求...",
      aiDescription: "",
      pollingTimer: null,
      totalPrice: 0,
      partialContent: "",
      previousPartialLength: 0,
      md: null,
      renderedMarkdown: "",
      aiResponse: "",
      outputScrollTop: 0,
      outputScrollId: 'output-content',
      scrollIntoViewId: '',
      formattedAiDescription: '',
      aiTaskStatus: 'PROCESSING',
      errorMessage: '',
      userRequirements: [],
      force_update: Date.now(),
      // 强制更新标志
      showRecommendationDetails: true,
      // 添加展示推荐详情的标志
      // 添加mp-html组件的样式配置
      htmlTagStyle: {
        h1: 'font-size: 36rpx; font-weight: bold; color: #333; margin: 10rpx 0; border-bottom: 1px solid #eee; padding-bottom: 10rpx;',
        h2: 'font-size: 32rpx; font-weight: bold; color: #333; margin: 10rpx 0; border-bottom: 1px solid #f3f3f3; padding-bottom: 8rpx;',
        h3: 'font-size: 30rpx; font-weight: bold; color: #333; margin: 10rpx 0;',
        p: 'margin: 8rpx 0; line-height: 1.5;',
        ul: 'padding-left: 30rpx; margin: 8rpx 0 15rpx 0;',
        ol: 'padding-left: 30rpx; margin: 8rpx 0 15rpx 0;',
        li: 'margin: 5rpx 0;',
        blockquote: 'border-left: 4rpx solid #c9ab79; padding: 8rpx 16rpx; margin: 10rpx 0; background-color: #f9f5eb; color: #666;',
        strong: 'font-weight: bold; color: #333;',
        em: 'font-style: italic;',
        img: 'max-width: 100%; height: auto; display: block; margin: 10rpx auto; border-radius: 6rpx;'
      }
    };
  },
  onLoad: function onLoad() {
    var _this = this;
    // 加载用户历史需求
    this.loadUserRequirements();

    // 确保 rich-text 内的样式能正确应用
    setTimeout(function () {
      _this.applyMarkdownStyles();
    }, 500);
  },
  onUnload: function onUnload() {
    // 页面卸载时清除定时器
    this.clearPollingTimer();
  },
  created: function created() {
    // 初始化Markdown解析器
    // this.md = new MarkdownIt({
    //     html: true,
    //     breaks: true,
    //     linkify: true,
    //     typographer: true
    // });

    // 配置Markdown解析器，为元素添加类名
    this.configureMarkdownRenderer();

    // 继续原有的初始化内容
    // ... existing created code ...
  },

  watch: {
    // 监听aiDescription变化，渲染Markdown
    aiDescription: function aiDescription(newVal) {
      var _this2 = this;
      if (newVal) {
        this.formattedAiDescription = this.renderMarkdown(newVal);
        this.$nextTick(function () {
          // 不管在哪个步骤，都需要滚动到底部
          _this2.scrollToBottom();
        });
      }
    }
  },
  computed: _objectSpread(_objectSpread({}, (0, _vuex.mapGetters)(['isLogin'])), {}, {
    // 计算AI进度文本
    aiProgressText: function aiProgressText() {
      if (this.aiProgress < 25) {
        return '正在分析您的需求...';
      } else if (this.aiProgress < 50) {
        return '正在匹配适合的水晶...';
      } else if (this.aiProgress < 75) {
        return '正在设计手串方案...';
      } else {
        return '正在生成能量解读...';
      }
    }
  }),
  methods: {
    // 修复性别切换问题
    setGenderMale: function setGenderMale() {
      this.formData.gender = 'male';
    },
    setGenderFemale: function setGenderFemale() {
      this.formData.gender = 'female';
    },
    // 修复颜色偏好选择问题
    toggleColorByIndex: function toggleColorByIndex(e) {
      var index = e.currentTarget.dataset.index;
      var color = this.colorOptions[index].value;
      this.toggleColor(color);
    },
    toggleColor: function toggleColor(color) {
      var index = this.formData.colorPreference.indexOf(color);
      if (index === -1) {
        this.formData.colorPreference.push(color);
      } else {
        this.formData.colorPreference.splice(index, 1);
      }
    },
    // 修复材质偏好选择问题
    toggleMaterialByIndex: function toggleMaterialByIndex(e) {
      var index = e.currentTarget.dataset.index;
      var material = this.materialOptions[index].value;
      this.toggleMaterial(material);
    },
    toggleMaterial: function toggleMaterial(material) {
      var index = this.formData.materials.indexOf(material);
      if (index === -1) {
        this.formData.materials.push(material);
      } else {
        this.formData.materials.splice(index, 1);
      }
    },
    // 修复脉轮需求选择问题
    toggleChakraByIndex: function toggleChakraByIndex(e) {
      var index = e.currentTarget.dataset.index;
      var chakra = this.chakraOptions[index].value;
      this.toggleChakra(chakra);
    },
    toggleChakra: function toggleChakra(chakra) {
      var index = this.formData.chakraNeeds.indexOf(chakra);
      if (index === -1) {
        this.formData.chakraNeeds.push(chakra);
      } else {
        this.formData.chakraNeeds.splice(index, 1);
      }
    },
    onOccupationChange: function onOccupationChange(e) {
      this.formData.occupation = this.occupationOptions[e.detail.value];
    },
    onPurposeChange: function onPurposeChange(e) {
      this.formData.purpose = this.purposeOptions[e.detail.value];
    },
    onBudgetChange: function onBudgetChange(e) {
      this.formData.budget = e.detail.value;
    },
    submitForm: function submitForm() {
      var _requirementData;
      if (!this.isLogin) {
        (0, _login.toLogin)();
        return;
      }
      // 验证表单
      if (!this.formData.age) {
        uni.showToast({
          title: '请输入年龄',
          icon: 'none'
        });
        return;
      }
      if (!this.formData.occupation) {
        uni.showToast({
          title: '请选择职业',
          icon: 'none'
        });
        return;
      }
      if (!this.formData.purpose) {
        uni.showToast({
          title: '请选择目的',
          icon: 'none'
        });
        return;
      }
      if (!this.formData.hand) {
        uni.showToast({
          title: '请输入手围尺寸',
          icon: 'none'
        });
        return;
      }

      // 将脉轮需求合并到特殊需求字段中
      var specialNeeds = this.formData.specialNeeds || '';
      if (this.formData.chakraNeeds && this.formData.chakraNeeds.length > 0) {
        // 将脉轮需求转换为可读文本
        var chakraNames = {
          'root': '海底轮',
          'sacral': '脐轮',
          'solar': '太阳轮',
          'heart': '心轮',
          'throat': '喉轮',
          'third_eye': '眉心轮',
          'crown': '顶轮'
        };
        var chakraText = '脉轮需求: ';
        this.formData.chakraNeeds.forEach(function (chakra, index) {
          if (index > 0) chakraText += ', ';
          chakraText += chakraNames[chakra] || chakra;
        });
        if (specialNeeds) {
          specialNeeds += '\n' + chakraText;
        } else {
          specialNeeds = chakraText;
        }
      }

      // 将需求保存到服务器并提交AI生成请求
      var requirementData = (_requirementData = {
        userId: uni.getStorageSync('uid') || 0,
        requirementType: 'bracelet',
        age: this.formData.age,
        gender: this.formData.gender,
        occupation: this.formData.occupation,
        hand: this.formData.hand,
        purpose: this.formData.purpose,
        budget: this.formData.budget,
        colorPreference: JSON.stringify(this.formData.colorPreference),
        materials: JSON.stringify(this.formData.materials),
        specialNeeds: specialNeeds
      }, (0, _defineProperty2.default)(_requirementData, "hand", this.formData.hand), (0, _defineProperty2.default)(_requirementData, "status", 0), _requirementData);
      uni.showLoading({
        title: '正在提交需求...'
      });

      // 进入生成阶段
      console.log('表单提交成功，进入AI生成阶段');
      this.currentStep = 1;
      this.aiDescription = "";
      this.aiProgress = 0;
      this.aiTaskStatus = 'PROCESSING'; // 确保状态设置为处理中

      // 先保存用户需求
      // saveUserRequirement(requirementData).then(res => {
      //     uni.hideLoading();
      //     if (res.data) {
      //         console.log('需求保存成功，ID：', res.data);
      //         this.requirementId = res.data;

      // 提交流式AI生成请求
      this.submitStreamAiGenerationRequest(requirementData);
      //     } else {
      //         uni.showToast({
      //             title: '需求保存失败',
      //             icon: 'none'
      //         });
      //         this.currentStep = 0; // 返回到需求收集页面
      //     }
      // }).catch(err => {
      //     uni.hideLoading();
      //     uni.showToast({
      //         title: '需求保存失败: ' + err.message,
      //         icon: 'none'
      //     });
      //     this.currentStep = 0; // 返回到需求收集页面
      // });
    },
    // 提交流式AI生成请求
    submitStreamAiGenerationRequest: function submitStreamAiGenerationRequest(requirementData) {
      var _this3 = this;
      this.generatingStatusText = "正在向AI提交需求...";
      this.aiProgress = 5;
      this.partialContent = ""; // 用于存储部分生成的内容
      this.previousPartialLength = 0; // 用于跟踪内容增量
      this.aiTaskStatus = 'PROCESSING'; // 确保状态正确设置为处理中
      this.force_update = Date.now(); // 强制触发视图更新

      (0, _requirement.submitStreamAiRecommendation)(requirementData).then(function (res) {
        if (res.code == 200 && res.data) {
          uni.hideLoading();
          console.log('流式AI任务提交成功：', res.data);
          _this3.aiTaskId = res.data.taskId;
          _this3.requirementId = res.data.requirementId;
          _this3.startPollingPartialContent();
        } else {
          console.error('AI任务提交失败：', res.message);
          _this3.generatingStatusText = "AI请求失败，将使用默认推荐...";
          _this3.aiTaskStatus = 'FAILED'; // 设置状态为失败
          _this3.force_update = Date.now(); // 强制触发视图更新
          // this.useFallbackGeneration();
        }
      }).catch(function (err) {
        uni.hideLoading();
        console.error('AI任务提交请求失败：', err);
        _this3.generatingStatusText = "AI请求失败，将使用默认推荐...";
        _this3.aiTaskStatus = 'FAILED'; // 设置状态为失败
        _this3.force_update = Date.now(); // 强制触发视图更新
        // this.useFallbackGeneration();
      });
    },
    // 开始轮询部分内容结果
    startPollingPartialContent: function startPollingPartialContent() {
      var _this4 = this;
      this.clearPollingTimer(); // 确保没有重复的轮询

      // 设置轮询间隔
      this.pollingTimer = setInterval(function () {
        _this4.pollPartialContent();
      }, 500); // 使用较短的间隔以实现更流畅的打字效果
    },
    // 轮询查询部分生成内容
    pollPartialContent: function pollPartialContent() {
      var _this5 = this;
      if (!this.aiTaskId) {
        console.log('没有aiTaskId，无法轮询');
        return;
      }
      console.log('开始轮询AI任务状态，taskId:', this.aiTaskId);
      (0, _requirement.checkAiRecommendation)(this.aiTaskId).then(function (res) {
        if (res.code == 200 && res.data) {
          var taskStatus = res.data;
          console.log('轮询返回状态:', taskStatus.status, '进度:', taskStatus.progress);

          // 更新任务状态
          var oldStatus = _this5.aiTaskStatus;
          _this5.aiTaskStatus = taskStatus.status;
          if (oldStatus !== _this5.aiTaskStatus) {
            console.log('任务状态变更:', oldStatus, '->', _this5.aiTaskStatus);
            _this5.force_update = Date.now(); // 状态变更时强制更新视图
          }

          // 更新进度
          if (taskStatus.progress) {
            _this5.aiProgress = taskStatus.progress;
          }

          // 检查是否有新的部分内容
          if (taskStatus.partialContent) {
            var newContent = taskStatus.partialContent;

            // 如果内容有更新，则更新显示
            if (newContent.length > _this5.previousPartialLength) {
              console.log('检测到内容更新，当前内容长度:', newContent.length);
              _this5.aiDescription = newContent;
              _this5.formattedAiDescription = _this5.renderMarkdown(newContent);
              _this5.previousPartialLength = newContent.length;

              // 滚动到底部 - 无论在哪个步骤都执行
              _this5.$nextTick(function () {
                _this5.scrollToBottom();
              });
            }
          }

          // 根据任务状态处理
          if (taskStatus.status === 'COMPLETED') {
            console.log('轮询检测到任务已完成，将切换到结果页面');
            // 任务完成，停止轮询
            _this5.clearPollingTimer();

            // 显示最终的结果
            if (taskStatus.result && taskStatus.result.aiDescription) {
              console.log('处理完整结果:', taskStatus.result);
              // 处理AI生成完成的结果
              _this5.handleAiGenerationCompleted(taskStatus.result);
            } else {
              console.log('使用部分内容作为最终结果');
              // 如果没有result但有部分内容，则使用部分内容作为最终结果
              var finalResult = {
                aiDescription: _this5.aiDescription || ''
              };
              _this5.handleAiGenerationCompleted(finalResult);
            }
          } else if (taskStatus.status === 'FAILED') {
            console.error('AI生成任务失败:', taskStatus.error);
            // 任务失败，显示错误
            _this5.aiTaskStatus = 'FAILED';
            _this5.errorMessage = taskStatus.error || '生成失败，请重试';
            _this5.clearPollingTimer();
            // 失败时也切换到结果页面
            _this5.currentStep = 2;
          }
        } else {
          console.error('轮询AI任务状态失败：', res.message);
        }
      }).catch(function (err) {
        console.error('轮询请求失败：', err);
        // 避免无限轮询失败，如果多次请求失败也进入结果页面
        _this5.errorTries = (_this5.errorTries || 0) + 1;
        if (_this5.errorTries > 3) {
          console.log('多次轮询失败，强制进入结果页面');
          _this5.clearPollingTimer();
          _this5.aiTaskStatus = 'FAILED';
          _this5.errorMessage = '网络连接异常，请重试';
          _this5.currentStep = 2;
        }
      });
    },
    // 配置Markdown渲染器
    configureMarkdownRenderer: function configureMarkdownRenderer() {
      if (!this.md) return;

      // 添加类名到HTML标签
      this.md.renderer.rules.heading_open = function (tokens, idx, options, env, self) {
        var token = tokens[idx];
        var tag = token.tag;
        var className = tag; // 使用标签名作为类名
        token.attrJoin('class', className);
        return self.renderToken(tokens, idx, options);
      };
      this.md.renderer.rules.paragraph_open = function (tokens, idx, options, env, self) {
        tokens[idx].attrJoin('class', 'p');
        return self.renderToken(tokens, idx, options);
      };
      this.md.renderer.rules.bullet_list_open = function (tokens, idx, options, env, self) {
        tokens[idx].attrJoin('class', 'ul');
        return self.renderToken(tokens, idx, options);
      };
      this.md.renderer.rules.ordered_list_open = function (tokens, idx, options, env, self) {
        tokens[idx].attrJoin('class', 'ol');
        return self.renderToken(tokens, idx, options);
      };
      this.md.renderer.rules.list_item_open = function (tokens, idx, options, env, self) {
        tokens[idx].attrJoin('class', 'li');
        return self.renderToken(tokens, idx, options);
      };
      this.md.renderer.rules.blockquote_open = function (tokens, idx, options, env, self) {
        tokens[idx].attrJoin('class', 'blockquote');
        return self.renderToken(tokens, idx, options);
      };
      this.md.renderer.rules.strong_open = function (tokens, idx, options, env, self) {
        tokens[idx].attrJoin('class', 'strong');
        return self.renderToken(tokens, idx, options);
      };
      this.md.renderer.rules.em_open = function (tokens, idx, options, env, self) {
        tokens[idx].attrJoin('class', 'em');
        return self.renderToken(tokens, idx, options);
      };
      this.md.renderer.rules.image = function (tokens, idx, options, env, self) {
        var token = tokens[idx];
        token.attrJoin('class', 'img');
        return self.renderToken(tokens, idx, options);
      };
    },
    // 修复: 增强渲染Markdown内容的方法
    renderMarkdown: function renderMarkdown(content) {
      if (!content) return '';
      try {
        console.log('正在渲染Markdown:', content.substring(0, 100) + '...');
        return (0, _marked.marked)(content);
      } catch (error) {
        console.error('Markdown渲染错误:', error);
        return content || '';
      }
    },
    // 处理滚动到底部事件
    onScrollToBottom: function onScrollToBottom() {
      console.log('已滚动到底部');
    },
    // 保存到我的手串 - 确保保存aiResponse
    saveToMyBracelets: function saveToMyBracelets() {
      var _this6 = this;
      // 检查是否生成完成
      if (this.aiTaskStatus !== 'COMPLETED') {
        uni.showToast({
          title: '请等待AI生成完成',
          icon: 'none'
        });
        return;
      }

      // 准备更新的数据，包含aiResponse
      var updatedData = {
        id: this.requirementId,
        age: this.formData.age,
        gender: this.formData.gender,
        occupation: this.formData.occupation,
        purpose: this.formData.purpose || '',
        specialNeeds: this.formData.specialNeeds || '',
        budget: this.formData.budget,
        aiResponse: this.aiResponse,
        // 确保保存完整的AI响应
        hand: this.formData.hand,
        status: 1 // 已完成
      };

      // 根据是否有requirementId决定是更新还是创建
      if (this.requirementId) {
        // 更新已有需求
        (0, _requirement.updateUserRequirement)(updatedData).then(function (res) {
          if (res.code === 200) {
            uni.showToast({
              title: '已保存到我的手串',
              icon: 'success'
            });
          } else {
            uni.showToast({
              title: res.message || '保存失败',
              icon: 'none'
            });
          }
        }).catch(function (err) {
          console.error('保存失败：', err);
          uni.showToast({
            title: '保存失败，请重试',
            icon: 'none'
          });
        });
      } else {
        // 创建新需求
        var requirementData = {
          age: this.formData.age,
          gender: this.formData.gender,
          occupation: this.formData.occupation,
          purpose: this.formData.purpose || '',
          specialNeeds: this.formData.specialNeeds || '',
          budget: this.formData.budget,
          aiResponse: this.aiResponse,
          // 确保保存完整的AI响应
          hand: this.formData.hand,
          status: 1 // 已完成
        };

        (0, _requirement.saveUserRequirement)(requirementData).then(function (res) {
          if (res.code === 200) {
            // 更新requirementId
            _this6.requirementId = res.data;
            uni.showToast({
              title: '已保存到我的手串',
              icon: 'success'
            });
          } else {
            uni.showToast({
              title: res.message || '保存失败',
              icon: 'none'
            });
          }
        }).catch(function (err) {
          console.error('保存失败：', err);
          uni.showToast({
            title: '保存失败，请重试',
            icon: 'none'
          });
        });
      }
    },
    // 恢复生成珠子布局的方法
    generateMarbles: function generateMarbles() {
      var realRoundWidth = this.beadsList.map(function (e) {
        return parseFloat(e.width);
      }).reduce(function (accumulator, currentValue) {
        return accumulator + currentValue;
      }, 0);
      this.realRadius = realRoundWidth.toFixed(2);
      if (this.beadsList.length < 6) {
        this.radius = 100;
        return;
      }
      var totalBeadsWidth = this.beadsList.reduce(function (sum, bead) {
        return sum + parseFloat(bead.width) * 3;
      }, 0);
      var spacing = totalBeadsWidth / this.beadsList.length * 0.05;
      var totalCircumference = totalBeadsWidth + spacing * this.beadsList.length;

      // Set radius to match circle border
      this.radius = totalCircumference / (2 * Math.PI);
      //   this.realRadius = (totalCircumference / Math.PI).toFixed(2);
    },
    generateMockRecommendation: function generateMockRecommendation() {
      var _this7 = this;
      var mockBeads = [];
      var totalPrice = 0;
      if (this.formData.chakraNeeds.length > 0) {
        this.formData.chakraNeeds.forEach(function (chakra) {
          var chakraInfo = _this7.chakraOptions.find(function (c) {
            return c.value === chakra;
          });
          var count = Math.floor(Math.random() * 2) + 10;
          for (var i = 0; i < count; i++) {
            var price = Math.floor(Math.random() * 300) + 100;
            totalPrice += price;
            mockBeads.push({
              id: "bead-".concat(mockBeads.length + 1),
              name: _this7.getRandomBeadName(chakra),
              image: 'https://mpjoy.oss-cn-beijing.aliyuncs.com/crmebimage/public/content/2024/12/14/b3cf9d2d83d14999a57e3c0ce922e98ak1u8k8kow5.png',
              width: 20,
              height: 20,
              effect: _this7.getRandomEffect(chakra),
              price: price,
              chakra: chakra,
              color: chakraInfo.color
            });
          }
        });
      } else {
        var count = Math.floor(Math.random() * 5) + 8;
        for (var i = 0; i < count; i++) {
          var randomChakra = this.chakraOptions[Math.floor(Math.random() * this.chakraOptions.length)];
          var price = Math.floor(Math.random() * 300) + 100;
          totalPrice += price;
          mockBeads.push({
            id: "bead-".concat(i + 1),
            name: this.getRandomBeadName(randomChakra.value),
            image: 'https://mpjoy.oss-cn-beijing.aliyuncs.com/crmebimage/public/content/2024/12/14/b3cf9d2d83d14999a57e3c0ce922e98ak1u8k8kow5.png',
            width: 20,
            height: 20,
            effect: this.getRandomEffect(randomChakra.value),
            price: price,
            chakra: randomChakra.value,
            color: randomChakra.color
          });
        }
      }

      // 设置推荐结果
      this.beadsList = mockBeads;
      this.totalPrice = totalPrice;
    },
    getRandomBeadName: function getRandomBeadName(chakra) {
      var beadNames = {
        'root': ['红玛瑙', '石榴石', '黑曜石', '红虎眼石', '红碧玺'],
        'sacral': ['橙色玛瑙', '橙色月光石', '橙色方解石', '橙色玉髓'],
        'solar': ['黄水晶', '虎眼石', '琥珀', '金发晶', '黄玉'],
        'heart': ['绿幽灵', '绿碧玺', '翡翠', '孔雀石', '绿aventurine'],
        'throat': ['青金石', '海蓝宝', '蓝玉髓', '蓝碧玺', '绿松石'],
        'third_eye': ['紫水晶', '荧石', '舒俱来', '蓝宝石', '坦桑石'],
        'crown': ['白水晶', '紫晶簇', '月光石', '钻石', '白碧玺']
      };
      var names = beadNames[chakra] || ['水晶珠'];
      return names[Math.floor(Math.random() * names.length)];
    },
    getRandomEffect: function getRandomEffect(chakra) {
      var effects = {
        'root': ['增强稳定性', '提供安全感', '增强生命力', '促进物质丰盛'],
        'sacral': ['激发创造力', '增强性能量', '促进情感平衡', '增强自信'],
        'solar': ['增强个人力量', '提升自尊', '促进智慧决策', '增强意志力'],
        'heart': ['促进爱与和谐', '平衡情感', '增强同理心', '促进人际关系'],
        'throat': ['促进沟通', '增强表达能力', '促进真实性', '增强创意表达'],
        'third_eye': ['增强直觉', '促进洞察力', '增强精神意识', '促进冥想'],
        'crown': ['连接高我', '促进精神觉醒', '增强宇宙意识', '促进内在平静']
      };
      var effectList = effects[chakra] || ['增强能量'];
      return effectList[Math.floor(Math.random() * effectList.length)];
    },
    getMarbleStyle: function getMarbleStyle(index) {
      // Calculate total circumference based on bead sizes
      var totalBeadsWidth = this.beadsList.reduce(function (sum, bead) {
        return sum + bead.width * 3;
      }, 0);

      // Add small gaps between beads (5% of average bead size)
      var spacing = totalBeadsWidth / this.beadsList.length * 0.05;
      var circumference = totalBeadsWidth + spacing * this.beadsList.length;

      // Calculate radius to match circle border
      var dynamicRadius = circumference / (2 * Math.PI);

      // Calculate position on circle for current bead
      var angleOffset = 0;
      for (var i = 0; i < index; i++) {
        var prevBeadWidth = this.beadsList[i].width * 3;
        angleOffset += (prevBeadWidth + spacing) / dynamicRadius;
      }
      var currentBeadWidth = this.beadsList[index].width * 3;
      var angle = angleOffset + currentBeadWidth / 2 / dynamicRadius;

      // Position bead exactly on circle circumference
      var x = dynamicRadius * (1 + Math.cos(angle));
      var y = dynamicRadius * (1 + Math.sin(angle));
      return {
        position: 'absolute',
        left: x - currentBeadWidth / 2 + 'rpx',
        top: y - this.beadsList[index].height * 3 / 2 + 'rpx',
        width: currentBeadWidth + 'rpx',
        height: this.beadsList[index].height * 3 + 'rpx',
        transform: "rotate(".concat(angle + Math.PI / 2, "rad)"),
        background: "url('".concat(this.beadsList[index].image, "') no-repeat center"),
        backgroundSize: 'cover',
        transition: 'all 0.3s ease'
      };
    },
    editRecommendation: function editRecommendation() {
      // 返回到需求收集页面
      this.currentStep = 0;
    },
    nextStep: function nextStep() {
      // 进入确认下单页面
      this.currentStep = 3;
    },
    placeOrder: function placeOrder() {
      // 模拟下单
      uni.showLoading({
        title: '正在下单...'
      });

      // 准备下单数据
      var orderData = {
        requirementId: this.requirementId,
        // 关联需求ID
        productInfo: JSON.stringify({
          beadsList: this.beadsList,
          totalPrice: this.totalPrice,
          aiDescription: this.aiDescription
        }),
        totalPrice: this.totalPrice
      };

      // 记录用户的下单行为 - 可以接入实际的下单API
      console.log('准备下单数据：', orderData);

      // 这里应该调用实际的下单API，目前使用模拟
      setTimeout(function () {
        uni.hideLoading();
        uni.showToast({
          title: '下单成功！',
          icon: 'success'
        });

        // 跳转到订单详情页
        setTimeout(function () {
          uni.navigateTo({
            url: '/pages/order_details/index?id=mock-order-id'
          });
        }, 1500);
      }, 2000);
    },
    // 清除轮询定时器
    clearPollingTimer: function clearPollingTimer() {
      if (this.pollingTimer) {
        clearInterval(this.pollingTimer);
        this.pollingTimer = null;
      }
    },
    // 滚动到底部 - 优化版本
    scrollToBottom: function scrollToBottom() {
      var _this8 = this;
      console.log('执行滚动到底部');
      this.$nextTick(function () {
        // 使用scroll-into-view方式滚动
        _this8.scrollIntoViewId = 'scroll-bottom-anchor';

        // 同时尝试使用传统的pageScrollTo方法（微信小程序API）
        try {
          uni.pageScrollTo({
            scrollTop: 10000,
            duration: 100
          });
        } catch (e) {
          console.log('pageScrollTo方法调用失败', e);
        }

        // 重置后再设置，确保触发滚动效果
        setTimeout(function () {
          _this8.scrollIntoViewId = '';
          setTimeout(function () {
            _this8.scrollIntoViewId = 'scroll-bottom-anchor';
          }, 10);
        }, 100);
      });
    },
    // 滚动推荐内容到可见区域
    scrollRecommendationToView: function scrollRecommendationToView() {
      var _this9 = this;
      if (this.$refs.recommendationDetails) {
        this.$nextTick(function () {
          var query = uni.createSelectorQuery().in(_this9);
          query.select('.recommendation-details').boundingClientRect(function (data) {
            if (data) {
              uni.pageScrollTo({
                scrollTop: data.top,
                duration: 300
              });
            }
          }).exec();
        });
      }
    },
    // 更新需求状态时保存AI响应结果
    updateUserRequirement: function updateUserRequirement(updatedData) {
      // 确保包含AI响应结果
      if (this.aiResponse && !updatedData.aiResponse) {
        updatedData.aiResponse = this.aiResponse;
      }
      return (0, _requirement.updateUserRequirement)(updatedData);
    },
    // 生成新推荐
    generateNewRecommendation: function generateNewRecommendation() {
      this.currentStep = 0;
      this.aiDescription = "";
      this.aiProgress = 0;
      this.aiTaskStatus = 'PROCESSING';
      this.errorMessage = '';
      this.submitStreamAiGenerationRequest(this.formData);
    },
    // 确保 Markdown 样式应用到 rich-text 内容
    applyMarkdownStyles: function applyMarkdownStyles() {
      // 适用于所有平台的样式处理
      var pages = getCurrentPages();
      var page = pages[pages.length - 1];
      if (page && page.$getAppWebview) {
        var webview = page.$getAppWebview();
        if (webview) {
          // 注入辅助样式处理脚本
          webview.evalJS("\n                        document.querySelectorAll('.markdown-content').forEach(el => {\n                            if(el._styled) return;\n                            el._styled = true;\n                            console.log('\u5E94\u7528Markdown\u6837\u5F0F');\n                        });\n                    ");
        }
      }
    },
    // 加载用户历史需求
    loadUserRequirements: function loadUserRequirements() {
      var _this10 = this;
      var userId = uni.getStorageSync('uid');
      if (!userId) {
        console.log('用户未登录，无法加载历史需求');
        return;
      }
      uni.showLoading({
        title: '加载历史数据...'
      });
      (0, _requirement.getRequirementByUserId)(userId).then(function (res) {
        uni.hideLoading();
        if (res.data && res.data.length > 0) {
          _this10.userRequirements = res.data.filter(function (item) {
            return item.requirementType === 'bracelet';
          });
          console.log('加载到用户历史需求：', _this10.userRequirements.length);

          // 可以在这里添加一个弹窗，询问用户是否要加载历史需求
          if (_this10.userRequirements.length > 0) {
            uni.showModal({
              title: '发现历史需求',
              content: '是否加载您最近的手串需求？',
              success: function success(res) {
                if (res.confirm) {
                  // 加载最新的一条需求
                  _this10.loadHistoryRequirement(_this10.userRequirements[0]);
                }
              }
            });
          }
        }
      }).catch(function (err) {
        uni.hideLoading();
        console.error('加载历史需求失败：', err);
      });
    },
    // 处理AI生成完成 - 恢复模拟珠子数据生成
    handleAiGenerationCompleted: function handleAiGenerationCompleted(result) {
      var _this11 = this;
      try {
        console.log('处理AI生成完成回调，当前步骤:', this.currentStep, '当前状态:', this.aiTaskStatus);
        console.log('AI生成完成，准备展示结果:', result);

        // 保存AI生成的描述
        if (result && result.aiDescription) {
          this.aiDescription = result.aiDescription;

          // 确保格式化后的内容是正确的HTML字符串
          var htmlContent = this.renderMarkdown(result.aiDescription);

          // 微信小程序的rich-text组件需要特殊处理
          if (uni.getSystemInfoSync().platform === 'mp-weixin') {
            htmlContent = htmlContent.replace(/<(?!\/?(h\d|p|strong|em|ul|ol|li|blockquote|img)[>\s])/g, '&lt;');
          }
          this.formattedAiDescription = htmlContent;

          // 保存到aiResponse字段以便后续更新
          this.aiResponse = result.aiDescription;
        }

        // 更新状态 - 先设置状态再切换步骤
        this.aiTaskStatus = 'COMPLETED';
        this.force_update = Date.now(); // 强制触发视图更新
        console.log('设置状态为COMPLETED，准备切换到结果页面');

        // 明确地切换到结果展示步骤
        // this.currentStep = 2;

        // 滚动到推荐内容区域
        this.$nextTick(function () {
          console.log('AI响应完成，滚动到底部');
          _this11.scrollToBottom();
        });

        // 模拟AI生成过程
        setTimeout(function () {
          if (result && result.recommendedProducts) {
            _this11.beadsList = result.recommendedProducts;
          }
          if (_this11.beadsList.length <= 0) {
            _this11.generateMockRecommendation();
          }
          _this11.currentStep = 2;
          console.log('已切换到结果展示步骤，当前步骤:', _this11.currentStep);
          // 生成珠子布局
          _this11.generateMarbles();
        }, 1500);
      } catch (error) {
        console.error('处理AI响应时出错:', error);
        // 发生错误时也要确保切换到结果页面
        this.currentStep = 2;
        this.force_update = Date.now(); // 强制触发视图更新
        console.log('发生错误，强制切换到结果页面，当前步骤:', this.currentStep);
      }
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 136:
/*!**********************************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/bracelets/axureIndex.vue?vue&type=style&index=0&lang=scss& ***!
  \**********************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_axureIndex_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--8-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./axureIndex.vue?vue&type=style&index=0&lang=scss& */ 137);
/* harmony import */ var _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_axureIndex_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_axureIndex_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_axureIndex_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_axureIndex_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_axureIndex_vue_vue_type_style_index_0_lang_scss___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 137:
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/bracelets/axureIndex.vue?vue&type=style&index=0&lang=scss& ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[128,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/bracelets/axureIndex.js.map