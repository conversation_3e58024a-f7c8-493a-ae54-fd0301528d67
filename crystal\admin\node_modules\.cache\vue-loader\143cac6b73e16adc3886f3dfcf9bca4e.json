{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\order\\orderSend.vue?vue&type=template&id=58fc766a&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\order\\orderSend.vue", "mtime": 1753666157911}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\n<el-dialog :visible.sync=\"modals\" title=\"发送货\" class=\"order_box\" :before-close=\"handleClose\" width=\"600px\">\n  <el-form ref=\"formItem\" :model=\"formItem\" label-width=\"110px\" @submit.native.prevent :rules=\"rules\">\n    <el-form-item label=\"选择类型：\">\n      <el-radio-group v-model=\"formItem.type\" @change=\"changeRadioType(formItem.type)\">\n        <el-radio label=\"1\">发货</el-radio>\n        <!-- <el-radio label=\"2\">送货</el-radio>\n        <el-radio label=\"3\">虚拟</el-radio> -->\n      </el-radio-group>\n    </el-form-item>\n    <div v-if=\"formItem.type==='1'\">\n      <!-- <el-form-item label=\"发货类型：\" prop=\"expressId\">\n        <el-radio-group v-model=\"formItem.expressRecordType\" @change=\"changeRadio(formItem.expressRecordType)\">\n          <el-radio label=\"1\">手动填写</el-radio>\n          <el-radio label=\"2\" v-if=\"checkPermi(['admin:order:sheet:info'])\">电子面单打印</el-radio>\n        </el-radio-group>\n      </el-form-item> -->\n      <el-form-item label=\"快递公司：\" prop=\"expressCode\">\n        <el-select v-model=\"formItem.expressCode\" filterable style=\"width:80%;\"\n                   @change=\"onChangeExport(formItem.expressCode)\">\n          <el-option v-for=\"(item,i) in express\" :value=\"item.code\" :key=\"i\" :label=\"item.name\"></el-option>\n        </el-select>\n      </el-form-item>\n      <el-form-item v-if=\"formItem.expressRecordType === '1'\" label=\"快递单号：\" prop=\"expressNumber\">\n        <el-input v-model=\"formItem.expressNumber\" placeholder=\"请输入快递单号\" style=\"width:80%;\"></el-input>\n      </el-form-item>\n      <template v-if=\"formItem.expressRecordType === '2'\">\n        <el-form-item label=\"电子面单：\" class=\"express_temp_id\" prop=\"expressTempId\">\n          <div class=\"acea-row\">\n            <el-select v-model=\"formItem.expressTempId\" placeholder=\"请选择电子面单\"\n                       :class=\"[formItem.expressTempId?'width9':'width8']\" @change=\"onChangeImg\">\n              <el-option v-for=\"(item,i) in exportTempList\" :value=\"item.temp_id\" :key=\"i\"\n                         :label=\"item.title\"></el-option>\n            </el-select>\n            <div v-if=\"formItem.expressTempId\" style=\"position: relative;\">\n              <!--<span class=\"tempImg\" @click=\"\">预览</span>-->\n              <div class=\"tempImgList ml10\">\n                <div class=\"demo-image__preview\">\n                  <el-image\n                    style=\"width: 36px; height: 36px\"\n                    :src=\"tempImg\"\n                    :preview-src-list=\"[tempImg]\"\n                  />\n                </div>\n              </div>\n            </div>\n            <!--<Button v-if=\"formItem.expressTempId\" type=\"text\" @click=\"preview\">预览</Button>-->\n          </div>\n\n        </el-form-item>\n        <el-form-item label=\"寄件人姓名：\" prop=\"toName\">\n          <el-input v-model=\"formItem.toName\" placeholder=\"请输入寄件人姓名\" style=\"width:80%;\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"寄件人电话：\" prop=\"toTel\">\n          <el-input v-model=\"formItem.toTel\" placeholder=\"请输入寄件人电话\" style=\"width:80%;\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"寄件人地址：\" prop=\"toAddr\">\n          <el-input v-model=\"formItem.toAddr\" placeholder=\"请输入寄件人地址\" style=\"width:80%;\"></el-input>\n        </el-form-item>\n      </template>\n    </div>\n    <div v-if=\"formItem.type==='2'\">\n      <el-form-item label=\"送货人姓名：\" prop=\"deliveryName\">\n        <el-input v-model=\"formItem.deliveryName\" placeholder=\"请输入送货人姓名\" style=\"width:80%;\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"送货人电话：\" prop=\"deliveryTel\">\n        <el-input v-model=\"formItem.deliveryTel\" placeholder=\"请输入送货人电话\" style=\"width:80%;\"></el-input>\n      </el-form-item>\n    </div>\n    <div>\n      <el-form-item label=\"\" >\n        <div style=\"color:#CECECE;\">顺丰请输入单号：收件人或寄件人手机号后四位</div>\n        <div style=\"color:#CECECE;\">例如：SF000000000000:3941</div>\n      </el-form-item>\n    </div>\n  </el-form>\n  <div slot=\"footer\">\n    <el-button  type=\"primary\" @click=\"putSend('formItem')\">提交</el-button>\n    <el-button  @click=\"cancel('formItem')\">取消</el-button>\n  </div>\n</el-dialog>\n", null]}