{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/goods_logistics/index.vue?8851", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/goods_logistics/index.vue?ae29", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/goods_logistics/index.vue?7c59", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/goods_logistics/index.vue?c9ab", "uni-app:///pages/users/goods_logistics/index.vue", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/goods_logistics/index.vue?771a", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/goods_logistics/index.vue?e417"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "recommend", "authorize", "data", "orderId", "product", "productInfo", "orderInfo", "expressList", "hostProduct", "loading", "goodScroll", "params", "page", "limit", "computed", "watch", "is<PERSON>ogin", "handler", "deep", "onLoad", "title", "onReady", "methods", "onLoadFun", "copyOrderId", "uni", "getExpress", "that", "get_host_product", "onReachBottom"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACmM;AACnM,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAkwB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACgDtxB;AAGA;AAGA;AACA;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAOA;EACAC;IACAC;IAEAC;EAEA;EACAC;IACA;MACAC;MACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QAAA;QACAC;QACAC;MACA;IACA;EACA;EACAC;EACAC;IACAC;MACAC;QACA;UACA;UACA;QACA;MACA;MACAC;IACA;EACA;EACAC;IACA;MAAAC;IAAA;IACA;IACA;MACA;MACA;IACA;MACA;IACA;EACA;EACAC,6BAWA;EACAC;IACA;AACA;AACA;IACAC;MACA;MACA;IACA;IACAC;MACAC;QAAAvB;MAAA;IACA;IACAwB;MACA;MACA;QACA;QACAC;QACAA;QACAA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACA;QACAD;QACAA;QACAA;QACAA;MACA;IACA;EACA;EACA;EACAE;IAEA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnKA;AAAA;AAAA;AAAA;AAAq8C,CAAgB,ovCAAG,EAAC,C;;;;;;;;;;;ACAz9C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/users/goods_logistics/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/users/goods_logistics/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=0ebde93d&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=0ebde93d&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0ebde93d\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/users/goods_logistics/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=0ebde93d&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.hostProduct.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view class='logistics'>\r\n\t\t\t<view class='header acea-row row-between row-top'>\r\n\t\t\t\t<view class='pictrue'>\r\n\t\t\t\t\t<image :src='product.productImg'></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='text acea-row row-between'>\r\n\t\t\t\t\t<view class='name line2'>{{product.productName}}</view>\r\n\t\t\t\t\t<view class='money'>\r\n\t\t\t\t\t\t<view>￥{{product.price}}</view>\r\n\t\t\t\t\t\t<view>x{{product.payNum}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class='logisticsCon'>\r\n\t\t\t\t<view class='company acea-row row-between-wrapper'>\r\n\t\t\t\t\t<view class='picTxt acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t<view class='iconfont icon-wuliu'></view>\r\n\t\t\t\t\t\t<view class='text'>\r\n\t\t\t\t\t\t\t<view><text class='name line1'>物流公司：</text> {{orderInfo.deliveryName}}</view>\r\n\t\t\t\t\t\t\t<view class='express line1'><text class='name'>快递单号：</text> {{orderInfo.deliveryId}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- #ifndef H5 -->\r\n\t\t\t\t\t<view class='copy' @tap='copyOrderId'>复制单号</view>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t\t\t<view class='copy copy-data' :data-clipboard-text=\"orderInfo.deliveryId\">复制单号</view>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='item' v-for=\"(item,index) in expressList\" :key=\"index\">\r\n\t\t\t\t\t<view class='circular' :class='index === 0 ? \"on\":\"\"'></view>\r\n\t\t\t\t\t<view class='text' :class='index===0 ? \"on-font on\":\"\"'>\r\n\t\t\t\t\t\t<view>{{item.status}}</view>\r\n\t\t\t\t\t\t<view class='data' :class='index===0 ? \"on-font on\":\"\"'>{{item.time}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<recommend :hostProduct='hostProduct' v-if=\"hostProduct.length\"></recommend>\r\n\t\t</view>\r\n\t\t<!-- #ifdef MP -->\r\n\t\t<!-- <authorize :isAuto=\"isAuto\" :isShowAuth=\"isShowAuth\" @authColse=\"authColse\"></authorize> -->\r\n\t\t<!-- #endif -->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\texpress\r\n\t} from '@/api/order.js';\r\n\timport {\r\n\t\tgetProductHot\r\n\t} from '@/api/store.js';\r\n\timport ClipboardJS from \"@/plugin/clipboard/clipboard.js\";\r\n\timport {\r\n\t\ttoLogin\r\n\t} from '@/libs/login.js';\r\n\timport {\r\n\t\tmapGetters\r\n\t} from \"vuex\";\r\n\timport recommend from '@/components/recommend';\r\n\t// #ifdef MP\r\n\timport authorize from '@/components/Authorize';\r\n\t// #endif\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\trecommend,\r\n\t\t\t// #ifdef MP\r\n\t\t\tauthorize\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\torderId: '',\r\n\t\t\t\tproduct: {\r\n\t\t\t\t\tproductInfo: {}\r\n\t\t\t\t},\r\n\t\t\t\torderInfo: {},\r\n\t\t\t\texpressList: [],\r\n\t\t\t\thostProduct: [],\r\n\t\t\t\tloading: false,\r\n\t\t\t\tgoodScroll: true,\r\n\t\t\t\tparams: { //精品推荐分页\r\n\t\t\t\t\tpage: 1,\r\n\t\t\t\t\tlimit: 10,\r\n\t\t\t\t},\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: mapGetters(['isLogin']),\r\n\t\twatch:{\r\n\t\t\tisLogin:{\r\n\t\t\t\thandler:function(newV,oldV){\r\n\t\t\t\t\tif(newV){\r\n\t\t\t\t\t\tthis.getExpress();\r\n\t\t\t\t\t\tthis.get_host_product();\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tdeep:true\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad: function (options) {\r\n\t\t    if (!options.orderId) return this.$util.Tips({title:'缺少订单号'});\r\n\t\t\tthis.orderId = options.orderId;\r\n\t\t\tif (this.isLogin) {\r\n\t\t\t\tthis.getExpress();\r\n\t\t\t\tthis.get_host_product();\r\n\t\t\t} else {\r\n\t\t\t\ttoLogin();\r\n\t\t\t}\r\n\t\t  },\r\n\t\t  onReady: function() {\r\n\t\t  \t// #ifdef H5\r\n\t\t  \tthis.$nextTick(function() {\r\n\t\t  \t\tconst clipboard = new ClipboardJS(\".copy-data\");\r\n\t\t  \t\tclipboard.on(\"success\", () => {\r\n\t\t  \t\t\tthis.$util.Tips({\r\n\t\t  \t\t\t\ttitle: '复制成功'\r\n\t\t  \t\t\t});\r\n\t\t  \t\t});\r\n\t\t  \t});\r\n\t\t  \t// #endif\r\n\t\t  },\r\n\t\tmethods: {\r\n\t\t\t/**\r\n\t\t\t * 授权回调\r\n\t\t\t */\r\n\t\t\tonLoadFun: function() {\r\n\t\t\t\tthis.getExpress();\r\n\t\t\t\tthis.get_host_product();\r\n\t\t\t},\r\n\t\t\tcopyOrderId:function(){\r\n\t\t\t    uni.setClipboardData({ data: this.orderInfo.deliveryId });\r\n\t\t\t  },\r\n\t\t\t  getExpress:function(){\r\n\t\t\t    let that=this;\r\n\t\t\t    express(that.orderId).then(function(res){\r\n\t\t\t      let result = res.data.express|| {};\r\n\t\t\t\t  that.$set(that,'product',res.data.order.info[0] || {});\r\n\t\t\t\t  that.$set(that,'orderInfo',res.data.order);\r\n\t\t\t\t  that.$set(that,'expressList',result.list || []);\r\n\t\t\t    });\r\n\t\t\t  },\r\n\t\t\t  get_host_product: function () {\r\n\t\t\t\t  \tthis.loading = true\r\n\t\t\t\t  \tif (!this.goodScroll) return\r\n\t\t\t    let that = this;\r\n\t\t\t    getProductHot(that.params.page,that.params.limit).then(function (res) {\r\n\t\t\t\t\t\t\t//this.iSshowH = false\r\n\t\t\t\t\t\t\tthat.loading = false\r\n\t\t\t\t\t\t\tthat.goodScroll = res.data.list.length >= that.params.limit\r\n\t\t\t\t\t\t\tthat.params.page++\r\n\t\t\t\t\t\t\tthat.hostProduct = that.hostProduct.concat(res.data.list)\r\n\t\t\t    });\r\n\t\t\t  },\r\n\t\t},\r\n\t\t// 滚动到底部\r\n\t\tonReachBottom() {\r\n\t\t\r\n\t\t\tif (this.params.page != 1) {\r\n\t\t\t\tthis.get_host_product();\r\n\t\t\t}\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t.logistics .header {\r\n\t\tpadding: 23rpx 30rpx;\r\n\t\tbackground-color: #fff;\r\n\t\theight: 166rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.logistics .header .pictrue {\r\n\t\twidth: 120rpx;\r\n\t\theight: 120rpx;\r\n\t}\r\n\r\n\t.logistics .header .pictrue image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 6rpx;\r\n\t}\r\n\r\n\t.logistics .header .text {\r\n\t\twidth: 540rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #999;\r\n\t\tmargin-top: 6rpx;\r\n\t}\r\n\r\n\t.logistics .header .text .name {\r\n\t\twidth: 365rpx;\r\n\t\tcolor: #282828;\r\n\t}\r\n\r\n\t.logistics .header .text .money {\r\n\t\ttext-align: right;\r\n\t}\r\n\r\n\t.logistics .logisticsCon {\r\n\t\tbackground-color: #fff;\r\n\t\tmargin: 12rpx 0;\r\n\t}\r\n\r\n\t.logistics .logisticsCon .company {\r\n\t\theight: 120rpx;\r\n\t\tmargin: 0 0 45rpx 30rpx;\r\n\t\tpadding-right: 30rpx;\r\n\t\tborder-bottom: 1rpx solid #f5f5f5;\r\n\t}\r\n\r\n\t.logistics .logisticsCon .company .picTxt {\r\n\t\twidth: 520rpx;\r\n\t}\r\n\r\n\t.logistics .logisticsCon .company .picTxt .iconfont {\r\n\t\twidth: 50rpx;\r\n\t\theight: 50rpx;\r\n\t\tbackground-color: #666;\r\n\t\ttext-align: center;\r\n\t\tline-height: 50rpx;\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 35rpx;\r\n\t}\r\n\r\n\t.logistics .logisticsCon .company .picTxt .text {\r\n\t\twidth: 450rpx;\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #282828;\r\n\t}\r\n\r\n\t.logistics .logisticsCon .company .picTxt .text .name {\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.logistics .logisticsCon .company .picTxt .text .express {\r\n\t\tmargin-top: 5rpx;\r\n\t}\r\n\r\n\t.logistics .logisticsCon .company .copy {\r\n\t\tfont-size: 20rpx;\r\n\t\twidth: 106rpx;\r\n\t\theight: 40rpx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 40rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tborder: 1rpx solid #999;\r\n\t}\r\n\r\n\t.logistics .logisticsCon .item {\r\n\t\tpadding: 0 40rpx;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.logistics .logisticsCon .item .circular {\r\n\t\twidth: 20rpx;\r\n\t\theight: 20rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tposition: absolute;\r\n\t\ttop: -1rpx;\r\n\t\tleft: 31.5rpx;\r\n\t\tbackground-color: #ddd;\r\n\t}\r\n\r\n\t.logistics .logisticsCon .item .circular.on {\r\n\t\tbackground-color: $theme-color;\r\n\t}\r\n\r\n\t.logistics .logisticsCon .item .text.on-font {\r\n\t\tcolor: $theme-color;\r\n\t}\r\n\r\n\t.logistics .logisticsCon .item .text .data.on-font {\r\n\t\tcolor: $theme-color;\r\n\t}\r\n\r\n\t.logistics .logisticsCon .item .text {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #666;\r\n\t\twidth: 615rpx;\r\n\t\tborder-left: 1rpx solid #e6e6e6;\r\n\t\tpadding: 0 0 60rpx 38rpx;\r\n\t}\r\n\r\n\t.logistics .logisticsCon .item .text.on {\r\n\t\tborder-left-color: #f8c1bd;\r\n\t}\r\n\r\n\t.logistics .logisticsCon .item .text .data {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999;\r\n\t\tmargin-top: 10rpx;\r\n\t}\r\n\r\n\t.logistics .logisticsCon .item .text .data .time {\r\n\t\tmargin-left: 15rpx;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=0ebde93d&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=0ebde93d&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363903482\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}