(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["uni_modules/mp-html/components/mp-html/node/node"],{"28fd":function(t,e,r){"use strict";r.r(e);var i=r("ded6"),o=r.n(i);for(var n in i)["default"].indexOf(n)<0&&function(t){r.d(e,t,(function(){return i[t]}))}(n);e["default"]=o.a},3101:function(t,e,r){"use strict";r.d(e,"b",(function(){return i})),r.d(e,"c",(function(){return o})),r.d(e,"a",(function(){}));var i=function(){var t=this.$createElement;this._self._c},o=[]},8611:function(t,e,r){"use strict";e["a"]=function(t){t.options.wxsCallMethods||(t.options.wxsCallMethods=[])}},"931e":function(t,e,r){"use strict";r.r(e);var i=r("3101"),o=r("28fd");for(var n in o)["default"].indexOf(n)<0&&function(t){r.d(e,t,(function(){return o[t]}))}(n);r("fdf8");var s=r("828b"),a=r("8611"),c=Object(s["a"])(o["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);"function"===typeof a["a"]&&Object(a["a"])(c),e["default"]=c.exports},a7e0:function(t,e,r){},ded6:function(t,e,r){"use strict";(function(t){var i=r("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=i(r("7ca3"));function n(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,i)}return r}function s(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?n(Object(r),!0).forEach((function(e){(0,o.default)(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}var a={name:"node",options:{virtualHost:!0},data:function(){return{ctrl:{},isiOS:t.getSystemInfoSync().system.includes("iOS")}},props:{name:String,attrs:{type:Object,default:function(){return{}}},childs:Array,opts:Array},components:{node:function(){Promise.resolve().then(function(){return resolve(r("931e"))}.bind(null,r)).catch(r.oe)}},mounted:function(){var t=this;this.$nextTick((function(){for(t.root=t.$parent;"mp-html"!==t.root.$options.name;t.root=t.root.$parent);}))},beforeDestroy:function(){},methods:{toJSON:function(){return this},play:function(e){var r=e.currentTarget.dataset.i,i=this.childs[r];if(this.root.$emit("play",{source:i.name,attrs:s(s({},i.attrs),{},{src:i.src[this.ctrl[r]||0]})}),this.root.pauseVideo){for(var o=!1,n=e.target.id,a=this.root._videos.length;a--;)this.root._videos[a].id===n?o=!0:this.root._videos[a].pause();if(!o){var c=t.createVideoContext(n,this);c.id=n,this.root.playbackRate&&c.playbackRate(this.root.playbackRate),this.root._videos.push(c)}}},imgTap:function(e){var r=this.childs[e.currentTarget.dataset.i];r.a?this.linkTap(r.a):r.attrs.ignore||(this.root.$emit("imgtap",r.attrs),this.root.previewImg&&t.previewImage({showmenu:this.root.showImgMenu,current:parseInt(r.attrs.i),urls:this.root.imgList}))},imgLongTap:function(t){},imgLoad:function(t){var e=t.currentTarget.dataset.i;this.childs[e].w?(this.opts[1]&&!this.ctrl[e]||-1===this.ctrl[e])&&this.$set(this.ctrl,e,1):this.$set(this.ctrl,e,t.detail.width),this.checkReady()},checkReady:function(){var t=this;this.root&&!this.root.lazyLoad&&(this.root._unloadimgs-=1,this.root._unloadimgs||setTimeout((function(){t.root.getRect().then((function(e){t.root.$emit("ready",e)})).catch((function(){t.root.$emit("ready",{})}))}),350))},linkTap:function(e){var r=e.currentTarget?this.childs[e.currentTarget.dataset.i]:{},i=r.attrs||e,o=i.href;this.root.$emit("linktap",Object.assign({innerText:this.root.getText(r.children||[])},i)),o&&("#"===o[0]?this.root.navigateTo(o.substring(1)).catch((function(){})):o.split("?")[0].includes("://")?this.root.copyLink&&t.setClipboardData({data:o,success:function(){return t.showToast({title:"链接已复制"})}}):t.navigateTo({url:o,fail:function(){t.switchTab({url:o,fail:function(){}})}}))},mediaError:function(t){var e=t.currentTarget.dataset.i,r=this.childs[e];if("video"===r.name||"audio"===r.name){var i=(this.ctrl[e]||0)+1;if(i>r.src.length&&(i=0),i<r.src.length)return void this.$set(this.ctrl,e,i)}else"img"===r.name&&(this.opts[2]&&this.$set(this.ctrl,e,-1),this.checkReady());this.root&&this.root.$emit("error",{source:r.name,attrs:r.attrs,errMsg:t.detail.errMsg})}}};e.default=a}).call(this,r("df3c")["default"])},fdf8:function(t,e,r){"use strict";var i=r("a7e0"),o=r.n(i);o.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uni_modules/mp-html/components/mp-html/node/node-create-component',
    {
        'uni_modules/mp-html/components/mp-html/node/node-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("931e"))
        })
    },
    [['uni_modules/mp-html/components/mp-html/node/node-create-component']]
]);
