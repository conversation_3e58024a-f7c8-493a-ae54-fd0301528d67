{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/columnGoods/HotNewGoods/index.vue?5c49", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/columnGoods/HotNewGoods/index.vue?3086", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/columnGoods/HotNewGoods/index.vue?259a", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/columnGoods/HotNewGoods/index.vue?39db", "uni-app:///pages/columnGoods/HotNewGoods/index.vue", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/columnGoods/HotNewGoods/index.vue?df6f", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/columnGoods/HotNewGoods/index.vue?7855"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "components", "GoodList", "emptyPage", "props", "data", "imgUrls", "goodsList", "icon", "type", "autoplay", "circular", "interval", "duration", "page", "limit", "isScroll", "onLoad", "methods", "titleInfo", "uni", "title", "getIndexGroomList", "that", "catch", "onReachBottom"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACmM;AACnM,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAAkwB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;AC+BtxB;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eACA;EACAC;EACAC;IACAC;IACAC;EACA;EACAC;EACAC;IACA;MACAC;MACAC;MACAP;MACAQ;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACAC;MACA;QACA;QACA;QACAC;UACAC;QACA;MACA;QACA;QACA;QACAD;UACAC;QACA;MACA;QACA;QACA;QACAD;UACAC;QACA;MACA;QACA;QACA;QACAD;UACAC;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACAR;QACAC;MACA;QACAQ;QACAA;QACAA;QACAA;MACA,GACAC;QACAD;UAAAF;QAAA;MACA;IACA;EACA;EACAI;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7GA;AAAA;AAAA;AAAA;AAA66C,CAAgB,4tCAAG,EAAC,C;;;;;;;;;;;ACAj8C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/columnGoods/HotNewGoods/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/columnGoods/HotNewGoods/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=05dcbcce&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/columnGoods/HotNewGoods/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=05dcbcce&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.goodsList.length > 0 && !_vm.isScroll\n  var g1 = _vm.goodsList.length == 0 && !_vm.isScroll\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <div class=\"quality-recommend\">\r\n    <div class=\"slider-banner swiper\">\r\n\t\t\t<view class=\"swiper\">\r\n\t\t\t\t<swiper indicator-dots=\"true\" :autoplay=\"autoplay\" :circular=\"circular\" :interval=\"interval\" :duration=\"duration\"\r\n\t\t\t\t indicator-color=\"rgba(255,255,255,0.6)\" indicator-active-color=\"#fff\">\r\n\t\t\t\t\t<block v-for=\"(item,index) in imgUrls\" :key=\"index\">\r\n\t\t\t\t\t\t<swiper-item>\r\n\t\t\t\t\t\t\t<image :src=\"item.pic\" class=\"slide-image\"></image>\r\n\t\t\t\t\t\t</swiper-item>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</swiper>\r\n\t\t\t</view>\r\n    </div>\r\n    <div class=\"title acea-row row-center-wrapper\">\r\n      <div class=\"line\"></div>\r\n      <div class=\"name\">\r\n        <span class=\"iconfont\" :class=\"icon\"></span>{{ name }}\r\n      </div>\r\n      <div class=\"line\"></div>\r\n    </div>\r\n\t\t<view class=\"wrapper\">\r\n\t\t\t<GoodList  :bastList=\"goodsList\" :is-sort=\"false\"></GoodList>\r\n\t\t\t<view class=\"txt-bar\" v-if=\"goodsList.length>0 && !isScroll\">我是有底线的~</view>\r\n\t\t\t<emptyPage v-if=\"goodsList.length==0 && !isScroll\" title=\"暂无数据~\"></emptyPage>\r\n\t\t</view>\r\n  </div>\r\n</template>\r\n<script>\r\nimport emptyPage from '@/components/emptyPage.vue'\r\nimport GoodList from \"@/components/goodList\";\r\nimport { getGroomList } from \"@/api/store\";\r\nexport default {\r\n  name: \"HotNewGoods\",\r\n  components: {\r\n    GoodList,\r\n\t\temptyPage,\r\n  },\r\n  props: {},\r\n  data: function() {\r\n    return {\r\n      imgUrls: [],\r\n      goodsList: [],\r\n      name: \"\",\r\n      icon: \"\",\r\n\t\t\ttype:0,\r\n\t\t\tautoplay:true,\r\n\t\t\tcircular:true,\r\n\t\t\tinterval: 3000,\r\n\t\t\tduration: 500,\r\n\t\t\tpage:1,\r\n\t\t\tlimit:8,\r\n\t\t\tisScroll:true\r\n    };\r\n  },\r\n  onLoad: function(option) {\r\n\tthis.type = option.type\r\n    this.titleInfo();\r\n    this.getIndexGroomList();\r\n  },\r\n  methods: {\r\n    titleInfo: function() {\r\n      if (this.type === \"1\") {\r\n        this.name = \"精品推荐\";\r\n        this.icon = \"icon-jingpintuijian\";\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle:\"精品推荐\"\r\n\t\t\t\t})\r\n      } else if (this.type === \"2\") {\r\n        this.name = \"热门榜单\";\r\n        this.icon = \"icon-remen\";\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle:\"热门榜单\"\r\n\t\t\t\t})\r\n      } else if (this.type === \"3\") {\r\n        this.name = \"首发新品\";\r\n        this.icon = \"icon-xinpin\";\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle:\"首发新品\"\r\n\t\t\t\t})\r\n      }else if (this.type === \"4\") {\r\n        this.name = \"促销单品\";\r\n        this.icon = \"icon-xinpin\";\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle:\"促销单品\"\r\n\t\t\t\t})\r\n      }\r\n    },\r\n    getIndexGroomList: function() {\r\n\t\t\tif(!this.isScroll) return\r\n      let that = this;\r\n      let type = this.type;\r\n      getGroomList(type,{\r\n\t\t\t\tpage:this.page,\r\n\t\t\t\tlimit:this.limit\r\n\t\t\t}).then(res => {\r\n          that.imgUrls = res.data.banner;\r\n          that.goodsList = that.goodsList.concat(res.data.list);\r\n\t\t\t\t\tthat.isScroll = res.data.list.length>=that.limit\r\n\t\t\t\t\tthat.page++\r\n        })\r\n        .catch(function(res) {\r\n          that.$util.Tips({ title: res });\r\n        });\r\n    }\r\n  },\r\n\tonReachBottom() {\r\n\t\tthis.getIndexGroomList()\r\n\t}\r\n};\r\n</script>\r\n<style lang=\"scss\">\r\n\t/deep/ .empty-box{\r\n\t\tbackground-color: #f5f5f5;\r\n\t} \r\n\t.swiper,swiper,swiper-item,.slide-image{\r\n\t\twidth: 100%;\r\n\t\theight: 280rpx;\r\n\t}\r\n\t.quality-recommend {\r\n\t\t.wrapper{\r\n\t\t\tbackground: #fff;\r\n\t\t}\r\n\t\t.title {\r\n\t    height: 120rpx;\r\n\t    font-size:32rpx;\r\n\t    color: #282828;\r\n\t    background-color: #f5f5f5;\r\n\t\t\t.line{\r\n\t\t\t\twidth: 230rpx;\r\n\t\t\t\theight: 2rpx;\r\n\t\t\t\tbackground-color: #e9e9e9;\r\n\t\t\t}\r\n\t\t}\t\r\n\t}\r\n\t.txt-bar{\r\n\t\tpadding: 20rpx 0;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #666;\r\n\t\tbackground-color: #f5f5f5;\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363902853\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}