{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\taoBao.vue?vue&type=template&id=74cf9dd4&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\taoBao.vue", "mtime": 1753666157925}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"Box\" },\n    [\n      _c(\"el-card\", [\n        _c(\n          \"div\",\n          { staticClass: \"line-ht\" },\n          [\n            _vm._v(\"生成的商品默认是没有上架的，请手动上架商品！\\n      \"),\n            _vm.copyConfig.copyType && _vm.copyConfig.copyType == 1\n              ? _c(\n                  \"span\",\n                  [\n                    _vm._v(\n                      \"您当前剩余\" +\n                        _vm._s(_vm.copyConfig.copyNum) +\n                        \"条采集次数，\\n        \"\n                    ),\n                    _c(\n                      \"router-link\",\n                      {\n                        attrs: {\n                          to: { path: \"/operation/systemSms/pay?type=copy\" },\n                        },\n                      },\n                      [\n                        _c(\"span\", { staticStyle: { color: \"#1890ff\" } }, [\n                          _vm._v(\"增加采集次数\"),\n                        ]),\n                      ]\n                    ),\n                  ],\n                  1\n                )\n              : _vm._e(),\n            _vm._v(\" \"),\n            _vm.copyConfig.copyType && _vm.copyConfig.copyType != 1\n              ? _c(\n                  \"el-link\",\n                  {\n                    attrs: {\n                      type: \"primary\",\n                      underline: false,\n                      href: \"https://help.crmeb.net/crmeb_java/2103903\",\n                      target: \"_blank\",\n                    },\n                  },\n                  [_vm._v(\"如何配置密钥\\n      \")]\n                )\n              : _vm._e(),\n            _vm._v(\" \"),\n            _c(\"br\"),\n            _vm._v(\n              \"\\n      商品采集设置：设置 > 系统设置 > 第三方接口设置 > 采集商品配置（如配置一号通采集，请先登录一号通账号，无一号通，请选择99Api设置）\\n    \"\n            ),\n          ],\n          1\n        ),\n      ]),\n      _vm._v(\" \"),\n      _c(\n        \"el-form\",\n        {\n          directives: [\n            {\n              name: \"loading\",\n              rawName: \"v-loading\",\n              value: _vm.loading,\n              expression: \"loading\",\n            },\n          ],\n          ref: \"formValidate\",\n          staticClass: \"formValidate mt20\",\n          attrs: {\n            model: _vm.formValidate,\n            rules: _vm.ruleInline,\n            \"label-width\": \"120px\",\n          },\n          nativeOn: {\n            submit: function ($event) {\n              $event.preventDefault()\n            },\n          },\n        },\n        [\n          _vm.copyConfig.copyType && _vm.copyConfig.copyType != 1\n            ? _c(\n                \"el-form-item\",\n                [\n                  _c(\n                    \"el-radio-group\",\n                    {\n                      model: {\n                        value: _vm.form,\n                        callback: function ($$v) {\n                          _vm.form = $$v\n                        },\n                        expression: \"form\",\n                      },\n                    },\n                    [\n                      _c(\"el-radio\", { attrs: { label: 1 } }, [_vm._v(\"淘宝\")]),\n                      _vm._v(\" \"),\n                      _c(\"el-radio\", { attrs: { label: 2 } }, [_vm._v(\"京东\")]),\n                      _vm._v(\" \"),\n                      _c(\"el-radio\", { attrs: { label: 5 } }, [_vm._v(\"天猫\")]),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              )\n            : _vm._e(),\n          _vm._v(\" \"),\n          _c(\n            \"el-row\",\n            { attrs: { gutter: 24 } },\n            [\n              _vm.copyConfig.copyType\n                ? _c(\n                    \"el-col\",\n                    { attrs: { span: 24 } },\n                    [\n                      _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"链接地址：\" } },\n                        [\n                          _c(\n                            \"el-input\",\n                            {\n                              staticClass: \"selWidth\",\n                              attrs: {\n                                placeholder: \"请输入链接地址\",\n                                size: \"small\",\n                              },\n                              model: {\n                                value: _vm.url,\n                                callback: function ($$v) {\n                                  _vm.url = $$v\n                                },\n                                expression: \"url\",\n                              },\n                            },\n                            [\n                              _c(\"el-button\", {\n                                directives: [\n                                  {\n                                    name: \"hasPermi\",\n                                    rawName: \"v-hasPermi\",\n                                    value: [\n                                      \"admin:product:copy:product\",\n                                      \"admin:product:import:product\",\n                                    ],\n                                    expression:\n                                      \"['admin:product:copy:product','admin:product:import:product']\",\n                                  },\n                                ],\n                                attrs: {\n                                  slot: \"append\",\n                                  icon: \"el-icon-search\",\n                                  size: \"small\",\n                                },\n                                on: { click: _vm.add },\n                                slot: \"append\",\n                              }),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  )\n                : _vm._e(),\n              _vm._v(\" \"),\n              _vm.formValidate\n                ? _c(\n                    \"el-col\",\n                    [\n                      _c(\n                        \"el-col\",\n                        { attrs: { span: 24 } },\n                        [\n                          _c(\n                            \"el-form-item\",\n                            {\n                              attrs: { label: \"商品名称：\", prop: \"storeName\" },\n                            },\n                            [\n                              _c(\"el-input\", {\n                                attrs: {\n                                  maxlength: \"249\",\n                                  placeholder: \"请输入商品名称\",\n                                },\n                                model: {\n                                  value: _vm.formValidate.storeName,\n                                  callback: function ($$v) {\n                                    _vm.$set(_vm.formValidate, \"storeName\", $$v)\n                                  },\n                                  expression: \"formValidate.storeName\",\n                                },\n                              }),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-col\",\n                        { attrs: { span: 24 } },\n                        [\n                          _c(\n                            \"el-form-item\",\n                            { attrs: { label: \"商品简介：\" } },\n                            [\n                              _c(\"el-input\", {\n                                attrs: {\n                                  maxlength: \"250\",\n                                  type: \"textarea\",\n                                  rows: 3,\n                                  placeholder: \"请输入商品简介\",\n                                },\n                                model: {\n                                  value: _vm.formValidate.storeInfo,\n                                  callback: function ($$v) {\n                                    _vm.$set(_vm.formValidate, \"storeInfo\", $$v)\n                                  },\n                                  expression: \"formValidate.storeInfo\",\n                                },\n                              }),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-col\",\n                        { attrs: { span: 24 } },\n                        [\n                          _c(\n                            \"el-form-item\",\n                            { attrs: { label: \"商品分类：\", prop: \"cateIds\" } },\n                            [\n                              _c(\"el-cascader\", {\n                                staticClass: \"selWidth\",\n                                attrs: {\n                                  options: _vm.merCateList,\n                                  props: _vm.props2,\n                                  clearable: \"\",\n                                  \"show-all-levels\": false,\n                                },\n                                model: {\n                                  value: _vm.formValidate.cateIds,\n                                  callback: function ($$v) {\n                                    _vm.$set(_vm.formValidate, \"cateIds\", $$v)\n                                  },\n                                  expression: \"formValidate.cateIds\",\n                                },\n                              }),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-col\",\n                        { attrs: { span: 24 } },\n                        [\n                          _c(\n                            \"el-form-item\",\n                            {\n                              attrs: { label: \"商品关键字：\", prop: \"keyword\" },\n                            },\n                            [\n                              _c(\"el-input\", {\n                                attrs: { placeholder: \"请输入商品关键字\" },\n                                model: {\n                                  value: _vm.formValidate.keyword,\n                                  callback: function ($$v) {\n                                    _vm.$set(_vm.formValidate, \"keyword\", $$v)\n                                  },\n                                  expression: \"formValidate.keyword\",\n                                },\n                              }),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-col\",\n                        _vm._b({}, \"el-col\", _vm.grid, false),\n                        [\n                          _c(\n                            \"el-form-item\",\n                            { attrs: { label: \"单位：\", prop: \"unitName\" } },\n                            [\n                              _c(\"el-input\", {\n                                staticClass: \"selWidth\",\n                                attrs: { placeholder: \"请输入单位\" },\n                                model: {\n                                  value: _vm.formValidate.unitName,\n                                  callback: function ($$v) {\n                                    _vm.$set(_vm.formValidate, \"unitName\", $$v)\n                                  },\n                                  expression: \"formValidate.unitName\",\n                                },\n                              }),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-col\",\n                        _vm._b({}, \"el-col\", _vm.grid, false),\n                        [\n                          _c(\n                            \"el-form-item\",\n                            { attrs: { label: \"运费模板：\", prop: \"tempId\" } },\n                            [\n                              _c(\n                                \"el-select\",\n                                {\n                                  staticClass: \"selWidth\",\n                                  attrs: { placeholder: \"请选择\" },\n                                  model: {\n                                    value: _vm.formValidate.tempId,\n                                    callback: function ($$v) {\n                                      _vm.$set(_vm.formValidate, \"tempId\", $$v)\n                                    },\n                                    expression: \"formValidate.tempId\",\n                                  },\n                                },\n                                _vm._l(_vm.shippingList, function (item) {\n                                  return _c(\"el-option\", {\n                                    key: item.id,\n                                    attrs: { label: item.name, value: item.id },\n                                  })\n                                }),\n                                1\n                              ),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-col\",\n                        { attrs: { span: 24 } },\n                        [\n                          _c(\n                            \"el-form-item\",\n                            { attrs: { label: \"商品封面图：\", prop: \"image\" } },\n                            [\n                              _c(\n                                \"div\",\n                                {\n                                  staticClass: \"upLoadPicBox\",\n                                  on: {\n                                    click: function ($event) {\n                                      return _vm.modalPicTap(\"1\")\n                                    },\n                                  },\n                                },\n                                [\n                                  _vm.formValidate.image\n                                    ? _c(\"div\", { staticClass: \"pictrue\" }, [\n                                        _c(\"img\", {\n                                          attrs: {\n                                            src: _vm.formValidate.image,\n                                          },\n                                        }),\n                                      ])\n                                    : _c(\"div\", { staticClass: \"upLoad\" }, [\n                                        _c(\"i\", {\n                                          staticClass:\n                                            \"el-icon-camera cameraIconfont\",\n                                        }),\n                                      ]),\n                                ]\n                              ),\n                            ]\n                          ),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-col\",\n                        { attrs: { span: 24 } },\n                        [\n                          _c(\n                            \"el-form-item\",\n                            { attrs: { label: \"商品轮播图：\" } },\n                            [\n                              _c(\n                                \"div\",\n                                { staticClass: \"acea-row\" },\n                                _vm._l(\n                                  _vm.formValidate.sliderImages,\n                                  function (item, index) {\n                                    return _c(\n                                      \"div\",\n                                      {\n                                        key: index,\n                                        staticClass: \"lunBox mr5\",\n                                        attrs: { draggable: \"false\" },\n                                        on: {\n                                          dragstart: function ($event) {\n                                            return _vm.handleDragStart(\n                                              $event,\n                                              item\n                                            )\n                                          },\n                                          dragover: function ($event) {\n                                            $event.preventDefault()\n                                            return _vm.handleDragOver(\n                                              $event,\n                                              item\n                                            )\n                                          },\n                                          dragenter: function ($event) {\n                                            return _vm.handleDragEnter(\n                                              $event,\n                                              item\n                                            )\n                                          },\n                                          dragend: function ($event) {\n                                            return _vm.handleDragEnd(\n                                              $event,\n                                              item\n                                            )\n                                          },\n                                        },\n                                      },\n                                      [\n                                        _c(\"div\", { staticClass: \"pictrue\" }, [\n                                          _c(\"img\", { attrs: { src: item } }),\n                                        ]),\n                                        _vm._v(\" \"),\n                                        _c(\n                                          \"el-button-group\",\n                                          [\n                                            _c(\n                                              \"el-button\",\n                                              {\n                                                attrs: { size: \"mini\" },\n                                                nativeOn: {\n                                                  click: function ($event) {\n                                                    return _vm.checked(\n                                                      item,\n                                                      index\n                                                    )\n                                                  },\n                                                },\n                                              },\n                                              [_vm._v(\"主图\")]\n                                            ),\n                                            _vm._v(\" \"),\n                                            _c(\n                                              \"el-button\",\n                                              {\n                                                attrs: { size: \"mini\" },\n                                                nativeOn: {\n                                                  click: function ($event) {\n                                                    return _vm.handleRemove(\n                                                      index\n                                                    )\n                                                  },\n                                                },\n                                              },\n                                              [_vm._v(\"移除\")]\n                                            ),\n                                          ],\n                                          1\n                                        ),\n                                      ],\n                                      1\n                                    )\n                                  }\n                                ),\n                                0\n                              ),\n                            ]\n                          ),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _vm.formValidate.specType || _vm.formValidate.attr.length\n                        ? _c(\n                            \"el-col\",\n                            { staticClass: \"noForm\", attrs: { span: 24 } },\n                            [\n                              _c(\n                                \"el-form-item\",\n                                {\n                                  staticClass: \"labeltop\",\n                                  attrs: { label: \"批量设置：\" },\n                                },\n                                [\n                                  _c(\n                                    \"el-table\",\n                                    {\n                                      staticClass: \"tabNumWidth\",\n                                      attrs: {\n                                        data: _vm.oneFormBatch,\n                                        border: \"\",\n                                        size: \"mini\",\n                                      },\n                                    },\n                                    [\n                                      _c(\"el-table-column\", {\n                                        attrs: {\n                                          align: \"center\",\n                                          label: \"图片\",\n                                          \"min-width\": \"80\",\n                                        },\n                                        scopedSlots: _vm._u(\n                                          [\n                                            {\n                                              key: \"default\",\n                                              fn: function (scope) {\n                                                return [\n                                                  _c(\n                                                    \"div\",\n                                                    {\n                                                      staticClass:\n                                                        \"upLoadPicBox\",\n                                                      on: {\n                                                        click: function (\n                                                          $event\n                                                        ) {\n                                                          return _vm.modalPicTap(\n                                                            \"1\",\n                                                            \"pi\"\n                                                          )\n                                                        },\n                                                      },\n                                                    },\n                                                    [\n                                                      scope.row.image\n                                                        ? _c(\n                                                            \"div\",\n                                                            {\n                                                              staticClass:\n                                                                \"pictrue pictrueTab\",\n                                                            },\n                                                            [\n                                                              _c(\"img\", {\n                                                                attrs: {\n                                                                  src: scope.row\n                                                                    .image,\n                                                                },\n                                                              }),\n                                                            ]\n                                                          )\n                                                        : _c(\n                                                            \"div\",\n                                                            {\n                                                              staticClass:\n                                                                \"upLoad pictrueTab\",\n                                                            },\n                                                            [\n                                                              _c(\"i\", {\n                                                                staticClass:\n                                                                  \"el-icon-camera cameraIconfont\",\n                                                              }),\n                                                            ]\n                                                          ),\n                                                    ]\n                                                  ),\n                                                ]\n                                              },\n                                            },\n                                          ],\n                                          null,\n                                          false,\n                                          3789294462\n                                        ),\n                                      }),\n                                      _vm._v(\" \"),\n                                      _vm._l(\n                                        _vm.attrValue,\n                                        function (item, iii) {\n                                          return _c(\"el-table-column\", {\n                                            key: iii,\n                                            attrs: {\n                                              label: _vm.formThead[iii].title,\n                                              align: \"center\",\n                                              \"min-width\": \"120\",\n                                            },\n                                            scopedSlots: _vm._u(\n                                              [\n                                                {\n                                                  key: \"default\",\n                                                  fn: function (scope) {\n                                                    return [\n                                                      _c(\"el-input\", {\n                                                        staticClass: \"priceBox\",\n                                                        attrs: {\n                                                          type:\n                                                            _vm.formThead[iii]\n                                                              .title ===\n                                                            \"商品编号\"\n                                                              ? \"text\"\n                                                              : \"number\",\n                                                          min: 0,\n                                                        },\n                                                        model: {\n                                                          value: scope.row[iii],\n                                                          callback: function (\n                                                            $$v\n                                                          ) {\n                                                            _vm.$set(\n                                                              scope.row,\n                                                              iii,\n                                                              $$v\n                                                            )\n                                                          },\n                                                          expression:\n                                                            \"scope.row[iii]\",\n                                                        },\n                                                      }),\n                                                    ]\n                                                  },\n                                                },\n                                              ],\n                                              null,\n                                              true\n                                            ),\n                                          })\n                                        }\n                                      ),\n                                      _vm._v(\" \"),\n                                      _c(\n                                        \"el-table-column\",\n                                        {\n                                          attrs: {\n                                            align: \"center\",\n                                            label: \"操作\",\n                                            \"min-width\": \"80\",\n                                          },\n                                        },\n                                        [\n                                          [\n                                            _c(\n                                              \"el-button\",\n                                              {\n                                                staticClass: \"submission\",\n                                                attrs: { type: \"text\" },\n                                                on: { click: _vm.batchAdd },\n                                              },\n                                              [_vm._v(\"批量添加\")]\n                                            ),\n                                          ],\n                                        ],\n                                        2\n                                      ),\n                                    ],\n                                    2\n                                  ),\n                                ],\n                                1\n                              ),\n                            ],\n                            1\n                          )\n                        : _vm._e(),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-col\",\n                        { attrs: { span: 24 } },\n                        [\n                          _c(\n                            \"el-form-item\",\n                            {\n                              attrs: {\n                                label: \"商品规格：\",\n                                props: \"spec_type\",\n                                \"label-for\": \"spec_type\",\n                              },\n                            },\n                            [\n                              _c(\n                                \"el-table\",\n                                {\n                                  staticClass: \"tabNumWidth\",\n                                  attrs: {\n                                    data: _vm.formValidate.attrValue,\n                                    border: \"\",\n                                    size: \"mini\",\n                                  },\n                                },\n                                [\n                                  _vm.manyTabDate\n                                    ? _vm._l(\n                                        _vm.manyTabDate,\n                                        function (item, iii) {\n                                          return _c(\"el-table-column\", {\n                                            key: iii,\n                                            attrs: {\n                                              align: \"center\",\n                                              label: _vm.manyTabTit[iii].title,\n                                              \"min-width\": \"80\",\n                                            },\n                                            scopedSlots: _vm._u(\n                                              [\n                                                {\n                                                  key: \"default\",\n                                                  fn: function (scope) {\n                                                    return [\n                                                      _c(\"span\", {\n                                                        staticClass: \"priceBox\",\n                                                        domProps: {\n                                                          textContent: _vm._s(\n                                                            scope.row[iii]\n                                                          ),\n                                                        },\n                                                      }),\n                                                    ]\n                                                  },\n                                                },\n                                              ],\n                                              null,\n                                              true\n                                            ),\n                                          })\n                                        }\n                                      )\n                                    : _vm._e(),\n                                  _vm._v(\" \"),\n                                  _c(\"el-table-column\", {\n                                    attrs: {\n                                      align: \"center\",\n                                      label: \"图片\",\n                                      \"min-width\": \"80\",\n                                    },\n                                    scopedSlots: _vm._u(\n                                      [\n                                        {\n                                          key: \"default\",\n                                          fn: function (scope) {\n                                            return [\n                                              _c(\n                                                \"el-form-item\",\n                                                {\n                                                  attrs: {\n                                                    rules: [\n                                                      {\n                                                        required: true,\n                                                        message: \"请上传图片\",\n                                                        trigger: \"change\",\n                                                      },\n                                                    ],\n                                                    prop:\n                                                      \"attrValue.\" +\n                                                      scope.$index +\n                                                      \".image\",\n                                                  },\n                                                },\n                                                [\n                                                  _c(\n                                                    \"div\",\n                                                    {\n                                                      staticClass:\n                                                        \"upLoadPicBox\",\n                                                      on: {\n                                                        click: function (\n                                                          $event\n                                                        ) {\n                                                          return _vm.modalPicTap(\n                                                            \"1\",\n                                                            \"duo\",\n                                                            scope.$index\n                                                          )\n                                                        },\n                                                      },\n                                                    },\n                                                    [\n                                                      scope.row.image\n                                                        ? _c(\n                                                            \"div\",\n                                                            {\n                                                              staticClass:\n                                                                \"pictrue pictrueTab\",\n                                                            },\n                                                            [\n                                                              _c(\"img\", {\n                                                                attrs: {\n                                                                  src: scope.row\n                                                                    .image,\n                                                                },\n                                                              }),\n                                                            ]\n                                                          )\n                                                        : _c(\n                                                            \"div\",\n                                                            {\n                                                              staticClass:\n                                                                \"upLoad pictrueTab\",\n                                                            },\n                                                            [\n                                                              _c(\"i\", {\n                                                                staticClass:\n                                                                  \"el-icon-camera cameraIconfont\",\n                                                              }),\n                                                            ]\n                                                          ),\n                                                    ]\n                                                  ),\n                                                ]\n                                              ),\n                                            ]\n                                          },\n                                        },\n                                      ],\n                                      null,\n                                      false,\n                                      799156793\n                                    ),\n                                  }),\n                                  _vm._v(\" \"),\n                                  _vm._l(_vm.attrValue, function (item, iii) {\n                                    return _c(\"el-table-column\", {\n                                      key: iii,\n                                      attrs: {\n                                        label: _vm.formThead[iii].title,\n                                        align: \"center\",\n                                        \"min-width\": \"120\",\n                                      },\n                                      scopedSlots: _vm._u(\n                                        [\n                                          {\n                                            key: \"default\",\n                                            fn: function (scope) {\n                                              return [\n                                                _c(\n                                                  \"el-form-item\",\n                                                  {\n                                                    attrs: {\n                                                      rules: [\n                                                        {\n                                                          required: true,\n                                                          message:\n                                                            \"请输入\" +\n                                                            _vm.formThead[iii]\n                                                              .title,\n                                                          trigger: \"blur\",\n                                                        },\n                                                      ],\n                                                      prop:\n                                                        _vm.formThead[iii]\n                                                          .title !== \"商品编号\"\n                                                          ? \"attrValue.\" +\n                                                            scope.$index +\n                                                            \".\" +\n                                                            iii\n                                                          : \"\",\n                                                    },\n                                                  },\n                                                  [\n                                                    _c(\"el-input\", {\n                                                      staticClass: \"priceBox\",\n                                                      attrs: {\n                                                        type:\n                                                          _vm.formThead[iii]\n                                                            .title ===\n                                                          \"商品编号\"\n                                                            ? \"text\"\n                                                            : \"number\",\n                                                      },\n                                                      model: {\n                                                        value: scope.row[iii],\n                                                        callback: function (\n                                                          $$v\n                                                        ) {\n                                                          _vm.$set(\n                                                            scope.row,\n                                                            iii,\n                                                            $$v\n                                                          )\n                                                        },\n                                                        expression:\n                                                          \"scope.row[iii]\",\n                                                      },\n                                                    }),\n                                                  ],\n                                                  1\n                                                ),\n                                              ]\n                                            },\n                                          },\n                                        ],\n                                        null,\n                                        true\n                                      ),\n                                    })\n                                  }),\n                                  _vm._v(\" \"),\n                                  _c(\"el-table-column\", {\n                                    attrs: {\n                                      align: \"center\",\n                                      label: \"操作\",\n                                      \"min-width\": \"80\",\n                                    },\n                                    scopedSlots: _vm._u(\n                                      [\n                                        {\n                                          key: \"default\",\n                                          fn: function (scope) {\n                                            return [\n                                              _c(\n                                                \"el-button\",\n                                                {\n                                                  staticClass: \"submission\",\n                                                  attrs: { type: \"text\" },\n                                                  on: {\n                                                    click: function ($event) {\n                                                      return _vm.delAttrTable(\n                                                        scope.$index\n                                                      )\n                                                    },\n                                                  },\n                                                },\n                                                [_vm._v(\"删除\")]\n                                              ),\n                                            ]\n                                          },\n                                        },\n                                      ],\n                                      null,\n                                      false,\n                                      2803824461\n                                    ),\n                                  }),\n                                ],\n                                2\n                              ),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-col\",\n                        { attrs: { span: 24 } },\n                        [\n                          _c(\n                            \"el-form-item\",\n                            { attrs: { label: \"商品详情：\" } },\n                            [\n                              _c(\"Tinymce\", {\n                                model: {\n                                  value: _vm.formValidate.content,\n                                  callback: function ($$v) {\n                                    _vm.$set(_vm.formValidate, \"content\", $$v)\n                                  },\n                                  expression: \"formValidate.content\",\n                                },\n                              }),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-col\",\n                        { attrs: { span: 24 } },\n                        [\n                          [\n                            _c(\n                              \"el-row\",\n                              { attrs: { gutter: 24 } },\n                              [\n                                _c(\n                                  \"el-col\",\n                                  { attrs: { span: 24 } },\n                                  [\n                                    [\n                                      _c(\n                                        \"el-row\",\n                                        { attrs: { gutter: 24 } },\n                                        [\n                                          _c(\n                                            \"el-col\",\n                                            { attrs: { span: 8 } },\n                                            [\n                                              _c(\n                                                \"el-form-item\",\n                                                { attrs: { label: \"排序：\" } },\n                                                [\n                                                  _c(\"el-input-number\", {\n                                                    attrs: {\n                                                      max: 9999,\n                                                      placeholder: \"请输入排序\",\n                                                      disabled: _vm.isDisabled,\n                                                    },\n                                                    model: {\n                                                      value:\n                                                        _vm.formValidate.sort,\n                                                      callback: function ($$v) {\n                                                        _vm.$set(\n                                                          _vm.formValidate,\n                                                          \"sort\",\n                                                          $$v\n                                                        )\n                                                      },\n                                                      expression:\n                                                        \"formValidate.sort\",\n                                                    },\n                                                  }),\n                                                ],\n                                                1\n                                              ),\n                                            ],\n                                            1\n                                          ),\n                                          _vm._v(\" \"),\n                                          _c(\n                                            \"el-col\",\n                                            { attrs: { span: 8 } },\n                                            [\n                                              _c(\n                                                \"el-form-item\",\n                                                { attrs: { label: \"积分：\" } },\n                                                [\n                                                  _c(\"el-input-number\", {\n                                                    attrs: {\n                                                      placeholder: \"请输入排序\",\n                                                      disabled: _vm.isDisabled,\n                                                    },\n                                                    model: {\n                                                      value:\n                                                        _vm.formValidate\n                                                          .giveIntegral,\n                                                      callback: function ($$v) {\n                                                        _vm.$set(\n                                                          _vm.formValidate,\n                                                          \"giveIntegral\",\n                                                          $$v\n                                                        )\n                                                      },\n                                                      expression:\n                                                        \"formValidate.giveIntegral\",\n                                                    },\n                                                  }),\n                                                ],\n                                                1\n                                              ),\n                                            ],\n                                            1\n                                          ),\n                                          _vm._v(\" \"),\n                                          _c(\n                                            \"el-col\",\n                                            { attrs: { span: 8 } },\n                                            [\n                                              _c(\n                                                \"el-form-item\",\n                                                {\n                                                  attrs: {\n                                                    label: \"虚拟销量：\",\n                                                  },\n                                                },\n                                                [\n                                                  _c(\"el-input-number\", {\n                                                    attrs: {\n                                                      placeholder: \"请输入排序\",\n                                                      disabled: _vm.isDisabled,\n                                                    },\n                                                    model: {\n                                                      value:\n                                                        _vm.formValidate.ficti,\n                                                      callback: function ($$v) {\n                                                        _vm.$set(\n                                                          _vm.formValidate,\n                                                          \"ficti\",\n                                                          $$v\n                                                        )\n                                                      },\n                                                      expression:\n                                                        \"formValidate.ficti\",\n                                                    },\n                                                  }),\n                                                ],\n                                                1\n                                              ),\n                                            ],\n                                            1\n                                          ),\n                                        ],\n                                        1\n                                      ),\n                                    ],\n                                  ],\n                                  2\n                                ),\n                                _vm._v(\" \"),\n                                _c(\n                                  \"el-col\",\n                                  { attrs: { span: 24 } },\n                                  [\n                                    _c(\n                                      \"el-form-item\",\n                                      { attrs: { label: \"商品推荐：\" } },\n                                      [\n                                        _c(\n                                          \"el-checkbox-group\",\n                                          {\n                                            attrs: {\n                                              size: \"small\",\n                                              disabled: _vm.isDisabled,\n                                            },\n                                            on: { change: _vm.onChangeGroup },\n                                            model: {\n                                              value: _vm.checkboxGroup,\n                                              callback: function ($$v) {\n                                                _vm.checkboxGroup = $$v\n                                              },\n                                              expression: \"checkboxGroup\",\n                                            },\n                                          },\n                                          _vm._l(\n                                            _vm.recommend,\n                                            function (item, index) {\n                                              return _c(\n                                                \"el-checkbox\",\n                                                {\n                                                  key: index,\n                                                  attrs: { label: item.value },\n                                                },\n                                                [_vm._v(_vm._s(item.name))]\n                                              )\n                                            }\n                                          ),\n                                          1\n                                        ),\n                                      ],\n                                      1\n                                    ),\n                                  ],\n                                  1\n                                ),\n                              ],\n                              1\n                            ),\n                          ],\n                        ],\n                        2\n                      ),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-col\",\n                        { attrs: { span: 24 } },\n                        [\n                          _c(\n                            \"el-form-item\",\n                            [\n                              _c(\n                                \"el-button\",\n                                {\n                                  staticClass: \"submission\",\n                                  attrs: {\n                                    type: \"primary\",\n                                    loading: _vm.modal_loading,\n                                  },\n                                  on: {\n                                    click: function ($event) {\n                                      return _vm.handleSubmit(\"formValidate\")\n                                    },\n                                  },\n                                },\n                                [_vm._v(\"提交\\n            \")]\n                              ),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  )\n                : _vm._e(),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}