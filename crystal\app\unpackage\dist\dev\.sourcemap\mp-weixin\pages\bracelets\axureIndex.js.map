{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/bracelets/axureIndex.vue?9da6", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/bracelets/axureIndex.vue?0c72", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/bracelets/axureIndex.vue?4584", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/bracelets/axureIndex.vue?e01f", "uni-app:///pages/bracelets/axureIndex.vue", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/bracelets/axureIndex.vue?3506", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/bracelets/axureIndex.vue?6ac9"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "currentStep", "steps", "formData", "age", "gender", "occupation", "purpose", "budget", "hand", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "specialNeeds", "colorPreference", "materials", "occupationOptions", "purposeOptions", "colorOptions", "label", "value", "materialOptions", "chakraOptions", "color", "radius", "beadsList", "aiTaskId", "aiProgress", "generatingStatusText", "aiDescription", "pollingTimer", "totalPrice", "partialContent", "previousPartial<PERSON><PERSON><PERSON>", "md", "renderedMarkdown", "aiResponse", "outputScrollTop", "outputScrollId", "scrollIntoViewId", "formattedAiDescription", "aiTaskStatus", "errorMessage", "userRequirements", "force_update", "showRecommendationDetails", "htmlTagStyle", "h1", "h2", "h3", "p", "ul", "ol", "li", "blockquote", "strong", "em", "img", "onLoad", "setTimeout", "onUnload", "created", "watch", "computed", "aiProgressText", "methods", "setGenderMale", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toggleColorByIndex", "toggleColor", "toggleMaterialByIndex", "toggleMaterial", "toggleChakraByIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onOccupationChange", "onPurposeChange", "onBudgetChange", "submitForm", "uni", "title", "icon", "chakraText", "userId", "requirementType", "console", "submitStreamAiGenerationRequest", "startPollingPartialContent", "pollPartial<PERSON>ontent", "configureMark<PERSON><PERSON><PERSON><PERSON>", "token", "tokens", "renderMarkdown", "onScrollToBottom", "saveToMyBracelets", "id", "status", "generateMarbles", "sum", "generateMockRecommendation", "mockBeads", "name", "image", "width", "height", "effect", "price", "chakra", "getRandomBeadName", "getRandomEffect", "getMarbleStyle", "angleOffset", "position", "left", "top", "transform", "background", "backgroundSize", "transition", "editRecommendation", "nextStep", "placeOrder", "requirementId", "productInfo", "url", "clearPollingTimer", "clearInterval", "scrollToBottom", "scrollTop", "duration", "scrollRecommendationToView", "query", "updateUserRequirement", "updatedData", "generateNewRecommendation", "applyMarkdownStyles", "webview", "loadUserRequirements", "content", "success", "handleAiGenerationCompleted", "htmlContent"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACc;;;AAGvE;AACgM;AAChM,gBAAgB,2LAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,oSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxGA;AAAA;AAAA;AAAA;AAAwvB,CAAgB,0rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACkQ5wB;AAGA;AACA;AACA;AAEA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC,eACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACAC,kBACA;QAAAF;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACAE,gBACA;QAAAH;QAAAC;QAAAG;MAAA,GACA;QAAAJ;QAAAC;QAAAG;MAAA,GACA;QAAAJ;QAAAC;QAAAG;MAAA,GACA;QAAAJ;QAAAC;QAAAG;MAAA,GACA;QAAAJ;QAAAC;QAAAG;MAAA,GACA;QAAAJ;QAAAC;QAAAG;MAAA,GACA;QAAAJ;QAAAC;QAAAG;MAAA,EACA;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IAAA;IACA;IACA;;IAEA;IACAC;MACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA;;IAEA;IACA;EACA;;EACAC;IACA;IACAjC;MAAA;MACA;QACA;QACA;UACA;UACA;QACA;MACA;IACA;EACA;EACAkC,0CACA;IACA;IACAC;MACA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;EAAA,EACA;EACAC;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;QACA;QACA;MACA;MACA;MACA;QACAC;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAF;UACAC;UACAC;QACA;QACA;MACA;MAEA;QACAF;UACAC;UACAC;QACA;QACA;MACA;MACA;QACAF;UACAC;UACAC;QACA;QACA;MACA;;MAEA;MACA;MACA;QACA;QACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;QAEA;QACA;UACA;UACAC;QACA;QAEA;UACApE;QACA;UACAA;QACA;MACA;;MAEA;MACA;QACAqE;QACAC;QACA7E;QACAC;QACAC;QACAG;QACAF;QACAC;QACAI;QACAC;QACAF;MAAA,2DACA,+EACA,qBACA;MAEAiE;QACAC;MACA;;MAEA;MACAK;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;QACA;UACAP;UACAM;UACA;UACA;UACA;QACA;UACAA;UACA;UACA;UACA;UACA;QACA;MACA;QACAN;QACAM;QACA;QACA;QACA;QACA;MACA;IACA;IAEA;IACAE;MAAA;MACA;;MAEA;MACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;QACAH;QACA;MACA;MAEAA;MAEA;QACA;UACA;UACAA;;UAEA;UACA;UACA;UAEA;YACAA;YACA;UACA;;UAEA;UACA;YACA;UACA;;UAEA;UACA;YACA;;YAEA;YACA;cACAA;cACA;cACA;cACA;;cAEA;cACA;gBACA;cACA;YACA;UACA;;UAEA;UACA;YACAA;YACA;YACA;;YAEA;YACA;cACAA;cACA;cACA;YACA;cACAA;cACA;cACA;gBACAvD;cACA;cACA;YACA;UACA;YACAuD;YACA;YACA;YACA;YACA;YACA;YACA;UACA;QACA;UACAA;QACA;MACA;QACAA;QACA;QACA;QACA;UACAA;UACA;UACA;UACA;UACA;QACA;MACA;IACA;IAEA;IACAI;MACA;;MAEA;MACA;QACA;QACA;QACA;QACAC;QACA;MACA;MAEA;QACAC;QACA;MACA;MAEA;QACAA;QACA;MACA;MAEA;QACAA;QACA;MACA;MAEA;QACAA;QACA;MACA;MAEA;QACAA;QACA;MACA;MAEA;QACAA;QACA;MACA;MAEA;QACAA;QACA;MACA;MAEA;QACA;QACAD;QACA;MACA;IACA;IAEA;IACAE;MACA;MACA;QACAP;QACA;MACA;QACAA;QACA;MACA;IACA;IAEA;IACAQ;MACAR;IACA;IAEA;IACAS;MAAA;MACA;MACA;QACAf;UACAC;UACAC;QACA;QACA;MACA;;MAEA;MACA;QACAc;QACAxF;QACAC;QACAC;QACAC;QACAI;QACAH;QACA0B;QAAA;QACAzB;QACAoF;MACA;;MAEA;MACA;QACA;QACA;UACA;YACAjB;cACAC;cACAC;YACA;UACA;YACAF;cACAC;cACAC;YACA;UACA;QACA;UACAI;UACAN;YACAC;YACAC;UACA;QACA;MACA;QACA;QACA;UACA1E;UACAC;UACAC;UACAC;UACAI;UACAH;UACA0B;UAAA;UACAzB;UACAoF;QACA;;QAEA;UACA;YACA;YACA;YACAjB;cACAC;cACAC;YACA;UACA;YACAF;cACAC;cACAC;YACA;UACA;QACA;UACAI;UACAN;YACAC;YACAC;UACA;QACA;MACA;IACA;IAEA;IACAgB;MACA;QAAA;MAAA;QAAA;MAAA;MACA;MACA;QACA;QACA;MACA;MAEA;QAAA,OACAC;MAAA;MAEA;MACA;;MAEA;MACA;MACA;IACA;IAEAC;MAAA;MACA;MACA;MAEA;QACA;UACA;YAAA;UAAA;UAEA;UACA;YACA;YACAnE;YAEAoE;cACAL;cACAM;cACAC;cACAC;cACAC;cACAC;cACAC;cACAC;cACAnF;YACA;UACA;QACA;MACA;QACA;QACA;UACA;UACA;UACAQ;UAEAoE;YACAL;YACAM;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAnF;UACA;QACA;MACA;;MAEA;MACA;MACA;IAEA;IACAoF;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MAEA;MACA;IACA;IACAC;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MAEA;MACA;IACA;IAEAC;MACA;MACA;QAAA,OACAZ;MAAA;;MAEA;MACA;MACA;;MAEA;MACA;;MAEA;MACA;MACA;QACA;QACAa;MACA;MAEA;MACA;;MAEA;MACA;MACA;MAEA;QACAC;QACAC;QACAC;QACAX;QACAC;QACAW;QACAC;QACAC;QACAC;MACA;IACA;IAGAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA1C;QACAC;MACA;;MAEA;MACA;QACA0C;QAAA;QACAC;UACAjG;UACAM;UACAF;QACA;QACAE;MACA;;MAEA;MACAqD;;MAEA;MACAzB;QACAmB;QACAA;UACAC;UACAC;QACA;;QAEA;QACArB;UACAmB;YACA6C;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;QACAC;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA1C;MACA;QACA;QACA;;QAEA;QACA;UACAN;YACAiD;YACAC;UACA;QACA;UACA5C;QACA;;QAEA;QACAzB;UACA;UACAA;YACA;UACA;QACA;MACA;IACA;IACA;IACAsE;MAAA;MACA;QACA;UACA;UACAC;YACA;cACApD;gBACAiD;gBACAC;cACA;YACA;UACA;QACA;MACA;IACA;IACA;IACAG;MACA;MACA;QACAC;MACA;MAEA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MAEA;QACA;QACA;UACA;UACAC,+UAMA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;QACApD;QACA;MACA;MAEAN;QACAC;MACA;MAEA;QACAD;QACA;UACA;YAAA;UAAA;UACAM;;UAEA;UACA;YACAN;cACAC;cACA0D;cACAC;gBACA;kBACA;kBACA;gBACA;cACA;YACA;UACA;QACA;MACA;QACA5D;QACAM;MACA;IACA;IACA;IACAuD;MAAA;MACA;QACAvD;QAEAA;;QAEA;QACA;UACA;;UAEA;UACA;;UAEA;UACA;YACAwD;UACA;UAEA;;UAEA;UACA;QACA;;QAEA;QACA;QACA;QACAxD;;QAEA;QACA;;QAEA;QACA;UACAA;UACA;QACA;;QAEA;QACAzB;UAEA;YACA;UACA;UACA;YACA;UACA;UACA;UACAyB;UACA;UACA;QAEA;MACA;QACAA;QACA;QACA;QACA;QACAA;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AChuCA;AAAA;AAAA;AAAA;AAAu5C,CAAgB,iuCAAG,EAAC,C;;;;;;;;;;;ACA36C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/bracelets/axureIndex.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/bracelets/axureIndex.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./axureIndex.vue?vue&type=template&id=a9834dc4&\"\nvar renderjs\nimport script from \"./axureIndex.vue?vue&type=script&lang=js&\"\nexport * from \"./axureIndex.vue?vue&type=script&lang=js&\"\nimport style0 from \"./axureIndex.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/bracelets/axureIndex.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./axureIndex.vue?vue&type=template&id=a9834dc4&\"", "var components\ntry {\n  components = {\n    mpHtml: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/mp-html/components/mp-html/mp-html\" */ \"@/uni_modules/mp-html/components/mp-html/mp-html.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.steps.length\n  var l0 =\n    _vm.currentStep === 0\n      ? _vm.__map(_vm.colorOptions, function (color, index) {\n          var $orig = _vm.__get_orig(color)\n          var g1 = _vm.formData.colorPreference.includes(color.value)\n          return {\n            $orig: $orig,\n            g1: g1,\n          }\n        })\n      : null\n  var l1 =\n    _vm.currentStep === 0\n      ? _vm.__map(_vm.materialOptions, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var g2 = _vm.formData.materials.includes(item.value)\n          return {\n            $orig: $orig,\n            g2: g2,\n          }\n        })\n      : null\n  var l2 =\n    _vm.currentStep === 0\n      ? _vm.__map(_vm.chakraOptions, function (chakra, index) {\n          var $orig = _vm.__get_orig(chakra)\n          var g3 = _vm.formData.chakraNeeds.includes(chakra.value)\n          return {\n            $orig: $orig,\n            g3: g3,\n          }\n        })\n      : null\n  var l3 =\n    _vm.currentStep === 2\n      ? _vm.__map(_vm.beadsList, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var s0 = _vm.__get_style([_vm.getMarbleStyle(index)])\n          return {\n            $orig: $orig,\n            s0: s0,\n          }\n        })\n      : null\n  var g4 = _vm.currentStep === 3 ? _vm.beadsList.length : null\n  var l4 =\n    _vm.currentStep === 3\n      ? _vm.__map(_vm.beadsList, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var s1 = _vm.__get_style([_vm.getMarbleStyle(index)])\n          return {\n            $orig: $orig,\n            s1: s1,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        l1: l1,\n        l2: l2,\n        l3: l3,\n        g4: g4,\n        l4: l4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./axureIndex.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./axureIndex.vue?vue&type=script&lang=js&\"", "<template>\r\n    <div class=\"ai-recommend\">\r\n        <!-- 步骤指示器 -->\r\n        <div class=\"steps-container\">\r\n            <div class=\"steps\">\r\n                <div v-for=\"(step, index) in steps\" :key=\"index\" class=\"step\"\r\n                    :class=\"{ 'active': currentStep >= index }\">\r\n                    <div class=\"step-number\">{{ index + 1 }}</div>\r\n                    <div class=\"step-text\">{{ step }}</div>\r\n                    <div class=\"step-line\" v-if=\"index < steps.length - 1\"></div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- 需求收集表单 -->\r\n        <div v-if=\"currentStep === 0\" class=\"form-container\">\r\n            <form @submit.prevent=\"submitForm\">\r\n                <!-- 基础信息 -->\r\n                <div class=\"form-section\">\r\n                    <div class=\"section-title\">基础信息</div>\r\n                    <div class=\"form-item\">\r\n                        <text class=\"form-label\">年龄</text>\r\n                        <input class=\"form-input\" type=\"number\" placeholder=\"请输入您的年龄\" v-model=\"formData.age\" />\r\n                    </div>\r\n\r\n                    <div class=\"form-item\">\r\n                        <text class=\"form-label\">手围尺寸（cm）</text>\r\n                        <input class=\"form-input\" type=\"number\" placeholder=\"请输入您的手腕围度\" v-model=\"formData.hand\"\r\n                            step=\"0.1\" />\r\n                    </div>\r\n                    <div class=\"form-item\">\r\n                        <text class=\"form-label\">性别</text>\r\n                        <div class=\"radio-group\">\r\n                            <div class=\"radio-item\" :class=\"{ 'active': formData.gender === 'male' }\"\r\n                                @tap=\"setGenderMale\">\r\n                                <text>男</text>\r\n                            </div>\r\n                            <div class=\"radio-item\" :class=\"{ 'active': formData.gender === 'female' }\"\r\n                                @tap=\"setGenderFemale\">\r\n                                <text>女</text>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div class=\"form-item\">\r\n                        <text class=\"form-label\">职业</text>\r\n                        <picker class=\"form-picker\" :range=\"occupationOptions\" @change=\"onOccupationChange\">\r\n                            <div class=\"picker-value\">{{ formData.occupation || '请选择您的职业' }}</div>\r\n                        </picker>\r\n                    </div>\r\n                </div>\r\n\r\n                <!-- 需求信息 -->\r\n                <div class=\"form-section\">\r\n                    <div class=\"section-title\">需求信息</div>\r\n\r\n                    <div class=\"form-item\">\r\n                        <text class=\"form-label\">目的</text>\r\n                        <picker class=\"form-picker\" :range=\"purposeOptions\" @change=\"onPurposeChange\">\r\n                            <div class=\"picker-value\">{{ formData.purpose || '请选择您的目的' }}</div>\r\n                        </picker>\r\n                    </div>\r\n\r\n                    <div class=\"form-item\">\r\n                        <text class=\"form-label\">预算范围</text>\r\n                        <div class=\"budget-slider\">\r\n                            <slider :min=\"100\" :max=\"10000\" :step=\"100\" :value=\"formData.budget\"\r\n                                @change=\"onBudgetChange\" show-value />\r\n                            <div class=\"budget-value\">¥{{ formData.budget }}</div>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div class=\"form-item\">\r\n                        <text class=\"form-label\">颜色偏好</text>\r\n                        <div class=\"color-grid\">\r\n                            <div v-for=\"(color, index) in colorOptions\" :key=\"index\" class=\"color-item\"\r\n                                :style=\"{ backgroundColor: color.value }\"\r\n                                :class=\"{ 'active': formData.colorPreference.includes(color.value) }\"\r\n                                @tap=\"toggleColorByIndex\" :data-index=\"index\">\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div class=\"form-item\">\r\n                        <text class=\"form-label\">材质偏好</text>\r\n                        <div class=\"checkbox-group\">\r\n                            <div v-for=\"(item, index) in materialOptions\" :key=\"index\" class=\"checkbox-item\"\r\n                                :class=\"{ 'active': formData.materials.includes(item.value) }\"\r\n                                @tap=\"toggleMaterialByIndex\" :data-index=\"index\">\r\n                                <text>{{ item.label }}</text>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <!-- 特殊需求 -->\r\n                <div class=\"form-section\">\r\n                    <div class=\"section-title\">特殊需求</div>\r\n\r\n                    <div class=\"form-item\">\r\n                        <text class=\"form-label\">特殊需求描述</text>\r\n                        <textarea class=\"form-textarea\" placeholder=\"请描述您的特殊需求...\"\r\n                            v-model=\"formData.specialNeeds\"></textarea>\r\n                    </div>\r\n\r\n                    <div class=\"form-item\">\r\n                        <text class=\"form-label\">脉轮需求</text>\r\n                        <div class=\"chakra-selector\">\r\n                            <div v-for=\"(chakra, index) in chakraOptions\" :key=\"index\" class=\"chakra-item\"\r\n                                :class=\"{ 'active': formData.chakraNeeds.includes(chakra.value) }\"\r\n                                @tap=\"toggleChakraByIndex\" :data-index=\"index\">\r\n                                <div class=\"chakra-color\" :style=\"{ backgroundColor: chakra.color }\"></div>\r\n                                <text class=\"chakra-name\">{{ chakra.label }}</text>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <button class=\"submit-button\" @tap=\"submitForm\">开始生成</button>\r\n            </form>\r\n        </div>\r\n\r\n        <!-- AI生成中 -->\r\n        <div v-if=\"currentStep === 1\" class=\"generating\">\r\n            <div class=\"loading-animation\" v-if=\"aiTaskStatus === 'PROCESSING'\">\r\n                <div class=\"loading-circle\" v-for=\"i in 3\" :key=\"i\"></div>\r\n            </div>\r\n            <div class=\"generating-text\">AI正在为您精心设计手串...</div>\r\n            <div class=\"generating-subtext\">{{ generatingStatusText }}</div>\r\n\r\n            <!-- AI生成进度条 -->\r\n            <div class=\"progress-container\" v-if=\"aiProgress > 0\">\r\n                <div class=\"progress-bar\">\r\n                    <div class=\"progress-fill\" :style=\"{ width: aiProgress + '%' }\"></div>\r\n                </div>\r\n                <div class=\"progress-text\">{{ aiProgress }}%</div>\r\n            </div>\r\n\r\n            <!-- AI实时输出显示 -->\r\n            <scroll-view class=\"ai-output\" v-if=\"aiDescription\" ref=\"aiOutput\" scroll-y=\"true\"\r\n                :scroll-into-view=\"scrollIntoViewId\" @scrolltolower=\"onScrollToBottom\">\r\n                <div class=\"output-title\">AI分析结果预览：</div>\r\n                <mp-html :content=\"formattedAiDescription\" :tag-style=\"htmlTagStyle\" lazy-load selectable />\r\n                <div id=\"scroll-bottom-anchor\"></div>\r\n            </scroll-view>\r\n        </div>\r\n\r\n        <!-- 结果展示 -->\r\n        <div v-if=\"currentStep === 2\" class=\"result-display\">\r\n            <!-- 手串预览 -->\r\n            <div class=\"bracelet-prediv\">\r\n                <div\r\n                    style=\"display: flex;justify-content: center;align-items: center;height: 500rpx;position: relative;\">\r\n                    <div class=\"circle-container\"\r\n                        :style=\"{ width: radius * 2 + 'rpx', height: radius * 2 + 'rpx', border: '2px dashed #c9ab79' }\">\r\n                        <div v-for=\"(item, index) in beadsList\" :key=\"index\" class=\"marble\"\r\n                            :style=\"[getMarbleStyle(index)]\">\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- 推荐说明 -->\r\n            <view class=\"recommendation-details\" v-if=\"showRecommendationDetails\">\r\n                <view class=\"recommendation-header\">\r\n                    <text class=\"recommendation-title\">推荐结果</text>\r\n                </view>\r\n\r\n                <!-- 使用scroll-view来实现更好的滚动控制 -->\r\n                <scroll-view class=\"recommendation-body\" scroll-y=\"true\" :scroll-into-view=\"scrollIntoViewId\"\r\n                    @scrolltolower=\"onScrollToBottom\" id=\"recommendationScrollView\">\r\n\r\n                    <!-- AI生成中进度条 -->\r\n                    <view class=\"ai-generation-status\" v-if=\"aiTaskStatus === 'PROCESSING'\">\r\n                        <view class=\"loading-animation\">\r\n                            <view class=\"dot\"></view>\r\n                            <view class=\"dot\"></view>\r\n                            <view class=\"dot\"></view>\r\n                        </view>\r\n                        <text class=\"generation-text\">AI正在为您设计手串方案，请稍候...</text>\r\n                        <view class=\"progress-bar\">\r\n                            <view class=\"progress-filled\" :style=\"{ width: `${aiProgress}%` }\"></view>\r\n                        </view>\r\n                        <text class=\"progress-text\">{{ aiProgressText }}</text>\r\n                    </view>\r\n\r\n                    <!-- AI输出内容 -->\r\n                    <view class=\"ai-output-container\">\r\n                        <mp-html :content=\"formattedAiDescription\" :tag-style=\"htmlTagStyle\" lazy-load selectable />\r\n                    </view>\r\n\r\n                    <!-- 滚动锚点 -->\r\n                    <view id=\"scroll-bottom-anchor\" style=\"height: 2px;\"></view>\r\n\r\n                    <!-- 错误信息 -->\r\n                    <view class=\"error-message\" v-if=\"aiTaskStatus === 'FAILED'\">\r\n                        <text>{{ errorMessage || '生成失败，请重试' }}</text>\r\n                    </view>\r\n\r\n                    <!-- 恢复模拟的手串珠子展示部分 -->\r\n                    <!-- <view class=\"beads-list\" v-if=\"aiTaskStatus === 'COMPLETED' && beadsList.length > 0\">\r\n                        <view class=\"beads-section-title\">推荐手串珠子配置</view>\r\n                        <view v-for=\"(bead, index) in beadsList\" :key=\"index\" class=\"bead-item\">\r\n                            <image :src=\"bead.image\" mode=\"aspectFill\"></image>\r\n                            <view class=\"bead-info\">\r\n                                <view class=\"bead-name\">{{ bead.name }}</view>\r\n                                <view class=\"bead-effect\">{{ bead.effect }}</view>\r\n                            </view>\r\n                        </view>\r\n                        <view class=\"total-price\">总价: ¥{{ totalPrice }}</view>\r\n                    </view> -->\r\n                </scroll-view>\r\n\r\n                <!-- 底部操作按钮 -->\r\n                <view class=\"action-buttons action-buttons-row\">\r\n                    <!-- <button class=\"action-button\" @tap=\"saveToMyBracelets\" v-if=\"aiTaskStatus === 'COMPLETED'\">\r\n                        保存到我的手串\r\n                    </button> -->\r\n                    <button class=\"action-button outline\" @tap=\"generateNewRecommendation\">\r\n                        {{ aiTaskStatus === 'PROCESSING' ? '取消生成' : '重新生成' }}\r\n                    </button>\r\n                </view>\r\n            </view>\r\n\r\n        </div>\r\n\r\n        <!-- 确认下单 -->\r\n        <div v-if=\"currentStep === 3\" class=\"confirm-order\">\r\n            <div class=\"order-summary\">\r\n                <div class=\"price-info\">\r\n                    <text>总价：</text>\r\n                    <text class=\"price\">¥{{ totalPrice }}</text>\r\n                </div>\r\n                <div class=\"beads-count\">共{{ beadsList.length }}颗水晶</div>\r\n            </div>\r\n\r\n\r\n            <div class=\"bracelet-prediv\">\r\n                <div\r\n                    style=\"display: flex;justify-content: center;align-items: center;height: 500rpx;position: relative;\">\r\n                    <div class=\"circle-container\"\r\n                        :style=\"{ width: radius * 2 + 'rpx', height: radius * 2 + 'rpx', border: '2px dashed #c9ab79' }\">\r\n                        <div v-for=\"(item, index) in beadsList\" :key=\"index\" class=\"marble\"\r\n                            :style=\"[getMarbleStyle(index)]\">\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"order-buttons\">\r\n                <button class=\"order-button\" type=\"primary\" @tap=\"placeOrder\">立即下单</button>\r\n                <button class=\"save-button\" @tap=\"saveToMyBracelets\">保存到我的手串</button>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n    toLogin\r\n} from '@/libs/login.js';\r\nimport { saveUserRequirement, updateUserRequirement, submitStreamAiRecommendation, checkAiRecommendation, getRequirementByUserId } from '@/api/requirement.js';\r\nimport { marked } from '@/utils/marked.js';\r\n\timport {\r\n\t\tmapGetters\r\n\t} from \"vuex\";\r\n\r\nexport default {\r\n    data() {\r\n        return {\r\n            currentStep: 0,\r\n            steps: ['需求收集', 'AI生成', '结果展示', '确认下单'],\r\n            formData: {\r\n                age: 25,\r\n                gender: 'female',\r\n                occupation: '上班族',\r\n                purpose: '健康',\r\n                budget: 1000,\r\n                hand: 16.5,\r\n                chakraNeeds: [],\r\n                specialNeeds: '',\r\n                colorPreference: [],\r\n                materials: []\r\n            },\r\n            occupationOptions: ['学生', '上班族', '自由职业', '企业家', '艺术家', '医疗工作者', '教育工作者', '其他'],\r\n            purposeOptions: ['健康', '事业', '爱情', '财运', '学业', '人际关系', '平衡能量', '冥想辅助'],\r\n            colorOptions: [\r\n                { label: '紫色', value: 'purple' },\r\n                { label: '白色', value: 'white' },\r\n                { label: '黑色', value: 'black' },\r\n                { label: '粉色', value: 'pink' },\r\n                { label: '绿色', value: 'green' },\r\n                { label: '蓝色', value: 'blue' },\r\n                { label: '黄色', value: 'yellow' },\r\n                { label: '红色', value: 'red' }\r\n            ],\r\n            materialOptions: [\r\n                { label: '紫水晶', value: 'amethyst' },\r\n                { label: '白水晶', value: 'clear_quartz' },\r\n                { label: '黑曜石', value: 'obsidian' },\r\n                { label: '月光石', value: 'moonstone' },\r\n                { label: '虎眼石', value: 'tiger_eye' },\r\n                { label: '粉晶', value: 'rose_quartz' }\r\n            ],\r\n            chakraOptions: [\r\n                { label: '海底轮', value: 'root', color: '#FF0000' },\r\n                { label: '脐轮', value: 'sacral', color: '#FF7F00' },\r\n                { label: '太阳轮', value: 'solar', color: '#FFFF00' },\r\n                { label: '心轮', value: 'heart', color: '#00FF00' },\r\n                { label: '喉轮', value: 'throat', color: '#0000FF' },\r\n                { label: '眉心轮', value: 'third_eye', color: '#4B0082' },\r\n                { label: '顶轮', value: 'crown', color: '#9400D3' }\r\n            ],\r\n            // 模拟数据 - 推荐结果\r\n            radius: 150,\r\n            beadsList: [],\r\n            aiTaskId: null,\r\n            aiProgress: 0,\r\n            generatingStatusText: \"正在分析您的需求...\",\r\n            aiDescription: \"\",\r\n            pollingTimer: null,\r\n            totalPrice: 0,\r\n            partialContent: \"\",\r\n            previousPartialLength: 0,\r\n            md: null,\r\n            renderedMarkdown: \"\",\r\n            aiResponse: \"\",\r\n            outputScrollTop: 0,\r\n            outputScrollId: 'output-content',\r\n            scrollIntoViewId: '',\r\n            formattedAiDescription: '',\r\n            aiTaskStatus: 'PROCESSING',\r\n            errorMessage: '',\r\n            userRequirements: [],\r\n            force_update: Date.now(), // 强制更新标志\r\n            showRecommendationDetails: true, // 添加展示推荐详情的标志\r\n            // 添加mp-html组件的样式配置\r\n            htmlTagStyle: {\r\n                h1: 'font-size: 36rpx; font-weight: bold; color: #333; margin: 10rpx 0; border-bottom: 1px solid #eee; padding-bottom: 10rpx;',\r\n                h2: 'font-size: 32rpx; font-weight: bold; color: #333; margin: 10rpx 0; border-bottom: 1px solid #f3f3f3; padding-bottom: 8rpx;',\r\n                h3: 'font-size: 30rpx; font-weight: bold; color: #333; margin: 10rpx 0;',\r\n                p: 'margin: 8rpx 0; line-height: 1.5;',\r\n                ul: 'padding-left: 30rpx; margin: 8rpx 0 15rpx 0;',\r\n                ol: 'padding-left: 30rpx; margin: 8rpx 0 15rpx 0;',\r\n                li: 'margin: 5rpx 0;',\r\n                blockquote: 'border-left: 4rpx solid #c9ab79; padding: 8rpx 16rpx; margin: 10rpx 0; background-color: #f9f5eb; color: #666;',\r\n                strong: 'font-weight: bold; color: #333;',\r\n                em: 'font-style: italic;',\r\n                img: 'max-width: 100%; height: auto; display: block; margin: 10rpx auto; border-radius: 6rpx;'\r\n            }\r\n        }\r\n    },\r\n    onLoad() {\r\n        // 加载用户历史需求\r\n        this.loadUserRequirements();\r\n\r\n        // 确保 rich-text 内的样式能正确应用\r\n        setTimeout(() => {\r\n            this.applyMarkdownStyles();\r\n        }, 500);\r\n    },\r\n    onUnload() {\r\n        // 页面卸载时清除定时器\r\n        this.clearPollingTimer();\r\n    },\r\n    created() {\r\n        // 初始化Markdown解析器\r\n        // this.md = new MarkdownIt({\r\n        //     html: true,\r\n        //     breaks: true,\r\n        //     linkify: true,\r\n        //     typographer: true\r\n        // });\r\n\r\n        // 配置Markdown解析器，为元素添加类名\r\n        this.configureMarkdownRenderer();\r\n\r\n        // 继续原有的初始化内容\r\n        // ... existing created code ...\r\n    },\r\n    watch: {\r\n        // 监听aiDescription变化，渲染Markdown\r\n        aiDescription(newVal) {\r\n            if (newVal) {\r\n                this.formattedAiDescription = this.renderMarkdown(newVal);\r\n                this.$nextTick(() => {\r\n                    // 不管在哪个步骤，都需要滚动到底部\r\n                    this.scrollToBottom();\r\n                });\r\n            }\r\n        }\r\n    },\r\n    computed: {\r\n        ...mapGetters(['isLogin']),\r\n        // 计算AI进度文本\r\n        aiProgressText() {\r\n            if (this.aiProgress < 25) {\r\n                return '正在分析您的需求...';\r\n            } else if (this.aiProgress < 50) {\r\n                return '正在匹配适合的水晶...';\r\n            } else if (this.aiProgress < 75) {\r\n                return '正在设计手串方案...';\r\n            } else {\r\n                return '正在生成能量解读...';\r\n            }\r\n        }\r\n    },\r\n    methods: {\r\n        // 修复性别切换问题\r\n        setGenderMale() {\r\n            this.formData.gender = 'male';\r\n        },\r\n        setGenderFemale() {\r\n            this.formData.gender = 'female';\r\n        },\r\n        // 修复颜色偏好选择问题\r\n        toggleColorByIndex(e) {\r\n            const index = e.currentTarget.dataset.index;\r\n            const color = this.colorOptions[index].value;\r\n            this.toggleColor(color);\r\n        },\r\n        toggleColor(color) {\r\n            const index = this.formData.colorPreference.indexOf(color);\r\n            if (index === -1) {\r\n                this.formData.colorPreference.push(color);\r\n            } else {\r\n                this.formData.colorPreference.splice(index, 1);\r\n            }\r\n        },\r\n        // 修复材质偏好选择问题\r\n        toggleMaterialByIndex(e) {\r\n            const index = e.currentTarget.dataset.index;\r\n            const material = this.materialOptions[index].value;\r\n            this.toggleMaterial(material);\r\n        },\r\n        toggleMaterial(material) {\r\n            const index = this.formData.materials.indexOf(material);\r\n            if (index === -1) {\r\n                this.formData.materials.push(material);\r\n            } else {\r\n                this.formData.materials.splice(index, 1);\r\n            }\r\n        },\r\n        // 修复脉轮需求选择问题\r\n        toggleChakraByIndex(e) {\r\n            const index = e.currentTarget.dataset.index;\r\n            const chakra = this.chakraOptions[index].value;\r\n            this.toggleChakra(chakra);\r\n        },\r\n        toggleChakra(chakra) {\r\n            const index = this.formData.chakraNeeds.indexOf(chakra);\r\n            if (index === -1) {\r\n                this.formData.chakraNeeds.push(chakra);\r\n            } else {\r\n                this.formData.chakraNeeds.splice(index, 1);\r\n            }\r\n        },\r\n        onOccupationChange(e) {\r\n            this.formData.occupation = this.occupationOptions[e.detail.value];\r\n        },\r\n        onPurposeChange(e) {\r\n            this.formData.purpose = this.purposeOptions[e.detail.value];\r\n        },\r\n        onBudgetChange(e) {\r\n            this.formData.budget = e.detail.value;\r\n        },\r\n        submitForm() {\r\n\t\t\tif (!this.isLogin) {\r\n\t\t\t\ttoLogin();\r\n\t\t\t\treturn;\r\n            }\r\n            // 验证表单\r\n            if (!this.formData.age) {\r\n                uni.showToast({\r\n                    title: '请输入年龄',\r\n                    icon: 'none'\r\n                });\r\n                return;\r\n            }\r\n\r\n            if (!this.formData.occupation) {\r\n                uni.showToast({\r\n                    title: '请选择职业',\r\n                    icon: 'none'\r\n                });\r\n                return;\r\n            }\r\n\r\n            if (!this.formData.purpose) {\r\n                uni.showToast({\r\n                    title: '请选择目的',\r\n                    icon: 'none'\r\n                });\r\n                return;\r\n            }\r\n            if (!this.formData.hand) {\r\n                uni.showToast({\r\n                    title: '请输入手围尺寸',\r\n                    icon: 'none'\r\n                });\r\n                return;\r\n            }\r\n\r\n            // 将脉轮需求合并到特殊需求字段中\r\n            let specialNeeds = this.formData.specialNeeds || '';\r\n            if (this.formData.chakraNeeds && this.formData.chakraNeeds.length > 0) {\r\n                // 将脉轮需求转换为可读文本\r\n                const chakraNames = {\r\n                    'root': '海底轮',\r\n                    'sacral': '脐轮',\r\n                    'solar': '太阳轮',\r\n                    'heart': '心轮',\r\n                    'throat': '喉轮',\r\n                    'third_eye': '眉心轮',\r\n                    'crown': '顶轮'\r\n                };\r\n\r\n                let chakraText = '脉轮需求: ';\r\n                this.formData.chakraNeeds.forEach((chakra, index) => {\r\n                    if (index > 0) chakraText += ', ';\r\n                    chakraText += chakraNames[chakra] || chakra;\r\n                });\r\n\r\n                if (specialNeeds) {\r\n                    specialNeeds += '\\n' + chakraText;\r\n                } else {\r\n                    specialNeeds = chakraText;\r\n                }\r\n            }\r\n\r\n            // 将需求保存到服务器并提交AI生成请求\r\n            const requirementData = {\r\n                userId: uni.getStorageSync('uid') || 0,\r\n                requirementType: 'bracelet',\r\n                age: this.formData.age,\r\n                gender: this.formData.gender,\r\n                occupation: this.formData.occupation,\r\n                hand: this.formData.hand,\r\n                purpose: this.formData.purpose,\r\n                budget: this.formData.budget,\r\n                colorPreference: JSON.stringify(this.formData.colorPreference),\r\n                materials: JSON.stringify(this.formData.materials),\r\n                specialNeeds: specialNeeds,\r\n                hand: this.formData.hand,\r\n                status: 0 // 初始状态\r\n            };\r\n\r\n            uni.showLoading({\r\n                title: '正在提交需求...'\r\n            });\r\n\r\n            // 进入生成阶段\r\n            console.log('表单提交成功，进入AI生成阶段');\r\n            this.currentStep = 1;\r\n            this.aiDescription = \"\";\r\n            this.aiProgress = 0;\r\n            this.aiTaskStatus = 'PROCESSING'; // 确保状态设置为处理中\r\n\r\n            // 先保存用户需求\r\n            // saveUserRequirement(requirementData).then(res => {\r\n            //     uni.hideLoading();\r\n            //     if (res.data) {\r\n            //         console.log('需求保存成功，ID：', res.data);\r\n            //         this.requirementId = res.data;\r\n\r\n            // 提交流式AI生成请求\r\n            this.submitStreamAiGenerationRequest(requirementData);\r\n            //     } else {\r\n            //         uni.showToast({\r\n            //             title: '需求保存失败',\r\n            //             icon: 'none'\r\n            //         });\r\n            //         this.currentStep = 0; // 返回到需求收集页面\r\n            //     }\r\n            // }).catch(err => {\r\n            //     uni.hideLoading();\r\n            //     uni.showToast({\r\n            //         title: '需求保存失败: ' + err.message,\r\n            //         icon: 'none'\r\n            //     });\r\n            //     this.currentStep = 0; // 返回到需求收集页面\r\n            // });\r\n        },\r\n\r\n        // 提交流式AI生成请求\r\n        submitStreamAiGenerationRequest(requirementData) {\r\n            this.generatingStatusText = \"正在向AI提交需求...\";\r\n            this.aiProgress = 5;\r\n            this.partialContent = \"\"; // 用于存储部分生成的内容\r\n            this.previousPartialLength = 0; // 用于跟踪内容增量\r\n            this.aiTaskStatus = 'PROCESSING'; // 确保状态正确设置为处理中\r\n            this.force_update = Date.now(); // 强制触发视图更新\r\n\r\n            submitStreamAiRecommendation(requirementData).then(res => {\r\n                if (res.code == 200 && res.data) {\r\n                    uni.hideLoading();\r\n                    console.log('流式AI任务提交成功：', res.data);\r\n                    this.aiTaskId = res.data.taskId;\r\n                    this.requirementId = res.data.requirementId;\r\n                    this.startPollingPartialContent();\r\n                } else {\r\n                    console.error('AI任务提交失败：', res.message);\r\n                    this.generatingStatusText = \"AI请求失败，将使用默认推荐...\";\r\n                    this.aiTaskStatus = 'FAILED'; // 设置状态为失败\r\n                    this.force_update = Date.now(); // 强制触发视图更新\r\n                    // this.useFallbackGeneration();\r\n                }\r\n            }).catch(err => {\r\n                uni.hideLoading();\r\n                console.error('AI任务提交请求失败：', err);\r\n                this.generatingStatusText = \"AI请求失败，将使用默认推荐...\";\r\n                this.aiTaskStatus = 'FAILED'; // 设置状态为失败\r\n                this.force_update = Date.now(); // 强制触发视图更新\r\n                // this.useFallbackGeneration();\r\n            });\r\n        },\r\n\r\n        // 开始轮询部分内容结果\r\n        startPollingPartialContent() {\r\n            this.clearPollingTimer(); // 确保没有重复的轮询\r\n\r\n            // 设置轮询间隔\r\n            this.pollingTimer = setInterval(() => {\r\n                this.pollPartialContent();\r\n            }, 500); // 使用较短的间隔以实现更流畅的打字效果\r\n        },\r\n\r\n        // 轮询查询部分生成内容\r\n        pollPartialContent() {\r\n            if (!this.aiTaskId) {\r\n                console.log('没有aiTaskId，无法轮询');\r\n                return;\r\n            }\r\n\r\n            console.log('开始轮询AI任务状态，taskId:', this.aiTaskId);\r\n\r\n            checkAiRecommendation(this.aiTaskId).then(res => {\r\n                if (res.code == 200 && res.data) {\r\n                    const taskStatus = res.data;\r\n                    console.log('轮询返回状态:', taskStatus.status, '进度:', taskStatus.progress);\r\n\r\n                    // 更新任务状态\r\n                    const oldStatus = this.aiTaskStatus;\r\n                    this.aiTaskStatus = taskStatus.status;\r\n\r\n                    if (oldStatus !== this.aiTaskStatus) {\r\n                        console.log('任务状态变更:', oldStatus, '->', this.aiTaskStatus);\r\n                        this.force_update = Date.now(); // 状态变更时强制更新视图\r\n                    }\r\n\r\n                    // 更新进度\r\n                    if (taskStatus.progress) {\r\n                        this.aiProgress = taskStatus.progress;\r\n                    }\r\n\r\n                    // 检查是否有新的部分内容\r\n                    if (taskStatus.partialContent) {\r\n                        const newContent = taskStatus.partialContent;\r\n\r\n                        // 如果内容有更新，则更新显示\r\n                        if (newContent.length > this.previousPartialLength) {\r\n                            console.log('检测到内容更新，当前内容长度:', newContent.length);\r\n                            this.aiDescription = newContent;\r\n                            this.formattedAiDescription = this.renderMarkdown(newContent);\r\n                            this.previousPartialLength = newContent.length;\r\n\r\n                            // 滚动到底部 - 无论在哪个步骤都执行\r\n                            this.$nextTick(() => {\r\n                                this.scrollToBottom();\r\n                            });\r\n                        }\r\n                    }\r\n\r\n                    // 根据任务状态处理\r\n                    if (taskStatus.status === 'COMPLETED') {\r\n                        console.log('轮询检测到任务已完成，将切换到结果页面');\r\n                        // 任务完成，停止轮询\r\n                        this.clearPollingTimer();\r\n\r\n                        // 显示最终的结果\r\n                        if (taskStatus.result && taskStatus.result.aiDescription) {\r\n                            console.log('处理完整结果:', taskStatus.result);\r\n                            // 处理AI生成完成的结果\r\n                            this.handleAiGenerationCompleted(taskStatus.result);\r\n                        } else {\r\n                            console.log('使用部分内容作为最终结果');\r\n                            // 如果没有result但有部分内容，则使用部分内容作为最终结果\r\n                            const finalResult = {\r\n                                aiDescription: this.aiDescription || ''\r\n                            };\r\n                            this.handleAiGenerationCompleted(finalResult);\r\n                        }\r\n                    } else if (taskStatus.status === 'FAILED') {\r\n                        console.error('AI生成任务失败:', taskStatus.error);\r\n                        // 任务失败，显示错误\r\n                        this.aiTaskStatus = 'FAILED';\r\n                        this.errorMessage = taskStatus.error || '生成失败，请重试';\r\n                        this.clearPollingTimer();\r\n                        // 失败时也切换到结果页面\r\n                        this.currentStep = 2;\r\n                    }\r\n                } else {\r\n                    console.error('轮询AI任务状态失败：', res.message);\r\n                }\r\n            }).catch(err => {\r\n                console.error('轮询请求失败：', err);\r\n                // 避免无限轮询失败，如果多次请求失败也进入结果页面\r\n                this.errorTries = (this.errorTries || 0) + 1;\r\n                if (this.errorTries > 3) {\r\n                    console.log('多次轮询失败，强制进入结果页面');\r\n                    this.clearPollingTimer();\r\n                    this.aiTaskStatus = 'FAILED';\r\n                    this.errorMessage = '网络连接异常，请重试';\r\n                    this.currentStep = 2;\r\n                }\r\n            });\r\n        },\r\n\r\n        // 配置Markdown渲染器\r\n        configureMarkdownRenderer() {\r\n            if (!this.md) return;\r\n\r\n            // 添加类名到HTML标签\r\n            this.md.renderer.rules.heading_open = function (tokens, idx, options, env, self) {\r\n                const token = tokens[idx];\r\n                const tag = token.tag;\r\n                const className = tag; // 使用标签名作为类名\r\n                token.attrJoin('class', className);\r\n                return self.renderToken(tokens, idx, options);\r\n            };\r\n\r\n            this.md.renderer.rules.paragraph_open = function (tokens, idx, options, env, self) {\r\n                tokens[idx].attrJoin('class', 'p');\r\n                return self.renderToken(tokens, idx, options);\r\n            };\r\n\r\n            this.md.renderer.rules.bullet_list_open = function (tokens, idx, options, env, self) {\r\n                tokens[idx].attrJoin('class', 'ul');\r\n                return self.renderToken(tokens, idx, options);\r\n            };\r\n\r\n            this.md.renderer.rules.ordered_list_open = function (tokens, idx, options, env, self) {\r\n                tokens[idx].attrJoin('class', 'ol');\r\n                return self.renderToken(tokens, idx, options);\r\n            };\r\n\r\n            this.md.renderer.rules.list_item_open = function (tokens, idx, options, env, self) {\r\n                tokens[idx].attrJoin('class', 'li');\r\n                return self.renderToken(tokens, idx, options);\r\n            };\r\n\r\n            this.md.renderer.rules.blockquote_open = function (tokens, idx, options, env, self) {\r\n                tokens[idx].attrJoin('class', 'blockquote');\r\n                return self.renderToken(tokens, idx, options);\r\n            };\r\n\r\n            this.md.renderer.rules.strong_open = function (tokens, idx, options, env, self) {\r\n                tokens[idx].attrJoin('class', 'strong');\r\n                return self.renderToken(tokens, idx, options);\r\n            };\r\n\r\n            this.md.renderer.rules.em_open = function (tokens, idx, options, env, self) {\r\n                tokens[idx].attrJoin('class', 'em');\r\n                return self.renderToken(tokens, idx, options);\r\n            };\r\n\r\n            this.md.renderer.rules.image = function (tokens, idx, options, env, self) {\r\n                const token = tokens[idx];\r\n                token.attrJoin('class', 'img');\r\n                return self.renderToken(tokens, idx, options);\r\n            };\r\n        },\r\n\r\n        // 修复: 增强渲染Markdown内容的方法\r\n        renderMarkdown(content) {\r\n            if (!content) return '';\r\n            try {\r\n                console.log('正在渲染Markdown:', content.substring(0, 100) + '...');\r\n                return marked(content);\r\n            } catch (error) {\r\n                console.error('Markdown渲染错误:', error);\r\n                return content || '';\r\n            }\r\n        },\r\n\r\n        // 处理滚动到底部事件\r\n        onScrollToBottom() {\r\n            console.log('已滚动到底部');\r\n        },\r\n\r\n        // 保存到我的手串 - 确保保存aiResponse\r\n        saveToMyBracelets() {\r\n            // 检查是否生成完成\r\n            if (this.aiTaskStatus !== 'COMPLETED') {\r\n                uni.showToast({\r\n                    title: '请等待AI生成完成',\r\n                    icon: 'none'\r\n                });\r\n                return;\r\n            }\r\n\r\n            // 准备更新的数据，包含aiResponse\r\n            const updatedData = {\r\n                id: this.requirementId,\r\n                age: this.formData.age,\r\n                gender: this.formData.gender,\r\n                occupation: this.formData.occupation,\r\n                purpose: this.formData.purpose || '',\r\n                specialNeeds: this.formData.specialNeeds || '',\r\n                budget: this.formData.budget,\r\n                aiResponse: this.aiResponse, // 确保保存完整的AI响应\r\n                hand: this.formData.hand,\r\n                status: 1 // 已完成\r\n            };\r\n\r\n            // 根据是否有requirementId决定是更新还是创建\r\n            if (this.requirementId) {\r\n                // 更新已有需求\r\n                updateUserRequirement(updatedData).then(res => {\r\n                    if (res.code === 200) {\r\n                        uni.showToast({\r\n                            title: '已保存到我的手串',\r\n                            icon: 'success'\r\n                        });\r\n                    } else {\r\n                        uni.showToast({\r\n                            title: res.message || '保存失败',\r\n                            icon: 'none'\r\n                        });\r\n                    }\r\n                }).catch(err => {\r\n                    console.error('保存失败：', err);\r\n                    uni.showToast({\r\n                        title: '保存失败，请重试',\r\n                        icon: 'none'\r\n                    });\r\n                });\r\n            } else {\r\n                // 创建新需求\r\n                const requirementData = {\r\n                    age: this.formData.age,\r\n                    gender: this.formData.gender,\r\n                    occupation: this.formData.occupation,\r\n                    purpose: this.formData.purpose || '',\r\n                    specialNeeds: this.formData.specialNeeds || '',\r\n                    budget: this.formData.budget,\r\n                    aiResponse: this.aiResponse, // 确保保存完整的AI响应\r\n                    hand: this.formData.hand,\r\n                    status: 1 // 已完成\r\n                };\r\n\r\n                saveUserRequirement(requirementData).then(res => {\r\n                    if (res.code === 200) {\r\n                        // 更新requirementId\r\n                        this.requirementId = res.data;\r\n                        uni.showToast({\r\n                            title: '已保存到我的手串',\r\n                            icon: 'success'\r\n                        });\r\n                    } else {\r\n                        uni.showToast({\r\n                            title: res.message || '保存失败',\r\n                            icon: 'none'\r\n                        });\r\n                    }\r\n                }).catch(err => {\r\n                    console.error('保存失败：', err);\r\n                    uni.showToast({\r\n                        title: '保存失败，请重试',\r\n                        icon: 'none'\r\n                    });\r\n                });\r\n            }\r\n        },\r\n\r\n        // 恢复生成珠子布局的方法\r\n        generateMarbles() {\r\n            let realRoundWidth = this.beadsList.map(e => { return parseFloat(e.width) }).reduce((accumulator, currentValue) => accumulator + currentValue, 0);\r\n            this.realRadius = (realRoundWidth).toFixed(2);\r\n            if (this.beadsList.length < 6) {\r\n                this.radius = 100;\r\n                return;\r\n            }\r\n\r\n            const totalBeadsWidth = this.beadsList.reduce((sum, bead) =>\r\n                sum + (parseFloat(bead.width) * 3), 0);\r\n\r\n            const spacing = (totalBeadsWidth / this.beadsList.length) * 0.05;\r\n            const totalCircumference = totalBeadsWidth + (spacing * this.beadsList.length);\r\n\r\n            // Set radius to match circle border\r\n            this.radius = totalCircumference / (2 * Math.PI);\r\n            //   this.realRadius = (totalCircumference / Math.PI).toFixed(2);\r\n        },\r\n\r\n        generateMockRecommendation() {\r\n            const mockBeads = [];\r\n            let totalPrice = 0;\r\n\r\n            if (this.formData.chakraNeeds.length > 0) {\r\n                this.formData.chakraNeeds.forEach(chakra => {\r\n                    const chakraInfo = this.chakraOptions.find(c => c.value === chakra);\r\n\r\n                    const count = Math.floor(Math.random() * 2) + 10;\r\n                    for (let i = 0; i < count; i++) {\r\n                        const price = Math.floor(Math.random() * 300) + 100;\r\n                        totalPrice += price;\r\n\r\n                        mockBeads.push({\r\n                            id: `bead-${mockBeads.length + 1}`,\r\n                            name: this.getRandomBeadName(chakra),\r\n                            image: 'https://mpjoy.oss-cn-beijing.aliyuncs.com/crmebimage/public/content/2024/12/14/b3cf9d2d83d14999a57e3c0ce922e98ak1u8k8kow5.png',\r\n                            width: 20,\r\n                            height: 20,\r\n                            effect: this.getRandomEffect(chakra),\r\n                            price: price,\r\n                            chakra: chakra,\r\n                            color: chakraInfo.color\r\n                        });\r\n                    }\r\n                });\r\n            } else {\r\n                const count = Math.floor(Math.random() * 5) + 8;\r\n                for (let i = 0; i < count; i++) {\r\n                    const randomChakra = this.chakraOptions[Math.floor(Math.random() * this.chakraOptions.length)];\r\n                    const price = Math.floor(Math.random() * 300) + 100;\r\n                    totalPrice += price;\r\n\r\n                    mockBeads.push({\r\n                        id: `bead-${i + 1}`,\r\n                        name: this.getRandomBeadName(randomChakra.value),\r\n                        image: 'https://mpjoy.oss-cn-beijing.aliyuncs.com/crmebimage/public/content/2024/12/14/b3cf9d2d83d14999a57e3c0ce922e98ak1u8k8kow5.png',\r\n                        width: 20,\r\n                        height: 20,\r\n                        effect: this.getRandomEffect(randomChakra.value),\r\n                        price: price,\r\n                        chakra: randomChakra.value,\r\n                        color: randomChakra.color\r\n                    });\r\n                }\r\n            }\r\n\r\n            // 设置推荐结果\r\n            this.beadsList = mockBeads;\r\n            this.totalPrice = totalPrice;\r\n\r\n        },\r\n        getRandomBeadName(chakra) {\r\n            const beadNames = {\r\n                'root': ['红玛瑙', '石榴石', '黑曜石', '红虎眼石', '红碧玺'],\r\n                'sacral': ['橙色玛瑙', '橙色月光石', '橙色方解石', '橙色玉髓'],\r\n                'solar': ['黄水晶', '虎眼石', '琥珀', '金发晶', '黄玉'],\r\n                'heart': ['绿幽灵', '绿碧玺', '翡翠', '孔雀石', '绿aventurine'],\r\n                'throat': ['青金石', '海蓝宝', '蓝玉髓', '蓝碧玺', '绿松石'],\r\n                'third_eye': ['紫水晶', '荧石', '舒俱来', '蓝宝石', '坦桑石'],\r\n                'crown': ['白水晶', '紫晶簇', '月光石', '钻石', '白碧玺']\r\n            };\r\n\r\n            const names = beadNames[chakra] || ['水晶珠'];\r\n            return names[Math.floor(Math.random() * names.length)];\r\n        },\r\n        getRandomEffect(chakra) {\r\n            const effects = {\r\n                'root': ['增强稳定性', '提供安全感', '增强生命力', '促进物质丰盛'],\r\n                'sacral': ['激发创造力', '增强性能量', '促进情感平衡', '增强自信'],\r\n                'solar': ['增强个人力量', '提升自尊', '促进智慧决策', '增强意志力'],\r\n                'heart': ['促进爱与和谐', '平衡情感', '增强同理心', '促进人际关系'],\r\n                'throat': ['促进沟通', '增强表达能力', '促进真实性', '增强创意表达'],\r\n                'third_eye': ['增强直觉', '促进洞察力', '增强精神意识', '促进冥想'],\r\n                'crown': ['连接高我', '促进精神觉醒', '增强宇宙意识', '促进内在平静']\r\n            };\r\n\r\n            const effectList = effects[chakra] || ['增强能量'];\r\n            return effectList[Math.floor(Math.random() * effectList.length)];\r\n        },\r\n\r\n        getMarbleStyle(index) {\r\n            // Calculate total circumference based on bead sizes\r\n            const totalBeadsWidth = this.beadsList.reduce((sum, bead) =>\r\n                sum + (bead.width * 3), 0);\r\n\r\n            // Add small gaps between beads (5% of average bead size)\r\n            const spacing = (totalBeadsWidth / this.beadsList.length) * 0.05;\r\n            const circumference = totalBeadsWidth + (spacing * this.beadsList.length);\r\n\r\n            // Calculate radius to match circle border\r\n            const dynamicRadius = circumference / (2 * Math.PI);\r\n\r\n            // Calculate position on circle for current bead\r\n            let angleOffset = 0;\r\n            for (let i = 0; i < index; i++) {\r\n                const prevBeadWidth = this.beadsList[i].width * 3;\r\n                angleOffset += (prevBeadWidth + spacing) / dynamicRadius;\r\n            }\r\n\r\n            const currentBeadWidth = this.beadsList[index].width * 3;\r\n            const angle = angleOffset + (currentBeadWidth / 2) / dynamicRadius;\r\n\r\n            // Position bead exactly on circle circumference\r\n            const x = dynamicRadius * (1 + Math.cos(angle));\r\n            const y = dynamicRadius * (1 + Math.sin(angle));\r\n\r\n            return {\r\n                position: 'absolute',\r\n                left: (x - currentBeadWidth / 2) + 'rpx',\r\n                top: (y - (this.beadsList[index].height * 3 / 2)) + 'rpx',\r\n                width: currentBeadWidth + 'rpx',\r\n                height: (this.beadsList[index].height * 3) + 'rpx',\r\n                transform: `rotate(${angle + Math.PI / 2}rad)`,\r\n                background: `url('${this.beadsList[index].image}') no-repeat center`,\r\n                backgroundSize: 'cover',\r\n                transition: 'all 0.3s ease'\r\n            };\r\n        },\r\n\r\n\r\n        editRecommendation() {\r\n            // 返回到需求收集页面\r\n            this.currentStep = 0;\r\n        },\r\n        nextStep() {\r\n            // 进入确认下单页面\r\n            this.currentStep = 3;\r\n        },\r\n        placeOrder() {\r\n            // 模拟下单\r\n            uni.showLoading({\r\n                title: '正在下单...'\r\n            });\r\n\r\n            // 准备下单数据\r\n            const orderData = {\r\n                requirementId: this.requirementId, // 关联需求ID\r\n                productInfo: JSON.stringify({\r\n                    beadsList: this.beadsList,\r\n                    totalPrice: this.totalPrice,\r\n                    aiDescription: this.aiDescription\r\n                }),\r\n                totalPrice: this.totalPrice\r\n            };\r\n\r\n            // 记录用户的下单行为 - 可以接入实际的下单API\r\n            console.log('准备下单数据：', orderData);\r\n\r\n            // 这里应该调用实际的下单API，目前使用模拟\r\n            setTimeout(() => {\r\n                uni.hideLoading();\r\n                uni.showToast({\r\n                    title: '下单成功！',\r\n                    icon: 'success'\r\n                });\r\n\r\n                // 跳转到订单详情页\r\n                setTimeout(() => {\r\n                    uni.navigateTo({\r\n                        url: '/pages/order_details/index?id=mock-order-id'\r\n                    });\r\n                }, 1500);\r\n            }, 2000);\r\n        },\r\n        // 清除轮询定时器\r\n        clearPollingTimer() {\r\n            if (this.pollingTimer) {\r\n                clearInterval(this.pollingTimer);\r\n                this.pollingTimer = null;\r\n            }\r\n        },\r\n        // 滚动到底部 - 优化版本\r\n        scrollToBottom() {\r\n            console.log('执行滚动到底部');\r\n            this.$nextTick(() => {\r\n                // 使用scroll-into-view方式滚动\r\n                this.scrollIntoViewId = 'scroll-bottom-anchor';\r\n\r\n                // 同时尝试使用传统的pageScrollTo方法（微信小程序API）\r\n                try {\r\n                    uni.pageScrollTo({\r\n                        scrollTop: 10000,\r\n                        duration: 100\r\n                    });\r\n                } catch (e) {\r\n                    console.log('pageScrollTo方法调用失败', e);\r\n                }\r\n\r\n                // 重置后再设置，确保触发滚动效果\r\n                setTimeout(() => {\r\n                    this.scrollIntoViewId = '';\r\n                    setTimeout(() => {\r\n                        this.scrollIntoViewId = 'scroll-bottom-anchor';\r\n                    }, 10);\r\n                }, 100);\r\n            });\r\n        },\r\n        // 滚动推荐内容到可见区域\r\n        scrollRecommendationToView() {\r\n            if (this.$refs.recommendationDetails) {\r\n                this.$nextTick(() => {\r\n                    const query = uni.createSelectorQuery().in(this);\r\n                    query.select('.recommendation-details').boundingClientRect(data => {\r\n                        if (data) {\r\n                            uni.pageScrollTo({\r\n                                scrollTop: data.top,\r\n                                duration: 300\r\n                            });\r\n                        }\r\n                    }).exec();\r\n                });\r\n            }\r\n        },\r\n        // 更新需求状态时保存AI响应结果\r\n        updateUserRequirement(updatedData) {\r\n            // 确保包含AI响应结果\r\n            if (this.aiResponse && !updatedData.aiResponse) {\r\n                updatedData.aiResponse = this.aiResponse;\r\n            }\r\n\r\n            return updateUserRequirement(updatedData);\r\n        },\r\n        // 生成新推荐\r\n        generateNewRecommendation() {\r\n            this.currentStep = 0;\r\n            this.aiDescription = \"\";\r\n            this.aiProgress = 0;\r\n            this.aiTaskStatus = 'PROCESSING';\r\n            this.errorMessage = '';\r\n            this.submitStreamAiGenerationRequest(this.formData);\r\n        },\r\n        // 确保 Markdown 样式应用到 rich-text 内容\r\n        applyMarkdownStyles() {\r\n            // 适用于所有平台的样式处理\r\n            const pages = getCurrentPages();\r\n            const page = pages[pages.length - 1];\r\n\r\n            if (page && page.$getAppWebview) {\r\n                const webview = page.$getAppWebview();\r\n                if (webview) {\r\n                    // 注入辅助样式处理脚本\r\n                    webview.evalJS(`\r\n                        document.querySelectorAll('.markdown-content').forEach(el => {\r\n                            if(el._styled) return;\r\n                            el._styled = true;\r\n                            console.log('应用Markdown样式');\r\n                        });\r\n                    `);\r\n                }\r\n            }\r\n        },\r\n        // 加载用户历史需求\r\n        loadUserRequirements() {\r\n            const userId = uni.getStorageSync('uid');\r\n            if (!userId) {\r\n                console.log('用户未登录，无法加载历史需求');\r\n                return;\r\n            }\r\n\r\n            uni.showLoading({\r\n                title: '加载历史数据...'\r\n            });\r\n\r\n            getRequirementByUserId(userId).then(res => {\r\n                uni.hideLoading();\r\n                if (res.data && res.data.length > 0) {\r\n                    this.userRequirements = res.data.filter(item => item.requirementType === 'bracelet');\r\n                    console.log('加载到用户历史需求：', this.userRequirements.length);\r\n\r\n                    // 可以在这里添加一个弹窗，询问用户是否要加载历史需求\r\n                    if (this.userRequirements.length > 0) {\r\n                        uni.showModal({\r\n                            title: '发现历史需求',\r\n                            content: '是否加载您最近的手串需求？',\r\n                            success: res => {\r\n                                if (res.confirm) {\r\n                                    // 加载最新的一条需求\r\n                                    this.loadHistoryRequirement(this.userRequirements[0]);\r\n                                }\r\n                            }\r\n                        });\r\n                    }\r\n                }\r\n            }).catch(err => {\r\n                uni.hideLoading();\r\n                console.error('加载历史需求失败：', err);\r\n            });\r\n        },\r\n        // 处理AI生成完成 - 恢复模拟珠子数据生成\r\n        handleAiGenerationCompleted(result) {\r\n            try {\r\n                console.log('处理AI生成完成回调，当前步骤:', this.currentStep, '当前状态:', this.aiTaskStatus);\r\n\r\n                console.log('AI生成完成，准备展示结果:', result);\r\n\r\n                // 保存AI生成的描述\r\n                if (result && result.aiDescription) {\r\n                    this.aiDescription = result.aiDescription;\r\n\r\n                    // 确保格式化后的内容是正确的HTML字符串\r\n                    let htmlContent = this.renderMarkdown(result.aiDescription);\r\n\r\n                    // 微信小程序的rich-text组件需要特殊处理\r\n                    if (uni.getSystemInfoSync().platform === 'mp-weixin') {\r\n                        htmlContent = htmlContent.replace(/<(?!\\/?(h\\d|p|strong|em|ul|ol|li|blockquote|img)[>\\s])/g, '&lt;');\r\n                    }\r\n\r\n                    this.formattedAiDescription = htmlContent;\r\n\r\n                    // 保存到aiResponse字段以便后续更新\r\n                    this.aiResponse = result.aiDescription;\r\n                }\r\n\r\n                // 更新状态 - 先设置状态再切换步骤\r\n                this.aiTaskStatus = 'COMPLETED';\r\n                this.force_update = Date.now(); // 强制触发视图更新\r\n                console.log('设置状态为COMPLETED，准备切换到结果页面');\r\n\r\n                // 明确地切换到结果展示步骤\r\n                // this.currentStep = 2;\r\n\r\n                // 滚动到推荐内容区域\r\n                this.$nextTick(() => {\r\n                    console.log('AI响应完成，滚动到底部');\r\n                    this.scrollToBottom();\r\n                });\r\n\r\n                // 模拟AI生成过程\r\n                setTimeout(() => {\r\n\r\n                    if (result && result.recommendedProducts) {\r\n                        this.beadsList = result.recommendedProducts;\r\n                    }\r\n                    if (this.beadsList.length <= 0) {\r\n                        this.generateMockRecommendation();\r\n                    }\r\n                    this.currentStep = 2;\r\n                    console.log('已切换到结果展示步骤，当前步骤:', this.currentStep);\r\n                    // 生成珠子布局\r\n                    this.generateMarbles();\r\n\r\n                }, 1500);\r\n            } catch (error) {\r\n                console.error('处理AI响应时出错:', error);\r\n                // 发生错误时也要确保切换到结果页面\r\n                this.currentStep = 2;\r\n                this.force_update = Date.now(); // 强制触发视图更新\r\n                console.log('发生错误，强制切换到结果页面，当前步骤:', this.currentStep);\r\n            }\r\n        },\r\n\r\n    }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.ai-recommend {\r\n    min-height: 100vh;\r\n    background-color: #f8f8f8;\r\n    padding-bottom: 40rpx;\r\n}\r\n\r\n/* 步骤指示器样式 */\r\n.steps-container {\r\n    padding: 30rpx 0;\r\n    background-color: #fff;\r\n    margin-bottom: 20rpx;\r\n}\r\n\r\n.steps {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    padding: 0 40rpx;\r\n}\r\n\r\n.step {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    position: relative;\r\n    flex: 1;\r\n}\r\n\r\n.step-number {\r\n    width: 50rpx;\r\n    height: 50rpx;\r\n    border-radius: 50%;\r\n    background-color: #ddd;\r\n    color: #fff;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    font-size: 28rpx;\r\n    margin-bottom: 10rpx;\r\n    z-index: 2;\r\n}\r\n\r\n.step.active .step-number {\r\n    background-color: #c9ab79;\r\n}\r\n\r\n.step-text {\r\n    font-size: 24rpx;\r\n    color: #666;\r\n}\r\n\r\n.step.active .step-text {\r\n    color: #c9ab79;\r\n}\r\n\r\n.step-line {\r\n    position: absolute;\r\n    top: 25rpx;\r\n    right: -50%;\r\n    width: 100%;\r\n    height: 2rpx;\r\n    background-color: #ddd;\r\n    z-index: 1;\r\n}\r\n\r\n.step.active .step-line {\r\n    background-color: #c9ab79;\r\n}\r\n\r\n/* 表单样式 */\r\n.form-container {\r\n    background-color: #fff;\r\n    padding: 30rpx;\r\n    border-radius: 20rpx;\r\n    margin: 0 20rpx;\r\n}\r\n\r\n.form-section {\r\n    margin-bottom: 40rpx;\r\n}\r\n\r\n.section-title {\r\n    font-size: 32rpx;\r\n    font-weight: bold;\r\n    color: #333;\r\n    margin-bottom: 30rpx;\r\n    padding-bottom: 15rpx;\r\n    border-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.form-item {\r\n    margin-bottom: 30rpx;\r\n}\r\n\r\n.form-label {\r\n    display: block;\r\n    font-size: 28rpx;\r\n    color: #666;\r\n    margin-bottom: 15rpx;\r\n}\r\n\r\n.form-input {\r\n    width: 100%;\r\n    height: 80rpx;\r\n    border: 1rpx solid #e0e0e0;\r\n    border-radius: 10rpx;\r\n    padding: 0 20rpx;\r\n    font-size: 28rpx;\r\n    box-sizing: border-box;\r\n}\r\n\r\n.radio-group {\r\n    display: flex;\r\n    gap: 30rpx;\r\n}\r\n\r\n.radio-item {\r\n    padding: 15rpx 40rpx;\r\n    border: 1rpx solid #e0e0e0;\r\n    border-radius: 10rpx;\r\n    font-size: 28rpx;\r\n    color: #666;\r\n}\r\n\r\n.radio-item.active {\r\n    background-color: #c9ab79;\r\n    color: #fff;\r\n    border-color: #c9ab79;\r\n}\r\n\r\n.form-picker {\r\n    width: 100%;\r\n    height: 80rpx;\r\n    border: 1rpx solid #e0e0e0;\r\n    border-radius: 10rpx;\r\n    padding: 0 20rpx;\r\n    font-size: 28rpx;\r\n    box-sizing: border-box;\r\n    display: flex;\r\n    align-items: center;\r\n}\r\n\r\n.picker-value {\r\n    color: #333;\r\n}\r\n\r\n.budget-slider {\r\n    padding: 20rpx 0;\r\n}\r\n\r\n.budget-value {\r\n    text-align: center;\r\n    font-size: 32rpx;\r\n    color: #c9ab79;\r\n    margin-top: 10rpx;\r\n    font-weight: bold;\r\n}\r\n\r\n.color-grid {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    gap: 20rpx;\r\n}\r\n\r\n.color-item {\r\n    width: 60rpx;\r\n    height: 60rpx;\r\n    border-radius: 50%;\r\n    border: 1rpx solid #e0e0e0;\r\n    box-sizing: border-box;\r\n}\r\n\r\n.color-item.active {\r\n    border: 3rpx solid #c9ab79;\r\n    transform: scale(1.1);\r\n}\r\n\r\n.checkbox-group {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    gap: 20rpx;\r\n}\r\n\r\n.checkbox-item {\r\n    padding: 10rpx 20rpx;\r\n    border: 1rpx solid #e0e0e0;\r\n    border-radius: 10rpx;\r\n    font-size: 26rpx;\r\n    color: #666;\r\n}\r\n\r\n.checkbox-item.active {\r\n    background-color: #c9ab79;\r\n    color: #fff;\r\n    border-color: #c9ab79;\r\n}\r\n\r\n.form-textarea {\r\n    width: 100%;\r\n    height: 200rpx;\r\n    border: 1rpx solid #e0e0e0;\r\n    border-radius: 10rpx;\r\n    padding: 20rpx;\r\n    font-size: 28rpx;\r\n    box-sizing: border-box;\r\n}\r\n\r\n.chakra-selector {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 20rpx;\r\n}\r\n\r\n.chakra-item {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 15rpx;\r\n    border: 1rpx solid #e0e0e0;\r\n    border-radius: 10rpx;\r\n    gap: 20rpx;\r\n}\r\n\r\n.chakra-item.active {\r\n    background-color: rgba(201, 171, 121, 0.1);\r\n    border-color: #c9ab79;\r\n}\r\n\r\n.chakra-color {\r\n    width: 40rpx;\r\n    height: 40rpx;\r\n    border-radius: 50%;\r\n}\r\n\r\n.chakra-name {\r\n    font-size: 28rpx;\r\n    color: #333;\r\n}\r\n\r\n.submit-button {\r\n    width: 100%;\r\n    height: 90rpx;\r\n    background-color: #c9ab79;\r\n    color: #fff;\r\n    font-size: 32rpx;\r\n    border-radius: 45rpx;\r\n    margin-top: 40rpx;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n}\r\n\r\n/* 生成中样式 */\r\n.generating {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n    min-height: 80vh;\r\n    background-color: #fff;\r\n    margin: 0 20rpx;\r\n    border-radius: 20rpx;\r\n    padding: 40rpx 30rpx;\r\n}\r\n\r\n.loading-animation {\r\n    display: flex;\r\n    gap: 20rpx;\r\n    margin-bottom: 40rpx;\r\n}\r\n\r\n.loading-circle {\r\n    width: 30rpx;\r\n    height: 30rpx;\r\n    border-radius: 50%;\r\n    background-color: #c9ab79;\r\n    animation: loading 1.5s infinite ease-in-out;\r\n}\r\n\r\n.loading-circle:nth-child(2) {\r\n    animation-delay: 0.5s;\r\n}\r\n\r\n.loading-circle:nth-child(3) {\r\n    animation-delay: 1s;\r\n}\r\n\r\n@keyframes loading {\r\n\r\n    0%,\r\n    100% {\r\n        transform: scale(0.5);\r\n        opacity: 0.5;\r\n    }\r\n\r\n    50% {\r\n        transform: scale(1);\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n.generating-text {\r\n    font-size: 36rpx;\r\n    color: #333;\r\n    margin-bottom: 20rpx;\r\n}\r\n\r\n.generating-subtext {\r\n    font-size: 28rpx;\r\n    color: #999;\r\n}\r\n\r\n/* AI生成进度条样式 */\r\n.progress-container {\r\n    width: 90%;\r\n    margin: 30rpx 0;\r\n}\r\n\r\n.progress-bar {\r\n    height: 20rpx;\r\n    background-color: #f0f0f0;\r\n    border-radius: 10rpx;\r\n    overflow: hidden;\r\n}\r\n\r\n.progress-fill {\r\n    height: 100%;\r\n    background-color: #c9ab79;\r\n    border-radius: 10rpx;\r\n    transition: width 0.3s ease;\r\n}\r\n\r\n.progress-text {\r\n    text-align: center;\r\n    font-size: 24rpx;\r\n    color: #999;\r\n    margin-top: 10rpx;\r\n}\r\n\r\n/* AI输出显示 */\r\n.ai-output {\r\n    width: 100%;\r\n    margin-top: 30rpx;\r\n    padding: 20rpx;\r\n    background-color: #f9f6f0;\r\n    border-radius: 10rpx;\r\n    max-height: 40vh;\r\n    overflow-y: auto;\r\n}\r\n\r\n.output-title {\r\n    font-size: 28rpx;\r\n    color: #c9ab79;\r\n    margin-bottom: 10rpx;\r\n    font-weight: bold;\r\n}\r\n\r\n.output-content {\r\n    font-size: 26rpx;\r\n    color: #666;\r\n    line-height: 1.6;\r\n    white-space: pre-wrap;\r\n    position: relative;\r\n}\r\n\r\n.typing-cursor {\r\n    display: inline-block;\r\n    width: 2px;\r\n    height: 18px;\r\n    background-color: #666;\r\n    margin-left: 2px;\r\n    vertical-align: middle;\r\n    animation: cursor-blink 0.8s infinite;\r\n}\r\n\r\n@keyframes cursor-blink {\r\n\r\n    0%,\r\n    100% {\r\n        opacity: 0;\r\n    }\r\n\r\n    50% {\r\n        opacity: 1;\r\n    }\r\n}\r\n\r\n/* 结果展示样式 */\r\n.result-display {\r\n    background-color: #fff;\r\n    border-radius: 20rpx;\r\n    margin: 0 20rpx;\r\n    padding: 30rpx;\r\n}\r\n\r\n.bracelet-prediv {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    margin: 40rpx 0;\r\n    position: relative;\r\n}\r\n\r\n.circle-container {\r\n    position: relative;\r\n    border-radius: 50%;\r\n    border: 1rpx solid #e0e0e0;\r\n}\r\n\r\n.marble {\r\n    position: absolute;\r\n    border-radius: 50%;\r\n    overflow: hidden;\r\n    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.recommendation-details {\r\n    margin-top: 40rpx;\r\n}\r\n\r\n.recommendation-header {\r\n    font-size: 36rpx;\r\n    font-weight: bold;\r\n    color: #333;\r\n    margin-bottom: 20rpx;\r\n    text-align: center;\r\n}\r\n\r\n.recommendation-body {\r\n    height: 700rpx;\r\n    width: 100%;\r\n}\r\n\r\n.ai-generation-status {\r\n    padding: 20rpx;\r\n    background-color: #f9f5eb;\r\n    border-radius: 8rpx;\r\n    margin-bottom: 20rpx;\r\n}\r\n\r\n.loading-animation {\r\n    display: flex;\r\n    justify-content: center;\r\n    margin-bottom: 10rpx;\r\n}\r\n\r\n.loading-animation .dot {\r\n    width: 12rpx;\r\n    height: 12rpx;\r\n    background-color: #c9ab79;\r\n    border-radius: 50%;\r\n    margin: 0 6rpx;\r\n    animation: bounce 1.4s infinite ease-in-out both;\r\n}\r\n\r\n.loading-animation .dot:nth-child(1) {\r\n    animation-delay: -0.32s;\r\n}\r\n\r\n.loading-animation .dot:nth-child(2) {\r\n    animation-delay: -0.16s;\r\n}\r\n\r\n@keyframes bounce {\r\n\r\n    0%,\r\n    80%,\r\n    100% {\r\n        transform: scale(0);\r\n    }\r\n\r\n    40% {\r\n        transform: scale(1);\r\n    }\r\n}\r\n\r\n.generation-text {\r\n    font-size: 28rpx;\r\n    color: #666;\r\n    text-align: center;\r\n    margin-bottom: 15rpx;\r\n    display: block;\r\n}\r\n\r\n.progress-bar {\r\n    height: 10rpx;\r\n    background-color: #e0e0e0;\r\n    border-radius: 10rpx;\r\n    overflow: hidden;\r\n    margin-bottom: 10rpx;\r\n}\r\n\r\n.progress-filled {\r\n    height: 100%;\r\n    background-color: #c9ab79;\r\n    border-radius: 10rpx;\r\n    transition: width 0.3s;\r\n}\r\n\r\n.progress-text {\r\n    font-size: 24rpx;\r\n    color: #888;\r\n    text-align: center;\r\n    display: block;\r\n}\r\n\r\n.ai-output-container {\r\n    padding: 15rpx;\r\n    background-color: #fff;\r\n    border-radius: 8rpx;\r\n    margin: 10rpx 0;\r\n}\r\n\r\n.action-buttons {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    margin-top: 30rpx;\r\n    padding: 0 20rpx;\r\n}\r\n\r\n.action-buttons-row {\r\n    padding: 0 20rpx 30rpx;\r\n}\r\n\r\n.action-buttons-fixed {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    margin-top: 30rpx;\r\n    padding: 0 20rpx 30rpx;\r\n    position: fixed;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background-color: #fff;\r\n    border-top: 1px solid #f0f0f0;\r\n    z-index: 100;\r\n}\r\n\r\n.action-button {\r\n    flex: 1;\r\n    margin: 0 10rpx;\r\n    height: 80rpx;\r\n    line-height: 80rpx;\r\n    text-align: center;\r\n    border-radius: 40rpx;\r\n    font-size: 28rpx;\r\n    background-color: #c9ab79;\r\n    color: #fff;\r\n}\r\n\r\n.action-button.outline {\r\n    background-color: transparent;\r\n    border: 1px solid #c9ab79;\r\n    color: #c9ab79;\r\n}\r\n\r\n.edit-button,\r\n.confirm-button {\r\n    flex: 1;\r\n    margin: 0 10rpx;\r\n    height: 80rpx;\r\n    line-height: 80rpx;\r\n    text-align: center;\r\n    border-radius: 40rpx;\r\n    font-size: 28rpx;\r\n}\r\n\r\n.edit-button {\r\n    background-color: #f8f8f8;\r\n    color: #666;\r\n    border: 1px solid #e0e0e0;\r\n}\r\n\r\n.confirm-button {\r\n    background-color: #c9ab79;\r\n    color: #fff;\r\n}\r\n\r\n.error-message {\r\n    padding: 20rpx;\r\n    color: #ff4d4f;\r\n    text-align: center;\r\n    font-size: 28rpx;\r\n}\r\n\r\n/* 优化Markdown样式 - 确保在微信小程序环境中正确渲染 */\r\n.markdown-content {\r\n    font-size: 28rpx;\r\n    line-height: 1.5;\r\n    white-space: pre-wrap;\r\n    color: #333;\r\n    padding: 10rpx 0;\r\n}\r\n\r\n/* 修复微信小程序不支持 >>> 选择器的问题 */\r\n.h1,\r\nrich-text .h1 {\r\n    font-size: 36rpx;\r\n    font-weight: bold;\r\n    color: #333;\r\n    margin: 10rpx 0;\r\n    border-bottom: 1px solid #eee;\r\n    padding-bottom: 10rpx;\r\n}\r\n\r\n.h2,\r\nrich-text .h2 {\r\n    font-size: 32rpx;\r\n    font-weight: bold;\r\n    color: #333;\r\n    margin: 10rpx 0;\r\n    border-bottom: 1px solid #f3f3f3;\r\n    padding-bottom: 8rpx;\r\n}\r\n\r\n.h3,\r\nrich-text .h3 {\r\n    font-size: 30rpx;\r\n    font-weight: bold;\r\n    color: #333;\r\n    margin: 10rpx 0;\r\n}\r\n\r\n.p,\r\nrich-text .p {\r\n    margin: 8rpx 0;\r\n    line-height: 1.5;\r\n}\r\n\r\n.ul,\r\n.ol,\r\nrich-text .ul,\r\nrich-text .ol {\r\n    padding-left: 30rpx;\r\n    margin: 8rpx 0 15rpx 0;\r\n}\r\n\r\n.li,\r\nrich-text .li {\r\n    margin: 5rpx 0;\r\n}\r\n\r\n.blockquote,\r\nrich-text .blockquote {\r\n    border-left: 4rpx solid #c9ab79;\r\n    padding: 8rpx 16rpx;\r\n    margin: 10rpx 0;\r\n    background-color: #f9f5eb;\r\n    color: #666;\r\n}\r\n\r\n.strong,\r\nrich-text .strong {\r\n    font-weight: bold;\r\n    color: #333;\r\n}\r\n\r\n.em,\r\nrich-text .em {\r\n    font-style: italic;\r\n}\r\n\r\n/* 修复图片和其他元素的样式 */\r\n.img,\r\nrich-text .img {\r\n    max-width: 100%;\r\n    height: auto;\r\n    display: block;\r\n    margin: 10rpx auto;\r\n    border-radius: 6rpx;\r\n}\r\n\r\n/* 确保珠子列表样式正确 */\r\n.beads-list {\r\n    margin-top: 20rpx;\r\n    border-top: 1px solid #eee;\r\n    padding-top: 20rpx;\r\n    background-color: #fff;\r\n    border-radius: 8rpx;\r\n}\r\n\r\n.beads-section-title {\r\n    font-size: 32rpx;\r\n    font-weight: bold;\r\n    margin-bottom: 20rpx;\r\n    color: #333;\r\n}\r\n\r\n.bead-item {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 20rpx;\r\n    border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.bead-item image {\r\n    width: 80rpx;\r\n    height: 80rpx;\r\n    border-radius: 50%;\r\n    margin-right: 20rpx;\r\n    background-color: #f5f5f5;\r\n    /* 占位背景色，防止图片加载失败时显示空白 */\r\n}\r\n\r\n.bead-info {\r\n    flex: 1;\r\n}\r\n\r\n.bead-name {\r\n    font-size: 28rpx;\r\n    color: #333;\r\n    margin-bottom: 6rpx;\r\n    font-weight: bold;\r\n}\r\n\r\n.bead-effect {\r\n    font-size: 24rpx;\r\n    color: #666;\r\n}\r\n\r\n.total-price {\r\n    text-align: right;\r\n    padding: 20rpx;\r\n    font-size: 30rpx;\r\n    font-weight: bold;\r\n    color: #c9ab79;\r\n}\r\n</style>", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./axureIndex.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./axureIndex.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363904109\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}