{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\element-ui\\lib\\select.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\element-ui\\lib\\select.js", "mtime": 1753666303636}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}], "contextDependencies": [], "result": ["\"use strict\";\n\nfunction _typeof2(o) { \"@babel/helpers - typeof\"; return _typeof2 = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof2(o); }\nmodule.exports = /******/function (modules) {\n  // webpackBootstrap\n  /******/ // The module cache\n  /******/\n  var installedModules = {};\n  /******/\n  /******/ // The require function\n  /******/\n  function __webpack_require__(moduleId) {\n    /******/\n    /******/ // Check if module is in cache\n    /******/if (installedModules[moduleId]) {\n      /******/return installedModules[moduleId].exports;\n      /******/\n    }\n    /******/ // Create a new module (and put it into the cache)\n    /******/\n    var module = installedModules[moduleId] = {\n      /******/i: moduleId,\n      /******/l: false,\n      /******/exports: {}\n      /******/\n    };\n    /******/\n    /******/ // Execute the module function\n    /******/\n    modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n    /******/\n    /******/ // Flag the module as loaded\n    /******/\n    module.l = true;\n    /******/\n    /******/ // Return the exports of the module\n    /******/\n    return module.exports;\n    /******/\n  }\n  /******/\n  /******/\n  /******/ // expose the modules object (__webpack_modules__)\n  /******/\n  __webpack_require__.m = modules;\n  /******/\n  /******/ // expose the module cache\n  /******/\n  __webpack_require__.c = installedModules;\n  /******/\n  /******/ // define getter function for harmony exports\n  /******/\n  __webpack_require__.d = function (exports, name, getter) {\n    /******/if (!__webpack_require__.o(exports, name)) {\n      /******/Object.defineProperty(exports, name, {\n        enumerable: true,\n        get: getter\n      });\n      /******/\n    }\n    /******/\n  };\n  /******/\n  /******/ // define __esModule on exports\n  /******/\n  __webpack_require__.r = function (exports) {\n    /******/if (typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n      /******/Object.defineProperty(exports, Symbol.toStringTag, {\n        value: 'Module'\n      });\n      /******/\n    }\n    /******/\n    Object.defineProperty(exports, '__esModule', {\n      value: true\n    });\n    /******/\n  };\n  /******/\n  /******/ // create a fake namespace object\n  /******/ // mode & 1: value is a module id, require it\n  /******/ // mode & 2: merge all properties of value into the ns\n  /******/ // mode & 4: return value when already ns object\n  /******/ // mode & 8|1: behave like require\n  /******/\n  __webpack_require__.t = function (value, mode) {\n    /******/if (mode & 1) value = __webpack_require__(value);\n    /******/\n    if (mode & 8) return value;\n    /******/\n    if (mode & 4 && _typeof2(value) === 'object' && value && value.__esModule) return value;\n    /******/\n    var ns = Object.create(null);\n    /******/\n    __webpack_require__.r(ns);\n    /******/\n    Object.defineProperty(ns, 'default', {\n      enumerable: true,\n      value: value\n    });\n    /******/\n    if (mode & 2 && typeof value != 'string') for (var key in value) __webpack_require__.d(ns, key, function (key) {\n      return value[key];\n    }.bind(null, key));\n    /******/\n    return ns;\n    /******/\n  };\n  /******/\n  /******/ // getDefaultExport function for compatibility with non-harmony modules\n  /******/\n  __webpack_require__.n = function (module) {\n    /******/var getter = module && module.__esModule ? /******/function getDefault() {\n      return module['default'];\n    } : /******/function getModuleExports() {\n      return module;\n    };\n    /******/\n    __webpack_require__.d(getter, 'a', getter);\n    /******/\n    return getter;\n    /******/\n  };\n  /******/\n  /******/ // Object.prototype.hasOwnProperty.call\n  /******/\n  __webpack_require__.o = function (object, property) {\n    return Object.prototype.hasOwnProperty.call(object, property);\n  };\n  /******/\n  /******/ // __webpack_public_path__\n  /******/\n  __webpack_require__.p = \"/dist/\";\n  /******/\n  /******/\n  /******/ // Load entry module and return exports\n  /******/\n  return __webpack_require__(__webpack_require__.s = 61);\n  /******/\n}\n/************************************************************************/\n/******/([(/* 0 */\n/***/function (module, __webpack_exports__, __webpack_require__) {\n  \"use strict\";\n\n  /* harmony export (binding) */\n  __webpack_require__.d(__webpack_exports__, \"a\", function () {\n    return normalizeComponent;\n  });\n  /* globals __VUE_SSR_CONTEXT__ */\n\n  // IMPORTANT: Do NOT use ES2015 features in this file (except for modules).\n  // This module is a runtime utility for cleaner component module output and will\n  // be included in the final webpack user bundle.\n\n  function normalizeComponent(scriptExports, render, staticRenderFns, functionalTemplate, injectStyles, scopeId, moduleIdentifier, /* server only */\n  shadowMode /* vue-cli only */) {\n    // Vue.extend constructor export interop\n    var options = typeof scriptExports === 'function' ? scriptExports.options : scriptExports;\n\n    // render functions\n    if (render) {\n      options.render = render;\n      options.staticRenderFns = staticRenderFns;\n      options._compiled = true;\n    }\n\n    // functional template\n    if (functionalTemplate) {\n      options.functional = true;\n    }\n\n    // scopedId\n    if (scopeId) {\n      options._scopeId = 'data-v-' + scopeId;\n    }\n    var hook;\n    if (moduleIdentifier) {\n      // server build\n      hook = function hook(context) {\n        // 2.3 injection\n        context = context ||\n        // cached call\n        this.$vnode && this.$vnode.ssrContext ||\n        // stateful\n        this.parent && this.parent.$vnode && this.parent.$vnode.ssrContext; // functional\n        // 2.2 with runInNewContext: true\n        if (!context && typeof __VUE_SSR_CONTEXT__ !== 'undefined') {\n          context = __VUE_SSR_CONTEXT__;\n        }\n        // inject component styles\n        if (injectStyles) {\n          injectStyles.call(this, context);\n        }\n        // register component module identifier for async chunk inferrence\n        if (context && context._registeredComponents) {\n          context._registeredComponents.add(moduleIdentifier);\n        }\n      };\n      // used by ssr in case component is cached and beforeCreate\n      // never gets called\n      options._ssrRegister = hook;\n    } else if (injectStyles) {\n      hook = shadowMode ? function () {\n        injectStyles.call(this, this.$root.$options.shadowRoot);\n      } : injectStyles;\n    }\n    if (hook) {\n      if (options.functional) {\n        // for template-only hot-reload because in that case the render fn doesn't\n        // go through the normalizer\n        options._injectStyles = hook;\n        // register for functioal component in vue file\n        var originalRender = options.render;\n        options.render = function renderWithStyleInjection(h, context) {\n          hook.call(context);\n          return originalRender(h, context);\n        };\n      } else {\n        // inject component registration as beforeCreate hook\n        var existing = options.beforeCreate;\n        options.beforeCreate = existing ? [].concat(existing, hook) : [hook];\n      }\n    }\n    return {\n      exports: scriptExports,\n      options: options\n    };\n  }\n\n  /***/\n}),\n\n  /* 1 */\n  /* 2 */\n\n  /* 7 */\n  /* 8 */\n  /* 9 */\n\n  /* 11 */\n\n  /* 13 */\n\n  /* 15 */\n\n  /* 18 */\n\n  /* 20 */\n\n  /* 23 */\n  /* 24 */\n  /* 25 */\n  /* 26 */\n  /* 27 */\n  /* 28 */\n  /* 29 */\n  /* 30 */\n\n  /* 32 */\n  /* 33 */\n\n  /* 35 */\n  /* 36 */\n  /* 37 */\n\n  /* 39 */\n  /* 40 */\n  /* 41 */\n  /* 42 */\n  /* 43 */\n  /* 44 */\n  /* 45 */\n  /* 46 */\n  /* 47 */\n  /* 48 */\n  /* 49 */\n  /* 50 */\n  /* 51 */\n  /* 52 */\n  /* 53 */\n  /* 54 */\n  /* 55 */\n  /* 56 */\n  /* 57 */\n  /* 58 */\n  /* 59 */\n  /* 60 */\n,, (/* 3 */\n/***/function (module, exports) {\n  module.exports = require(\"element-ui/lib/utils/util\");\n\n  /***/\n}), (/* 4 */\n/***/function (module, exports) {\n  module.exports = require(\"element-ui/lib/mixins/emitter\");\n\n  /***/\n}), (/* 5 */\n/***/function (module, exports) {\n  module.exports = require(\"element-ui/lib/utils/vue-popper\");\n\n  /***/\n}), (/* 6 */\n/***/function (module, exports) {\n  module.exports = require(\"element-ui/lib/mixins/locale\");\n\n  /***/\n}),,,, (/* 10 */\n/***/function (module, exports) {\n  module.exports = require(\"element-ui/lib/input\");\n\n  /***/\n}),, (/* 12 */\n/***/function (module, exports) {\n  module.exports = require(\"element-ui/lib/utils/clickoutside\");\n\n  /***/\n}),, (/* 14 */\n/***/function (module, exports) {\n  module.exports = require(\"element-ui/lib/scrollbar\");\n\n  /***/\n}),, (/* 16 */\n/***/function (module, exports) {\n  module.exports = require(\"element-ui/lib/utils/resize-event\");\n\n  /***/\n}), (/* 17 */\n/***/function (module, exports) {\n  module.exports = require(\"throttle-debounce/debounce\");\n\n  /***/\n}),, (/* 19 */\n/***/function (module, exports) {\n  module.exports = require(\"element-ui/lib/locale\");\n\n  /***/\n}),, (/* 21 */\n/***/function (module, exports) {\n  module.exports = require(\"element-ui/lib/utils/shared\");\n\n  /***/\n}), (/* 22 */\n/***/function (module, exports) {\n  module.exports = require(\"element-ui/lib/mixins/focus\");\n\n  /***/\n}),,,,,,,,, (/* 31 */\n/***/function (module, exports) {\n  module.exports = require(\"element-ui/lib/utils/scroll-into-view\");\n\n  /***/\n}),,, (/* 34 */\n/***/function (module, __webpack_exports__, __webpack_require__) {\n  \"use strict\";\n\n  // CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/vue-loader/lib??vue-loader-options!./packages/select/src/option.vue?vue&type=template&id=7a44c642&\n  var render = function render() {\n    var _vm = this;\n    var _h = _vm.$createElement;\n    var _c = _vm._self._c || _h;\n    return _c(\"li\", {\n      directives: [{\n        name: \"show\",\n        rawName: \"v-show\",\n        value: _vm.visible,\n        expression: \"visible\"\n      }],\n      staticClass: \"el-select-dropdown__item\",\n      class: {\n        selected: _vm.itemSelected,\n        \"is-disabled\": _vm.disabled || _vm.groupDisabled || _vm.limitReached,\n        hover: _vm.hover\n      },\n      on: {\n        mouseenter: _vm.hoverItem,\n        click: function click($event) {\n          $event.stopPropagation();\n          return _vm.selectOptionClick($event);\n        }\n      }\n    }, [_vm._t(\"default\", [_c(\"span\", [_vm._v(_vm._s(_vm.currentLabel))])])], 2);\n  };\n  var staticRenderFns = [];\n  render._withStripped = true;\n\n  // CONCATENATED MODULE: ./packages/select/src/option.vue?vue&type=template&id=7a44c642&\n\n  // EXTERNAL MODULE: external \"element-ui/lib/mixins/emitter\"\n  var emitter_ = __webpack_require__(4);\n  var emitter_default = /*#__PURE__*/__webpack_require__.n(emitter_);\n\n  // EXTERNAL MODULE: external \"element-ui/lib/utils/util\"\n  var util_ = __webpack_require__(3);\n\n  // CONCATENATED MODULE: ./node_modules/babel-loader/lib!./node_modules/vue-loader/lib??vue-loader-options!./packages/select/src/option.vue?vue&type=script&lang=js&\n  var _typeof = typeof Symbol === \"function\" && _typeof2(Symbol.iterator) === \"symbol\" ? function (obj) {\n    return _typeof2(obj);\n  } : function (obj) {\n    return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : _typeof2(obj);\n  };\n\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n\n  /* harmony default export */\n  var optionvue_type_script_lang_js_ = {\n    mixins: [emitter_default.a],\n    name: 'ElOption',\n    componentName: 'ElOption',\n    inject: ['select'],\n    props: {\n      value: {\n        required: true\n      },\n      label: [String, Number],\n      created: Boolean,\n      disabled: {\n        type: Boolean,\n        default: false\n      }\n    },\n    data: function data() {\n      return {\n        index: -1,\n        groupDisabled: false,\n        visible: true,\n        hitState: false,\n        hover: false\n      };\n    },\n    computed: {\n      isObject: function isObject() {\n        return Object.prototype.toString.call(this.value).toLowerCase() === '[object object]';\n      },\n      currentLabel: function currentLabel() {\n        return this.label || (this.isObject ? '' : this.value);\n      },\n      currentValue: function currentValue() {\n        return this.value || this.label || '';\n      },\n      itemSelected: function itemSelected() {\n        if (!this.select.multiple) {\n          return this.isEqual(this.value, this.select.value);\n        } else {\n          return this.contains(this.select.value, this.value);\n        }\n      },\n      limitReached: function limitReached() {\n        if (this.select.multiple) {\n          return !this.itemSelected && (this.select.value || []).length >= this.select.multipleLimit && this.select.multipleLimit > 0;\n        } else {\n          return false;\n        }\n      }\n    },\n    watch: {\n      currentLabel: function currentLabel() {\n        if (!this.created && !this.select.remote) this.dispatch('ElSelect', 'setSelected');\n      },\n      value: function value(val, oldVal) {\n        var _select = this.select,\n          remote = _select.remote,\n          valueKey = _select.valueKey;\n        if (!this.created && !remote) {\n          if (valueKey && (typeof val === 'undefined' ? 'undefined' : _typeof(val)) === 'object' && (typeof oldVal === 'undefined' ? 'undefined' : _typeof(oldVal)) === 'object' && val[valueKey] === oldVal[valueKey]) {\n            return;\n          }\n          this.dispatch('ElSelect', 'setSelected');\n        }\n      }\n    },\n    methods: {\n      isEqual: function isEqual(a, b) {\n        if (!this.isObject) {\n          return a === b;\n        } else {\n          var valueKey = this.select.valueKey;\n          return Object(util_[\"getValueByPath\"])(a, valueKey) === Object(util_[\"getValueByPath\"])(b, valueKey);\n        }\n      },\n      contains: function contains() {\n        var arr = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n        var target = arguments[1];\n        if (!this.isObject) {\n          return arr && arr.indexOf(target) > -1;\n        } else {\n          var valueKey = this.select.valueKey;\n          return arr && arr.some(function (item) {\n            return Object(util_[\"getValueByPath\"])(item, valueKey) === Object(util_[\"getValueByPath\"])(target, valueKey);\n          });\n        }\n      },\n      handleGroupDisabled: function handleGroupDisabled(val) {\n        this.groupDisabled = val;\n      },\n      hoverItem: function hoverItem() {\n        if (!this.disabled && !this.groupDisabled) {\n          this.select.hoverIndex = this.select.options.indexOf(this);\n        }\n      },\n      selectOptionClick: function selectOptionClick() {\n        if (this.disabled !== true && this.groupDisabled !== true) {\n          this.dispatch('ElSelect', 'handleOptionClick', [this, true]);\n        }\n      },\n      queryChange: function queryChange(query) {\n        this.visible = new RegExp(Object(util_[\"escapeRegexpString\"])(query), 'i').test(this.currentLabel) || this.created;\n        if (!this.visible) {\n          this.select.filteredOptionsCount--;\n        }\n      }\n    },\n    created: function created() {\n      this.select.options.push(this);\n      this.select.cachedOptions.push(this);\n      this.select.optionsCount++;\n      this.select.filteredOptionsCount++;\n      this.$on('queryChange', this.queryChange);\n      this.$on('handleGroupDisabled', this.handleGroupDisabled);\n    },\n    beforeDestroy: function beforeDestroy() {\n      var _select2 = this.select,\n        selected = _select2.selected,\n        multiple = _select2.multiple;\n      var selectedOptions = multiple ? selected : [selected];\n      var index = this.select.cachedOptions.indexOf(this);\n      var selectedIndex = selectedOptions.indexOf(this);\n\n      // if option is not selected, remove it from cache\n      if (index > -1 && selectedIndex < 0) {\n        this.select.cachedOptions.splice(index, 1);\n      }\n      this.select.onOptionDestroy(this.select.options.indexOf(this));\n    }\n  };\n  // CONCATENATED MODULE: ./packages/select/src/option.vue?vue&type=script&lang=js&\n  /* harmony default export */\n  var src_optionvue_type_script_lang_js_ = optionvue_type_script_lang_js_;\n  // EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js\n  var componentNormalizer = __webpack_require__(0);\n\n  // CONCATENATED MODULE: ./packages/select/src/option.vue\n\n  /* normalize component */\n\n  var component = Object(componentNormalizer[\"a\" /* default */])(src_optionvue_type_script_lang_js_, render, staticRenderFns, false, null, null, null);\n\n  /* hot reload */\n  if (false) {\n    var api;\n  }\n  component.options.__file = \"packages/select/src/option.vue\";\n  /* harmony default export */\n  var src_option = __webpack_exports__[\"a\"] = component.exports;\n\n  /***/\n}),,,, (/* 38 */\n/***/function (module, exports) {\n  module.exports = require(\"element-ui/lib/tag\");\n\n  /***/\n}),,,,,,,,,,,,,,,,,,,,,,, (/* 61 */\n/***/function (module, __webpack_exports__, __webpack_require__) {\n  \"use strict\";\n\n  __webpack_require__.r(__webpack_exports__);\n\n  // CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/vue-loader/lib??vue-loader-options!./packages/select/src/select.vue?vue&type=template&id=0e4aade6&\n  var render = function render() {\n    var _vm = this;\n    var _h = _vm.$createElement;\n    var _c = _vm._self._c || _h;\n    return _c(\"div\", {\n      directives: [{\n        name: \"clickoutside\",\n        rawName: \"v-clickoutside\",\n        value: _vm.handleClose,\n        expression: \"handleClose\"\n      }],\n      staticClass: \"el-select\",\n      class: [_vm.selectSize ? \"el-select--\" + _vm.selectSize : \"\"],\n      on: {\n        click: function click($event) {\n          $event.stopPropagation();\n          return _vm.toggleMenu($event);\n        }\n      }\n    }, [_vm.multiple ? _c(\"div\", {\n      ref: \"tags\",\n      staticClass: \"el-select__tags\",\n      style: {\n        \"max-width\": _vm.inputWidth - 32 + \"px\",\n        width: \"100%\"\n      }\n    }, [_vm.collapseTags && _vm.selected.length ? _c(\"span\", [_c(\"el-tag\", {\n      attrs: {\n        closable: !_vm.selectDisabled,\n        size: _vm.collapseTagSize,\n        hit: _vm.selected[0].hitState,\n        type: \"info\",\n        \"disable-transitions\": \"\"\n      },\n      on: {\n        close: function close($event) {\n          _vm.deleteTag($event, _vm.selected[0]);\n        }\n      }\n    }, [_c(\"span\", {\n      staticClass: \"el-select__tags-text\"\n    }, [_vm._v(_vm._s(_vm.selected[0].currentLabel))])]), _vm.selected.length > 1 ? _c(\"el-tag\", {\n      attrs: {\n        closable: false,\n        size: _vm.collapseTagSize,\n        type: \"info\",\n        \"disable-transitions\": \"\"\n      }\n    }, [_c(\"span\", {\n      staticClass: \"el-select__tags-text\"\n    }, [_vm._v(\"+ \" + _vm._s(_vm.selected.length - 1))])]) : _vm._e()], 1) : _vm._e(), !_vm.collapseTags ? _c(\"transition-group\", {\n      on: {\n        \"after-leave\": _vm.resetInputHeight\n      }\n    }, _vm._l(_vm.selected, function (item) {\n      return _c(\"el-tag\", {\n        key: _vm.getValueKey(item),\n        attrs: {\n          closable: !_vm.selectDisabled,\n          size: _vm.collapseTagSize,\n          hit: item.hitState,\n          type: \"info\",\n          \"disable-transitions\": \"\"\n        },\n        on: {\n          close: function close($event) {\n            _vm.deleteTag($event, item);\n          }\n        }\n      }, [_c(\"span\", {\n        staticClass: \"el-select__tags-text\"\n      }, [_vm._v(_vm._s(item.currentLabel))])]);\n    }), 1) : _vm._e(), _vm.filterable ? _c(\"input\", {\n      directives: [{\n        name: \"model\",\n        rawName: \"v-model\",\n        value: _vm.query,\n        expression: \"query\"\n      }],\n      ref: \"input\",\n      staticClass: \"el-select__input\",\n      class: [_vm.selectSize ? \"is-\" + _vm.selectSize : \"\"],\n      style: {\n        \"flex-grow\": \"1\",\n        width: _vm.inputLength / (_vm.inputWidth - 32) + \"%\",\n        \"max-width\": _vm.inputWidth - 42 + \"px\"\n      },\n      attrs: {\n        type: \"text\",\n        disabled: _vm.selectDisabled,\n        autocomplete: _vm.autoComplete || _vm.autocomplete\n      },\n      domProps: {\n        value: _vm.query\n      },\n      on: {\n        focus: _vm.handleFocus,\n        blur: function blur($event) {\n          _vm.softFocus = false;\n        },\n        keyup: _vm.managePlaceholder,\n        keydown: [_vm.resetInputState, function ($event) {\n          if (!(\"button\" in $event) && _vm._k($event.keyCode, \"down\", 40, $event.key, [\"Down\", \"ArrowDown\"])) {\n            return null;\n          }\n          $event.preventDefault();\n          _vm.navigateOptions(\"next\");\n        }, function ($event) {\n          if (!(\"button\" in $event) && _vm._k($event.keyCode, \"up\", 38, $event.key, [\"Up\", \"ArrowUp\"])) {\n            return null;\n          }\n          $event.preventDefault();\n          _vm.navigateOptions(\"prev\");\n        }, function ($event) {\n          if (!(\"button\" in $event) && _vm._k($event.keyCode, \"enter\", 13, $event.key, \"Enter\")) {\n            return null;\n          }\n          $event.preventDefault();\n          return _vm.selectOption($event);\n        }, function ($event) {\n          if (!(\"button\" in $event) && _vm._k($event.keyCode, \"esc\", 27, $event.key, [\"Esc\", \"Escape\"])) {\n            return null;\n          }\n          $event.stopPropagation();\n          $event.preventDefault();\n          _vm.visible = false;\n        }, function ($event) {\n          if (!(\"button\" in $event) && _vm._k($event.keyCode, \"delete\", [8, 46], $event.key, [\"Backspace\", \"Delete\", \"Del\"])) {\n            return null;\n          }\n          return _vm.deletePrevTag($event);\n        }, function ($event) {\n          if (!(\"button\" in $event) && _vm._k($event.keyCode, \"tab\", 9, $event.key, \"Tab\")) {\n            return null;\n          }\n          _vm.visible = false;\n        }],\n        compositionstart: _vm.handleComposition,\n        compositionupdate: _vm.handleComposition,\n        compositionend: _vm.handleComposition,\n        input: [function ($event) {\n          if ($event.target.composing) {\n            return;\n          }\n          _vm.query = $event.target.value;\n        }, _vm.debouncedQueryChange]\n      }\n    }) : _vm._e()], 1) : _vm._e(), _c(\"el-input\", {\n      ref: \"reference\",\n      class: {\n        \"is-focus\": _vm.visible\n      },\n      attrs: {\n        type: \"text\",\n        placeholder: _vm.currentPlaceholder,\n        name: _vm.name,\n        id: _vm.id,\n        autocomplete: _vm.autoComplete || _vm.autocomplete,\n        size: _vm.selectSize,\n        disabled: _vm.selectDisabled,\n        readonly: _vm.readonly,\n        \"validate-event\": false,\n        tabindex: _vm.multiple && _vm.filterable ? \"-1\" : null\n      },\n      on: {\n        focus: _vm.handleFocus,\n        blur: _vm.handleBlur\n      },\n      nativeOn: {\n        keyup: function keyup($event) {\n          return _vm.debouncedOnInputChange($event);\n        },\n        keydown: [function ($event) {\n          if (!(\"button\" in $event) && _vm._k($event.keyCode, \"down\", 40, $event.key, [\"Down\", \"ArrowDown\"])) {\n            return null;\n          }\n          $event.stopPropagation();\n          $event.preventDefault();\n          _vm.navigateOptions(\"next\");\n        }, function ($event) {\n          if (!(\"button\" in $event) && _vm._k($event.keyCode, \"up\", 38, $event.key, [\"Up\", \"ArrowUp\"])) {\n            return null;\n          }\n          $event.stopPropagation();\n          $event.preventDefault();\n          _vm.navigateOptions(\"prev\");\n        }, function ($event) {\n          if (!(\"button\" in $event) && _vm._k($event.keyCode, \"enter\", 13, $event.key, \"Enter\")) {\n            return null;\n          }\n          $event.preventDefault();\n          return _vm.selectOption($event);\n        }, function ($event) {\n          if (!(\"button\" in $event) && _vm._k($event.keyCode, \"esc\", 27, $event.key, [\"Esc\", \"Escape\"])) {\n            return null;\n          }\n          $event.stopPropagation();\n          $event.preventDefault();\n          _vm.visible = false;\n        }, function ($event) {\n          if (!(\"button\" in $event) && _vm._k($event.keyCode, \"tab\", 9, $event.key, \"Tab\")) {\n            return null;\n          }\n          _vm.visible = false;\n        }],\n        paste: function paste($event) {\n          return _vm.debouncedOnInputChange($event);\n        },\n        mouseenter: function mouseenter($event) {\n          _vm.inputHovering = true;\n        },\n        mouseleave: function mouseleave($event) {\n          _vm.inputHovering = false;\n        }\n      },\n      model: {\n        value: _vm.selectedLabel,\n        callback: function callback($$v) {\n          _vm.selectedLabel = $$v;\n        },\n        expression: \"selectedLabel\"\n      }\n    }, [_vm.$slots.prefix ? _c(\"template\", {\n      slot: \"prefix\"\n    }, [_vm._t(\"prefix\")], 2) : _vm._e(), _c(\"template\", {\n      slot: \"suffix\"\n    }, [_c(\"i\", {\n      directives: [{\n        name: \"show\",\n        rawName: \"v-show\",\n        value: !_vm.showClose,\n        expression: \"!showClose\"\n      }],\n      class: [\"el-select__caret\", \"el-input__icon\", \"el-icon-\" + _vm.iconClass]\n    }), _vm.showClose ? _c(\"i\", {\n      staticClass: \"el-select__caret el-input__icon el-icon-circle-close\",\n      on: {\n        click: _vm.handleClearClick\n      }\n    }) : _vm._e()])], 2), _c(\"transition\", {\n      attrs: {\n        name: \"el-zoom-in-top\"\n      },\n      on: {\n        \"before-enter\": _vm.handleMenuEnter,\n        \"after-leave\": _vm.doDestroy\n      }\n    }, [_c(\"el-select-menu\", {\n      directives: [{\n        name: \"show\",\n        rawName: \"v-show\",\n        value: _vm.visible && _vm.emptyText !== false,\n        expression: \"visible && emptyText !== false\"\n      }],\n      ref: \"popper\",\n      attrs: {\n        \"append-to-body\": _vm.popperAppendToBody\n      }\n    }, [_c(\"el-scrollbar\", {\n      directives: [{\n        name: \"show\",\n        rawName: \"v-show\",\n        value: _vm.options.length > 0 && !_vm.loading,\n        expression: \"options.length > 0 && !loading\"\n      }],\n      ref: \"scrollbar\",\n      class: {\n        \"is-empty\": !_vm.allowCreate && _vm.query && _vm.filteredOptionsCount === 0\n      },\n      attrs: {\n        tag: \"ul\",\n        \"wrap-class\": \"el-select-dropdown__wrap\",\n        \"view-class\": \"el-select-dropdown__list\"\n      }\n    }, [_vm.showNewOption ? _c(\"el-option\", {\n      attrs: {\n        value: _vm.query,\n        created: \"\"\n      }\n    }) : _vm._e(), _vm._t(\"default\")], 2), _vm.emptyText && (!_vm.allowCreate || _vm.loading || _vm.allowCreate && _vm.options.length === 0) ? [_vm.$slots.empty ? _vm._t(\"empty\") : _c(\"p\", {\n      staticClass: \"el-select-dropdown__empty\"\n    }, [_vm._v(\"\\n          \" + _vm._s(_vm.emptyText) + \"\\n        \")])] : _vm._e()], 2)], 1)], 1);\n  };\n  var staticRenderFns = [];\n  render._withStripped = true;\n\n  // CONCATENATED MODULE: ./packages/select/src/select.vue?vue&type=template&id=0e4aade6&\n\n  // EXTERNAL MODULE: external \"element-ui/lib/mixins/emitter\"\n  var emitter_ = __webpack_require__(4);\n  var emitter_default = /*#__PURE__*/__webpack_require__.n(emitter_);\n\n  // EXTERNAL MODULE: external \"element-ui/lib/mixins/focus\"\n  var focus_ = __webpack_require__(22);\n  var focus_default = /*#__PURE__*/__webpack_require__.n(focus_);\n\n  // EXTERNAL MODULE: external \"element-ui/lib/mixins/locale\"\n  var locale_ = __webpack_require__(6);\n  var locale_default = /*#__PURE__*/__webpack_require__.n(locale_);\n\n  // EXTERNAL MODULE: external \"element-ui/lib/input\"\n  var input_ = __webpack_require__(10);\n  var input_default = /*#__PURE__*/__webpack_require__.n(input_);\n\n  // CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/vue-loader/lib??vue-loader-options!./packages/select/src/select-dropdown.vue?vue&type=template&id=06828748&\n  var select_dropdownvue_type_template_id_06828748_render = function select_dropdownvue_type_template_id_06828748_render() {\n    var _vm = this;\n    var _h = _vm.$createElement;\n    var _c = _vm._self._c || _h;\n    return _c(\"div\", {\n      staticClass: \"el-select-dropdown el-popper\",\n      class: [{\n        \"is-multiple\": _vm.$parent.multiple\n      }, _vm.popperClass],\n      style: {\n        minWidth: _vm.minWidth\n      }\n    }, [_vm._t(\"default\")], 2);\n  };\n  var select_dropdownvue_type_template_id_06828748_staticRenderFns = [];\n  select_dropdownvue_type_template_id_06828748_render._withStripped = true;\n\n  // CONCATENATED MODULE: ./packages/select/src/select-dropdown.vue?vue&type=template&id=06828748&\n\n  // EXTERNAL MODULE: external \"element-ui/lib/utils/vue-popper\"\n  var vue_popper_ = __webpack_require__(5);\n  var vue_popper_default = /*#__PURE__*/__webpack_require__.n(vue_popper_);\n\n  // CONCATENATED MODULE: ./node_modules/babel-loader/lib!./node_modules/vue-loader/lib??vue-loader-options!./packages/select/src/select-dropdown.vue?vue&type=script&lang=js&\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n\n  /* harmony default export */\n  var select_dropdownvue_type_script_lang_js_ = {\n    name: 'ElSelectDropdown',\n    componentName: 'ElSelectDropdown',\n    mixins: [vue_popper_default.a],\n    props: {\n      placement: {\n        default: 'bottom-start'\n      },\n      boundariesPadding: {\n        default: 0\n      },\n      popperOptions: {\n        default: function _default() {\n          return {\n            gpuAcceleration: false\n          };\n        }\n      },\n      visibleArrow: {\n        default: true\n      },\n      appendToBody: {\n        type: Boolean,\n        default: true\n      }\n    },\n    data: function data() {\n      return {\n        minWidth: ''\n      };\n    },\n    computed: {\n      popperClass: function popperClass() {\n        return this.$parent.popperClass;\n      }\n    },\n    watch: {\n      '$parent.inputWidth': function $parentInputWidth() {\n        this.minWidth = this.$parent.$el.getBoundingClientRect().width + 'px';\n      }\n    },\n    mounted: function mounted() {\n      var _this = this;\n      this.referenceElm = this.$parent.$refs.reference.$el;\n      this.$parent.popperElm = this.popperElm = this.$el;\n      this.$on('updatePopper', function () {\n        if (_this.$parent.visible) _this.updatePopper();\n      });\n      this.$on('destroyPopper', this.destroyPopper);\n    }\n  };\n  // CONCATENATED MODULE: ./packages/select/src/select-dropdown.vue?vue&type=script&lang=js&\n  /* harmony default export */\n  var src_select_dropdownvue_type_script_lang_js_ = select_dropdownvue_type_script_lang_js_;\n  // EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js\n  var componentNormalizer = __webpack_require__(0);\n\n  // CONCATENATED MODULE: ./packages/select/src/select-dropdown.vue\n\n  /* normalize component */\n\n  var component = Object(componentNormalizer[\"a\" /* default */])(src_select_dropdownvue_type_script_lang_js_, select_dropdownvue_type_template_id_06828748_render, select_dropdownvue_type_template_id_06828748_staticRenderFns, false, null, null, null);\n\n  /* hot reload */\n  if (false) {\n    var api;\n  }\n  component.options.__file = \"packages/select/src/select-dropdown.vue\";\n  /* harmony default export */\n  var select_dropdown = component.exports;\n  // EXTERNAL MODULE: ./packages/select/src/option.vue + 4 modules\n  var src_option = __webpack_require__(34);\n\n  // EXTERNAL MODULE: external \"element-ui/lib/tag\"\n  var tag_ = __webpack_require__(38);\n  var tag_default = /*#__PURE__*/__webpack_require__.n(tag_);\n\n  // EXTERNAL MODULE: external \"element-ui/lib/scrollbar\"\n  var scrollbar_ = __webpack_require__(14);\n  var scrollbar_default = /*#__PURE__*/__webpack_require__.n(scrollbar_);\n\n  // EXTERNAL MODULE: external \"throttle-debounce/debounce\"\n  var debounce_ = __webpack_require__(17);\n  var debounce_default = /*#__PURE__*/__webpack_require__.n(debounce_);\n\n  // EXTERNAL MODULE: external \"element-ui/lib/utils/clickoutside\"\n  var clickoutside_ = __webpack_require__(12);\n  var clickoutside_default = /*#__PURE__*/__webpack_require__.n(clickoutside_);\n\n  // EXTERNAL MODULE: external \"element-ui/lib/utils/resize-event\"\n  var resize_event_ = __webpack_require__(16);\n\n  // EXTERNAL MODULE: external \"element-ui/lib/locale\"\n  var lib_locale_ = __webpack_require__(19);\n\n  // EXTERNAL MODULE: external \"element-ui/lib/utils/scroll-into-view\"\n  var scroll_into_view_ = __webpack_require__(31);\n  var scroll_into_view_default = /*#__PURE__*/__webpack_require__.n(scroll_into_view_);\n\n  // EXTERNAL MODULE: external \"element-ui/lib/utils/util\"\n  var util_ = __webpack_require__(3);\n\n  // CONCATENATED MODULE: ./packages/select/src/navigation-mixin.js\n  /* harmony default export */\n  var navigation_mixin = {\n    data: function data() {\n      return {\n        hoverOption: -1\n      };\n    },\n    computed: {\n      optionsAllDisabled: function optionsAllDisabled() {\n        return this.options.filter(function (option) {\n          return option.visible;\n        }).every(function (option) {\n          return option.disabled;\n        });\n      }\n    },\n    watch: {\n      hoverIndex: function hoverIndex(val) {\n        var _this = this;\n        if (typeof val === 'number' && val > -1) {\n          this.hoverOption = this.options[val] || {};\n        }\n        this.options.forEach(function (option) {\n          option.hover = _this.hoverOption === option;\n        });\n      }\n    },\n    methods: {\n      navigateOptions: function navigateOptions(direction) {\n        var _this2 = this;\n        if (!this.visible) {\n          this.visible = true;\n          return;\n        }\n        if (this.options.length === 0 || this.filteredOptionsCount === 0) return;\n        if (!this.optionsAllDisabled) {\n          if (direction === 'next') {\n            this.hoverIndex++;\n            if (this.hoverIndex === this.options.length) {\n              this.hoverIndex = 0;\n            }\n          } else if (direction === 'prev') {\n            this.hoverIndex--;\n            if (this.hoverIndex < 0) {\n              this.hoverIndex = this.options.length - 1;\n            }\n          }\n          var option = this.options[this.hoverIndex];\n          if (option.disabled === true || option.groupDisabled === true || !option.visible) {\n            this.navigateOptions(direction);\n          }\n          this.$nextTick(function () {\n            return _this2.scrollToOption(_this2.hoverOption);\n          });\n        }\n      }\n    }\n  };\n  // EXTERNAL MODULE: external \"element-ui/lib/utils/shared\"\n  var shared_ = __webpack_require__(21);\n\n  // CONCATENATED MODULE: ./node_modules/babel-loader/lib!./node_modules/vue-loader/lib??vue-loader-options!./packages/select/src/select.vue?vue&type=script&lang=js&\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n  //\n\n  /* harmony default export */\n  var selectvue_type_script_lang_js_ = {\n    mixins: [emitter_default.a, locale_default.a, focus_default()('reference'), navigation_mixin],\n    name: 'ElSelect',\n    componentName: 'ElSelect',\n    inject: {\n      elForm: {\n        default: ''\n      },\n      elFormItem: {\n        default: ''\n      }\n    },\n    provide: function provide() {\n      return {\n        'select': this\n      };\n    },\n    computed: {\n      _elFormItemSize: function _elFormItemSize() {\n        return (this.elFormItem || {}).elFormItemSize;\n      },\n      readonly: function readonly() {\n        return !this.filterable || this.multiple || !Object(util_[\"isIE\"])() && !Object(util_[\"isEdge\"])() && !this.visible;\n      },\n      showClose: function showClose() {\n        var hasValue = this.multiple ? Array.isArray(this.value) && this.value.length > 0 : this.value !== undefined && this.value !== null && this.value !== '';\n        var criteria = this.clearable && !this.selectDisabled && this.inputHovering && hasValue;\n        return criteria;\n      },\n      iconClass: function iconClass() {\n        return this.remote && this.filterable ? '' : this.visible ? 'arrow-up is-reverse' : 'arrow-up';\n      },\n      debounce: function debounce() {\n        return this.remote ? 300 : 0;\n      },\n      emptyText: function emptyText() {\n        if (this.loading) {\n          return this.loadingText || this.t('el.select.loading');\n        } else {\n          if (this.remote && this.query === '' && this.options.length === 0) return false;\n          if (this.filterable && this.query && this.options.length > 0 && this.filteredOptionsCount === 0) {\n            return this.noMatchText || this.t('el.select.noMatch');\n          }\n          if (this.options.length === 0) {\n            return this.noDataText || this.t('el.select.noData');\n          }\n        }\n        return null;\n      },\n      showNewOption: function showNewOption() {\n        var _this = this;\n        var hasExistingOption = this.options.filter(function (option) {\n          return !option.created;\n        }).some(function (option) {\n          return option.currentLabel === _this.query;\n        });\n        return this.filterable && this.allowCreate && this.query !== '' && !hasExistingOption;\n      },\n      selectSize: function selectSize() {\n        return this.size || this._elFormItemSize || (this.$ELEMENT || {}).size;\n      },\n      selectDisabled: function selectDisabled() {\n        return this.disabled || (this.elForm || {}).disabled;\n      },\n      collapseTagSize: function collapseTagSize() {\n        return ['small', 'mini'].indexOf(this.selectSize) > -1 ? 'mini' : 'small';\n      }\n    },\n    components: {\n      ElInput: input_default.a,\n      ElSelectMenu: select_dropdown,\n      ElOption: src_option[\"a\" /* default */],\n      ElTag: tag_default.a,\n      ElScrollbar: scrollbar_default.a\n    },\n    directives: {\n      Clickoutside: clickoutside_default.a\n    },\n    props: {\n      name: String,\n      id: String,\n      value: {\n        required: true\n      },\n      autocomplete: {\n        type: String,\n        default: 'off'\n      },\n      /** @Deprecated in next major version */\n      autoComplete: {\n        type: String,\n        validator: function validator(val) {\n          false && false;\n          return true;\n        }\n      },\n      automaticDropdown: Boolean,\n      size: String,\n      disabled: Boolean,\n      clearable: Boolean,\n      filterable: Boolean,\n      allowCreate: Boolean,\n      loading: Boolean,\n      popperClass: String,\n      remote: Boolean,\n      loadingText: String,\n      noMatchText: String,\n      noDataText: String,\n      remoteMethod: Function,\n      filterMethod: Function,\n      multiple: Boolean,\n      multipleLimit: {\n        type: Number,\n        default: 0\n      },\n      placeholder: {\n        type: String,\n        default: function _default() {\n          return Object(lib_locale_[\"t\"])('el.select.placeholder');\n        }\n      },\n      defaultFirstOption: Boolean,\n      reserveKeyword: Boolean,\n      valueKey: {\n        type: String,\n        default: 'value'\n      },\n      collapseTags: Boolean,\n      popperAppendToBody: {\n        type: Boolean,\n        default: true\n      }\n    },\n    data: function data() {\n      return {\n        options: [],\n        cachedOptions: [],\n        createdLabel: null,\n        createdSelected: false,\n        selected: this.multiple ? [] : {},\n        inputLength: 20,\n        inputWidth: 0,\n        initialInputHeight: 0,\n        cachedPlaceHolder: '',\n        optionsCount: 0,\n        filteredOptionsCount: 0,\n        visible: false,\n        softFocus: false,\n        selectedLabel: '',\n        hoverIndex: -1,\n        query: '',\n        previousQuery: null,\n        inputHovering: false,\n        currentPlaceholder: '',\n        menuVisibleOnFocus: false,\n        isOnComposition: false,\n        isSilentBlur: false\n      };\n    },\n    watch: {\n      selectDisabled: function selectDisabled() {\n        var _this2 = this;\n        this.$nextTick(function () {\n          _this2.resetInputHeight();\n        });\n      },\n      placeholder: function placeholder(val) {\n        this.cachedPlaceHolder = this.currentPlaceholder = val;\n      },\n      value: function value(val, oldVal) {\n        if (this.multiple) {\n          this.resetInputHeight();\n          if (val && val.length > 0 || this.$refs.input && this.query !== '') {\n            this.currentPlaceholder = '';\n          } else {\n            this.currentPlaceholder = this.cachedPlaceHolder;\n          }\n          if (this.filterable && !this.reserveKeyword) {\n            this.query = '';\n            this.handleQueryChange(this.query);\n          }\n        }\n        this.setSelected();\n        if (this.filterable && !this.multiple) {\n          this.inputLength = 20;\n        }\n        if (!Object(util_[\"valueEquals\"])(val, oldVal)) {\n          this.dispatch('ElFormItem', 'el.form.change', val);\n        }\n      },\n      visible: function visible(val) {\n        var _this3 = this;\n        if (!val) {\n          this.broadcast('ElSelectDropdown', 'destroyPopper');\n          if (this.$refs.input) {\n            this.$refs.input.blur();\n          }\n          this.query = '';\n          this.previousQuery = null;\n          this.selectedLabel = '';\n          this.inputLength = 20;\n          this.menuVisibleOnFocus = false;\n          this.resetHoverIndex();\n          this.$nextTick(function () {\n            if (_this3.$refs.input && _this3.$refs.input.value === '' && _this3.selected.length === 0) {\n              _this3.currentPlaceholder = _this3.cachedPlaceHolder;\n            }\n          });\n          if (!this.multiple) {\n            if (this.selected) {\n              if (this.filterable && this.allowCreate && this.createdSelected && this.createdLabel) {\n                this.selectedLabel = this.createdLabel;\n              } else {\n                this.selectedLabel = this.selected.currentLabel;\n              }\n              if (this.filterable) this.query = this.selectedLabel;\n            }\n            if (this.filterable) {\n              this.currentPlaceholder = this.cachedPlaceHolder;\n            }\n          }\n        } else {\n          this.broadcast('ElSelectDropdown', 'updatePopper');\n          if (this.filterable) {\n            this.query = this.remote ? '' : this.selectedLabel;\n            this.handleQueryChange(this.query);\n            if (this.multiple) {\n              this.$refs.input.focus();\n            } else {\n              if (!this.remote) {\n                this.broadcast('ElOption', 'queryChange', '');\n                this.broadcast('ElOptionGroup', 'queryChange');\n              }\n              if (this.selectedLabel) {\n                this.currentPlaceholder = this.selectedLabel;\n                this.selectedLabel = '';\n              }\n            }\n          }\n        }\n        this.$emit('visible-change', val);\n      },\n      options: function options() {\n        var _this4 = this;\n        if (this.$isServer) return;\n        this.$nextTick(function () {\n          _this4.broadcast('ElSelectDropdown', 'updatePopper');\n        });\n        if (this.multiple) {\n          this.resetInputHeight();\n        }\n        var inputs = this.$el.querySelectorAll('input');\n        if ([].indexOf.call(inputs, document.activeElement) === -1) {\n          this.setSelected();\n        }\n        if (this.defaultFirstOption && (this.filterable || this.remote) && this.filteredOptionsCount) {\n          this.checkDefaultFirstOption();\n        }\n      }\n    },\n    methods: {\n      handleComposition: function handleComposition(event) {\n        var _this5 = this;\n        var text = event.target.value;\n        if (event.type === 'compositionend') {\n          this.isOnComposition = false;\n          this.$nextTick(function (_) {\n            return _this5.handleQueryChange(text);\n          });\n        } else {\n          var lastCharacter = text[text.length - 1] || '';\n          this.isOnComposition = !Object(shared_[\"isKorean\"])(lastCharacter);\n        }\n      },\n      handleQueryChange: function handleQueryChange(val) {\n        var _this6 = this;\n        if (this.previousQuery === val || this.isOnComposition) return;\n        if (this.previousQuery === null && (typeof this.filterMethod === 'function' || typeof this.remoteMethod === 'function')) {\n          this.previousQuery = val;\n          return;\n        }\n        this.previousQuery = val;\n        this.$nextTick(function () {\n          if (_this6.visible) _this6.broadcast('ElSelectDropdown', 'updatePopper');\n        });\n        this.hoverIndex = -1;\n        if (this.multiple && this.filterable) {\n          this.$nextTick(function () {\n            var length = _this6.$refs.input.value.length * 15 + 20;\n            _this6.inputLength = _this6.collapseTags ? Math.min(50, length) : length;\n            _this6.managePlaceholder();\n            _this6.resetInputHeight();\n          });\n        }\n        if (this.remote && typeof this.remoteMethod === 'function') {\n          this.hoverIndex = -1;\n          this.remoteMethod(val);\n        } else if (typeof this.filterMethod === 'function') {\n          this.filterMethod(val);\n          this.broadcast('ElOptionGroup', 'queryChange');\n        } else {\n          this.filteredOptionsCount = this.optionsCount;\n          this.broadcast('ElOption', 'queryChange', val);\n          this.broadcast('ElOptionGroup', 'queryChange');\n        }\n        if (this.defaultFirstOption && (this.filterable || this.remote) && this.filteredOptionsCount) {\n          this.checkDefaultFirstOption();\n        }\n      },\n      scrollToOption: function scrollToOption(option) {\n        var target = Array.isArray(option) && option[0] ? option[0].$el : option.$el;\n        if (this.$refs.popper && target) {\n          var menu = this.$refs.popper.$el.querySelector('.el-select-dropdown__wrap');\n          scroll_into_view_default()(menu, target);\n        }\n        this.$refs.scrollbar && this.$refs.scrollbar.handleScroll();\n      },\n      handleMenuEnter: function handleMenuEnter() {\n        var _this7 = this;\n        this.$nextTick(function () {\n          return _this7.scrollToOption(_this7.selected);\n        });\n      },\n      emitChange: function emitChange(val) {\n        if (!Object(util_[\"valueEquals\"])(this.value, val)) {\n          this.$emit('change', val);\n        }\n      },\n      getOption: function getOption(value) {\n        var option = void 0;\n        var isObject = Object.prototype.toString.call(value).toLowerCase() === '[object object]';\n        var isNull = Object.prototype.toString.call(value).toLowerCase() === '[object null]';\n        var isUndefined = Object.prototype.toString.call(value).toLowerCase() === '[object undefined]';\n        for (var i = this.cachedOptions.length - 1; i >= 0; i--) {\n          var cachedOption = this.cachedOptions[i];\n          var isEqual = isObject ? Object(util_[\"getValueByPath\"])(cachedOption.value, this.valueKey) === Object(util_[\"getValueByPath\"])(value, this.valueKey) : cachedOption.value === value;\n          if (isEqual) {\n            option = cachedOption;\n            break;\n          }\n        }\n        if (option) return option;\n        var label = !isObject && !isNull && !isUndefined ? value : '';\n        var newOption = {\n          value: value,\n          currentLabel: label\n        };\n        if (this.multiple) {\n          newOption.hitState = false;\n        }\n        return newOption;\n      },\n      setSelected: function setSelected() {\n        var _this8 = this;\n        if (!this.multiple) {\n          var option = this.getOption(this.value);\n          if (option.created) {\n            this.createdLabel = option.currentLabel;\n            this.createdSelected = true;\n          } else {\n            this.createdSelected = false;\n          }\n          this.selectedLabel = option.currentLabel;\n          this.selected = option;\n          if (this.filterable) this.query = this.selectedLabel;\n          return;\n        }\n        var result = [];\n        if (Array.isArray(this.value)) {\n          this.value.forEach(function (value) {\n            result.push(_this8.getOption(value));\n          });\n        }\n        this.selected = result;\n        this.$nextTick(function () {\n          _this8.resetInputHeight();\n        });\n      },\n      handleFocus: function handleFocus(event) {\n        if (!this.softFocus) {\n          if (this.automaticDropdown || this.filterable) {\n            this.visible = true;\n            if (this.filterable) {\n              this.menuVisibleOnFocus = true;\n            }\n          }\n          this.$emit('focus', event);\n        } else {\n          this.softFocus = false;\n        }\n      },\n      blur: function blur() {\n        this.visible = false;\n        this.$refs.reference.blur();\n      },\n      handleBlur: function handleBlur(event) {\n        var _this9 = this;\n        setTimeout(function () {\n          if (_this9.isSilentBlur) {\n            _this9.isSilentBlur = false;\n          } else {\n            _this9.$emit('blur', event);\n          }\n        }, 50);\n        this.softFocus = false;\n      },\n      handleClearClick: function handleClearClick(event) {\n        this.deleteSelected(event);\n      },\n      doDestroy: function doDestroy() {\n        this.$refs.popper && this.$refs.popper.doDestroy();\n      },\n      handleClose: function handleClose() {\n        this.visible = false;\n      },\n      toggleLastOptionHitState: function toggleLastOptionHitState(hit) {\n        if (!Array.isArray(this.selected)) return;\n        var option = this.selected[this.selected.length - 1];\n        if (!option) return;\n        if (hit === true || hit === false) {\n          option.hitState = hit;\n          return hit;\n        }\n        option.hitState = !option.hitState;\n        return option.hitState;\n      },\n      deletePrevTag: function deletePrevTag(e) {\n        if (e.target.value.length <= 0 && !this.toggleLastOptionHitState()) {\n          var value = this.value.slice();\n          value.pop();\n          this.$emit('input', value);\n          this.emitChange(value);\n        }\n      },\n      managePlaceholder: function managePlaceholder() {\n        if (this.currentPlaceholder !== '') {\n          this.currentPlaceholder = this.$refs.input.value ? '' : this.cachedPlaceHolder;\n        }\n      },\n      resetInputState: function resetInputState(e) {\n        if (e.keyCode !== 8) this.toggleLastOptionHitState(false);\n        this.inputLength = this.$refs.input.value.length * 15 + 20;\n        this.resetInputHeight();\n      },\n      resetInputHeight: function resetInputHeight() {\n        var _this10 = this;\n        if (this.collapseTags && !this.filterable) return;\n        this.$nextTick(function () {\n          if (!_this10.$refs.reference) return;\n          var inputChildNodes = _this10.$refs.reference.$el.childNodes;\n          var input = [].filter.call(inputChildNodes, function (item) {\n            return item.tagName === 'INPUT';\n          })[0];\n          var tags = _this10.$refs.tags;\n          var sizeInMap = _this10.initialInputHeight || 40;\n          input.style.height = _this10.selected.length === 0 ? sizeInMap + 'px' : Math.max(tags ? tags.clientHeight + (tags.clientHeight > sizeInMap ? 6 : 0) : 0, sizeInMap) + 'px';\n          if (_this10.visible && _this10.emptyText !== false) {\n            _this10.broadcast('ElSelectDropdown', 'updatePopper');\n          }\n        });\n      },\n      resetHoverIndex: function resetHoverIndex() {\n        var _this11 = this;\n        setTimeout(function () {\n          if (!_this11.multiple) {\n            _this11.hoverIndex = _this11.options.indexOf(_this11.selected);\n          } else {\n            if (_this11.selected.length > 0) {\n              _this11.hoverIndex = Math.min.apply(null, _this11.selected.map(function (item) {\n                return _this11.options.indexOf(item);\n              }));\n            } else {\n              _this11.hoverIndex = -1;\n            }\n          }\n        }, 300);\n      },\n      handleOptionSelect: function handleOptionSelect(option, byClick) {\n        var _this12 = this;\n        if (this.multiple) {\n          var value = (this.value || []).slice();\n          var optionIndex = this.getValueIndex(value, option.value);\n          if (optionIndex > -1) {\n            value.splice(optionIndex, 1);\n          } else if (this.multipleLimit <= 0 || value.length < this.multipleLimit) {\n            value.push(option.value);\n          }\n          this.$emit('input', value);\n          this.emitChange(value);\n          if (option.created) {\n            this.query = '';\n            this.handleQueryChange('');\n            this.inputLength = 20;\n          }\n          if (this.filterable) this.$refs.input.focus();\n        } else {\n          this.$emit('input', option.value);\n          this.emitChange(option.value);\n          this.visible = false;\n        }\n        this.isSilentBlur = byClick;\n        this.setSoftFocus();\n        if (this.visible) return;\n        this.$nextTick(function () {\n          _this12.scrollToOption(option);\n        });\n      },\n      setSoftFocus: function setSoftFocus() {\n        this.softFocus = true;\n        var input = this.$refs.input || this.$refs.reference;\n        if (input) {\n          input.focus();\n        }\n      },\n      getValueIndex: function getValueIndex() {\n        var arr = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n        var value = arguments[1];\n        var isObject = Object.prototype.toString.call(value).toLowerCase() === '[object object]';\n        if (!isObject) {\n          return arr.indexOf(value);\n        } else {\n          var valueKey = this.valueKey;\n          var index = -1;\n          arr.some(function (item, i) {\n            if (Object(util_[\"getValueByPath\"])(item, valueKey) === Object(util_[\"getValueByPath\"])(value, valueKey)) {\n              index = i;\n              return true;\n            }\n            return false;\n          });\n          return index;\n        }\n      },\n      toggleMenu: function toggleMenu() {\n        if (!this.selectDisabled) {\n          if (this.menuVisibleOnFocus) {\n            this.menuVisibleOnFocus = false;\n          } else {\n            this.visible = !this.visible;\n          }\n          if (this.visible) {\n            (this.$refs.input || this.$refs.reference).focus();\n          }\n        }\n      },\n      selectOption: function selectOption() {\n        if (!this.visible) {\n          this.toggleMenu();\n        } else {\n          if (this.options[this.hoverIndex]) {\n            this.handleOptionSelect(this.options[this.hoverIndex]);\n          }\n        }\n      },\n      deleteSelected: function deleteSelected(event) {\n        event.stopPropagation();\n        var value = this.multiple ? [] : '';\n        this.$emit('input', value);\n        this.emitChange(value);\n        this.visible = false;\n        this.$emit('clear');\n      },\n      deleteTag: function deleteTag(event, tag) {\n        var index = this.selected.indexOf(tag);\n        if (index > -1 && !this.selectDisabled) {\n          var value = this.value.slice();\n          value.splice(index, 1);\n          this.$emit('input', value);\n          this.emitChange(value);\n          this.$emit('remove-tag', tag.value);\n        }\n        event.stopPropagation();\n      },\n      onInputChange: function onInputChange() {\n        if (this.filterable && this.query !== this.selectedLabel) {\n          this.query = this.selectedLabel;\n          this.handleQueryChange(this.query);\n        }\n      },\n      onOptionDestroy: function onOptionDestroy(index) {\n        if (index > -1) {\n          this.optionsCount--;\n          this.filteredOptionsCount--;\n          this.options.splice(index, 1);\n        }\n      },\n      resetInputWidth: function resetInputWidth() {\n        this.inputWidth = this.$refs.reference.$el.getBoundingClientRect().width;\n      },\n      handleResize: function handleResize() {\n        this.resetInputWidth();\n        if (this.multiple) this.resetInputHeight();\n      },\n      checkDefaultFirstOption: function checkDefaultFirstOption() {\n        this.hoverIndex = -1;\n        // highlight the created option\n        var hasCreated = false;\n        for (var i = this.options.length - 1; i >= 0; i--) {\n          if (this.options[i].created) {\n            hasCreated = true;\n            this.hoverIndex = i;\n            break;\n          }\n        }\n        if (hasCreated) return;\n        for (var _i = 0; _i !== this.options.length; ++_i) {\n          var option = this.options[_i];\n          if (this.query) {\n            // highlight first options that passes the filter\n            if (!option.disabled && !option.groupDisabled && option.visible) {\n              this.hoverIndex = _i;\n              break;\n            }\n          } else {\n            // highlight currently selected option\n            if (option.itemSelected) {\n              this.hoverIndex = _i;\n              break;\n            }\n          }\n        }\n      },\n      getValueKey: function getValueKey(item) {\n        if (Object.prototype.toString.call(item.value).toLowerCase() !== '[object object]') {\n          return item.value;\n        } else {\n          return Object(util_[\"getValueByPath\"])(item.value, this.valueKey);\n        }\n      }\n    },\n    created: function created() {\n      var _this13 = this;\n      this.cachedPlaceHolder = this.currentPlaceholder = this.placeholder;\n      if (this.multiple && !Array.isArray(this.value)) {\n        this.$emit('input', []);\n      }\n      if (!this.multiple && Array.isArray(this.value)) {\n        this.$emit('input', '');\n      }\n      this.debouncedOnInputChange = debounce_default()(this.debounce, function () {\n        _this13.onInputChange();\n      });\n      this.debouncedQueryChange = debounce_default()(this.debounce, function (e) {\n        _this13.handleQueryChange(e.target.value);\n      });\n      this.$on('handleOptionClick', this.handleOptionSelect);\n      this.$on('setSelected', this.setSelected);\n    },\n    mounted: function mounted() {\n      var _this14 = this;\n      if (this.multiple && Array.isArray(this.value) && this.value.length > 0) {\n        this.currentPlaceholder = '';\n      }\n      Object(resize_event_[\"addResizeListener\"])(this.$el, this.handleResize);\n      var reference = this.$refs.reference;\n      if (reference && reference.$el) {\n        var sizeMap = {\n          medium: 36,\n          small: 32,\n          mini: 28\n        };\n        var input = reference.$el.querySelector('input');\n        this.initialInputHeight = input.getBoundingClientRect().height || sizeMap[this.selectSize];\n      }\n      if (this.remote && this.multiple) {\n        this.resetInputHeight();\n      }\n      this.$nextTick(function () {\n        if (reference && reference.$el) {\n          _this14.inputWidth = reference.$el.getBoundingClientRect().width;\n        }\n      });\n      this.setSelected();\n    },\n    beforeDestroy: function beforeDestroy() {\n      if (this.$el && this.handleResize) Object(resize_event_[\"removeResizeListener\"])(this.$el, this.handleResize);\n    }\n  };\n  // CONCATENATED MODULE: ./packages/select/src/select.vue?vue&type=script&lang=js&\n  /* harmony default export */\n  var src_selectvue_type_script_lang_js_ = selectvue_type_script_lang_js_;\n  // CONCATENATED MODULE: ./packages/select/src/select.vue\n\n  /* normalize component */\n\n  var select_component = Object(componentNormalizer[\"a\" /* default */])(src_selectvue_type_script_lang_js_, render, staticRenderFns, false, null, null, null);\n\n  /* hot reload */\n  if (false) {\n    var select_api;\n  }\n  select_component.options.__file = \"packages/select/src/select.vue\";\n  /* harmony default export */\n  var src_select = select_component.exports;\n  // CONCATENATED MODULE: ./packages/select/index.js\n\n  /* istanbul ignore next */\n  src_select.install = function (Vue) {\n    Vue.component(src_select.name, src_select);\n  };\n\n  /* harmony default export */\n  var packages_select = __webpack_exports__[\"default\"] = src_select;\n\n  /***/\n}\n/******/)]);", null]}