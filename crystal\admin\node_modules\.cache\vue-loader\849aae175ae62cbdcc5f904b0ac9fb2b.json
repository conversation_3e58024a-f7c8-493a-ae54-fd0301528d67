{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\list\\level.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\list\\level.vue", "mtime": 1753666157941}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { userLevelUpdateApi} from '@/api/user'\nimport {Debounce} from '@/utils/validate'\nexport default {\n  props:{\n    levelInfo:{\n      type:Object,\n      default:{},\n    },\n    levelList:{\n      type:Array,\n      default:[]\n    }\n  },\n  data() {\n    return {\n      grade:'',\n      levelStatus:false,\n      ruleForm: {\n        isSub: false,\n        levelId:\"\",\n        uid:this.levelInfo.uid\n      },\n    };\n  },\n  created(){\n    this.ruleForm.levelId = this.levelInfo.level?Number(this.levelInfo.level):''\n  },\n  watch: {\n    levelInfo(val){\n      this.ruleForm.uid = val.uid || 0;\n      this.ruleForm.levelId = this.levelInfo.level?Number(this.levelInfo.level):val.levelId;\n    },\n  },\n  methods: {\n    submitForm:Debounce(function(formName) {\n      this.$refs[formName].validate((valid) => {\n        if (valid) {\n          userLevelUpdateApi(this.ruleForm).then(res=>{\n            this.$message.success('编辑成功');\n            this.$parent.$parent.getList();\n            this.$parent.$parent.levelVisible = false;\n            this.$refs[formName].resetFields()\n            this.grade = '';\n          })\n        } else {\n          return false;\n        }\n      });\n    }),\n    currentSel(){\n      this.levelList.forEach(item=>{\n        if(item.id == this.ruleForm.levelId){\n          this.grade = item.grade;\n        }\n      })\n    },\n    resetForm(formName) {\n      this.$nextTick(() => {\n        this.$refs[formName].resetFields();\n        this.grade = '';\n      })\n      this.$parent.$parent.levelVisible = false\n    },\n  },\n};\n", null]}