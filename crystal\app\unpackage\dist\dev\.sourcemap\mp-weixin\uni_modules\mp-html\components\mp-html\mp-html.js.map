{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/uni_modules/mp-html/components/mp-html/mp-html.vue?2020", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/uni_modules/mp-html/components/mp-html/mp-html.vue?3bc3", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/uni_modules/mp-html/components/mp-html/mp-html.vue?de89", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/uni_modules/mp-html/components/mp-html/mp-html.vue?3fda", "uni-app:///uni_modules/mp-html/components/mp-html/mp-html.vue", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/uni_modules/mp-html/components/mp-html/mp-html.vue?8094", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/uni_modules/mp-html/components/mp-html/mp-html.vue?3290"], "names": ["name", "data", "nodes", "props", "containerStyle", "type", "default", "content", "copyLink", "domain", "errorImg", "lazyLoad", "loadingImg", "pauseVideo", "previewImg", "scrollTable", "selectable", "setTitle", "showImgMenu", "tagStyle", "useAnchor", "components", "node", "watch", "created", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "methods", "in", "page", "selector", "scrollTop", "navigateTo", "reject", "offset", "deep", "select", "uni", "duration", "resolve", "getText", "text", "traversal", "getRect", "pauseMedia", "setPlaybackRate", "<PERSON><PERSON><PERSON><PERSON>", "height", "setTimeout", "_hook"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACsM;AACtM,gBAAgB,2LAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAmxB,CAAgB,urBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC0CvyB;AAAA;EAAA;IAAA;EAAA;AAAA;AACA;AAAA,eAIA;EACAA;EACAC;IACA;MACAC;IAIA;EACA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;IACAC;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;IACAO;MACAR;MACAC;IACA;IACAQ;MACAT;MACAC;IACA;IACAS;IACAC;IACAC;MACAZ;MACAC;IACA;IACAY;MACAb;MACAC;IACA;IACAa;IACAC;EACA;EAKAC;IACAC;EACA;EAEAC;IACAhB;MACA;IACA;EACA;EACAiB;IACA;IACA;MACA;IACA;EACA;EACAC;IACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;AACA;AACA;AACA;AACA;AACA;IACAC;MAEA;QACA;UACAC;UACAC;UACAC;QACA;MACA;IAEA;IAEA;AACA;AACA;AACA;AACA;AACA;IACAC;MAAA;MACA;QACA;UACAC;UACA;QACA;QACAC;QAiBA;QAEAC;QAEA,yCAEAP,uCAEAQ;QACA;UACAN,mDACAM;QACA;UACA;UACAN;QACA;;QACAA;UACA;YACAG;YACA;UACA;UACA;UACA;YACA;YACA;UACA;YACA;YACAI;cACAN;cACAO;YACA;UACA;UACAC;QACA;MAEA;IACA;IAEA;AACA;AACA;AACA;IACAC;MACA;MACA;QACA;UACA;UACA;YACAC;UACA;YACAA;UACA;YACA;YACA;YACA;cACAA;YACA;YACA;YACA;cACAC;YACA;YACA;cACAD;YACA;cACAA;YACA;UACA;QACA;MACA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAE;MAAA;MACA;QACAN,0BAEAT,WAEAQ;UAAA;QAAA;MACA;IACA;IAEA;AACA;AACA;IACAQ;MACA;QACA;MACA;IAYA;IAEA;AACA;AACA;AACA;IACAC;MACA;MACA;QACA;MACA;IAYA;IAEA;AACA;AACA;AACA;AACA;IACAC;MAAA;MACA;QACA;MACA;MACA;MAMA;MAGA;MACA;QACA;QACA;MACA;MAEA;QACA;QACA;QACA;UACA;UACA;UACA;YACA;UACA;YACAC;YACAC;cACA;YACA;UACA;QACA;QACA;MACA;QACA;QACA;UACA;YACA;UACA;YACA;UACA;QACA;MACA;IAEA;IAEA;AACA;AACA;IACAC;MACA;QACA;UACA;QACA;MACA;IACA;EA6GA;AACA;AAAA,2B;;;;;;;;;;;;;AC/dA;AAAA;AAAA;AAAA;AAA4mC,CAAgB,w8BAAG,EAAC,C;;;;;;;;;;;ACAhoC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/mp-html/components/mp-html/mp-html.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./mp-html.vue?vue&type=template&id=0cfd6ca1&\"\nvar renderjs\nimport script from \"./mp-html.vue?vue&type=script&lang=js&\"\nexport * from \"./mp-html.vue?vue&type=script&lang=js&\"\nimport style0 from \"./mp-html.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/mp-html/components/mp-html/mp-html.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mp-html.vue?vue&type=template&id=0cfd6ca1&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mp-html.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mp-html.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view id=\"_root\" :class=\"(selectable?'_select ':'')+'_root'\" :style=\"containerStyle\">\r\n    <slot v-if=\"!nodes[0]\" />\r\n    <!-- #ifndef APP-PLUS-NVUE -->\r\n    <node v-else :childs=\"nodes\" :opts=\"[lazyLoad,loadingImg,errorImg,showImgMenu,selectable]\" name=\"span\" />\r\n    <!-- #endif -->\r\n    <!-- #ifdef APP-PLUS-NVUE -->\r\n    <web-view ref=\"web\" src=\"/uni_modules/mp-html/static/app-plus/mp-html/local.html\" :style=\"'margin-top:-2px;height:' + height + 'px'\" @onPostMessage=\"_onMessage\" />\r\n    <!-- #endif -->\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n/**\r\n * mp-html v2.5.0\r\n * @description 富文本组件\r\n * @tutorial https://github.com/jin-yufeng/mp-html\r\n * @property {String} container-style 容器的样式\r\n * @property {String} content 用于渲染的 html 字符串\r\n * @property {Boolean} copy-link 是否允许外部链接被点击时自动复制\r\n * @property {String} domain 主域名，用于拼接链接\r\n * @property {String} error-img 图片出错时的占位图链接\r\n * @property {Boolean} lazy-load 是否开启图片懒加载\r\n * @property {string} loading-img 图片加载过程中的占位图链接\r\n * @property {Boolean} pause-video 是否在播放一个视频时自动暂停其他视频\r\n * @property {Boolean} preview-img 是否允许图片被点击时自动预览\r\n * @property {Boolean} scroll-table 是否给每个表格添加一个滚动层使其能单独横向滚动\r\n * @property {Boolean | String} selectable 是否开启长按复制\r\n * @property {Boolean} set-title 是否将 title 标签的内容设置到页面标题\r\n * @property {Boolean} show-img-menu 是否允许图片被长按时显示菜单\r\n * @property {Object} tag-style 标签的默认样式\r\n * @property {Boolean | Number} use-anchor 是否使用锚点链接\r\n * @event {Function} load dom 结构加载完毕时触发\r\n * @event {Function} ready 所有图片加载完毕时触发\r\n * @event {Function} imgtap 图片被点击时触发\r\n * @event {Function} linktap 链接被点击时触发\r\n * @event {Function} play 音视频播放时触发\r\n * @event {Function} error 媒体加载出错时触发\r\n */\r\n// #ifndef APP-PLUS-NVUE\r\nimport node from './node/node'\r\n// #endif\r\nimport Parser from './parser'\r\nconst plugins=[]\r\n// #ifdef APP-PLUS-NVUE\r\nconst dom = weex.requireModule('dom')\r\n// #endif\r\nexport default {\r\n  name: 'mp-html',\r\n  data () {\r\n    return {\r\n      nodes: [],\r\n      // #ifdef APP-PLUS-NVUE\r\n      height: 3\r\n      // #endif\r\n    }\r\n  },\r\n  props: {\r\n    containerStyle: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    content: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    copyLink: {\r\n      type: [Boolean, String],\r\n      default: true\r\n    },\r\n    domain: String,\r\n    errorImg: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    lazyLoad: {\r\n      type: [Boolean, String],\r\n      default: false\r\n    },\r\n    loadingImg: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    pauseVideo: {\r\n      type: [Boolean, String],\r\n      default: true\r\n    },\r\n    previewImg: {\r\n      type: [Boolean, String],\r\n      default: true\r\n    },\r\n    scrollTable: [Boolean, String],\r\n    selectable: [Boolean, String],\r\n    setTitle: {\r\n      type: [Boolean, String],\r\n      default: true\r\n    },\r\n    showImgMenu: {\r\n      type: [Boolean, String],\r\n      default: true\r\n    },\r\n    tagStyle: Object,\r\n    useAnchor: [Boolean, Number]\r\n  },\r\n  // #ifdef VUE3\r\n  emits: ['load', 'ready', 'imgtap', 'linktap', 'play', 'error'],\r\n  // #endif\r\n  // #ifndef APP-PLUS-NVUE\r\n  components: {\r\n    node\r\n  },\r\n  // #endif\r\n  watch: {\r\n    content (content) {\r\n      this.setContent(content)\r\n    }\r\n  },\r\n  created () {\r\n    this.plugins = []\r\n    for (let i = plugins.length; i--;) {\r\n      this.plugins.push(new plugins[i](this))\r\n    }\r\n  },\r\n  mounted () {\r\n    if (this.content && !this.nodes.length) {\r\n      this.setContent(this.content)\r\n    }\r\n  },\r\n  beforeDestroy () {\r\n    this._hook('onDetached')\r\n  },\r\n  methods: {\r\n    /**\r\n     * @description 将锚点跳转的范围限定在一个 scroll-view 内\r\n     * @param {Object} page scroll-view 所在页面的示例\r\n     * @param {String} selector scroll-view 的选择器\r\n     * @param {String} scrollTop scroll-view scroll-top 属性绑定的变量名\r\n     */\r\n    in (page, selector, scrollTop) {\r\n      // #ifndef APP-PLUS-NVUE\r\n      if (page && selector && scrollTop) {\r\n        this._in = {\r\n          page,\r\n          selector,\r\n          scrollTop\r\n        }\r\n      }\r\n      // #endif\r\n    },\r\n\r\n    /**\r\n     * @description 锚点跳转\r\n     * @param {String} id 要跳转的锚点 id\r\n     * @param {Number} offset 跳转位置的偏移量\r\n     * @returns {Promise}\r\n     */\r\n    navigateTo (id, offset) {\r\n      return new Promise((resolve, reject) => {\r\n        if (!this.useAnchor) {\r\n          reject(Error('Anchor is disabled'))\r\n          return\r\n        }\r\n        offset = offset || parseInt(this.useAnchor) || 0\r\n        // #ifdef APP-PLUS-NVUE\r\n        if (!id) {\r\n          dom.scrollToElement(this.$refs.web, {\r\n            offset\r\n          })\r\n          resolve()\r\n        } else {\r\n          this._navigateTo = {\r\n            resolve,\r\n            reject,\r\n            offset\r\n          }\r\n          this.$refs.web.evalJs('uni.postMessage({data:{action:\"getOffset\",offset:(document.getElementById(' + id + ')||{}).offsetTop}})')\r\n        }\r\n        // #endif\r\n        // #ifndef APP-PLUS-NVUE\r\n        let deep = ' '\r\n        // #ifdef MP-WEIXIN || MP-QQ || MP-TOUTIAO\r\n        deep = '>>>'\r\n        // #endif\r\n        const selector = uni.createSelectorQuery()\r\n          // #ifndef MP-ALIPAY\r\n          .in(this._in ? this._in.page : this)\r\n          // #endif\r\n          .select((this._in ? this._in.selector : '._root') + (id ? `${deep}#${id}` : '')).boundingClientRect()\r\n        if (this._in) {\r\n          selector.select(this._in.selector).scrollOffset()\r\n            .select(this._in.selector).boundingClientRect()\r\n        } else {\r\n          // 获取 scroll-view 的位置和滚动距离\r\n          selector.selectViewport().scrollOffset() // 获取窗口的滚动距离\r\n        }\r\n        selector.exec(res => {\r\n          if (!res[0]) {\r\n            reject(Error('Label not found'))\r\n            return\r\n          }\r\n          const scrollTop = res[1].scrollTop + res[0].top - (res[2] ? res[2].top : 0) + offset\r\n          if (this._in) {\r\n            // scroll-view 跳转\r\n            this._in.page[this._in.scrollTop] = scrollTop\r\n          } else {\r\n            // 页面跳转\r\n            uni.pageScrollTo({\r\n              scrollTop,\r\n              duration: 300\r\n            })\r\n          }\r\n          resolve()\r\n        })\r\n        // #endif\r\n      })\r\n    },\r\n\r\n    /**\r\n     * @description 获取文本内容\r\n     * @return {String}\r\n     */\r\n    getText (nodes) {\r\n      let text = '';\r\n      (function traversal (nodes) {\r\n        for (let i = 0; i < nodes.length; i++) {\r\n          const node = nodes[i]\r\n          if (node.type === 'text') {\r\n            text += node.text.replace(/&amp;/g, '&')\r\n          } else if (node.name === 'br') {\r\n            text += '\\n'\r\n          } else {\r\n            // 块级标签前后加换行\r\n            const isBlock = node.name === 'p' || node.name === 'div' || node.name === 'tr' || node.name === 'li' || (node.name[0] === 'h' && node.name[1] > '0' && node.name[1] < '7')\r\n            if (isBlock && text && text[text.length - 1] !== '\\n') {\r\n              text += '\\n'\r\n            }\r\n            // 递归获取子节点的文本\r\n            if (node.children) {\r\n              traversal(node.children)\r\n            }\r\n            if (isBlock && text[text.length - 1] !== '\\n') {\r\n              text += '\\n'\r\n            } else if (node.name === 'td' || node.name === 'th') {\r\n              text += '\\t'\r\n            }\r\n          }\r\n        }\r\n      })(nodes || this.nodes)\r\n      return text\r\n    },\r\n\r\n    /**\r\n     * @description 获取内容大小和位置\r\n     * @return {Promise}\r\n     */\r\n    getRect () {\r\n      return new Promise((resolve, reject) => {\r\n        uni.createSelectorQuery()\r\n          // #ifndef MP-ALIPAY\r\n          .in(this)\r\n          // #endif\r\n          .select('#_root').boundingClientRect().exec(res => res[0] ? resolve(res[0]) : reject(Error('Root label not found')))\r\n      })\r\n    },\r\n\r\n    /**\r\n     * @description 暂停播放媒体\r\n     */\r\n    pauseMedia () {\r\n      for (let i = (this._videos || []).length; i--;) {\r\n        this._videos[i].pause()\r\n      }\r\n      // #ifdef APP-PLUS\r\n      const command = 'for(var e=document.getElementsByTagName(\"video\"),i=e.length;i--;)e[i].pause()'\r\n      // #ifndef APP-PLUS-NVUE\r\n      let page = this.$parent\r\n      while (!page.$scope) page = page.$parent\r\n      page.$scope.$getAppWebview().evalJS(command)\r\n      // #endif\r\n      // #ifdef APP-PLUS-NVUE\r\n      this.$refs.web.evalJs(command)\r\n      // #endif\r\n      // #endif\r\n    },\r\n\r\n    /**\r\n     * @description 设置媒体播放速率\r\n     * @param {Number} rate 播放速率\r\n     */\r\n    setPlaybackRate (rate) {\r\n      this.playbackRate = rate\r\n      for (let i = (this._videos || []).length; i--;) {\r\n        this._videos[i].playbackRate(rate)\r\n      }\r\n      // #ifdef APP-PLUS\r\n      const command = 'for(var e=document.getElementsByTagName(\"video\"),i=e.length;i--;)e[i].playbackRate=' + rate\r\n      // #ifndef APP-PLUS-NVUE\r\n      let page = this.$parent\r\n      while (!page.$scope) page = page.$parent\r\n      page.$scope.$getAppWebview().evalJS(command)\r\n      // #endif\r\n      // #ifdef APP-PLUS-NVUE\r\n      this.$refs.web.evalJs(command)\r\n      // #endif\r\n      // #endif\r\n    },\r\n\r\n    /**\r\n     * @description 设置内容\r\n     * @param {String} content html 内容\r\n     * @param {Boolean} append 是否在尾部追加\r\n     */\r\n    setContent (content, append) {\r\n      if (!append || !this.imgList) {\r\n        this.imgList = []\r\n      }\r\n      const nodes = new Parser(this).parse(content)\r\n      // #ifdef APP-PLUS-NVUE\r\n      if (this._ready) {\r\n        this._set(nodes, append)\r\n      }\r\n      // #endif\r\n      this.$set(this, 'nodes', append ? (this.nodes || []).concat(nodes) : nodes)\r\n\r\n      // #ifndef APP-PLUS-NVUE\r\n      this._videos = []\r\n      this.$nextTick(() => {\r\n        this._hook('onLoad')\r\n        this.$emit('load')\r\n      })\r\n\r\n      if (this.lazyLoad || this.imgList._unloadimgs < this.imgList.length / 2) {\r\n        // 设置懒加载，每 350ms 获取高度，不变则认为加载完毕\r\n        let height = 0\r\n        const callback = rect => {\r\n          if (!rect || !rect.height) rect = {}\r\n          // 350ms 总高度无变化就触发 ready 事件\r\n          if (rect.height === height) {\r\n            this.$emit('ready', rect)\r\n          } else {\r\n            height = rect.height\r\n            setTimeout(() => {\r\n              this.getRect().then(callback).catch(callback)\r\n            }, 350)\r\n          }\r\n        }\r\n        this.getRect().then(callback).catch(callback)\r\n      } else {\r\n        // 未设置懒加载，等待所有图片加载完毕\r\n        if (!this.imgList._unloadimgs) {\r\n          this.getRect().then(rect => {\r\n            this.$emit('ready', rect)\r\n          }).catch(() => {\r\n            this.$emit('ready', {})\r\n          })\r\n        }\r\n      }\r\n      // #endif\r\n    },\r\n\r\n    /**\r\n     * @description 调用插件钩子函数\r\n     */\r\n    _hook (name) {\r\n      for (let i = plugins.length; i--;) {\r\n        if (this.plugins[i][name]) {\r\n          this.plugins[i][name]()\r\n        }\r\n      }\r\n    },\r\n\r\n    // #ifdef APP-PLUS-NVUE\r\n    /**\r\n     * @description 设置内容\r\n     */\r\n    _set (nodes, append) {\r\n      this.$refs.web.evalJs('setContent(' + JSON.stringify(nodes).replace(/%22/g, '') + ',' + JSON.stringify([this.containerStyle.replace(/(?:margin|padding)[^;]+/g, ''), this.errorImg, this.loadingImg, this.pauseVideo, this.scrollTable, this.selectable]) + ',' + append + ')')\r\n    },\r\n\r\n    /**\r\n     * @description 接收到 web-view 消息\r\n     */\r\n    _onMessage (e) {\r\n      const message = e.detail.data[0]\r\n      switch (message.action) {\r\n        // web-view 初始化完毕\r\n        case 'onJSBridgeReady':\r\n          this._ready = true\r\n          if (this.nodes) {\r\n            this._set(this.nodes)\r\n          }\r\n          break\r\n        // 内容 dom 加载完毕\r\n        case 'onLoad':\r\n          this.height = message.height\r\n          this._hook('onLoad')\r\n          this.$emit('load')\r\n          break\r\n        // 所有图片加载完毕\r\n        case 'onReady':\r\n          this.getRect().then(res => {\r\n            this.$emit('ready', res)\r\n          }).catch(() => {\r\n            this.$emit('ready', {})\r\n          })\r\n          break\r\n        // 总高度发生变化\r\n        case 'onHeightChange':\r\n          this.height = message.height\r\n          break\r\n        // 图片点击\r\n        case 'onImgTap':\r\n          this.$emit('imgtap', message.attrs)\r\n          if (this.previewImg) {\r\n            uni.previewImage({\r\n              current: parseInt(message.attrs.i),\r\n              urls: this.imgList\r\n            })\r\n          }\r\n          break\r\n        // 链接点击\r\n        case 'onLinkTap': {\r\n          const href = message.attrs.href\r\n          this.$emit('linktap', message.attrs)\r\n          if (href) {\r\n            // 锚点跳转\r\n            if (href[0] === '#') {\r\n              if (this.useAnchor) {\r\n                dom.scrollToElement(this.$refs.web, {\r\n                  offset: message.offset\r\n                })\r\n              }\r\n            } else if (href.includes('://')) {\r\n              // 打开外链\r\n              if (this.copyLink) {\r\n                plus.runtime.openWeb(href)\r\n              }\r\n            } else {\r\n              uni.navigateTo({\r\n                url: href,\r\n                fail () {\r\n                  uni.switchTab({\r\n                    url: href\r\n                  })\r\n                }\r\n              })\r\n            }\r\n          }\r\n          break\r\n        }\r\n        case 'onPlay':\r\n          this.$emit('play')\r\n          break\r\n        // 获取到锚点的偏移量\r\n        case 'getOffset':\r\n          if (typeof message.offset === 'number') {\r\n            dom.scrollToElement(this.$refs.web, {\r\n              offset: message.offset + this._navigateTo.offset\r\n            })\r\n            this._navigateTo.resolve()\r\n          } else {\r\n            this._navigateTo.reject(Error('Label not found'))\r\n          }\r\n          break\r\n        // 点击\r\n        case 'onClick':\r\n          this.$emit('tap')\r\n          this.$emit('click')\r\n          break\r\n        // 出错\r\n        case 'onError':\r\n          this.$emit('error', {\r\n            source: message.source,\r\n            attrs: message.attrs\r\n          })\r\n      }\r\n    }\r\n    // #endif\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n/* #ifndef APP-PLUS-NVUE */\r\n/* 根节点样式 */\r\n._root {\r\n  padding: 1px 0;\r\n  overflow-x: auto;\r\n  overflow-y: hidden;\r\n  -webkit-overflow-scrolling: touch;\r\n}\r\n\r\n/* 长按复制 */\r\n._select {\r\n  user-select: text;\r\n}\r\n/* #endif */\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mp-html.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mp-html.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363903573\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}