{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\braceletsIndex.vue?vue&type=template&id=6608ecaf&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\braceletsIndex.vue", "mtime": 1753666157921}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\n<div class=\"divBox relative\">\n  <el-card class=\"box-card\">\n    <div slot=\"header\" class=\"clearfix\">\n      <el-tabs v-model=\"tableFrom.type\" @tab-click=\"seachList\" v-if=\"checkPermi(['admin:product:tabs:headers'])\">\n        <el-tab-pane :label=\"item.name + '(' + item.count + ')'\" :name=\"item.type.toString()\"\n          v-for=\"(item, index) in headeNum\" :key=\"index\" />\n      </el-tabs>\n      <div class=\"container mt-1\">\n        <el-form inline size=\"small\">\n          <el-form-item label=\"商品分类：\">\n            <el-cascader v-model=\"tableFrom.cateId\" :options=\"merCateList\" :props=\"props\" clearable\n              class=\"selWidth mr20\" @change=\"seachList\" size=\"small\" />\n          </el-form-item>\n          <el-form-item label=\"商品搜索：\">\n            <el-input v-model=\"tableFrom.keywords\" placeholder=\"请输入商品名称，关键字，商品ID\" class=\"selWidth\" size=\"small\"\n              clearable>\n              <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"seachList\" size=\"small\"\n                v-hasPermi=\"['admin:product:list']\" />\n            </el-input>\n          </el-form-item>\n        </el-form>\n      </div>\n      <router-link :to=\"{ path: '/store/list/creatBraceletsProduct' }\">\n        <el-button size=\"small\" type=\"primary\" class=\"mr10\" v-hasPermi=\"['admin:product:save']\">添加商品</el-button>\n      </router-link>\n      <!-- <el-button size=\"small\" type=\"success\" class=\"mr10\" @click=\"onCopy\"\n        v-hasPermi=\"['admin:product:save']\">商品采集</el-button> -->\n      <!-- <el-button size=\"small\" icon=\"el-icon-upload2\" @click=\"exports\"\n        v-hasPermi=\"['admin:export:excel:product']\">导出</el-button> -->\n    </div>\n    <el-table v-loading=\"listLoading\" :data=\"tableData.data\" style=\"width: 100%\" size=\"mini\"\n      :highlight-current-row=\"true\" :header-cell-style=\"{ fontWeight: 'bold' }\">\n      <el-table-column type=\"expand\">\n        <template slot-scope=\"props\">\n          <el-form label-position=\"left\" inline class=\"demo-table-expand\">\n            <el-form-item label=\"商品分类：\">\n              <span v-for=\"(item, index) in props.row.cateValues.split(',')\" :key=\"index\" class=\"mr10\">{{ item\n                }}</span>\n            </el-form-item>\n            <el-form-item label=\"市场价：\">\n              <span>{{ props.row.otPrice }}</span>\n            </el-form-item>\n            <el-form-item label=\"成本价：\">\n              <span>{{ props.row.cost }}</span>\n            </el-form-item>\n            <el-form-item label=\"收藏：\">\n              <span>{{ props.row.collectCount }}</span>\n            </el-form-item>\n            <el-form-item label=\"虚拟销量：\">\n              <span>{{ props.row.ficti }}</span>\n            </el-form-item>\n          </el-form>\n        </template>\n      </el-table-column>\n      <el-table-column prop=\"id\" label=\"ID\" min-width=\"50\" />\n      <el-table-column label=\"商品图\" min-width=\"80\">\n        <template slot-scope=\"scope\">\n          <div class=\"demo-image__preview\">\n            <el-image style=\"width: 36px; height: 36px\" :src=\"scope.row.image\"\n              :preview-src-list=\"[scope.row.image]\" />\n          </div>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"商品名称\" prop=\"storeName\" min-width=\"300\" :show-overflow-tooltip=\"true\">\n      </el-table-column>\n      <el-table-column prop=\"price\" label=\"商品售价\" min-width=\"90\" align=\"center\" />\n      <el-table-column prop=\"sales\" label=\"销量\" min-width=\"90\" align=\"center\" />\n      <el-table-column prop=\"stock\" label=\"库存\" min-width=\"90\" align=\"center\" />\n      <el-table-column prop=\"width\" label=\"宽度\" min-width=\"90\" align=\"center\" >\n        <div slot-scope=\"scope\">{{ scope.row.width }}mm</div>\n      </el-table-column>\n      <el-table-column prop=\"height\" label=\"高度\" min-width=\"90\" align=\"center\" >\n        <div slot-scope=\"scope\">{{ scope.row.height }}mm</div></el-table-column>\n      <el-table-column prop=\"sort\" label=\"排序\" min-width=\"70\" align=\"center\" />\n\n      <el-table-column label=\"添加时间\" min-width=\"120\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.addTime | formatDate }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"状态\" min-width=\"80\" fixed=\"right\">\n        <template slot-scope=\"scope\" v-if=\"checkPermi(['admin:product:up', 'admin:product:down'])\">\n          <el-switch :disabled=\"Number(tableFrom.type) > 2\" v-model=\"scope.row.isShow\" :active-value=\"true\"\n            :inactive-value=\"false\" active-text=\"上架\" inactive-text=\"下架\" @change=\"onchangeIsShow(scope.row)\" />\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" min-width=\"150\" fixed=\"right\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <router-link :to=\"{ path: '/store/list/creatBraceletsProduct/' + scope.row.id + '/1' }\">\n            <el-button type=\"text\" size=\"small\" class=\"mr10\" v-hasPermi=\"['admin:product:info']\">详情</el-button>\n          </router-link>\n          <router-link :to=\"{ path: '/store/list/creatBraceletsProduct/' + scope.row.id }\">\n            <el-button type=\"text\" size=\"small\" class=\"mr10\" v-if=\"tableFrom.type !== '5' \"\n              v-hasPermi=\"['admin:product:update']\">编辑</el-button>\n          </router-link>\n          <el-button v-if=\"tableFrom.type === '5'\" type=\"text\" size=\"small\"\n            @click=\"handleRestore(scope.row.id, scope.$index)\" v-hasPermi=\"['admin:product:restore']\">恢复商品</el-button>\n          <el-button type=\"text\" size=\"small\" @click=\"handleDelete(scope.row.id, tableFrom.type)\"\n            v-hasPermi=\"['admin:product:delete']\">{{ tableFrom.type === \"5\" ? \"删除\" : \"加入回收站\" }}</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <div class=\"block\">\n      <el-pagination :page-sizes=\"[20, 40, 60, 80]\" :page-size=\"tableFrom.limit\" :current-page=\"tableFrom.page\"\n        layout=\"total, sizes, prev, pager, next, jumper\" :total=\"tableData.total\" @size-change=\"handleSizeChange\"\n        @current-change=\"pageChange\" />\n    </div>\n  </el-card>\n  <!-- <el-dialog title=\"复制淘宝、天猫、京东、苏宁\" :visible.sync=\"dialogVisible\" :close-on-click-modal=\"false\" width=\"1200px\"\n    class=\"taoBaoModal\" :before-close=\"handleClose\">\n    <tao-bao v-if=\"dialogVisible\" @handleCloseMod=\"handleCloseMod\"></tao-bao>\n  </el-dialog> -->\n</div>\n", null]}