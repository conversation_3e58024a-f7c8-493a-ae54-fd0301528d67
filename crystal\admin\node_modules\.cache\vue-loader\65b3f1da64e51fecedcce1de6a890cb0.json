{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\FormGenerator\\index\\ResourceDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\FormGenerator\\index\\ResourceDialog.vue", "mtime": 1753666157770}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nexport default {\r\n  components: {},\r\n  inheritAttrs: false,\r\n  props: ['originResource'],\r\n  data() {\r\n    return {\r\n      resources: null\r\n    }\r\n  },\r\n  computed: {},\r\n  watch: {},\r\n  created() {},\r\n  mounted() {},\r\n  methods: {\r\n    onOpen() {\r\n      this.resources = this.originResource.length ? JSON.parse(JSON.stringify(this.originResource)) : ['']\r\n    },\r\n    onClose() {\r\n    },\r\n    close() {\r\n      this.$emit('update:visible', false)\r\n    },\r\n    handelConfirm() {\r\n      const results = this.resources.filter(item => !!item) || []\r\n      this.$emit('save', results)\r\n      this.close()\r\n      if (results.length) {\r\n        this.resources = results\r\n      }\r\n    },\r\n    deleteOne(index) {\r\n      this.resources.splice(index, 1)\r\n    },\r\n    addOne(url) {\r\n      if (this.resources.indexOf(url) > -1) {\r\n        this.$message('资源已存在')\r\n      } else {\r\n        this.resources.push(url)\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n", null]}