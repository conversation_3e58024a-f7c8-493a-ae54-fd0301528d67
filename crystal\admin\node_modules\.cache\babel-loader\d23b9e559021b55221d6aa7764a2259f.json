{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\creatStore\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\creatStore\\index.vue", "mtime": 1753666157922}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _index = _interopRequireDefault(require(\"@/components/Tinymce/index\"));\nvar _store = require(\"@/api/store\");\nvar _marketing = require(\"@/api/marketing\");\nvar _logistics = require(\"@/api/logistics\");\nvar _systemGroup = require(\"@/api/systemGroup\");\nvar _ZBKJIutil = require(\"@/utils/ZBKJIutil\");\nvar _creatTemplates = _interopRequireDefault(require(\"@/views/systemSetting/logistics/shippingTemplates/creatTemplates\"));\nvar _index2 = _interopRequireDefault(require(\"../../appSetting/wxAccount/wxTemplate/index\"));\nvar _validate = require(\"@/utils/validate\");\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _regenerator() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */ var e, t, r = \"function\" == typeof Symbol ? Symbol : {}, n = r.iterator || \"@@iterator\", o = r.toStringTag || \"@@toStringTag\"; function i(r, n, o, i) { var c = n && n.prototype instanceof Generator ? n : Generator, u = Object.create(c.prototype); return _regeneratorDefine2(u, \"_invoke\", function (r, n, o) { var i, c, u, f = 0, p = o || [], y = !1, G = { p: 0, n: 0, v: e, a: d, f: d.bind(e, 4), d: function d(t, r) { return i = t, c = 0, u = e, G.n = r, a; } }; function d(r, n) { for (c = r, u = n, t = 0; !y && f && !o && t < p.length; t++) { var o, i = p[t], d = G.p, l = i[2]; r > 3 ? (o = l === n) && (u = i[(c = i[4]) ? 5 : (c = 3, 3)], i[4] = i[5] = e) : i[0] <= d && ((o = r < 2 && d < i[1]) ? (c = 0, G.v = n, G.n = i[1]) : d < l && (o = r < 3 || i[0] > n || n > l) && (i[4] = r, i[5] = n, G.n = l, c = 0)); } if (o || r > 1) return a; throw y = !0, n; } return function (o, p, l) { if (f > 1) throw TypeError(\"Generator is already running\"); for (y && 1 === p && d(p, l), c = p, u = l; (t = c < 2 ? e : u) || !y;) { i || (c ? c < 3 ? (c > 1 && (G.n = -1), d(c, u)) : G.n = u : G.v = u); try { if (f = 2, i) { if (c || (o = \"next\"), t = i[o]) { if (!(t = t.call(i, u))) throw TypeError(\"iterator result is not an object\"); if (!t.done) return t; u = t.value, c < 2 && (c = 0); } else 1 === c && (t = i.return) && t.call(i), c < 2 && (u = TypeError(\"The iterator does not provide a '\" + o + \"' method\"), c = 1); i = e; } else if ((t = (y = G.n < 0) ? u : r.call(n, G)) !== a) break; } catch (t) { i = e, c = 1, u = t; } finally { f = 1; } } return { value: t, done: y }; }; }(r, o, i), !0), u; } var a = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} t = Object.getPrototypeOf; var c = [][n] ? t(t([][n]())) : (_regeneratorDefine2(t = {}, n, function () { return this; }), t), u = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c); function f(e) { return Object.setPrototypeOf ? Object.setPrototypeOf(e, GeneratorFunctionPrototype) : (e.__proto__ = GeneratorFunctionPrototype, _regeneratorDefine2(e, o, \"GeneratorFunction\")), e.prototype = Object.create(u), e; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, _regeneratorDefine2(u, \"constructor\", GeneratorFunctionPrototype), _regeneratorDefine2(GeneratorFunctionPrototype, \"constructor\", GeneratorFunction), GeneratorFunction.displayName = \"GeneratorFunction\", _regeneratorDefine2(GeneratorFunctionPrototype, o, \"GeneratorFunction\"), _regeneratorDefine2(u), _regeneratorDefine2(u, o, \"Generator\"), _regeneratorDefine2(u, n, function () { return this; }), _regeneratorDefine2(u, \"toString\", function () { return \"[object Generator]\"; }), (_regenerator = function _regenerator() { return { w: i, m: f }; })(); }\nfunction _regeneratorDefine2(e, r, n, t) { var i = Object.defineProperty; try { i({}, \"\", {}); } catch (e) { i = 0; } _regeneratorDefine2 = function _regeneratorDefine(e, r, n, t) { function o(r, n) { _regeneratorDefine2(e, r, function (e) { return this._invoke(r, n, e); }); } r ? i ? i(e, r, { value: n, enumerable: !t, configurable: !t, writable: !t }) : e[r] = n : (o(\"next\", 0), o(\"throw\", 1), o(\"return\", 2)); }, _regeneratorDefine2(e, r, n, t); }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nfunction _createForOfIteratorHelper(r, e) { var t = \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); } //\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar defaultObj = {\n  image: '',\n  sliderImages: [],\n  videoLink: '',\n  sliderImage: '',\n  storeName: '',\n  storeInfo: '',\n  keyword: '',\n  cateIds: [],\n  // 商品分类id\n  cateId: null,\n  // 商品分类id传值\n  unitName: '',\n  sort: 0,\n  giveIntegral: 0,\n  ficti: 0,\n  isShow: false,\n  isBenefit: false,\n  isNew: false,\n  isGood: false,\n  isHot: false,\n  isBest: false,\n  tempId: '',\n  attrValue: [{\n    image: '',\n    price: 0,\n    cost: 0,\n    otPrice: 0,\n    stock: 0,\n    barCode: '',\n    weight: 0,\n    volume: 0\n  }],\n  attr: [],\n  selectRule: '',\n  isSub: false,\n  content: '',\n  specType: false,\n  id: 0,\n  couponIds: [],\n  coupons: [],\n  activity: ['默认', '秒杀', '砍价', '拼团']\n};\nvar objTitle = {\n  price: {\n    title: '售价'\n  },\n  cost: {\n    title: '成本价'\n  },\n  otPrice: {\n    title: '原价'\n  },\n  stock: {\n    title: '库存'\n  },\n  barCode: {\n    title: '商品编号'\n  },\n  weight: {\n    title: '重量（KG）'\n  },\n  volume: {\n    title: '体积(m³)'\n  }\n};\nvar _default = exports.default = {\n  name: 'ProductProductAdd',\n  components: {\n    Templates: _index2.default,\n    CreatTemplates: _creatTemplates.default,\n    Tinymce: _index.default\n  },\n  data: function data() {\n    return {\n      isDisabled: this.$route.params.isDisabled === '1' ? true : false,\n      activity: {\n        '默认': 'red',\n        '秒杀': 'blue',\n        '砍价': 'green',\n        '拼团': 'yellow'\n      },\n      props2: {\n        children: 'child',\n        label: 'name',\n        value: 'id',\n        multiple: true,\n        emitPath: false\n      },\n      checkboxGroup: [],\n      recommend: [],\n      tabs: [],\n      fullscreenLoading: false,\n      props: {\n        multiple: true\n      },\n      active: 0,\n      OneattrValue: [Object.assign({}, defaultObj.attrValue[0])],\n      // 单规格\n      ManyAttrValue: [Object.assign({}, defaultObj.attrValue[0])],\n      // 多规格\n      ruleList: [],\n      merCateList: [],\n      // 商户分类筛选\n      shippingList: [],\n      // 运费模板\n      formThead: Object.assign({}, objTitle),\n      formValidate: Object.assign({}, defaultObj),\n      formDynamics: {\n        ruleName: '',\n        ruleValue: []\n      },\n      tempData: {\n        page: 1,\n        limit: 9999\n      },\n      manyTabTit: {},\n      manyTabDate: {},\n      grid2: {\n        xl: 12,\n        lg: 12,\n        md: 12,\n        sm: 24,\n        xs: 24\n      },\n      // 规格数据\n      formDynamic: {\n        attrsName: '',\n        attrsVal: ''\n      },\n      isBtn: false,\n      manyFormValidate: [],\n      currentTab: 0,\n      isChoice: '',\n      grid: {\n        xl: 8,\n        lg: 8,\n        md: 12,\n        sm: 24,\n        xs: 24\n      },\n      ruleValidate: {\n        storeName: [{\n          required: true,\n          message: '请输入商品名称',\n          trigger: 'blur'\n        }],\n        cateIds: [{\n          required: true,\n          message: '请选择商品分类',\n          trigger: 'change',\n          type: 'array',\n          min: '1'\n        }],\n        keyword: [{\n          required: true,\n          message: '请输入商品关键字',\n          trigger: 'blur'\n        }],\n        unitName: [{\n          required: true,\n          message: '请输入单位',\n          trigger: 'blur'\n        }],\n        storeInfo: [{\n          required: true,\n          message: '请输入商品简介',\n          trigger: 'blur'\n        }],\n        tempId: [{\n          required: true,\n          message: '请选择运费模板',\n          trigger: 'change'\n        }],\n        image: [{\n          required: true,\n          message: '请上传商品图',\n          trigger: 'change'\n        }],\n        sliderImages: [{\n          required: true,\n          message: '请上传商品轮播图',\n          type: 'array',\n          trigger: 'change'\n        }],\n        specType: [{\n          required: true,\n          message: '请选择商品规格',\n          trigger: 'change'\n        }]\n      },\n      attrInfo: {},\n      tableFrom: {\n        page: 1,\n        limit: 9999,\n        keywords: ''\n      },\n      tempRoute: {},\n      keyNum: 0,\n      isAttr: false,\n      showAll: false,\n      videoLink: \"\"\n    };\n  },\n  computed: {\n    attrValue: function attrValue() {\n      var obj = Object.assign({}, defaultObj.attrValue[0]);\n      delete obj.image;\n      return obj;\n    },\n    oneFormBatch: function oneFormBatch() {\n      var obj = [Object.assign({}, defaultObj.attrValue[0])];\n      delete obj[0].barCode;\n      return obj;\n    }\n  },\n  watch: {\n    'formValidate.attr': {\n      handler: function handler(val) {\n        if (this.formValidate.specType && this.isAttr) this.watCh(val); //重要！！！\n      },\n      immediate: false,\n      deep: true\n    }\n  },\n  created: function created() {\n    this.tempRoute = Object.assign({}, this.$route);\n    if (this.$route.params.id && this.formValidate.specType) {\n      this.$watch('formValidate.attr', this.watCh);\n    }\n  },\n  mounted: function mounted() {\n    this.formValidate.sliderImages = [];\n    if (this.$route.params.id) {\n      this.setTagsViewTitle();\n      this.getInfo();\n    }\n    this.getCategorySelect();\n    this.getShippingList();\n    this.getGoodsType();\n  },\n  methods: {\n    // 校验输入框不能输入0，保留2位小数，库存为正整数\n    keyupEvent: function keyupEvent(key, val, index, num) {\n      if (key === 'barCode') return;\n      var re = /^\\D*([0-9]\\d*\\.?\\d{0,2})?.*$/;\n      switch (num) {\n        case 1:\n          if (val == 0) {\n            this.oneFormBatch[index][key] = key === 'stock' ? 0 : 0.01;\n          } else {\n            this.oneFormBatch[index][key] = key === 'stock' ? parseInt(val) : this.$set(this.oneFormBatch[index], key, val.toString().replace(re, '$1'));\n          }\n          break;\n        case 2:\n          if (val == 0) {\n            this.OneattrValue[index][key] = key === 'stock' ? 0 : 0.01;\n          } else {\n            this.OneattrValue[index][key] = key === 'stock' ? parseInt(val) : this.$set(this.OneattrValue[index], key, val.toString().replace(re, '$1'));\n          }\n          break;\n        default:\n          if (val == 0) {\n            this.ManyAttrValue[index][key] = key === 'stock' ? 0 : 0.01;\n          } else {\n            this.ManyAttrValue[index][key] = key === 'stock' ? parseInt(val) : this.$set(this.ManyAttrValue[index], key, val.toString().replace(re, '$1'));\n          }\n          break;\n      }\n    },\n    handleCloseCoupon: function handleCloseCoupon(tag) {\n      this.isAttr = true;\n      this.formValidate.coupons.splice(this.formValidate.coupons.indexOf(tag), 1);\n      this.formValidate.couponIds.splice(this.formValidate.couponIds.indexOf(tag.id), 1);\n    },\n    addCoupon: function addCoupon() {\n      var _this = this;\n      this.$modalCoupon('wu', this.keyNum += 1, this.formValidate.coupons, function (row) {\n        _this.formValidate.couponIds = [];\n        _this.formValidate.coupons = row;\n        row.map(function (item) {\n          _this.formValidate.couponIds.push(item.id);\n        });\n      }, '');\n    },\n    setTagsViewTitle: function setTagsViewTitle() {\n      var title = this.isDisabled ? '商品详情' : '编辑商品';\n      var route = Object.assign({}, this.tempRoute, {\n        title: \"\".concat(title, \"-\").concat(this.$route.params.id)\n      });\n      this.$store.dispatch('tagsView/updateVisitedView', route);\n    },\n    onChangeGroup: function onChangeGroup() {\n      this.checkboxGroup.includes('isGood') ? this.formValidate.isGood = true : this.formValidate.isGood = false;\n      this.checkboxGroup.includes('isBenefit') ? this.formValidate.isBenefit = true : this.formValidate.isBenefit = false;\n      this.checkboxGroup.includes('isBest') ? this.formValidate.isBest = true : this.formValidate.isBest = false;\n      this.checkboxGroup.includes('isNew') ? this.formValidate.isNew = true : this.formValidate.isNew = false;\n      this.checkboxGroup.includes('isHot') ? this.formValidate.isHot = true : this.formValidate.isHot = false;\n    },\n    watCh: function watCh(val) {\n      var _this2 = this;\n      var tmp = {};\n      var tmpTab = {};\n      this.formValidate.attr.forEach(function (o, i) {\n        // tmp['value' + i] = { title: o.attrName }\n        // tmpTab['value' + i] = ''\n        tmp[o.attrName] = {\n          title: o.attrName\n        };\n        tmpTab[o.attrName] = '';\n      });\n      this.ManyAttrValue = this.attrFormat(val);\n      this.ManyAttrValue.forEach(function (val, index) {\n        var key = Object.values(val.attrValue).sort().join('/');\n        if (_this2.attrInfo[key]) _this2.ManyAttrValue[index] = _this2.attrInfo[key];\n      });\n      this.attrInfo = [];\n      this.ManyAttrValue.forEach(function (val) {\n        _this2.attrInfo[Object.values(val.attrValue).sort().join('/')] = val;\n      });\n      this.manyTabTit = tmp;\n      this.manyTabDate = tmpTab;\n      this.formThead = Object.assign({}, this.formThead, tmp);\n    },\n    attrFormat: function attrFormat(arr) {\n      var data = [];\n      var res = [];\n      return format(arr);\n      function format(arr) {\n        if (arr.length > 1) {\n          arr.forEach(function (v, i) {\n            if (i === 0) data = arr[i]['attrValue'];\n            var tmp = [];\n            if (!data) return;\n            data.forEach(function (vv) {\n              arr[i + 1] && arr[i + 1]['attrValue'] && arr[i + 1]['attrValue'].forEach(function (g) {\n                var rep2 = (i !== 0 ? '' : arr[i]['attrName'] + '_') + vv + '$&' + arr[i + 1]['attrName'] + '_' + g;\n                tmp.push(rep2);\n                if (i === arr.length - 2) {\n                  var rep4 = {\n                    image: '',\n                    price: 0,\n                    cost: 0,\n                    otPrice: 0,\n                    stock: 0,\n                    barCode: '',\n                    weight: 0,\n                    volume: 0,\n                    brokerage: 0,\n                    brokerage_two: 0\n                  };\n                  rep2.split('$&').forEach(function (h, k) {\n                    var rep3 = h.split('_');\n                    if (!rep4['attrValue']) rep4['attrValue'] = {};\n                    rep4['attrValue'][rep3[0]] = rep3.length > 1 ? rep3[1] : '';\n                  });\n                  for (var attrValueKey in rep4.attrValue) {\n                    rep4[attrValueKey] = rep4.attrValue[attrValueKey];\n                  }\n                  res.push(rep4);\n                }\n              });\n            });\n            data = tmp.length ? tmp : [];\n          });\n        } else {\n          var dataArr = [];\n          arr.forEach(function (v, k) {\n            v['attrValue'].forEach(function (vv, kk) {\n              dataArr[kk] = v['attrName'] + '_' + vv;\n              res[kk] = {\n                image: '',\n                price: 0,\n                cost: 0,\n                otPrice: 0,\n                stock: 0,\n                barCode: '',\n                weight: 0,\n                volume: 0,\n                brokerage: 0,\n                brokerage_two: 0,\n                attrValue: _defineProperty({}, v['attrName'], vv)\n              };\n              // Object.values(res[kk].attrValue).forEach((v, i) => {\n              //   res[kk]['value' + i] = v\n              // })\n              for (var attrValueKey in res[kk].attrValue) {\n                res[kk][attrValueKey] = res[kk].attrValue[attrValueKey];\n              }\n            });\n          });\n          data.push(dataArr.join('$&'));\n        }\n        return res;\n      }\n    },\n    // 运费模板\n    addTem: function addTem() {\n      this.$refs.addTemplates.dialogVisible = true;\n      this.$refs.addTemplates.getCityList();\n    },\n    // 添加规则；\n    addRule: function addRule() {\n      var _this = this;\n      this.$modalAttr(this.formDynamics, function () {\n        _this.productGetRule();\n      });\n    },\n    // 选择规格\n    onChangeSpec: function onChangeSpec(num) {\n      this.isAttr = true;\n      if (num) this.productGetRule();\n    },\n    // 选择属性确认\n    confirm: function confirm() {\n      var _this3 = this;\n      this.isAttr = true;\n      if (!this.formValidate.selectRule) {\n        return this.$message.warning('请选择属性');\n      }\n      var data = [];\n      this.ruleList.forEach(function (item) {\n        if (item.id === _this3.formValidate.selectRule) {\n          item.ruleValue.forEach(function (i) {\n            data.push({\n              attrName: i.value,\n              attrValue: i.detail\n            });\n          });\n        }\n        _this3.formValidate.attr = data;\n      });\n    },\n    // 商品分类；\n    getCategorySelect: function getCategorySelect() {\n      var _this4 = this;\n      (0, _store.categoryApi)({\n        status: -1,\n        type: 1\n      }).then(function (res) {\n        _this4.merCateList = _this4.filerMerCateList(res);\n        var newArr = [];\n        res.forEach(function (value, index) {\n          newArr[index] = value;\n          if (value.child) newArr[index].child = value.child.filter(function (item) {\n            return item.status === true;\n          });\n        }); //过滤商品分类设置为隐藏的子分类不出现在树形列表里\n        _this4.merCateList = _this4.filerMerCateList(newArr);\n      });\n    },\n    filerMerCateList: function filerMerCateList(treeData) {\n      return treeData.map(function (item) {\n        if (!item.child) {\n          item.disabled = true;\n        }\n        item.label = item.name;\n        return item;\n      });\n    },\n    // 获取商品属性模板；\n    productGetRule: function productGetRule() {\n      var _this5 = this;\n      (0, _store.templateListApi)(this.tableFrom).then(function (res) {\n        var list = res.list;\n        for (var i = 0; i < list.length; i++) {\n          list[i].ruleValue = JSON.parse(list[i].ruleValue);\n        }\n        _this5.ruleList = list;\n      });\n    },\n    // 运费模板；\n    getShippingList: function getShippingList() {\n      var _this6 = this;\n      (0, _logistics.shippingTemplatesList)(this.tempData).then(function (res) {\n        _this6.shippingList = res.list;\n      });\n    },\n    showInput: function showInput(item) {\n      this.$set(item, 'inputVisible', true);\n    },\n    onChangetype: function onChangetype(item) {\n      var _this7 = this;\n      if (item === 1) {\n        this.OneattrValue.map(function (item) {\n          _this7.$set(item, 'brokerage', null);\n          _this7.$set(item, 'brokerageTwo', null);\n        });\n        this.ManyAttrValue.map(function (item) {\n          _this7.$set(item, 'brokerage', null);\n          _this7.$set(item, 'brokerageTwo', null);\n        });\n      } else {\n        this.OneattrValue.map(function (item) {\n          delete item.brokerage;\n          delete item.brokerageTwo;\n          _this7.$set(item, 'brokerage', null);\n          _this7.$set(item, 'brokerageTwo', null);\n        });\n        this.ManyAttrValue.map(function (item) {\n          delete item.brokerage;\n          delete item.brokerageTwo;\n        });\n      }\n    },\n    // 删除表格中的属性\n    delAttrTable: function delAttrTable(index) {\n      this.ManyAttrValue.splice(index, 1);\n    },\n    // 批量添加\n    batchAdd: function batchAdd() {\n      // if (!this.oneFormBatch[0].pic || !this.oneFormBatch[0].price || !this.oneFormBatch[0].cost || !this.oneFormBatch[0].ot_price ||\n      //     !this.oneFormBatch[0].stock || !this.oneFormBatch[0].bar_code) return this.$Message.warning('请填写完整的批量设置内容！');\n      var _iterator = _createForOfIteratorHelper(this.ManyAttrValue),\n        _step;\n      try {\n        for (_iterator.s(); !(_step = _iterator.n()).done;) {\n          var val = _step.value;\n          this.$set(val, 'image', this.oneFormBatch[0].image);\n          this.$set(val, 'price', this.oneFormBatch[0].price);\n          this.$set(val, 'cost', this.oneFormBatch[0].cost);\n          this.$set(val, 'otPrice', this.oneFormBatch[0].otPrice);\n          this.$set(val, 'stock', this.oneFormBatch[0].stock);\n          this.$set(val, 'barCode', this.oneFormBatch[0].barCode);\n          this.$set(val, 'weight', this.oneFormBatch[0].weight);\n          this.$set(val, 'volume', this.oneFormBatch[0].volume);\n          this.$set(val, 'brokerage', this.oneFormBatch[0].brokerage);\n          this.$set(val, 'brokerageTwo', this.oneFormBatch[0].brokerageTwo);\n        }\n      } catch (err) {\n        _iterator.e(err);\n      } finally {\n        _iterator.f();\n      }\n    },\n    // 添加按钮\n    addBtn: function addBtn() {\n      this.clearAttr();\n      this.isBtn = true;\n    },\n    // 取消\n    offAttrName: function offAttrName() {\n      this.isBtn = false;\n    },\n    clearAttr: function clearAttr() {\n      this.isAttr = true;\n      this.formDynamic.attrsName = '';\n      this.formDynamic.attrsVal = '';\n    },\n    // 删除规格\n    handleRemoveAttr: function handleRemoveAttr(index) {\n      this.isAttr = true;\n      this.formValidate.attr.splice(index, 1);\n      this.manyFormValidate.splice(index, 1);\n    },\n    // 删除属性\n    handleClose: function handleClose(item, index) {\n      item.splice(index, 1);\n    },\n    // 添加规则名称\n    createAttrName: function createAttrName() {\n      this.isAttr = true;\n      if (this.formDynamic.attrsName && this.formDynamic.attrsVal) {\n        var data = {\n          attrName: this.formDynamic.attrsName,\n          attrValue: [this.formDynamic.attrsVal]\n        };\n        this.formValidate.attr.push(data);\n        var hash = {};\n        this.formValidate.attr = this.formValidate.attr.reduce(function (item, next) {\n          /* eslint-disable */\n          hash[next.attrName] ? '' : hash[next.attrName] = true && item.push(next);\n          return item;\n        }, []);\n        this.clearAttr();\n        this.isBtn = false;\n      } else {\n        this.$Message.warning('请添加完整的规格！');\n      }\n    },\n    // 添加属性\n    createAttr: function createAttr(num, idx) {\n      this.isAttr = true;\n      if (num) {\n        this.formValidate.attr[idx].attrValue.push(num);\n        var hash = {};\n        this.formValidate.attr[idx].attrValue = this.formValidate.attr[idx].attrValue.reduce(function (item, next) {\n          /* eslint-disable */\n          hash[next] ? '' : hash[next] = true && item.push(next);\n          return item;\n        }, []);\n        this.formValidate.attr[idx].inputVisible = false;\n      } else {\n        this.$message.warning('请添加属性');\n      }\n    },\n    //点击展示所有多规格属性\n    showAllSku: function showAllSku() {\n      if (this.isAttr == false) {\n        this.isAttr = true;\n        if (this.formValidate.specType && this.isAttr) this.watCh(this.formValidate.attr); //重要！！！\n      } else if (this.isAttr == true) {\n        this.isAttr = false;\n        this.getInfo();\n      }\n    },\n    // 详情\n    getInfo: function getInfo() {\n      var _this8 = this;\n      this.fullscreenLoading = true;\n      (0, _store.productDetailApi)(this.$route.params.id).then(/*#__PURE__*/function () {\n        var _ref = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee(res) {\n          var info, imgs, imgss, manyAttr, tmp, tmpTab;\n          return _regenerator().w(function (_context) {\n            while (1) switch (_context.n) {\n              case 0:\n                // this.isAttr = true;\n                info = res;\n                _this8.formValidate = {\n                  image: _this8.$selfUtil.setDomain(info.image),\n                  sliderImage: info.sliderImage,\n                  sliderImages: JSON.parse(info.sliderImage),\n                  storeName: info.storeName,\n                  storeInfo: info.storeInfo,\n                  keyword: info.keyword,\n                  cateIds: info.cateId.split(','),\n                  // 商品分类id\n                  cateId: info.cateId,\n                  // 商品分类id传值\n                  unitName: info.unitName,\n                  sort: info.sort,\n                  isShow: info.isShow,\n                  isBenefit: info.isBenefit,\n                  isNew: info.isNew,\n                  isGood: info.isGood,\n                  isHot: info.isHot,\n                  isBest: info.isBest,\n                  tempId: info.tempId,\n                  attr: info.attr,\n                  attrValue: info.attrValue,\n                  selectRule: info.selectRule,\n                  isSub: info.isSub,\n                  content: _this8.$selfUtil.replaceImgSrcHttps(info.content),\n                  specType: info.specType,\n                  id: info.id,\n                  giveIntegral: info.giveIntegral,\n                  ficti: info.ficti,\n                  coupons: info.coupons,\n                  couponIds: info.couponIds,\n                  activity: info.activityStr ? info.activityStr.split(',') : ['默认', '秒杀', '砍价', '拼团']\n                };\n                (0, _marketing.marketingSendApi)({\n                  type: 3\n                }).then(function (res) {\n                  if (_this8.formValidate.couponIds !== null) {\n                    var ids = _this8.formValidate.couponIds.toString();\n                    var arr = res.list;\n                    var obj = {};\n                    for (var i in arr) {\n                      obj[arr[i].id] = arr[i];\n                    }\n                    var strArr = ids.split(',');\n                    var newArr = [];\n                    var _iterator2 = _createForOfIteratorHelper(strArr),\n                      _step2;\n                    try {\n                      for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {\n                        var item = _step2.value;\n                        if (obj[item]) {\n                          newArr.push(obj[item]);\n                        }\n                      }\n                    } catch (err) {\n                      _iterator2.e(err);\n                    } finally {\n                      _iterator2.f();\n                    }\n                    _this8.$set(_this8.formValidate, 'coupons', newArr); //在编辑回显时，让返回数据中的优惠券id，通过接口匹配显示,\n                  }\n                });\n                imgs = JSON.parse(info.sliderImage);\n                imgss = [];\n                Object.keys(imgs).map(function (i) {\n                  imgss.push(_this8.$selfUtil.setDomain(imgs[i]));\n                });\n                _this8.formValidate.sliderImages = [].concat(imgss);\n                if (info.isHot) _this8.checkboxGroup.push('isHot');\n                if (info.isGood) _this8.checkboxGroup.push('isGood');\n                if (info.isBenefit) _this8.checkboxGroup.push('isBenefit');\n                if (info.isBest) _this8.checkboxGroup.push('isBest');\n                if (info.isNew) _this8.checkboxGroup.push('isNew');\n                _this8.productGetRule();\n                if (info.specType) {\n                  _this8.formValidate.attr = info.attr.map(function (item) {\n                    return {\n                      attrName: item.attrName,\n                      attrValue: item.attrValues.split(',')\n                    };\n                  });\n                  _this8.ManyAttrValue = info.attrValue;\n                  _this8.ManyAttrValue.forEach(function (val) {\n                    val.image = _this8.$selfUtil.setDomain(val.image);\n                    val.attrValue = JSON.parse(val.attrValue);\n                    _this8.attrInfo[Object.values(val.attrValue).sort().join('/')] = val;\n                  });\n                  /***多规格商品如果被删除过sku，优先展示api返回的数据,否则会有没有删除的错觉***/\n                  manyAttr = _this8.attrFormat(_this8.formValidate.attr);\n                  if (manyAttr.length !== _this8.ManyAttrValue.length) {\n                    _this8.$set(_this8, 'showAll', true);\n                    _this8.isAttr = false;\n                  } else {\n                    _this8.isAttr = true;\n                  }\n                  /*******/\n                  tmp = {};\n                  tmpTab = {};\n                  _this8.formValidate.attr.forEach(function (o, i) {\n                    // tmp['value' + i] = { title: o.attrName }\n                    // tmpTab['value' + i] = ''\n                    tmp[o.attrName] = {\n                      title: o.attrName\n                    };\n                    tmpTab[o.attrName] = '';\n                  });\n\n                  // 此处手动实现后台原本value0 value1的逻辑\n                  _this8.formValidate.attrValue.forEach(function (item) {\n                    for (var attrValueKey in item.attrValue) {\n                      item[attrValueKey] = item.attrValue[attrValueKey];\n                    }\n                  });\n                  _this8.manyTabTit = tmp;\n                  _this8.manyTabDate = tmpTab;\n                  _this8.formThead = Object.assign({}, _this8.formThead, tmp);\n                } else {\n                  _this8.OneattrValue = info.attrValue;\n                  // this.formValidate.attr = [] //单规格商品规格设置为空\n                }\n                _this8.fullscreenLoading = false;\n              case 1:\n                return _context.a(2);\n            }\n          }, _callee);\n        }));\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }()).catch(function (res) {\n        _this8.fullscreenLoading = false;\n        _this8.$message.error(res.message);\n      });\n    },\n    handleRemove: function handleRemove(i) {\n      this.formValidate.sliderImages.splice(i, 1);\n    },\n    // 点击商品图\n    modalPicTap: function modalPicTap(tit, num, i, status) {\n      var _this = this;\n      if (_this.isDisabled) return;\n      this.$modalUpload(function (img) {\n        if (tit === '1' && !num) {\n          _this.formValidate.image = img[0].sattDir;\n          _this.OneattrValue[0].image = img[0].sattDir;\n        }\n        if (tit === '2' && !num) {\n          if (img.length > 10) return this.$message.warning(\"最多选择10张图片！\");\n          if (img.length + _this.formValidate.sliderImages.length > 10) return this.$message.warning(\"最多选择10张图片！\");\n          img.map(function (item) {\n            _this.formValidate.sliderImages.push(item.sattDir);\n          });\n        }\n        if (tit === '1' && num === 'dan') {\n          _this.OneattrValue[0].image = img[0].sattDir;\n        }\n        if (tit === '1' && num === 'duo') {\n          _this.ManyAttrValue[i].image = img[0].sattDir;\n        }\n        if (tit === '1' && num === 'pi') {\n          _this.oneFormBatch[0].image = img[0].sattDir;\n        }\n      }, tit, 'content');\n    },\n    handleSubmitUp: function handleSubmitUp() {\n      // this.currentTab --\n      if (this.currentTab-- < 0) this.currentTab = 0;\n    },\n    handleSubmitNest: function handleSubmitNest(name) {\n      var _this9 = this;\n      this.$refs[name].validate(function (valid) {\n        if (valid) {\n          if (_this9.currentTab++ > 2) _this9.currentTab = 0;\n        } else {\n          if (!_this9.formValidate.store_name || !_this9.formValidate.cate_id || !_this9.formValidate.keyword || !_this9.formValidate.unit_name || !_this9.formValidate.store_info || !_this9.formValidate.image || !_this9.formValidate.slider_image) {\n            _this9.$message.warning(\"请填写完整商品信息！\");\n          }\n        }\n      });\n    },\n    // 提交\n    handleSubmit: (0, _validate.Debounce)(function (name) {\n      var _this0 = this;\n      this.onChangeGroup();\n      if (this.formValidate.specType && this.formValidate.attr.length < 1) return this.$message.warning(\"请填写多规格属性！\");\n      this.formValidate.cateId = this.formValidate.cateIds.join(',');\n      this.formValidate.sliderImage = JSON.stringify(this.formValidate.sliderImages);\n      if (this.formValidate.specType) {\n        this.formValidate.attrValue = this.ManyAttrValue;\n        this.formValidate.attr = this.formValidate.attr.map(function (item) {\n          return {\n            attrName: item.attrName,\n            id: item.id,\n            attrValues: item.attrValue.join(',')\n          };\n        });\n        for (var i = 0; i < this.formValidate.attrValue.length; i++) {\n          this.$set(this.formValidate.attrValue[i], 'id', 0);\n          this.$set(this.formValidate.attrValue[i], 'productId', 0);\n          this.$set(this.formValidate.attrValue[i], 'attrValue', JSON.stringify(this.formValidate.attrValue[i].attrValue)); //\n          delete this.formValidate.attrValue[i].value0;\n        }\n      } else {\n        this.formValidate.attr = [{\n          attrName: '规格',\n          attrValues: '默认',\n          id: this.$route.params.id ? this.formValidate.attr[0].id : 0\n        }];\n        this.OneattrValue.map(function (item) {\n          _this0.$set(item, 'attrValue', JSON.stringify({\n            '规格': '默认'\n          }));\n        });\n        this.formValidate.attrValue = this.OneattrValue;\n      }\n      this.$refs[name].validate(function (valid) {\n        if (valid) {\n          _this0.fullscreenLoading = true;\n          _this0.$route.params.id ? (0, _store.productUpdateApi)(_this0.formValidate).then(/*#__PURE__*/function () {\n            var _ref2 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee2(res) {\n              return _regenerator().w(function (_context2) {\n                while (1) switch (_context2.n) {\n                  case 0:\n                    _this0.$message.success('编辑成功');\n                    setTimeout(function () {\n                      _this0.$router.push({\n                        path: '/store/index'\n                      });\n                    }, 500);\n                    _this0.fullscreenLoading = false;\n                  case 1:\n                    return _context2.a(2);\n                }\n              }, _callee2);\n            }));\n            return function (_x2) {\n              return _ref2.apply(this, arguments);\n            };\n          }()).catch(function (res) {\n            _this0.fullscreenLoading = false;\n          }) : (0, _store.productCreateApi)(_this0.formValidate).then(/*#__PURE__*/function () {\n            var _ref3 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee3(res) {\n              return _regenerator().w(function (_context3) {\n                while (1) switch (_context3.n) {\n                  case 0:\n                    _this0.$message.success('新增成功');\n                    setTimeout(function () {\n                      _this0.$router.push({\n                        path: '/store/index'\n                      });\n                    }, 500);\n                    _this0.fullscreenLoading = false;\n                  case 1:\n                    return _context3.a(2);\n                }\n              }, _callee3);\n            }));\n            return function (_x3) {\n              return _ref3.apply(this, arguments);\n            };\n          }()).catch(function (res) {\n            _this0.fullscreenLoading = false;\n          });\n        } else {\n          if (!_this0.formValidate.storeName || !_this0.formValidate.cateId || !_this0.formValidate.keyword || !_this0.formValidate.unitName || !_this0.formValidate.storeInfo || !_this0.formValidate.image || !_this0.formValidate.sliderImages) {\n            _this0.$message.warning(\"请填写完整商品信息！\");\n          }\n        }\n      });\n    }),\n    // 表单验证\n    validate: function validate(prop, status, error) {\n      if (status === false) {\n        this.$message.warning(error);\n      }\n    },\n    // 移动\n    handleDragStart: function handleDragStart(e, item) {\n      if (!this.isDisabled) this.dragging = item;\n    },\n    handleDragEnd: function handleDragEnd(e, item) {\n      if (!this.isDisabled) this.dragging = null;\n    },\n    handleDragOver: function handleDragOver(e) {\n      if (!this.isDisabled) e.dataTransfer.dropEffect = 'move';\n    },\n    handleDragEnter: function handleDragEnter(e, item) {\n      if (!this.isDisabled) {\n        e.dataTransfer.effectAllowed = 'move';\n        if (item === this.dragging) {\n          return;\n        }\n        var newItems = _toConsumableArray(this.formValidate.sliderImages);\n        var src = newItems.indexOf(this.dragging);\n        var dst = newItems.indexOf(item);\n        newItems.splice.apply(newItems, [dst, 0].concat(_toConsumableArray(newItems.splice(src, 1))));\n        this.formValidate.sliderImages = newItems;\n      }\n    },\n    handleDragEnterFont: function handleDragEnterFont(e, item) {\n      if (!this.isDisabled) {\n        e.dataTransfer.effectAllowed = 'move';\n        if (item === this.dragging) {\n          return;\n        }\n        var newItems = _toConsumableArray(this.formValidate.activity);\n        var src = newItems.indexOf(this.dragging);\n        var dst = newItems.indexOf(item);\n        newItems.splice.apply(newItems, [dst, 0].concat(_toConsumableArray(newItems.splice(src, 1))));\n        this.formValidate.activity = newItems;\n      }\n    },\n    getGoodsType: function getGoodsType() {\n      var _this1 = this;\n      /** 让商品推荐列表的name属性与页面设置tab的name匹配**/\n      (0, _systemGroup.goodDesignList)({\n        gid: 70\n      }).then(function (response) {\n        var list = response.list;\n        var arr = [],\n          arr1 = [];\n        var listArr = [{\n          name: '是否热卖',\n          value: 'isGood'\n        }];\n        var typeLists = [{\n          name: '',\n          value: 'isHot',\n          type: '2'\n        },\n        //热门榜单 \n        {\n          name: '',\n          value: 'isBenefit',\n          type: '4'\n        },\n        //促销单品\n        {\n          name: '',\n          value: 'isBest',\n          type: '1'\n        },\n        //精品推荐\n        {\n          name: '',\n          value: 'isNew',\n          type: '3'\n        }]; //首发新品\n        list.forEach(function (item) {\n          var obj = {};\n          obj.value = JSON.parse(item.value);\n          obj.id = item.id;\n          obj.gid = item.gid;\n          obj.status = item.status;\n          arr.push(obj);\n        });\n        arr.forEach(function (item1) {\n          var obj1 = {};\n          obj1.name = item1.value.fields[1].value;\n          obj1.status = item1.status;\n          obj1.type = item1.value.fields[3].value;\n          arr1.push(obj1);\n        });\n        typeLists.forEach(function (item) {\n          arr1.forEach(function (item1) {\n            if (item.type == item1.type) {\n              listArr.push({\n                name: item1.name,\n                value: item.value,\n                type: item.type\n              });\n            }\n          });\n        });\n        _this1.recommend = listArr;\n      });\n    }\n  }\n};", null]}