@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page-wrapper.data-v-5d5dcf67 {
  min-height: 100vh;
  background-color: #f8f8f8;
}
.nav-title.data-v-5d5dcf67 {
  padding: 10px 40rpx;
  display: flex;
  align-items: center;
  height: 60rpx;
  line-height: 60rpx;
  background: #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 20rpx;
}
.nav-title .color.data-v-5d5dcf67 {
  width: 10rpx;
  height: 30rpx;
  background: #c9ab79;
  border-radius: 6rpx;
  margin-right: 10rpx;
}
.nav-title .text.data-v-5d5dcf67 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.content-container.data-v-5d5dcf67 {
  padding: 0 30rpx 40rpx;
}
.list-content.data-v-5d5dcf67 {
  padding: 0;
}
.question-card.data-v-5d5dcf67 {
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  margin-bottom: 30rpx;
  transition: all 0.3s ease;
}
.question-card.data-v-5d5dcf67:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}
.question-card.data-v-5d5dcf67:first-child {
  margin-top: 20rpx;
}
.card-header.data-v-5d5dcf67 {
  padding: 24rpx;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.date-info.data-v-5d5dcf67 {
  display: flex;
  align-items: center;
  color: #666;
  font-size: 28rpx;
}
.date-info .uni-icons.data-v-5d5dcf67 {
  margin-right: 8rpx;
}
.status-tag.data-v-5d5dcf67 {
  padding: 6rpx 24rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
}
.status-pending.data-v-5d5dcf67 {
  background-color: rgba(255, 152, 0, 0.1);
  color: #ff9800;
  border: 1px solid #ff9800;
}
.status-submitted.data-v-5d5dcf67 {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
  border: 1px solid #4caf50;
}
.card-content.data-v-5d5dcf67 {
  padding: 30rpx 24rpx;
  min-height: 150rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.chakra-preview.data-v-5d5dcf67 {
  width: 100%;
  text-align: center;
}
.chakra-title.data-v-5d5dcf67 {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}
.chakra-indicators.data-v-5d5dcf67 {
  display: flex;
  justify-content: center;
  gap: 10rpx;
  margin-bottom: 20rpx;
  flex-wrap: wrap;
}
.chakra-item.data-v-5d5dcf67 {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 80rpx;
  margin-bottom: 16rpx;
}
.chakra-dot.data-v-5d5dcf67 {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  margin-bottom: 8rpx;
}
.chakra-value.data-v-5d5dcf67 {
  font-size: 22rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}
.chakra-name.data-v-5d5dcf67 {
  font-size: 20rpx;
  color: #666;
  white-space: nowrap;
}
.preview-hint.data-v-5d5dcf67 {
  font-size: 24rpx;
  color: #999;
}
.test-preview.data-v-5d5dcf67 {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}
.preview-icon.data-v-5d5dcf67 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: rgba(201, 171, 121, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
}
.preview-text.data-v-5d5dcf67 {
  font-size: 28rpx;
  color: #666;
}
.card-footer.data-v-5d5dcf67 {
  padding: 24rpx;
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
  background-color: rgba(0, 0, 0, 0.02);
}
.action-button.data-v-5d5dcf67 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 24rpx;
  height: 64rpx;
  border-radius: 32rpx;
  font-size: 26rpx;
  font-weight: 500;
  transition: all 0.3s ease;
  gap: 8rpx;
}
.action-button.data-v-5d5dcf67:active {
  opacity: 0.8;
}
.detail-btn.data-v-5d5dcf67 {
  background-color: #c9ab79;
  color: #fff;
}
.delete-btn.data-v-5d5dcf67 {
  background-color: #dd5c5f;
  color: #fff;
}
.create-btn.data-v-5d5dcf67 {
  background-color: #c9ab79;
  color: #fff;
  margin-top: 30rpx;
}
.empty-state.data-v-5d5dcf67 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 160rpx 60rpx;
}
.empty-image.data-v-5d5dcf67 {
  width: 240rpx;
  height: 240rpx;
  margin-bottom: 30rpx;
}
.empty-text.data-v-5d5dcf67 {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 20rpx;
}

