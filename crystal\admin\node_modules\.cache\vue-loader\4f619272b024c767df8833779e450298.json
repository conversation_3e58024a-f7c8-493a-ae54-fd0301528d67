{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\userList\\index.vue?vue&type=template&id=ec567b48&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\userList\\index.vue", "mtime": 1753666157797}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\n<div class=\"divBox\">\n  <el-card class=\"box-card\">\n    <div slot=\"header\" class=\"clearfix\">\n      <el-form inline>\n        <el-form-item>\n          <el-input v-model=\"tableFrom.keywords\" placeholder=\"请输入用户名称\" class=\"selWidth\">\n            <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"search\" />\n          </el-input>\n        </el-form-item>\n      </el-form>\n    </div>\n    <el-table\n      v-loading=\"loading\"\n      :data=\"tableData.data\"\n      width=\"800px\"\n      size=\"small\"\n    >\n      <el-table-column label=\"\" width=\"40\">\n        <template slot-scope=\"scope\">\n          <el-radio v-model=\"templateRadio\" :label=\"scope.row.uid\" @change.native=\"getTemplateRow(scope.$index,scope.row)\">&nbsp</el-radio>\n        </template>\n      </el-table-column>\n      <el-table-column\n        prop=\"uid\"\n        label=\"ID\"\n        min-width=\"60\"\n      />\n      <el-table-column\n        prop=\"nickname\"\n        label=\"微信用户名称\"\n        min-width=\"130\"\n      />\n      <el-table-column label=\"用户头像\" min-width=\"80\">\n        <template slot-scope=\"scope\">\n          <div class=\"demo-image__preview\">\n            <el-image\n              class=\"tabImage\"\n              :src=\"scope.row.avatar\"\n              :preview-src-list=\"[scope.row.avatar]\"\n            />\n          </div>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"性别\"\n        min-width=\"80\"\n      >\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.sex | saxFilter }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"地区\"\n        min-width=\"130\"\n      >\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.addres }}</span>\n        </template>\n      </el-table-column>\n    </el-table>\n    <div class=\"block\">\n      <el-pagination\n        :page-sizes=\"[10, 20, 30, 40]\"\n        :page-size=\"tableFrom.limit\"\n        :current-page=\"tableFrom.page\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        :total=\"tableData.total\"\n        @size-change=\"handleSizeChange\"\n        @current-change=\"pageChange\"\n      />\n    </div>\n  </el-card>\n</div>\n", null]}