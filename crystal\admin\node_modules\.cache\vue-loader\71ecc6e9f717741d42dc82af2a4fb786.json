{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\order\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\order\\index.vue", "mtime": 1753666157910}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\n  import { orderListDataApi, orderStatusNumApi, writeUpdateApi, orderListApi, updatePriceApi, orderLogApi, orderMarkApi, orderDeleteApi, orderRefuseApi, orderRefundApi,orderPrint } from '@/api/order'\n  import cardsData from '@/components/cards/index'\n  import zbParser from '@/components/FormGenerator/components/parser/ZBParser'\n  import detailsFrom from './orderDetail'\n  import orderSend from './orderSend'\n  import orderVideoSend from './orderVideoSend'\n  import { storeStaffListApi } from '@/api/storePoint'\n  import Cookies from 'js-cookie'\n  import { isWriteOff } from \"@/utils\";\n  import {orderExcelApi} from '@/api/store'\n  import { checkPermi } from \"@/utils/permission\"; // 权限判断函数\n  export default {\n    name: 'orderlistDetails',\n    components: {\n      cardsData,\n      zbParser,\n      detailsFrom,\n      orderSend,\n      orderVideoSend\n    },\n    data() {\n        return {\n          RefuseVisible: false,\n          RefuseData:{},\n          orderId: '',\n          refundVisible: false,\n          refundData: {},\n          dialogVisibleJI: false,\n          tableDataLog: {\n            data: [],\n            total: 0\n          },\n          tableFromLog: {\n            page: 1,\n            limit: 10,\n            orderNo: 0\n          },\n          LogLoading: false,\n          isCreate: 1,\n          editData: null,\n          dialogVisible: false,\n          tableData: {\n            data: [],\n            total: 0\n          },\n          listLoading: true,\n          tableFrom: {\n            status: 'all',\n            dateLimit: '',\n            orderNo: '',\n            page: 1,\n            limit: 10,\n            type: 0\n          },\n          orderChartType: {},\n          timeVal: [],\n          fromList: this.$constants.fromList,\n          fromType:[\n            {value:'all',text:'全部'},\n            {value:'info',text:'普通'},\n            {value:'pintuan',text:'拼团'},\n            {value:'bragin',text:'砍价'},\n            {value:'miaosha',text:'秒杀'},\n          ],\n          selectionList: [],\n          ids: '',\n          orderids: '',\n          cardLists: [],\n          isWriteOff: isWriteOff(),\n          proType: 0,\n          active:false,\n        }\n      },\n    mounted() {\n      this.getList();\n      this.getOrderStatusNum();\n      // this.getOrderListData();\n    },\n    methods: {\n      checkPermi,\n      resetFormRefundhandler(){\n        this.refundVisible = false\n      },\n      resetFormRefusehand(){\n        this.RefuseVisible = false\n      },\n      resetForm(formValue) {\n        this.dialogVisible = false\n      },\n      // 核销订单\n      onWriteOff(row) {\n        this.$modalSure('核销订单吗').then(() => {\n          writeUpdateApi(row.verifyCode).then(() => {\n            this.$message.success('核销成功')\n            this.tableFrom.page = 1\n            this.getList()\n          })\n        })\n      },\n      seachList() {\n        this.tableFrom.page = 1\n        this.getList()\n        this.getOrderStatusNum()\n      },\n      // 拒绝退款\n      RefusehandleClose() {\n        this.RefuseVisible = false\n      },\n      onOrderRefuse(row) {\n        this.orderids = row.orderId\n        this.RefuseData = {\n          orderId: row.orderId,\n          reason: ''\n        }\n        this.RefuseVisible = true\n      },\n      RefusehandlerSubmit(formValue) {\n        orderRefuseApi({ orderNo: this.orderids, reason: formValue.reason}).then(data => {\n          this.$message.success('操作成功')\n          this.RefuseVisible = false\n          this.getList()\n        })\n      },\n      // 立即退款\n      refundhandleClose() {\n        this.refundVisible = false\n      },\n      onOrderRefund(row) {\n        this.refundData = {\n          orderId: row.orderId,\n          amount: row.payPrice,\n          type: ''\n        }\n        this.orderids = row.orderId\n        this.refundVisible = true\n      },\n      refundhandlerSubmit(formValue) {\n        orderRefundApi({ amount: formValue.amount, orderNo: this.orderids}).then(data => {\n          this.$message.success('操作成功')\n          this.refundVisible = false\n          this.getList()\n        })\n      },\n      // 发送\n      sendOrder(row) {\n        if(row.type===0 || row.type===2){\n          this.$refs.send.modals = true;\n          this.$refs.send.getList();\n          this.$refs.send.sheetInfo();\n        }else{\n          this.$refs.videoSend.modals = true;\n          if(!JSON.parse(sessionStorage.getItem('videoExpress'))) this.$refs.videoSend.companyGetList();\n        }\n        this.orderId = row.orderId;\n      },\n      // 订单删除\n      handleDelete(row, idx) {\n        if (row.isDel) {\n          this.$modalSure().then(() => {\n            orderDeleteApi({ orderNo: row.orderId }).then(() => {\n              this.$message.success('删除成功')\n              this.tableData.data.splice(idx, 1)\n            })\n          })\n        } else {\n          this.$confirm('您选择的的订单存在用户未删除的订单，无法删除用户未删除的订单！', '提示', {\n            confirmButtonText: '确定',\n            type: 'error'\n          })\n        }\n      },\n      // 详情\n      onOrderDetails(id) {\n        this.orderId = id\n        this.$refs.orderDetail.getDetail(id)\n        this.$refs.orderDetail.dialogVisible = true\n      },\n      // 订单记录\n      onOrderLog(id) {\n        this.dialogVisibleJI = true\n        this.LogLoading = true\n        this.tableFromLog.orderNo = id\n        orderLogApi( this.tableFromLog ).then(res => {\n          this.tableDataLog.data = res.list\n          this.tableDataLog.total = res.total\n          this.LogLoading = false\n        }).catch(() => {\n          this.LogLoading = false\n        })\n      },\n      pageChangeLog(page) {\n        this.tableFromLog.page = page\n        this.onOrderLog()\n      },\n      handleSizeChangeLog(val) {\n        this.tableFromLog.limit = val\n        this.onOrderLog()\n      },\n      handleClose() {\n        this.dialogVisible = false;\n      },\n      // 备注\n      onOrderMark(row) {\n        this.$prompt('订单备注', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          inputErrorMessage: '请输入订单备注',\n          inputType: 'textarea',\n          inputValue: row.remark,\n          inputPlaceholder: '请输入订单备注',\n          inputValidator: (value) => { if(!value) return '输入不能为空'}\n        }).then(({value}) => {\n          orderMarkApi({ mark : value, orderNo: row.orderId}).then(() => {\n            this.$message.success('操作成功')\n            this.getList();\n          })\n        }).catch(() => {\n          this.$message.info('取消输入')\n        })\n      },\n      handleSelectionChange(val) {\n        this.selectionList = val\n        const data = []\n        this.selectionList.map((item) => {\n          data.push(item.orderId)\n        })\n        this.ids = data.join(',')\n      },\n      // 选择时间\n      selectChange (tab) {\n        this.timeVal = [];\n        this.tableFrom.page = 1\n        this.getList();\n        this.getOrderStatusNum();\n        // this.getOrderListData();\n      },\n      // 具体日期\n      onchangeTime (e) {\n        this.timeVal = e;\n        this.tableFrom.dateLimit = e ? this.timeVal.join(',') : ''\n        this.tableFrom.page = 1\n        this.getList();\n        this.getOrderStatusNum();\n        // this.getOrderListData();\n      },\n      // 编辑\n      edit(row) {\n        this.orderId = row.orderId\n        this.editData = {\n          orderId: row.orderId,\n          totalPrice: row.totalPrice,\n          totalPostage: row.totalPostage,\n          payPrice: row.payPrice,\n          payPostage: row.payPostage,\n          gainIntegral: row.gainIntegral,\n        }\n        this.dialogVisible = true\n      },\n      handlerSubmit(formValue) {\n        let data = {\n          orderNo:formValue.orderId,\n          payPrice:formValue.payPrice\n        }\n        updatePriceApi(data).then(data => {\n          this.$message.success('编辑数据成功')\n          this.dialogVisible = false\n          this.getList()\n        })\n      },\n      // 列表\n      getList() {\n        this.listLoading = true\n        orderListApi(this.tableFrom).then(res => {\n          this.tableData.data = res.list || [];\n          this.tableData.total = res.total;\n          this.listLoading = false\n          this.checkedCities = this.$cache.local.has('order_stroge') ? this.$cache.local.getJSON('order_stroge') : this.checkedCities;\n        }).catch(() => {\n          this.listLoading = false\n        })\n      },\n      // 数据统计\n      getOrderListData() {\n        orderListDataApi({dateLimit:this.tableFrom.dateLimit}).then(res => {\n          this.cardLists = [\n            { name: '订单数量', count: res.count,color:'#1890FF',class:'one',icon:'icondingdan' },\n            { name: '订单金额', count: res.amount, color:'#A277FF',class:'two',icon:'icondingdanjine' },\n            { name: '微信支付金额', count: res.weChatAmount, color:'#EF9C20',class:'three',icon:'iconweixinzhifujine' },\n            { name: '余额支付金额', count: res.yueAmount,color:'#1BBE6B',class:'four',icon:'iconyuezhifujine2' }\n          ]\n        });\n      },\n      // 获取各状态数量\n      getOrderStatusNum() {\n        orderStatusNumApi({dateLimit:this.tableFrom.dateLimit,type:this.tableFrom.type}).then(res => {\n          this.orderChartType = res;\n        });\n      },\n      pageChange(page) {\n        this.tableFrom.page = page\n        this.getList()\n      },\n      handleSizeChange(val) {\n        this.tableFrom.limit = val\n        this.getList()\n      },\n      exports(){\n        let data = {\n          dateLimit:this.tableFrom.dateLimit,\n          orderNo:this.tableFrom.orderNo,\n          status:this.tableFrom.status,\n          type:this.tableFrom.type\n        };\n        orderExcelApi(data).then(res=>{\n          window.open(res.fileName);\n        })\n      },\n      //打印小票\n      onOrderPrint(data){\n        orderPrint(data.orderId).then(res=>{\n          this.$modal.msgSuccess('打印成功');\n        }).catch(error=>{\n          this.$modal.msgError(error.message)\n        })\n      }\n    }\n}\n", null]}