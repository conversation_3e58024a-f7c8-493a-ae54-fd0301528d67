{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/jyf-parser/libs/trees.vue?e029", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/jyf-parser/libs/trees.vue?24e2", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/jyf-parser/libs/trees.vue?27b3", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/jyf-parser/libs/trees.vue?0586", "uni-app:///components/jyf-parser/libs/trees.vue", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/jyf-parser/libs/trees.vue?9645", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/jyf-parser/libs/trees.vue?db8f", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/jyf-parser/libs/handler.wxs?87ec", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/jyf-parser/libs/handler.wxs?9c16"], "names": ["global", "components", "trees", "name", "data", "controls", "imgLoad", "loadVideo", "props", "nodes", "lazyLoad", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "methods", "play", "imgtap", "id", "src", "ignore", "current", "uni", "urls", "imglongtap", "linkpress", "attrs", "appId", "path", "success", "title", "url", "error", "target", "source", "errMsg", "errCode", "context", "_loadVideo", "index"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsT;AACtT;AACyD;AACL;AACa;;;AAGjE;AACmM;AACnM,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,oRAAM;AACR,EAAE,6RAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wRAAU;AACZ;AACA;;AAEA;AACqO;AACrO,WAAW,uPAAM,iBAAiB,+PAAM;;AAExC;AACe,gF;;;;;;;;;;;;AC3Bf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAkwB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACuHtxBA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAEA;EACAC;IACAC;EACA;EACAC;EACAC;IACA;MACAC;MAEAC;MAGAC;IAEA;EACA;EACAC;IACAC;IAEAC;EAKA;EACAC;IACA;IACA;IACA;MACA;QACA;QACA;MACA;MACA;IACA;EACA;EAEAC;IACA,mBACA;EACA;EAEAC;IAEAC;MACA,6DACA;QACA,gEACA;MAAA;IACA;IAEAC;MACA;MACA;QACA;UAAAX;YACAY;YACAC;YACAC;cAAA;YAAA;UACA;QACAlB;QACA;QACA;UACA;YACAmB;UACAC;YACAD;YACAE;UACA;QACA;MACA;IACA;IACAC;MACA;MACA,mBACA;QACAN;QACAC;MACA;IACA;IACAM;MACA;QACAC;MACAA;QAAA;MAAA;MACAxB;MACA;MACA;QAEA;UACA;YACAyB;YACAC;UACA;QACA;QAEA;UACA;YACA,wBACA;cACAV;YACA;UACA;YAKAI;cACAhB;cACAuB;gBAAA,OACAP;kBACAQ;gBACA;cAAA;YACA;UAEA,OACAR;YACAS;UACA;QACA;MACA;IACA;IACAC;MACA;QAAAC;QACAC;MACA;QACA;QACA;QACA,0CACA;QACA;MACA;MACA;QACAA;QACAD;QACAE;QACAC;QACAC;MACA;IACA;IACAC;MACA;QACAtB;QACAuB;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1QA;AAAA;AAAA;AAAA;AAAqlC,CAAgB,s8BAAG,EAAC,C;;;;;;;;;;;ACAzmC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA,wCAAoY,CAAgB,sbAAG,EAAC,C;;;;;;;;;;;;ACAxZ;AAAe;AACf;AACA;AACA;;AAEA,M", "file": "components/jyf-parser/libs/trees.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./trees.vue?vue&type=template&id=13da2543&filter-modules=eyJoYW5kbGVyIjp7InR5cGUiOiJzY3JpcHQiLCJjb250ZW50IjoiIiwic3RhcnQiOjY0ODUsImF0dHJzIjp7Im1vZHVsZSI6ImhhbmRsZXIiLCJsYW5nIjoid3hzIiwic3JjIjoiLi9oYW5kbGVyLnd4cyJ9LCJlbmQiOjY0ODV9fQ%3D%3D&\"\nvar renderjs\nimport script from \"./trees.vue?vue&type=script&lang=js&\"\nexport * from \"./trees.vue?vue&type=script&lang=js&\"\nimport style0 from \"./trees.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\n/* custom blocks */\nimport block0 from \"./handler.wxs?vue&type=custom&index=0&blockType=script&issuerPath=C%3A%5CUsers%5Cchenjiayin%5CDesktop%5Ccode%5Ccrystal-mall%5Ccrystal%5Capp%5Ccomponents%5Cjyf-parser%5Clibs%5Ctrees.vue&module=handler&lang=wxs\"\nif (typeof block0 === 'function') block0(component)\n\ncomponent.options.__file = \"components/jyf-parser/libs/trees.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./trees.vue?vue&type=template&id=13da2543&filter-modules=eyJoYW5kbGVyIjp7InR5cGUiOiJzY3JpcHQiLCJjb250ZW50IjoiIiwic3RhcnQiOjY0ODUsImF0dHJzIjp7Im1vZHVsZSI6ImhhbmRsZXIiLCJsYW5nIjoid3hzIiwic3JjIjoiLi9oYW5kbGVyLnd4cyJ9LCJlbmQiOjY0ODV9fQ%3D%3D&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./trees.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./trees.vue?vue&type=script&lang=js&\"", "<!--\r\n  trees 递归显示组件\r\n  github：https://github.com/jin-yufeng/Parser \r\n  docs：https://jin-yufeng.github.io/Parser\r\n  插件市场：https://ext.dcloud.net.cn/plugin?id=805\r\n  author：Jin<PERSON>ufeng\r\n  update：2020/04/13\r\n-->\r\n<template>\r\n\t<view class=\"interlayer\">\r\n\t\t<block v-for=\"(n, index) in nodes\" v-bind:key=\"index\">\r\n\t\t\t<!--图片-->\r\n\t\t\t<!--#ifdef MP-WEIXIN || MP-QQ || MP-ALIPAY || APP-PLUS-->\r\n\t\t\t<rich-text v-if=\"n.name=='img'\" :id=\"n.attrs.id\" class=\"_img\" :style=\"''+handler.getStyle(n.attrs.style)\" :nodes=\"handler.getNode(n,!lazyLoad||imgLoad)\"\r\n\t\t\t :data-attrs=\"n.attrs\" @tap=\"imgtap\" @longpress=\"imglongtap\" />\r\n\t\t\t<!--#endif-->\r\n\t\t\t<!--#ifdef MP-BAIDU || MP-TOUTIAO-->\r\n\t\t\t<rich-text v-if=\"n.name=='img'\" :id=\"n.attrs.id\" class=\"_img\" :style=\"n.attrs.contain\" :nodes='[n]' :data-attrs=\"n.attrs\"\r\n\t\t\t @tap=\"imgtap\" @longpress=\"imglongtap\" />\r\n\t\t\t<!--#endif-->\r\n\t\t\t<!--文本-->\r\n\t\t\t<!--#ifdef MP-WEIXIN || MP-QQ || APP-PLUS-->\r\n\t\t\t<rich-text v-else-if=\"n.decode\" class=\"_entity\" :nodes=\"[n]\"></rich-text>\r\n\t\t\t<!--#endif-->\r\n\t\t\t<text v-else-if=\"n.type=='text'\" decode>{{n.text}}</text>\r\n\t\t\t<text v-else-if=\"n.name=='br'\">\\n</text>\r\n\t\t\t<!--视频-->\r\n\t\t\t<view v-else-if=\"n.name=='video'\">\r\n\t\t\t\t<view v-if=\"(!loadVideo||n.lazyLoad)&&!(controls[n.attrs.id]&&controls[n.attrs.id].play)\" :id=\"n.attrs.id\" :class=\"'_video '+(n.attrs.class||'')\"\r\n\t\t\t\t :style=\"n.attrs.style\" @tap=\"_loadVideo\" />\r\n\t\t\t\t<video v-else :id=\"n.attrs.id\" :class=\"n.attrs.class\" :style=\"n.attrs.style\" :autoplay=\"n.attrs.autoplay||(controls[n.attrs.id]&&controls[n.attrs.id].play)\"\r\n\t\t\t\t :controls=\"n.attrs.controls\" :loop=\"n.attrs.loop\" :muted=\"n.attrs.muted\" :poster=\"n.attrs.poster\" :src=\"n.attrs.source[(controls[n.attrs.id]&&controls[n.attrs.id].index)||0]\"\r\n\t\t\t\t :unit-id=\"n.attrs['unit-id']\" :data-id=\"n.attrs.id\" data-from=\"video\" data-source=\"source\" @error=\"error\" @play=\"play\" />\r\n\t\t\t</view>\r\n\t\t\t<!--音频-->\r\n\t\t\t<audio v-else-if=\"n.name=='audio'\" :class=\"n.attrs.class\" :style=\"n.attrs.style\" :author=\"n.attrs.author\" :autoplay=\"n.attrs.autoplay\"\r\n\t\t\t :controls=\"n.attrs.controls\" :loop=\"n.attrs.loop\" :name=\"n.attrs.name\" :poster=\"n.attrs.poster\" :src=\"n.attrs.source[(controls[n.attrs.id]&&controls[n.attrs.id].index)||0]\"\r\n\t\t\t :data-id=\"n.attrs.id\" data-from=\"audio\" data-source=\"source\" @error=\"error\" @play=\"play\" />\r\n\t\t\t<!--链接-->\r\n\t\t\t<view v-else-if=\"n.name=='a'\" :class=\"'_a '+(n.attrs.class||'')\" hover-class=\"_hover\" :style=\"n.attrs.style\"\r\n\t\t\t :data-attrs=\"n.attrs\" @tap=\"linkpress\">\r\n\t\t\t\t<trees class=\"_span\" :nodes=\"n.children\" />\r\n\t\t\t</view>\r\n\t\t\t<!--广告（按需打开注释）-->\r\n\t\t\t<!--#ifdef MP-WEIXIN || MP-QQ || MP-TOUTIAO-->\r\n\t\t\t<!--<ad v-else-if=\"n.name=='ad'\" :class=\"n.attrs.class\" :style=\"n.attrs.style\" :unit-id=\"n.attrs['unit-id']\"\r\n\t\t\t data-from=\"ad\" @error=\"error\" />-->\r\n\t\t\t<!--#endif-->\r\n\t\t\t<!--#ifdef MP-BAIDU-->\r\n\t\t\t<!--<ad v-else-if=\"n.name=='ad'\" :class=\"n.attrs.class\" :style=\"n.attrs.style\" :appid=\"n.attrs.appid\"\r\n\t\t\t :apid=\"n.attrs.apid\" :type=\"n.attrs.type\" data-from=\"ad\" @error=\"error\" />-->\r\n\t\t\t<!--#endif-->\r\n\t\t\t<!--#ifdef APP-PLUS-->\r\n\t\t\t<!--<ad v-else-if=\"n.name=='ad'\" :class=\"n.attrs.class\" :style=\"n.attrs.style\" :adpid=\"n.attrs.adpid\"\r\n\t\t\t data-from=\"ad\" @error=\"error\" />-->\r\n\t\t\t<!--#endif-->\r\n\t\t\t<!--列表-->\r\n\t\t\t<view v-else-if=\"n.name=='li'\" :id=\"n.attrs.id\" :class=\"n.attrs.class\" :style=\"(n.attrs.style||'')+';display:flex'\">\r\n\t\t\t\t<view v-if=\"n.type=='ol'\" class=\"_ol-bef\">{{n.num}}</view>\r\n\t\t\t\t<view v-else class=\"_ul-bef\">\r\n\t\t\t\t\t<view v-if=\"n.floor%3==0\" class=\"_ul-p1\">█</view>\r\n\t\t\t\t\t<view v-else-if=\"n.floor%3==2\" class=\"_ul-p2\" />\r\n\t\t\t\t\t<view v-else class=\"_ul-p1\" style=\"border-radius:50%\">█</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!--#ifdef MP-ALIPAY-->\r\n\t\t\t\t<view class=\"_li\">\r\n\t\t\t\t\t<trees :nodes=\"n.children\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t<!--#endif-->\r\n\t\t\t\t<!--#ifndef MP-ALIPAY-->\r\n\t\t\t\t<trees class=\"_li\" :nodes=\"n.children\" :lazyLoad=\"lazyLoad\" :loadVideo=\"loadVideo\" />\r\n\t\t\t\t<!--#endif-->\r\n\t\t\t</view>\r\n\t\t\t<!--表格-->\r\n\t\t\t<view v-else-if=\"n.name=='table'&&n.c\" :id=\"n.attrs.id\" :class=\"n.attrs.class\" :style=\"(n.attrs.style||'')+';display:table'\">\r\n\t\t\t\t<view v-for=\"(tbody, i) in n.children\" v-bind:key=\"i\" :class=\"tbody.attrs.class\" :style=\"(tbody.attrs.style||'')+(tbody.name[0]=='t'?';display:table-'+(tbody.name=='tr'?'row':'row-group'):'')\">\r\n\t\t\t\t\t<view v-for=\"(tr, j) in tbody.children\" v-bind:key=\"j\" :class=\"tr.attrs.class\" :style=\"(tr.attrs.style||'')+(tr.name[0]=='t'?';display:table-'+(tr.name=='tr'?'row':'cell'):'')\">\r\n\t\t\t\t\t\t<trees v-if=\"tr.name=='td'\" :nodes=\"tr.children\" :lazyLoad=\"lazyLoad\" :loadVideo=\"loadVideo\" />\r\n\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t<!--#ifdef MP-ALIPAY-->\r\n\t\t\t\t\t\t\t<view v-for=\"(td, k) in tr.children\" v-bind:key=\"k\" :class=\"td.attrs.class\" :style=\"(td.attrs.style||'')+(td.name[0]=='t'?';display:table-'+(td.name=='tr'?'row':'cell'):'')\">\r\n\t\t\t\t\t\t\t\t<trees :nodes=\"td.children\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<!--#endif-->\r\n\t\t\t\t\t\t\t<!--#ifndef MP-ALIPAY-->\r\n\t\t\t\t\t\t\t<trees v-for=\"(td, k) in tr.children\" v-bind:key=\"k\" :class=\"td.attrs.class\" :style=\"(td.attrs.style||'')+(td.name[0]=='t'?';display:table-'+(td.name=='tr'?'row':'cell'):'')\"\r\n\t\t\t\t\t\t\t :nodes=\"td.children\" :lazyLoad=\"lazyLoad\" :loadVideo=\"loadVideo\" />\r\n\t\t\t\t\t\t\t<!--#endif-->\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!--#ifdef APP-PLUS-->\r\n\t\t\t<iframe v-else-if=\"n.name=='iframe'\" :style=\"n.attrs.style\" :allowfullscreen=\"n.attrs.allowfullscreen\" :frameborder=\"n.attrs.frameborder\"\r\n\t\t\t :width=\"n.attrs.width\" :height=\"n.attrs.height\" :src=\"n.attrs.src\" />\r\n\t\t\t<embed v-else-if=\"n.name=='embed'\" :style=\"n.attrs.style\" :width=\"n.attrs.width\" :height=\"n.attrs.height\" :src=\"n.attrs.src\" />\r\n\t\t\t<!--#endif-->\r\n\t\t\t<!--富文本-->\r\n\t\t\t<!--#ifdef MP-WEIXIN || MP-QQ || MP-ALIPAY || APP-PLUS-->\r\n\t\t\t<rich-text v-else-if=\"handler.useRichText(n)\" :id=\"n.attrs.id\" :class=\"'_p __'+n.name\" :nodes=\"[n]\" />\r\n\t\t\t<!--#endif-->\r\n\t\t\t<!--#ifdef MP-BAIDU || MP-TOUTIAO-->\r\n\t\t\t<rich-text v-else-if=\"!(n.c||n.continue)\" :id=\"n.attrs.id\" :class=\"_p\" :style=\"n.attrs.contain\" :nodes=\"[n]\" />\r\n\t\t\t<!--#endif-->\r\n\t\t\t<!--#ifdef MP-ALIPAY-->\r\n\t\t\t<view v-else :id=\"n.attrs.id\" :class=\"'_'+n.name+' '+(n.attrs.class||'')\" :style=\"n.attrs.style\">\r\n\t\t\t\t<trees :nodes=\"n.children\" />\r\n\t\t\t</view>\r\n\t\t\t<!--#endif-->\r\n\t\t\t<!--#ifndef MP-ALIPAY-->\r\n\t\t\t<trees v-else :class=\"(n.attrs.id||'')+' _'+n.name+' '+(n.attrs.class||'')\" :style=\"n.attrs.style\" :nodes=\"n.children\"\r\n\t\t\t :lazyLoad=\"lazyLoad\" :loadVideo=\"loadVideo\" />\r\n\t\t\t<!--#endif-->\r\n\t\t</block>\r\n\t</view>\r\n</template>\r\n<script module=\"handler\" lang=\"wxs\" src=\"./handler.wxs\"></script>\r\n<script module=\"handler\" lang=\"sjs\" src=\"./handler.sjs\"></script>\r\n<script>\r\n\tglobal.Parser = {};\r\n\timport trees from './trees'\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\ttrees\r\n\t\t},\r\n\t\tname: 'trees',\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tcontrols: {},\r\n\t\t\t\t// #ifdef MP-WEIXIN || MP-QQ || APP-PLUS\r\n\t\t\t\timgLoad: false,\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef APP-PLUS\r\n\t\t\t\tloadVideo: true\r\n\t\t\t\t// #endif\r\n\t\t\t}\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tnodes: Array,\r\n\t\t\t// #ifdef MP-WEIXIN || MP-QQ || H5 || APP-PLUS\r\n\t\t\tlazyLoad: Boolean,\r\n\t\t\t// #endif\r\n\t\t\t// #ifdef APP-PLUS\r\n\t\t\tloadVideo: Boolean\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\t// 获取顶层组件\r\n\t\t\tthis.top = this.$parent;\r\n\t\t\twhile (this.top.$options.name != 'parser') {\r\n\t\t\t\tif (this.top.top) {\r\n\t\t\t\t\tthis.top = this.top.top;\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t\tthis.top = this.top.$parent;\r\n\t\t\t}\r\n\t\t},\r\n\t\t// #ifdef MP-WEIXIN || MP-QQ || APP-PLUS\r\n\t\tbeforeDestroy() {\r\n\t\t\tif (this.observer)\r\n\t\t\t\tthis.observer.disconnect();\r\n\t\t},\r\n\t\t// #endif\r\n\t\tmethods: {\r\n\t\t\t// #ifndef MP-ALIPAY\r\n\t\t\tplay(e) {\r\n\t\t\t\tif (this.top.videoContexts.length > 1 && this.top.autopause)\r\n\t\t\t\t\tfor (var i = this.top.videoContexts.length; i--;)\r\n\t\t\t\t\t\tif (this.top.videoContexts[i].id != e.currentTarget.dataset.id)\r\n\t\t\t\t\t\t\tthis.top.videoContexts[i].pause();\r\n\t\t\t},\r\n\t\t\t// #endif\r\n\t\t\timgtap(e) {\r\n\t\t\t\tvar attrs = e.currentTarget.dataset.attrs;\r\n\t\t\t\tif (!attrs.ignore) {\r\n\t\t\t\t\tvar preview = true, data = {\r\n\t\t\t\t\t\tid: e.target.id,\r\n\t\t\t\t\t\tsrc: attrs.src,\r\n\t\t\t\t\t\tignore: () => preview = false\r\n\t\t\t\t\t};\r\n\t\t\t\t\tglobal.Parser.onImgtap && global.Parser.onImgtap(data);\r\n\t\t\t\t\tthis.top.$emit('imgtap', data);\r\n\t\t\t\t\tif (preview) {\r\n\t\t\t\t\t\tvar urls = this.top.imgList,\r\n\t\t\t\t\t\t\tcurrent = urls[attrs.i] ? parseInt(attrs.i) : (urls = [attrs.src], 0);\r\n\t\t\t\t\t\tuni.previewImage({\r\n\t\t\t\t\t\t\tcurrent,\r\n\t\t\t\t\t\t\turls\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\timglongtap(e) {\r\n\t\t\t\tvar attrs = e.item.dataset.attrs;\r\n\t\t\t\tif (!attrs.ignore)\r\n\t\t\t\t\tthis.top.$emit('imglongtap', {\r\n\t\t\t\t\t\tid: e.target.id,\r\n\t\t\t\t\t\tsrc: attrs.src\r\n\t\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tlinkpress(e) {\r\n\t\t\t\tvar jump = true,\r\n\t\t\t\t\tattrs = e.currentTarget.dataset.attrs;\r\n\t\t\t\tattrs.ignore = () => jump = false;\r\n\t\t\t\tglobal.Parser.onLinkpress && global.Parser.onLinkpress(attrs);\r\n\t\t\t\tthis.top.$emit('linkpress', attrs);\r\n\t\t\t\tif (jump) {\r\n\t\t\t\t\t// #ifdef MP\r\n\t\t\t\t\tif (attrs['app-id']) {\r\n\t\t\t\t\t\treturn uni.navigateToMiniProgram({\r\n\t\t\t\t\t\t\tappId: attrs['app-id'],\r\n\t\t\t\t\t\t\tpath: attrs.path\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\tif (attrs.href) {\r\n\t\t\t\t\t\tif (attrs.href[0] == '#') {\r\n\t\t\t\t\t\t\tif (this.top.useAnchor)\r\n\t\t\t\t\t\t\t\tthis.top.navigateTo({\r\n\t\t\t\t\t\t\t\t\tid: attrs.href.substring(1)\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t} else if (attrs.href.indexOf('http') == 0 || attrs.href.indexOf('//') == 0) {\r\n\t\t\t\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\t\t\t\tplus.runtime.openWeb(attrs.href);\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t// #ifndef APP-PLUS\r\n\t\t\t\t\t\t\tuni.setClipboardData({\r\n\t\t\t\t\t\t\t\tdata: attrs.href,\r\n\t\t\t\t\t\t\t\tsuccess: () =>\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '链接已复制'\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t} else\r\n\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\turl: attrs.href\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\terror(e) {\r\n\t\t\t\tvar context, target = e.currentTarget,\r\n\t\t\t\t\tsource = target.dataset.from;\r\n\t\t\t\tif (source == 'video' || source == 'audio') {\r\n\t\t\t\t\t// 加载其他 source\r\n\t\t\t\t\tvar index = this.controls[target.id] ? this.controls[target.id].index + 1 : 1;\r\n\t\t\t\t\tif (index < target.dataset.source.length)\r\n\t\t\t\t\t\tthis.$set(this.controls, target.id + '.index', index);\r\n\t\t\t\t\tif (source == 'video') context = uni.createVideoContext(target.id, this);\r\n\t\t\t\t}\r\n\t\t\t\tthis.top && this.top.$emit('error', {\r\n\t\t\t\t\tsource,\r\n\t\t\t\t\ttarget,\r\n\t\t\t\t\terrMsg: e.detail.errMsg,\r\n\t\t\t\t\terrCode: e.detail.errCode,\r\n\t\t\t\t\tcontext\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t_loadVideo(e) {\r\n\t\t\t\tthis.$set(this.controls, e.currentTarget.id, {\r\n\t\t\t\t\tplay: true,\r\n\t\t\t\t\tindex: 0\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t/* 在这里引入自定义样式 */\r\n\r\n\t/* 链接和图片效果 */\r\n\t._a {\r\n\t\tdisplay: inline;\r\n\t\tcolor: #366092;\r\n\t\tword-break: break-all;\r\n\t\tpadding: 1.5px 0 1.5px 0;\r\n\t}\r\n\r\n\t._hover {\r\n\t\topacity: 0.7;\r\n\t\ttext-decoration: underline;\r\n\t}\r\n\r\n\t._img {\r\n\t\tdisplay: inline-block;\r\n\t\ttext-indent: 0;\r\n\t}\r\n\r\n\t/* #ifdef MP-WEIXIN */\r\n\t:host {\r\n\t\tdisplay: inline;\r\n\t}\r\n\r\n\t/* #endif */\r\n\r\n\t/* #ifdef MP */\r\n\t.interlayer {\r\n\t\talign-content: inherit;\r\n\t\talign-items: inherit;\r\n\t\tdisplay: inherit;\r\n\t\tflex-direction: inherit;\r\n\t\tflex-wrap: inherit;\r\n\t\tjustify-content: inherit;\r\n\t\twidth: 100%;\r\n\t\twhite-space: inherit;\r\n\t}\r\n\r\n\t/* #endif */\r\n\r\n\t._b,\r\n\t._strong {\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t._blockquote,\r\n\t._div,\r\n\t._p,\r\n\t._ol,\r\n\t._ul,\r\n\t._li {\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t._code {\r\n\t\tfont-family: monospace;\r\n\t}\r\n\r\n\t._del {\r\n\t\ttext-decoration: line-through;\r\n\t}\r\n\r\n\t._em,\r\n\t._i {\r\n\t\tfont-style: italic;\r\n\t}\r\n\r\n\t._h1 {\r\n\t\tfont-size: 2em;\r\n\t}\r\n\r\n\t._h2 {\r\n\t\tfont-size: 1.5em;\r\n\t}\r\n\r\n\t._h3 {\r\n\t\tfont-size: 1.17em;\r\n\t}\r\n\r\n\t._h5 {\r\n\t\tfont-size: 0.83em;\r\n\t}\r\n\r\n\t._h6 {\r\n\t\tfont-size: 0.67em;\r\n\t}\r\n\r\n\t._h1,\r\n\t._h2,\r\n\t._h3,\r\n\t._h4,\r\n\t._h5,\r\n\t._h6 {\r\n\t\tdisplay: block;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t._ins {\r\n\t\ttext-decoration: underline;\r\n\t}\r\n\r\n\t._li {\r\n\t\tflex: 1;\r\n\t\twidth: 0;\r\n\t}\r\n\r\n\t._ol-bef {\r\n\t\tmargin-right: 5px;\r\n\t\ttext-align: right;\r\n\t\twidth: 36px;\r\n\t}\r\n\r\n\t._ul-bef {\r\n\t\tline-height: normal;\r\n\t\tmargin: 0 12px 0 23px;\r\n\t}\r\n\r\n\t._ol-bef,\r\n\t._ul_bef {\r\n\t\tflex: none;\r\n\t\tuser-select: none;\r\n\t}\r\n\r\n\t._ul-p1 {\r\n\t\tdisplay: inline-block;\r\n\t\theight: 0.3em;\r\n\t\tline-height: 0.3em;\r\n\t\toverflow: hidden;\r\n\t\twidth: 0.3em;\r\n\t}\r\n\r\n\t._ul-p2 {\r\n\t\tborder: 0.05em solid black;\r\n\t\tborder-radius: 50%;\r\n\t\tdisplay: inline-block;\r\n\t\theight: 0.23em;\r\n\t\twidth: 0.23em;\r\n\t}\r\n\r\n\t._q::before {\r\n\t\tcontent: '\"';\r\n\t}\r\n\r\n\t._q::after {\r\n\t\tcontent: '\"';\r\n\t}\r\n\r\n\t._sub {\r\n\t\tfont-size: smaller;\r\n\t\tvertical-align: sub;\r\n\t}\r\n\r\n\t._sup {\r\n\t\tfont-size: smaller;\r\n\t\tvertical-align: super;\r\n\t}\r\n\r\n\t/* #ifndef MP-WEIXIN */\r\n\t._abbr,\r\n\t._b,\r\n\t._code,\r\n\t._del,\r\n\t._em,\r\n\t._i,\r\n\t._ins,\r\n\t._label,\r\n\t._q,\r\n\t._span,\r\n\t._strong,\r\n\t._sub,\r\n\t._sup {\r\n\t\tdisplay: inline;\r\n\t}\r\n\r\n\t/* #endif */\r\n\r\n\t/* #ifdef MP-WEIXIN || MP-QQ || MP-ALIPAY */\r\n\t.__bdo,\r\n\t.__bdi,\r\n\t.__ruby,\r\n\t.__rt,\r\n\t._entity {\r\n\t\tdisplay: inline-block;\r\n\t}\r\n\r\n\t/* #endif */\r\n\t._video {\r\n\t\tbackground-color: black;\r\n\t\tdisplay: inline-block;\r\n\t\theight: 225px;\r\n\t\tposition: relative;\r\n\t\twidth: 300px;\r\n\t}\r\n\r\n\t._video::after {\r\n\t\tborder-color: transparent transparent transparent white;\r\n\t\tborder-style: solid;\r\n\t\tborder-width: 15px 0 15px 30px;\r\n\t\tcontent: '';\r\n\t\tleft: 50%;\r\n\t\tmargin: -15px 0 0 -15px;\r\n\t\tposition: absolute;\r\n\t\ttop: 50%;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./trees.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./trees.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363903277\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-filter-loader/index.js!./handler.wxs?vue&type=custom&index=0&blockType=script&issuerPath=C%3A%5CUsers%5Cchenjiayin%5CDesktop%5Ccode%5Ccrystal-mall%5Ccrystal%5Capp%5Ccomponents%5Cjyf-parser%5Clibs%5Ctrees.vue&module=handler&lang=wxs\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-filter-loader/index.js!./handler.wxs?vue&type=custom&index=0&blockType=script&issuerPath=C%3A%5CUsers%5Cchenjiayin%5CDesktop%5Ccode%5Ccrystal-mall%5Ccrystal%5Capp%5Ccomponents%5Cjyf-parser%5Clibs%5Ctrees.vue&module=handler&lang=wxs\"", "export default function (Component) {\n       if(!Component.options.wxsCallMethods){\n         Component.options.wxsCallMethods = []\n       }\n       \n     }"], "sourceRoot": ""}