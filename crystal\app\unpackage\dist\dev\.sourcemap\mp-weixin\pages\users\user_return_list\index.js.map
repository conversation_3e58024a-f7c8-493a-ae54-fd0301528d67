{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/user_return_list/index.vue?2389", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/user_return_list/index.vue?5afc", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/user_return_list/index.vue?8c9a", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/user_return_list/index.vue?2b31", "uni-app:///pages/users/user_return_list/index.vue", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/user_return_list/index.vue?0696", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/user_return_list/index.vue?beae"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "emptyPage", "home", "authorize", "data", "loading", "loadend", "loadTitle", "orderList", "orderStatus", "page", "limit", "isAuto", "isShowAuth", "computed", "watch", "is<PERSON>ogin", "handler", "deep", "onLoad", "onReachBottom", "methods", "onLoadFun", "auth<PERSON><PERSON><PERSON>", "goOrderDetails", "title", "uni", "url", "getOrderList", "that", "type"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACmM;AACnM,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrBA;AAAA;AAAA;AAAA;AAAkwB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACwCtxB;AAGA;AAGA;AAEA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAIA;EACAC;IACAC;IACAC;IAEAC;EAEA;EACAC;IACA;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;EACAC;IACAC;MACAC;QACA;UACA;QACA;MACA;MACAC;IACA;EACA;EACAC;IACA;MACA;IACA;MACA;IACA;EACA;EACA;AACA;AACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;QACAC;MACA;MACAC;QACAC;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;MACA;MACA;MACAC;MACAA;MACA;QACAC;QACApB;QACAC;MACA;QACA;QACA;QACAkB;QACAA;QACAA;QACAA;QACAA;QACAA;MACA;QACAA;QACAA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjJA;AAAA;AAAA;AAAA;AAAq8C,CAAgB,ovCAAG,EAAC,C;;;;;;;;;;;ACAz9C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/users/user_return_list/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/users/user_return_list/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=1a17ef08&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=1a17ef08&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1a17ef08\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/users/user_return_list/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=1a17ef08&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.orderList.length\n  var g1 = _vm.orderList.length\n  var g2 = _vm.orderList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view class='return-list pad30' v-if=\"orderList.length\">\r\n\t\t\t<view class='goodWrapper borRadius14' v-for=\"(item,index) in orderList\" :key=\"index\" @click='goOrderDetails(item.orderId)'>\r\n\t\t\t\t<view class='iconfont icon-tuikuanzhong powder' v-if=\"item.refundStatus==1 || item.refundStatus==3\"></view>\r\n\t\t\t\t<view class='iconfont icon-yituikuan' v-if=\"item.refundStatus==2\"></view>\r\n\t\t\t\t<view class='orderNum'>订单号：{{item.orderId}}</view>\r\n\t\t\t\t<view class='item acea-row row-between-wrapper' v-for=\"(items,index) in item.orderInfoList\" :key=\"index\">\r\n\t\t\t\t\t<view class='pictrue'>\r\n\t\t\t\t\t\t<image :src='items.image'></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='text'>\r\n\t\t\t\t\t\t<view class='acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t\t<view class='name line1'>{{items.storeName}}</view>\r\n\t\t\t\t\t\t\t<view class='num'>x {{items.cartNum}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='attr line1' v-if=\"items.suk\">{{items.suk}}</view>\r\n\t\t\t\t\t\t<view class='attr line1' v-else>{{items.storeName}}</view>\r\n\t\t\t\t\t\t<view class='money'>￥{{items.price}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='totalSum'>共{{item.totalNum || 0}}件商品，总金额 <text class='font-color price'>￥{{item.payPrice}}</text></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class='loadingicon acea-row row-center-wrapper' v-if=\"orderList.length\">\r\n\t\t\t<text class='loading iconfont icon-jiazai' :hidden='loading==false'></text>{{loadTitle}}\r\n\t\t</view>\r\n\t\t<view v-if=\"orderList.length == 0\">\r\n\t\t\t<emptyPage title=\"暂无订单~\"></emptyPage>\r\n\t\t</view>\r\n\t\t<!-- #ifdef MP -->\r\n\t\t<!-- <authorize @onLoadFun=\"onLoadFun\" :isAuto=\"isAuto\" :isShowAuth=\"isShowAuth\" @authColse=\"authColse\"></authorize> -->\r\n\t\t<!-- #endif -->\r\n\t\t<home></home>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport home from '@/components/home';\r\n\timport emptyPage from '@/components/emptyPage.vue'\r\n\timport {\r\n\t\tgetOrderList\r\n\t} from '@/api/order.js';\r\n\timport {\r\n\t\ttoLogin\r\n\t} from '@/libs/login.js';\r\n\timport {\r\n\t\tmapGetters\r\n\t} from \"vuex\";\r\n\t// #ifdef MP\r\n\timport authorize from '@/components/Authorize';\r\n\t// #endif\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\temptyPage,\r\n\t\t\thome,\r\n\t\t\t// #ifdef MP\r\n\t\t\tauthorize\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tloading: false,\r\n\t\t\t\tloadend: false,\r\n\t\t\t\tloadTitle: '加载更多', //提示语\r\n\t\t\t\torderList: [], //订单数组\r\n\t\t\t\torderStatus: -3, //订单状态\r\n\t\t\t\tpage: 1,\r\n\t\t\t\tlimit: 20,\r\n\t\t\t\tisAuto: false, //没有授权的不会自动授权\r\n\t\t\t\tisShowAuth: false //是否隐藏授权\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: mapGetters(['isLogin']),\r\n\t\twatch:{\r\n\t\t\tisLogin:{\r\n\t\t\t\thandler:function(newV,oldV){\r\n\t\t\t\t\tif(newV){\r\n\t\t\t\t\t\tthis.getOrderList();\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tdeep:true\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tif (this.isLogin) {\r\n\t\t\t\tthis.getOrderList();\r\n\t\t\t} else {\r\n\t\t\t\ttoLogin();\r\n\t\t\t}\r\n\t\t},\r\n\t\t/**\r\n\t\t * 页面上拉触底事件的处理函数\r\n\t\t */\r\n\t\tonReachBottom: function() {\r\n\t\t\tthis.getOrderList();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tonLoadFun() {\r\n\t\t\t\tthis.getOrderList();\r\n\t\t\t},\r\n\t\t\t// 授权关闭\r\n\t\t\tauthColse: function(e) {\r\n\t\t\t\tthis.isShowAuth = e\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 去订单详情\r\n\t\t\t */\r\n\t\t\tgoOrderDetails: function(order_id) {\r\n\t\t\t\tif (!order_id) return that.$util.Tips({\r\n\t\t\t\t\ttitle: '缺少订单号无法查看订单详情'\r\n\t\t\t\t});\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/order_details/index?order_id=' + order_id + '&isReturen=1'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 获取订单列表\r\n\t\t\t */\r\n\t\t\tgetOrderList: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif (that.loadend) return;\r\n\t\t\t\tif (that.loading) return;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tthat.loadTitle = \"\";\r\n\t\t\t\tgetOrderList({\r\n\t\t\t\t\ttype: that.orderStatus,\r\n\t\t\t\t\tpage: that.page,\r\n\t\t\t\t\tlimit: that.limit,\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tlet list = res.data.list || [];\r\n\t\t\t\t\tlet loadend = list.length < that.limit;\r\n\t\t\t\t\tthat.orderList = that.$util.SplitArray(list, that.orderList);\r\n\t\t\t\t\tthat.$set(that,'orderList',that.orderList);\r\n\t\t\t\t\tthat.loadend = loadend;\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tthat.loadTitle = loadend ? \"我也是有底线的\" : '加载更多';\r\n\t\t\t\t\tthat.page = that.page + 1;\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tthat.loadTitle = \"加载更多\";\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.return-list .goodWrapper {\r\n\t\tbackground-color: #fff;\r\n\t\tmargin-top: 20rpx;\r\n\t\tposition: relative;\r\n\t\tpadding: 0rpx 24rpx;\r\n\t}\r\n\r\n\t.return-list .goodWrapper .orderNum {\r\n\t\tborder-bottom: 1px solid #eee;\r\n\t\theight: 87rpx;\r\n\t\tline-height: 87rpx;\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\t.return-list .goodWrapper .item {\r\n\t\tborder-bottom: 0;\r\n\t}\r\n\r\n\t.return-list .goodWrapper .totalSum {\r\n\t\tpadding: 0 30rpx 32rpx 30rpx;\r\n\t\ttext-align: right;\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #282828;\r\n\t}\r\n\r\n\t.return-list .goodWrapper .totalSum .price {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.return-list .goodWrapper .iconfont {\r\n\t\tposition: absolute;\r\n\t\tfont-size: 109rpx;\r\n\t\ttop: 7rpx;\r\n\t\tright: 22rpx;\r\n\t\tcolor: #ccc;\r\n\t}\r\n\r\n\t.return-list .goodWrapper .iconfont.powder {\r\n\t\tcolor: #f8c1bd;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=1a17ef08&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=1a17ef08&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363903502\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}