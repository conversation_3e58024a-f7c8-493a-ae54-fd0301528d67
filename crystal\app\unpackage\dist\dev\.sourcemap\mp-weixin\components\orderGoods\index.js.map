{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/orderGoods/index.vue?c9da", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/orderGoods/index.vue?1f0c", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/orderGoods/index.vue?8551", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/orderGoods/index.vue?a5b8", "uni-app:///components/orderGoods/index.vue", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/orderGoods/index.vue?9905", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/orderGoods/index.vue?4d33"], "names": ["props", "evaluate", "type", "default", "cartInfo", "orderId", "ids", "jump", "orderProNum", "productType", "data", "radius", "totalNmu", "watch", "nVal", "num", "sum", "methods", "evaluateTap", "uni", "url", "jumpCon", "getMarbleStyle", "angleOffset", "position", "left", "top", "width", "height", "transform", "background", "backgroundSize", "transition"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACgM;AAChM,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3BA;AAAA;AAAA;AAAA;AAAmvB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBC0CvwB;EACAA;IACAC;MACAC;MACAC;IACA;IACAD;MACAA;MACAC;IACA;IACAC;MACAF;MACAC;QACA;MACA;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;QACA;MACA;IACA;IACAM;MACAP;MACAC;QACA;MACA;IACA;EACA;EACAO;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACAT;MACA;MACAU;QACAC;MACA;MACA;MAEA;QAAA,OACAC;MAAA;MAEA;MACA;;MAEA;MACA;IACA;EACA;EACAC;IACAC;MACAC;QACAC;MACA;IACA;IACAC;MACA;MACA;QACAF;UACAC;QACA;MACA;IACA;IAEAE;MACA;MACA;QAAA,OACAN;MAAA;;MAEA;MACA;MACA;;MAEA;MACA;;MAEA;MACA;MACA;QACA;QACAO;MACA;MAEA;MACA;;MAEA;MACA;MACA;MAEA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;ACjKA;AAAA;AAAA;AAAA;AAA06C,CAAgB,ovCAAG,EAAC,C;;;;;;;;;;;ACA97C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/orderGoods/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=0d23a466&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=0d23a466&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0d23a466\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/orderGoods/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=0d23a466&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 =\n    _vm.type == 2\n      ? _vm.__map(_vm.cartInfo, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var s0 = _vm.__get_style([_vm.getMarbleStyle(index)])\n          return {\n            $orig: $orig,\n            s0: s0,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"orderGoods borRadius14\">\r\n\t\t<view class='total' v-if=\"type == 2\">共1条手链</view>\r\n\t\t<view class='total' v-else>共{{ orderProNum ? orderProNum : totalNmu }}件商品</view>\r\n\t\t<view class='goodWrapper pad30'>\r\n\t\t\t<view v-if=\"type == 2\">\r\n\t\t\t\t\r\n\t\t\t\t<div\r\n\t\t\t\t\tstyle=\"display: flex;justify-content: center;align-items: center;height: 500rpx;position: relative;\">\r\n\t\t\t\t\t<div class=\"circle-container\" :style=\"{ width: radius * 2 + 'rpx', height: radius * 2 + 'rpx' }\">\r\n\t\t\t\t\t\t<div v-for=\"(item, index) in cartInfo\" :key=\"index\" class=\"marble\"\r\n\t\t\t\t\t\t\t:style=\"[getMarbleStyle(index)]\">\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</view>\r\n\t\t\t<view v-else>\r\n\t\t\t\t<view class='item acea-row row-between-wrapper' v-for=\"(item, index) in cartInfo\" :key=\"index\"\r\n\t\t\t\t\t@click=\"jumpCon(item.productId)\">\r\n\t\t\t\t\t<view class='pictrue'>\r\n\t\t\t\t\t\t<image :src='item.image'></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='text'>\r\n\t\t\t\t\t\t<view class='acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t\t<view class='name line1'>{{ item.productName ? item.productName : item.storeName }}</view>\r\n\t\t\t\t\t\t\t<view class='num'>x {{ item.payNum ? item.payNum : item.cartNum }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='attr line1' v-if=\"item.sku\">{{ item.sku }}</view>\r\n\t\t\t\t\t\t<view class='money font-color'>￥{{ item.price }}</view>\r\n\t\t\t\t\t\t<view class='evaluate' v-if='item.isReply == 0 && evaluate == 2'\r\n\t\t\t\t\t\t\************=\"evaluateTap(item)\">评价\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='evaluate' v-else-if=\"item.isReply == 1\">已评价</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n\tprops: {\r\n\t\tevaluate: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 0,\r\n\t\t},\r\n\t\ttype: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 0,\r\n\t\t},\r\n\t\tcartInfo: {\r\n\t\t\ttype: Array,\r\n\t\t\tdefault: function () {\r\n\t\t\t\treturn [];\r\n\t\t\t}\r\n\t\t},\r\n\t\torderId: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: '',\r\n\t\t},\r\n\t\tids: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 0,\r\n\t\t},\r\n\t\tjump: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false,\r\n\t\t},\r\n\t\torderProNum: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: function () {\r\n\t\t\t\treturn 0;\r\n\t\t\t}\r\n\t\t},\r\n\t\tproductType: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: function () {\r\n\t\t\t\treturn 0;\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tradius : 0,\r\n\t\t\ttotalNmu: ''\r\n\t\t};\r\n\t},\r\n\twatch: {\r\n\t\tcartInfo: function (nVal, oVal) {\r\n\t\t\tlet num = 0\r\n\t\t\tnVal.forEach((item, index) => {\r\n\t\t\t\tnum += item.cartNum\r\n\t\t\t})\r\n\t\t\tthis.totalNmu = num\r\n\t\t\t\r\n\t\t\tconst totalBeadsWidth = this.cartInfo.reduce((sum, bead) =>\r\n\t\t\t\tsum + (parseFloat(bead.width) * 3), 0);\r\n\r\n\t\t\tconst spacing = (totalBeadsWidth / this.cartInfo.length) * 0.05;\r\n\t\t\tconst totalCircumference = totalBeadsWidth + (spacing * this.cartInfo.length);\r\n\r\n\t\t\t// Set radius to match circle border\r\n\t\t\tthis.radius = totalCircumference / (2 * Math.PI);\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\tevaluateTap(item) {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: \"/pages/users/goods_comment_con/index?unique=\" + item.attrId + \"&orderId=\" + this.orderId + '&id=' + this.ids\r\n\t\t\t})\r\n\t\t},\r\n\t\tjumpCon: function (id) {\r\n\t\t\tlet type = this.productType == 0 ? 'normal' : 'video'\r\n\t\t\tif (this.jump) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/goods_details/index?id=${id}&type=${type}`\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\tgetMarbleStyle(index) {\r\n\t\t\t// Calculate total circumference based on bead sizes\r\n\t\t\tconst totalBeadsWidth = this.cartInfo.reduce((sum, bead) =>\r\n\t\t\t\tsum + (parseFloat(bead.width) * 3), 0);\r\n\r\n\t\t\t// Add small gaps between beads (5% of average bead size)\r\n\t\t\tconst spacing = (totalBeadsWidth / this.cartInfo.length) * 0.05;\r\n\t\t\tconst circumference = totalBeadsWidth + (spacing * this.cartInfo.length);\r\n\r\n\t\t\t// Calculate radius to match circle border\r\n\t\t\tconst dynamicRadius = circumference / (2 * Math.PI);\r\n\r\n\t\t\t// Calculate position on circle for current bead\r\n\t\t\tlet angleOffset = 0;\r\n\t\t\tfor (let i = 0; i < index; i++) {\r\n\t\t\t\tconst prevBeadWidth = parseFloat(this.cartInfo[i].width) * 3;\r\n\t\t\t\tangleOffset += (prevBeadWidth + spacing) / dynamicRadius;\r\n\t\t\t}\r\n\r\n\t\t\tconst currentBeadWidth = parseFloat(this.cartInfo[index].width) * 3;\r\n\t\t\tconst angle = angleOffset + (currentBeadWidth / 2) / dynamicRadius;\r\n\r\n\t\t\t// Position bead exactly on circle circumference\r\n\t\t\tconst x = dynamicRadius * (1 + Math.cos(angle));\r\n\t\t\tconst y = dynamicRadius * (1 + Math.sin(angle));\r\n\r\n\t\t\treturn {\r\n\t\t\t\tposition: 'absolute',\r\n\t\t\t\tleft: (x - currentBeadWidth / 2) + 'rpx',\r\n\t\t\t\ttop: (y - (parseFloat(this.cartInfo[index].height) * 3 / 2)) + 'rpx',\r\n\t\t\t\twidth: currentBeadWidth + 'rpx',\r\n\t\t\t\theight: (parseFloat(this.cartInfo[index].height) * 3) + 'rpx',\r\n\t\t\t\ttransform: `rotate(${angle + Math.PI / 2}rad)`,\r\n\t\t\t\tbackground: `url('${this.cartInfo[index].image}') no-repeat center`,\r\n\t\t\t\tbackgroundSize: 'cover',\r\n\t\t\t\ttransition: 'all 0.3s ease'\r\n\t\t\t};\r\n\t\t},\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.orderGoods {\r\n\tbackground-color: #fff;\r\n\tmargin-top: 15rpx;\r\n}\r\n\r\n.orderGoods .total {\r\n\twidth: 100%;\r\n\theight: 86rpx;\r\n\tpadding: 0 24rpx;\r\n\tborder-bottom: 2rpx solid #f0f0f0;\r\n\tfont-size: 30rpx;\r\n\tcolor: #282828;\r\n\tline-height: 86rpx;\r\n\tbox-sizing: border-box;\r\n}\r\n\r\n.pictrue image {\r\n\tbackground: #f4f4f4;\r\n}\r\n.circle-container {\r\n\tposition: relative;\r\n\tborder-radius: 50%;\r\n\tborder: gray 1px solid;\r\n\ttransition: all 1s;\r\n}\r\n\r\n.marble {\r\n\tposition: absolute;\r\n\ttransition: all 1s;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=0d23a466&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=0d23a466&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363904150\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}