{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\FormGenerator\\index\\IconsDialog.vue?vue&type=style&index=0&id=585439f6&lang=scss&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\FormGenerator\\index\\IconsDialog.vue", "mtime": 1753666157770}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\css-loader\\index.js", "mtime": 1753666298053}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753666301105}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753666299466}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1753666297707}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\r\n.icon-ul {\r\n  margin: 0;\r\n  padding: 0;\r\n  font-size: 0;\r\n  li {\r\n    list-style-type: none;\r\n    text-align: center;\r\n    font-size: 14px;\r\n    display: inline-block;\r\n    width: 16.66%;\r\n    box-sizing: border-box;\r\n    height: 108px;\r\n    padding: 15px 6px 6px 6px;\r\n    cursor: pointer;\r\n    overflow: hidden;\r\n    &:hover {\r\n      background: #f2f2f2;\r\n    }\r\n    &.active-item{\r\n      background: #e1f3fb;\r\n      color: #7a6df0\r\n    }\r\n    > i {\r\n      font-size: 30px;\r\n      line-height: 50px;\r\n    }\r\n  }\r\n}\r\n.icon-dialog {\r\n  ::v-deep .el-dialog {\r\n    border-radius: 8px;\r\n    margin-bottom: 0;\r\n    margin-top: 4vh !important;\r\n    display: flex;\r\n    flex-direction: column;\r\n    max-height: 92vh;\r\n    overflow: hidden;\r\n    box-sizing: border-box;\r\n    .el-dialog__header {\r\n      padding-top: 14px;\r\n    }\r\n    .el-dialog__body {\r\n      margin: 0 20px 20px 20px;\r\n      padding: 0;\r\n      overflow: auto;\r\n    }\r\n  }\r\n}\r\n", null]}