{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\marketing\\coupon\\list\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\marketing\\coupon\\list\\index.vue", "mtime": 1753666157892}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { marketingListApi, couponIssueStatusApi, couponUserListApi, couponDeleteApi } from '@/api/marketing'\nimport { roterPre } from '@/settings'\nimport { checkPermi } from \"@/utils/permission\"; // 权限判断函数\nexport default {\n  name: 'CouponList',\n  data() {\n    return {\n      Loading: false,\n      dialogVisible: false,\n      roterPre: roterPre,\n      listLoading: true,\n      tableData: {\n        data: [],\n        total: 0\n      },\n      tableFrom: {\n        page: 1,\n        limit: 20,\n        status: '',\n        name: ''\n      },\n      tableFromIssue: {\n        page: 1,\n        limit: 10,\n        couponId: ''\n      },\n      issueData: {\n        data: [],\n        total: 0\n      }\n    }\n  },\n  mounted() {\n    this.getList()\n  },\n  methods: {\n    checkPermi,\n    seachList() {\n      this.tableFrom.page = 1\n      this.getList()\n    },\n    handleClose() {\n      this.dialogVisible = false\n    },\n    // 领取记录\n    receive(row) {\n      this.dialogVisible = true\n      this.tableFromIssue.couponId = row.id\n      this.getIssueList()\n    },\n    // 列表\n    getIssueList() {\n      this.Loading = true\n      couponUserListApi(this.tableFromIssue).then(res => {\n        this.issueData.data = res.list\n        this.issueData.total = res.total\n        this.Loading = false\n      }).catch(res => {\n        this.Loading = false\n        this.$message.error(res.message)\n      })\n    },\n    pageChangeIssue(page) {\n      this.tableFromIssue.page = page\n      this.getIssueList()\n    },\n    handleSizeChangeIssue(val) {\n      this.tableFromIssue.limit = val\n      this.getIssueList()\n    },\n    // 列表\n    getList() {\n      this.listLoading = true\n      marketingListApi(this.tableFrom).then(res => {\n        this.tableData.data = res.list\n        this.tableData.total = res.total\n        this.listLoading = false\n      }).catch(res => {\n        this.listLoading = false\n      })\n    },\n    pageChange(page) {\n      this.tableFrom.page = page\n      this.getList()\n    },\n    handleSizeChange(val) {\n      this.tableFrom.limit = val\n      this.getList()\n    },\n    // 修改状态\n    onchangeIsShow(row) {\n      couponIssueStatusApi({id:row.id, status:row.status}).then(() => {\n        this.$message.success('修改成功')\n        this.getList()\n      }).catch(()=>{\n        row.status = !row.status\n      })\n    },\n    handleDelMenu(rowData) {\n      this.$confirm('确定删除当前数据?').then(() => {\n        couponDeleteApi({id: rowData.id}).then(data => {\n          this.$message.success('删除成功')\n          this.getList()\n        })\n      })\n    },\n  }\n}\n", null]}