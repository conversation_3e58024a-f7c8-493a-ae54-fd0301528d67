.navTabBox{width:100%;color:#fff}.navTabBox .click{color:#fff}.navTabBox .longTab{width:100%;padding-top:12rpx;padding-bottom:12rpx}.navTabBox .longTab .longItem{height:50rpx;display:inline-block;line-height:50rpx;text-align:center;font-size:30rpx}.navTabBox .longTab .longItem.click{font-weight:700}.navTabBox .longTab .underlineBox{height:3px;width:20%;display:flex;align-content:center;justify-content:center;transition:.5s}.navTabBox .longTab .underlineBox .underline{width:33rpx;height:4rpx;background-color:#fff}.child-box{width:100%;position:relative;background-color:#fff;box-shadow:0 2rpx 3rpx 1rpx #f9f9f9}.child-box .wrapper{display:flex;align-items:center;padding:20rpx 0;background:#fff}.child-box .child-item{flex-shrink:0;width:140rpx;display:flex;flex-direction:column;align-items:center;justify-content:center;margin-left:10rpx}.child-box .child-item image{width:90rpx;height:90rpx;border-radius:50%}.child-box .child-item .txt{font-size:24rpx;color:#282828;text-align:center;margin-top:10rpx;width:100%}.child-box .child-item.on image{border:1px solid rgba(233,51,35,.6)}.child-box .child-item.on .txt{color:#c9ab79}