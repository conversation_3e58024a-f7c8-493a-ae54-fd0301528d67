{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\grade\\creatGrade.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\grade\\creatGrade.vue", "mtime": 1753666157938}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _user = require(\"@/api/user\");\nvar _validate = require(\"@/utils/validate\");\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nvar obj = {\n  name: '',\n  grade: 1,\n  discount: '',\n  icon: '',\n  image: '',\n  id: null\n};\nvar _default = exports.default = {\n  name: \"CreatGrade\",\n  props: {\n    'user': Object\n  },\n  data: function data() {\n    return {\n      dialogVisible: false,\n      formValidate: Object.assign({}, obj),\n      loading: false,\n      rules: {\n        name: [{\n          required: true,\n          message: '请输入等级名称',\n          trigger: 'blur'\n        }],\n        grade: [{\n          required: true,\n          message: '请输入等级',\n          trigger: 'blur'\n        }, {\n          type: 'number',\n          message: '等级必须为数字值'\n        }],\n        discount: [{\n          required: true,\n          message: '请输入折扣',\n          trigger: 'blur'\n        }],\n        experience: [{\n          required: true,\n          message: '请输入经验',\n          trigger: 'blur'\n        }, {\n          type: 'number',\n          message: '经验必须为数字值'\n        }],\n        icon: [{\n          required: true,\n          message: '请上传图标',\n          trigger: 'change'\n        }],\n        image: [{\n          required: true,\n          message: '请上传用户背景',\n          trigger: 'change'\n        }]\n      }\n    };\n  },\n  methods: {\n    // 点击商品图\n    modalPicTap: function modalPicTap(tit, num) {\n      var _this = this;\n      this.$modalUpload(function (img) {\n        tit === '1' && num === 'icon' ? _this.formValidate.icon = img[0].sattDir : _this.formValidate.image = img[0].sattDir;\n        this.$set(_this.user, 'icon', _this.formValidate.icon);\n        this.$set(_this.user, 'isShow', false);\n      }, tit, 'user');\n    },\n    info: function info(id) {\n      var _this2 = this;\n      this.loading = true;\n      (0, _user.levelInfoApi)({\n        id: id\n      }).then(function (res) {\n        _this2.formValidate = res;\n        _this2.loading = false;\n      }).catch(function () {\n        _this2.loading = false;\n      });\n    },\n    handleClose: function handleClose() {\n      var _this3 = this;\n      this.$nextTick(function () {\n        _this3.$refs.user.resetFields();\n      });\n      this.dialogVisible = false;\n      // this.user = Object.assign({}, '')\n    },\n    submitForm: (0, _validate.Debounce)(function (formName) {\n      var _this4 = this;\n      this.$refs.user.validate(function (valid) {\n        if (valid) {\n          _this4.loading = true;\n          var data = {\n            discount: _this4.user.discount,\n            experience: _this4.user.experience,\n            grade: _this4.user.grade,\n            icon: _this4.user.icon,\n            id: _this4.user.id,\n            isShow: _this4.user.isShow,\n            name: _this4.user.name\n          };\n          _this4.user.id ? (0, _user.levelUpdateApi)(_this4.user.id, data).then(function (res) {\n            _this4.$message.success('编辑成功');\n            _this4.loading = false;\n            _this4.handleClose();\n            _this4.formValidate = Object.assign({}, obj);\n            _this4.$parent.getList();\n          }).catch(function () {\n            _this4.loading = false;\n          }) : (0, _user.levelSaveApi)(_this4.user).then(function (res) {\n            _this4.$message.success('添加成功');\n            _this4.loading = false;\n            _this4.handleClose();\n            _this4.formValidate = Object.assign({}, obj);\n            _this4.$parent.getList();\n          }).catch(function () {\n            _this4.loading = false;\n            _this4.formValidate = Object.assign({}, obj);\n          });\n        } else {\n          return false;\n        }\n      });\n    }),\n    resetForm: function resetForm(formName) {\n      var _this5 = this;\n      // this[formName] = {};\n      this.$nextTick(function () {\n        _this5.$refs.user.resetFields();\n      });\n      this.dialogVisible = false;\n    }\n  }\n};", null]}