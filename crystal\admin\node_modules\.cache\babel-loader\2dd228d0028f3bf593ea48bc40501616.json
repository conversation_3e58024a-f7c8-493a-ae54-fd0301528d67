{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\element-ui\\node_modules\\async-validator\\es\\validator\\object.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\element-ui\\node_modules\\async-validator\\es\\validator\\object.js", "mtime": 1753666301390}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _rule = _interopRequireDefault(require(\"../rule/\"));\nvar _util = require(\"../util\");\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\n/**\n *  Validates an object.\n *\n *  @param rule The validation rule.\n *  @param value The value of the field on the source object.\n *  @param callback The callback function.\n *  @param source The source object being validated.\n *  @param options The validation options.\n *  @param options.messages The validation messages.\n */\nfunction object(rule, value, callback, source, options) {\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if ((0, _util.isEmptyValue)(value) && !rule.required) {\n      return callback();\n    }\n    _rule.default.required(rule, value, source, errors, options);\n    if (value !== undefined) {\n      _rule.default.type(rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n}\nvar _default = exports.default = object;", null]}