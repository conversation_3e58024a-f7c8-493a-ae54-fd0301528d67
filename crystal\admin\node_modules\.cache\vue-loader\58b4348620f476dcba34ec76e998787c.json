{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\storeComment\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\storeComment\\index.vue", "mtime": 1753666157925}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport creatComment from './creatComment.vue'\r\nimport { categoryApi, replyListApi, replyDeleteApi, replyCommentApi } from '@/api/store'\r\nimport { formatDates } from '@/utils/index';\r\nimport { userList<PERSON><PERSON> } from '@/api/user'\r\nexport default {\r\n  name: 'StoreComment',\r\n  filters: {\r\n    formatDate (time) {\r\n      if (time !== 0) {\r\n        const date = new Date(time * 1000);\r\n        return formatDates(date, 'yyyy-MM-dd hh:mm');\r\n      }\r\n    }\r\n  },\r\n  components: { creatComment },\r\n  data() {\r\n    return {\r\n      merCateList: [],\r\n      props: {\r\n        children: 'child',\r\n        label: 'name',\r\n        value: 'id',\r\n        emitPath: false\r\n      },\r\n      fromList: this.$constants.fromList,\r\n      tableData: {\r\n        data: [],\r\n        total: 0\r\n      },\r\n      listLoading: true,\r\n      tableFrom: {\r\n        page: 1,\r\n        limit: 20,\r\n        isReply: '',\r\n        dateLimit: '',\r\n        // uid: '',\r\n        nickname: '',\r\n        productSearch:'',\r\n        isDel: false\r\n      },\r\n      timeVal: [],\r\n      loading: false,\r\n      uids: [],\r\n      options: [],\r\n      dialogVisible: false,\r\n      timer: ''\r\n    }\r\n  },\r\n  watch: {\r\n    $route(to, from) {\r\n      this.getList()\r\n      this.getCategorySelect()\r\n    }\r\n  },\r\n  mounted() {\r\n    // this.getLstFilterApi()\r\n    this.getList()\r\n    this.getCategorySelect()\r\n  },\r\n  methods:{\r\n    remoteMethod(query) {\r\n      if (query !== '') {\r\n        this.loading = true;\r\n        setTimeout(() => {\r\n          this.loading = false;\r\n          userListApi({keywords: query, page: 1, limit: 10}).then(res => {\r\n            this.options = res.list\r\n          })\r\n        }, 200);\r\n      } else {\r\n        this.options = [];\r\n      }\r\n    },\r\n    seachList() {\r\n      this.dialogVisible = false\r\n      this.tableFrom.page = 1\r\n      this.getList()\r\n    },\r\n    // 回复\r\n    reply(id) {\r\n      this.$prompt('回复内容', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        inputErrorMessage: '请输入回复内容',\r\n        inputType: 'textarea',\r\n        inputPlaceholder: '请输入回复内容',\r\n        inputValidator: (value) => {\r\n          if (!value) {\r\n            return '输入不能为空';\r\n          }\r\n        }\r\n      }).then(({value}) => {\r\n        replyCommentApi({\r\n          ids: id,\r\n          merchantReplyContent: value\r\n        }).then(res => {\r\n          this.$message({\r\n            type: 'success',\r\n            message: '回复成功'\r\n          });\r\n          this.getList();\r\n        })\r\n      }).catch(() => {\r\n          this.$message({\r\n            type: 'info',\r\n            message: '取消输入'\r\n          })\r\n        })\r\n    },\r\n    // 选择时间\r\n    selectChange (tab) {\r\n      this.timeVal = [];\r\n      this.tableFrom.page = 1\r\n      this.getList();\r\n    },\r\n    // 商户分类；\r\n    getCategorySelect() {\r\n      categoryApi({ status: -1, type: 1 }).then(res => {\r\n        this.merCateList = res\r\n      }).catch(res => {\r\n        this.$message.error(res.message)\r\n      })\r\n    },\r\n    add() {\r\n      this.dialogVisible = true\r\n      this.timer = new Date().getTime()\r\n    },\r\n    handleClose(){\r\n      this.dialogVisible = false\r\n    },\r\n    // 具体日期\r\n    onchangeTime (e) {\r\n      this.timeVal = e;\r\n      this.tableFrom.dateLimit = e ? this.timeVal.join(',') : ''\r\n      this.tableFrom.page = 1\r\n      this.getList();\r\n    },\r\n    // 删除\r\n    handleDelete(id, idx) {\r\n      this.$modalSure().then(() => {\r\n        replyDeleteApi(id).then(() => {\r\n          this.$message.success('删除成功')\r\n          this.tableData.data.splice(idx, 1)\r\n        })\r\n      })\r\n    },\r\n    // 列表\r\n    getList() {\r\n      this.listLoading = true\r\n      this.tableFrom.uid = this.uids.join(',')\r\n      replyListApi(this.tableFrom).then(res => {\r\n        this.tableData.data = res.list\r\n        this.tableData.total = res.total\r\n        this.listLoading = false\r\n      }).catch(() => {\r\n        this.listLoading = false\r\n      })\r\n    },\r\n    pageChange(page) {\r\n      this.tableFrom.page = page\r\n      this.getList()\r\n    },\r\n    handleSizeChange(val) {\r\n      this.tableFrom.limit = val\r\n      this.getList()\r\n    },\r\n  }\r\n}\r\n", null]}