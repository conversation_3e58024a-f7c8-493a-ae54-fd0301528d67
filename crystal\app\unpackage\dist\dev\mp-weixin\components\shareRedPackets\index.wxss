@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.sharing-packets.data-v-1cb57b71 {
  position: fixed;
  left: 30rpx;
  bottom: 200rpx;
  z-index: 5;
  transition: all 0.3s ease-in-out 0s;
  opacity: 1;
  -webkit-transform: scale(1);
          transform: scale(1);
}
.sharing-packets.on.data-v-1cb57b71 {
  -webkit-transform: scale(0);
          transform: scale(0);
  opacity: 0;
}
.sharing-packets .iconfont.data-v-1cb57b71 {
  width: 44rpx;
  height: 44rpx;
  border-radius: 50%;
  text-align: center;
  line-height: 44rpx;
  background-color: #999;
  font-size: 20rpx;
  color: #fff;
  margin: 0 auto;
  box-sizing: border-box;
  padding-left: 1px;
}
.sharing-packets .line.data-v-1cb57b71 {
  width: 2rpx;
  height: 40rpx;
  background-color: #999;
  margin: 0 auto;
}
.sharing-packets .sharing-con.data-v-1cb57b71 {
  width: 187rpx;
  height: 210rpx;
  position: relative;
}
.sharing-packets .sharing-con image.data-v-1cb57b71 {
  width: 100%;
  height: 100%;
}
.sharing-packets .sharing-con .text.data-v-1cb57b71 {
  position: absolute;
  top: 30rpx;
  font-size: 20rpx;
  width: 100%;
  text-align: center;
}
.sharing-packets .sharing-con .text .money.data-v-1cb57b71 {
  font-size: 32rpx;
  font-weight: bold;
  margin-top: 5rpx;
}
.sharing-packets .sharing-con .text .money .label.data-v-1cb57b71 {
  font-size: 20rpx;
}
.sharing-packets .sharing-con .text .tip.data-v-1cb57b71 {
  font-size: 18rpx;
  color: #999;
  margin-top: 5rpx;
}
.sharing-packets .sharing-con .text .shareBut.data-v-1cb57b71 {
  font-size: 22rpx;
  color: #fff;
  margin-top: 18rpx;
  height: 50rpx;
  line-height: 50rpx;
}

