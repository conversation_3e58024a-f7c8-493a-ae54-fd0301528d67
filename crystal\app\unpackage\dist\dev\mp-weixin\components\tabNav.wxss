@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.navTabBox {
  width: 100%;
  color: white;
}
.navTabBox .click {
  color: white;
}
.navTabBox .longTab {
  width: 100%;
  padding-top: 12rpx;
  padding-bottom: 12rpx;
}
.navTabBox .longTab .longItem {
  height: 50rpx;
  display: inline-block;
  line-height: 50rpx;
  text-align: center;
  font-size: 30rpx;
}
.navTabBox .longTab .longItem.click {
  font-weight: bold;
}
.navTabBox .longTab .underlineBox {
  height: 3px;
  width: 20%;
  display: flex;
  align-content: center;
  justify-content: center;
  transition: .5s;
}
.navTabBox .longTab .underlineBox .underline {
  width: 33rpx;
  height: 4rpx;
  background-color: white;
}
.child-box {
  width: 100%;
  position: relative;
  background-color: #fff;
  box-shadow: 0 2rpx 3rpx 1rpx #f9f9f9;
}
.child-box .wrapper {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  background: #fff;
}
.child-box .child-item {
  flex-shrink: 0;
  width: 140rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-left: 10rpx;
}
.child-box .child-item image {
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
}
.child-box .child-item .txt {
  font-size: 24rpx;
  color: #282828;
  text-align: center;
  margin-top: 10rpx;
  width: 100%;
}
.child-box .child-item.on image {
  border: 1px solid rgba(233, 51, 35, 0.6);
}
.child-box .child-item.on .txt {
  color: #c9ab79;
}

