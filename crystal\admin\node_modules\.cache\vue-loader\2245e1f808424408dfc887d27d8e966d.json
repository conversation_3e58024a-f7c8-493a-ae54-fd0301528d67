{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\list\\userDetails.vue?vue&type=template&id=879441c0&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\list\\userDetails.vue", "mtime": 1753666157942}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\n<div>\n  <div class=\"acea-row row-middle border_bottom pb-24\" v-if=\"psInfo\">\n    <div class=\"avatar mr20\"><img :src=\"psInfo.user.avatar\"></div>\n    <div class=\"dashboard-workplace-header-tip\">\n    <p class=\"dashboard-workplace-header-tip-title\" v-text=\"psInfo.user.nickname || '-'\"></p>\n    <div class=\"dashboard-workplace-header-tip-desc\">\n    <span class=\"dashboard-workplace-header-tip-desc-sp pb-1\">余额: {{ psInfo.balance }}</span>\n    <span class=\"dashboard-workplace-header-tip-desc-sp pb-1\">总计订单: {{ psInfo.allOrderCount }}</span>\n    <span class=\"dashboard-workplace-header-tip-desc-sp pb-1\">总消费金额: {{ psInfo.allConsumeCount }}</span>\n    <span class=\"dashboard-workplace-header-tip-desc-sp\">积分: {{ psInfo.integralCount }}</span>\n    <span class=\"dashboard-workplace-header-tip-desc-sp\">本月订单: {{ psInfo.mothOrderCount }}</span>\n    <span class=\"dashboard-workplace-header-tip-desc-sp\">本月消费金额: {{ psInfo.mothConsumeCount }}</span>\n    </div>\n    </div>\n  </div>\n  <el-row align=\"middle\" :gutter=\"10\" class=\"ivu-mt mt20\">\n    <el-col :span=\"4\">\n      <el-menu\n        default-active=\"0\"\n        class=\"el-menu-vertical-demo\"\n        @select=\"changeType\"\n      >\n        <el-menu-item :name=\"item.val\" v-for=\"(item, index) in list\" :key=\"index\" :index=\"item.val\">\n          <span slot=\"title\">{{item.label}}</span>\n        </el-menu-item >\n      </el-menu>\n    </el-col>\n    <el-col :span=\"20\">\n      <el-table :data=\"tableData.data\" class=\"tabNumWidth\"  v-loading=\"loading\" max-height=\"400\">\n        <el-table-column\n          :prop=\"item.key\"\n          :label=\"item.title\"\n          width=\"item.minWidth\"\n          :show-overflow-tooltip=\"true\"\n          v-for=\"(item, index) in columns\" :key=\"index\"\n        />\n      </el-table>\n      <div class=\"block\">\n        <el-pagination\n          :page-sizes=\"[6, 12, 18, 24]\"\n          :page-size=\"tableFrom.limit\"\n          :current-page=\"tableFrom.page\"\n          layout=\"total, sizes, prev, pager, next, jumper\"\n          :total=\"tableData.total\"\n          @size-change=\"handleSizeChange\"\n          @current-change=\"pageChange\"\n        />\n      </div>\n    </el-col>\n  </el-row>\n</div>\n", null]}