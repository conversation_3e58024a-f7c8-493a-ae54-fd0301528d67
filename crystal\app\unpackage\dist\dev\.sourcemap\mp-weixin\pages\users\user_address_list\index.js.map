{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/user_address_list/index.vue?bf41", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/user_address_list/index.vue?c2dd", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/user_address_list/index.vue?d04e", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/user_address_list/index.vue?acb5", "uni-app:///pages/users/user_address_list/index.vue", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/user_address_list/index.vue?aa49", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/user_address_list/index.vue?8369"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "authorize", "home", "data", "addressList", "cartId", "pinkId", "couponId", "loading", "loadend", "loadTitle", "page", "limit", "isAuto", "isShowAuth", "bargain", "combination", "secKill", "computed", "watch", "is<PERSON>ogin", "handler", "deep", "onLoad", "onShow", "that", "methods", "onLoadFun", "auth<PERSON><PERSON><PERSON>", "getWxAddress", "uni", "scope", "success", "addressP", "address", "isDefault", "realName", "postCode", "phone", "detail", "id", "title", "icon", "fail", "content", "console", "get<PERSON><PERSON><PERSON>", "province", "city", "district", "cityId", "then", "catch", "getAddressList", "radioChange", "<PERSON><PERSON><PERSON><PERSON>", "url", "<PERSON><PERSON><PERSON><PERSON>", "addAddress", "goOrder", "onReachBottom"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACmM;AACnM,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzBA;AAAA;AAAA;AAAA;AAAkwB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACoEtxB;AAOA;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAOA;EACAC;IAEAC;IAEAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;EACAC;IACAC;MACAC;QACA;UACA;QACA;MACA;MACAC;IACA;EACA;EACAC;IACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;MACA;IACA;EACA;EACAC;IACA;IACAC;EACA;EACAC;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACAC;QACAC;QACAC;UACAF;YACAE;cACA;cACAC;cACAA;cACAA;cACAA;cACA;gBACAC;gBACAC;gBACAC;gBACAC;gBACAC;gBACAC;gBACAC;gBACA;cACA;gBACAf;kBACAgB;kBACAC;gBACA;kBACAjB;gBACA;cACA;gBACA;kBACAgB;gBACA;cACA;YACA;YACAE;cACA;gBACAF;cACA;YACA;UACA;QACA;QACAE;UACAb;YACAW;YACAG;YACAZ;cACA;gBACAF;kBACAE;oBACAa;kBACA;gBACA;cACA;gBACA;kBACAJ;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAK;MACA;MACArB;QACA;QACA;UACAW;UACAE;UACAJ;YACAa;YACAC;YACAC;YACAC;UACA;UACAX;UACAF;UACAF;QACA,GACAgB;UACA1B;YACAgB;YACAC;UACA;YACA;YACAjB;UACA;QACA,GACA2B;UACA;UACA;YACAX;UACA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAY;MACA;MACA;QACA5B;QACAA;QACAA;MACA;MAAA;MACA;MACA;MACAA;MACAA;MACA;QACAd;QACAC;MACA;QACA;QACA;QACAa;QACAA;QACAA;QACAA;QACAA;QACAA;MACA;QACAA;QACAA;MACA;IACA;IACA;AACA;AACA;IACA6B;MACA;QACA7B;MACA;MACA;QACAgB;MACA;MACA;QACA;UACA,0DACAhB;QACA;QACAA;UACAgB;UACAC;QACA;UACAjB;QACA;MACA;QACA;UACAgB;QACA;MACA;IACA;IACA;AACA;AACA;IACAc;MACA;QACAjD;QACAC;MACA;MACA;MACA;MACAuB;QACA0B,6GACAjD;MACA;IACA;IACA;AACA;AACA;IACAkD;MACA;QACAvB;MACA;QACAO;MACA;MACA;QACAhB;UACAgB;UACAC;QACA;UACAjB;UACAA;QACA;MACA;QACA;UACAgB;QACA;MACA;IACA;IACA;AACA;AACA;IACAiB;MACA;QACApD;QACAC;MACA;MACA;MACA;MACAuB;QACA0B;MACA;IACA;IACAG;MACA;QACA7B;UACA0B;QACA;MACA;IACA;EACA;EACAI;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClXA;AAAA;AAAA;AAAA;AAAq8C,CAAgB,ovCAAG,EAAC,C;;;;;;;;;;;ACAz9C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/users/user_address_list/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/users/user_address_list/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=21ff4d10&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=21ff4d10&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"21ff4d10\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/users/user_address_list/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=21ff4d10&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.addressList.length\n  var g1 = _vm.addressList.length < 1 && _vm.page > 1\n  var g2 = _vm.addressList.length\n  var g3 = _vm.addressList.length\n  var g4 = _vm.addressList.length < 1 && _vm.page > 1\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<!-- #ifdef APP-->\r\n\t\t<view class='status_1'></view>\r\n\t\t<!-- #endif -->\r\n\t\t<view class='line'>\r\n\t\t\t<image src='../../../static/images/line.jpg' v-if=\"addressList.length\"></image>\r\n\t\t</view>\r\n\t\t<view class='address-management' :class='addressList.length < 1 && page > 1 ? \"fff\":\"\"'>\r\n\t\t\t<radio-group class=\"radio-group\" @change=\"radioChange\" v-if=\"addressList.length\">\r\n\t\t\t\t<view class='item borRadius14' v-for=\"(item,index) in addressList\" :key=\"index\">\r\n\t\t\t\t\t<view class='address' @click='goOrder(item.id)'>\r\n\t\t\t\t\t\t<view class='consignee'>收货人：{{item.realName}}<text class='phone'>{{item.phone}}</text></view>\r\n\t\t\t\t\t\t<view>收货地址：{{item.province}}{{item.city}}{{item.district}}{{item.detail}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='operation acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t<!-- #ifndef MP -->\r\n\t\t\t\t\t\t<radio class=\"radio\" :value=\"index.toString()\" :checked=\"item.isDefault\">\r\n\t\t\t\t\t\t\t<text>设为默认</text>\r\n\t\t\t\t\t\t</radio>\r\n\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t<!-- #ifdef MP -->\r\n\t\t\t\t\t\t<radio class=\"radio\" :value=\"index\" :checked=\"item.isDefault\">\r\n\t\t\t\t\t\t\t<text>设为默认</text>\r\n\t\t\t\t\t\t</radio>\r\n\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t<view class='acea-row row-middle'>\r\n\t\t\t\t\t\t\t<view @click='editAddress(item.id)'><text class='iconfont icon-bianji'></text>编辑</view>\r\n\t\t\t\t\t\t\t<view @click='delAddress(index)'><text class='iconfont icon-shanchu'></text>删除</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</radio-group>\r\n\t\t\t<view class='loadingicon acea-row row-center-wrapper' v-if=\"addressList.length\">\r\n\t\t\t\t<text class='loading iconfont icon-jiazai' :hidden='loading==false'></text>{{loadTitle}}\r\n\t\t\t</view>\r\n\t\t\t<view class='noCommodity' v-if=\"addressList.length < 1 && page > 1\">\r\n\t\t\t\t<view class='pictrue'>\r\n\t\t\t\t\t<image src='../../../static/images/noAddress.png'></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view style='height:120rpx;'></view>\r\n\t\t</view>\r\n\t\t<view class='footer acea-row row-between-wrapper'>\r\n\t\t\t<!-- #ifdef MP-->\r\n\t\t\t<view class='addressBnt bg-color' @click='addAddress'><text class='iconfont icon-tianjiadizhi'></text>添加新地址</view>\r\n\t\t\t<view class='addressBnt wxbnt' @click='getWxAddress'><text class='iconfont icon-weixin2'></text>导入微信地址</view>\r\n\t\t\t<!-- #endif -->\r\n\t\t\t<!-- #ifdef H5-->\r\n\t\t\t\r\n\t\t\t<view class='addressBnt bg-color' :class=\"this.$wechat.isWeixin()?'':'on'\" @click='addAddress'><text class='iconfont icon-tianjiadizhi'></text>添加新地址</view>\r\n\t\t\t<view v-if=\"this.$wechat.isWeixin()\" class='addressBnt wxbnt' @click='getAddress'><text class='iconfont icon-weixin2'></text>导入微信地址</view>\r\n\t\t\t<!-- #endif -->\r\n\t\t\t\r\n\t\t\t\r\n\t\t\t<!-- #ifdef APP-->\r\n\t\t\t<view class='addressBnt on bg-color' @click='addAddress'><text class='iconfont icon-tianjiadizhi'></text>添加新地址</view>\r\n\t\t\t<!-- #endif -->\r\n\t\t\t\r\n\t\t</view>\r\n\t\t<!-- #ifdef MP -->\r\n\t\t<!-- <authorize @onLoadFun=\"onLoadFun\" :isAuto=\"isAuto\" :isShowAuth=\"isShowAuth\" @authColse=\"authColse\"></authorize> -->\r\n\t\t<!-- #endif -->\r\n\t\t<home></home>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tgetAddressList,\r\n\t\tsetAddressDefault,\r\n\t\tdelAddress,\r\n\t\teditAddress,\r\n\t\tpostAddress\r\n\t} from '@/api/user.js';\r\n\timport {\r\n\t\ttoLogin\r\n\t} from '@/libs/login.js';\r\n\timport {\r\n\t\tmapGetters\r\n\t} from \"vuex\";\r\n\t// #ifdef MP\r\n\timport authorize from '@/components/Authorize';\r\n\t// #endif\r\n\timport home from '@/components/home';\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\t// #ifdef MP\r\n\t\t\tauthorize,\r\n\t\t\t// #endif\r\n\t\t\thome\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\taddressList: [],\r\n\t\t\t\tcartId: '',\r\n\t\t\t\tpinkId: 0,\r\n\t\t\t\tcouponId: 0,\r\n\t\t\t\tloading: false,\r\n\t\t\t\tloadend: false,\r\n\t\t\t\tloadTitle: '加载更多',\r\n\t\t\t\tpage: 1,\r\n\t\t\t\tlimit: 20,\r\n\t\t\t\tisAuto: false, //没有授权的不会自动授权\r\n\t\t\t\tisShowAuth: false, //是否隐藏授权\r\n\t\t\t\tbargain: false, //是否是砍价\r\n\t\t\t\tcombination: false, //是否是拼团\r\n\t\t\t\tsecKill: false, //是否是秒杀\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: mapGetters(['isLogin']),\r\n\t\twatch:{\r\n\t\t\tisLogin:{\r\n\t\t\t\thandler:function(newV,oldV){\r\n\t\t\t\t\tif(newV){\r\n\t\t\t\t\t\tthis.getUserAddress(true);\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tdeep:true\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\tif (this.isLogin) {\r\n\t\t\t\tthis.preOrderNo = options.preOrderNo || 0;\r\n\t\t\t\t// this.pinkId = options.pinkId || 0;\r\n\t\t\t\t// this.couponId = options.couponId || 0;\r\n\t\t\t\t// this.secKill = options.secKill || false;\r\n\t\t\t\t// this.combination = options.combination || false;\r\n\t\t\t\t// this.bargain = options.bargain || false;\r\n\t\t\t\tthis.getAddressList(true);\r\n\t\t\t} else {\r\n\t\t\t\ttoLogin();\r\n\t\t\t}\r\n\t\t},\r\n\t\tonShow: function() {\r\n\t\t\tlet that = this;\r\n\t\t\tthat.getAddressList(true);\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tonLoadFun: function() {\r\n\t\t\t\tthis.getAddressList();\r\n\t\t\t},\r\n\t\t\t// 授权关闭\r\n\t\t\tauthColse: function(e) {\r\n\t\t\t\tthis.isShowAuth = e\r\n\t\t\t},\r\n\t\t\t/*\r\n\t\t\t * 导入微信地址（小程序）\r\n\t\t\t */\r\n\t\t\tgetWxAddress: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tuni.authorize({\r\n\t\t\t\t\tscope: 'scope.address',\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tuni.chooseAddress({\r\n\t\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\t\tlet addressP = {};\r\n\t\t\t\t\t\t\t\taddressP.province = res.provinceName;\r\n\t\t\t\t\t\t\t\taddressP.city = res.cityName;\r\n\t\t\t\t\t\t\t\taddressP.district = res.countyName;\r\n\t\t\t\t\t\t\t\taddressP.cityId = 0;\r\n\t\t\t\t\t\t\t\teditAddress({\r\n\t\t\t\t\t\t\t\t\taddress: addressP,\r\n\t\t\t\t\t\t\t\t\tisDefault: true,\r\n\t\t\t\t\t\t\t\t\trealName: res.userName,\r\n\t\t\t\t\t\t\t\t\tpostCode: res.postalCode,\r\n\t\t\t\t\t\t\t\t\tphone: res.telNumber,\r\n\t\t\t\t\t\t\t\t\tdetail: res.detailInfo,\r\n\t\t\t\t\t\t\t\t\tid: 0\r\n\t\t\t\t\t\t\t\t\t//type: 1//区别城市id（导入微信地址无城市id需要后台自己查找）;\r\n\t\t\t\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\ttitle: \"添加成功\",\r\n\t\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t\t}, function() {\r\n\t\t\t\t\t\t\t\t\t\tthat.getAddressList(true);\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}).catch(err => {\r\n\t\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tfail: function(res) {\r\n\t\t\t\t\t\t\t\tif (res.errMsg == 'chooseAddress:cancel') return that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\ttitle: '取消选择'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: function(res) {\r\n\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\ttitle: '您已拒绝导入微信地址权限',\r\n\t\t\t\t\t\t\tcontent: '是否进入权限管理，调整授权？',\r\n\t\t\t\t\t\t\tsuccess(res) {\r\n\t\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\t\tuni.openSetting({\r\n\t\t\t\t\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\t\t\t\t\tconsole.log(res.authSetting)\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '已取消！'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t/*\r\n\t\t\t * 导入微信地址（公众号）\r\n\t\t\t */\r\n\t\t\tgetAddress() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tthat.$wechat.openAddress().then(userInfo => {\r\n\t\t\t\t\t// open();\r\n\t\t\t\t\teditAddress({\r\n\t\t\t\t\t\t\trealName: userInfo.userName,\r\n\t\t\t\t\t\t\tphone: userInfo.telNumber,\r\n\t\t\t\t\t\t\taddress: {\r\n\t\t\t\t\t\t\t\tprovince: userInfo.provinceName,\r\n\t\t\t\t\t\t\t\tcity: userInfo.cityName,\r\n\t\t\t\t\t\t\t\tdistrict: userInfo.countryName,\r\n\t\t\t\t\t\t\t\tcityId: 0\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tdetail: userInfo.detailInfo,\r\n\t\t\t\t\t\t\tpostCode: userInfo.postalCode,\r\n\t\t\t\t\t\t\tisDefault: true\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.then(() => {\r\n\t\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: \"添加成功\",\r\n\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t}, function() {\r\n\t\t\t\t\t\t\t\t// close();\r\n\t\t\t\t\t\t\t\tthat.getAddressList(true);\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\t\t// close();\r\n\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: err || \"添加失败\"\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 获取地址列表\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tgetAddressList: function(isPage) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif (isPage) {\r\n\t\t\t\t\tthat.loadend = false;\r\n\t\t\t\t\tthat.page = 1;\r\n\t\t\t\t\tthat.$set(that, 'addressList', []);\r\n\t\t\t\t};\r\n\t\t\t\tif (that.loading) return;\r\n\t\t\t\tif (that.loadend) return;\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tthat.loadTitle = '';\r\n\t\t\t\tgetAddressList({\r\n\t\t\t\t\tpage: that.page,\r\n\t\t\t\t\tlimit: that.limit\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tlet list = res.data.list;\r\n\t\t\t\t\tlet loadend = list.length < that.limit;\r\n\t\t\t\t\tthat.addressList = that.$util.SplitArray(list, that.addressList);\r\n\t\t\t\t\tthat.$set(that, 'addressList', that.addressList);\r\n\t\t\t\t\tthat.loadend = loadend;\r\n\t\t\t\t\tthat.loadTitle = loadend ? '我也是有底线的' : '加载更多';\r\n\t\t\t\t\tthat.page = that.page + 1;\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tthat.loadTitle = '加载更多';\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 设置默认地址\r\n\t\t\t */\r\n\t\t\tradioChange: function(e) {\r\n\t\t\t\tlet index = parseInt(e.detail.value),\r\n\t\t\t\t\tthat = this;\r\n\t\t\t\tlet address = this.addressList[index];\r\n\t\t\t\tif (address == undefined) return that.$util.Tips({\r\n\t\t\t\t\ttitle: '您设置的默认地址不存在!'\r\n\t\t\t\t});\r\n\t\t\t\tsetAddressDefault(address.id).then(res => {\r\n\t\t\t\t\tfor (let i = 0, len = that.addressList.length; i < len; i++) {\r\n\t\t\t\t\t\tif (i == index) that.addressList[i].isDefault = true;\r\n\t\t\t\t\t\telse that.addressList[i].isDefault = false;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\ttitle: '设置成功',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t}, function() {\r\n\t\t\t\t\t\tthat.$set(that, 'addressList', that.addressList);\r\n\t\t\t\t\t});\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 编辑地址\r\n\t\t\t */\r\n\t\t\teditAddress: function(id) {\r\n\t\t\t\tlet cartId = this.cartId,\r\n\t\t\t\t\tpinkId = this.pinkId,\r\n\t\t\t\t\tcouponId = this.couponId;\r\n\t\t\t\tthis.cartId = '';\r\n\t\t\t\tthis.pinkId = '';\r\n\t\t\t\tthis.couponId = '';\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/users/user_address/index?id=' + id + '&cartId=' + cartId + '&pinkId=' + pinkId + '&couponId=' +\r\n\t\t\t\t\t\tcouponId + '&secKill' + this.secKill + '&combination=' + this.combination + '&bargain=' + this.bargain\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 删除地址\r\n\t\t\t */\r\n\t\t\tdelAddress: function(index) {\r\n\t\t\t\tlet that = this,\r\n\t\t\t\t\taddress = this.addressList[index];\r\n\t\t\t\tif (address == undefined) return that.$util.Tips({\r\n\t\t\t\t\ttitle: '您删除的地址不存在!'\r\n\t\t\t\t});\r\n\t\t\t\tdelAddress(address.id).then(res => {\r\n\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\ttitle: '删除成功',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t}, function() {\r\n\t\t\t\t\t\tthat.addressList.splice(index, 1);\r\n\t\t\t\t\t\tthat.$set(that, 'addressList', that.addressList);\r\n\t\t\t\t\t});\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 新增地址\r\n\t\t\t */\r\n\t\t\taddAddress: function() {\r\n\t\t\t\tlet cartId = this.cartId,\r\n\t\t\t\t\tpinkId = this.pinkId,\r\n\t\t\t\t\tcouponId = this.couponId;\r\n\t\t\t\tthis.cartId = '';\r\n\t\t\t\tthis.pinkId = '';\r\n\t\t\t\tthis.couponId = '';\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/users/user_address/index?preOrderNo=' + this.preOrderNo\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgoOrder: function(id) {\r\n\t\t\t\tif(this.preOrderNo){\r\n\t\t\t\t\tuni.redirectTo({\r\n\t\t\t\t\t\turl: '/pages/users/order_confirm/index?is_address=1&preOrderNo=' + this.preOrderNo + '&addressId=' +  id \r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tonReachBottom: function() {\r\n\t\t\tthis.getAddressList();\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.address-management{\r\n\t\tpadding: 20rpx 30rpx;\r\n\t}\r\n\t.address-management.fff {\r\n\t\tbackground-color: #fff;\r\n\t\theight: 1300rpx\r\n\t}\r\n\r\n\t.line {\r\n\t\twidth: 100%;\r\n\t\theight: 3rpx;\r\n\t\timage {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t\tdisplay: block;\r\n\t\t}\r\n\t}\r\n\t.address-management .item {\r\n\t\tbackground-color: #fff;\r\n\t\tpadding: 0 20rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.address-management .item .address {\r\n\t\tpadding: 35rpx 0;\r\n\t\tborder-bottom: 1rpx solid #eee;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #282828;\r\n\t}\r\n\r\n\t.address-management .item .address .consignee {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-bottom: 8rpx;\r\n\t}\r\n\r\n\t.address-management .item .address .consignee .phone {\r\n\t\tmargin-left: 25rpx;\r\n\t}\r\n\r\n\t.address-management .item .operation {\r\n\t\theight: 83rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #282828;\r\n\t}\r\n\r\n\t.address-management .item .operation .radio text {\r\n\t\tmargin-left: 13rpx;\r\n\t}\r\n\r\n\t.address-management .item .operation .iconfont {\r\n\t\tcolor: #2c2c2c;\r\n\t\tfont-size: 35rpx;\r\n\t\tvertical-align: -2rpx;\r\n\t\tmargin-right: 10rpx;\r\n\t}\r\n\r\n\t.address-management .item .operation .iconfont.icon-shanchu {\r\n\t\tmargin-left: 35rpx;\r\n\t\tfont-size: 38rpx;\r\n\t}\r\n\r\n\t .footer {\r\n\t\tposition: fixed;\r\n\t\twidth: 100%;\r\n\t\tbackground-color: #fff;\r\n\t\tbottom: 0;\r\n\t\theight: 106rpx;\r\n\t\tpadding: 0 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n    .footer .addressBnt {\r\n\t\twidth: 330rpx;\r\n\t\theight: 76rpx;\r\n\t\tborder-radius: 50rpx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 76rpx;\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #fff;\r\n\t}\r\n\r\n\t .footer .addressBnt.on {\r\n\t\twidth: 690rpx;\r\n\t\tmargin: 0 auto;\r\n\t}\r\n\r\n\t .footer .addressBnt .iconfont {\r\n\t\tfont-size: 35rpx;\r\n\t\tmargin-right: 8rpx;\r\n\t\tvertical-align: -1rpx;\r\n\t}\r\n\r\n\t .footer .addressBnt.wxbnt {\r\n\t\tbackground-color: #fe960f;\r\n\t}\r\n\t.status_1{\r\n\t\tdisplay: flex;\r\n\t\twidth: 750rpx;\r\n\t\t// background-color: #c9ab79;\r\n\t\theight: var(--status-bar-height);\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=21ff4d10&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=21ff4d10&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363903616\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}