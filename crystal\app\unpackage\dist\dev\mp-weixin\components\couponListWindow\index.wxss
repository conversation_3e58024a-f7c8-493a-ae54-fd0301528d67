@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.coupon-list-window.data-v-06f3951c {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #f5f5f5;
  border-radius: 16rpx 16rpx 0 0;
  z-index: 555;
  -webkit-transform: translate3d(0, 100%, 0);
          transform: translate3d(0, 100%, 0);
  transition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);
}
.coupon-list-window.on.data-v-06f3951c {
  -webkit-transform: translate3d(0, 0, 0);
          transform: translate3d(0, 0, 0);
}
.coupon-list-window .title.data-v-06f3951c {
  height: 124rpx;
  width: 100%;
  text-align: center;
  line-height: 124rpx;
  font-size: 32rpx;
  font-weight: bold;
  position: relative;
}
.coupon-list-window .title .iconfont.data-v-06f3951c {
  position: absolute;
  right: 30rpx;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  font-size: 35rpx;
  color: #8a8a8a;
  font-weight: normal;
}
.coupon-list-window .coupon-list.data-v-06f3951c {
  margin: 0 0 30rpx 0;
  height: 823rpx;
  overflow: auto;
  padding-top: 30rpx;
}
.coupon-list-window .pictrue.data-v-06f3951c {
  width: 414rpx;
  height: 336rpx;
  margin: 208rpx auto;
}
.coupon-list-window .pictrue image.data-v-06f3951c {
  width: 100%;
  height: 100%;
}
.pic-num.data-v-06f3951c {
  color: #fff;
  font-size: 24rpx;
}
.line-title.data-v-06f3951c {
  width: 90rpx;
  padding: 0 10rpx;
  box-sizing: border-box;
  background: #fff7f7;
  border: 1px solid #e83323;
  opacity: 1;
  border-radius: 20rpx;
  font-size: 20rpx;
  color: #E83323;
  margin-right: 12rpx;
}
.line-title.gray.data-v-06f3951c {
  border-color: #BBB;
  color: #bbb;
  background-color: #F5F5F5;
}
.nav.data-v-06f3951c {
  width: 100%;
  height: 96rpx;
  border-bottom: 2rpx solid #F5F5F5;
  border-top-left-radius: 16rpx;
  border-top-right-radius: 16rpx;
  background-color: #FFFFFF;
  font-size: 30rpx;
  color: #999999;
}
.nav .acea-row.data-v-06f3951c {
  border-top: 5rpx solid transparent;
  border-bottom: 5rpx solid transparent;
}
.nav .acea-row.on.data-v-06f3951c {
  border-bottom-color: #c9ab79;
  color: #282828;
}
.nav .acea-row.data-v-06f3951c:only-child {
  border-bottom-color: transparent;
}
.occupy.data-v-06f3951c {
  height: 106rpx;
}
.coupon-list .item.data-v-06f3951c {
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.06);
}
.coupon-list .item .money.data-v-06f3951c {
  font-weight: normal;
}

