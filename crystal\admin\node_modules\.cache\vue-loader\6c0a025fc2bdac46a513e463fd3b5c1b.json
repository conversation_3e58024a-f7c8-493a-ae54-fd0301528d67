{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\distribution\\config\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\distribution\\config\\index.vue", "mtime": 1753666157867}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { configApi, configUpdateApi, productCheckApi } from '@/api/distribution'\nimport * as selfUtil from '@/utils/ZBKJIutil.js'\nimport { checkPermi } from \"@/utils/permission\"; // 权限判断函数\nimport {Debounce} from '@/utils/validate'\nexport default {\n  name: 'Index',\n  data() {\n    return {\n      promoterForm: {},\n      loading: true,\n      rules: {\n        brokerageFuncStatus: [\n          { required: true, message: '请选择是否启用分销', trigger: 'change' }\n        ],\n        storeBrokerageRatio: [\n          { required: true, message: '请输入一级返佣比例', trigger: 'blur' }\n        ],\n        storeBrokerageTwo: [\n          { required: true, message: '请输入二级返佣比例', trigger: 'blur' }\n        ]\n      }\n    }\n  },\n  mounted() {\n    this.getDetal()\n  },\n  methods: {\n    checkPermi,\n    channelInputLimit(e){\n      let key = e.key\n    // 不允许输入'e'和'.'\n    if (key === 'e' || key === '.') {\n      e.returnValue = false\n      return false\n    }\n    return true\n    },\n    getDetal() {\n      this.loading = true;\n      configApi().then(res => {\n        this.loading = false;\n        this.promoterForm = res;\n        this.promoterForm.storeBrokerageIsBubble = res.storeBrokerageIsBubble.toString();\n        this.promoterForm.brokerageFuncStatus = res.brokerageFuncStatus.toString();\n        this.promoterForm.brokerageBindind = res.brokerageBindind.toString();\n      }).catch((res) => {\n        this.$message.error(res.message)\n      })\n    },\n    submitForm:Debounce(function(formName) {\n      this.$refs[formName].validate((valid) => {\n        if (valid) {\n        if(selfUtil.Add(this.promoterForm.storeBrokerageRatio,this.promoterForm.storeBrokerageTwo)>100) return this.$message.warning('返佣比例相加不能超过100%')\n          this.loading = true\n          configUpdateApi(this.promoterForm).then(res => {\n            this.loading = false\n            this.$message.success('提交成功')\n            // this.$modalSure('提交成功，是否自动下架商户低于此佣金比例的商品').then(() => {\n            //   productCheckApi().then(({ message }) => {\n            //     this.$message.success(message)\n            //   }).catch(({ message }) => {\n            //     this.$message.error(message)\n            //   })\n            // })\n          }).catch((err) => {\n            this.loading = false\n          })\n        } else {\n          return false\n        }\n      })\n    })\n  }\n}\n", null]}