{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\storeComment\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\storeComment\\index.vue", "mtime": 1753666157925}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _creatComment = _interopRequireDefault(require(\"./creatComment.vue\"));\nvar _store = require(\"@/api/store\");\nvar _index = require(\"@/utils/index\");\nvar _user = require(\"@/api/user\");\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  name: 'StoreComment',\n  filters: {\n    formatDate: function formatDate(time) {\n      if (time !== 0) {\n        var date = new Date(time * 1000);\n        return (0, _index.formatDates)(date, 'yyyy-MM-dd hh:mm');\n      }\n    }\n  },\n  components: {\n    creatComment: _creatComment.default\n  },\n  data: function data() {\n    return {\n      merCateList: [],\n      props: {\n        children: 'child',\n        label: 'name',\n        value: 'id',\n        emitPath: false\n      },\n      fromList: this.$constants.fromList,\n      tableData: {\n        data: [],\n        total: 0\n      },\n      listLoading: true,\n      tableFrom: {\n        page: 1,\n        limit: 20,\n        isReply: '',\n        dateLimit: '',\n        // uid: '',\n        nickname: '',\n        productSearch: '',\n        isDel: false\n      },\n      timeVal: [],\n      loading: false,\n      uids: [],\n      options: [],\n      dialogVisible: false,\n      timer: ''\n    };\n  },\n  watch: {\n    $route: function $route(to, from) {\n      this.getList();\n      this.getCategorySelect();\n    }\n  },\n  mounted: function mounted() {\n    // this.getLstFilterApi()\n    this.getList();\n    this.getCategorySelect();\n  },\n  methods: {\n    remoteMethod: function remoteMethod(query) {\n      var _this = this;\n      if (query !== '') {\n        this.loading = true;\n        setTimeout(function () {\n          _this.loading = false;\n          (0, _user.userListApi)({\n            keywords: query,\n            page: 1,\n            limit: 10\n          }).then(function (res) {\n            _this.options = res.list;\n          });\n        }, 200);\n      } else {\n        this.options = [];\n      }\n    },\n    seachList: function seachList() {\n      this.dialogVisible = false;\n      this.tableFrom.page = 1;\n      this.getList();\n    },\n    // 回复\n    reply: function reply(id) {\n      var _this2 = this;\n      this.$prompt('回复内容', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        inputErrorMessage: '请输入回复内容',\n        inputType: 'textarea',\n        inputPlaceholder: '请输入回复内容',\n        inputValidator: function inputValidator(value) {\n          if (!value) {\n            return '输入不能为空';\n          }\n        }\n      }).then(function (_ref) {\n        var value = _ref.value;\n        (0, _store.replyCommentApi)({\n          ids: id,\n          merchantReplyContent: value\n        }).then(function (res) {\n          _this2.$message({\n            type: 'success',\n            message: '回复成功'\n          });\n          _this2.getList();\n        });\n      }).catch(function () {\n        _this2.$message({\n          type: 'info',\n          message: '取消输入'\n        });\n      });\n    },\n    // 选择时间\n    selectChange: function selectChange(tab) {\n      this.timeVal = [];\n      this.tableFrom.page = 1;\n      this.getList();\n    },\n    // 商户分类；\n    getCategorySelect: function getCategorySelect() {\n      var _this3 = this;\n      (0, _store.categoryApi)({\n        status: -1,\n        type: 1\n      }).then(function (res) {\n        _this3.merCateList = res;\n      }).catch(function (res) {\n        _this3.$message.error(res.message);\n      });\n    },\n    add: function add() {\n      this.dialogVisible = true;\n      this.timer = new Date().getTime();\n    },\n    handleClose: function handleClose() {\n      this.dialogVisible = false;\n    },\n    // 具体日期\n    onchangeTime: function onchangeTime(e) {\n      this.timeVal = e;\n      this.tableFrom.dateLimit = e ? this.timeVal.join(',') : '';\n      this.tableFrom.page = 1;\n      this.getList();\n    },\n    // 删除\n    handleDelete: function handleDelete(id, idx) {\n      var _this4 = this;\n      this.$modalSure().then(function () {\n        (0, _store.replyDeleteApi)(id).then(function () {\n          _this4.$message.success('删除成功');\n          _this4.tableData.data.splice(idx, 1);\n        });\n      });\n    },\n    // 列表\n    getList: function getList() {\n      var _this5 = this;\n      this.listLoading = true;\n      this.tableFrom.uid = this.uids.join(',');\n      (0, _store.replyListApi)(this.tableFrom).then(function (res) {\n        _this5.tableData.data = res.list;\n        _this5.tableData.total = res.total;\n        _this5.listLoading = false;\n      }).catch(function () {\n        _this5.listLoading = false;\n      });\n    },\n    pageChange: function pageChange(page) {\n      this.tableFrom.page = page;\n      this.getList();\n    },\n    handleSizeChange: function handleSizeChange(val) {\n      this.tableFrom.limit = val;\n      this.getList();\n    }\n  }\n};", null]}