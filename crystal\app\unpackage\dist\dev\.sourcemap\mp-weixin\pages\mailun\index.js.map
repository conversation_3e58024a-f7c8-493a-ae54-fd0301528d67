{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/mailun/index.vue?effb", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/mailun/index.vue?fc1a", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/mailun/index.vue?2d10", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/mailun/index.vue?5d51", "uni-app:///pages/mailun/index.vue", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/mailun/index.vue?8508", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/mailun/index.vue?bb93"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "data", "mailunshouye", "btnObj", "id", "name", "childs", "onLoad", "onShow", "onShareAppMessage", "methods", "start", "uni", "title", "mask", "url", "click", "console"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACgM;AAChM,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wUAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAmvB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACoBvwB;AACA;;;;;;;;;;;;;;;;;;;;eAGA;EACAC,aACA;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;QACAC;UACAF;UACAC;QACA;UACAD;UACAC;QACA;UACAD;UACAC;QACA;MACA;IACA;EACA;EACA;AACA;AACA;EACAE,kCACA;EACAC;IAAA;IACA;MACA;IAEA;EACA;EACA;AACA;AACA;;EAEAC;IACA;IACA;IACA;IACA;IACA;IACA;EAAA,CACA;EAEAC;IAEAC;MAAA;MACA;MACA;MACA;MACA;MACA;MACAC;QACAC;QACAC;MACA;MACA;QACAF;QACAjB;UAAAoB;QAAA;MAEA;QAEAH;QACA;UACAC;QACA;MAEA;MACA;MACA;MACA;MACA;MACA;IACA;IAAA;IACAG;MACAC;MACA;QACAL;UACAG;QACA;MACA;QACAH;UACAG;QACA;MACA;QACAH;UAAAG;QAAA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnHA;AAAA;AAAA;AAAA;AAA06C,CAAgB,ovCAAG,EAAC,C;;;;;;;;;;;ACA97C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mailun/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mailun/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=aa744fe8&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=aa744fe8&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"aa744fe8\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mailun/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=aa744fe8&scoped=true&\"", "var components\ntry {\n  components = {\n    liuRotatingMenu: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/liu-rotating-menu/components/liu-rotating-menu/liu-rotating-menu\" */ \"@/uni_modules/liu-rotating-menu/components/liu-rotating-menu/liu-rotating-menu.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view style=\"background-color: #fff;\">\r\n\t\t<view style=\"text-align: center;\">\r\n\t\t\t<image class=\"\"\r\n\t\t\t\t:src=\"mailunshouye\"\r\n\t\t\t\tmode=\"widthFix\" lazy-load=\"false\" style=\"width: 710rpx;margin: 30rpx 0;\" />\r\n\t\t\t\t<view class=\"maitext\">做脉轮测试之前先找个不受外界干扰的地方\r\n\t\t\t\t\t</view>\r\n\t\t\t\t<view class=\"maitext\" style=\"margin-bottom: 30rpx;\">让自己放松，放下刚才任何情绪\r\n\t\t\t\t\t</view>\r\n\t\t\t<view class=\"botton_1\" @click=\"start\">\r\n\t\t\t\t开始我的脉轮测试\r\n\t\t\t</view>\r\n\r\n\t\t</view>\r\n\t\t<liu-rotating-menu :btnObj=\"btnObj\" @click=\"click\"></liu-rotating-menu>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport { questionStartExam } from \"@/api/question\"\r\nimport {\r\n    mailunConfig,\r\n} from '@/api/api.js';\r\nexport default {\r\n\tcomponents: {\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tmailunshouye: '',\r\n\t\t\tbtnObj: {\r\n\t\t\t\tid: '1',\r\n\t\t\t\tname: '快捷导航',\r\n\t\t\t\tchilds: [{\r\n\t\t\t\t\tid: '1',\r\n\t\t\t\t\tname: '我的测试'\r\n\t\t\t\t}, {\r\n\t\t\t\t\tid: '2',\r\n\t\t\t\t\tname: '脉轮简介'\r\n\t\t\t\t}, {\r\n\t\t\t\t\tid: '3',\r\n\t\t\t\t\tname: '平衡脉轮'\r\n\t\t\t\t}]\r\n\t\t\t}\r\n\t\t};\r\n\t},\r\n\t/**\r\n\t  * 生命周期函数--监听页面加载\r\n\t  */\r\n\tonLoad: function (options) {\r\n\t},\r\n\tonShow: function () {\r\n        mailunConfig().then(res => {\r\n            this.$set(this, \"mailunshouye\", res.data.mailunshouye);\r\n\r\n        })\r\n\t},\r\n\t/**\r\n\t * 用户点击右上角分享\r\n\t */\r\n\t// #ifdef MP\r\n\tonShareAppMessage: function () {\r\n\t\t// return {\r\n\t\t// \ttitle: this.articleInfo.title,\r\n\t\t// \timageUrl: this.articleInfo.imageInput.length ? this.articleInfo.imageInput[0] : \"\",\r\n\t\t// \tdesc: this.articleInfo.synopsis,\r\n\t\t// \tpath: '/pages/news_details/index?id=' + this.id\r\n\t\t// };\r\n\t},\r\n\t// #endif\r\n\tmethods: {\r\n\r\n\t\tstart() {\r\n\t\t\t// uni.showModal({\r\n\t\t\t// \ttitle: '提示',\r\n\t\t\t// \tcontent: '确认开启脉轮测试',\r\n\t\t\t// \tsuccess: function (res) {\r\n\t\t\t// \t\tif (res.confirm) {\r\n\t\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\t\ttitle: '开启中',\r\n\t\t\t\t\t\t\tmask: true,\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tquestionStartExam().then(res => {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\twx.navigateTo({ url: '/pages/mailun/child/start?questionUserId=' + res.data.questionUserId + \"&token=\" + res.data.token })\r\n\r\n\t\t\t\t\t\t}).catch(res => {\r\n\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tthis.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: res\r\n\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t})\r\n\t\t\t// \t\t} else if (res.cancel) {\r\n\t\t\t// \t\t\tconsole.log('用户点击取消');\r\n\t\t\t// \t\t}\r\n\t\t\t// \t}\r\n\t\t\t// });\r\n\t\t},\t//点击菜单\r\n\t\tclick(e) {\r\n\t\t\tconsole.log('所点击菜单信息：', e)\r\n\t\t\tif (e.id == '1') {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/mailun/child/list'\r\n\t\t\t\t})\r\n\t\t\t} else if (e.id == '2') {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/mailun/child/mailunjianjie'\r\n\t\t\t\t})\r\n\t\t\t} else if (e.id == '3') {\r\n\t\t\t\tuni.navigateTo({ url: '/pages/mailun/child/mailundakai' })\r\n\t\t\t}\r\n\t\t},\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.maitext {\r\n\theight: 60rpx;\r\n\tline-height: 60rpx;\r\n\tfont-size: 32rpx;\r\n\tcolor: #c9ab79;\r\n\tfont-weight: 500;\r\n}\r\n.botton_1 {\r\n\tmargin-left: 20rpx;\r\n\twidth: 710rpx;\r\n\tbackground-color: #c9ab79;\r\n\tcolor: #fff;\r\n\tfont-size: 28rpx;\r\n\tfont-weight: 600;\r\n\theight: 88rpx;\r\n\tborder-radius: 50rpx;\r\n\ttext-align: center;\r\n\tline-height: 88rpx;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=aa744fe8&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=aa744fe8&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363904118\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}