(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["uni_modules/liu-rotating-menu/components/liu-rotating-menu/liu-rotating-menu"],{"1e6f":function(t,i,n){"use strict";n.r(i);var e=n("b256"),o=n("acbb");for(var u in o)["default"].indexOf(u)<0&&function(t){n.d(i,t,(function(){return o[t]}))}(u);n("aecb");var c=n("828b"),d=Object(c["a"])(o["default"],e["b"],e["c"],!1,null,"e7f22f48",null,!1,e["a"],void 0);i["default"]=d.exports},6891:function(t,i,n){"use strict";(function(t){Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var n={props:{btnObj:{type:Object,default:{}},disabled:{type:Boolean,default:!1},bottomPx:{type:Number,default:100},rightPx:{type:Number,default:0}},data:function(){return{left:0,top:0,isRemove:!0,windowWidth:0,windowHeight:0,btnWidth:0,btnHeight:0,x:1e4,y:1e4,old:{x:0,y:0},showBtn:!1,isLeft:!1}},mounted:function(){var t=this;this.$nextTick((function(i){t.getSysInfo()}))},methods:{getSysInfo:function(){var i=this,n=t.getSystemInfoSync();this.windowWidth=n.windowWidth,this.windowHeight=n.windowHeight,this.createSelectorQuery().select(".movable-view").boundingClientRect((function(t){i.btnWidth=t.width,i.btnHeight=t.height,i.x=i.old.x,i.y=i.old.y,i.$nextTick((function(t){i.x=i.windowWidth-i.btnWidth-i.rightPx,i.y=i.windowHeight-i.btnHeight-i.bottomPx}))})).exec()},onChange:function(t){this.old.x=t.detail.x,this.old.y=t.detail.y},touchstart:function(t){this.isRemove=!0},touchend:function(t){var i=this;if(!this.disabled&&this.old.x){this.x=this.old.x,this.y=this.old.y;var n=(this.windowWidth-this.btnWidth)/2;this.x<0||this.x>0&&this.x<=n?this.$nextTick((function(t){i.x=0,i.isLeft=!0})):this.$nextTick((function(t){i.x=i.windowWidth-i.btnWidth,i.isLeft=!1})),this.isRemove=!1}},clickBtn:function(){this.showBtn=!this.showBtn},click:function(t){this.$emit("click",t)}}};i.default=n}).call(this,n("df3c")["default"])},acbb:function(t,i,n){"use strict";n.r(i);var e=n("6891"),o=n.n(e);for(var u in e)["default"].indexOf(u)<0&&function(t){n.d(i,t,(function(){return e[t]}))}(u);i["default"]=o.a},aecb:function(t,i,n){"use strict";var e=n("b179"),o=n.n(e);o.a},b179:function(t,i,n){},b256:function(t,i,n){"use strict";n.d(i,"b",(function(){return e})),n.d(i,"c",(function(){return o})),n.d(i,"a",(function(){}));var e=function(){var t=this.$createElement;this._self._c},o=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uni_modules/liu-rotating-menu/components/liu-rotating-menu/liu-rotating-menu-create-component',
    {
        'uni_modules/liu-rotating-menu/components/liu-rotating-menu/liu-rotating-menu-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("1e6f"))
        })
    },
    [['uni_modules/liu-rotating-menu/components/liu-rotating-menu/liu-rotating-menu-create-component']]
]);
