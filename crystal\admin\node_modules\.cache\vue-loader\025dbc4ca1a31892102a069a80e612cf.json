{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\FormGenerator\\index\\TreeNodeDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\FormGenerator\\index\\TreeNodeDialog.vue", "mtime": 1753666157771}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport { isNumberStr } from '../utils/index'\r\nimport { getTreeNodeId, saveTreeNodeId } from '../utils/db'\r\n\r\nconst id = getTreeNodeId()\r\n\r\nexport default {\r\n  components: {},\r\n  inheritAttrs: false,\r\n  props: [],\r\n  data() {\r\n    return {\r\n      id,\r\n      formData: {\r\n        label: undefined,\r\n        value: undefined\r\n      },\r\n      rules: {\r\n        label: [\r\n          {\r\n            required: true,\r\n            message: '请输入选项名',\r\n            trigger: 'blur'\r\n          }\r\n        ],\r\n        value: [\r\n          {\r\n            required: true,\r\n            message: '请输入选项值',\r\n            trigger: 'blur'\r\n          }\r\n        ]\r\n      },\r\n      dataType: 'string',\r\n      dataTypeOptions: [\r\n        {\r\n          label: '字符串',\r\n          value: 'string'\r\n        },\r\n        {\r\n          label: '数字',\r\n          value: 'number'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  computed: {},\r\n  watch: {\r\n    // eslint-disable-next-line func-names\r\n    'formData.value': function(val) {\r\n      this.dataType = isNumberStr(val) ? 'number' : 'string'\r\n    },\r\n    id(val) {\r\n      saveTreeNodeId(val)\r\n    }\r\n  },\r\n  created() {},\r\n  mounted() {},\r\n  methods: {\r\n    onOpen() {\r\n      this.formData = {\r\n        label: undefined,\r\n        value: undefined\r\n      }\r\n    },\r\n    onClose() {},\r\n    close() {\r\n      this.$emit('update:visible', false)\r\n    },\r\n    handelConfirm() {\r\n      this.$refs.elForm.validate(valid => {\r\n        if (!valid) return\r\n        if (this.dataType === 'number') {\r\n          this.formData.value = parseFloat(this.formData.value)\r\n        }\r\n        this.formData.id = this.id++\r\n        this.$emit('commit', this.formData)\r\n        this.close()\r\n      })\r\n    }\r\n  }\r\n}\r\n", null]}