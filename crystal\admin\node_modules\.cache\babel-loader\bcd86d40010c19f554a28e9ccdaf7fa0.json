{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\element-ui\\node_modules\\async-validator\\es\\rule\\range.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\element-ui\\node_modules\\async-validator\\es\\rule\\range.js", "mtime": 1753666300294}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}], "contextDependencies": [], "result": ["\"use strict\";\n\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar util = _interopRequireWildcard(require(\"../util\"));\nfunction _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != _typeof(e) && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n/**\n *  Rule for validating minimum and maximum allowed values.\n *\n *  @param rule The validation rule.\n *  @param value The value of the field on the source object.\n *  @param source The source object being validated.\n *  @param errors An array of errors that this rule may add\n *  validation errors to.\n *  @param options The validation options.\n *  @param options.messages The validation messages.\n */\nfunction range(rule, value, source, errors, options) {\n  var len = typeof rule.len === 'number';\n  var min = typeof rule.min === 'number';\n  var max = typeof rule.max === 'number';\n  // 正则匹配码点范围从U+010000一直到U+10FFFF的文字（补充平面Supplementary Plane）\n  var spRegexp = /[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]/g;\n  var val = value;\n  var key = null;\n  var num = typeof value === 'number';\n  var str = typeof value === 'string';\n  var arr = Array.isArray(value);\n  if (num) {\n    key = 'number';\n  } else if (str) {\n    key = 'string';\n  } else if (arr) {\n    key = 'array';\n  }\n  // if the value is not of a supported type for range validation\n  // the validation rule rule should use the\n  // type property to also test for a particular type\n  if (!key) {\n    return false;\n  }\n  if (arr) {\n    val = value.length;\n  }\n  if (str) {\n    // 处理码点大于U+010000的文字length属性不准确的bug，如\"𠮷𠮷𠮷\".lenght !== 3\n    val = value.replace(spRegexp, '_').length;\n  }\n  if (len) {\n    if (val !== rule.len) {\n      errors.push(util.format(options.messages[key].len, rule.fullField, rule.len));\n    }\n  } else if (min && !max && val < rule.min) {\n    errors.push(util.format(options.messages[key].min, rule.fullField, rule.min));\n  } else if (max && !min && val > rule.max) {\n    errors.push(util.format(options.messages[key].max, rule.fullField, rule.max));\n  } else if (min && max && (val < rule.min || val > rule.max)) {\n    errors.push(util.format(options.messages[key].range, rule.fullField, rule.min, rule.max));\n  }\n}\nvar _default = exports.default = range;", null]}