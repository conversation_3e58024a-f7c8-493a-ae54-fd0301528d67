@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.icon-shitixing.data-v-02c7484c {
  color: #FFBB00 !important;
}
.evaluate-con .score.data-v-02c7484c {
  background-color: #fff;
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #282828;
  padding: 46rpx 24rpx;
}
.evaluate-con .score .item ~ .item.data-v-02c7484c {
  margin-top: 36rpx;
}
.evaluate-con .score .item .starsList.data-v-02c7484c {
  padding: 0 35rpx 0 40rpx;
}
.evaluate-con .score .item .starsList .iconfont.data-v-02c7484c {
  font-size: 40rpx;
  color: #aaa;
}
.evaluate-con .score .item .starsList .iconfont ~ .iconfont.data-v-02c7484c {
  margin-left: 20rpx;
}
.evaluate-con .score .item .evaluate.data-v-02c7484c {
  color: #aaa;
  font-size: 24rpx;
}
.evaluate-con .score .textarea.data-v-02c7484c {
  width: 100%;
  background-color: #F5F5F5;
  border-radius: 14rpx;
  margin-top: 55rpx;
}
.evaluate-con .score .textarea textarea.data-v-02c7484c {
  font-size: 28rpx;
  padding: 38rpx 30rpx 0 30rpx;
  width: 100%;
  box-sizing: border-box;
  height: 160rpx;
  width: auto !important;
}
.evaluate-con .score .textarea .placeholder.data-v-02c7484c {
  color: #bbb;
}
.evaluate-con .score .textarea .list.data-v-02c7484c {
  margin-top: 25rpx;
  padding-left: 5rpx;
}
.evaluate-con .score .textarea .list .pictrue.data-v-02c7484c {
  width: 140rpx;
  height: 140rpx;
  margin: 0 0 35rpx 25rpx;
  position: relative;
  font-size: 22rpx;
  color: #bbb;
  border-radius: 14rpx;
}
.evaluate-con .score .textarea .list .pictrue.data-v-02c7484c:nth-last-child(1) {
  border: 1rpx solid #ddd;
  box-sizing: border-box;
}
.evaluate-con .score .textarea .list .pictrue image.data-v-02c7484c {
  width: 100%;
  height: 100%;
  border-radius: 14rpx;
}
.evaluate-con .score .textarea .list .pictrue .icon-guanbi1.data-v-02c7484c {
  font-size: 45rpx;
  position: absolute;
  top: -20rpx;
  right: -20rpx;
}
.evaluate-con .score .textarea .list .pictrue .icon-icon25201.data-v-02c7484c {
  color: #bfbfbf;
  font-size: 50rpx;
}
.evaluate-con .score .evaluateBnt.data-v-02c7484c {
  font-size: 30rpx;
  color: #fff;
  width: 100%;
  height: 86rpx;
  border-radius: 43rpx;
  text-align: center;
  line-height: 86rpx;
  margin-top: 45rpx;
}

