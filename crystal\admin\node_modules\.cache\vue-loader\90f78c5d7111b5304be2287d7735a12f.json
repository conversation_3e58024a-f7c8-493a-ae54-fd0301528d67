{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\FormGenerator\\index\\IconsDialog.vue?vue&type=template&id=585439f6&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\FormGenerator\\index\\IconsDialog.vue", "mtime": 1753666157770}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\n<div class=\"icon-dialog\">\n  <el-dialog\n    v-bind=\"$attrs\"\n    width=\"980px\"\n    :modal-append-to-body=\"false\"\n    v-on=\"$listeners\"\n    @open=\"onOpen\"\n    @close=\"onClose\"\n  >\n    <div slot=\"title\">\n      选择图标\n      <el-input\n        v-model=\"key\"\n        size=\"mini\"\n        :style=\"{width: '260px'}\"\n        placeholder=\"请输入图标名称\"\n        prefix-icon=\"el-icon-search\"\n        clearable\n      />\n    </div>\n    <ul class=\"icon-ul\">\n      <li\n        v-for=\"icon in iconList\"\n        :key=\"icon\"\n        :class=\"active===icon?'active-item':''\"\n        @click=\"onSelect(icon)\"\n      >\n        <i :class=\"icon\" />\n        <div>{{ icon }}</div>\n      </li>\n    </ul>\n  </el-dialog>\n</div>\n", null]}