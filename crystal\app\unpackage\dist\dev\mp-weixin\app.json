{"pages": ["pages/index/index", "pages/order_addcart/order_addcart", "pages/user/index", "pages/goods_details/index", "pages/goods_cate/goods_cate", "pages/bracelets/index", "pages/bracelets/axureIndex", "pages/mailun/index", "pages/retrieve_password/index", "pages/goods_list/index", "pages/news_list/index", "pages/news_details/index", "pages/goods_search/index", "pages/order_pay_status/index", "pages/order_details/index", "pages/index/components/a_seckill", "pages/index/components/b_combination"], "subPackages": [{"root": "pages/users", "pages": ["privacy/index", "web_page/index", "retrievePassword/index", "user_info/index", "user_get_coupon/index", "user_goods_collection/index", "user_sgin/index", "user_sgin_list/index", "user_money/index", "user_bill/index", "user_integral/index", "user_coupon/index", "user_spread_user/index", "user_spread_code/index", "user_spread_money/index", "user_cash/index", "user_vip/index", "user_address_list/index", "user_address/index", "user_phone/index", "user_payment/index", "user_pwd_edit/index", "order_confirm/index", "goods_details_store/index", "promoter-list/index", "promoter-order/index", "promoter_rank/index", "commission_rank/index", "order_list/index", "goods_logistics/index", "user_return_list/index", "goods_return/index", "login/index", "goods_comment_list/index", "goods_comment_con/index", "wechat_login/index", "app_login/index"], "name": "users"}, {"root": "pages/activity", "pages": ["goods_bargain/index", "goods_bargain_details/index", "goods_combination/index", "goods_combination_details/index", "goods_combination_status/index", "goods_seckill/index", "goods_seckill_details/index", "poster-poster/index", "bargain/index"], "name": "activity"}, {"root": "pages/columnGoods", "pages": ["HotNewGoods/index"], "name": "columnGoods"}, {"root": "pages/bracelets/child", "pages": ["list", "edit"], "name": "braceletschild"}, {"root": "pages/mailun/child", "pages": ["start", "detail", "list", "mailundakai", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "name": "mailunchild"}], "window": {"navigationBarTextStyle": "black", "navigationBarBackgroundColor": "#c9ab79", "backgroundColor": "#F8F8F8", "titleNView": false, "rpxCalcMaxDeviceWidth": 960, "rpxCalcBaseDeviceWidth": 375, "rpxCalcIncludeWidth": 750}, "tabBar": {"color": "#282828", "selectedColor": "#c9ab79", "borderStyle": "white", "backgroundColor": "#ffffff", "list": [{"pagePath": "pages/index/index", "iconPath": "static/images/1-001.png", "selectedIconPath": "static/images/1-002.png", "text": "首页"}, {"pagePath": "pages/mailun/index", "iconPath": "static/images/tab_icon_ml_pre.png", "selectedIconPath": "static/images/tab_icon_ml.png", "text": "脉轮测试"}, {"pagePath": "pages/bracelets/axureIndex", "iconPath": "static/images/tab_icon_diy_pre.png", "selectedIconPath": "static/images/tab_icon_diy.png", "text": "AI手串"}, {"pagePath": "pages/order_addcart/order_addcart", "iconPath": "static/images/3-001.png", "selectedIconPath": "static/images/3-002.png", "text": "购物车"}, {"pagePath": "pages/user/index", "iconPath": "static/images/4-001.png", "selectedIconPath": "static/images/4-002.png", "text": "我的"}]}, "permission": {"scope.userLocation": {"desc": "你的位置信息将用于和门店的距离长度"}}, "usingComponents": {}}