{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\distribution\\config\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\distribution\\config\\index.vue", "mtime": 1753666157867}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\"use strict\";\n\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _distribution = require(\"@/api/distribution\");\nvar selfUtil = _interopRequireWildcard(require(\"@/utils/ZBKJIutil.js\"));\nvar _permission = require(\"@/utils/permission\");\nvar _validate = require(\"@/utils/validate\");\nfunction _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != _typeof(e) && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n// 权限判断函数\nvar _default = exports.default = {\n  name: 'Index',\n  data: function data() {\n    return {\n      promoterForm: {},\n      loading: true,\n      rules: {\n        brokerageFuncStatus: [{\n          required: true,\n          message: '请选择是否启用分销',\n          trigger: 'change'\n        }],\n        storeBrokerageRatio: [{\n          required: true,\n          message: '请输入一级返佣比例',\n          trigger: 'blur'\n        }],\n        storeBrokerageTwo: [{\n          required: true,\n          message: '请输入二级返佣比例',\n          trigger: 'blur'\n        }]\n      }\n    };\n  },\n  mounted: function mounted() {\n    this.getDetal();\n  },\n  methods: {\n    checkPermi: _permission.checkPermi,\n    channelInputLimit: function channelInputLimit(e) {\n      var key = e.key;\n      // 不允许输入'e'和'.'\n      if (key === 'e' || key === '.') {\n        e.returnValue = false;\n        return false;\n      }\n      return true;\n    },\n    getDetal: function getDetal() {\n      var _this = this;\n      this.loading = true;\n      (0, _distribution.configApi)().then(function (res) {\n        _this.loading = false;\n        _this.promoterForm = res;\n        _this.promoterForm.storeBrokerageIsBubble = res.storeBrokerageIsBubble.toString();\n        _this.promoterForm.brokerageFuncStatus = res.brokerageFuncStatus.toString();\n        _this.promoterForm.brokerageBindind = res.brokerageBindind.toString();\n      }).catch(function (res) {\n        _this.$message.error(res.message);\n      });\n    },\n    submitForm: (0, _validate.Debounce)(function (formName) {\n      var _this2 = this;\n      this.$refs[formName].validate(function (valid) {\n        if (valid) {\n          if (selfUtil.Add(_this2.promoterForm.storeBrokerageRatio, _this2.promoterForm.storeBrokerageTwo) > 100) return _this2.$message.warning('返佣比例相加不能超过100%');\n          _this2.loading = true;\n          (0, _distribution.configUpdateApi)(_this2.promoterForm).then(function (res) {\n            _this2.loading = false;\n            _this2.$message.success('提交成功');\n            // this.$modalSure('提交成功，是否自动下架商户低于此佣金比例的商品').then(() => {\n            //   productCheckApi().then(({ message }) => {\n            //     this.$message.success(message)\n            //   }).catch(({ message }) => {\n            //     this.$message.error(message)\n            //   })\n            // })\n          }).catch(function (err) {\n            _this2.loading = false;\n          });\n        } else {\n          return false;\n        }\n      });\n    })\n  }\n};", null]}