{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\marketing\\coupon\\list\\creatCoupon.vue?vue&type=template&id=945b1358&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\marketing\\coupon\\list\\creatCoupon.vue", "mtime": 1753666157891}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\n<div class=\"divBox\">\n  <el-card class=\"box-card\">\n    <el-form :model=\"ruleForm\" :rules=\"rules\" ref=\"ruleForm\" label-width=\"150px\" class=\"demo-ruleForm\">\n      <el-form-item label=\"优惠劵名称\" prop=\"name\">\n        <el-input v-model=\"ruleForm.name\" style=\"width: 350px\" placeholder=\"请输入优惠券名称\"></el-input>\n      </el-form-item>\n      <el-form-item label=\"优惠劵类型\">\n        <el-radio-group v-model=\"ruleForm.useType\">\n          <el-radio :label=\"1\">通用券</el-radio>\n          <el-radio :label=\"2\">商品券</el-radio>\n          <el-radio :label=\"3\">品类券</el-radio>\n        </el-radio-group>\n      </el-form-item>\n      <el-form-item label=\"选择品类：\" prop=\"primaryKey\" v-if=\"ruleForm.useType === 3\">\n        <el-cascader v-model=\"ruleForm.primaryKey\" :options=\"merCateList\" :props=\"props2\" clearable class=\"selWidth\" :show-all-levels=\"false\" />\n      </el-form-item>\n      <el-form-item label=\"商品：\" v-if=\"ruleForm.useType === 2\" prop=\"checked\">\n        <div class=\"acea-row\">\n          <template v-if=\"ruleForm.checked.length\">\n            <div class=\"pictrue\" v-for=\"(item, index) in ruleForm.checked\" :key=\"index\">\n              <img :src=\"item.image\">\n              <i class=\"el-icon-error btndel\" @click=\"handleRemove(index)\" />\n            </div>\n          </template>\n          <div class=\"upLoadPicBox\" @click=\"changeGood\">\n            <div class=\"upLoad\">\n              <i class=\"el-icon-camera cameraIconfont\" />\n            </div>\n          </div>\n        </div>\n      </el-form-item>\n      <el-form-item label=\"优惠券面值\" prop=\"money\">\n        <el-input-number v-model=\"ruleForm.money\" :min=\"1\" label=\"描述文字\"></el-input-number>\n      </el-form-item>\n      <el-form-item label=\"使用门槛\">\n        <el-radio-group v-model=\"threshold\">\n          <el-radio :label=\"false\">无门槛</el-radio>\n          <el-radio :label=\"true\">有门槛</el-radio>\n        </el-radio-group>\n      </el-form-item>\n      <el-form-item label=\"优惠券最低消费\" prop=\"minPrice\" v-if=\"threshold\">\n        <el-input-number v-model=\"ruleForm.minPrice\" :min=\"1\" label=\"描述文字\"></el-input-number>\n      </el-form-item>\n      <el-form-item label=\"使用有效期\">\n        <el-radio-group v-model=\"ruleForm.isFixedTime\">\n          <el-radio :label=\"false\">天数</el-radio>\n          <el-radio :label=\"true\">时间段</el-radio>\n        </el-radio-group>\n      </el-form-item>\n      <el-form-item label=\"使用有效期限（天）\" prop=\"day\" v-if=\"!ruleForm.isFixedTime\">\n        <el-input-number v-model=\"ruleForm.day\" :min=\"0\" :max=\"999\" label=\"描述文字\"></el-input-number>\n      </el-form-item>\n      <el-form-item label=\"使用有效期限\" prop=\"resource\" v-if=\"ruleForm.isFixedTime\">\n        <el-date-picker\n          style=\"width: 550px\"\n          v-model=\"termTime\"\n          type=\"datetimerange\"\n          range-separator=\"至\"\n          value-format=\"yyyy-MM-dd HH:mm:ss\"\n          start-placeholder=\"开始日期\"\n          :picker-options=\"pickerOptions\"\n          end-placeholder=\"结束日期\">\n        </el-date-picker>\n      </el-form-item>\n      <el-form-item label=\"领取是否限时\" prop=\"isForever\">\n        <el-radio-group v-model=\"ruleForm.isForever\">\n          <el-radio :label=\"true\">限时</el-radio>\n          <el-radio :label=\"false\">不限时</el-radio>\n        </el-radio-group>\n      </el-form-item>\n      <el-form-item label=\"领取时间\" v-if=\"ruleForm.isForever\">\n        <el-date-picker\n          style=\"width: 550px\"\n          v-model=\"isForeverTime\"\n          type=\"datetimerange\"\n          range-separator=\"至\"\n          value-format=\"yyyy-MM-dd HH:mm:ss\"\n          :picker-options=\"pickerOptions\"\n          start-placeholder=\"开始日期\"\n          end-placeholder=\"结束日期\"\n          @blur=\"handleTimestamp\">\n        </el-date-picker>\n      </el-form-item>\n      <el-form-item label=\"领取方式\" prop=\"resource\">\n        <el-radio-group v-model=\"ruleForm.type\">\n          <el-radio :label=\"1\">手动领取</el-radio>\n          <el-radio :label=\"2\">新人券</el-radio>\n          <el-radio :label=\"3\">赠送券</el-radio>\n          <!--<el-radio :label=\"4\">付费会员卷</el-radio>-->\n        </el-radio-group>\n      </el-form-item>\n      <el-form-item label=\"是否限量\" prop=\"isLimited\">\n        <el-radio-group v-model=\"ruleForm.isLimited\">\n          <el-radio :label=\"true\">限量</el-radio>\n          <el-radio :label=\"false\">不限量</el-radio>\n        </el-radio-group>\n      </el-form-item>\n      <el-form-item label=\"发布数量\" prop=\"total\" v-if=\"ruleForm.isLimited\">\n        <el-input-number v-model=\"ruleForm.total\" :min=\"1\" label=\"排序\"></el-input-number>\n      </el-form-item>\n      <el-form-item label=\"排序\" prop=\"sort\">\n        <el-input-number v-model=\"ruleForm.sort\" :min=\"0\" label=\"排序\"></el-input-number>\n      </el-form-item>\n      <el-form-item label=\"状态\" prop=\"status\">\n        <el-radio-group v-model=\"ruleForm.status\">\n          <el-radio :label=\"true\">开启</el-radio>\n          <el-radio :label=\"false\">关闭</el-radio>\n        </el-radio-group>\n      </el-form-item>\n      <el-form-item>\n        <el-button size=\"mini\" type=\"primary\" @click=\"submitForm('ruleForm')\" :loading=\"loading\" v-hasPermi=\"['admin:coupon:save']\">立即创建</el-button>\n        <!--<el-button @click=\"resetForm('ruleForm')\">重置</el-button>-->\n      </el-form-item>\n    </el-form>\n  </el-card>\n</div>\n", null]}