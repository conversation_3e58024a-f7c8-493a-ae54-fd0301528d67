<!DOCTYPE html>
<html lang=zh-CN>
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width,user-scalable=no,initial-scale=1,maximum-scale=1,minimum-scale=1">
        <title>crystal</title>
        <meta name="Copyright" content="helang">
       <!-- <link rel="shortcut icon" type="image/png" href="/uniapp-extend/static/logo.png"> -->
        <meta name="keywords" content="crystal">
        <meta name="description" content="crystal">
        <style type="text/css">
            body{
                margin: 0;
                background-color:  #f5f5f5;
            }
            iframe{
                width: 375px;
                height: 667px;
                background-color: #fff;
                /* box-sizing: content-box; */
				box-sizing: border-box;
                border: none;
            }
			
            @media screen and (min-width: 450px) {
                iframe {
                    position: fixed;
                    top: 0;
                    right: 0;
                    bottom: 0;
                    left: 0;
                    margin: auto;
					border: 1px solid #f5f5f5;
                    border-radius: 4px;
                }
            }
        </style>
        <script type="text/javascript">
            window.isPC = true;
            window.onload = function(){
                /* 监听电脑浏览器窗口尺寸改变 */
				if(window.innerWidth <= 420){
					window.location.pathname = '/';
				}
            }
        </script>
    </head>
    <body>
       <!--若你的项目未设置根目录（默认为 / 时），则使用下方代码-->
		<iframe src="/" id="iframe"></iframe>
		<script src="https://pv.sohu.com/cityjson?ie=utf-8"></script>
		<script type="text/javascript">
		      var Ip=returnCitySN['cip']
		      localStorage.setItem('Ip', Ip)
		</script>
    </body>
</html>