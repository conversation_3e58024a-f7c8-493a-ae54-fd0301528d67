{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\list\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\list\\index.vue", "mtime": 1753666157941}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _user = require(\"@/api/user\");\nvar _distribution = require(\"@/api/distribution\");\nvar _edit = _interopRequireDefault(require(\"./edit\"));\nvar _userDetails = _interopRequireDefault(require(\"./userDetails\"));\nvar _level = _interopRequireDefault(require(\"./level\"));\nvar _userList = _interopRequireDefault(require(\"@/components/userList\"));\nvar logistics = _interopRequireWildcard(require(\"@/api/logistics.js\"));\nvar _jsCookie = _interopRequireDefault(require(\"js-cookie\"));\nvar _permission = require(\"@/utils/permission\");\nvar _validate = require(\"@/utils/validate\");\nfunction _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != _typeof(e) && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _regenerator() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */ var e, t, r = \"function\" == typeof Symbol ? Symbol : {}, n = r.iterator || \"@@iterator\", o = r.toStringTag || \"@@toStringTag\"; function i(r, n, o, i) { var c = n && n.prototype instanceof Generator ? n : Generator, u = Object.create(c.prototype); return _regeneratorDefine2(u, \"_invoke\", function (r, n, o) { var i, c, u, f = 0, p = o || [], y = !1, G = { p: 0, n: 0, v: e, a: d, f: d.bind(e, 4), d: function d(t, r) { return i = t, c = 0, u = e, G.n = r, a; } }; function d(r, n) { for (c = r, u = n, t = 0; !y && f && !o && t < p.length; t++) { var o, i = p[t], d = G.p, l = i[2]; r > 3 ? (o = l === n) && (u = i[(c = i[4]) ? 5 : (c = 3, 3)], i[4] = i[5] = e) : i[0] <= d && ((o = r < 2 && d < i[1]) ? (c = 0, G.v = n, G.n = i[1]) : d < l && (o = r < 3 || i[0] > n || n > l) && (i[4] = r, i[5] = n, G.n = l, c = 0)); } if (o || r > 1) return a; throw y = !0, n; } return function (o, p, l) { if (f > 1) throw TypeError(\"Generator is already running\"); for (y && 1 === p && d(p, l), c = p, u = l; (t = c < 2 ? e : u) || !y;) { i || (c ? c < 3 ? (c > 1 && (G.n = -1), d(c, u)) : G.n = u : G.v = u); try { if (f = 2, i) { if (c || (o = \"next\"), t = i[o]) { if (!(t = t.call(i, u))) throw TypeError(\"iterator result is not an object\"); if (!t.done) return t; u = t.value, c < 2 && (c = 0); } else 1 === c && (t = i.return) && t.call(i), c < 2 && (u = TypeError(\"The iterator does not provide a '\" + o + \"' method\"), c = 1); i = e; } else if ((t = (y = G.n < 0) ? u : r.call(n, G)) !== a) break; } catch (t) { i = e, c = 1, u = t; } finally { f = 1; } } return { value: t, done: y }; }; }(r, o, i), !0), u; } var a = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} t = Object.getPrototypeOf; var c = [][n] ? t(t([][n]())) : (_regeneratorDefine2(t = {}, n, function () { return this; }), t), u = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c); function f(e) { return Object.setPrototypeOf ? Object.setPrototypeOf(e, GeneratorFunctionPrototype) : (e.__proto__ = GeneratorFunctionPrototype, _regeneratorDefine2(e, o, \"GeneratorFunction\")), e.prototype = Object.create(u), e; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, _regeneratorDefine2(u, \"constructor\", GeneratorFunctionPrototype), _regeneratorDefine2(GeneratorFunctionPrototype, \"constructor\", GeneratorFunction), GeneratorFunction.displayName = \"GeneratorFunction\", _regeneratorDefine2(GeneratorFunctionPrototype, o, \"GeneratorFunction\"), _regeneratorDefine2(u), _regeneratorDefine2(u, o, \"Generator\"), _regeneratorDefine2(u, n, function () { return this; }), _regeneratorDefine2(u, \"toString\", function () { return \"[object Generator]\"; }), (_regenerator = function _regenerator() { return { w: i, m: f }; })(); }\nfunction _regeneratorDefine2(e, r, n, t) { var i = Object.defineProperty; try { i({}, \"\", {}); } catch (e) { i = 0; } _regeneratorDefine2 = function _regeneratorDefine(e, r, n, t) { function o(r, n) { _regeneratorDefine2(e, r, function (e) { return this._invoke(r, n, e); }); } r ? i ? i(e, r, { value: n, enumerable: !t, configurable: !t, writable: !t }) : e[r] = n : (o(\"next\", 0), o(\"throw\", 1), o(\"return\", 2)); }, _regeneratorDefine2(e, r, n, t); }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); } //\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n// 权限判断函数\nvar _default = exports.default = {\n  name: 'UserIndex',\n  components: {\n    editFrom: _edit.default,\n    userDetails: _userDetails.default,\n    userList: _userList.default,\n    levelEdit: _level.default\n  },\n  filters: {\n    sexFilter: function sexFilter(status) {\n      var statusMap = {\n        0: '未知',\n        1: '男',\n        2: '女',\n        3: '保密'\n      };\n      return statusMap[status];\n    }\n  },\n  data: function data() {\n    return _defineProperty({\n      formExtension: {\n        image: '',\n        spreadUid: '',\n        userId: ''\n      },\n      ruleInline: {},\n      extensionVisible: false,\n      userVisible: false,\n      levelInfo: '',\n      pickerOptions: this.$timeOptions,\n      loadingBtn: false,\n      PointValidateForm: {\n        integralType: 2,\n        integralValue: 0,\n        moneyType: 2,\n        moneyValue: 0,\n        uid: ''\n      },\n      loadingPoint: false,\n      VisiblePoint: false,\n      visible: false,\n      userIds: '',\n      dialogVisible: false,\n      levelVisible: false,\n      levelData: [],\n      groupData: [],\n      labelData: [],\n      selData: [],\n      labelPosition: 'right',\n      collapse: false,\n      props: {\n        children: 'child',\n        label: 'name',\n        value: 'name',\n        emitPath: false\n      },\n      propsCity: {\n        children: 'child',\n        label: 'name',\n        value: 'name'\n      },\n      headeNum: [{\n        'type': '',\n        'name': '全部用户'\n      }, {\n        'type': 'wechat',\n        'name': '微信公众号用户'\n      }, {\n        'type': 'routine',\n        'name': '微信小程序用户'\n      }, {\n        'type': 'h5',\n        'name': 'H5用户'\n      }],\n      listLoading: true,\n      tableData: {\n        data: [],\n        total: 0\n      },\n      loginType: '',\n      userFrom: {\n        labelId: '',\n        userType: '',\n        sex: '',\n        isPromoter: '',\n        country: '',\n        payCount: '',\n        accessType: 0,\n        dateLimit: '',\n        keywords: '',\n        province: '',\n        city: '',\n        page: 1,\n        limit: 15,\n        level: '',\n        groupId: ''\n      },\n      grid: {\n        xl: 8,\n        lg: 12,\n        md: 12,\n        sm: 24,\n        xs: 24\n      },\n      levelList: [],\n      labelLists: [],\n      groupList: [],\n      selectedData: [],\n      timeVal: [],\n      addresData: [],\n      dynamicValidateForm: {\n        groupId: []\n      },\n      loading: false,\n      groupIdFrom: [],\n      selectionList: [],\n      batchName: '',\n      uid: 0,\n      Visible: false,\n      keyNum: 0,\n      address: [],\n      multipleSelectionAll: [],\n      idKey: 'uid'\n    }, \"uid\", '');\n  },\n  activated: function activated() {\n    this.userFrom.keywords = '';\n    this.loginType = '0';\n    this.getList(1);\n  },\n  mounted: function mounted() {\n    this.getList();\n    this.groupLists();\n    this.levelLists();\n    this.getTagList();\n    this.getCityList();\n  },\n  methods: {\n    checkPermi: _permission.checkPermi,\n    setPhone: function setPhone(row) {\n      var _this2 = this;\n      this.$prompt('修改手机号', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        inputErrorMessage: '请输入修改手机号',\n        inputType: 'text',\n        inputValue: row.phone,\n        inputPlaceholder: '请输入手机号',\n        closeOnClickModal: false,\n        inputValidator: function inputValidator(value) {\n          if (!value) return '请填写手机号';\n          // if (!/^1[3456789]\\d{9}$/.test(value)) return '手机号格式不正确!'\n          // if(!value) return '输入不能为空'\n        }\n      }).then(function (_ref2) {\n        var value = _ref2.value;\n        (0, _user.updatePhoneApi)({\n          id: row.uid,\n          phone: value\n        }).then(function () {\n          _this2.$message.success('编辑成功');\n          _this2.getList();\n        });\n      }).catch(function () {\n        _this2.$message.info('取消输入');\n      });\n    },\n    // 清除\n    clearSpread: function clearSpread(row) {\n      var _this3 = this;\n      this.$modalSure('解除【' + row.nickname + '】的上级推广人吗').then(function () {\n        (0, _distribution.spreadClearApi)(row.uid).then(function (res) {\n          _this3.$message.success('清除成功');\n          _this3.getList();\n        });\n      });\n    },\n    onSubExtension: function onSubExtension(formName) {\n      var _this4 = this;\n      this.$refs[formName].validate(function (valid) {\n        if (valid) {\n          (0, _user.updateSpreadApi)(_this4.formExtension).then(function (res) {\n            _this4.$message.success('设置成功');\n            _this4.extensionVisible = false;\n            _this4.getList();\n          });\n        } else {\n          return false;\n        }\n      });\n    },\n    getTemplateRow: function getTemplateRow(row) {\n      this.formExtension.image = row.avatar;\n      this.formExtension.spreadUid = row.uid;\n    },\n    setExtension: function setExtension(row) {\n      this.formExtension = {\n        image: '',\n        spreadUid: '',\n        userId: row.uid\n      };\n      this.extensionVisible = true;\n    },\n    handleCloseExtension: function handleCloseExtension() {\n      this.extensionVisible = false;\n    },\n    modalPicTap: function modalPicTap() {\n      this.userVisible = true;\n    },\n    resetForm: function resetForm() {\n      this.visible = false;\n    },\n    reset: function reset(formName) {\n      this.userFrom = {\n        labelId: '',\n        userType: '',\n        sex: '',\n        isPromoter: '',\n        country: '',\n        payCount: '',\n        accessType: 0,\n        dateLimit: '',\n        keywords: '',\n        province: '',\n        city: '',\n        page: 1,\n        limit: 15,\n        level: '',\n        groupId: ''\n      };\n      this.levelData = [];\n      this.groupData = [];\n      this.labelData = [];\n      this.timeVal = [];\n      this.getList();\n    },\n    // 列表\n    getCityList: function () {\n      var _getCityList = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee() {\n        var res;\n        return _regenerator().w(function (_context) {\n          while (1) switch (_context.n) {\n            case 0:\n              _context.n = 1;\n              return logistics.cityListTree();\n            case 1:\n              res = _context.v;\n              //res.forEach((el, index) => {\n              //     el.child.forEach((cel, j) => {\n              //       delete cel.child\n              //     })\n              //   }) \n              this.addresData = res;\n              // })\n            case 2:\n              return _context.a(2);\n          }\n        }, _callee, this);\n      }));\n      function getCityList() {\n        return _getCityList.apply(this, arguments);\n      }\n      return getCityList;\n    }(),\n    // 发送文章\n    sendNews: function sendNews() {\n      if (this.selectionList.length === 0) return this.$message.warning('请先选择用户');\n      var _this = this;\n      this.$modalArticle(function (row) {}, 'send');\n    },\n    // 发送优惠劵\n    onSend: function onSend() {\n      if (this.selectionList.length === 0) return this.$message.warning('请选择要设置的用户');\n      var _this = this;\n      this.$modalCoupon('send', this.keyNum += 1, [], function (row) {\n        _this.formValidate.give_coupon_ids = [];\n        _this.couponData = [];\n        row.map(function (item) {\n          _this.formValidate.give_coupon_ids.push(item.coupon_id);\n          _this.couponData.push(item.title);\n        });\n        _this.selectionList = [];\n      }, this.userIds, 'user');\n    },\n    Close: function Close() {\n      this.Visible = false;\n      this.levelVisible = false;\n    },\n    // 账户详情\n    onDetails: function onDetails(id) {\n      this.uid = id;\n      this.Visible = true;\n    },\n    // 等级\n    onLevel: function onLevel(id, level) {\n      var userLevel = new Object();\n      this.levelList.forEach(function (item) {\n        if (item.id == level) {\n          userLevel.gradeLevel = item.grade;\n        }\n      });\n      userLevel.uid = id;\n      userLevel.level = level;\n      this.levelInfo = userLevel;\n      this.levelVisible = true;\n    },\n    // 积分余额\n    editPoint: function editPoint(id) {\n      this.uid = id;\n      this.VisiblePoint = true;\n    },\n    // 积分余额\n    submitPointForm: (0, _validate.Debounce)(function (formName) {\n      var _this5 = this;\n      this.$refs[formName].validate(function (valid) {\n        if (valid) {\n          _this5.PointValidateForm.uid = _this5.uid;\n          _this5.loadingBtn = true;\n          (0, _user.foundsApi)(_this5.PointValidateForm).then(function (res) {\n            _this5.$message.success('设置成功');\n            _this5.loadingBtn = false;\n            _this5.handlePointClose();\n            _this5.getList();\n          }).catch(function () {\n            _this5.loadingBtn = false;\n          });\n        } else {\n          return false;\n        }\n      });\n    }),\n    // 积分余额\n    handlePointClose: function handlePointClose() {\n      this.VisiblePoint = false;\n      this.PointValidateForm = {\n        integralType: 2,\n        integralValue: 0,\n        moneyType: 2,\n        moneyValue: 0,\n        uid: ''\n      };\n    },\n    editUser: function editUser(id) {\n      this.uid = id;\n      this.visible = true;\n    },\n    goprocess: function goprocess(id) {\n      this.$router.push({\n        path: '/user/process',\n        query: {\n          id: id\n        }\n      });\n    },\n    submitForm: function submitForm(formName) {\n      var _this6 = this;\n      // let data = [];\n      // if(!this.userIds){\n      //   if(this.multipleSelectionAll.length){\n      //     this.multipleSelectionAll.map((item) => {\n      //       data.push(item.uid)\n      //     });\n      //     this.userIds = data.join(',');\n      //   }\n      // }\n      this.$refs[formName].validate(function (valid) {\n        if (valid) {\n          _this6.loading = true;\n          _this6.batchName === 'group' ? (0, _user.groupPiApi)({\n            groupId: _this6.dynamicValidateForm.groupId,\n            id: _this6.userIds\n          }).then(function (res) {\n            _this6.$message.success('设置成功');\n            _this6.loading = false;\n            _this6.handleClose();\n            _this6.getList();\n          }).catch(function () {\n            _this6.loading = false;\n          }) : (0, _user.tagPiApi)({\n            tagId: _this6.dynamicValidateForm.groupId.join(','),\n            id: _this6.userIds\n          }).then(function (res) {\n            _this6.$message.success('设置成功');\n            _this6.loading = false;\n            _this6.handleClose();\n            _this6.getList();\n          }).catch(function () {\n            _this6.loading = false;\n          });\n        } else {\n          return false;\n        }\n      });\n    },\n    setBatch: function setBatch(name, row) {\n      this.batchName = name;\n      if (row) {\n        this.userIds = row.uid;\n        if (this.batchName === 'group') {\n          this.dynamicValidateForm.groupId = row.groupId ? Number(row.groupId) : '';\n        } else {\n          this.dynamicValidateForm.groupId = row.tagId ? row.tagId.split(',').map(Number) : [];\n        }\n      } else {\n        this.dynamicValidateForm.groupId = '';\n      }\n      if (this.multipleSelectionAll.length === 0 && !row) return this.$message.warning('请选择要设置的用户');\n      this.dialogVisible = true;\n    },\n    handleClose: function handleClose() {\n      this.dialogVisible = false;\n      this.$refs['dynamicValidateForm'].resetFields();\n    },\n    // 全选\n    onSelectTab: function onSelectTab(selection) {\n      var _this7 = this;\n      this.selectionList = selection;\n      setTimeout(function () {\n        _this7.changePageCoreRecordData();\n        var data = [];\n        if (_this7.multipleSelectionAll.length) {\n          _this7.multipleSelectionAll.map(function (item) {\n            data.push(item.uid);\n          });\n          _this7.userIds = data.join(',');\n        }\n      }, 50);\n    },\n    // 搜索\n    userSearchs: function userSearchs() {\n      this.userFrom.page = 1;\n      this.getList();\n    },\n    // 选择国家\n    changeCountry: function changeCountry() {\n      if (this.userFrom.country === 'OTHER' || !this.userFrom.country) {\n        this.selectedData = [];\n        this.userFrom.province = '';\n        this.userFrom.city = '';\n        this.address = [];\n      }\n    },\n    // 选择地址\n    handleChange: function handleChange(value) {\n      this.userFrom.province = value[0];\n      this.userFrom.city = value[1];\n    },\n    // 具体日期\n    onchangeTime: function onchangeTime(e) {\n      this.timeVal = e;\n      this.userFrom.dateLimit = e ? this.timeVal.join(',') : '';\n    },\n    // 分组列表\n    groupLists: function groupLists() {\n      var _this8 = this;\n      (0, _user.groupListApi)({\n        page: 1,\n        limit: 9999\n      }).then(/*#__PURE__*/function () {\n        var _ref3 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee2(res) {\n          return _regenerator().w(function (_context2) {\n            while (1) switch (_context2.n) {\n              case 0:\n                _this8.groupList = res.list;\n              case 1:\n                return _context2.a(2);\n            }\n          }, _callee2);\n        }));\n        return function (_x) {\n          return _ref3.apply(this, arguments);\n        };\n      }());\n    },\n    //标签列表\n    getTagList: function getTagList() {\n      var _this9 = this;\n      (0, _user.tagListApi)({\n        page: 1,\n        limit: 9999\n      }).then(function (res) {\n        _this9.labelLists = res.list;\n      });\n    },\n    // 等级列表\n    levelLists: function levelLists() {\n      var _this0 = this;\n      (0, _user.levelListApi)().then(/*#__PURE__*/function () {\n        var _ref4 = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee3(res) {\n          return _regenerator().w(function (_context3) {\n            while (1) switch (_context3.n) {\n              case 0:\n                _this0.levelList = res;\n                localStorage.setItem('levelKey', JSON.stringify(res));\n              case 1:\n                return _context3.a(2);\n            }\n          }, _callee3);\n        }));\n        return function (_x2) {\n          return _ref4.apply(this, arguments);\n        };\n      }());\n    },\n    // 列表\n    getList: function getList(num) {\n      var _this1 = this;\n      this.listLoading = true;\n      this.userFrom.page = num ? num : this.userFrom.page;\n      this.userFrom.userType = this.loginType;\n      if (this.loginType == 0) this.userFrom.userType = '';\n      this.userFrom.level = this.levelData.join(',');\n      this.userFrom.groupId = this.groupData.join(',');\n      this.userFrom.labelId = this.labelData.join(',');\n      (0, _user.userListApi)(this.userFrom).then(function (res) {\n        _this1.tableData.data = res.list;\n        _this1.tableData.total = res.total;\n        _this1.$nextTick(function () {\n          this.setSelectRow(); // 调用跨页选中方法\n        });\n        _this1.listLoading = false;\n      }).catch(function () {\n        _this1.listLoading = false;\n      });\n      this.checkedCities = this.$cache.local.has('user_stroge') ? this.$cache.local.getJSON('user_stroge') : this.checkedCities;\n    },\n    // 设置选中的方法\n    setSelectRow: function setSelectRow() {\n      if (!this.multipleSelectionAll || this.multipleSelectionAll.length <= 0) {\n        return;\n      }\n      // 标识当前行的唯一键的名称\n      var idKey = this.idKey;\n      var selectAllIds = [];\n      this.multipleSelectionAll.forEach(function (row) {\n        selectAllIds.push(row[idKey]);\n      });\n      this.$refs.table.clearSelection();\n      for (var i = 0; i < this.tableData.data.length; i++) {\n        if (selectAllIds.indexOf(this.tableData.data[i][idKey]) >= 0) {\n          // 设置选中，记住table组件需要使用ref=\"table\"\n          this.$refs.table.toggleRowSelection(this.tableData.data[i], true);\n        }\n      }\n    },\n    // 记忆选择核心方法\n    changePageCoreRecordData: function changePageCoreRecordData() {\n      // 标识当前行的唯一键的名称\n      var idKey = this.idKey;\n      var that = this;\n      // 如果总记忆中还没有选择的数据，那么就直接取当前页选中的数据，不需要后面一系列计算\n      if (this.multipleSelectionAll.length <= 0) {\n        this.multipleSelectionAll = this.selectionList;\n        return;\n      }\n      // 总选择里面的key集合\n      var selectAllIds = [];\n      this.multipleSelectionAll.forEach(function (row) {\n        selectAllIds.push(row[idKey]);\n      });\n      var selectIds = [];\n      // 获取当前页选中的id\n      this.selectionList.forEach(function (row) {\n        selectIds.push(row[idKey]);\n        // 如果总选择里面不包含当前页选中的数据，那么就加入到总选择集合里\n        if (selectAllIds.indexOf(row[idKey]) < 0) {\n          that.multipleSelectionAll.push(row);\n        }\n      });\n      var noSelectIds = [];\n      // 得到当前页没有选中的id\n      this.tableData.data.forEach(function (row) {\n        if (selectIds.indexOf(row[idKey]) < 0) {\n          noSelectIds.push(row[idKey]);\n        }\n      });\n      noSelectIds.forEach(function (uid) {\n        if (selectAllIds.indexOf(uid) >= 0) {\n          for (var i = 0; i < that.multipleSelectionAll.length; i++) {\n            if (that.multipleSelectionAll[i][idKey] == uid) {\n              // 如果总选择中有未被选中的，那么就删除这条\n              that.multipleSelectionAll.splice(i, 1);\n              break;\n            }\n          }\n        }\n      });\n    },\n    pageChange: function pageChange(page) {\n      this.changePageCoreRecordData();\n      this.userFrom.page = page;\n      this.getList();\n    },\n    handleSizeChange: function handleSizeChange(val) {\n      this.changePageCoreRecordData();\n      this.userFrom.limit = val;\n      this.getList();\n    },\n    // 删除\n    handleDelete: function handleDelete(id, idx) {\n      var _this10 = this;\n      this.$modalSure().then(function () {\n        productDeleteApi(id).then(function () {\n          _this10.$message.success('删除成功');\n          _this10.getList();\n        });\n      });\n    },\n    onchangeIsShow: function onchangeIsShow(row) {\n      var _this11 = this;\n      row.isShow ? putOnShellApi(row.id).then(function () {\n        _this11.$message.success('上架成功');\n        _this11.getList();\n      }).catch(function () {\n        row.isShow = !row.isShow;\n      }) : offShellApi(row.id).then(function () {\n        _this11.$message.success('下架成功');\n        _this11.getList();\n      }).catch(function () {\n        row.isShow = !row.isShow;\n      });\n    }\n  }\n};", null]}