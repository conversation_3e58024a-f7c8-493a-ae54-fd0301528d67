<view class="data-v-c28bc500"><view class="sign data-v-c28bc500"><view class="header bg-color data-v-c28bc500"><view class="headerCon acea-row row-between-wrapper data-v-c28bc500"><view class="left acea-row row-between-wrapper data-v-c28bc500"><view class="pictrue data-v-c28bc500"><image src="{{userInfo.avatar}}" class="data-v-c28bc500"></image></view><view class="text data-v-c28bc500"><view class="line1 data-v-c28bc500">{{userInfo.nickname}}</view><view class="integral acea-row data-v-c28bc500"><text class="data-v-c28bc500">{{"积分: "+userInfo.integral}}</text></view></view></view><navigator class="right acea-row row-middle data-v-c28bc500" hover-class="none" url="/pages/users/user_sgin_list/index"><view class="iconfont icon-caidan data-v-c28bc500"></view><view class="data-v-c28bc500">明细</view></navigator></view></view><view class="wrapper data-v-c28bc500"><view class="list acea-row row-between-wrapper data-v-c28bc500"><block wx:for="{{signSystemList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item data-v-c28bc500"><view class="{{['data-v-c28bc500',(index+1===$root.g0?'reward':'')+' '+(sign_index>=index+1?'rewardTxt':'')]}}">{{''+item.title+''}}</view><view class="{{['venus','data-v-c28bc500',(index+1===$root.g1?'reward':'')+' '+(sign_index>=index+1?'venusSelect':'')]}}"></view><view class="{{['num','data-v-c28bc500',item.is_sgin?'on':'']}}">{{"+"+item.integral}}</view></view></block></view><block wx:if="{{signInfo.isDaySign}}"><button class="but bg-color on data-v-c28bc500">已签到</button></block><block wx:else><form report-submit="true" data-event-opts="{{[['submit',[['goSign',['$event']]]]]}}" bindsubmit="__e" class="data-v-c28bc500"><button class="but bg-color data-v-c28bc500" formType="submit">立即签到</button></form></block><view class="lock data-v-c28bc500"></view></view><view class="wrapper wrapper2 data-v-c28bc500"><view class="tip data-v-c28bc500">已累计签到</view><view class="list2 acea-row row-center row-bottom data-v-c28bc500"><view class="item data-v-c28bc500">{{signCount[0]||0}}</view><view class="item data-v-c28bc500">{{signCount[1]||0}}</view><view class="item data-v-c28bc500">{{signCount[2]||0}}</view><view class="item data-v-c28bc500">{{signCount[3]||0}}</view><view class="data data-v-c28bc500">天</view></view><view class="tip2 data-v-c28bc500">{{"据说连续签到第"+day+"天可获得超额积分，一定要坚持签到哦~~~"}}</view><view class="list3 data-v-c28bc500"><block wx:for="{{signList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item acea-row data-v-c28bc500"><view class="data-v-c28bc500"><view class="name line1 data-v-c28bc500">{{item.title}}</view><view class="data data-v-c28bc500">{{item.createDay}}</view></view><view class="num font-color data-v-c28bc500">{{"+"+item.number}}</view></view></block><block wx:if="{{$root.g2>=3}}"><view data-event-opts="{{[['tap',[['goSignList',['$event']]]]]}}" class="loading data-v-c28bc500" bindtap="__e">点击加载更多<text class="iconfont icon-xiangyou data-v-c28bc500"></text></view></block></view></view><view class="{{['signTip','acea-row','row-center-wrapper','data-v-c28bc500',active==true?'on':'']}}"><view class="signTipLight loadingpic data-v-c28bc500"></view><view class="signTipCon data-v-c28bc500"><view class="state data-v-c28bc500">签到成功</view><view class="integral data-v-c28bc500">{{"获得"+integral+"积分"}}</view><view data-event-opts="{{[['tap',[['close',['$event']]]]]}}" class="signTipBnt data-v-c28bc500" bindtap="__e">好的</view></view></view><view class="mask data-v-c28bc500" hidden="{{active==false}}" data-event-opts="{{[['touchmove',[['e0',['$event']]]]]}}" catchtouchmove="__e"></view></view></view>