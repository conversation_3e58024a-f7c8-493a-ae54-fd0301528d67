{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/App.vue?266e", "uni-app:///App.vue", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/App.vue?7e5e", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/App.vue?4adf"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "<PERSON><PERSON>", "prototype", "$util", "util", "$config", "configs", "$Cache", "<PERSON><PERSON>", "$eventHub", "config", "productionTip", "$Order", "Order", "App", "mpType", "app", "store", "$mount", "globalData", "spid", "code", "is<PERSON>ogin", "userInfo", "MyMenus", "windowHeight", "id", "onLaunch", "console", "that", "uni", "success", "Routine", "then", "catch", "mounted", "methods", "onShow", "onHide"], "mappings": ";;;;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AACA;AACA;AACA;AACA;AAAsC;AAAA;AAAA;AAAA;AAPtC;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAQ1DC,YAAG,CAACC,SAAS,CAACC,KAAK,GAAGC,aAAI;AAC1BH,YAAG,CAACC,SAAS,CAACG,OAAO,GAAGC,YAAO;AAC/BL,YAAG,CAACC,SAAS,CAACK,MAAM,GAAGC,cAAK;AAC5BP,YAAG,CAACC,SAAS,CAACO,SAAS,GAAG,IAAIR,YAAG,EAAE;AACnCA,YAAG,CAACS,MAAM,CAACC,aAAa,GAAG,KAAK;AAChCV,YAAG,CAACC,SAAS,CAACU,MAAM,GAAGC,KAAK;AAwC5BC,YAAG,CAACC,MAAM,GAAG,KAAK;AAGlB,IAAMC,GAAG,GAAG,IAAIf,YAAG,iCACZa,YAAG;EACTG,KAAK,EAALA,cAAK;EACLT,KAAK,EAALA;AAAK,GACJ;AACF,UAAAQ,GAAG,EAACE,MAAM,EAAE,C;;;;;;;;;;;;;AC/DZ;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACuD;AACL;AACa;;;AAG/D;AAC0L;AAC1L,gBAAgB,2LAAU;AAC1B,EAAE,yEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAmtB,CAAgB,mrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACCvuB;AAGA;AAGA;AACA;AACA;AACA;AAEA,eAEA;EACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACA;IAgBA;MACAC,cACA,+HACA;MACA;IACA;IACA;MACA;QACA;QACA;QACA;QACA;UAAA;UACA;UACA;UACA;YACA;YACA;cACAC;YACA;cACAA;YACA;YACA;YACA;cACAA;YACA;cACAA;YACA;UACA;YACAA;UACA;UACA;MAAA;IAEA;;IAEA;IACAC;MACAC;QACAF;MACA;IACA;IAEA;IACAA;;IAyDA;IACA;MACA;MACAG,2BACAC;QACAD;UACA;QACA;UACA;QAAA,CACA;MACA,GACAE;QACAJ;MACA;IACA;EAEA;EACAK;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cAAA,MACA;gBAAA;gBAAA;cAAA;cAAA;cAAA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAC,UACA;EACAC,2BAaA;EACAC;IACA;EAAA;AAEA;AAAA,2B;;;;;;;;;;;;;ACnLA;AAAA;AAAA;AAAA;AAAohC,CAAgB,o8BAAG,EAAC,C;;;;;;;;;;;ACAxiC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "common/main.js", "sourcesContent": ["import 'uni-pages';\r\n// @ts-ignore\r\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import Vue from 'vue'\r\nimport App from './App'\r\nimport store from './store'\r\nimport Cache from './utils/cache'\r\nimport util from 'utils/util'\r\nimport configs from './config/app.js'\r\nimport * as Order from './libs/order';\r\n\r\nVue.prototype.$util = util;\r\nVue.prototype.$config = configs;\r\nVue.prototype.$Cache = Cache;\r\nVue.prototype.$eventHub = new Vue();\r\nVue.config.productionTip = false\r\nVue.prototype.$Order = Order;\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\nApp.mpType = 'app'\r\n\r\n\r\nconst app = new Vue({\r\n    ...App,\r\n\tstore,\r\n\tCache\r\n})\r\napp.$mount();", "var render, staticRenderFns, recyclableRender, components\nvar renderjs\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"App.vue\"\nexport default component.exports", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"", "<script>\r\n\timport {\r\n\t\tcheckLogin\r\n\t} from \"./libs/login\";\r\n\timport {\r\n\t\tHTTP_REQUEST_URL\r\n\t} from './config/app';\r\n\timport Auth from './libs/wechat.js';\r\n\timport Routine from './libs/routine.js';\r\n\timport Apps from './libs/apps.js';\r\n\timport {\r\n\t\tmapActions\r\n\t} from 'vuex'\r\n\r\n\texport default {\r\n\t\tglobalData: {\r\n\t\t\tspid: 0,\r\n\t\t\tcode: 0,\r\n\t\t\tisLogin: false,\r\n\t\t\tuserInfo: {},\r\n\t\t\tMyMenus: [],\r\n\t\t\twindowHeight: 0,\r\n\t\t\tid: 0\r\n\t\t},\r\n\t\tonLaunch: function(option) {\r\n\t\t\tlet that = this;\r\n\t\t\t// #ifdef H5\r\n\t\t\tuni.getSystemInfo({\r\n\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t// 首页没有title获取的整个页面的高度，里面的页面有原生标题要减掉就是视口的高度\r\n\t\t\t\t\t// 状态栏是动态的可以拿到 标题栏是固定写死的是44px\r\n\t\t\t\t\tlet height = res.windowHeight - res.statusBarHeight - 44\r\n\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\tthat.globalData.windowHeight = res.windowHeight + 'px'\r\n\t\t\t\t\t// #endif\r\n\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t// #endif\t\r\n\r\n\t\t\t// #ifdef MP\r\n\t\t\tif (HTTP_REQUEST_URL == '') {\r\n\t\t\t\tconsole.error(\r\n\t\t\t\t\t\"请配置根目录下的config.js文件中的 'HTTP_REQUEST_URL'\\n\\n请修改开发者工具中【详情】->【AppID】改为自己的Appid\\n\\n请前往后台【小程序】->【小程序配置】填写自己的 appId and AppSecret\"\r\n\t\t\t\t);\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\tif (option.query.hasOwnProperty('scene')) {\r\n\t\t\t\tswitch(option.scene){\r\n\t\t\t\t\tcase 1047: //扫描小程序码\r\n\t\t\t\t\tcase 1048: //长按图片识别小程序码\r\n\t\t\t\t\tcase 1049: //手机相册选取小程序码\r\n\t\t\t\t\tcase 1001: //直接进入小程序\r\n\t\t\t\t\tlet value = this.$util.getUrlParams(decodeURIComponent(option.query.scene));\r\n\t\t\t\t\tlet values = value.split(',');\r\n\t\t\t\t\tif(values.length === 2){\r\n\t\t\t\t\t\tlet v1 = values[0].split(\":\");\r\n\t\t\t\t\t\tif (v1[0] === 'pid') {\r\n\t\t\t\t\t\t\tthat.globalData.spid = v1[1];\r\n\t\t\t\t\t\t} else{\r\n\t\t\t\t\t\t\tthat.globalData.id = v1[1];\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tlet v2 = values[1].split(\":\");\r\n\t\t\t\t\t\tif (v2[0] === 'pid') {\r\n\t\t\t\t\t\t\tthat.globalData.spid = v2[1];\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tthat.globalData.id = v2[1];\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}else{\r\n\t\t\t\t\t\tthat.globalData.spid = values[0].split(\":\")[1];\r\n\t\t\t\t\t}\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t\t// 获取导航高度；\r\n\t\t\tuni.getSystemInfo({\r\n\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\tthat.globalData.navHeight = res.statusBarHeight * (750 / res.windowWidth) + 91;\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t// #ifdef MP\r\n\t\t\tlet menuButtonInfo = uni.getMenuButtonBoundingClientRect();\r\n\t\t\tthat.globalData.navH = menuButtonInfo.top * 2 + menuButtonInfo.height / 2;\r\n\t\t\t// #endif\r\n\r\n\t\t\t// #ifdef H5\t\t\t\r\n\t\t\tlet snsapiBase = 'snsapi_base';\r\n\t\t\tlet urlData = location.pathname + location.search;\r\n\t\t\tif (!that.$store.getters.isLogin && Auth.isWeixin()) {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tcode,\r\n\t\t\t\t\tstate,\r\n\t\t\t\t\tscope\r\n\t\t\t\t} = option.query;\r\n\t\t\t\tif (code && code != uni.getStorageSync('snsapiCode') && location.pathname.indexOf(\r\n\t\t\t\t\t\t'/pages/users/wechat_login/index') === -1) {\r\n\t\t\t\t\t// 存储静默授权code\r\n\t\t\t\t\tuni.setStorageSync('snsapiCode', code);\r\n\t\t\t\t\tlet spread = that.globalData.spid ? that.globalData.spid : 0;\r\n\t\t\t\t\tAuth.auth(code, that.$Cache.get('spread'))\r\n\t\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\t\tuni.setStorageSync('snRouter', decodeURIComponent(decodeURIComponent(option.query\r\n\t\t\t\t\t\t\t\t.back_url)));\r\n\t\t\t\t\t\t\tif (res.type === 'register') {\r\n\t\t\t\t\t\t\t\tthis.$Cache.set('snsapiKey', res.key);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tif (res.type === 'login') {\r\n\t\t\t\t\t\t\t\tthis.$store.commit('LOGIN', {\r\n\t\t\t\t\t\t\t\t\ttoken: res.token\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\tthis.$store.commit(\"SETUID\", res.uid);\r\n\t\t\t\t\t\t\t\tlocation.replace(decodeURIComponent(decodeURIComponent(option.query.back_url)));\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.catch(error => {\r\n\t\t\t\t\t\t\t// this.$util.Tips({\r\n\t\t\t\t\t\t\t// \ttitle: error\r\n\t\t\t\t\t\t\t// });\r\n\t\t\t\t\t\t\tif (!this.$Cache.has('snsapiKey')) {\r\n\t\t\t\t\t\t\t\tif (location.pathname.indexOf('/pages/users/wechat_login/index') === -1) {\r\n\t\t\t\t\t\t\t\t\tAuth.oAuth(snsapiBase, option.query.back_url);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tif (!this.$Cache.has('snsapiKey')) {\r\n\t\t\t\t\t\tif (location.pathname.indexOf('/pages/users/wechat_login/index') === -1) {\r\n\t\t\t\t\t\t\tAuth.oAuth(snsapiBase, urlData);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tif (option.query.back_url) {\r\n\t\t\t\t\tlocation.replace(uni.getStorageSync('snRouter'));\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\r\n\t\t\t// #ifdef MP\r\n\t\t\t// 小程序静默授权\r\n\t\t\tif (!this.$store.getters.isLogin) {\r\n\t\t\t\tlet spread = that.globalData.spid ? that.globalData.spid : 0;\r\n\t\t\t\tRoutine.getCode()\r\n\t\t\t\t\t.then(code => {\r\n\t\t\t\t\t\tRoutine.authUserInfo(code, {\r\n\t\t\t\t\t\t\t'spread_spid': spread\r\n\t\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\t\t// that.$store.commit('AuthorizeType', res.data.type);\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(res => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t});\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tasync mounted() {\r\n\t\t\tif(this.$store.getters.isLogin && !this.$Cache.get('USER_INFO'))await this.$store.dispatch('USERINFO');\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t},\r\n\t\tonShow: function() {\r\n\t\t\t// #ifdef H5\r\n\t\t\tuni.getSystemInfo({\r\n\t\t\t\tsuccess(e) {\r\n\t\t\t\t\t/* 窗口宽度大于420px且不在PC页面且不在移动设备时跳转至 PC.html 页面 */\r\n\t\t\t\t\tif (e.windowWidth > 420 && !window.top.isPC && !/iOS|Android/i.test(e.system)) {\r\n\t\t\t\t\t\t// window.location.pathname = 'https://java.crmeb.net/';\r\n\t\t\t\t\t\t/* 若你的项目未设置根目录（默认为 / 时），则使用下方代码 */\r\n\t\t\t\t\t\twindow.location.pathname = '/static/html/pc.html';\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tonHide: function() {\r\n\t\t\t//console.log('App Hide')\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t@import url(\"@/plugin/animate/animate.min.css\");\r\n\t@import 'static/css/base.css';\r\n\t@import 'static/iconfont/iconfont.css';\r\n\t@import 'static/css/guildford.css';\r\n\t@import 'static/css/style.scss';\r\n\t\r\n\t// #ifdef APP\r\n\t@import 'static/iconfont/iconfont.css';\r\n\t@import 'static/iconfont/iconfont-app.css';\r\n\t// #endif\r\n\r\n\t/* 条件编译，仅在H5平台生效 */\r\n\t// #ifdef H5\r\n\tbody::-webkit-scrollbar,\r\n\thtml::-webkit-scrollbar {\r\n\t\tdisplay: none;\r\n\t}\r\n\r\n\t// #endif\r\n\tview {\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.bg-color-red {\r\n\t\tbackground-color: #c9ab79 !important;\r\n\t}\r\n\r\n\t.syspadding {\r\n\t\tpadding-top: var(--status-bar-height);\r\n\t}\r\n\r\n\t.flex {\r\n\t\tdisplay: flex;\r\n\t}\r\n\r\n\t.uni-scroll-view::-webkit-scrollbar {\r\n\t\t/* 隐藏滚动条，但依旧具备可以滚动的功能 */\r\n\t\tdisplay: none\r\n\t}\r\n\r\n\t::-webkit-scrollbar {\r\n\t\twidth: 0;\r\n\t\theight: 0;\r\n\t\tcolor: transparent;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363904295\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}