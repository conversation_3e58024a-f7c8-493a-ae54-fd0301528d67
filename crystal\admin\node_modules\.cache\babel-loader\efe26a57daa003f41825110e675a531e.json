{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\list\\userDetails.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\list\\userDetails.vue", "mtime": 1753666157942}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _user = require(\"@/api/user\");\nvar _marketing = require(\"@/api/marketing\");\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  name: \"UserDetails\",\n  props: {\n    uid: {\n      type: Number,\n      default: null\n    }\n  },\n  data: function data() {\n    return {\n      loading: false,\n      columns: [],\n      Visible: false,\n      list: [{\n        val: '0',\n        label: '消费记录'\n      }, {\n        val: '1',\n        label: '积分明细'\n      }, {\n        val: '2',\n        label: '签到记录'\n      }, {\n        val: '3',\n        label: '持有优惠券'\n      }, {\n        val: '4',\n        label: '余额变动'\n      }, {\n        val: '5',\n        label: '好友关系'\n      }],\n      tableData: {\n        data: [],\n        total: 0\n      },\n      tableFrom: {\n        page: 1,\n        limit: 6,\n        type: '0',\n        userId: ''\n      },\n      psInfo: null\n    };\n  },\n  mounted: function mounted() {\n    if (this.uid) {\n      this.getHeader();\n      this.getInfo();\n    }\n  },\n  methods: {\n    changeType: function changeType(key) {\n      this.tableFrom.type = key;\n      if (key === '1') {\n        this.integral();\n      } else {\n        this.getInfo();\n      }\n    },\n    integral: function integral() {\n      var _this = this;\n      this.loading = true;\n      (0, _marketing.integralListApi)({\n        limit: this.tableFrom.limit,\n        page: this.tableFrom.page\n      }, {\n        uid: this.uid\n      }).then(function (res) {\n        _this.tableData.data = res.list;\n        _this.tableData.total = res.total;\n        _this.columns = [{\n          title: '来源/用途',\n          key: 'title',\n          minWidth: 120\n        }, {\n          title: '积分变化',\n          key: 'integral',\n          minWidth: 120\n        }, {\n          title: '变化后积分',\n          key: 'balance',\n          minWidth: 120\n        }, {\n          title: '日期',\n          key: 'updateTime',\n          minWidth: 120\n        }, {\n          title: '备注',\n          key: 'mark',\n          minWidth: 120\n        }];\n        _this.loading = false;\n      }).catch(function (res) {\n        _this.loading = false;\n      });\n    },\n    getInfo: function getInfo() {\n      var _this2 = this;\n      this.tableFrom.userId = this.uid;\n      this.loading = true;\n      (0, _user.infobyconditionApi)(this.tableFrom).then(function (res) {\n        _this2.tableData.data = res.list;\n        _this2.tableData.total = res.total;\n        switch (_this2.tableFrom.type) {\n          case '0':\n            _this2.columns = [{\n              title: '订单ID',\n              key: 'orderId',\n              minWidth: 250\n            }, {\n              title: '收货人',\n              key: 'realName',\n              minWidth: 90\n            }, {\n              title: '商品数量',\n              key: 'totalNum',\n              minWidth: 80\n            }, {\n              title: '商品总价',\n              key: 'totalPrice',\n              minWidth: 90\n            }, {\n              title: '实付金额',\n              key: 'payPrice',\n              minWidth: 90\n            }, {\n              title: '交易完成时间',\n              key: 'payTime',\n              minWidth: 160\n            }];\n            break;\n          case '2':\n            _this2.columns = [{\n              title: '动作',\n              key: 'title',\n              minWidth: 120\n            }, {\n              title: '获得积分',\n              key: 'number',\n              minWidth: 120\n            }, {\n              title: '签到时间',\n              key: 'createTime',\n              minWidth: 120\n            }, {\n              title: '备注',\n              key: 'title',\n              minWidth: 120\n            }];\n            break;\n          case '3':\n            _this2.columns = [{\n              title: '优惠券名称',\n              key: 'name',\n              minWidth: 120\n            }, {\n              title: '面值',\n              key: 'money',\n              minWidth: 120\n            }, {\n              title: '有效期',\n              key: 'endTime',\n              minWidth: 120\n            }, {\n              title: '最低消费额',\n              key: 'minPrice',\n              minWidth: 120\n            }, {\n              title: '兑换时间',\n              key: 'updateTime',\n              minWidth: 120\n            }];\n            break;\n          case '4':\n            _this2.columns = [{\n              title: '变动金额',\n              key: 'number',\n              minWidth: 120\n            }, {\n              title: '变动后',\n              key: 'balance',\n              minWidth: 120\n            }, {\n              title: '类型',\n              key: 'title',\n              minWidth: 120\n            }, {\n              title: '创建时间',\n              key: 'add_time',\n              minWidth: 120\n            }, {\n              title: '备注',\n              key: 'mark',\n              minWidth: 120\n            }];\n            break;\n          default:\n            _this2.columns = [{\n              title: 'ID',\n              key: 'uid',\n              minWidth: 120\n            }, {\n              title: '昵称',\n              key: 'nickname',\n              minWidth: 120\n            }, {\n              title: '等级',\n              key: 'level',\n              minWidth: 120\n            }, {\n              title: '加入时间',\n              key: 'createTime',\n              minWidth: 120\n            }];\n        }\n        _this2.loading = false;\n      }).catch(function () {\n        _this2.loading = false;\n      });\n    },\n    pageChange: function pageChange(page) {\n      this.tableFrom.page = page;\n      if (this.tableFrom.type === '1') {\n        this.integral();\n      } else {\n        this.getInfo();\n      }\n    },\n    handleSizeChange: function handleSizeChange(val) {\n      this.tableFrom.limit = val;\n      if (this.tableFrom.type === '1') {\n        this.integral();\n      } else {\n        this.getInfo();\n      }\n    },\n    getHeader: function getHeader() {\n      var _this3 = this;\n      (0, _user.topdetailApi)({\n        userId: this.uid\n      }).then(function (res) {\n        _this3.psInfo = res;\n      });\n    }\n  }\n};", null]}