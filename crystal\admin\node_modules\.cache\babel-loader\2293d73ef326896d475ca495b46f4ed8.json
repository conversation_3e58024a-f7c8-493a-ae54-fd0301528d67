{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\grade\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\grade\\index.vue", "mtime": 1753666157939}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _user = require(\"@/api/user\");\nvar _creatGrade = _interopRequireDefault(require(\"./creatGrade\"));\nvar _permission = require(\"@/utils/permission\");\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n// 权限判断函数\nvar _default = exports.default = {\n  name: 'Grade',\n  filters: {\n    typeFilter: function typeFilter(status) {\n      var statusMap = {\n        'wechat': '微信用户',\n        'routine': '小程序你用户',\n        'h5': 'H5用户'\n      };\n      return statusMap[status];\n    }\n  },\n  components: {\n    creatGrade: _creatGrade.default\n  },\n  data: function data() {\n    return {\n      listLoading: true,\n      userInfo: {},\n      tableData: {\n        data: [],\n        total: 0\n      }\n    };\n  },\n  mounted: function mounted() {\n    this.getList();\n  },\n  methods: {\n    checkPermi: _permission.checkPermi,\n    seachList: function seachList() {\n      this.getList();\n    },\n    add: function add() {\n      this.$refs.grades.dialogVisible = true;\n      this.userInfo = {};\n    },\n    edit: function edit(id) {\n      // this.$refs.grades.info(id)\n      this.userInfo = id;\n      this.$refs.grades.dialogVisible = true;\n    },\n    // 列表\n    getList: function getList() {\n      var _this = this;\n      this.listLoading = true;\n      (0, _user.levelListApi)().then(function (res) {\n        _this.tableData.data = res;\n        _this.listLoading = false;\n      }).catch(function () {\n        _this.listLoading = false;\n      });\n    },\n    // 删除\n    handleDelete: function handleDelete(id, idx) {\n      var _this2 = this;\n      this.$modalSure('删除吗？删除会导致对应用户等级数据清空，请谨慎操作！').then(function () {\n        (0, _user.levelDeleteApi)(id).then(function () {\n          _this2.$message.success('删除成功');\n          _this2.tableData.data.splice(idx, 1);\n        });\n      });\n    },\n    onchangeIsShow: function onchangeIsShow(row) {\n      var _this3 = this;\n      if (row.isShow == false) {\n        row.isShow = !row.isShow;\n        (0, _user.levelUseApi)({\n          id: row.id,\n          isShow: row.isShow\n        }).then(function () {\n          _this3.$message.success('修改成功');\n          _this3.getList();\n        }).catch(function () {\n          row.isShow = !row.isShow;\n        });\n      } else {\n        this.$modalSure('该操作会导致对应用户等级隐藏，请谨慎操作').then(function () {\n          row.isShow = !row.isShow;\n          (0, _user.levelUseApi)({\n            id: row.id,\n            isShow: row.isShow\n          }).then(function () {\n            _this3.$message.success('修改成功');\n            _this3.getList();\n          }).catch(function () {\n            row.isShow = !row.isShow;\n          });\n        });\n      }\n    }\n  }\n};", null]}