{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\marketing\\bargain\\bargainGoods\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\marketing\\bargain\\bargainGoods\\index.vue", "mtime": 1753666157888}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _marketing = require(\"@/api/marketing\");\nvar _permission = require(\"@/utils/permission\");\nfunction _regenerator() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */ var e, t, r = \"function\" == typeof Symbol ? Symbol : {}, n = r.iterator || \"@@iterator\", o = r.toStringTag || \"@@toStringTag\"; function i(r, n, o, i) { var c = n && n.prototype instanceof Generator ? n : Generator, u = Object.create(c.prototype); return _regeneratorDefine2(u, \"_invoke\", function (r, n, o) { var i, c, u, f = 0, p = o || [], y = !1, G = { p: 0, n: 0, v: e, a: d, f: d.bind(e, 4), d: function d(t, r) { return i = t, c = 0, u = e, G.n = r, a; } }; function d(r, n) { for (c = r, u = n, t = 0; !y && f && !o && t < p.length; t++) { var o, i = p[t], d = G.p, l = i[2]; r > 3 ? (o = l === n) && (u = i[(c = i[4]) ? 5 : (c = 3, 3)], i[4] = i[5] = e) : i[0] <= d && ((o = r < 2 && d < i[1]) ? (c = 0, G.v = n, G.n = i[1]) : d < l && (o = r < 3 || i[0] > n || n > l) && (i[4] = r, i[5] = n, G.n = l, c = 0)); } if (o || r > 1) return a; throw y = !0, n; } return function (o, p, l) { if (f > 1) throw TypeError(\"Generator is already running\"); for (y && 1 === p && d(p, l), c = p, u = l; (t = c < 2 ? e : u) || !y;) { i || (c ? c < 3 ? (c > 1 && (G.n = -1), d(c, u)) : G.n = u : G.v = u); try { if (f = 2, i) { if (c || (o = \"next\"), t = i[o]) { if (!(t = t.call(i, u))) throw TypeError(\"iterator result is not an object\"); if (!t.done) return t; u = t.value, c < 2 && (c = 0); } else 1 === c && (t = i.return) && t.call(i), c < 2 && (u = TypeError(\"The iterator does not provide a '\" + o + \"' method\"), c = 1); i = e; } else if ((t = (y = G.n < 0) ? u : r.call(n, G)) !== a) break; } catch (t) { i = e, c = 1, u = t; } finally { f = 1; } } return { value: t, done: y }; }; }(r, o, i), !0), u; } var a = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} t = Object.getPrototypeOf; var c = [][n] ? t(t([][n]())) : (_regeneratorDefine2(t = {}, n, function () { return this; }), t), u = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c); function f(e) { return Object.setPrototypeOf ? Object.setPrototypeOf(e, GeneratorFunctionPrototype) : (e.__proto__ = GeneratorFunctionPrototype, _regeneratorDefine2(e, o, \"GeneratorFunction\")), e.prototype = Object.create(u), e; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, _regeneratorDefine2(u, \"constructor\", GeneratorFunctionPrototype), _regeneratorDefine2(GeneratorFunctionPrototype, \"constructor\", GeneratorFunction), GeneratorFunction.displayName = \"GeneratorFunction\", _regeneratorDefine2(GeneratorFunctionPrototype, o, \"GeneratorFunction\"), _regeneratorDefine2(u), _regeneratorDefine2(u, o, \"Generator\"), _regeneratorDefine2(u, n, function () { return this; }), _regeneratorDefine2(u, \"toString\", function () { return \"[object Generator]\"; }), (_regenerator = function _regenerator() { return { w: i, m: f }; })(); }\nfunction _regeneratorDefine2(e, r, n, t) { var i = Object.defineProperty; try { i({}, \"\", {}); } catch (e) { i = 0; } _regeneratorDefine2 = function _regeneratorDefine(e, r, n, t) { function o(r, n) { _regeneratorDefine2(e, r, function (e) { return this._invoke(r, n, e); }); } r ? i ? i(e, r, { value: n, enumerable: !t, configurable: !t, writable: !t }) : e[r] = n : (o(\"next\", 0), o(\"throw\", 1), o(\"return\", 2)); }, _regeneratorDefine2(e, r, n, t); }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; } //\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  name: \"index\",\n  data: function data() {\n    return {\n      tableFrom: {\n        page: 1,\n        limit: 20,\n        keywords: '',\n        status: null\n      },\n      listLoading: true,\n      tableData: {\n        data: [],\n        total: 0\n      },\n      afterData: []\n    };\n  },\n  mounted: function mounted() {\n    // 源数据\n    var oldData = [{\n      city_id: 1,\n      city_name: '北京',\n      city_img: \"http://dfknbdjknvkjsfnvlkjdn.png\",\n      city_country: \"中国\"\n    }, {\n      city_id: 2,\n      city_name: '上海',\n      city_img: \"http://wergerbe.png\",\n      city_country: \"中国\"\n    }, {\n      city_id: 3,\n      city_name: '广州',\n      city_img: \"http://hrthhr.png\",\n      city_country: \"中国\"\n    }, {\n      city_id: 4,\n      city_name: '西雅图',\n      city_img: \"http://frevfd.png\",\n      city_country: \"美国\"\n    }, {\n      city_id: 5,\n      city_name: '纽约',\n      city_img: \"http://反而个.png\",\n      city_country: \"美国\"\n    }];\n    // 把源数据先变成目标数据的规则\n    var oldDataRule = [];\n    oldData.forEach(function (el) {\n      var oldObj = {\n        name: el.city_country,\n        citys: []\n      };\n      var cityObj = {\n        city_name: el.city_name,\n        city_img: el.city_img,\n        city_id: el.city_id\n      };\n      oldObj.citys.push(cityObj);\n      oldDataRule.push(oldObj);\n    });\n    var newData = [];\n    var newObj = {};\n    oldDataRule.forEach(function (el, i) {\n      if (!newObj[el.name]) {\n        newData.push(el);\n        newObj[el.name] = true;\n      } else {\n        newData.forEach(function (el) {\n          if (el.name === oldDataRule[i].name) {\n            el.citys = el.citys.concat(oldDataRule[i].citys);\n            // el.citys = [...el.citys, ...oldDataRule[i].citys]; // es6语法\n          }\n        });\n      }\n    });\n    this.getList();\n  },\n  methods: {\n    checkPermi: _permission.checkPermi,\n    //权限控制\n    //导出\n    exportList: function exportList() {\n      (0, _marketing.exportBargainApi)({\n        keywords: this.tableFrom.keywords,\n        status: this.tableFrom.status\n      }).then(function (res) {\n        window.open(res.fileName);\n      });\n    },\n    // 删除\n    handleDelete: function handleDelete(id, idx) {\n      var that = this;\n      this.$modal.confirm('确认删除该商品吗').then(function () {\n        (0, _marketing.bargainDeleteApi)({\n          id: id\n        }).then(function () {\n          that.$message.success('删除成功');\n          that.getList();\n        });\n      }).catch(function () {});\n    },\n    onchangeIsShow: function onchangeIsShow(row) {\n      var _this = this;\n      (0, _marketing.bargainStatusApi)({\n        id: row.id,\n        status: row.status\n      }).then(/*#__PURE__*/_asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee() {\n        return _regenerator().w(function (_context) {\n          while (1) switch (_context.n) {\n            case 0:\n              _this.$message.success('修改成功');\n              _this.getList();\n            case 1:\n              return _context.a(2);\n          }\n        }, _callee);\n      }))).catch(function () {\n        row.status = !row.status;\n      });\n    },\n    // 列表\n    getList: function getList(num) {\n      var _this2 = this;\n      this.listLoading = true;\n      this.tableFrom.page = num ? num : this.tableFrom.page;\n      (0, _marketing.bargainListApi)(this.tableFrom).then(function (res) {\n        _this2.tableData.data = res.list;\n        _this2.tableData.total = res.total;\n        _this2.listLoading = false;\n      }).catch(function (res) {\n        _this2.listLoading = false;\n      });\n    },\n    pageChange: function pageChange(page) {\n      this.tableFrom.page = page;\n      this.getList();\n    },\n    handleSizeChange: function handleSizeChange(val) {\n      this.tableFrom.limit = val;\n      this.getList();\n    },\n    add: function add() {\n      this.isCreate = 0;\n      this.dialogVisible = true;\n    },\n    handleClose: function handleClose() {\n      this.dialogVisible = false;\n      this.editData = {};\n    }\n  }\n};", null]}