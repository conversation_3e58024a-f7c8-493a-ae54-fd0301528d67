@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.pic.data-v-0a3f79d3 {
  width: 130rpx;
  height: 30rpx;
}
.default.data-v-0a3f79d3 {
  width: 690rpx;
  height: 300rpx;
  border-radius: 14rpx;
  margin: 26rpx auto 0 auto;
  background-color: #ccc;
  text-align: center;
  line-height: 300rpx;
}
.default .iconfont.data-v-0a3f79d3 {
  font-size: 80rpx;
}
.combination.data-v-0a3f79d3 {
  width: auto;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAArIAAACcBAMAAABxblXcAAAAFVBMVEUAAAD/Hx//X1//PT3/kpL/Jyf/dna5WQZDAAAAB3RSTlMAEhIGEgwLAPQzbAAABKBJREFUeNrt3M2O0zAUQGGnl3SNqWY/kwrWdWBYowHNmsyI/ajw/s9AGJc6JXV+bF+JxTkSAgGrT5FzfWkxFGn7re8l8vsHQ8m96QEfzTjx4pTcrx7wS+QPmr2h5MKjOXqYm+aDoeSix+m26TOU8wJ7NFd76GU5aLNkv5qrHXvZW/Pa52Pz4aehtaPBl8gfNaeDVo5NH6+z9bIvxkQPWg/r46ld06e4rDR9L/4nHxeHlePswUwctFuPynlQUrb9c9D2P3hoS8ueHlce2mRZE0mai7g5FJM1zWXcHErJtg3Hgco5exy5sqIpIRtgmQ6ybwpx2NCtoWTZsCrgOCi9kQmwzF1Zsl/jsMxdRTffEhyZu5KTb33XLrUcB0X+hTECy9yVOdA+3F6HZe7KfIU9DEepz81c7w3NFZ7RS1iOgyLJCSvAchwU6hiwwnKLuatA7Rkr3A+Yu/IKHyt4vALLNazQQetvtBwHJXt9DD0sx0HxV9iHAMvcVaxNM4y5K7v4fZZrWKmayzgO1J/ZeN8N5cHuN8xdCc2vCn6aDXOXDqxpOWh1YE3LNSyt7dz7v2XuSus4N1i1zF2lb1+Hafo7rmGTxS9ZB+N7Yt+V0nYW1nANS6qdfyC5hiV1nJdl/Z2SzKtJE+2doVjbHFlrOWgTZq7bWVlr7VtD6bJ1ZJq1fTeGIrUJsgGW46CobIDlOEiS/X7lwPgRYE/tDK2VfRnLPtdDWJ+htbKH0V95NpsRrOUaFmmzWPbu0P8ywHIcFJM9/N16BVXmrom2y2T95ksuYZm7ppJ52afzSlEuYJm7plsk62FN5WE5DvKGA/O3sARvX2E5DorJ7s9Pr+3jOFhYPSsb5qqgydyV8Qozo8QO4xo21/KPadR2ENew2Y6L/98NZzkOCrzC3sdkmbuWtvzTsfZPzF2599v99RcYc1f2cLC/9gLjOFjVUtmqN2TuWtNxoaxDdmXtwi/SdciubLNQ1iJbZHMwet8LsmuTZR/grJFd3TJZh+zqWmQn0pe1yJYZDg7mMkF2fbJEtka2jOzdv7IVsgk9jWHDZlD8Tw7ZEq+wwc5VOv9zh2yBV9gAS+zN6Wdk8++3AyyxyJZ7hQ2w5LzWrpBNagTrPe/Dv8w6ZHOHAxtk722Q7ZDNHA5skL23A1mLbFLVeYwNsvfDf5gVZNOqA+ypnbND2QrZtCTAhoayDtnE3nnY6OfhOmQTe/KwUVmLbGJtDNYvEATZ1DZ3dkq2RjY1sZOyFbJKsh2yyXWTahbZ5NyUmiCrJFsjm149peaQTU+mPs/dIZvRlKxFNqMuLivIJjX/Nboa2ZyquKxDVmM42A0PCr54m5DEZa2PrzAmFpUVYFWGg93MousG2LlcTNYBm1cVkwVWZzjYibV8lVljOLhxwOZmo/G/nOTVAauUs6t6NrSwClilBNiQmiw7mKIBq1R4hbHcKlwFrE7hfstyq3ACrFbAqhTutyy3iudYFShVAatUDaxSwqpAK2C16oBVyrGDUapiVaBUDaxSAqxWrAq0csAqVQGrlLDc0opVgVYOWKUEWK0cqwKlBFitBFitpGOM1erjx4+G/ot+AzVUsRRz96GlAAAAAElFTkSuQmCC);
  background-repeat: no-repeat;
  background-size: 100%;
  background-color: #fff;
  border-radius: 14rpx;
  margin: 30rpx auto 0 auto;
  padding: 25rpx 20rpx 25rpx 20rpx;
}
.combination .title .sign.data-v-0a3f79d3 {
  font-size: 32rpx;
  color: #c9ab79;
  margin-bottom: 2rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}
.combination .title .name text.data-v-0a3f79d3 {
  color: #333333;
  font-size: 26rpx;
  font-weight: 400;
}
.combination .title .more.data-v-0a3f79d3 {
  width: 86rpx;
  height: 40rpx;
  background: linear-gradient(142deg, #FFE9CE 0%, #FFD6A7 100%);
  opacity: 1;
  border-radius: 18px;
  font-size: 22rpx;
  color: #FE960F;
  padding-left: 8rpx;
  font-weight: 800;
}
.combination .title .more .iconfont.data-v-0a3f79d3 {
  font-size: 21rpx;
}
.combination .conter.data-v-0a3f79d3 {
  margin-top: 28rpx;
}
.combination .conter .itemCon.data-v-0a3f79d3 {
  display: inline-block;
  width: 220rpx;
  margin-right: 24rpx;
}
.combination .conter .item.data-v-0a3f79d3 {
  width: 100%;
}
.combination .conter .item .pictrue.data-v-0a3f79d3 {
  width: 100%;
  height: 220rpx;
  border-radius: 6rpx;
}
.combination .conter .item .pictrue image.data-v-0a3f79d3 {
  width: 100%;
  height: 100%;
  border-radius: 6rpx;
}
.combination .conter .item .text.data-v-0a3f79d3 {
  margin-top: 4rpx;
}
.combination .conter .item .text .y_money.data-v-0a3f79d3 {
  font-size: 24rpx;
  color: #999999;
  text-decoration: line-through;
}
.combination .conter .item .text .name.data-v-0a3f79d3 {
  font-size: 24rpx;
  color: #000;
  margin-top: 14rpx;
}
.combination .conter .item .text .money.data-v-0a3f79d3 {
  color: #FD502F;
  font-size: 28rpx;
  height: 100%;
  font-weight: bold;
  margin: 10rpx 0;
}
.combination .conter .item .text .money .num.data-v-0a3f79d3 {
  font-size: 28rpx;
}
.combination .conter .item .text .btn.data-v-0a3f79d3 {
  width: 220rpx;
  height: 48rpx;
  line-height: 48rpx;
  text-align: center;
  background: linear-gradient(129deg, #FF5555 0%, #FF0000 100%);
  opacity: 1;
  border-radius: 0px 0px 14rpx 14rpx;
  color: #FFFFFF;
  font-size: 26rpx;
  margin-top: 6rpx;
}

