{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/index/components/b_combination.vue?0778", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/index/components/b_combination.vue?2ab5", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/index/components/b_combination.vue?c117", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/index/components/b_combination.vue?2774", "uni-app:///pages/index/components/b_combination.vue", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/index/components/b_combination.vue?8836", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/index/components/b_combination.vue?f14b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "combinationList", "isBorader", "assistUserList", "assistUserCount", "created", "mounted", "methods", "getCombinationList", "that", "title", "goDetail", "uni", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,sBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AACmM;AACnM,gBAAgB,2LAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/BA;AAAA;AAAA;AAAA;AAA0wB,CAAgB,6rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACmD9xB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AADA;AAAA,eAIA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;EACAC;IACA;IACAC;MACA;MACA;QACAC;QACAA;QACAA;MACA;QACA;UACAC;QACA;MACA;IACA;IACAC;MACAC;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxFA;AAAA;AAAA;AAAA;AAA68C,CAAgB,4vCAAG,EAAC,C;;;;;;;;;;;ACAj+C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/components/b_combination.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/components/b_combination.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./b_combination.vue?vue&type=template&id=a1cf24e2&scoped=true&\"\nvar renderjs\nimport script from \"./b_combination.vue?vue&type=script&lang=js&\"\nexport * from \"./b_combination.vue?vue&type=script&lang=js&\"\nimport style0 from \"./b_combination.vue?vue&type=style&index=0&id=a1cf24e2&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"a1cf24e2\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/components/b_combination.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./b_combination.vue?vue&type=template&id=a1cf24e2&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.combinationList.length\n  var g1 = g0 ? _vm.assistUserList.length : null\n  var l0 =\n    g0 && g1 > 0\n      ? _vm.__map(_vm.assistUserList, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m0 = index === 2 && Number(_vm.assistUserCount) > 3\n          return {\n            $orig: $orig,\n            m0: m0,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./b_combination.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./b_combination.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view :class=\"{borderShow:isBorader}\">\r\n\t\t<view class=\"combination\" v-if=\"combinationList.length\">\r\n\t\t\t<view class=\"title acea-row row-between\">\r\n\t\t\t\t<view class=\"spike-bd\">\r\n\t\t\t\t\t<view v-if=\"assistUserList.length > 0\" class=\"activity_pic\">\r\n\t\t\t\t\t\t<view v-for=\"(item,index) in assistUserList\" :key=\"index\" class=\"picture\"\r\n\t\t\t\t\t\t\t:style='index===2?\"position: relative\":\"position: static\"'>\r\n\t\t\t\t\t\t\t<span class=\"avatar\" :style='\"background-image: url(\"+item+\")\"'></span>\r\n\t\t\t\t\t\t\t<span v-if=\"index===2 && Number(assistUserCount) > 3\" class=\"mengceng\">\r\n\t\t\t\t\t\t\t\t<i>···</i>\r\n\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<text class=\"pic_count\">{{assistUserCount}}人参与</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<navigator url=\"/pages/activity/goods_combination/index\" hover-class=\"none\"\r\n\t\t\t\t\tclass=\"more acea-row row-center-wrapper\">GO<text class=\"iconfont icon-xiangyou\"></text></navigator>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"conter acea-row\">\r\n\t\t\t\t<scroll-view scroll-x=\"true\" style=\"white-space: nowrap; vertical-align: middle;\"\r\n\t\t\t\t\tshow-scrollbar=\"false\">\r\n\t\t\t\t\t<view class=\"itemCon\" v-for=\"(item, index) in combinationList\" :key=\"index\" @click=\"goDetail(item)\">\r\n\t\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t\t<view class=\"pictrue\">\r\n\t\t\t\t\t\t\t\t<image :src=\"item.image\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"text lines1\">\r\n\t\t\t\t\t\t\t\t<view class=\"name line1\">{{item.title}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"money\">¥<text class=\"num\">{{item.price}}</text></view>\r\n\t\t\t\t\t\t\t\t<view class=\"y_money\">¥{{item.otPrice}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- <navigator :url=\"`/pages/activity/goods_combination_details/index?id=${item.id}`\" hover-class=\"none\" class=\"item\" v-for=\"(item, index) in combinationList\" :key=\"index\">\r\n\t\t\t\t\t\t<view class=\"pictrue\">\r\n\t\t\t\t\t\t\t<image :src=\"item.image\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"text lines1\">\r\n\t\t\t\t\t\t\t<text class=\"money\">¥<text class=\"num\">{{item.price}}</text></text>\r\n\t\t\t\t\t\t\t<text class=\"y_money\">¥{{item.otPrice}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</navigator> -->\r\n\t\t\t\t</scroll-view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tlet app = getApp();\r\n\timport {\r\n\t\tgetCombinationIndexApi\r\n\t} from '@/api/activity.js';\r\n\texport default {\r\n\t\tname: 'b_combination',\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tcombinationList: [],\r\n\t\t\t\tisBorader: false,\r\n\t\t\t\tassistUserList: [],\r\n\t\t\t\tassistUserCount: 0\r\n\t\t\t};\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.getCombinationList();\r\n\t\t},\r\n\t\tmounted() {},\r\n\t\tmethods: {\r\n\t\t\t// 拼团列表\r\n\t\t\tgetCombinationList: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tgetCombinationIndexApi().then(function(res) {\r\n\t\t\t\t\tthat.combinationList = res.data.productList;\r\n\t\t\t\t\tthat.assistUserList = res.data.avatarList;\r\n\t\t\t\t\tthat.assistUserCount = res.data.totalPeople;\r\n\t\t\t\t}).catch((res) => {\r\n\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\ttitle: res\r\n\t\t\t\t\t});\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgoDetail(item) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/activity/goods_combination_details/index?id=${item.id}`\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.mengceng {\r\n\t\twidth: 38rpx;\r\n\t\theight: 38rpx;\r\n\t\tline-height: 36rpx;\r\n\t\tbackground: rgba(51, 51, 51, 0.6);\r\n\t\ttext-align: center;\r\n\t\tborder-radius: 50%;\r\n\t\topacity: 1;\r\n\t\tposition: absolute;\r\n\t\tleft: 0px;\r\n\t\ttop: 2rpx;\r\n\t\tcolor: #FFF;\r\n\t\ti{\r\n\t\t\tfont-style: normal;\r\n\t\t\tfont-size: 20rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.activity_pic {\r\n\t\tmargin-left: 28rpx;\r\n\t\tpadding-left: 20rpx;\r\n\r\n\t\t.picture {\r\n\t\t\tdisplay: inline-block;\r\n\t\t}\r\n\r\n\t\t.avatar {\r\n\t\t\twidth: 38rpx;\r\n\t\t\theight: 38rpx;\r\n\t\t\tdisplay: inline-table;\r\n\t\t\tvertical-align: middle;\r\n\t\t\t-webkit-user-select: none;\r\n\t\t\t-moz-user-select: none;\r\n\t\t\t-ms-user-select: none;\r\n\t\t\tuser-select: none;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\tbackground-repeat: no-repeat;\r\n\t\t\tbackground-size: cover;\r\n\t\t\tbackground-position: 0 0;\r\n\t\t\tmargin-right: -10rpx;\r\n\t\t\tbox-shadow: 0 0 0 1px #fff;\r\n\t\t}\r\n\r\n\t\t.pic_count {\r\n\t\t\tmargin-left: 30rpx;\r\n\t\t\tcolor: $theme-color;\r\n\t\t\tfont-size: 22rpx;\r\n\t\t\tfont-weight: 500;\r\n\t\t}\r\n\t}\r\n\r\n\t.default {\r\n\t\twidth: 690rpx;\r\n\t\theight: 300rpx;\r\n\t\tborder-radius: 14rpx;\r\n\t\tmargin: 26rpx auto 0 auto;\r\n\t\tbackground-color: #ccc;\r\n\t\ttext-align: center;\r\n\t\tline-height: 300rpx;\r\n\r\n\t\t.iconfont {\r\n\t\t\tfont-size: 80rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.combination {\r\n\t\twidth: auto;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 14rpx;\r\n\t\tmargin: 0 auto 30rpx auto;\r\n\t\tpadding: 16rpx 24rpx 24rpx 24rpx;\r\n\t\tbackground-image: url(../../../static/images/pth.png);\r\n\t\tbackground-repeat: no-repeat;\r\n\t\tbackground-size: 100%;\r\n\r\n\t\t.title {\r\n\t\t\twidth: 80%;\r\n\t\t\tmargin-left: 128rpx;\r\n\r\n\t\t\t.sign {\r\n\t\t\t\twidth: 40rpx;\r\n\t\t\t\theight: 40rpx;\r\n\r\n\t\t\t\timage {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 100%;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.name {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tcolor: #282828;\r\n\t\t\t\tmargin-left: 12rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\r\n\t\t\t\ttext {\r\n\t\t\t\t\tcolor: #797979;\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\tmargin-left: 14rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.more {\r\n\t\t\t\twidth: 86rpx;\r\n\t\t\t\theight: 40rpx;\r\n\t\t\t\tbackground: linear-gradient(142deg, #FFE9CE 0%, #FFD6A7 100%);\r\n\t\t\t\topacity: 1;\r\n\t\t\t\tborder-radius: 18px;\r\n\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\tcolor: #FE960F;\r\n\t\t\t\tpadding-left: 8rpx;\r\n                 font-weight: 800;\r\n\t\t\t\t.iconfont {\r\n\t\t\t\t\tfont-size: 21rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.conter {\r\n\t\t\tmargin-top: 24rpx;\r\n\r\n\t\t\t.itemCon {\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\twidth: 220rpx;\r\n\t\t\t\tmargin-right: 24rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.item {\r\n\t\t\t\twidth: 100%;\r\n\r\n\t\t\t\t.pictrue {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 220rpx;\r\n\t\t\t\t\tborder-radius: 6rpx;\r\n\r\n\t\t\t\t\timage {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\tborder-radius: 6rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.text {\r\n\t\t\t\t\tmargin-top: 4rpx;\r\n\r\n\t\t\t\t\t.y_money {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\t\ttext-decoration: line-through;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.name {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: #000;\r\n\t\t\t\t\t\tmargin-top: 14rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.money {\r\n\t\t\t\t\t\tcolor: #FD502F;\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\tmargin: 10rpx 0 0rpx 0;\r\n\r\n\t\t\t\t\t\t.num {\r\n\t\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./b_combination.vue?vue&type=style&index=0&id=a1cf24e2&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./b_combination.vue?vue&type=style&index=0&id=a1cf24e2&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363903790\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}