{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\marketing\\coupon\\list\\index.vue?vue&type=template&id=15f24157&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\marketing\\coupon\\list\\index.vue", "mtime": 1753666157892}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\n<div class=\"divBox\">\n  <el-card class=\"box-card\">\n    <div slot=\"header\" class=\"clearfix\">\n      <div class=\"filter-container\">\n        <div class=\"demo-input-suffix acea-row\">\n          <span class=\"seachTiele\">状态：</span>\n          <el-select v-model=\"tableFrom.status\" placeholder=\"请选择\" class=\"filter-item selWidth mr20\" @change=\"seachList\" clearable>\n            <el-option label=\"未开启\" :value=\"0\" />\n            <el-option label=\"开启\" :value=\"1\" />\n          </el-select>\n          <span class=\"seachTiele\">优惠券名称：</span>\n          <el-input v-model=\"tableFrom.name\" placeholder=\"请输入优惠券名称\" class=\"selWidth\" clearable>\n            <el-button slot=\"append\" icon=\"el-icon-search\" size=\"small\" @click=\"seachList\" />\n          </el-input>\n        </div>\n      </div>\n      <router-link :to=\" { path: '/marketing/coupon/list/save' } \">\n        <el-button size=\"small\" type=\"primary\" v-hasPermi=\"['admin:coupon:save']\">添加优惠劵</el-button>\n      </router-link>\n    </div>\n    <el-table\n      v-loading=\"listLoading\"\n      :data=\"tableData.data\"\n      style=\"width: 100%\"\n      size=\"mini\"\n      :header-cell-style=\" {fontWeight:'bold'}\"\n    >\n      <el-table-column\n        prop=\"id\"\n        label=\"ID\"\n        min-width=\"50\"\n      />\n      <el-table-column\n        prop=\"name\"\n        label=\"名称\"\n        min-width=\"180\"\n      />\n      <el-table-column\n        label=\"类型\"\n        min-width=\"80\"\n      >\n        <template slot-scope=\"{ row }\">\n          <span>{{row.useType | couponUserTypeFilter}}</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        prop=\"money\"\n        label=\"面值\"\n        min-width=\"100\"\n      />\n      <el-table-column\n        prop=\"name\"\n        label=\"领取方式\"\n        min-width=\"100\"\n      >\n        <template slot-scope=\"{ row }\">\n          <span>{{row.type | couponTypeFilter}}</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        min-width=\"260\"\n        label=\"领取日期\"\n      >\n        <template slot-scope=\"{ row }\">\n          <div v-if=\"row.receiveEndTime\">\n            {{ row.receiveStartTime }} - {{ row.receiveEndTime }}\n          </div>\n          <span v-else>不限时</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        min-width=\"260\"\n        label=\"使用时间\"\n      >\n        <template slot-scope=\"{ row }\">\n          <div v-if=\"row.day\">\n            {{ row.day }}天\n          </div>\n          <span v-else>\n             {{ row.useStartTime }} - {{ row.useEndTime }}\n          </span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        min-width=\"100\"\n        label=\"发布数量\"\n      >\n        <template slot-scope=\"{ row }\">\n          <span v-if=\" !row.isLimited \">不限量</span>\n          <div v-else>\n            <span class=\"fa\">发布：{{ row.total }}</span>\n            <span class=\"sheng\">剩余：{{ row.lastTotal }}</span>\n          </div>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"是否开启\"\n        min-width=\"100\"\n      >\n        <template slot-scope=\"scope\" v-if=\"checkPermi(['admin:coupon:update:status'])\">\n          <el-switch\n            v-model=\"scope.row.status\"\n            :active-value=\"true\"\n            :inactive-value=\"false\"\n            active-text=\"开启\"\n            inactive-text=\"关闭\"\n            @click.native=\"onchangeIsShow(scope.row)\"\n          />\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" min-width=\"180\" fixed=\"right\">\n        <template slot-scope=\"scope\">\n          <el-button type=\"text\" class=\"mr10\" size=\"small\" @click=\"receive(scope.row)\" v-hasPermi=\"['admin:coupon:user:list']\">领取记录</el-button>\n          <router-link :to=\" { path: '/marketing/coupon/list/save/' + scope.row.id } \">\n            <el-button v-if=\"scope.row.status\" type=\"text\" size=\"small\" class=\"mr10\" v-hasPermi=\"['admin:coupon:info']\">复制</el-button>\n          </router-link>\n          <el-button type=\"text\" class=\"mr10\" size=\"small\" @click=\"handleDelMenu(scope.row)\" v-hasPermi=\"['admin:coupon:delete']\">删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <div class=\"block\">\n      <el-pagination\n        :page-sizes=\"[20, 40, 60, 80]\"\n        :page-size=\"tableFrom.limit\"\n        :current-page=\"tableFrom.page\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        :total=\"tableData.total\"\n        @size-change=\"handleSizeChange\"\n        @current-change=\"pageChange\"\n      />\n    </div>\n  </el-card>\n  <!--领取记录-->\n  <el-dialog\n    title=\"领取记录\"\n    :visible.sync=\"dialogVisible\"\n    width=\"500px\"\n    :before-close=\"handleClose\"\n  >\n    <el-table\n      v-loading=\"Loading\"\n      :data=\"issueData.data\"\n      style=\"width: 100%\"\n    >\n      <el-table-column\n        prop=\"nickname\"\n        label=\"用户名\"\n        min-width=\"120\"\n      />\n      <el-table-column label=\"用户头像\" min-width=\"80\">\n        <template slot-scope=\"scope\">\n          <div class=\"demo-image__preview\">\n            <el-image\n              style=\"width: 36px; height: 36px\"\n              :src=\"scope.row.avatar\"\n              :preview-src-list=\"[scope.row.avatar]\"\n            />\n          </div>\n        </template>\n      </el-table-column>\n      <el-table-column\n        prop=\"createTime\"\n        label=\"领取时间\"\n        min-width=\"180\"\n      />\n    </el-table>\n    <div class=\"block\">\n      <el-pagination\n        :page-sizes=\"[10, 20, 30, 40]\"\n        :page-size=\"tableFromIssue.limit\"\n        :current-page=\"tableFromIssue.page\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        :total=\"issueData.total\"\n        @size-change=\"handleSizeChangeIssue\"\n        @current-change=\"pageChangeIssue\"\n      />\n    </div>\n  </el-dialog>\n</div>\n", null]}