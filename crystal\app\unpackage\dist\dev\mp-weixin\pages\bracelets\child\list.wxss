@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.bigtitle.data-v-0b75b564 {
  height: 80rpx;
  line-height: 80rpx;
  background-color: #ffe4b7;
  font-size: 32rpx;
  font-weight: 600;
}
.circle-container.data-v-0b75b564 {
  position: relative;
  border-radius: 50%;
  border: gray 1px solid;
  transition: all 1s;
}
.marble.data-v-0b75b564 {
  position: absolute;
  transition: all 1s;
}
.content.data-v-0b75b564 {
  width: 710rpx;
  margin-left: 20rpx;
  margin-top: 20rpx;
  margin-bottom: 20rpx;
  background-color: white;
  border-radius: 16rpx;
}
.top.data-v-0b75b564 {
  gap: 10rpx;
  width: 710rpx;
  margin-left: 20rpx;
  margin-top: 20rpx;
  background-color: white;
  border-radius: 16rpx;
}
.botton.data-v-0b75b564 {
  background-color: #c9ab79;
  color: #fff;
  font-size: 22rpx;
  height: 65rpx;
  border-radius: 50rpx;
  text-align: center;
  line-height: 65rpx;
}
.botton_1.data-v-0b75b564 {
  background-color: #c9ab79;
  padding: 0 20rpx;
  color: #fff;
  font-size: 22rpx;
  height: 65rpx;
  border-radius: 50rpx;
  text-align: center;
  line-height: 65rpx;
}
.botton_2.data-v-0b75b564 {
  background-color: #DD5C5F;
  padding: 0 20rpx;
  color: #fff;
  font-size: 22rpx;
  height: 65rpx;
  border-radius: 50rpx;
  text-align: center;
  line-height: 65rpx;
}
.botton_3.data-v-0b75b564 {
  background-color: #398ade;
  padding: 0 20rpx;
  color: #fff;
  font-size: 22rpx;
  height: 65rpx;
  border-radius: 50rpx;
  text-align: center;
  line-height: 65rpx;
}
.comTc .comForm.data-v-0b75b564 {
  width: 100%;
  height: calc(100% - 100rpx);
  overflow-y: auto;
}
.comTc .operate.data-v-0b75b564 {
  height: 100rpx;
  padding: 0 30rpx;
  margin-top: 20rpx;
  display: flex;
  align-items: center;
}
.comTc .operate .but.data-v-0b75b564 {
  flex: 1;
  height: 56rpx;
  line-height: 56rpx;
  text-align: center;
  border-radius: 10rpx;
  border: 2rpx solid #084AA1;
  background-color: #084AA1;
  color: #FFF;
  margin-right: 20rpx;
}
.comTc .operate .but.data-v-0b75b564:last-child {
  margin-right: 0;
}
.comTc .operate .but.grey.data-v-0b75b564 {
  border: 2rpx solid #eee;
  background-color: #FFF;
  color: #666;
}
.form-group.data-v-0b75b564 {
  position: relative;
  margin-bottom: 20px;
}
.form-group input.data-v-0b75b564 {
  padding: 10px;
  margin-bottom: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
}
.unit.data-v-0b75b564 {
  position: absolute;
  right: 10px;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  color: #888;
}
.hand-text-1.data-v-0b75b564 {
  font-size: 24rpx;
  font-weight: 600;
  color: #888888;
  margin: 10rpx 0;
}
.hand-text-2.data-v-0b75b564 {
  font-size: 24rpx;
  font-weight: 600;
  color: #888888;
  margin: 10rpx 0;
}
.lianying.data-v-0b75b564 {
  position: fixed;
  bottom: 100rpx;
  right: 0;
  width: 100rpx;
  height: 100rpx;
  background-color: #c9ab79;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-direction: column;
  font-size: 28rpx;
  font-weight: 600;
  line-height: 36rpx;
}

