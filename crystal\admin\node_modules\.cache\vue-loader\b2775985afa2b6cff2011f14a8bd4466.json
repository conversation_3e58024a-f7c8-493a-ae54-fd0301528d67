{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\userprocess-add-and-update.vue?vue&type=template&id=4d28cc6e", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\userprocess-add-and-update.vue", "mtime": 1753666157945}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\n<el-dialog :title=\"!dataForm.id ? '新增' : '修改'\" :close-on-click-modal=\"false\" :visible.sync=\"visible\">\n  <el-form :model=\"dataForm\" :rules=\"dataRule\" ref=\"dataForm\" label-width=\"120px\">\n    <el-form-item label=\"记录名称\" prop=\"name\">\n      <el-input v-model=\"dataForm.name\" placeholder=\"记录名称\"></el-input>\n    </el-form-item>\n    <el-form-item label=\"记录状态\" prop=\"userStatus\">\n      <el-select v-model=\"dataForm.userStatus\" placeholder=\"记录状态\" filterable>\n        <el-option v-for=\"item in userStatus\" :key=\"item\" :label=\"item\" :value=\"item\"></el-option>\n      </el-select>\n    </el-form-item>\n    <el-form-item label=\"记录时间\" prop=\"doTime\">\n      <el-date-picker style=\"width: 100%\" v-model=\"dataForm.doTime\" type=\"datetime\" :placeholder=\"'记录时间'\"\n        value-format=\"yyyy/MM/dd HH:mm:ss\">\n      </el-date-picker>\n    </el-form-item>\n    <el-form-item label=\"备注\" prop=\"remarks\">\n      <Tinymce v-model=\"dataForm.remarks\"></Tinymce>\n    </el-form-item>\n  </el-form>\n  <span slot=\"footer\" class=\"dialog-footer\">\n    <el-button @click=\"visible = false\">取消</el-button>\n    <el-button type=\"primary\" @click=\"dataFormSubmit()\">确定</el-button>\n  </span>\n</el-dialog>\n", null]}