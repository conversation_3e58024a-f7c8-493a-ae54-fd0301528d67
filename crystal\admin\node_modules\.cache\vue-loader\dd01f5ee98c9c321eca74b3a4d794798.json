{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\marketing\\bargain\\bargainGoods\\index.vue?vue&type=template&id=139608a7&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\marketing\\bargain\\bargainGoods\\index.vue", "mtime": 1753666157888}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"divBox\" },\n    [\n      _c(\n        \"el-card\",\n        { staticClass: \"box-card\" },\n        [\n          _c(\n            \"div\",\n            {\n              staticClass: \"clearfix\",\n              attrs: { slot: \"header\" },\n              slot: \"header\",\n            },\n            [\n              _c(\n                \"div\",\n                { staticClass: \"container\" },\n                [\n                  _c(\n                    \"el-form\",\n                    { attrs: { inline: \"\" } },\n                    [\n                      _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"砍价状态：\" } },\n                        [\n                          _c(\n                            \"el-select\",\n                            {\n                              staticClass: \"filter-item selWidth mr20\",\n                              attrs: { placeholder: \"请选择\", clearable: \"\" },\n                              on: {\n                                change: function ($event) {\n                                  return _vm.getList(1)\n                                },\n                              },\n                              model: {\n                                value: _vm.tableFrom.status,\n                                callback: function ($$v) {\n                                  _vm.$set(_vm.tableFrom, \"status\", $$v)\n                                },\n                                expression: \"tableFrom.status\",\n                              },\n                            },\n                            [\n                              _c(\"el-option\", {\n                                attrs: { label: \"关闭\", value: 0 },\n                              }),\n                              _vm._v(\" \"),\n                              _c(\"el-option\", {\n                                attrs: { label: \"开启\", value: 1 },\n                              }),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"商品搜索：\" } },\n                        [\n                          _c(\n                            \"el-input\",\n                            {\n                              staticClass: \"selWidth\",\n                              attrs: {\n                                placeholder: \"请输入商品名称、ID\",\n                                clearable: \"\",\n                              },\n                              model: {\n                                value: _vm.tableFrom.keywords,\n                                callback: function ($$v) {\n                                  _vm.$set(_vm.tableFrom, \"keywords\", $$v)\n                                },\n                                expression: \"tableFrom.keywords\",\n                              },\n                            },\n                            [\n                              _c(\"el-button\", {\n                                attrs: {\n                                  slot: \"append\",\n                                  icon: \"el-icon-search\",\n                                },\n                                on: {\n                                  click: function ($event) {\n                                    return _vm.getList(1)\n                                  },\n                                },\n                                slot: \"append\",\n                              }),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"router-link\",\n                { attrs: { to: { path: \"/marketing/bargain/creatBargain\" } } },\n                [\n                  _c(\n                    \"el-button\",\n                    {\n                      directives: [\n                        {\n                          name: \"hasPermi\",\n                          rawName: \"v-hasPermi\",\n                          value: [\"admin:bargain:save\"],\n                          expression: \"['admin:bargain:save']\",\n                        },\n                      ],\n                      staticClass: \"mr10\",\n                      attrs: { size: \"mini\", type: \"primary\" },\n                    },\n                    [_vm._v(\"添加砍价商品\")]\n                  ),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-button\",\n                {\n                  directives: [\n                    {\n                      name: \"hasPermi\",\n                      rawName: \"v-hasPermi\",\n                      value: [\"admin:export:excel:bargain\"],\n                      expression: \"['admin:export:excel:bargain']\",\n                    },\n                  ],\n                  staticClass: \"mr10\",\n                  attrs: { size: \"mini\" },\n                  on: { click: _vm.exportList },\n                },\n                [_vm._v(\"导出\")]\n              ),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-table\",\n            {\n              directives: [\n                {\n                  name: \"loading\",\n                  rawName: \"v-loading\",\n                  value: _vm.listLoading,\n                  expression: \"listLoading\",\n                },\n              ],\n              ref: \"multipleTable\",\n              staticStyle: { width: \"100%\" },\n              attrs: {\n                data: _vm.tableData.data,\n                size: \"mini\",\n                \"header-cell-style\": { fontWeight: \"bold\" },\n              },\n            },\n            [\n              _c(\"el-table-column\", {\n                attrs: { prop: \"id\", label: \"ID\", \"min-width\": \"50\" },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { label: \"砍价图片\", \"min-width\": \"80\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\n                          \"div\",\n                          { staticClass: \"demo-image__preview\" },\n                          [\n                            _c(\"el-image\", {\n                              staticStyle: { width: \"36px\", height: \"36px\" },\n                              attrs: {\n                                src: scope.row.image,\n                                \"preview-src-list\": [scope.row.image],\n                              },\n                            }),\n                          ],\n                          1\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { label: \"砍价名称\", prop: \"title\", \"min-width\": \"300\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\n                          \"el-popover\",\n                          {\n                            attrs: {\n                              trigger: \"hover\",\n                              placement: \"right\",\n                              \"open-delay\": 800,\n                            },\n                          },\n                          [\n                            _c(\n                              \"div\",\n                              {\n                                staticClass: \"text_overflow\",\n                                attrs: { slot: \"reference\" },\n                                slot: \"reference\",\n                              },\n                              [_vm._v(_vm._s(scope.row.title))]\n                            ),\n                            _vm._v(\" \"),\n                            _c(\"div\", { staticClass: \"pup_card\" }, [\n                              _vm._v(_vm._s(scope.row.title)),\n                            ]),\n                          ]\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: \"砍价价格\",\n                  prop: \"price\",\n                  \"min-width\": \"100\",\n                  align: \"center\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: \"最低价\",\n                  prop: \"minPrice\",\n                  \"min-width\": \"100\",\n                  align: \"center\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: \"参与人数\",\n                  prop: \"countPeopleAll\",\n                  \"min-width\": \"100\",\n                  align: \"center\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: \"帮忙砍价人数\",\n                  prop: \"countPeopleHelp\",\n                  \"min-width\": \"100\",\n                  align: \"center\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: \"砍价成功人数\",\n                  prop: \"countPeopleSuccess\",\n                  \"min-width\": \"100\",\n                  align: \"center\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: \"限量\",\n                  \"min-width\": \"100\",\n                  prop: \"quotaShow\",\n                  align: \"center\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: \"限量剩余\",\n                  prop: \"surplusQuota\",\n                  \"min-width\": \"100\",\n                  align: \"center\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"stopTime\",\n                  label: \"活动时间\",\n                  \"min-width\": \"160\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _vm._v(\n                          \"\\n          \" +\n                            _vm._s(\n                              scope.row.startTime + \" ~ \" + scope.row.stopTime\n                            ) +\n                            \"\\n        \"\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { label: \"砍价状态\", \"min-width\": \"150\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _vm.checkPermi([\"admin:bargain:update:status\"])\n                          ? _c(\"el-switch\", {\n                              attrs: {\n                                \"active-value\": true,\n                                \"inactive-value\": false,\n                                \"active-text\": \"开启\",\n                                \"inactive-text\": \"关闭\",\n                              },\n                              on: {\n                                change: function ($event) {\n                                  return _vm.onchangeIsShow(scope.row)\n                                },\n                              },\n                              model: {\n                                value: scope.row.status,\n                                callback: function ($$v) {\n                                  _vm.$set(scope.row, \"status\", $$v)\n                                },\n                                expression: \"scope.row.status\",\n                              },\n                            })\n                          : _vm._e(),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: \"操作\",\n                  \"min-width\": \"130\",\n                  fixed: \"right\",\n                  align: \"center\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\n                          \"router-link\",\n                          {\n                            attrs: {\n                              to: {\n                                path:\n                                  \"/marketing/bargain/creatBargain/\" +\n                                  scope.row.id,\n                              },\n                            },\n                          },\n                          [\n                            _c(\n                              \"el-button\",\n                              {\n                                directives: [\n                                  {\n                                    name: \"hasPermi\",\n                                    rawName: \"v-hasPermi\",\n                                    value: [\"admin:bargain:info\"],\n                                    expression: \"['admin:bargain:info']\",\n                                  },\n                                ],\n                                attrs: { type: \"text\", size: \"small\" },\n                              },\n                              [_vm._v(\"编辑\")]\n                            ),\n                          ],\n                          1\n                        ),\n                        _vm._v(\" \"),\n                        _c(\n                          \"el-button\",\n                          {\n                            directives: [\n                              {\n                                name: \"hasPermi\",\n                                rawName: \"v-hasPermi\",\n                                value: [\"admin:bargain:delete\"],\n                                expression: \"['admin:bargain:delete']\",\n                              },\n                            ],\n                            staticClass: \"mr10\",\n                            attrs: { type: \"text\", size: \"small\" },\n                            on: {\n                              click: function ($event) {\n                                return _vm.handleDelete(\n                                  scope.row.id,\n                                  scope.$index\n                                )\n                              },\n                            },\n                          },\n                          [_vm._v(\"删除\")]\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"div\",\n            { staticClass: \"block mb20\" },\n            [\n              _vm.checkPermi([\"admin:bargain:list\"])\n                ? _c(\"el-pagination\", {\n                    attrs: {\n                      \"page-sizes\": [10, 20, 30, 40],\n                      \"page-size\": _vm.tableFrom.limit,\n                      \"current-page\": _vm.tableFrom.page,\n                      layout: \"total, sizes, prev, pager, next, jumper\",\n                      total: _vm.tableData.total,\n                    },\n                    on: {\n                      \"size-change\": _vm.handleSizeChange,\n                      \"current-change\": _vm.pageChange,\n                    },\n                  })\n                : _vm._e(),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}