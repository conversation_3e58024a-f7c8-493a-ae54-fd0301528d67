<view class="data-v-21ff4d10"><view class="line data-v-21ff4d10"><block wx:if="{{$root.g0}}"><image src="../../../static/images/line.jpg" class="data-v-21ff4d10"></image></block></view><view class="{{['address-management','data-v-21ff4d10',$root.g1?'fff':'']}}"><block wx:if="{{$root.g2}}"><radio-group data-event-opts="{{[['change',[['radioChange',['$event']]]]]}}" class="radio-group data-v-21ff4d10" bindchange="__e"><block wx:for="{{addressList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item borRadius14 data-v-21ff4d10"><view data-event-opts="{{[['tap',[['goOrder',['$0'],[[['addressList','',index,'id']]]]]]]}}" class="address data-v-21ff4d10" bindtap="__e"><view class="consignee data-v-21ff4d10">{{"收货人："+item.realName}}<text class="phone data-v-21ff4d10">{{item.phone}}</text></view><view class="data-v-21ff4d10">{{"收货地址："+item.province+item.city+item.district+item.detail}}</view></view><view class="operation acea-row row-between-wrapper data-v-21ff4d10"><radio class="radio data-v-21ff4d10" value="{{index}}" checked="{{item.isDefault}}"><text class="data-v-21ff4d10">设为默认</text></radio><view class="acea-row row-middle data-v-21ff4d10"><view data-event-opts="{{[['tap',[['editAddress',['$0'],[[['addressList','',index,'id']]]]]]]}}" bindtap="__e" class="data-v-21ff4d10"><text class="iconfont icon-bianji data-v-21ff4d10"></text>编辑</view><view data-event-opts="{{[['tap',[['delAddress',[index]]]]]}}" bindtap="__e" class="data-v-21ff4d10"><text class="iconfont icon-shanchu data-v-21ff4d10"></text>删除</view></view></view></view></block></radio-group></block><block wx:if="{{$root.g3}}"><view class="loadingicon acea-row row-center-wrapper data-v-21ff4d10"><text class="loading iconfont icon-jiazai data-v-21ff4d10" hidden="{{loading==false}}"></text>{{loadTitle+''}}</view></block><block wx:if="{{$root.g4}}"><view class="noCommodity data-v-21ff4d10"><view class="pictrue data-v-21ff4d10"><image src="../../../static/images/noAddress.png" class="data-v-21ff4d10"></image></view></view></block><view style="height:120rpx;" class="data-v-21ff4d10"></view></view><view class="footer acea-row row-between-wrapper data-v-21ff4d10"><view data-event-opts="{{[['tap',[['addAddress',['$event']]]]]}}" class="addressBnt bg-color data-v-21ff4d10" bindtap="__e"><text class="iconfont icon-tianjiadizhi data-v-21ff4d10"></text>添加新地址</view><view data-event-opts="{{[['tap',[['getWxAddress',['$event']]]]]}}" class="addressBnt wxbnt data-v-21ff4d10" bindtap="__e"><text class="iconfont icon-weixin2 data-v-21ff4d10"></text>导入微信地址</view></view><home vue-id="1dead5f6-1" class="data-v-21ff4d10" bind:__l="__l"></home></view>