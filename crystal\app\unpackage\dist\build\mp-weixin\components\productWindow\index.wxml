<view class="data-v-31e4016d"><view class="{{['product-window','data-v-31e4016d',(attr.cartAttr===true?'on':'')+' '+(iSbnt?'join':'')+' '+(iScart?'joinCart':'')]}}"><view class="textpic acea-row row-between-wrapper data-v-31e4016d"><view class="pictrue data-v-31e4016d"><image src="{{attr.productSelect.image}}" class="data-v-31e4016d"></image></view><view class="text data-v-31e4016d"><view class="line1 data-v-31e4016d">{{''+attr.productSelect.storeName+''}}</view><view class="money font-color data-v-31e4016d">￥<text class="num data-v-31e4016d">{{attr.productSelect.price}}</text><block wx:if="{{isShow}}"><text class="stock data-v-31e4016d">{{"库存: "+attr.productSelect.stock}}</text></block><block wx:if="{{limitNum}}"><text class="stock data-v-31e4016d">{{"限量: "+attr.productSelect.quota}}</text></block></view></view><view data-event-opts="{{[['tap',[['closeAttr',['$event']]]]]}}" class="iconfont icon-guanbi data-v-31e4016d" bindtap="__e"></view></view><view class="rollTop data-v-31e4016d"><view class="productWinList data-v-31e4016d"><block wx:for="{{attr.productAttr}}" wx:for-item="item" wx:for-index="indexw" wx:key="indexw"><view class="item data-v-31e4016d"><view class="title data-v-31e4016d">{{item.attrName}}</view><view class="listn acea-row row-middle data-v-31e4016d"><block wx:for="{{item.attrValues}}" wx:for-item="itemn" wx:for-index="indexn" wx:key="indexn"><view data-event-opts="{{[['tap',[['tapAttr',[indexw,indexn]]]]]}}" class="{{['itemn','data-v-31e4016d',item.index===itemn?'on':'']}}" bindtap="__e">{{''+itemn+''}}</view></block></view></view></block></view><view class="cart acea-row row-between-wrapper data-v-31e4016d"><view class="title data-v-31e4016d">数量</view><view class="carnum acea-row row-left data-v-31e4016d"><view data-event-opts="{{[['tap',[['CartNumDes',['$event']]]]]}}" class="{{['item','reduce','data-v-31e4016d',attr.productSelect.cart_num<=1?'on':'']}}" bindtap="__e">-</view><view class="item num data-v-31e4016d"><input type="number" data-name="productSelect.cart_num" data-event-opts="{{[['input',[['__set_model',['$0','cart_num','$event',[]],['attr.productSelect']],['bindCode',['$0'],['attr.productSelect.cart_num']]]]]}}" value="{{attr.productSelect.cart_num}}" bindinput="__e" class="data-v-31e4016d"/></view><block wx:if="{{iSplus}}"><view data-event-opts="{{[['tap',[['CartNumAdd',['$event']]]]]}}" class="{{['item','plus','data-v-31e4016d',attr.productSelect.cart_num>=attr.productSelect.stock?'on':'']}}" bindtap="__e">+</view></block><block wx:else><view data-event-opts="{{[['tap',[['CartNumAdd',['$event']]]]]}}" class="{{['item','plus','data-v-31e4016d',attr.productSelect.cart_num>=attr.productSelect.quota||attr.productSelect.cart_num>=attr.productSelect.stock||attr.productSelect.cart_num>=attr.productSelect.num?'on':'']}}" bindtap="__e">+</view></block></view></view></view><block wx:if="{{iSbnt&&attr.productSelect.stock>0&&attr.productSelect.quota>0}}"><view data-event-opts="{{[['tap',[['goCat',['$event']]]]]}}" class="joinBnt bg-color data-v-31e4016d" bindtap="__e">我要参团</view></block><block wx:else><block wx:if="{{iSbnt&&attr.productSelect.quota<=0||iSbnt&&attr.productSelect.stock<=0}}"><view class="joinBnt on data-v-31e4016d">已售罄</view></block></block><block wx:if="{{iScart&&attr.productSelect.stock}}"><view data-event-opts="{{[['tap',[['goCat',['$event']]]]]}}" class="joinBnt bg-color data-v-31e4016d" bindtap="__e">确定</view></block><block wx:else><block wx:if="{{iScart&&!attr.productSelect.stock}}"><view class="joinBnt on data-v-31e4016d">已售罄</view></block></block></view><view class="mask data-v-31e4016d" hidden="{{attr.cartAttr===false}}" data-event-opts="{{[['touchmove',[['',['$event']]]],['tap',[['closeAttr',['$event']]]]]}}" bindtouchmove="__e" bindtap="__e"></view></view>