<view class="data-v-66177934"><view class="{{['coupon-window','data-v-66177934',window==true?'on':'']}}"><view class="couponWinList data-v-66177934"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item acea-row row-between-wrapper data-v-66177934"><view class="money font-color data-v-66177934">￥<text class="num data-v-66177934">{{item.$orig.money?item.m0:''}}</text></view><view class="text data-v-66177934"><view class="name data-v-66177934">{{"购物买"+item.$orig.minPrice+"减"+item.$orig.money}}</view><block wx:if="{{item.$orig.day>0}}"><view class="data-v-66177934">{{"领取后"+item.$orig.day+"天内可用"}}</view></block><block wx:else><view class="data-v-66177934">{{item.$orig.useStartTimeStr&&item.$orig.useEndTimeStr?item.$orig.useStartTimeStr+'~'+item.$orig.useEndTimeStr:''}}</view></block></view></view></block></view><view class="lid data-v-66177934"><navigator class="bnt font-color data-v-66177934" hover-class="none" url="/pages/users/user_get_coupon/index">立即领取</navigator><view data-event-opts="{{[['tap',[['close',['$event']]]]]}}" class="iconfont icon-guanbi3 data-v-66177934" bindtap="__e"></view></view></view><view class="mask data-v-66177934" catchtouchmove="true" hidden="{{window==false}}"></view></view>