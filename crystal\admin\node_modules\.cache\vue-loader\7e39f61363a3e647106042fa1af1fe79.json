{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\userList\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\userList\\index.vue", "mtime": 1753666157797}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport { userListApi } from '@/api/user'\r\nexport default {\r\n  name: 'UserList',\r\n  filters: {\r\n    saxFilter(status) {\r\n      const statusMap = {\r\n        0: '未知',\r\n        1: '男',\r\n        2: '女'\r\n      }\r\n      return statusMap[status]\r\n    },\r\n    statusFilter(status) {\r\n      const statusMap = {\r\n        'wechat': '微信用户',\r\n        'routine': '小程序用户'\r\n      }\r\n      return statusMap[status]\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      templateRadio: 0,\r\n      loading: false,\r\n      tableData: {\r\n        data: [],\r\n        total: 0\r\n      },\r\n      tableFrom: {\r\n        page: 1,\r\n        limit: 10,\r\n        keywords: ''\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    getTemplateRow(idx, row) {\r\n       this.$emit('getTemplateRow', row);\r\n    },\r\n    // 列表\r\n    getList() {\r\n      this.loading = true\r\n      userListApi(this.tableFrom).then(res => {\r\n        this.tableData.data = res.list\r\n        this.tableData.total = res.total\r\n        this.loading = false\r\n      }).catch(res => {\r\n        this.$message.error(res.message)\r\n        this.loading = false\r\n      })\r\n    },\r\n    search(){\r\n       this.loading = true\r\n      userListApi({keywords:this.tableFrom.keywords}).then(res => {\r\n        this.tableData.data = res.list\r\n        this.tableData.total = res.total\r\n        this.loading = false\r\n      }).catch(res => {\r\n        this.$message.error(res.message)\r\n        this.loading = false\r\n      })\r\n    },\r\n    pageChange(page) {\r\n      this.tableFrom.page = page\r\n      this.getList()\r\n    },\r\n    handleSizeChange(val) {\r\n      this.tableFrom.limit = val\r\n      this.getList()\r\n    }\r\n  }\r\n}\r\n", null]}