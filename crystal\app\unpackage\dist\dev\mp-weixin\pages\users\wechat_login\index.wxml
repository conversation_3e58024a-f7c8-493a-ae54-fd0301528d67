<view class="page"><view class="system-height" style="{{'height:'+(statusBarHeight)+';'}}"></view><view class="title-bar" style="height:43px;"><block wx:if="{{!isHome}}"><view data-event-opts="{{[['tap',[['back',['$event']]]]]}}" class="icon" bindtap="__e"><image src="../static/left.png"></image></view></block><block wx:else><view data-event-opts="{{[['tap',[['home',['$event']]]]]}}" class="icon" bindtap="__e"><image src="../static/home.png"></image></view></block>账户登录</view><view class="wechat_login"><view class="img"><image src="../static/wechat_login.png" mode="widthFix"></image></view><view class="btn-wrapper"><button class="bg-green btn1" hover-class="none" data-event-opts="{{[['tap',[['getUserProfile',['$event']]]]]}}" bindtap="__e">微信登录</button></view></view><block wx:if="{{isUp}}"><block><mobile-login vue-id="42503e73-1" isUp="{{isUp}}" authKey="{{authKey}}" data-event-opts="{{[['^close',[['maskClose']]],['^wechatPhone',[['wechatPhone']]]]}}" bind:close="__e" bind:wechatPhone="__e" bind:__l="__l"></mobile-login></block></block><block wx:if="{{isPhoneBox}}"><block><routine-phone vue-id="42503e73-2" logoUrl="{{logoUrl}}" isPhoneBox="{{isPhoneBox}}" authKey="{{authKey}}" data-event-opts="{{[['^close',[['bindPhoneClose']]]]}}" bind:close="__e" bind:__l="__l"></routine-phone></block></block></view>