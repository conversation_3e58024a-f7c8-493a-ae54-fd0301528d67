{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\marketing\\coupon\\list\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\marketing\\coupon\\list\\index.vue", "mtime": 1753666157892}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _marketing = require(\"@/api/marketing\");\nvar _settings = require(\"@/settings\");\nvar _permission = require(\"@/utils/permission\");\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n// 权限判断函数\nvar _default = exports.default = {\n  name: 'CouponList',\n  data: function data() {\n    return {\n      Loading: false,\n      dialogVisible: false,\n      roterPre: _settings.roterPre,\n      listLoading: true,\n      tableData: {\n        data: [],\n        total: 0\n      },\n      tableFrom: {\n        page: 1,\n        limit: 20,\n        status: '',\n        name: ''\n      },\n      tableFromIssue: {\n        page: 1,\n        limit: 10,\n        couponId: ''\n      },\n      issueData: {\n        data: [],\n        total: 0\n      }\n    };\n  },\n  mounted: function mounted() {\n    this.getList();\n  },\n  methods: {\n    checkPermi: _permission.checkPermi,\n    seachList: function seachList() {\n      this.tableFrom.page = 1;\n      this.getList();\n    },\n    handleClose: function handleClose() {\n      this.dialogVisible = false;\n    },\n    // 领取记录\n    receive: function receive(row) {\n      this.dialogVisible = true;\n      this.tableFromIssue.couponId = row.id;\n      this.getIssueList();\n    },\n    // 列表\n    getIssueList: function getIssueList() {\n      var _this = this;\n      this.Loading = true;\n      (0, _marketing.couponUserListApi)(this.tableFromIssue).then(function (res) {\n        _this.issueData.data = res.list;\n        _this.issueData.total = res.total;\n        _this.Loading = false;\n      }).catch(function (res) {\n        _this.Loading = false;\n        _this.$message.error(res.message);\n      });\n    },\n    pageChangeIssue: function pageChangeIssue(page) {\n      this.tableFromIssue.page = page;\n      this.getIssueList();\n    },\n    handleSizeChangeIssue: function handleSizeChangeIssue(val) {\n      this.tableFromIssue.limit = val;\n      this.getIssueList();\n    },\n    // 列表\n    getList: function getList() {\n      var _this2 = this;\n      this.listLoading = true;\n      (0, _marketing.marketingListApi)(this.tableFrom).then(function (res) {\n        _this2.tableData.data = res.list;\n        _this2.tableData.total = res.total;\n        _this2.listLoading = false;\n      }).catch(function (res) {\n        _this2.listLoading = false;\n      });\n    },\n    pageChange: function pageChange(page) {\n      this.tableFrom.page = page;\n      this.getList();\n    },\n    handleSizeChange: function handleSizeChange(val) {\n      this.tableFrom.limit = val;\n      this.getList();\n    },\n    // 修改状态\n    onchangeIsShow: function onchangeIsShow(row) {\n      var _this3 = this;\n      (0, _marketing.couponIssueStatusApi)({\n        id: row.id,\n        status: row.status\n      }).then(function () {\n        _this3.$message.success('修改成功');\n        _this3.getList();\n      }).catch(function () {\n        row.status = !row.status;\n      });\n    },\n    handleDelMenu: function handleDelMenu(rowData) {\n      var _this4 = this;\n      this.$confirm('确定删除当前数据?').then(function () {\n        (0, _marketing.couponDeleteApi)({\n          id: rowData.id\n        }).then(function (data) {\n          _this4.$message.success('删除成功');\n          _this4.getList();\n        });\n      });\n    }\n  }\n};", null]}