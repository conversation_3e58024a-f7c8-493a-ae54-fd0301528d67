{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\order\\orderDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\order\\orderDetail.vue", "mtime": 1753666157910}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n0\r\nimport { orderDetailApi, getLogisticsInfoApi } from '@/api/order'\r\nexport default {\r\n  name: 'OrderDetail',\r\n  props: {\r\n    orderId: {\r\n      type: String,\r\n      default: 0\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      reverse: true,\r\n      dialogVisible: false,\r\n      orderDatalist: null,\r\n      loading: false,\r\n      modal2: false,\r\n      result: []\r\n    }\r\n  },\r\n  mounted() {\r\n  },\r\n  methods: {\r\n    openLogistics () {\r\n      this.getOrderData()\r\n      this.modal2 = true;\r\n    },\r\n    // 获取订单物流信息\r\n    getOrderData () {\r\n      getLogisticsInfoApi({orderNo:this.orderId}).then(async res => {\r\n        this.result = res.list;\r\n      })\r\n    },\r\n    getDetail(id) {\r\n      this.loading = true\r\n      orderDetailApi({orderNo: id}).then(res => {\r\n        this.orderDatalist = res\r\n        this.loading = false\r\n      }).catch(() => {\r\n        this.orderDatalist = null\r\n        this.loading = false\r\n      })\r\n    }\r\n  }\r\n}\r\n", null]}