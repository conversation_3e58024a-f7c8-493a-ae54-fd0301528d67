@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.apply-return.data-v-56ab2edc {
  padding: 20rpx 30rpx 70rpx 30rpx;
}
.apply-return .list.data-v-56ab2edc {
  background-color: #fff;
  margin-top: 18rpx;
  padding: 0 24rpx 70rpx 24rpx;
}
.apply-return .list .item.data-v-56ab2edc {
  min-height: 90rpx;
  border-bottom: 1rpx solid #eee;
  font-size: 30rpx;
  color: #333;
}
.apply-return .list .item .num.data-v-56ab2edc {
  color: #282828;
  width: 427rpx;
  text-align: right;
}
.apply-return .list .item .num .picker .reason.data-v-56ab2edc {
  width: 385rpx;
}
.apply-return .list .item .num .picker .iconfont.data-v-56ab2edc {
  color: #666;
  font-size: 30rpx;
  margin-top: 2rpx;
}
.apply-return .list .item.textarea.data-v-56ab2edc {
  padding: 24rpx 0;
}
.apply-return .list .item textarea.data-v-56ab2edc {
  height: 100rpx;
  font-size: 30rpx;
}
.apply-return .list .item .placeholder.data-v-56ab2edc {
  color: #bbb;
}
.apply-return .list .item .title.data-v-56ab2edc {
  height: 95rpx;
  width: 100%;
}
.apply-return .list .item .title .tip.data-v-56ab2edc {
  font-size: 30rpx;
  color: #bbb;
}
.apply-return .list .item .upload.data-v-56ab2edc {
  padding-bottom: 36rpx;
}
.apply-return .list .item .upload .pictrue.data-v-56ab2edc {
  border-radius: 14rpx;
  margin: 22rpx 23rpx 0 0;
  width: 156rpx;
  height: 156rpx;
  position: relative;
  font-size: 24rpx;
  color: #bbb;
}
.apply-return .list .item .upload .pictrue.data-v-56ab2edc:nth-of-type(4n) {
  margin-right: 0;
}
.apply-return .list .item .upload .pictrue image.data-v-56ab2edc {
  width: 100%;
  height: 100%;
  border-radius: 14rpx;
}
.apply-return .list .item .upload .pictrue .icon-guanbi1.data-v-56ab2edc {
  position: absolute;
  font-size: 45rpx;
  top: -10rpx;
  right: -10rpx;
}
.apply-return .list .item .upload .pictrue .icon-icon25201.data-v-56ab2edc {
  color: #bfbfbf;
  font-size: 50rpx;
}
.apply-return .list .item .upload .pictrue.data-v-56ab2edc:nth-last-child(1) {
  border: 1rpx solid #ddd;
  box-sizing: border-box;
}
.apply-return .returnBnt.data-v-56ab2edc {
  font-size: 32rpx;
  color: #fff;
  width: 100%;
  height: 86rpx;
  border-radius: 50rpx;
  text-align: center;
  line-height: 86rpx;
  margin: 43rpx auto;
}
.circle-container.data-v-56ab2edc {
  position: relative;
  border-radius: 50%;
  border: gray 1px solid;
  transition: all 1s;
}
.marble.data-v-56ab2edc {
  position: absolute;
  transition: all 1s;
}

