{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\creatStore\\braceletsIndex.vue?vue&type=template&id=c58d0934&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\creatStore\\braceletsIndex.vue", "mtime": 1753666157922}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\n<div class=\"divBox\">\n  <el-card class=\"box-card\">\n    <!-- <div slot=\"header\" class=\"clearfix\">\n      <el-steps :active=\"currentTab\" align-center finish-status=\"success\">\n        <el-step title=\"珠子信息\" />\n        <el-step title=\"珠子详情\" />\n        <el-step title=\"其他设置\" />\n      </el-steps>\n    </div> -->\n    <el-form ref=\"formValidate\" v-loading=\"fullscreenLoading\" class=\"formValidate mt20\" :rules=\"ruleValidate\"\n      :model=\"formValidate\" label-width=\"120px\" @submit.native.prevent>\n      <el-row v-show=\"currentTab === 0\" :gutter=\"24\">\n        <!-- 珠子信息-->\n        <el-col v-bind=\"grid2\">\n          <el-form-item label=\"珠子名称：\" prop=\"storeName\">\n            <el-input v-model=\"formValidate.storeName\" maxlength=\"249\" placeholder=\"请输入珠子名称\" :disabled=\"isDisabled\" />\n          </el-form-item>\n        </el-col>\n        <el-col v-bind=\"grid2\">\n          <el-form-item label=\"珠子分类：\" prop=\"cateIds\">\n            <el-cascader v-model=\"formValidate.cateIds\" :options=\"merCateList\" :props=\"props2\" clearable\n              class=\"selWidth\" :show-all-levels=\"false\" :disabled=\"isDisabled\" />\n          </el-form-item>\n        </el-col>\n        <!-- <el-col v-bind=\"grid2\">\n          <el-form-item label=\"珠子关键字：\" prop=\"keyword\">\n            <el-input v-model=\"formValidate.keyword\" placeholder=\"请输入珠子关键字\" :disabled=\"isDisabled\"/>\n          </el-form-item>\n        </el-col> -->\n        <el-col v-bind=\"grid2\">\n          <el-form-item label=\"宽度：\" prop=\"width\">\n            <el-input v-model=\"formValidate.width\" placeholder=\"请输入宽度\" :disabled=\"isDisabled\" />\n            <div style=\"color: red;\">单位：mm</div>\n          </el-form-item>\n        </el-col>\n        <el-col v-bind=\"grid2\">\n          <el-form-item label=\"高度：\" prop=\"height\">\n            <el-input v-model=\"formValidate.height\" placeholder=\"请输入高度\" :disabled=\"isDisabled\" />\n            <div style=\"color: red;\">单位：mm</div>\n          </el-form-item>\n        </el-col>\n        <!-- <el-col v-bind=\"grid2\">\n          <el-form-item label=\"单位：\" prop=\"unitName\">\n            <el-input v-model=\"formValidate.unitName\" placeholder=\"请输入单位\"  :disabled=\"isDisabled\"/>\n          </el-form-item>\n        </el-col> -->\n        <el-col v-bind=\"grid2\">\n          <el-form-item label=\"珠子简介：\" prop=\"storeInfo\">\n            <el-input v-model=\"formValidate.storeInfo\" type=\"textarea\" maxlength=\"250\" :rows=\"3\" placeholder=\"请输入珠子简介\"\n              :disabled=\"isDisabled\" />\n          </el-form-item>\n        </el-col>\n        <el-col v-bind=\"grid2\">\n          <el-form-item label=\"珠子封面图：\" prop=\"image\">\n            <div class=\"upLoadPicBox\" @click=\"modalPicTap('1')\" :disabled=\"isDisabled\">\n              <div v-if=\"formValidate.image\" class=\"pictrue\"><img :src=\"formValidate.image\"></div>\n              <div v-else class=\"upLoad\">\n                <i class=\"el-icon-camera cameraIconfont\" />\n              </div>\n            </div>\n          </el-form-item>\n        </el-col>\n        <!-- <el-col :span=\"24\">\n          <el-form-item label=\"珠子轮播图：\" prop=\"sliderImages\">\n            <div class=\"acea-row\">\n              <div\n                v-for=\"(item,index) in formValidate.sliderImages\"\n                :key=\"index\"\n                class=\"pictrue\"\n                draggable=\"true\"\n                @dragstart=\"handleDragStart($event, item)\"\n                @dragover.prevent=\"handleDragOver($event, item)\"\n                @dragenter=\"handleDragEnter($event, item)\"\n                @dragend=\"handleDragEnd($event, item)\"\n              >\n                <img :src=\"item\">\n                <i v-if=\"!isDisabled\" class=\"el-icon-error btndel\" @click=\"handleRemove(index)\"/>\n              </div>\n              <div v-if=\"formValidate.sliderImages.length<10 && !isDisabled\" class=\"upLoadPicBox\" @click=\"modalPicTap('2')\">\n                <div class=\"upLoad\">\n                  <i class=\"el-icon-camera cameraIconfont\" />\n                </div>\n              </div>\n            </div>\n          </el-form-item>\n        </el-col> -->\n        <!-- <el-col :xs=\"18\" :sm=\"18\" :md=\"18\" :lg=\"12\" :xl=\"12\">\n          <el-form-item label=\"运费模板：\" prop=\"tempId\">\n            <el-select v-model=\"formValidate.tempId\" placeholder=\"请选择\" class=\"mr20\" :disabled=\"isDisabled\" style=\"width:100%;\">\n                <el-option\n                  v-for=\"item in shippingList\"\n                  :key=\"item.id\"\n                  :label=\"item.name\"\n                  :value=\"item.id\"\n                />\n              </el-select>\n          </el-form-item>\n        </el-col>\n        <el-col :xs=\"6\" :sm=\"6\" :md=\"6\" :lg=\"12\" :xl=\"12\">\n          <el-button v-show=\"!isDisabled\" class=\"mr15\" @click=\"addTem\">运费模板</el-button>\n        </el-col> -->\n        <el-col :span=\"24\">\n          <!-- <el-form-item label=\"珠子规格：\" props=\"specType\">\n            <el-radio-group v-model=\"formValidate.specType\" @change=\"onChangeSpec(formValidate.specType)\" :disabled=\"isDisabled\">\n              <el-radio :label=\"false\" class=\"radio\">单规格</el-radio>\n              <el-radio :label=\"true\">多规格</el-radio>\n            </el-radio-group>\n          </el-form-item> -->\n          <el-form-item label=\"佣金设置：\" props=\"isSub\">\n            <el-radio-group v-model=\"formValidate.isSub\" @change=\"onChangetype(formValidate.isSub)\"\n              :disabled=\"isDisabled\">\n              <el-radio :label=\"true\" class=\"radio\">单独设置</el-radio>\n              <el-radio :label=\"false\">默认设置</el-radio>\n            </el-radio-group>\n          </el-form-item>\n        </el-col>\n        <!-- 多规格添加-->\n        <el-col v-if=\"formValidate.specType && !isDisabled\" :span=\"24\" class=\"noForm\">\n          <el-form-item label=\"选择规格：\" prop=\"\">\n            <div class=\"acea-row\">\n              <el-select v-model=\"formValidate.selectRule\">\n                <el-option v-for=\"item in ruleList\" :key=\"item.id\" :label=\"item.ruleName\" :value=\"item.id\" />\n              </el-select>\n              <el-button type=\"primary\" class=\"mr20\" @click=\"confirm\">确认</el-button>\n              <el-button class=\"mr15\" @click=\"addRule\">添加规格</el-button>\n            </div>\n          </el-form-item>\n          <el-form-item>\n            <div v-for=\"(item, index) in formValidate.attr\" :key=\"index\">\n              <div class=\"acea-row row-middle\"><span class=\"mr5\">{{ item.attrName }}</span><i\n                  class=\"el-icon-circle-close\" @click=\"handleRemoveAttr(index)\" /></div>\n              <div class=\"rulesBox\">\n                <el-tag v-for=\"(j, indexn) in item.attrValue\" :key=\"indexn\" closable size=\"medium\"\n                  :disable-transitions=\"false\" class=\"mb5 mr10\" @close=\"handleClose(item.attrValue, indexn)\">\n                  {{ j }}\n                </el-tag>\n                <el-input v-if=\"item.inputVisible\" ref=\"saveTagInput\" v-model=\"item.attrValue.attrsVal\"\n                  class=\"input-new-tag\" size=\"small\" @keyup.enter.native=\"createAttr(item.attrValue.attrsVal, index)\"\n                  @blur=\"createAttr(item.attrValue.attrsVal, index)\" />\n                <el-button v-else class=\"button-new-tag\" size=\"small\" @click=\"showInput(item)\">+ 添加</el-button>\n              </div>\n            </div>\n          </el-form-item>\n          <el-col v-if=\"isBtn\">\n            <el-col :xl=\"6\" :lg=\"9\" :md=\"9\" :sm=\"24\" :xs=\"24\">\n              <el-form-item label=\"规格：\">\n                <el-input v-model=\"formDynamic.attrsName\" placeholder=\"请输入规格\" />\n              </el-form-item>\n            </el-col>\n            <el-col :xl=\"6\" :lg=\"9\" :md=\"9\" :sm=\"24\" :xs=\"24\">\n              <el-form-item label=\"规格值：\">\n                <el-input v-model=\"formDynamic.attrsVal\" placeholder=\"请输入规格值\" />\n              </el-form-item>\n            </el-col>\n            <el-col :xl=\"12\" :lg=\"6\" :md=\"6\" :sm=\"24\" :xs=\"24\">\n              <el-form-item class=\"noLeft\">\n                <el-button type=\"primary\" class=\"mr15\" @click=\"createAttrName\">确定</el-button>\n                <el-button @click=\"offAttrName\">取消</el-button>\n              </el-form-item>\n            </el-col>\n          </el-col>\n          <el-form-item v-if=\"!isBtn\">\n            <el-button type=\"primary\" icon=\"md-add\" class=\"mr15\" @click=\"addBtn\">添加新规格</el-button>\n          </el-form-item>\n        </el-col>\n        <!-- 批量设置-->\n        <el-col v-if=\"formValidate.attr.length > 0 && formValidate.specType && !isDisabled\" :span=\"24\" class=\"noForm\">\n          <el-form-item label=\"批量设置：\">\n            <el-table :data=\"oneFormBatch\" border class=\"tabNumWidth\" size=\"mini\">\n              <el-table-column align=\"center\" label=\"图片\" min-width=\"80\">\n                <template slot-scope=\"scope\">\n                  <div class=\"upLoadPicBox\" @click=\"modalPicTap('1', 'pi')\">\n                    <div v-if=\"scope.row.image\" class=\"pictrue tabPic\"><img :src=\"scope.row.image\"></div>\n                    <div v-else class=\"upLoad tabPic\">\n                      <i class=\"el-icon-camera cameraIconfont\" />\n                    </div>\n                  </div>\n                </template>\n              </el-table-column>\n              <el-table-column v-for=\"(item, iii) in attrValue\" :key=\"iii\" :label=\"formThead[iii].title\" align=\"center\"\n                min-width=\"120\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row[iii]\" maxlength=\"9\" min=\"0.01\" class=\"priceBox\"\n                    @blur=\"keyupEvent(iii, scope.row[iii], scope.$index, 1)\" />\n                </template>\n              </el-table-column>\n              <template v-if=\"formValidate.isSub\">\n                <el-table-column align=\"center\" label=\"一级返佣(元)\" min-width=\"120\">\n                  <template slot-scope=\"scope\">\n                    <el-input v-model=\"scope.row.brokerage\" type=\"number\" :min=\"0\" :max=\"scope.row.price\"\n                      class=\"priceBox\" />\n                  </template>\n                </el-table-column>\n                <el-table-column align=\"center\" label=\"二级返佣(元)\" min-width=\"120\">\n                  <template slot-scope=\"scope\">\n                    <el-input v-model=\"scope.row.brokerageTwo\" type=\"number\" :min=\"0\" :max=\"scope.row.price\"\n                      class=\"priceBox\" />\n                  </template>\n                </el-table-column>\n              </template>\n              <el-table-column align=\"center\" label=\"操作\" min-width=\"80\">\n                <template>\n                  <el-button type=\"text\" class=\"submission\" @click=\"batchAdd\">批量添加</el-button>\n                </template>\n              </el-table-column>\n            </el-table>\n          </el-form-item>\n        </el-col>\n        <el-col :xl=\"24\" :lg=\"24\" :md=\"24\" :sm=\"24\" :xs=\"24\">\n          <!-- 单规格表格-->\n          <el-form-item v-if=\"formValidate.specType === false\">\n            <el-table :data=\"OneattrValue\" border class=\"tabNumWidth\" size=\"mini\">\n              <el-table-column align=\"center\" label=\"图片\" min-width=\"80\">\n                <template slot-scope=\"scope\">\n                  <div class=\"upLoadPicBox\" @click=\"modalPicTap('1', 'dan', 'pi')\">\n                    <div v-if=\"formValidate.image\" class=\"pictrue tabPic\"><img :src=\"scope.row.image\"></div>\n                    <div v-else class=\"upLoad tabPic\">\n                      <i class=\"el-icon-camera cameraIconfont\" />\n                    </div>\n                  </div>\n                </template>\n              </el-table-column>\n              <el-table-column v-for=\"(item, iii) in attrValue\" :key=\"iii\" :label=\"formThead[iii].title\" align=\"center\"\n                min-width=\"120\">\n                <template slot-scope=\"scope\">\n                  <el-input :disabled=\"isDisabled\" v-model=\"scope.row[iii]\" maxlength=\"9\" min=\"0.01\" class=\"priceBox\"\n                    @blur=\"keyupEvent(iii, scope.row[iii], scope.$index, 2)\" />\n                </template>\n              </el-table-column>\n              <template v-if=\"formValidate.isSub\">\n                <el-table-column align=\"center\" label=\"一级返佣(元)\" min-width=\"120\">\n                  <template slot-scope=\"scope\">\n                    <el-input :disabled=\"isDisabled\" v-model=\"scope.row.brokerage\" type=\"number\" :min=\"0\"\n                      class=\"priceBox\" />\n                  </template>\n                </el-table-column>\n                <el-table-column align=\"center\" label=\"二级返佣(元)\" min-width=\"120\">\n                  <template slot-scope=\"scope\">\n                    <el-input :disabled=\"isDisabled\" v-model=\"scope.row.brokerageTwo\" type=\"number\" :min=\"0\"\n                      class=\"priceBox\" />\n                  </template>\n                </el-table-column>\n              </template>\n            </el-table>\n          </el-form-item>\n          <!-- <div>manyTabDate:{{manyTabDate}}</div> -->\n          <el-form-item label=\"全部sku：\" v-if=\"$route.params.id && showAll\">\n            <el-button type=\"default\" @click=\"showAllSku()\" :disabled=\"isDisabled\">展示</el-button>\n          </el-form-item>\n          <!-- 多规格表格-->\n          <el-form-item v-if=\"formValidate.attr.length > 0 && formValidate.specType\" label=\"珠子属性：\" class=\"labeltop\"\n            :class=\"isDisabled ? 'disLabel' : 'disLabelmoren'\">\n            <el-table :data=\"ManyAttrValue\" border class=\"tabNumWidth\" size=\"mini\">\n              <template v-if=\"manyTabDate\">\n                <el-table-column v-for=\"(item, iii) in manyTabDate\" :key=\"iii\" align=\"center\"\n                  :label=\"manyTabTit[iii].title\" min-width=\"80\">\n                  <template slot-scope=\"scope\">\n                    <span class=\"priceBox\" v-text=\"scope.row[iii]\" />\n                  </template>\n                </el-table-column>\n              </template>\n              <el-table-column align=\"center\" label=\"图片\" min-width=\"80\">\n                <template slot-scope=\"scope\">\n                  <div class=\"upLoadPicBox\" @click=\"modalPicTap('1', 'duo', scope.$index)\">\n                    <div v-if=\"scope.row.image\" class=\"pictrue tabPic\"><img :src=\"scope.row.image\"></div>\n                    <div v-else class=\"upLoad tabPic\">\n                      <i class=\"el-icon-camera cameraIconfont\" />\n                    </div>\n                  </div>\n                </template>\n              </el-table-column>\n              <el-table-column v-for=\"(item, iii) in attrValue\" :key=\"iii\" :label=\"formThead[iii].title\" align=\"center\"\n                min-width=\"120\">\n                <template slot-scope=\"scope\">\n                  <!--                    <span>scope.row:{{scope.row}}</span>-->\n                  <el-input :disabled=\"isDisabled\" maxlength=\"9\" min=\"0.01\" v-model=\"scope.row[iii]\" class=\"priceBox\"\n                    @blur=\"keyupEvent(iii, scope.row[iii], scope.$index, 3)\" />\n                </template>\n              </el-table-column>\n              <el-table-column align=\"center\" label=\"一级返佣(元)\" min-width=\"120\" v-if=\"formValidate.isSub\">\n                <template slot-scope=\"scope\">\n                  <el-input :disabled=\"isDisabled\" v-model=\"scope.row.brokerage\" type=\"number\" :min=\"0\"\n                    :max=\"scope.row.price\" class=\"priceBox\" />\n                </template>\n              </el-table-column>\n              <el-table-column align=\"center\" label=\"二级返佣(元)\" min-width=\"120\" v-if=\"formValidate.isSub\">\n                <template slot-scope=\"scope\">\n                  <el-input :disabled=\"isDisabled\" v-model=\"scope.row.brokerageTwo\" type=\"number\" :min=\"0\"\n                    :max=\"scope.row.price\" class=\"priceBox\" />\n                </template>\n              </el-table-column>\n              <el-table-column v-if=\"!isDisabled\" key=\"3\" align=\"center\" label=\"操作\" min-width=\"80\">\n                <template slot-scope=\"scope\">\n                  <el-button type=\"text\" class=\"submission\" @click=\"delAttrTable(scope.$index)\">删除</el-button>\n                </template>\n              </el-table-column>\n            </el-table>\n          </el-form-item>\n        </el-col>\n        <el-col v-bind=\"grid\">\n          <el-form-item label=\"排序：\">\n            <el-input-number v-model=\"formValidate.sort\" :min=\"0\" placeholder=\"请输入排序\" :disabled=\"isDisabled\" />\n          </el-form-item>\n        </el-col>\n        <el-col v-bind=\"grid\">\n          <el-form-item label=\"虚拟销量：\">\n            <el-input-number v-model=\"formValidate.ficti\" :min=\"0\" placeholder=\"请输入虚拟销量\" :disabled=\"isDisabled\" />\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"24\">\n          <el-form-item label=\"珠子详情：\">\n            <Tinymce v-show=\"!isDisabled\" v-model=\"formValidate.content\"></Tinymce>\n            <span v-show=\"isDisabled\" v-html=\"formValidate.content || '无'\"></span>\n          </el-form-item>\n        </el-col>\n      </el-row>\n      <!-- 其他设置-->\n      <el-row v-show=\"currentTab === 2\">\n        <el-col :span=\"24\">\n          <el-col v-bind=\"grid\">\n            <el-form-item label=\"排序：\">\n              <el-input-number v-model=\"formValidate.sort\" :min=\"0\" placeholder=\"请输入排序\" :disabled=\"isDisabled\" />\n            </el-form-item>\n          </el-col>\n          <el-col v-bind=\"grid\">\n            <el-form-item label=\"虚拟销量：\">\n              <el-input-number v-model=\"formValidate.ficti\" :min=\"0\" placeholder=\"请输入排序\" :disabled=\"isDisabled\" />\n            </el-form-item>\n          </el-col>\n        </el-col>\n        <!-- <el-col v-bind=\"grid\">\n            <el-form-item label=\"积分：\">\n              <el-input-number v-model=\"formValidate.giveIntegral\" :min=\"0\" placeholder=\"请输入排序\" :disabled=\"isDisabled\" />\n            </el-form-item>\n          </el-col> -->\n        <!-- <el-col :span=\"24\">\n          <el-form-item label=\"珠子推荐：\">\n            <el-checkbox-group v-model=\"checkboxGroup\" size=\"small\" @change=\"onChangeGroup\" :disabled=\"isDisabled\">\n              <el-checkbox v-for=\"(item, index) in recommend\" :key=\"index\" :label=\"item.value\">{{ item.name }}</el-checkbox>\n            </el-checkbox-group>\n          </el-form-item>\n        </el-col> -->\n        <!-- <el-col :span=\"24\">\n          <el-form-item label=\"活动优先级：\">\n            <div class=\"color-list acea-row row-middle\">\n              <div\n                :disabled=\"isDisabled\"\n                class=\"color-item\" :class=\"activity[item]\"\n                v-for=\"item in formValidate.activity\"\n                :key=\"item\"\n                draggable=\"true\"\n                @dragstart=\"handleDragStart($event, item)\"\n                @dragover.prevent=\"handleDragOver($event, item)\"\n                @dragenter=\"handleDragEnterFont($event, item)\"\n                @dragend=\"handleDragEnd($event, item)\"\n              >{{item}}</div>\n              <div class=\"tip\">可拖动按钮调整活动的优先展示顺序</div>\n            </div>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"24\">\n          <el-form-item label=\"优惠券：\" class=\"proCoupon\">\n            <div class=\"acea-row\">\n              <el-tag\n                v-for=\"(tag, index) in formValidate.coupons\"\n                :key=\"index\"\n                class=\"mr10 mb10\"\n                :closable=\"!isDisabled\"\n                :disable-transitions=\"false\"\n                @close=\"handleCloseCoupon(tag)\"\n              >\n                {{ tag.name }}\n              </el-tag>\n              <el-button v-if=\"!isDisabled\" class=\"mr15\" @click=\"addCoupon\">选择优惠券</el-button>\n            </div>\n          </el-form-item>\n        </el-col> -->\n      </el-row>\n      <el-form-item>\n        <!-- <el-button v-show=\"currentTab > 0\" class=\"submission priamry_border\" @click=\"handleSubmitUp\">上一步</el-button> -->\n        <!-- <el-button v-show=\"currentTab < 2\" type=\"primary\" class=\"submission\"\n          @click=\"handleSubmitNest('formValidate')\">下一步</el-button> -->\n        <el-button v-show=\"!isDisabled\" type=\"primary\" class=\"submission\"\n          @click=\"handleSubmit('formValidate')\">提交</el-button>\n      </el-form-item>\n    </el-form>\n  </el-card>\n  <CreatTemplates ref=\"addTemplates\" @getList=\"getShippingList\" />\n</div>\n", null]}