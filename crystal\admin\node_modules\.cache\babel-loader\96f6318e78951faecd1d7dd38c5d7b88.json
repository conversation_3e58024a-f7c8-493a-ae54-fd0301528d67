{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\element-ui\\lib\\input-number.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\element-ui\\lib\\input-number.js", "mtime": 1753666302835}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}], "contextDependencies": [], "result": ["\"use strict\";\n\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nmodule.exports = /******/function (modules) {\n  // webpackBootstrap\n  /******/ // The module cache\n  /******/\n  var installedModules = {};\n  /******/\n  /******/ // The require function\n  /******/\n  function __webpack_require__(moduleId) {\n    /******/\n    /******/ // Check if module is in cache\n    /******/if (installedModules[moduleId]) {\n      /******/return installedModules[moduleId].exports;\n      /******/\n    }\n    /******/ // Create a new module (and put it into the cache)\n    /******/\n    var module = installedModules[moduleId] = {\n      /******/i: moduleId,\n      /******/l: false,\n      /******/exports: {}\n      /******/\n    };\n    /******/\n    /******/ // Execute the module function\n    /******/\n    modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n    /******/\n    /******/ // Flag the module as loaded\n    /******/\n    module.l = true;\n    /******/\n    /******/ // Return the exports of the module\n    /******/\n    return module.exports;\n    /******/\n  }\n  /******/\n  /******/\n  /******/ // expose the modules object (__webpack_modules__)\n  /******/\n  __webpack_require__.m = modules;\n  /******/\n  /******/ // expose the module cache\n  /******/\n  __webpack_require__.c = installedModules;\n  /******/\n  /******/ // define getter function for harmony exports\n  /******/\n  __webpack_require__.d = function (exports, name, getter) {\n    /******/if (!__webpack_require__.o(exports, name)) {\n      /******/Object.defineProperty(exports, name, {\n        enumerable: true,\n        get: getter\n      });\n      /******/\n    }\n    /******/\n  };\n  /******/\n  /******/ // define __esModule on exports\n  /******/\n  __webpack_require__.r = function (exports) {\n    /******/if (typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n      /******/Object.defineProperty(exports, Symbol.toStringTag, {\n        value: 'Module'\n      });\n      /******/\n    }\n    /******/\n    Object.defineProperty(exports, '__esModule', {\n      value: true\n    });\n    /******/\n  };\n  /******/\n  /******/ // create a fake namespace object\n  /******/ // mode & 1: value is a module id, require it\n  /******/ // mode & 2: merge all properties of value into the ns\n  /******/ // mode & 4: return value when already ns object\n  /******/ // mode & 8|1: behave like require\n  /******/\n  __webpack_require__.t = function (value, mode) {\n    /******/if (mode & 1) value = __webpack_require__(value);\n    /******/\n    if (mode & 8) return value;\n    /******/\n    if (mode & 4 && _typeof(value) === 'object' && value && value.__esModule) return value;\n    /******/\n    var ns = Object.create(null);\n    /******/\n    __webpack_require__.r(ns);\n    /******/\n    Object.defineProperty(ns, 'default', {\n      enumerable: true,\n      value: value\n    });\n    /******/\n    if (mode & 2 && typeof value != 'string') for (var key in value) __webpack_require__.d(ns, key, function (key) {\n      return value[key];\n    }.bind(null, key));\n    /******/\n    return ns;\n    /******/\n  };\n  /******/\n  /******/ // getDefaultExport function for compatibility with non-harmony modules\n  /******/\n  __webpack_require__.n = function (module) {\n    /******/var getter = module && module.__esModule ? /******/function getDefault() {\n      return module['default'];\n    } : /******/function getModuleExports() {\n      return module;\n    };\n    /******/\n    __webpack_require__.d(getter, 'a', getter);\n    /******/\n    return getter;\n    /******/\n  };\n  /******/\n  /******/ // Object.prototype.hasOwnProperty.call\n  /******/\n  __webpack_require__.o = function (object, property) {\n    return Object.prototype.hasOwnProperty.call(object, property);\n  };\n  /******/\n  /******/ // __webpack_public_path__\n  /******/\n  __webpack_require__.p = \"/dist/\";\n  /******/\n  /******/\n  /******/ // Load entry module and return exports\n  /******/\n  return __webpack_require__(__webpack_require__.s = 114);\n  /******/\n}\n/************************************************************************/\n/******/({\n  /***/0: (/***/function _(module, __webpack_exports__, __webpack_require__) {\n    \"use strict\";\n\n    /* harmony export (binding) */\n    __webpack_require__.d(__webpack_exports__, \"a\", function () {\n      return normalizeComponent;\n    });\n    /* globals __VUE_SSR_CONTEXT__ */\n\n    // IMPORTANT: Do NOT use ES2015 features in this file (except for modules).\n    // This module is a runtime utility for cleaner component module output and will\n    // be included in the final webpack user bundle.\n\n    function normalizeComponent(scriptExports, render, staticRenderFns, functionalTemplate, injectStyles, scopeId, moduleIdentifier, /* server only */\n    shadowMode /* vue-cli only */) {\n      // Vue.extend constructor export interop\n      var options = typeof scriptExports === 'function' ? scriptExports.options : scriptExports;\n\n      // render functions\n      if (render) {\n        options.render = render;\n        options.staticRenderFns = staticRenderFns;\n        options._compiled = true;\n      }\n\n      // functional template\n      if (functionalTemplate) {\n        options.functional = true;\n      }\n\n      // scopedId\n      if (scopeId) {\n        options._scopeId = 'data-v-' + scopeId;\n      }\n      var hook;\n      if (moduleIdentifier) {\n        // server build\n        hook = function hook(context) {\n          // 2.3 injection\n          context = context ||\n          // cached call\n          this.$vnode && this.$vnode.ssrContext ||\n          // stateful\n          this.parent && this.parent.$vnode && this.parent.$vnode.ssrContext; // functional\n          // 2.2 with runInNewContext: true\n          if (!context && typeof __VUE_SSR_CONTEXT__ !== 'undefined') {\n            context = __VUE_SSR_CONTEXT__;\n          }\n          // inject component styles\n          if (injectStyles) {\n            injectStyles.call(this, context);\n          }\n          // register component module identifier for async chunk inferrence\n          if (context && context._registeredComponents) {\n            context._registeredComponents.add(moduleIdentifier);\n          }\n        };\n        // used by ssr in case component is cached and beforeCreate\n        // never gets called\n        options._ssrRegister = hook;\n      } else if (injectStyles) {\n        hook = shadowMode ? function () {\n          injectStyles.call(this, this.$root.$options.shadowRoot);\n        } : injectStyles;\n      }\n      if (hook) {\n        if (options.functional) {\n          // for template-only hot-reload because in that case the render fn doesn't\n          // go through the normalizer\n          options._injectStyles = hook;\n          // register for functioal component in vue file\n          var originalRender = options.render;\n          options.render = function renderWithStyleInjection(h, context) {\n            hook.call(context);\n            return originalRender(h, context);\n          };\n        } else {\n          // inject component registration as beforeCreate hook\n          var existing = options.beforeCreate;\n          options.beforeCreate = existing ? [].concat(existing, hook) : [hook];\n        }\n      }\n      return {\n        exports: scriptExports,\n        options: options\n      };\n    }\n\n    /***/\n  }),\n  /***/10: (/***/function _(module, exports) {\n    module.exports = require(\"element-ui/lib/input\");\n\n    /***/\n  }),\n  /***/114: (/***/function _(module, __webpack_exports__, __webpack_require__) {\n    \"use strict\";\n\n    __webpack_require__.r(__webpack_exports__);\n\n    // CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/vue-loader/lib??vue-loader-options!./packages/input-number/src/input-number.vue?vue&type=template&id=42f8cf66&\n    var render = function render() {\n      var _vm = this;\n      var _h = _vm.$createElement;\n      var _c = _vm._self._c || _h;\n      return _c(\"div\", {\n        class: [\"el-input-number\", _vm.inputNumberSize ? \"el-input-number--\" + _vm.inputNumberSize : \"\", {\n          \"is-disabled\": _vm.inputNumberDisabled\n        }, {\n          \"is-without-controls\": !_vm.controls\n        }, {\n          \"is-controls-right\": _vm.controlsAtRight\n        }],\n        on: {\n          dragstart: function dragstart($event) {\n            $event.preventDefault();\n          }\n        }\n      }, [_vm.controls ? _c(\"span\", {\n        directives: [{\n          name: \"repeat-click\",\n          rawName: \"v-repeat-click\",\n          value: _vm.decrease,\n          expression: \"decrease\"\n        }],\n        staticClass: \"el-input-number__decrease\",\n        class: {\n          \"is-disabled\": _vm.minDisabled\n        },\n        attrs: {\n          role: \"button\"\n        },\n        on: {\n          keydown: function keydown($event) {\n            if (!(\"button\" in $event) && _vm._k($event.keyCode, \"enter\", 13, $event.key, \"Enter\")) {\n              return null;\n            }\n            return _vm.decrease($event);\n          }\n        }\n      }, [_c(\"i\", {\n        class: \"el-icon-\" + (_vm.controlsAtRight ? \"arrow-down\" : \"minus\")\n      })]) : _vm._e(), _vm.controls ? _c(\"span\", {\n        directives: [{\n          name: \"repeat-click\",\n          rawName: \"v-repeat-click\",\n          value: _vm.increase,\n          expression: \"increase\"\n        }],\n        staticClass: \"el-input-number__increase\",\n        class: {\n          \"is-disabled\": _vm.maxDisabled\n        },\n        attrs: {\n          role: \"button\"\n        },\n        on: {\n          keydown: function keydown($event) {\n            if (!(\"button\" in $event) && _vm._k($event.keyCode, \"enter\", 13, $event.key, \"Enter\")) {\n              return null;\n            }\n            return _vm.increase($event);\n          }\n        }\n      }, [_c(\"i\", {\n        class: \"el-icon-\" + (_vm.controlsAtRight ? \"arrow-up\" : \"plus\")\n      })]) : _vm._e(), _c(\"el-input\", {\n        ref: \"input\",\n        attrs: {\n          value: _vm.displayValue,\n          placeholder: _vm.placeholder,\n          disabled: _vm.inputNumberDisabled,\n          size: _vm.inputNumberSize,\n          max: _vm.max,\n          min: _vm.min,\n          name: _vm.name,\n          label: _vm.label\n        },\n        on: {\n          blur: _vm.handleBlur,\n          focus: _vm.handleFocus,\n          input: _vm.handleInput,\n          change: _vm.handleInputChange\n        },\n        nativeOn: {\n          keydown: [function ($event) {\n            if (!(\"button\" in $event) && _vm._k($event.keyCode, \"up\", 38, $event.key, [\"Up\", \"ArrowUp\"])) {\n              return null;\n            }\n            $event.preventDefault();\n            return _vm.increase($event);\n          }, function ($event) {\n            if (!(\"button\" in $event) && _vm._k($event.keyCode, \"down\", 40, $event.key, [\"Down\", \"ArrowDown\"])) {\n              return null;\n            }\n            $event.preventDefault();\n            return _vm.decrease($event);\n          }]\n        }\n      })], 1);\n    };\n    var staticRenderFns = [];\n    render._withStripped = true;\n\n    // CONCATENATED MODULE: ./packages/input-number/src/input-number.vue?vue&type=template&id=42f8cf66&\n\n    // EXTERNAL MODULE: external \"element-ui/lib/input\"\n    var input_ = __webpack_require__(10);\n    var input_default = /*#__PURE__*/__webpack_require__.n(input_);\n\n    // EXTERNAL MODULE: external \"element-ui/lib/mixins/focus\"\n    var focus_ = __webpack_require__(22);\n    var focus_default = /*#__PURE__*/__webpack_require__.n(focus_);\n\n    // EXTERNAL MODULE: ./src/directives/repeat-click.js\n    var repeat_click = __webpack_require__(30);\n\n    // CONCATENATED MODULE: ./node_modules/babel-loader/lib!./node_modules/vue-loader/lib??vue-loader-options!./packages/input-number/src/input-number.vue?vue&type=script&lang=js&\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n\n    /* harmony default export */\n    var input_numbervue_type_script_lang_js_ = {\n      name: 'ElInputNumber',\n      mixins: [focus_default()('input')],\n      inject: {\n        elForm: {\n          default: ''\n        },\n        elFormItem: {\n          default: ''\n        }\n      },\n      directives: {\n        repeatClick: repeat_click[\"a\" /* default */]\n      },\n      components: {\n        ElInput: input_default.a\n      },\n      props: {\n        step: {\n          type: Number,\n          default: 1\n        },\n        stepStrictly: {\n          type: Boolean,\n          default: false\n        },\n        max: {\n          type: Number,\n          default: Infinity\n        },\n        min: {\n          type: Number,\n          default: -Infinity\n        },\n        value: {},\n        disabled: Boolean,\n        size: String,\n        controls: {\n          type: Boolean,\n          default: true\n        },\n        controlsPosition: {\n          type: String,\n          default: ''\n        },\n        name: String,\n        label: String,\n        placeholder: String,\n        precision: {\n          type: Number,\n          validator: function validator(val) {\n            return val >= 0 && val === parseInt(val, 10);\n          }\n        }\n      },\n      data: function data() {\n        return {\n          currentValue: 0,\n          userInput: null\n        };\n      },\n      watch: {\n        value: {\n          immediate: true,\n          handler: function handler(value) {\n            var newVal = value === undefined ? value : Number(value);\n            if (newVal !== undefined) {\n              if (isNaN(newVal)) {\n                return;\n              }\n              if (this.stepStrictly) {\n                var stepPrecision = this.getPrecision(this.step);\n                var precisionFactor = Math.pow(10, stepPrecision);\n                newVal = Math.round(newVal / this.step) * precisionFactor * this.step / precisionFactor;\n              }\n              if (this.precision !== undefined) {\n                newVal = this.toPrecision(newVal, this.precision);\n              }\n            }\n            if (newVal >= this.max) newVal = this.max;\n            if (newVal <= this.min) newVal = this.min;\n            this.currentValue = newVal;\n            this.userInput = null;\n            this.$emit('input', newVal);\n          }\n        }\n      },\n      computed: {\n        minDisabled: function minDisabled() {\n          return this._decrease(this.value, this.step) < this.min;\n        },\n        maxDisabled: function maxDisabled() {\n          return this._increase(this.value, this.step) > this.max;\n        },\n        numPrecision: function numPrecision() {\n          var value = this.value,\n            step = this.step,\n            getPrecision = this.getPrecision,\n            precision = this.precision;\n          var stepPrecision = getPrecision(step);\n          if (precision !== undefined) {\n            if (stepPrecision > precision) {\n              console.warn('[Element Warn][InputNumber]precision should not be less than the decimal places of step');\n            }\n            return precision;\n          } else {\n            return Math.max(getPrecision(value), stepPrecision);\n          }\n        },\n        controlsAtRight: function controlsAtRight() {\n          return this.controls && this.controlsPosition === 'right';\n        },\n        _elFormItemSize: function _elFormItemSize() {\n          return (this.elFormItem || {}).elFormItemSize;\n        },\n        inputNumberSize: function inputNumberSize() {\n          return this.size || this._elFormItemSize || (this.$ELEMENT || {}).size;\n        },\n        inputNumberDisabled: function inputNumberDisabled() {\n          return this.disabled || (this.elForm || {}).disabled;\n        },\n        displayValue: function displayValue() {\n          if (this.userInput !== null) {\n            return this.userInput;\n          }\n          var currentValue = this.currentValue;\n          if (typeof currentValue === 'number') {\n            if (this.stepStrictly) {\n              var stepPrecision = this.getPrecision(this.step);\n              var precisionFactor = Math.pow(10, stepPrecision);\n              currentValue = Math.round(currentValue / this.step) * precisionFactor * this.step / precisionFactor;\n            }\n            if (this.precision !== undefined) {\n              currentValue = currentValue.toFixed(this.precision);\n            }\n          }\n          return currentValue;\n        }\n      },\n      methods: {\n        toPrecision: function toPrecision(num, precision) {\n          if (precision === undefined) precision = this.numPrecision;\n          return parseFloat(Math.round(num * Math.pow(10, precision)) / Math.pow(10, precision));\n        },\n        getPrecision: function getPrecision(value) {\n          if (value === undefined) return 0;\n          var valueString = value.toString();\n          var dotPosition = valueString.indexOf('.');\n          var precision = 0;\n          if (dotPosition !== -1) {\n            precision = valueString.length - dotPosition - 1;\n          }\n          return precision;\n        },\n        _increase: function _increase(val, step) {\n          if (typeof val !== 'number' && val !== undefined) return this.currentValue;\n          var precisionFactor = Math.pow(10, this.numPrecision);\n          // Solve the accuracy problem of JS decimal calculation by converting the value to integer.\n          return this.toPrecision((precisionFactor * val + precisionFactor * step) / precisionFactor);\n        },\n        _decrease: function _decrease(val, step) {\n          if (typeof val !== 'number' && val !== undefined) return this.currentValue;\n          var precisionFactor = Math.pow(10, this.numPrecision);\n          return this.toPrecision((precisionFactor * val - precisionFactor * step) / precisionFactor);\n        },\n        increase: function increase() {\n          if (this.inputNumberDisabled || this.maxDisabled) return;\n          var value = this.value || 0;\n          var newVal = this._increase(value, this.step);\n          this.setCurrentValue(newVal);\n        },\n        decrease: function decrease() {\n          if (this.inputNumberDisabled || this.minDisabled) return;\n          var value = this.value || 0;\n          var newVal = this._decrease(value, this.step);\n          this.setCurrentValue(newVal);\n        },\n        handleBlur: function handleBlur(event) {\n          this.$emit('blur', event);\n        },\n        handleFocus: function handleFocus(event) {\n          this.$emit('focus', event);\n        },\n        setCurrentValue: function setCurrentValue(newVal) {\n          var oldVal = this.currentValue;\n          if (typeof newVal === 'number' && this.precision !== undefined) {\n            newVal = this.toPrecision(newVal, this.precision);\n          }\n          if (newVal >= this.max) newVal = this.max;\n          if (newVal <= this.min) newVal = this.min;\n          if (oldVal === newVal) return;\n          this.userInput = null;\n          this.$emit('input', newVal);\n          this.$emit('change', newVal, oldVal);\n          this.currentValue = newVal;\n        },\n        handleInput: function handleInput(value) {\n          this.userInput = value;\n        },\n        handleInputChange: function handleInputChange(value) {\n          var newVal = value === '' ? undefined : Number(value);\n          if (!isNaN(newVal) || value === '') {\n            this.setCurrentValue(newVal);\n          }\n          this.userInput = null;\n        },\n        select: function select() {\n          this.$refs.input.select();\n        }\n      },\n      mounted: function mounted() {\n        var innerInput = this.$refs.input.$refs.input;\n        innerInput.setAttribute('role', 'spinbutton');\n        innerInput.setAttribute('aria-valuemax', this.max);\n        innerInput.setAttribute('aria-valuemin', this.min);\n        innerInput.setAttribute('aria-valuenow', this.currentValue);\n        innerInput.setAttribute('aria-disabled', this.inputNumberDisabled);\n      },\n      updated: function updated() {\n        if (!this.$refs || !this.$refs.input) return;\n        var innerInput = this.$refs.input.$refs.input;\n        innerInput.setAttribute('aria-valuenow', this.currentValue);\n      }\n    };\n    // CONCATENATED MODULE: ./packages/input-number/src/input-number.vue?vue&type=script&lang=js&\n    /* harmony default export */\n    var src_input_numbervue_type_script_lang_js_ = input_numbervue_type_script_lang_js_;\n    // EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js\n    var componentNormalizer = __webpack_require__(0);\n\n    // CONCATENATED MODULE: ./packages/input-number/src/input-number.vue\n\n    /* normalize component */\n\n    var component = Object(componentNormalizer[\"a\" /* default */])(src_input_numbervue_type_script_lang_js_, render, staticRenderFns, false, null, null, null);\n\n    /* hot reload */\n    if (false) {\n      var api;\n    }\n    component.options.__file = \"packages/input-number/src/input-number.vue\";\n    /* harmony default export */\n    var input_number = component.exports;\n    // CONCATENATED MODULE: ./packages/input-number/index.js\n\n    /* istanbul ignore next */\n    input_number.install = function (Vue) {\n      Vue.component(input_number.name, input_number);\n    };\n\n    /* harmony default export */\n    var packages_input_number = __webpack_exports__[\"default\"] = input_number;\n\n    /***/\n  }),\n  /***/2: (/***/function _(module, exports) {\n    module.exports = require(\"element-ui/lib/utils/dom\");\n\n    /***/\n  }),\n  /***/22: (/***/function _(module, exports) {\n    module.exports = require(\"element-ui/lib/mixins/focus\");\n\n    /***/\n  }),\n  /***/30: (/***/function _(module, __webpack_exports__, __webpack_require__) {\n    \"use strict\";\n\n    /* harmony import */\n    var element_ui_src_utils_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(2);\n    /* harmony import */\n    var element_ui_src_utils_dom__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(element_ui_src_utils_dom__WEBPACK_IMPORTED_MODULE_0__);\n\n    /* harmony default export */\n    __webpack_exports__[\"a\"] = {\n      bind: function bind(el, binding, vnode) {\n        var interval = null;\n        var startTime = void 0;\n        var handler = function handler() {\n          return vnode.context[binding.expression].apply();\n        };\n        var clear = function clear() {\n          if (Date.now() - startTime < 100) {\n            handler();\n          }\n          clearInterval(interval);\n          interval = null;\n        };\n        Object(element_ui_src_utils_dom__WEBPACK_IMPORTED_MODULE_0__[\"on\"])(el, 'mousedown', function (e) {\n          if (e.button !== 0) return;\n          startTime = Date.now();\n          Object(element_ui_src_utils_dom__WEBPACK_IMPORTED_MODULE_0__[\"once\"])(document, 'mouseup', clear);\n          clearInterval(interval);\n          interval = setInterval(handler, 100);\n        });\n      }\n    };\n\n    /***/\n  })\n\n  /******/\n});", null]}