@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.default.data-v-7d45776e {
  width: 690rpx;
  height: 300rpx;
  border-radius: 14rpx;
  margin: 26rpx auto 0 auto;
  background-color: #ccc;
  text-align: center;
  line-height: 300rpx;
}
.default .iconfont.data-v-7d45776e {
  font-size: 80rpx;
}
.seckill.data-v-7d45776e {
  width: auto;
  height: 420rpx;
  background-image: url("data:image/png;base64,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");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  border-radius: 14rpx;
  margin: 0 auto 30rpx auto;
  padding: 24rpx;
  box-sizing: border-box;
}
.seckill .title .pictrue.data-v-7d45776e {
  width: 148rpx;
  height: 40rpx;
}
.seckill .title .pictrue image.data-v-7d45776e {
  width: 100%;
  height: 100%;
}
.seckill .title .lines.data-v-7d45776e {
  width: 1rpx;
  height: 24rpx;
  background-color: #fff;
  opacity: 0.6;
  margin-left: 16rpx;
}
.seckill .title .point.data-v-7d45776e {
  font-size: 30rpx;
  color: #fff;
  margin-left: 21rpx;
  margin-right: 4rpx;
  font-weight: 800;
}
.seckill .title .styleAll.data-v-7d45776e {
  width: 35rpx;
  height: 35rpx;
  background-color: #2F2F2F;
  border-radius: 6rpx;
  color: #fff;
  text-align: center;
}
.seckill .title .more.data-v-7d45776e {
  width: 86rpx;
  height: 40rpx;
  background: linear-gradient(142deg, #FFE9CE 0%, #FFD6A7 100%);
  opacity: 1;
  border-radius: 18px;
  font-size: 22rpx;
  color: #FE960F;
  padding-left: 8rpx;
  font-weight: 800;
}
.seckill .title .more .iconfont.data-v-7d45776e {
  font-size: 21rpx;
}
.seckill .conter.data-v-7d45776e {
  width: 666rpx;
  height: 320rpx;
  border-radius: 12px;
  margin-top: 24rpx;
}
.seckill .conter .itemCon.data-v-7d45776e {
  display: inline-block;
  width: 186rpx;
  margin-right: 24rpx;
}
.seckill .conter .itemCon .item.data-v-7d45776e {
  width: 100%;
}
.seckill .conter .itemCon .item .pictrue.data-v-7d45776e {
  width: 100%;
  height: 186rpx;
  border-radius: 6rpx;
}
.seckill .conter .itemCon .item .pictrue image.data-v-7d45776e {
  width: 100%;
  height: 100%;
  border-radius: 6rpx;
}
.seckill .conter .itemCon .item .name.data-v-7d45776e {
  font-size: 24rpx;
  color: #000;
  margin-top: 14rpx;
}
.seckill .conter .itemCon .item .y_money.data-v-7d45776e {
  font-size: 24rpx;
  color: #999999;
  text-decoration: line-through;
}
.seckill .conter .itemCon .item .x_money.data-v-7d45776e {
  color: #FD502F;
  font-size: 28rpx;
  height: 100%;
  font-weight: bold;
  margin: 2rpx 0;
}
.seckill .conter .itemCon .item .x_money .num.data-v-7d45776e {
  font-size: 28rpx;
}
.seckill .conter .itemCon .item .money.data-v-7d45776e {
  margin-top: 14rpx;
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.mengceng.data-v-a1cf24e2 {
  width: 38rpx;
  height: 38rpx;
  line-height: 36rpx;
  background: rgba(51, 51, 51, 0.6);
  text-align: center;
  border-radius: 50%;
  opacity: 1;
  position: absolute;
  left: 0px;
  top: 2rpx;
  color: #FFF;
}
.mengceng ._i.data-v-a1cf24e2 {
  font-style: normal;
  font-size: 20rpx;
}
.activity_pic.data-v-a1cf24e2 {
  margin-left: 28rpx;
  padding-left: 20rpx;
}
.activity_pic .picture.data-v-a1cf24e2 {
  display: inline-block;
}
.activity_pic .avatar.data-v-a1cf24e2 {
  width: 38rpx;
  height: 38rpx;
  display: inline-table;
  vertical-align: middle;
  -webkit-user-select: none;
  user-select: none;
  border-radius: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: 0 0;
  margin-right: -10rpx;
  box-shadow: 0 0 0 1px #fff;
}
.activity_pic .pic_count.data-v-a1cf24e2 {
  margin-left: 30rpx;
  color: #c9ab79;
  font-size: 22rpx;
  font-weight: 500;
}
.default.data-v-a1cf24e2 {
  width: 690rpx;
  height: 300rpx;
  border-radius: 14rpx;
  margin: 26rpx auto 0 auto;
  background-color: #ccc;
  text-align: center;
  line-height: 300rpx;
}
.default .iconfont.data-v-a1cf24e2 {
  font-size: 80rpx;
}
.combination.data-v-a1cf24e2 {
  width: auto;
  background-color: #fff;
  border-radius: 14rpx;
  margin: 0 auto 30rpx auto;
  padding: 16rpx 24rpx 24rpx 24rpx;
  background-image: url(data:image/png;base64,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);
  background-repeat: no-repeat;
  background-size: 100%;
}
.combination .title.data-v-a1cf24e2 {
  width: 80%;
  margin-left: 128rpx;
}
.combination .title .sign.data-v-a1cf24e2 {
  width: 40rpx;
  height: 40rpx;
}
.combination .title .sign image.data-v-a1cf24e2 {
  width: 100%;
  height: 100%;
}
.combination .title .name.data-v-a1cf24e2 {
  font-size: 32rpx;
  color: #282828;
  margin-left: 12rpx;
  font-weight: bold;
}
.combination .title .name text.data-v-a1cf24e2 {
  color: #797979;
  font-size: 24rpx;
  font-weight: 400;
  margin-left: 14rpx;
}
.combination .title .more.data-v-a1cf24e2 {
  width: 86rpx;
  height: 40rpx;
  background: linear-gradient(142deg, #FFE9CE 0%, #FFD6A7 100%);
  opacity: 1;
  border-radius: 18px;
  font-size: 22rpx;
  color: #FE960F;
  padding-left: 8rpx;
  font-weight: 800;
}
.combination .title .more .iconfont.data-v-a1cf24e2 {
  font-size: 21rpx;
}
.combination .conter.data-v-a1cf24e2 {
  margin-top: 24rpx;
}
.combination .conter .itemCon.data-v-a1cf24e2 {
  display: inline-block;
  width: 220rpx;
  margin-right: 24rpx;
}
.combination .conter .item.data-v-a1cf24e2 {
  width: 100%;
}
.combination .conter .item .pictrue.data-v-a1cf24e2 {
  width: 100%;
  height: 220rpx;
  border-radius: 6rpx;
}
.combination .conter .item .pictrue image.data-v-a1cf24e2 {
  width: 100%;
  height: 100%;
  border-radius: 6rpx;
}
.combination .conter .item .text.data-v-a1cf24e2 {
  margin-top: 4rpx;
}
.combination .conter .item .text .y_money.data-v-a1cf24e2 {
  font-size: 24rpx;
  color: #999999;
  text-decoration: line-through;
}
.combination .conter .item .text .name.data-v-a1cf24e2 {
  font-size: 24rpx;
  color: #000;
  margin-top: 14rpx;
}
.combination .conter .item .text .money.data-v-a1cf24e2 {
  color: #FD502F;
  font-size: 28rpx;
  height: 100%;
  font-weight: bold;
  margin: 10rpx 0 0rpx 0;
}
.combination .conter .item .text .money .num.data-v-a1cf24e2 {
  font-size: 28rpx;
}


page {
	display: flex;
	flex-direction: column;
	height: 100%;
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.notice {
  width: 100%;
  height: 70rpx;
  border-radius: 10rpx;
  background-color: #fff;
  margin-bottom: 25rpx;
  line-height: 70rpx;
  padding: 0 14rpx;
}
.notice .line {
  color: #CCCCCC;
}
.notice .pic {
  width: 130rpx;
  height: 36rpx;
}
.notice .pic image {
  width: 100%;
  height: 100%;
  display: block !important;
}
.notice .swipers {
  height: 100%;
  width: 444rpx;
  overflow: hidden;
}
.notice .swipers swiper {
  height: 100%;
  width: 100%;
  overflow: hidden;
  font-size: 22rpx;
  color: #333333;
}
.notice .iconfont {
  color: #999999;
  font-size: 20rpx;
}
.couponIndex {
  width: auto;
  height: 238rpx;
  background-image: url(data:image/png;base64,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);
  background-size: 100% 100%;
  padding-left: 42rpx;
  margin-bottom: 30rpx;
}
.couponIndex .titBox {
  padding: 47rpx 0;
  text-align: center;
  height: 100%;
}
.couponIndex .titBox .tit1 {
  color: #FFEBD2;
  font-size: 34rpx;
  font-weight: 600;
}
.couponIndex .titBox .tit2 {
  color: #FFEBD2;
  font-size: 22rpx;
  margin: 10rpx 0 26rpx 0;
}
.couponIndex .titBox .tit3 {
  color: #FFDAAF;
  font-size: 24rpx;
}
.couponIndex .titBox .tit3 .iconfont {
  font-size: 20rpx;
}
.couponIndex .listBox {
  padding: 14rpx 0;
}
.couponIndex .listBox .listActive {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAKoAAADSCAMAAADt2ascAAABOFBMVEUAAAD9BAL/7NX/7db7BAD7BADMJQD/7NXLJQD/7NX/7dbMJADMJQD6k4f/7tj/69P/7NfNJAD/7df/7NT6CQTNJAD/69P/7df4CQLNJADNJQDNJAD/79f/69H/5sj/2q7/6M3/2Kv/27H/16j/1aX/5cX/48L/4Lz/FBT/Dw//7NT/BAT/69H/4sD/37n/3LT/3rf/JSX/GBj/7db/1KL/6s//RET/Ly//CAj/4b7/58v/3bX/Pj7/Ojr/KSn/HBz/QUH/ODj/NTX/MjL/LCz/IiL/Hx//DAz/AQH/woD/SUn/RkbSIQDYHQDVIAD/TEzOJAD/T0/QIwDWHwDMJQDaHAD/jXTbHAD/aV3/sIr/xon/UVH/y5vaHQD/qIf/WVT/cWP/ypD/hXH/eWf/wZf/lnn/tY//YFZEyPE/AAAAHnRSTlMA9PZZwGr018XCZ2UiDEbq5eTd3dKsnI2McEw4L/CovDjMAAAD70lEQVR42uzTy2rjMBSAYeFCbk3TG23pZRInODYIxwlkvHDyBILJMEy1Etn4/V+icHRiB2F3F3xUzq+FbLT5OLaE2+Rl+BBsOy54GL5MxPeN7rZkuhuJ9q4et6TqX7VKO//wbsGkRbolWONcPwJJsOCjgdqXZxVRaVQnmTIqziX9hrsv6/bGRMU0lx2UT4vImI2sG7nSm9vqLDyWiey0zBzD6uX2xr1T1VFiZrLz5qb+C9yb9SyxzKwkgVa19dmhDqVtamIJpV2tyhri09ChPqa2cp0SaXHEh75DDVJoXaZkKjO7/3KoeGxWKZmK09gaqQmhoaapWcLWTI2ilFC/57C51Bwqk5xQ2dHujVSzzAkVl99QVZgTaqdga6HmpGqmhpAKSYWcNmpOZlXUHzBVSjEVY6qn1B2kdqRCjrfUN8rUN1E3HmjKVD0YC+y6p/UUUlNSIUfr3jVKtaZO1Rqs454P1N5YCDHQPlD1QIh37QdVv4tXX6iv4skX6pO494V6L7RtCaklqZCDRPHPRpmKRKYy1aHGRJZHU43bqDGkYlIhh6lMBep/G2UqEj2kriC1IhVymMpUT6kFpApSIedE/WOjTEUiUy9JTSCVkAo5TGUqUy9NzSCVkQo5TGWqp9QNpDakQg5TL0n9a6NMRaKH1D2k9qRCDlOZek5dQ2pNKuQwlameUheQWpAKOSfqp40yFYlMZSpTL0adQWpGKuScqAcbZSoSmcpUpjL1cJhDak4q5DD1qx1652kciMIw/FmySzoKelLsylWiaJeCy164UyAKBo8wM5adwP//BzjOkRGIiAAZMp84z+lGI51XR1M1VVP71O3O2XZUJIcw9W4u5lRJxPVczKmSqKma2hl0zgZRkRxJxM1czKmSyJR6KypXRplaukoKcdXztYsu1dW+75PUp9pBPFwjnX3qC42bDNZv4hrpWZAqitqO18nVxStVuFygMuvJtaZaUCSpC3JrV46/TulqyXxnqvCNseF7S2sa/0YJzpfhK+Pux2HcO1P4ZSJwvjxfmKktV3jJaR+56lRRVMZYW34i0RpTFe/ei4sP87PmqbV2skTfpP03nRX6D+/DxWo8FDONea7pXh9WswK/aGhq61un/qGhqSFgl4amhoA9GtjbZRmqq9LAXxqaGgL+0dDUEPCfhqaGgN80NLX1rVMPaWhqCNinoakhYEgD+0OWYbrqkAZT6hENTQ0BxzQ0NQQc0NDUEDCigYMRyzBddUSDKfUHDU1taSoF/KShqS1NpYATGpoaAnZoaGoISHZIJNjMSWxiKyexhY0kp5BsAFlOIUMrzQmk6KSn0UshsuQ0akmGJ1kabW2SSugjX6So+4quoXsAAAAASUVORK5CYII=);
  background-size: 100% 100%;
}
.couponIndex .listBox .listHui {
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAKoAAADSCAMAAADt2ascAAABa1BMVEUAAAD9BALu7u77BAD7BADu7u7w8PDv7+/u7u7MJQDLJQDu7u7MJADu7u7NJAD6CQTu7u7s7OzNJAD4CQLNJADNJQDNJADMJADLJgD29vbzAADs7Ozw8PDv7+/v7+/u7u7e3t7X19fh4eHU1NTl5eXr6+vj4+Pb29vp6enZ2dno6Ojn5+ft7e3/MDD/GRn/EhL/Dw//BQXS0tLu7u7v7+//CQn/OTn/MzP/LS3/KCj/JSX/Hx//HBz/Fhb/DAz/AwP/RUX/Q0P/QUH/Pz//PT3/Ozv/Nzf/NTX/Kir/IyP/ISH/////AAD/SEjXHgD/S0vOzs7MzMzVIADOJADKysrRIgDTIQDQ0NDQIwDZHQDMJQD/TU3/T0/aHADJycnpjY37+/vbHADIyMj09PTesLD0aWn/UVHaHQD7WVn39/fhqanyc3PVycn0bGzYwcHko6PsiIjweXnmlpbiiorugYHXzMzbuLj2YGD/kZGODXpLAAAAIHRSTlMA9PbAamVV4df0xZRl6uTSw8CsjHBMOCMiDQrwQ0ExLhEI/skAAAfqSURBVHja7NPLisMgAIVhCbS5TUtbemVum1pK6y6+hcvZDK6ErPL+LzDVmKlInN2QI/gL2dTFx5ESv7d6lWd84rJ8Vb+Tv5vlHKZ8RsLNCw5VMQ9KJ394v2wekLIGLjZq/cwawLKPEWrROLWdUmKSlOpaV1KM/PddqL7PeTNBnOuVXOzMl54Wz9v66mTZqZ47LU7+qGyoVS2bvM5B+LPWTylnADmD1R51zfq4gJA+IL+TrT1qzvoQXn/YlfUVHjWjplZRmBSnpleP6vzMQA4fZhujMg40KmXDrKOrdh3OqIx2bYBq3x+ottPfEJVRoBqlvwGqoFAZTqLq/p16NYkrVJYTolKgY6lp1bRqoiZqhNTdzSRuUFnOzoEeK4lMldWR2F5KiU2V5YuVSolOldJYD2UM1PLwoFZSdzeJO1SWY4AVIXsZB1XuyTYW6pZsYqFuyFKaziZxhspypGlJpEe9A52B2ke++pBXtcSBiryqJca8KlKJ+ihRferFJC5QWU6IegY6lhrhqt99yKv+sEt3uW4CMRSAt+JTS7X86m2MVHkqWqv730jtAar0hxtSkbmp1DOBQJghX07YiDv1lVv9T/1P/UepH0bah5fKxjmifnyh8Qv1y5r3alVUsUaVDlrdiDt1fqvJBNvPYQj9INyjzgoJVpuqiFAOUV65e7uvQRUepqqQMsklKqAoVu2rUKmgkDwoGTNDkfuqmLLuUW5i359KVVyaBFuve1IJrp9AWlp9d6paCsvL8ufrQzuqpXelUhIGFHQ8R+tnaFrvUOmZQ8yYdujBpLokiSVYM7qlfl2zU58aWCAZO/QodUUNJM1M8ngj3lKf3KqYWVW6Q49brY1g0thCf6PSSKPnRSJAYji/wBqhFh1Qn1Yq1ZfClOj8ksakEZjdqpWUB/R0GkwkYmqrt9JHWiUYIea2asEE26Cn02hYp1I5jNjo0RSHmWZSNYLwqHTnGO/Uz2ueSo1QDfpLKtlGnEFFGIX+NVXmUdVdHn9QBbZzjqhy+QiHxqOLNNxxh3p54EEGeihq7uE2u9VwQRCd79OYEwoJn9wq3Ci0pKcixXR3IeIDqow0uTzmQMjZwDMK8xBR3zhHVLp2qLsYTt4W4W7mJhLOIt5UKpNahZu4nIu5O0THrqzWIDKv1XDAztw1E0nVUoYU2H6lflqzUy8O3CVwZmKwqJobe4bHyt5MKhvxyVRzg8vdaLhHvZeztDo+az6RGg6zu1AeRVbyCLVqrLGWbsyiuqtB3g57RgWDV2aE91qE5pngKVR1F8fd/96Slm+Dl2e9gMrCLWB5/gtVR5peG+4B1+MgtyRq9MqY6XnAqD2scd2DD6hy6bDO7PrGZefisicvkSVDGlWt4N6glSmtRoeZHsZ7JhC1Y3Dv65pQHtX2jTOlVe9sdtxq76PQrBCWQO9WtXove1qj6bxWvSOOW8Wo0XrhcmMsC8YHfTH1tE+mOutR0B0JVC2jddfITVF/fpXLU6n9z9TtAebunC4oyli11snYjUym6h+owzfeo5T1yiItz6w+xi5991bZe8bK5DEezeHsGK8o7dtUjDRcm76wM27DvizdbFmwxnqeL5Yzoy91kseVW85O/bbmxzW9ciQ17PaekZYAUuaoJNzZksfOObno/GP+Tt2I39mrl922YSAKw3oVYdbdzKLduvdFYZQEBuCiLYUSUitIvsa59PE7tExCUWKBdqgYBPxrEcSJ7Q/HhDykxk0w1cp6EzLRRuziwMoslXNL+wbUaVcVhOipwFCWdSmSXqQI/SP9p7/qqooEOtL+kDq1IiJw55lIQA7uAd/rUpGXIk/1+2Kn81RO5Dz/JalAlLspgW2wh/qDoLofgIKp3OWoHBGI3mGV7m71ZGi4OJUxKHu3WeXuVv58uAKof7smorIsJ+z9RlpC/3xAAPVAzH51+b9B3IsIhbAqvuysGjukvSwdR97RUQ/EITV2kgSSP4+oO1vvC2GscSpEvrC7B7gEebfoTi2cvCrs+wbR01opCS4kLcAmtDYCYSzPOUaNvavQBKTc61oiACjSWjJ0/LlD6u8uT4VJZ82B2MhQ1nN50KoH4pCaTzSrWxWUtokOGrSqp/451K7vplk1Jy2Q0K0KwrjPPmjVu3V7EGY/ffVtMwWVbQYFgQ8RwmJqc1t7n6N6LcRPGgKScHLNP+d01EHb9QrihtpI1ApOabXeDmHZj2eqNiXETBmjhMbwOTfVMypPHbYsSoyWNAYlYUhlsTwiyr6PtNw0C+TgxRcZQtIjL2VbNJvliIap49VtUS7wxRkjrfVYi7Jo6zGGpwZ4m3vE8xdGZZ7u2nXfFEunHKfOw6uXxc1ZC3ur0I+WvGHkPDhPDa9qi6I8ncxWraSRTCyLoq3mw+JTfTWbeeayXAVAV/x/u92urer5uWWzOD1Utm3xuG1le5hFKZslU0rU98mUEvVDMqVE/ZhMKVE/JVNK1M/JlBL1bTJdqVOUfUmmlKhfkykl6rtkulKnKHuTTFfq/3bp3QZhIAiD8L+6yx04cxHABZZ4XwqI/uthA4sOVvLo9qtggnFDp54xMjWCXhik1AtGpkbQFSNTI+iNQUr9YJBSbxiZGkF3jEyNoANGprqhUx8YmRpBR4xMdUOnnjAy1Q2d+sTI1AhaMTI1gqwxrKapQUyaG8SsBXKALVLtCFWudIAiQVqLNtW+u2ZV+qtlr7Xdyhb6A+wXuyHAhwHOAAAAAElFTkSuQmCC);
  background-size: 100% 100%;
}
.couponIndex .listBox .list {
  width: 170rpx;
  height: 210rpx;
  padding: 16rpx 0;
  text-align: center;
  margin-left: 24rpx;
}
.couponIndex .listBox .list .tit {
  font-size: 18rpx;
  padding: 0 26rpx;
}
.couponIndex .listBox .list .titActive {
  color: #C99959;
}
.couponIndex .listBox .list .price {
  font-size: 46rpx;
  font-weight: 900;
  margin-top: 4rpx;
}
.couponIndex .listBox .list .pricehui {
  color: #B2B2B2;
}
.couponIndex .listBox .list .fonthui {
  background-color: #F5F5F5 !important;
}
.couponIndex .listBox .list .yuan {
  font-size: 24rpx;
}
.couponIndex .listBox .list .ling {
  font-size: 24rpx;
  margin-top: 9.5rpx;
  width: 102rpx;
  height: 36rpx;
  line-height: 36rpx;
  background-color: #FFE5C7;
  border-radius: 28rpx;
  margin: auto;
}
.couponIndex .listBox .list .priceM {
  color: #FFDAAF;
  font-size: 22rpx;
  margin-top: 14rpx;
}
.sticky-box {
  display: flex;
  position: -webkit-sticky;
  position: sticky;
  z-index: 99;
  flex-direction: row;
  margin: 0px;
  background: #f5f5f5;
  padding: 30rpx 0;
}
.listAll {
  width: 20%;
  text-indent: 62rpx;
  font-size: 30rpx;
  border-left: 1px #eee solid;
  margin: 1% 0;
  padding: 5rpx;
  position: relative;
}
.listAll image {
  position: absolute;
  left: 20rpx;
  top: 8rpx;
}
.tab {
  position: relative;
  display: flex;
  font-size: 28rpx;
  white-space: nowrap;
}
.tab__item {
  flex: 1;
  padding: 0 20rpx;
  text-align: center;
  height: 60rpx;
  line-height: 60rpx;
  color: #666;
}
.tab__item.active {
  color: #09C2C9;
}
.tab__line {
  display: block;
  height: 6rpx;
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 1;
  border-radius: 3rpx;
  position: relative;
  background: #2FC6CD;
}
.scroll-view_H {
  /* 文本不会换行，文本会在在同一行上继续，直到遇到 <br> 标签为止。 */
  white-space: nowrap;
  width: 100%;
}
.privacy-wrapper {
  z-index: 999;
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: #7F7F7F;
}
.privacy-wrapper .privacy-box {
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  width: 560rpx;
  padding: 50rpx 45rpx 0;
  background: #fff;
  border-radius: 20rpx;
}
.privacy-wrapper .privacy-box .title {
  text-align: center;
  font-size: 32rpx;
  text-align: center;
  color: #333;
  font-weight: 700;
}
.privacy-wrapper .privacy-box .content {
  margin-top: 20rpx;
  line-height: 1.5;
  font-size: 26rpx;
  color: #666;
  text-indent: 54rpx;
}
.privacy-wrapper .privacy-box .content ._i {
  font-style: normal;
  color: #c9ab79;
}
.privacy-wrapper .privacy-box .btn-box {
  margin-top: 40rpx;
  text-align: center;
  font-size: 30rpx;
}
.privacy-wrapper .privacy-box .btn-box .btn-item {
  height: 82rpx;
  line-height: 82rpx;
  background: linear-gradient(90deg, #F67A38 0%, #F11B09 100%);
  color: #fff;
  border-radius: 41rpx;
}
.privacy-wrapper .privacy-box .btn-box .btn {
  padding: 30rpx 0;
}
.page-index {
  display: flex;
  flex-direction: column;
  min-height: 100%;
  background: linear-gradient(180deg, #fff 0%, #f5f5f5 100%);
}
.page-index .header {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 200000;
  width: 100%;
  background-color: #c9ab79;
  padding: 28rpx 30rpx;
}
.page-index .header .serch-wrapper {
  margin-top: 25px;
  align-items: center;
  width: 75%;
}
.page-index .header .serch-wrapper .logo {
  width: 168rpx;
  height: 60rpx;
  margin-right: 24rpx;
}
.page-index .header .serch-wrapper image {
  width: 168rpx;
  height: 60rpx;
}
.page-index .header .serch-wrapper .input {
  display: flex;
  align-items: center;
  width: 546rpx;
  height: 58rpx;
  padding: 0 0 0 30rpx;
  background: #f7f7f7;
  border: 1px solid #f1f1f1;
  border-radius: 29rpx;
  color: #BBBBBB;
  font-size: 26rpx;
}
.page-index .header .serch-wrapper .input .iconfont {
  margin-right: 20rpx;
  font-size: 26rpx;
  color: #666666;
}
.page-index .header .tabNav {
  padding-top: 24rpx;
}
.page-index .mp-header {
  z-index: 999;
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  background-color: #c9ab79;
}
.page-index .mp-header .serch-wrapper {
  height: 100%;
  align-items: center;
  padding: 0 50rpx 0 53rpx;
}
.page-index .mp-header .serch-wrapper image {
  width: 118rpx;
  height: 42rpx;
  margin-right: 30rpx;
}
.page-index .mp-header .serch-wrapper .input {
  display: flex;
  align-items: center;
  width: 305rpx;
  height: 50rpx;
  padding: 0 0 0 30rpx;
  background: #f7f7f7;
  border: 1px solid #f1f1f1;
  border-radius: 29rpx;
  color: #BBBBBB;
  font-size: 28rpx;
}
.page-index .mp-header .serch-wrapper .input .iconfont {
  margin-right: 20rpx;
}
.page-index .page_content {
  background-color: #f5f5f5;
  padding: 0 30rpx;
}
.page-index .page_content .swiper {
  position: relative;
  width: 100%;
  height: 280rpx;
  margin: 0 auto;
  border-radius: 10rpx;
  overflow: hidden;
  margin-bottom: 25rpx;
  z-index: 10;
  margin-top: 20rpx;
}
.page-index .page_content .swiper swiper,
.page-index .page_content .swiper .swiper-item,
.page-index .page_content .swiper image {
  width: 100%;
  height: 280rpx;
  border-radius: 10rpx;
}
.page-index .page_content .nav {
  padding-bottom: 26rpx;
  background: #fff;
  opacity: 1;
  border-radius: 14rpx;
  width: 100%;
  margin-bottom: 30rpx;
}
.page-index .page_content .nav .item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 25%;
  margin-top: 30rpx;
}
.page-index .page_content .nav .item image {
  width: 82rpx;
  height: 82rpx;
}
.page-index .page_content .nav-bd {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.page-index .page_content .nav-bd .item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.page-index .page_content .nav-bd .item .txt {
  font-size: 32rpx;
  color: #282828;
}
.page-index .page_content .nav-bd .item .label {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 124rpx;
  height: 32rpx;
  margin-top: 5rpx;
  font-size: 24rpx;
  color: #999;
}
.page-index .page_content .nav-bd .item.active {
  color: #c9ab79;
}
.page-index .page_content .nav-bd .item.active .txt {
  color: #c9ab79;
}
.page-index .page_content .nav-bd .item.active .label {
  background: linear-gradient(90deg, #f62c2c 0%, #f96e29 100%);
  border-radius: 16rpx;
  color: #fff;
}
.page-index .page_content .index-product-wrapper {
  margin-bottom: 110rpx;
}
.page-index .page_content .index-product-wrapper.on {
  min-height: 1500rpx;
}
.page-index .page_content .index-product-wrapper .list-box {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.page-index .page_content .index-product-wrapper .list-box .item {
  width: 335rpx;
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 10rpx;
  overflow: hidden;
}
.page-index .page_content .index-product-wrapper .list-box .item image {
  width: 100%;
  height: 330rpx;
}
.page-index .page_content .index-product-wrapper .list-box .item .text-info {
  padding: 10rpx 20rpx 15rpx;
}
.page-index .page_content .index-product-wrapper .list-box .item .text-info .title {
  color: #222222;
}
.page-index .page_content .index-product-wrapper .list-box .item .text-info .old-price {
  margin-top: 8rpx;
  font-size: 26rpx;
  color: #AAAAAA;
  text-decoration: line-through;
}
.page-index .page_content .index-product-wrapper .list-box .item .text-info .old-price text {
  margin-right: 2px;
  font-size: 20rpx;
}
.page-index .page_content .index-product-wrapper .list-box .item .text-info .price {
  display: flex;
  align-items: flex-end;
  color: #c9ab79;
  font-size: 34rpx;
  font-weight: 800;
}
.page-index .page_content .index-product-wrapper .list-box .item .text-info .price text {
  padding-bottom: 4rpx;
  font-size: 24rpx;
  font-weight: 800;
}
.page-index .page_content .index-product-wrapper .list-box .item .text-info .price .txt {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28rpx;
  height: 28rpx;
  margin-left: 15rpx;
  margin-bottom: 10rpx;
  border: 1px solid #c9ab79;
  border-radius: 4rpx;
  font-size: 22rpx;
  font-weight: normal;
}
.page-index .page_content .index-product-wrapper .list-box.on {
  display: flex;
}
.productList .list {
  padding: 0 20rpx;
}
.productList .list.on {
  background-color: #fff;
  border-top: 1px solid #f6f6f6;
}
.productList .list .item {
  width: 345rpx;
  margin-top: 20rpx;
  background-color: #fff;
  border-radius: 10rpx;
}
.productList .list .item.on {
  width: 100%;
  display: flex;
  border-bottom: 1rpx solid #f6f6f6;
  padding: 30rpx 0;
  margin: 0;
}
.productList .list .item .pictrue {
  position: relative;
  width: 100%;
  height: 345rpx;
}
.productList .list .item .pictrue.on {
  width: 180rpx;
  height: 180rpx;
}
.productList .list .item .pictrue image {
  width: 100%;
  height: 100%;
  border-radius: 20rpx 20rpx 0 0;
}
.productList .list .item .pictrue image.on {
  border-radius: 6rpx;
}
.productList .list .item .text {
  padding: 20rpx 17rpx 26rpx 17rpx;
  font-size: 30rpx;
  color: #222;
}
.productList .list .item .text.on {
  width: 508rpx;
  padding: 0 0 0 22rpx;
}
.productList .list .item .text .money {
  font-size: 26rpx;
  font-weight: bold;
  margin-top: 8rpx;
}
.productList .list .item .text .money.on {
  margin-top: 50rpx;
}
.productList .list .item .text .money .num {
  font-size: 34rpx;
}
.productList .list .item .text .vip {
  font-size: 22rpx;
  color: #aaa;
  margin-top: 7rpx;
}
.productList .list .item .text .vip.on {
  margin-top: 12rpx;
}
.productList .list .item .text .vip .vip-money {
  font-size: 24rpx;
  color: #282828;
  font-weight: bold;
}
.productList .list .item .text .vip .vip-money image {
  width: 46rpx;
  height: 21rpx;
  margin-left: 4rpx;
}
.pictrue {
  position: relative;
}
.fixed {
  z-index: 100;
  position: fixed;
  left: 0;
  top: 0;
  background: linear-gradient(90deg, red 50%, #ff5400 100%);
}
.mores-txt {
  width: 100%;
  align-items: center;
  justify-content: center;
  height: 70rpx;
  color: #999;
  font-size: 24rpx;
}
.mores-txt .iconfont {
  margin-top: 2rpx;
  font-size: 20rpx;
}
.menu-txt {
  font-size: 24rpx;
  color: #454545;
}
.mp-bg {
  position: absolute;
  left: 0;
  width: 100%;
  height: 304rpx;
  background: linear-gradient(180deg, #c9ab79 0%, #F5F5F5 100%);
}
.stats {
  position: absolute;
  left: 0px;
  top: 0px;
  z-index: 2000000;
  width: 750rpx;
  height: 25px;
  background: #ffffff;
}

