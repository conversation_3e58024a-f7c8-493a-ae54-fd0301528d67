{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\marketing\\coupon\\record\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\marketing\\coupon\\record\\index.vue", "mtime": 1753666157892}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { couponUserListApi } from '@/api/marketing'\nimport { roterPre } from '@/settings'\nimport { userListApi } from '@/api/user'\nexport default {\n  name: 'CouponUser',\n  filters: {\n    failFilter(status) {\n      const statusMap = {\n        'receive': '自己领取',\n        'send': '后台发送',\n        'give': '满赠',\n        'new': '新人',\n        'buy': '买赠送'\n      }\n      return statusMap[status]\n    },\n    statusFilter(status) {\n      const statusMap = {\n        0: '未使用',\n        1: '已使用',\n        2: '已过期'\n      }\n      return statusMap[status]\n    }\n  },\n  data() {\n    return {\n      Loading: false,\n      roterPre: roterPre,\n      imgList: [],\n      tableFromIssue: {\n        page: 1,\n        limit: 20,\n        uid: '',\n        name: '',\n        status: ''\n      },\n      issueData: {\n        data: [],\n        total: 0\n      },\n      loading: false,\n      options: []\n    }\n  },\n  mounted() {\n    this.getIssueList()\n  },\n  methods: {\n    remoteMethod(query) {\n      if (query !== '') {\n        this.loading = true;\n        setTimeout(() => {\n          this.loading = false;\n          userListApi({keywords: query, page: 1, limit: 10}).then(res => {\n            this.options = res.list\n          })\n        }, 200);\n      } else {\n        this.options = [];\n      }\n    },\n    seachList() {\n      this.tableFromIssue.page = 1\n      this.getIssueList()\n    },\n    // 列表\n    getIssueList() {\n      this.Loading = true\n      couponUserListApi(this.tableFromIssue).then(res => {\n        this.issueData.data = res.list\n        this.issueData.total = res.total\n        // this.issueData.data.map((item) => {\n        //   this.imgList.push(item.user.avatar)\n        // })\n        this.Loading = false\n      }).catch(res => {\n        this.Loading = false\n        this.$message.error(res.message)\n      })\n    },\n    pageChangeIssue(page) {\n      this.tableFromIssue.page = page\n      this.getIssueList()\n    },\n    handleSizeChangeIssue(val) {\n      this.tableFromIssue.limit = val\n      this.getIssueList()\n    }\n  }\n}\n", null]}