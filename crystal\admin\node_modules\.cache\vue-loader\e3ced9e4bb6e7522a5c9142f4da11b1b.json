{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\storeComment\\index.vue?vue&type=template&id=6098b449&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\storeComment\\index.vue", "mtime": 1753666157925}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\n<div class=\"divBox\">\n  <el-card class=\"box-card\">\n    <div slot=\"header\" class=\"clearfix\">\n      <div class=\"container\">\n        <el-form :inline=\"true\">\n          <el-form-item label=\"时间选择：\" class=\"width100\">\n            <el-radio-group v-model=\"tableFrom.dateLimit\" type=\"button\"  @change=\"selectChange(tableFrom.dateLimit)\" class=\"mr20\"  size=\"small\">\n              <el-radio-button :label=item.val v-for=\"(item,i) in fromList.fromTxt\" :key=\"i\">{{item.text}}</el-radio-button>\n            </el-radio-group>\n            <el-date-picker @change=\"onchangeTime\" v-model=\"timeVal\" value-format=\"yyyy-MM-dd\"  format=\"yyyy-MM-dd\"  size=\"small\" type=\"daterange\" placement=\"bottom-end\" placeholder=\"自定义时间\" style=\"width: 220px;\"></el-date-picker>\n          </el-form-item>\n          <el-form-item label=\"评价状态：\" class=\"mr10\">\n            <el-select v-model=\"tableFrom.isReply\" placeholder=\"请选择评价状态\" @change=\"seachList\" size=\"small\" class=\"selWidth\" clearable>\n              <el-option label=\"已回复\" value=\"1\"></el-option>\n              <el-option label=\"未回复\" value=\"0\"></el-option>\n            </el-select>\n          </el-form-item>\n          <el-form-item label=\"商品搜索：\" class=\"mr10\">\n            <el-input v-model=\"tableFrom.productSearch\" placeholder=\"请输入商品名称\" class=\"selWidth\" size=\"small\" clearable>\n              <el-button slot=\"append\" icon=\"el-icon-search\"  @click=\"seachList\" size=\"small\"/>\n            </el-input>\n          </el-form-item>\n          <el-form-item label=\"用户名称：\">\n            <el-input v-model=\"tableFrom.nickname\" placeholder=\"请输入用户名称\" class=\"selWidth\" size=\"small\" clearable>\n              <el-button slot=\"append\" icon=\"el-icon-search\"  @click=\"seachList\" size=\"small\"/>\n            </el-input>\n          </el-form-item>\n        </el-form>\n      </div>\n      <el-button size=\"small\" type=\"primary\" @click=\"add\" v-hasPermi=\"['admin:product:reply:save']\">添加虚拟评论</el-button>\n    </div>\n    <el-table\n      v-loading=\"listLoading\"\n      :data=\"tableData.data\"\n      style=\"width: 100%\"\n      size=\"mini\"\n      class=\"table\"\n    >\n      <el-table-column\n        prop=\"id\"\n        label=\"ID\"\n        width=\"50\"\n      />\n      <el-table-column label=\"商品信息\" min-width=\"400\">\n        <template slot-scope=\"scope\">\n          <div class=\"demo-image__preview acea-row row-middle\" v-if=\"scope.row.storeProduct\">\n            <el-image\n              style=\"width:30px;height: 30px;\"\n              :src=\"scope.row.storeProduct.image\"\n              :preview-src-list=\"[scope.row.storeProduct.image]\"\n              class=\"mr10\"\n            />\n            <div class=\"info\">{{scope.row.storeProduct.storeName}}</div>\n          </div>\n        </template>\n      </el-table-column>\n      <el-table-column\n        prop=\"nickname\"\n        label=\"用户名称\"\n        min-width=\"100\"\n      />\n      <el-table-column\n        prop=\"productScore\"\n        label=\"商品评分\"\n        min-width=\"90\"\n      />\n      <el-table-column\n        prop=\"serviceScore\"\n        label=\"服务评分\"\n        min-width=\"90\"\n      />\n      <el-table-column\n        label=\"评价内容\"\n        min-width=\"210\"\n      >\n        <template slot-scope=\"scope\">\n          <div class=\"mb5 content_font\">{{scope.row.comment}}</div>\n          <template v-if=\"scope.row.pics.length && scope.row.pics[0]\">\n            <div class=\"demo-image__preview\">\n              <el-image\n                :src=\"item\"\n                class='mr5'\n                :preview-src-list=\"[item]\"\n                v-for=\"(item,index) in scope.row.pics\" :key=\"index\"\n              />\n            </div>\n          </template>\n        </template>\n      </el-table-column>\n      <el-table-column\n        prop=\"merchantReplyContent\"\n        label=\"回复内容\"\n        min-width=\"250\"\n      />\n      <el-table-column\n        label=\"评价时间\"\n        min-width=\"120\"\n      >\n        <template slot-scope=\"scope\">\n          <span> {{scope.row.createTime}}</span>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" min-width=\"120\" fixed=\"right\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <el-button type=\"text\" size=\"small\" @click=\"reply(scope.row.id)\" class=\"mr10\" v-hasPermi=\"['admin:product:reply:comment']\">回复</el-button>\n          <el-button type=\"text\" size=\"small\" @click=\"handleDelete(scope.row.id, scope.$index)\" v-hasPermi=\"['admin:product:reply:delete']\">删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <div class=\"block\">\n      <el-pagination\n        :page-sizes=\"[20, 40, 60, 80]\"\n        :page-size=\"tableFrom.limit\"\n        :current-page=\"tableFrom.page\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        :total=\"tableData.total\"\n        @size-change=\"handleSizeChange\"\n        @current-change=\"pageChange\"\n      />\n    </div>\n    <el-dialog\n      title=\"提示\"\n      :visible.sync=\"dialogVisible\"\n      width=\"700px\"\n      z-index=\"4\"\n      :before-close=\"handleClose\">\n      <creat-comment :key=\"timer\" @getList=\"seachList\"></creat-comment>\n    </el-dialog>\n  </el-card>\n</div>\n", null]}