(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["uni_modules/shmily-drag-image/components/shmily-drag-image/shmily-drag-image"],{"0174":function(t,e,i){"use strict";i.r(e);var n=i("2706"),a=i.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);e["default"]=a.a},2706:function(t,e,i){"use strict";(function(t){var n=i("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n(i("7ca3"));function s(t,e){var i="undefined"!==typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!i){if(Array.isArray(t)||(i=function(t,e){if(!t)return;if("string"===typeof t)return r(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);"Object"===i&&t.constructor&&(i=t.constructor.name);if("Map"===i||"Set"===i)return Array.from(t);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return r(t,e)}(t))||e&&t&&"number"===typeof t.length){i&&(t=i);var n=0,a=function(){};return{s:a,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,o=!0,l=!1;return{s:function(){i=i.call(t)},n:function(){var t=i.next();return o=t.done,t},e:function(t){l=!0,s=t},f:function(){try{o||null==i.return||i.return()}finally{if(l)throw s}}}}function r(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=new Array(e);i<e;i++)n[i]=t[i];return n}var o={emits:["input","update:modelValue"],props:{value:{type:Array,default:function(){return[]}},modelValue:{type:Array,default:function(){return[]}},keyName:{type:String,default:null},number:{type:Number,default:6},imageWidth:{type:Number,default:0},cols:{type:Number,default:3},borderRadius:{type:Number,default:0},padding:{type:Number,default:10},scale:{type:Number,default:1.1},opacity:{type:Number,default:.7},addImage:{type:Function,default:null},delImage:{type:Function,default:null}},data:function(){return{imageList:[],width:0,add:{x:0,y:0},colsValue:0,viewWidth:0,tempItem:null,timer:null,changeStatus:!0,preStatus:!0,first:!0}},computed:{areaHeight:function(){var t="";return t=this.imageList.length<this.number?(Math.ceil((this.imageList.length+1)/this.colsValue)*this.viewWidth).toFixed()+"px":(Math.ceil(this.imageList.length/this.colsValue)*this.viewWidth).toFixed()+"px",console.log("areaHeight",t),t},childWidth:function(){return this.viewWidth-2*this.rpx2px(this.padding)+"px"}},watch:{value:{handler:function(t){if(!this.first&&this.changeStatus){console.log("watch",t);for(var e=!1,i=0;i<t.length;i++)e?this.addProperties(this.getSrc(t[i])):this.imageList.length!==i&&this.imageList[i].src===this.getSrc(t[i])||(e=!0,this.imageList.splice(i),this.addProperties(this.getSrc(t[i])))}},deep:!0},modelValue:{handler:function(t){if(!this.first&&this.changeStatus){console.log("watch",t);for(var e=!1,i=0;i<t.length;i++)e?this.addProperties(this.getSrc(t[i])):this.imageList.length!==i&&this.imageList[i].src===this.getSrc(t[i])||(e=!0,this.imageList.splice(i),this.addProperties(this.getSrc(t[i])))}},deep:!0}},created:function(){this.width=t.getSystemInfoSync().windowWidth},mounted:function(){var e=this,i=t.createSelectorQuery().in(this);i.select(".con").boundingClientRect((function(t){e.colsValue=e.cols,e.viewWidth=t.width/e.cols,e.imageWidth>0&&(e.viewWidth=e.rpx2px(e.imageWidth),e.colsValue=Math.floor(t.width/e.viewWidth));var i,n=e.value,a=s(n);try{for(a.s();!(i=a.n()).done;){var r=i.value;e.addProperties(e.getSrc(r))}}catch(o){a.e(o)}finally{a.f()}e.first=!1})),i.exec()},methods:{getSrc:function(t){return null!==this.keyName?t[this.keyName]:t},onChange:function(t,e){var i=this;if(e&&(e.oldX=t.detail.x,e.oldY=t.detail.y,"touch"===t.detail.source)){e.moveEnd&&(e.offset=Math.sqrt(Math.pow(e.oldX-e.absX*this.viewWidth,2)+Math.pow(e.oldY-e.absY*this.viewWidth,2)));var n=Math.floor((t.detail.x+this.viewWidth/2)/this.viewWidth);if(n>=this.colsValue)return;var a=Math.floor((t.detail.y+this.viewWidth/2)/this.viewWidth),r=this.colsValue*a+n;if(e.index!=r&&r<this.imageList.length){this.changeStatus=!1;var o,l=s(this.imageList);try{var u=function(){var t=o.value;e.index>r&&t.index>=r&&t.index<e.index?i.change(t,1):e.index<r&&t.index<=r&&t.index>e.index?i.change(t,-1):t.id!=e.id&&(t.offset=0,t.x=t.oldX,t.y=t.oldY,setTimeout((function(){i.$nextTick((function(){t.x=t.absX*i.viewWidth,t.y=t.absY*i.viewWidth}))}),0))};for(l.s();!(o=l.n()).done;)u()}catch(h){l.e(h)}finally{l.f()}e.index=r,e.absX=n,e.absY=a,e.moveEnd||setTimeout((function(){i.$nextTick((function(){e.x=e.absX*i.viewWidth,e.y=e.absY*i.viewWidth}))}),0),this.sortList()}}},change:function(t,e){var i=this;t.index+=e,t.offset=0,t.x=t.oldX,t.y=t.oldY,t.absX=t.index%this.colsValue,t.absY=Math.floor(t.index/this.colsValue),setTimeout((function(){i.$nextTick((function(){t.x=t.absX*i.viewWidth,t.y=t.absY*i.viewWidth}))}),0)},touchstart:function(t){var e=this;this.imageList.forEach((function(t){t.zIndex=t.index+9})),t.zIndex=99,t.moveEnd=!0,this.tempItem=t,this.timer=setTimeout((function(){t.scale=e.scale,t.opacity=e.opacity,clearTimeout(e.timer),e.timer=null}),200)},touchend:function(t){var e=this;this.previewImage(t),t.scale=1,t.opacity=1,t.x=t.oldX,t.y=t.oldY,t.offset=0,t.moveEnd=!1,setTimeout((function(){e.$nextTick((function(){t.x=t.absX*e.viewWidth,t.y=t.absY*e.viewWidth,e.tempItem=null,e.changeStatus=!0}))}),0)},previewImage:function(e){var i=this;if(this.timer&&this.preStatus&&this.changeStatus&&e.offset<28.28){clearTimeout(this.timer),this.timer=null;var n=this.value||this.modelValue,a=n.map((function(t){return i.getSrc(t)}));console.log(n,a),t.previewImage({urls:a,current:e.src,success:function(){i.preStatus=!1,setTimeout((function(){i.preStatus=!0}),600)},fail:function(t){console.log(t)}})}else this.timer&&(clearTimeout(this.timer),this.timer=null)},mouseenter:function(){},mouseleave:function(){},addImages:function(){var e=this;if("function"===typeof this.addImage)this.addImage.bind(this.$parent)();else{var i=this.number-this.imageList.length;t.chooseImage({count:i,sourceType:["album","camera"],success:function(t){for(var n=i<=t.tempFilePaths.length?i:t.tempFilePaths.length,a=0;a<n;a++)e.addProperties(t.tempFilePaths[a]);e.sortList()}})}},delImages:function(t,e){var i=this;"function"===typeof this.delImage?this.delImage.bind(this.$parent)((function(){i.delImageHandle(t,e)})):this.delImageHandle(t,e)},delImageHandle:function(t,e){var i=this;this.imageList.splice(e,1);var n,a=s(this.imageList);try{var r=function(){var e=n.value;e.index>t.index&&(e.index-=1,e.x=e.oldX,e.y=e.oldY,e.absX=e.index%i.colsValue,e.absY=Math.floor(e.index/i.colsValue),i.$nextTick((function(){e.x=e.absX*i.viewWidth,e.y=e.absY*i.viewWidth})))};for(a.s();!(n=a.n()).done;)r()}catch(o){a.e(o)}finally{a.f()}this.add.x=this.imageList.length%this.colsValue*this.viewWidth+"px",this.add.y=Math.floor(this.imageList.length/this.colsValue)*this.viewWidth+"px",this.sortList()},delImageMp:function(t,e){this.delImages(t,e)},sortList:function(){var t=this;console.log("sortList");var e=[],i=this.value,n=this.imageList.slice();n.sort((function(t,e){return t.index-e.index}));var r,o=s(n);try{var l=function(){var n=r.value,s=i.find((function(e){return t.getSrc(e)==n.src}));s?e.push(s):null!==t.keyName?e.push((0,a.default)({},t.keyName,n.src)):e.push(n.src)};for(o.s();!(r=o.n()).done;)l()}catch(u){o.e(u)}finally{o.f()}this.$emit("input",e),this.$emit("update:modelValue",e),this.$emit("changeprice")},addProperties:function(t){console.log(t);var e=this.imageList.length%this.colsValue,i=Math.floor(this.imageList.length/this.colsValue),n=e*this.viewWidth,a=i*this.viewWidth;this.imageList.push({src:t,x:n,y:a,oldX:n,oldY:a,absX:e,absY:i,scale:1,zIndex:9,opacity:1,index:this.imageList.length,id:this.guid(16),disable:!1,offset:0,moveEnd:!1}),this.add.x=this.imageList.length%this.colsValue*this.viewWidth+"px",this.add.y=Math.floor(this.imageList.length/this.colsValue)*this.viewWidth+"px"},nothing:function(){},rpx2px:function(t){return this.width*t/750},guid:function(){for(var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:32,e="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split(""),i=[],n=e.length,a=0;a<t;a++)i[a]=e[0|Math.random()*n];return i.shift(),"u".concat(i.join(""))}}};e.default=o}).call(this,i("df3c")["default"])},"3c68":function(t,e,i){"use strict";var n=i("902f"),a=i.n(n);a.a},"902f":function(t,e,i){},c5b0:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement;this._self._c},a=[]},c7a4:function(t,e,i){"use strict";i.r(e);var n=i("c5b0"),a=i("0174");for(var s in a)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return a[t]}))}(s);i("3c68");var r=i("828b"),o=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"617300b2",null,!1,n["a"],void 0);e["default"]=o.exports}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uni_modules/shmily-drag-image/components/shmily-drag-image/shmily-drag-image-create-component',
    {
        'uni_modules/shmily-drag-image/components/shmily-drag-image/shmily-drag-image-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("c7a4"))
        })
    },
    [['uni_modules/shmily-drag-image/components/shmily-drag-image/shmily-drag-image-create-component']]
]);
