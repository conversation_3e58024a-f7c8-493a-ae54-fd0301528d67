{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/order_confirm/index.vue?769e", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/order_confirm/index.vue?1bd5", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/order_confirm/index.vue?1396", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/order_confirm/index.vue?cd62", "uni-app:///pages/users/order_confirm/index.vue", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/order_confirm/index.vue?d689", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/order_confirm/index.vue?c6fb"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "couponListWindow", "addressWindow", "orderGoods", "home", "authorize", "data", "userBraceletId", "type", "orderShow", "textareaStatus", "cartArr", "value", "title", "payStatus", "payType", "openType", "active", "coupon", "list", "statusTile", "address", "addressId", "addressInfo", "couponId", "cartId", "userInfo", "mark", "couponTitle", "coupon_price", "useIntegral", "integral_price", "integral", "ChangePrice", "formIds", "status", "is_address", "toPay", "shippingType", "system_store", "storePostage", "contacts", "contactsTel", "mydata", "storeList", "store_self_mention", "cartInfo", "priceGroup", "animated", "totalPrice", "integralRatio", "pagesUrl", "orderKey", "offlinePostage", "isAuto", "isShowAuth", "payChannel", "news", "again", "addAgain", "bargain", "combination", "secKill", "orderInfoVo", "addressList", "orderProNum", "preOrderNo", "computed", "watch", "is<PERSON>ogin", "handler", "deep", "onLoad", "onShow", "uni", "_this", "methods", "getloadPreOrder", "onLoadFun", "getList", "latitude", "longitude", "page", "limit", "changeClose", "showStoreList", "url", "computedPrice", "addressType", "bindPickerChange", "ChangCouponsClone", "changeTextareaStatus", "Chang<PERSON>ou<PERSON>ns", "ChangeIntegral", "OnDefault<PERSON><PERSON><PERSON>", "OnChangeAddress", "bindHideKeyboard", "getCouponList", "getaddressInfo", "res", "that", "getAddressDefault", "payItem", "setTimeout", "couponTap", "car", "on<PERSON><PERSON><PERSON>", "realName", "phone", "payment", "getOrderPay", "orderNo", "scene", "timeStamp", "nonceStr", "package", "signType", "paySign", "ticket", "success", "icon", "tab", "fail", "complete", "Tips", "location", "protocol", "goPages", "getPayType", "SubOrder", "storeId"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACmM;AACnM,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAAkwB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACiKtxB;AASA;AAIA;AAGA;AAGA;AAQA;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAMA;EACAC;IACAC;IACAC;IACAC;IACAC;IAEAC;EAEA;EACAC;IACA;MACAC;MACAC;MACAC;MAAA;MACAC;MACA;MACAC;QACA;QACA;QACAC;QACAC;QACAC;MACA,GACA;QACA;QACA;QACAF;QACAC;QACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAAA,CACA;;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;QACAA;QACAC;QACAC;MACA;MAAA;MACAC;QACAA;QACAC;MACA;MAAA;MACAC;MAAA;MACAD;MAAA;MACAE;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MACAC;IACA;EACA;;EACAC;EACAC;IACAC;MACAC;QACA;UACA;UACA;QACA;MACA;;MACAC;IACA;EACA;EACAC;IAKA;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACA;MACA;IACA;MACA;IACA;EACA;EACA;AACA;AACA;EACAC;IACA;IACA;IACA;IACA;MACA;IAAA;IAIAC;MACA;QACAC;MACA;MACA;MACAD;IACA;;IAEA;IACA;IACA;IACA;IACA;IACA;EACA;;EACA;AACA;AACA;EACA;EACA;EACA;EACAE;IACA;IACAC;MAAA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;UACA;QACA;MACA;QACA;UACAhE;QACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAiE;MACA;MACA;MACA;IAAA,CACA;IACA;AACA;AACA;IACAC;MAAA;MACA;MACA;MACA;QACAC;QAAA;QACAC;QAAA;QACAC;QACAC;MACA;MACA;QACA;QACA;QACA;MACA;QACA;UACAtE;QACA;MACA;IACA;IACA;IACAuE;MACA;IACA;IACA;AACA;AACA;IACAC;MAEA;MACA;QACAX;UACAY;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;QACAjE;QACAQ;QACAN;QACAc;QACA4B;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;UACArD;QACA;MACA;IACA;IACA2E;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;MACA;MACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;QACAzE;QACAS;QACAJ;MACA;QACA;UACAL;UACAA;QACA;MACA;MACA;QACA;QACAA;QACAA;MACA;QACA;QACAA;QACAA;QACAS;QACAJ;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;AACA;AACA;IACAqE;MACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MAAA;MACA;QACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA;QACA;UACA;YACAC;YACAC;YACAA;YACAA;UACA;QACA;MACA;QACAC;UACA;YACAF;YACAC;YACAA;YACAA;UACA;QACA;MACA;IACA;IACAE;MACA;MACA;MACAF;MACAA;MACAA;MACAA;MACAG;QACAH;MACA;IACA;IACAI;MACA;MACA;IACA;IACAC;MACA;MACAL;IACA;IACAM;MACA;MACAN;MACAA;MACAA;IACA;IACAO;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;QACAT;MAEA;QACA1B;QACA;UACA7D;QACA;MACA;IACA;IACAiG;MACA;MACA;MACA;QACAC;QACAvD;QACAzC;QACAiG;MACA;QACA;QACA;UACA;YAEAtC;cACAuC;cACAC;cACAC;cACAC;cACAC;cACAC;cACAC;gBACA7C;gBACA;kBACAA;kBACA,wFACAX,uBACA;oBACAlD;oBACA2G;kBACA;oBACAC;oBACAnC;kBACA;kBACA;oBACAzE;oBACA2G;kBACA;oBACAC;oBACAnC;kBACA;gBACA;kBACAZ;kBACA;oBACA7D;kBACA;gBACA;cAEA;cACA6G;gBACAhD;gBACA;kBACA7D;gBACA;kBACA4G;kBACAnC;gBACA;cACA;cACAqC;gBACAjD;gBACA;gBACA,2DACAkD;kBACA/G;gBACA;kBACA4G;kBACAnC;gBACA;cACA;YACA;YA8CA;UACA;YACAZ;YACA;cACA7D;YACA;cACA4G;cACAnC;YACA;YACA;UACA;YACAZ;YACA0B;cACAvF;YACA;cACA4G;cACAnC;YACA;YACAiB;cACAsB,sEACAC,yCACAC;YACA;YACA;QAAA;MAEA;QACArD;QACA;UACA7D;QACA;MACA;IACA;IACAmH;MACA;MACA;MACA;QACA;QACA;QACA;UACAtD;UACA;YACA7D;UACA;YACA4G;YACAnC;UACA;UACA;QACA;UACAZ;UACA,+GACA;YACA7D;YACA2G;UACA;YACAC;YACAnC;UACA;UACA;YACAzE;YACA2G;UACA;YACAC;YACAnC;UACA;UACA;QACA;UAEAc;UACA;UACA1B;YACAuC;YACAC;YACAC;YACAC;YACAC;YACAE;cACA7C;cACA,+GACA;gBACA7D;gBACA2G;cACA;gBACAC;gBACAnC;cACA;cACA;gBACAzE;gBACA2G;cACA;gBACAC;gBACAnC;cACA;YACA;YACAoC;cACAhD;cACA;gBACA7D;cACA;gBACA4G;gBACAnC;cACA;YACA;YACAqC;cACAjD;cACA;cACA;gBACA7D;cACA;gBACA4G;gBACAnC;cACA;YACA;UACA;UA8BA;QACA;UACAZ;UACA;YACA7D;UACA;YACA4G;YACAnC;UACA;UACA;QACA;UAAA;UACAiB;YACA;YACA;YACAsB;YACA;cACAhH;cACA2G;YACA;cACAC;cACAnC;YACA;UACA;UACA;MAAA;IAEA;IACA2C;MAEA;QACA3H;MAEA;QACAO;MACA;MACA;QACAA;MACA;MACA;QACA;UACA;YACAA;UACA;QACA;QACA;UACA;YACAA;UACA;QACA;QACA;UACA;YACAA;UACA;QACA;QACA;UACAA;QACA;MACA;MACAP;QACAqG;QACAC;QACAtF;QACAE;QACAT;QACAe;QACAoC;QACAvC;QACAnB;QACAD;QACA2H;QACA5F;QACAkB;MAEA;MACA,+FACA,kBACAoE;QACA/G;MACA;MACA6D;QACA7D;MACA;MAEA;QACAuF;MACA;IAKA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACh9BA;AAAA;AAAA;AAAA;AAAq8C,CAAgB,ovCAAG,EAAC,C;;;;;;;;;;;ACAz9C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/users/order_confirm/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/users/order_confirm/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=064d693c&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=064d693c&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"064d693c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/users/order_confirm/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=064d693c&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !(_vm.shippingType == 0) ? _vm.storeList.length : null\n  var m0 = _vm.shippingType == 0 ? parseFloat(_vm.orderInfoVo.freightFee) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view class='order-submission'>\r\n\t\t\t<view class=\"allAddress\" :style=\"store_self_mention ? '':'padding-top:10rpx;'\">\r\n\t\t\t\t<view class=\"nav acea-row\">\r\n\t\t\t\t\t<view class=\"item font-color\" :class=\"shippingType == 0 ? 'on' : 'on2'\" @tap=\"addressType(0)\"\r\n\t\t\t\t\t\tv-if='store_self_mention'></view>\r\n\t\t\t\t\t<!-- <view class=\"item font-color\" :class=\"shippingType == 1 ? 'on' : 'on2'\" @tap=\"addressType(1)\"\r\n\t\t\t\t\t\tv-if='store_self_mention'></view> -->\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='address acea-row row-between-wrapper' @tap='onAddress' v-if='shippingType == 0' :style=\"store_self_mention ? '':'border-top-left-radius: 14rpx;border-top-right-radius: 14rpx;'\">\r\n\t\t\t\t\t<view class='addressCon' v-if=\"addressInfo.realName\">\r\n\t\t\t\t\t\t<view class='name'>{{addressInfo.realName}}\r\n\t\t\t\t\t\t\t<text class='phone'>{{addressInfo.phone}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"acea-row\">\r\n\t\t\t\t\t\t\t<text class='default font-color'\r\n\t\t\t\t\t\t\t\tv-if=\"addressInfo.isDefault\">[默认]</text>\r\n\t\t\t\t\t\t\t<text class=\"line2\">{{addressInfo.province}}{{addressInfo.city}}{{addressInfo.district}}{{addressInfo.detail}}</text>\t\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='addressCon' v-else>\r\n\t\t\t\t\t\t<view class='setaddress'>设置收货地址</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='iconfont icon-jiantou'></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='address acea-row row-between-wrapper' v-else @tap=\"showStoreList\">\r\n\t\t\t\t\t<block v-if=\"storeList.length>0\">\r\n\t\t\t\t\t\t<view class='addressCon'>\r\n\t\t\t\t\t\t\t<view class='name'>{{system_store.name}}\r\n\t\t\t\t\t\t\t\t<text class='phone'>{{system_store.phone}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"line1\"> {{system_store.address}}{{\", \" + system_store.detailedAddress}}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='iconfont icon-jiantou'></view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t<view>暂无门店信息</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='line'>\r\n\t\t\t\t\t<image src='/static/images/line.jpg'></image>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"pad30\">\r\n\t\t\t\t<orderGoods :cartInfo=\"cartInfo\" :orderProNum=\"orderProNum\" :type=\"type\"></orderGoods>\r\n\t\t\t\t<view class='wrapper borRadius14'>\r\n\t\t\t\t\t<view class='item acea-row row-between-wrapper' @tap='couponTap'\r\n\t\t\t\t\t\tv-if=\"!orderInfoVo.bargainId && !orderInfoVo.combinationId && !orderInfoVo.seckillId && productType==='normal'\">\r\n\t\t\t\t\t\t<view>优惠券</view>\r\n\t\t\t\t\t\t<view class='discount'>{{couponTitle}}\r\n\t\t\t\t\t\t\t<text class='iconfont icon-jiantou'></text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- <view class='item acea-row row-between-wrapper'\r\n\t\t\t\t\t\tv-if=\"!orderInfoVo.bargainId && !orderInfoVo.combinationId && !orderInfoVo.seckillId && productType==='normal'\">\r\n\t\t\t\t\t\t<view>积分抵扣</view>\r\n\t\t\t\t\t\t<view class='discount acea-row row-middle'>\r\n\t\t\t\t\t\t\t<view> {{useIntegral ? \"剩余积分\":\"当前积分\"}}\r\n\t\t\t\t\t\t\t\t<text class='num font-color'>{{useIntegral ? orderInfoVo.surplusIntegral : orderInfoVo.userIntegral || 0}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<checkbox-group @change=\"ChangeIntegral\">\r\n\t\t\t\t\t\t\t\t<checkbox :checked='useIntegral ? true : false' :disabled=\"orderInfoVo.userIntegral==0 && !useIntegral\"/>\r\n\t\t\t\t\t\t\t</checkbox-group>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view> -->\r\n\t\t\t\t\t<!-- <view class='item acea-row row-between-wrapper'\r\n\t\t\t\t\t\tv-if=\"priceGroup.vipPrice > 0 && userInfo.vip && !pinkId && !BargainId && !combinationId && !seckillId\">\r\n\t\t\t\t\t\t<view>会员优惠</view>\r\n\t\t\t\t\t\t<view class='discount'>-￥{{priceGroup.vipPrice}}</view>\r\n\t\t\t\t\t</view> -->\r\n\t\t\t\t\t<view class='item acea-row row-between-wrapper' v-if='shippingType==0'>\r\n\t\t\t\t\t\t<view>快递费用</view>\r\n\t\t\t\t\t\t<view class='discount' v-if='parseFloat(orderInfoVo.freightFee) > 0'>\r\n\t\t\t\t\t\t\t+￥{{orderInfoVo.freightFee}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='discount' v-else>免运费</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-else>\r\n\t\t\t\t\t\t<view class=\"item acea-row row-between-wrapper\">\r\n\t\t\t\t\t\t\t<view>联系人</view>\r\n\t\t\t\t\t\t\t<view class=\"discount textR\">\r\n\t\t\t\t\t\t\t\t<input type=\"text\" placeholder=\"请填写您的联系姓名\" placeholder-style=\"color:#ccc;\" placeholder-class=\"placeholder\"\r\n\t\t\t\t\t\t\t\t\t@blur='realName'></input>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"item acea-row row-between-wrapper\">\r\n\t\t\t\t\t\t\t<view>联系电话</view>\r\n\t\t\t\t\t\t\t<view class=\"discount textR\">\r\n\t\t\t\t\t\t\t\t<input type=\"text\" placeholder=\"请填写您的联系电话\"  placeholder-style=\"color:#ccc;\" placeholder-class=\"placeholder\"\r\n\t\t\t\t\t\t\t\t\t@blur='phone'></input>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- <view class='item acea-row row-between-wrapper' wx:else>\r\n\t\t      <view>自提门店</view>\r\n\t\t      <view class='discount'>{{system_store.name}}</view>\r\n\t\t    </view> -->\r\n\t\t\t\t\t<view class='item' v-if=\"textareaStatus\">\r\n\t\t\t\t\t\t<view>备注信息</view>\r\n\t\t\t\t\t\t<textarea v-if=\"coupon.coupon===false\" placeholder-class='placeholder' @input='bindHideKeyboard'\r\n\t\t\t\t\t\t\tvalue=\"\" name=\"mark\" placeholder='请添加备注（150字以内）'></textarea>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='wrapper borRadius14'>\r\n\t\t\t\t\t<view class='item'>\r\n\t\t\t\t\t\t<view>支付方式</view>\r\n\t\t\t\t\t\t<view class='list'>\r\n\t\t\t\t\t\t\t<view class='payItem acea-row row-middle' :class='active==index ?\"on\":\"\"'\r\n\t\t\t\t\t\t\t\t@tap='payItem(index)' v-for=\"(item,index) in cartArr\" :key='index'\r\n\t\t\t\t\t\t\t\tv-if=\"item.payStatus==1\">\r\n\t\t\t\t\t\t\t\t<view class='name acea-row row-center-wrapper'>\r\n\t\t\t\t\t\t\t\t\t<view class='iconfont animated'\r\n\t\t\t\t\t\t\t\t\t\t:class='(item.icon) + \" \" + (animated==true&&active==index ?\"bounceIn\":\"\")'>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t{{item.name}}\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class='tip'>{{item.title}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='moneyList borRadius14'>\r\n\t\t\t\t\t<view class='item acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t<view>商品总价：</view>\r\n\t\t\t\t\t\t<view class='money'>￥{{orderInfoVo.proTotalFee || 0}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item acea-row row-between-wrapper' v-if=\"orderInfoVo.couponFee > 0\">\r\n\t\t\t\t\t\t<view>优惠券抵扣：</view>\r\n\t\t\t\t\t\t<view class='money'>-￥{{orderInfoVo.couponFee}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item acea-row row-between-wrapper' v-if=\"orderInfoVo.deductionPrice > 0\">\r\n\t\t\t\t\t\t<view>积分抵扣：</view>\r\n\t\t\t\t\t\t<view class='money'>-￥{{orderInfoVo.deductionPrice}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item acea-row row-between-wrapper' v-if=\"orderInfoVo.freightFee > 0\">\r\n\t\t\t\t\t\t<view>运费：</view>\r\n\t\t\t\t\t\t<view class='money'>+￥{{orderInfoVo.freightFee}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view style='height:120rpx;'></view>\r\n\t\t\t</view>\r\n\t\t\t<view class='footer acea-row row-between-wrapper'>\r\n\t\t\t\t<view>合计:\r\n\t\t\t\t\t<text class='font-color'>￥{{orderInfoVo.payFee || 0}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='settlement' style='z-index:100' @tap=\"SubOrder\">立即结算</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<couponListWindow :coupon='coupon' @ChangCouponsClone=\"ChangCouponsClone\" :openType='openType' @ChangCoupons=\"ChangCoupons\" :orderShow=\"orderShow\"></couponListWindow>\r\n\t\t<addressWindow ref=\"addressWindow\" @changeTextareaStatus=\"changeTextareaStatus\" :address='address'\r\n\t\t\t:pagesUrl=\"pagesUrl\" @OnDefaultAddress=\"OnDefaultAddress\"  @OnChangeAddress=\"OnChangeAddress\" @changeClose=\"changeClose\"></addressWindow>\r\n\t\t<!-- #ifdef MP -->\r\n\t\t<!-- <authorize @onLoadFun=\"onLoadFun\" :isAuto=\"isAuto\" :isShowAuth=\"isShowAuth\" @authColse=\"authColse\"></authorize> -->\r\n\t\t<!-- #endif -->\r\n\t\t<home></home>\r\n\t</view>\r\n</template>\r\n<script>\r\n\timport {\r\n\t\t//orderConfirm,\r\n\t\tgetCouponsOrderPrice,\r\n\t\torderCreate,\r\n\t\tpostOrderComputed,\r\n\t\twechatOrderPay,\r\n\t\twechatQueryPayResult,\r\n\t\tloadPreOrderApi\r\n\t} from '@/api/order.js';\r\n\timport {\r\n\t\tgetAddressList,\r\n\t\tgetAddressDetail\r\n\t} from '@/api/user.js';\r\n\timport {\r\n\t\topenPaySubscribe\r\n\t} from '@/utils/SubscribeMessage.js';\r\n\timport {\r\n\t\tstoreListApi\r\n\t} from '@/api/store.js';\r\n\timport {\r\n\t\tCACHE_LONGITUDE,\r\n\t\tCACHE_LATITUDE\r\n\t} from '@/config/cache.js';\r\n\timport couponListWindow from '@/components/couponListWindow';\r\n\timport addressWindow from '@/components/addressWindow';\r\n\timport orderGoods from '@/components/orderGoods';\r\n\timport home from '@/components/home';\r\n\timport {\r\n\t\ttoLogin\r\n\t} from '@/libs/login.js';\r\n\timport {\r\n\t\tmapGetters\r\n\t} from \"vuex\";\r\n\t// #ifdef MP\r\n\timport authorize from '@/components/Authorize';\r\n\t// #endif\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tcouponListWindow,\r\n\t\t\taddressWindow,\r\n\t\t\torderGoods,\r\n\t\t\thome,\r\n\t\t\t// #ifdef MP\r\n\t\t\tauthorize\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tuserBraceletId: '',\r\n\t\t\t\ttype: 0,\r\n\t\t\t\torderShow: 'orderShow', //下单页面使用优惠券组件不展示tab切换页\r\n\t\t\t\ttextareaStatus: true,\r\n\t\t\t\t//支付方式\r\n\t\t\t\tcartArr: [{\r\n\t\t\t\t\t\t\"name\": \"微信支付\",\r\n\t\t\t\t\t\t\"icon\": \"icon-weixin2\",\r\n\t\t\t\t\t\tvalue: 'weixin',\r\n\t\t\t\t\t\ttitle: '微信快捷支付',\r\n\t\t\t\t\t\tpayStatus: 1,\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\t\"name\": \"余额支付\",\r\n\t\t\t\t\t\t\"icon\": \"icon-icon-test\",\r\n\t\t\t\t\t\tvalue: 'yue',\r\n\t\t\t\t\t\ttitle: '可用余额:',\r\n\t\t\t\t\t\tpayStatus: 1,\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// {\r\n\t\t\t\t\t// \t\"name\": \"线下支付\", //offlinePayStatu：1开启线下支付；2关闭；offlinePostage：true有邮费\r\n\t\t\t\t\t// \t\"icon\": \"icon-yinhangqia\",\r\n\t\t\t\t\t// \tvalue: 'offline',\r\n\t\t\t\t\t// \ttitle: '线下支付',\r\n\t\t\t\t\t// \tpayStatus: 1,\r\n\t\t\t\t\t// },\r\n\t\t\t\t],\r\n\t\t\t\tpayType: 'weixin', //支付方式\r\n\t\t\t\topenType: 1, //优惠券打开方式 1=使用\r\n\t\t\t\tactive: 0, //支付方式切换\r\n\t\t\t\tcoupon: {\r\n\t\t\t\t\tcoupon: false,\r\n\t\t\t\t\tlist: [],\r\n\t\t\t\t\tstatusTile: '立即使用'\r\n\t\t\t\t}, //优惠券组件\r\n\t\t\t\taddress: {\r\n\t\t\t\t\taddress: false,\r\n\t\t\t\t\taddressId: 0\r\n\t\t\t\t}, //地址组件\r\n\t\t\t\taddressInfo: {}, //地址信息\r\n\t\t\t\taddressId: 0, //地址id\r\n\t\t\t\tcouponId: 0, //优惠券id\r\n\t\t\t\tcartId: '', //购物车id\r\n\t\t\t\tuserInfo: {}, //用户信息\r\n\t\t\t\tmark: '', //备注信息\r\n\t\t\t\tcouponTitle: '请选择', //优惠券\r\n\t\t\t\tcoupon_price: 0, //优惠券抵扣金额\r\n\t\t\t\tuseIntegral: false, //是否使用积分\r\n\t\t\t\tintegral_price: 0, //积分抵扣金额\r\n\t\t\t\tintegral: 0,\r\n\t\t\t\tChangePrice: 0, //使用积分抵扣变动后的金额\r\n\t\t\t\tformIds: [], //收集formid\r\n\t\t\t\tstatus: 0,\r\n\t\t\t\tis_address: false,\r\n\t\t\t\ttoPay: false, //修复进入支付时页面隐藏从新刷新页面\r\n\t\t\t\tshippingType: 0,\r\n\t\t\t\tsystem_store: {},\r\n\t\t\t\tstorePostage: 0,\r\n\t\t\t\tcontacts: '',\r\n\t\t\t\tcontactsTel: '',\r\n\t\t\t\tmydata: {},\r\n\t\t\t\tstoreList: [],\r\n\t\t\t\tstore_self_mention: 0,\r\n\t\t\t\tcartInfo: [],\r\n\t\t\t\tpriceGroup: {},\r\n\t\t\t\tanimated: false,\r\n\t\t\t\ttotalPrice: 0,\r\n\t\t\t\tintegralRatio: \"0\",\r\n\t\t\t\tpagesUrl: \"\",\r\n\t\t\t\torderKey: \"\",\r\n\t\t\t\t// usableCoupon: {},\r\n\t\t\t\tofflinePostage: \"\",\r\n\t\t\t\tisAuto: false, //没有授权的不会自动授权\r\n\t\t\t\tisShowAuth: false, //是否隐藏授权\r\n\t\t\t\tpayChannel: '',\r\n\t\t\t\tnews: true,\r\n\t\t\t\tagain: false,\r\n\t\t\t\taddAgain: false,\r\n\t\t\t\tbargain: false, //是否是砍价\r\n\t\t\t\tcombination: false, //是否是拼团\r\n\t\t\t\tsecKill: false, //是否是秒杀\r\n\t\t\t\torderInfoVo: {},\r\n\t\t\t\taddressList: [], //地址列表数据\r\n\t\t\t\torderProNum: 0,\r\n\t\t\t\tpreOrderNo: '' //预下单订单号\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: mapGetters(['isLogin', 'systemPlatform', 'productType']),\r\n\t\twatch: {\r\n\t\t\tisLogin: {\r\n\t\t\t\thandler: function(newV, oldV) {\r\n\t\t\t\t\tif (newV) {\r\n\t\t\t\t\t\tthis.getloadPreOrder();\r\n\t\t\t\t\t\t//this.getaddressInfo();\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tdeep: true\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\t// #ifdef H5\r\n\t\t\tthis.payChannel = this.$wechat.isWeixin() ? 'public' : 'weixinh5';\r\n\t\t\t// #endif\r\n\t\t\t// #ifdef MP\r\n\t\t\tthis.payChannel = 'routine';\r\n\t\t\t// #endif\r\n\t\t\t// if (!options.cartId) return this.$util.Tips({\r\n\t\t\t// \ttitle: '请选择要购买的商品'\r\n\t\t\t// }, {\r\n\t\t\t// \ttab: 3,\r\n\t\t\t// \turl: 1\r\n\t\t\t// });\r\n            this.preOrderNo = options.preOrderNo || 0;\r\n\t\t\tthis.addressId = options.addressId || 0;\r\n\t\t\tthis.is_address = options.is_address ? true : false;\r\n\t\t\tif (this.isLogin) {\r\n\t\t\t\t//this.getaddressInfo();\r\n\t\t\t\tthis.getloadPreOrder();\r\n\t\t\t} else {\r\n\t\t\t\ttoLogin();\r\n\t\t\t}\r\n\t\t},\r\n\t\t/**\r\n\t\t * 生命周期函数--监听页面显示\r\n\t\t */\r\n\t\tonShow: function() {\r\n\t\t\tlet _this = this\r\n\t\t\t// wx.getLaunchOptionsSync \r\n\t\t\tthis.textareaStatus = true;\r\n\t\t\tif (this.isLogin && this.toPay == false) {\r\n\t\t\t\t//this.getaddressInfo();\r\n\t\t\t\t\r\n\t\t\t}\r\n\r\n\t\t\tuni.$on(\"handClick\", res => {\r\n\t\t\t\tif (res) {\r\n\t\t\t\t\t_this.system_store = res.address\r\n\t\t\t\t}\r\n\t\t\t\t// 清除监听\r\n\t\t\t\tuni.$off('handClick');\r\n\t\t\t})\r\n\r\n\t\t\t// let pages = getCurrentPages();\r\n\t\t\t// let currPage = pages[pages.length - 1]; //当前页面\r\n\t\t\t// if (currPage.data.storeItem) {\r\n\t\t\t// \tlet json = currPage.data.storeItem;\r\n\t\t\t// \tthis.$set(this, 'system_store', json);\r\n\t\t\t// }\r\n\t\t},\r\n\t\t/**\r\n\t\t * 生命周期函数--监听页面隐藏\r\n\t\t */\r\n\t\t// onHide: function() {\r\n\t\t// \tthis.isClose = true\r\n\t\t// },\r\n\t\tmethods: {\r\n\t\t\t// 订单详情\r\n\t\t\tgetloadPreOrder: function() {\r\n\t\t\t\tloadPreOrderApi(this.preOrderNo).then(res => {\r\n\t\t\t\t\tlet orderInfoVo = res.data.orderInfoVo\r\n\t\t\t\t\tthis.orderInfoVo = orderInfoVo;\r\n\t\t\t\t\tthis.cartInfo = orderInfoVo.orderDetailList;\r\n\t\t\t\t\tthis.type = orderInfoVo.type;\r\n\t\t\t\t\tthis.userBraceletId = orderInfoVo.userBraceletId;\r\n\t\t\t\t\tthis.orderProNum = orderInfoVo.orderProNum;\r\n\t\t\t\t\tthis.address.addressId = this.addressId ? this.addressId :orderInfoVo.addressId;\r\n\t\t\t\t\tthis.cartArr[1].title = '可用余额:' + orderInfoVo.userBalance;\r\n\t\t\t\t\tthis.cartArr[1].payStatus = parseInt(res.data.yuePayStatus) === 1 ? 1 : 2;\r\n\t\t\t\t\tthis.cartArr[0].payStatus = parseInt(res.data.payWeixinOpen) === 1 ? 1 : 0;\r\n\t\t\t\t\tthis.store_self_mention = res.data.storeSelfMention == 'true'&& this.productType === 'normal' ? true : false;\r\n\t\t\t\t\t//调用子页面方法授权后执行获取地址列表\r\n\t\t\t\t\tthis.$nextTick(function() {\r\n\t\t\t\t\t\tthis.$refs.addressWindow.getAddressList();\r\n\t\t\t\t\t})\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\treturn this.$util.Tips({\r\n\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t});\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 授权回调事件\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tonLoadFun: function() {\r\n\t\t\t\t//this.getaddressInfo();\r\n\t\t\t\t//调用子页面方法授权后执行获取地址列表\r\n\t\t\t\t// this.$scope.selectComponent('#address-window').getAddressList();\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 获取门店列表数据\r\n\t\t\t */\r\n\t\t\tgetList: function() {\r\n\t\t\t\tlet longitude = uni.getStorageSync(\"user_longitude\"); //经度\r\n\t\t\t\tlet latitude = uni.getStorageSync(\"user_latitude\"); //纬度\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\tlatitude: latitude, //纬度\r\n\t\t\t\t\tlongitude: longitude, //经度\r\n\t\t\t\t\tpage: 1,\r\n\t\t\t\t\tlimit: 10\r\n\t\t\t\t}\r\n\t\t\t\tstoreListApi(data).then(res => {\r\n\t\t\t\t\tlet list = res.data.list || [];\r\n\t\t\t\t\tthis.$set(this, 'storeList', list);\r\n\t\t\t\t\tthis.$set(this, 'system_store', list[0]);\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\treturn this.$util.Tips({\r\n\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t});\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 关闭地址弹窗；\r\n\t\t\tchangeClose: function() {\r\n\t\t\t\tthis.$set(this.address, 'address', false);\r\n\t\t\t},\r\n\t\t\t/*\r\n\t\t\t * 跳转门店列表\r\n\t\t\t */\r\n\t\t\tshowStoreList: function() {\r\n\r\n\t\t\t\tlet _this = this\r\n\t\t\t\tif (this.storeList.length > 0) {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/users/goods_details_store/index'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 计算订单价格\r\n\t\t\tcomputedPrice: function() {\r\n\t\t\t\tlet shippingType = this.shippingType;\r\n\t\t\t\tpostOrderComputed({\r\n\t\t\t\t\taddressId: this.address.addressId,\r\n\t\t\t\t\tuseIntegral: this.useIntegral ? true : false,\r\n\t\t\t\t\tcouponId: this.couponId,\r\n\t\t\t\t\tshippingType: parseInt(shippingType) + 1,\r\n\t\t\t\t\tpreOrderNo: this.preOrderNo\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tlet data = res.data;\r\n\t\t\t\t\tthis.orderInfoVo.couponFee = data.couponFee;\r\n\t\t\t\t\t//赋值操作，userIntegral 当前积分，surplusIntegral 剩余积分\r\n\t\t\t\t\tthis.orderInfoVo.userIntegral = data.surplusIntegral;\r\n\t\t\t\t\tthis.orderInfoVo.deductionPrice = data.deductionPrice;\r\n\t\t\t\t\tthis.orderInfoVo.freightFee = data.freightFee;\r\n\t\t\t\t\tthis.orderInfoVo.payFee = data.payFee;\r\n\t\t\t\t\tthis.orderInfoVo.proTotalFee = data.proTotalFee;\r\n\t\t\t\t\tthis.orderInfoVo.useIntegral = data.useIntegral;\r\n\t\t\t\t\tthis.orderInfoVo.usedIntegral = data.usedIntegral;\r\n\t\t\t\t\tthis.orderInfoVo.surplusIntegral = data.surplusIntegral;\r\n\t\t\t\t\t//this.orderInfoVo.userIntegral = data.userIntegral;\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\treturn this.$util.Tips({\r\n\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\taddressType: function(e) {\r\n\t\t\t\tlet index = e;\r\n\t\t\t\tthis.shippingType = parseInt(index);\r\n\t\t\t\tthis.computedPrice();\r\n\t\t\t\tif (index == 1) this.getList();\r\n\t\t\t},\r\n\t\t\tbindPickerChange: function(e) {\r\n\t\t\t\tlet value = e.detail.value;\r\n\t\t\t\tthis.shippingType = value;\r\n\t\t\t\tthis.computedPrice();\r\n\t\t\t},\r\n\t\t\tChangCouponsClone: function() {\r\n\t\t\t\tthis.$set(this.coupon, 'coupon', false);\r\n\t\t\t},\r\n\t\t\tchangeTextareaStatus: function() {\r\n\t\t\t\tfor (let i = 0, len = this.coupon.list.length; i < len; i++) {\r\n\t\t\t\t\tthis.coupon.list[i].use_title = '';\r\n\t\t\t\t\tthis.coupon.list[i].is_use = 0;\r\n\t\t\t\t}\r\n\t\t\t\tthis.textareaStatus = true;\r\n\t\t\t\tthis.status = 0;\r\n\t\t\t\tthis.$set(this.coupon, 'list', this.coupon.list);\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 处理点击优惠券后的事件\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tChangCoupons: function(e) {\r\n\t\t\t\t// this.usableCoupon = e\r\n\t\t\t\t// this.coupon.coupon = false\r\n\t\t\t\tlet index = e,\r\n\t\t\t\t\tlist = this.coupon.list,\r\n\t\t\t\t\tcouponTitle = '请选择',\r\n\t\t\t\t\tcouponId = 0;\r\n\t\t\t\tfor (let i = 0, len = list.length; i < len; i++) {\r\n\t\t\t\t\tif (i != index) {\r\n\t\t\t\t\t\tlist[i].use_title = '';\r\n\t\t\t\t\t\tlist[i].isUse = 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (list[index].isUse) {\r\n\t\t\t\t\t//不使用优惠券\r\n\t\t\t\t\tlist[index].use_title = '';\r\n\t\t\t\t\tlist[index].isUse = 0;\r\n\t\t\t\t} else {\r\n\t\t\t\t\t//使用优惠券\r\n\t\t\t\t\tlist[index].use_title = '不使用';\r\n\t\t\t\t\tlist[index].isUse = 1;\r\n\t\t\t\t\tcouponTitle = list[index].name;\r\n\t\t\t\t\tcouponId = list[index].id;\r\n\t\t\t\t}\r\n\t\t\t\tthis.couponTitle = couponTitle;\r\n\t\t\t\tthis.couponId = couponId;\r\n\t\t\t\tthis.$set(this.coupon, 'coupon', false);\r\n\t\t\t\tthis.$set(this.coupon, 'list', list);\r\n\t\t\t\tthis.computedPrice();\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 使用积分抵扣\r\n\t\t\t */\r\n\t\t\tChangeIntegral: function() {\r\n\t\t\t\tthis.useIntegral = !this.useIntegral;\r\n\t\t\t\tthis.computedPrice();\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 首次进页面展示默认地址\r\n\t\t\t */\r\n\t\t\tOnDefaultAddress: function(e) {\r\n\t\t\t\tthis.addressInfo = e;\r\n\t\t\t\tthis.address.addressId = e.id;\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 选择地址后改变事件\r\n\t\t\t * @param object e\r\n\t\t\t */\r\n\t\t\tOnChangeAddress: function(e) {\r\n\t\t\t\tthis.addressInfo = e;\r\n\t\t\t\tthis.address.addressId = e.id;\r\n\t\t\t\tthis.textareaStatus = true;\r\n\t\t\t\t//this.orderInfoVo.addressId = e;\r\n\t\t\t\tthis.address.address = false;\r\n\t\t\t\t//this.getaddressInfo();\r\n\t\t\t\tthis.computedPrice();\r\n\t\t\t},\r\n\t\t\tbindHideKeyboard: function(e) {\r\n\t\t\t\tthis.mark = e.detail.value;\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 获取当前金额可用优惠券\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tgetCouponList: function() {\r\n\t\t\t\tgetCouponsOrderPrice(this.preOrderNo).then(res => {\r\n\t\t\t\t\tthis.$set(this.coupon, 'list', res.data);\r\n\t\t\t\t\tthis.openType = 1;\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t/*\r\n\t\t\t * 获取默认收货地址或者获取某条地址信息\r\n\t\t\t */\r\n\t\t\tgetaddressInfo: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif (that.addressId) {\r\n\t\t\t\t\tgetAddressDetail(that.addressId).then(res => {\r\n\t\t\t\t\t\tif (res.data) {\r\n\t\t\t\t\t\t\tres.data.isDefault = parseInt(res.data.isDefault);\r\n\t\t\t\t\t\t\tthat.addressInfo = res.data || {};\r\n\t\t\t\t\t\t\tthat.addressId = res.data.id || 0;\r\n\t\t\t\t\t\t\tthat.address.addressId = res.data.id || 0;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tgetAddressDefault().then(res => {\r\n\t\t\t\t\t\tif (res.data) {\r\n\t\t\t\t\t\t\tres.data.isDefault = parseInt(res.data.isDefault);\r\n\t\t\t\t\t\t\tthat.addressInfo = res.data || {};\r\n\t\t\t\t\t\t\tthat.addressId = res.data.id || 0;\r\n\t\t\t\t\t\t\tthat.address.addressId = res.data.id || 0;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tpayItem: function(e) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet active = e;\r\n\t\t\t\tthat.active = active;\r\n\t\t\t\tthat.animated = true;\r\n\t\t\t\tthat.payType = that.cartArr[active].value;\r\n\t\t\t\tthat.computedPrice();\r\n\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\tthat.car();\r\n\t\t\t\t}, 500);\r\n\t\t\t},\r\n\t\t\tcouponTap: function() {\r\n\t\t\t\tthis.coupon.coupon = true;\r\n\t\t\t\tif(!this.coupon.list.length)this.getCouponList();\r\n\t\t\t},\r\n\t\t\tcar: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tthat.animated = false;\r\n\t\t\t},\r\n\t\t\tonAddress: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tthat.textareaStatus = false;\r\n\t\t\t\tthat.address.address = true;\r\n\t\t\t\tthat.pagesUrl = '/pages/users/user_address_list/index?preOrderNo='+ this.preOrderNo;\r\n\t\t\t},\r\n\t\t\trealName: function(e) {\r\n\t\t\t\tthis.contacts = e.detail.value;\r\n\t\t\t},\r\n\t\t\tphone: function(e) {\r\n\t\t\t\tthis.contactsTel = e.detail.value;\r\n\t\t\t},\r\n\t\t\tpayment: function(data) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\torderCreate(data).then(res => {\r\n\t\t\t\t\tthat.getOrderPay(res.data.orderNo, '支付成功');\r\n\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgetOrderPay: function(orderNo, message) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet goPages = '/pages/order_pay_status/index?order_id=' + orderNo + '&msg=' + message;\r\n\t\t\t\twechatOrderPay({\r\n\t\t\t\t\torderNo: orderNo,\r\n\t\t\t\t\tpayChannel: that.payChannel,\r\n\t\t\t\t\tpayType: that.payType,\r\n\t\t\t\t\tscene: that.productType==='normal'? 0 :1177 //下单时小程序的场景值\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tlet jsConfig = res.data.jsConfig;\r\n\t\t\t\t\tswitch (res.data.payType) {\r\n\t\t\t\t\t\tcase 'weixin':\r\n\t\t\t\t\t\t\t// #ifdef MP\r\n\t\t\t\t\t\t\tuni.requestPayment({\r\n\t\t\t\t\t\t\t\ttimeStamp: jsConfig.timeStamp,\r\n\t\t\t\t\t\t\t\tnonceStr: jsConfig.nonceStr,\r\n\t\t\t\t\t\t\t\tpackage: jsConfig.packages,\r\n\t\t\t\t\t\t\t\tsignType: jsConfig.signType,\r\n\t\t\t\t\t\t\t\tpaySign: jsConfig.paySign,\r\n\t\t\t\t\t\t\t\tticket: that.productType==='normal'? null : jsConfig.ticket,\r\n\t\t\t\t\t\t\t\tsuccess: function(ress) {\r\n\t\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t\twechatQueryPayResult(orderNo).then(res => {\r\n\t\t\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t\t\tif (that.orderInfoVo.bargainId || that.orderInfoVo.combinationId || that.pinkId || that\r\n\t\t\t\t\t\t\t\t\t\t\t.orderInfoVo.seckillId)\r\n\t\t\t\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: '支付成功',\r\n\t\t\t\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\t\t\t\t\ttab: 4,\r\n\t\t\t\t\t\t\t\t\t\t\t\turl: goPages\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\t\ttitle: '支付成功',\r\n\t\t\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\t\t\t\ttab: 5,\r\n\t\t\t\t\t\t\t\t\t\t\turl: goPages\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t}).cache(err => {\r\n\t\t\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tfail: function(e) {\r\n\t\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '取消支付'\r\n\t\t\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\t\t\ttab: 5,\r\n\t\t\t\t\t\t\t\t\t\turl: goPages + '&status=2'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tcomplete: function(e) {\r\n\t\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t\t//关闭当前页面跳转至订单状态\r\n\t\t\t\t\t\t\t\t\tif (e.errMsg == 'requestPayment:cancel') return that.$util\r\n\t\t\t\t\t\t\t\t\t\t.Tips({\r\n\t\t\t\t\t\t\t\t\t\t\ttitle: '取消支付'\r\n\t\t\t\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\t\t\t\ttab: 5,\r\n\t\t\t\t\t\t\t\t\t\t\turl: goPages + '&status=2'\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\t\t\tlet data = {\r\n\t\t\t\t\t\t\t\ttimestamp: jsConfig.timeStamp,\r\n\t\t\t\t\t\t\t\tnonceStr: jsConfig.nonceStr,\r\n\t\t\t\t\t\t\t\tpackage: jsConfig.packages,\r\n\t\t\t\t\t\t\t\tsignType: jsConfig.signType,\r\n\t\t\t\t\t\t\t\tpaySign: jsConfig.paySign\r\n\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t\tthat.$wechat.pay(data).then(res => {\r\n\t\t\t\t\t\t\t\tif (res.errMsg == 'chooseWXPay:cancel') {\r\n\t\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '取消支付'\r\n\t\t\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\t\t\ttab: 5,\r\n\t\t\t\t\t\t\t\t\t\turl: goPages + '&status=2'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\twechatQueryPayResult(orderNo).then(res => {\r\n\t\t\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\t\ttitle: '支付成功',\r\n\t\t\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\t\t\t\ttab: 5,\r\n\t\t\t\t\t\t\t\t\t\t\turl: goPages\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t}).cache(err => {\r\n\t\t\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}).cache(res => {\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\ttitle: '取消支付'\r\n\t\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\t\ttab: 5,\r\n\t\t\t\t\t\t\t\t\turl: goPages + '&status=0'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase 'yue':\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: message\r\n\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\ttab: 5,\r\n\t\t\t\t\t\t\t\turl: goPages + '&status=1'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\tcase 'weixinh5':\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: '订单创建成功'\r\n\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\ttab: 5,\r\n\t\t\t\t\t\t\t\turl: goPages + '&status=0'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\tlocation.href = jsConfig.mwebUrl + '&redirect_url=' + window.location\r\n\t\t\t\t\t\t\t\t\t.protocol + '//' + window.location.host +\r\n\t\t\t\t\t\t\t\t\tgoPages + '&status=1';\r\n\t\t\t\t\t\t\t}, 100)\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgetPayType: function(status, orderId, message, jsConfig) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet goPages = '/pages/order_pay_status/index?order_id=' + orderId + '&msg=' + message;\r\n\t\t\t\tswitch (status) {\r\n\t\t\t\t\tcase 'ORDER_EXIST':\r\n\t\t\t\t\tcase 'EXTEND_ORDER':\r\n\t\t\t\t\tcase 'PAY_ERROR':\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: message\r\n\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\ttab: 5,\r\n\t\t\t\t\t\t\turl: goPages\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 'SUCCESS':\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tif (that.orderInfoVo.bargainId || that.orderInfoVo.combinationId || that.pinkId || that.orderInfoVo.seckillId)\r\n\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: message,\r\n\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\ttab: 4,\r\n\t\t\t\t\t\t\t\turl: goPages\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: message,\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\ttab: 5,\r\n\t\t\t\t\t\t\turl: goPages\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 'WECHAT_PAY':\r\n\t\t\t\t\t\t// #ifdef MP\r\n\t\t\t\t\t\tthat.toPay = true;\r\n\t\t\t\t\t\tlet packagess = 'prepay_id=' + jsConfig.prepayId;\r\n\t\t\t\t\t\tuni.requestPayment({\r\n\t\t\t\t\t\t\ttimeStamp: jsConfig.timeStamp.toString(),\r\n\t\t\t\t\t\t\tnonceStr: jsConfig.nonceStr,\r\n\t\t\t\t\t\t\tpackage: packagess,\r\n\t\t\t\t\t\t\tsignType: jsConfig.signType,\r\n\t\t\t\t\t\t\tpaySign: jsConfig.paySign,\r\n\t\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\tif (that.orderInfoVo.bargainId || that.orderInfoVo.combinationId || that.pinkId || that.orderInfoVo.seckillId)\r\n\t\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '支付成功',\r\n\t\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\t\t\ttab: 4,\r\n\t\t\t\t\t\t\t\t\t\turl: goPages\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\ttitle: '支付成功',\r\n\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\t\ttab: 5,\r\n\t\t\t\t\t\t\t\t\turl: goPages\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tfail: function(e) {\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\ttitle: '取消支付'\r\n\t\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\t\ttab: 5,\r\n\t\t\t\t\t\t\t\t\turl: goPages + '&status=0'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tcomplete: function(e) {\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t//关闭当前页面跳转至订单状态\r\n\t\t\t\t\t\t\t\tif (res.errMsg == 'requestPayment:cancel') return that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\ttitle: '取消支付'\r\n\t\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\t\ttab: 5,\r\n\t\t\t\t\t\t\t\t\turl: goPages + '&status=0'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\t\tlet jsConfigAgain = jsConfig;\r\n\t\t\t\t\t\tlet packages = 'prepay_id=' + jsConfigAgain.prepayId;\r\n\t\t\t\t\t\tlet data = {\r\n\t\t\t\t\t\t\ttimestamp: jsConfigAgain.timeStamp,\r\n\t\t\t\t\t\t\tnonceStr: jsConfigAgain.nonceStr,\r\n\t\t\t\t\t\t\tpackage: packages,\r\n\t\t\t\t\t\t\tsignType: jsConfigAgain.signType,\r\n\t\t\t\t\t\t\tpaySign: jsConfigAgain.paySign,\r\n\t\t\t\t\t\t\th5PayUrl: jsConfigAgain.h5PayUrl\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\tthis.$wechat.pay(data).then(res => {\r\n\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: '支付成功',\r\n\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\ttab: 5,\r\n\t\t\t\t\t\t\t\turl: goPages\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}).cache(res => {\r\n\t\t\t\t\t\t\tif (res.errMsg == 'requestPayment:cancel') return that.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: '取消支付'\r\n\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\ttab: 5,\r\n\t\t\t\t\t\t\t\turl: goPages + '&status=0'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 'PAY_DEFICIENCY':\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: message\r\n\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\ttab: 5,\r\n\t\t\t\t\t\t\turl: goPages + '&status=1'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase \"WECHAT_H5_PAY\": //网页版公众号支付\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tlet domain = encodeURIComponent(location.href);\r\n\t\t\t\t\t\t\tlet urls = jsConfigAgain.h5PayUrl + '&redirect_url=' + domain;\r\n\t\t\t\t\t\t\tlocation.href = urls;\r\n\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: '支付成功',\r\n\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\ttab: 5,\r\n\t\t\t\t\t\t\t\turl: goPages\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}, 100);\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tSubOrder: function(e) {\r\n\r\n\t\t\t\tlet that = this,\r\n\t\t\t\t\tdata = {};\r\n\r\n\t\t\t\tif (!that.payType) return that.$util.Tips({\r\n\t\t\t\t\ttitle: '请选择支付方式'\r\n\t\t\t\t});\r\n\t\t\t\tif (!that.address.addressId && !that.shippingType) return that.$util.Tips({\r\n\t\t\t\t\ttitle: '请选择收货地址'\r\n\t\t\t\t});\r\n\t\t\t\tif (that.shippingType == 1) {\r\n\t\t\t\t\tif (that.contacts == \"\" || that.contactsTel == \"\") {\r\n\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: '请填写联系人或联系人电话'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (!/^1(3|4|5|7|8|9|6)\\d{9}$/.test(that.contactsTel)) {\r\n\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: '请填写正确的手机号'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (!/^[\\u4e00-\\u9fa5\\w]{2,16}$/.test(that.contacts)) {\r\n\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: '请填写您的真实姓名'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (that.storeList.length == 0) return that.$util.Tips({\r\n\t\t\t\t\t\ttitle: '暂无门店,请选择其他方式'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\tdata = {\r\n\t\t\t\t\trealName: that.contacts,\r\n\t\t\t\t\tphone: that.contactsTel,\r\n\t\t\t\t\taddressId: that.address.addressId,\r\n\t\t\t\t\tcouponId: that.couponId,\r\n\t\t\t\t\tpayType: that.payType,\r\n\t\t\t\t\tuseIntegral: that.useIntegral,\r\n\t\t\t\t\tpreOrderNo: that.preOrderNo,\r\n\t\t\t\t\tmark: that.mark,\r\n\t\t\t\t\ttype: that.type,\r\n\t\t\t\t\tuserBraceletId: that.userBraceletId,\r\n\t\t\t\t\tstoreId: that.system_store.id || 0,\r\n\t\t\t\t\tshippingType: that.$util.$h.Add(that.shippingType, 1),\r\n\t\t\t\t\tpayChannel: that.payChannel||'weixinh5'\r\n\r\n\t\t\t\t};\r\n\t\t\t\tif (data.payType == 'yue' && parseFloat(that.userInfo.nowMoney) < parseFloat(that.totalPrice))\r\n\t\t\t\t\treturn that.$util\r\n\t\t\t\t\t\t.Tips({\r\n\t\t\t\t\t\t\ttitle: '余额不足！'\r\n\t\t\t\t\t\t});\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '订单支付中'\r\n\t\t\t\t});\r\n\t\t\t\t// #ifdef MP\r\n\t\t\t\topenPaySubscribe().then(() => {\r\n\t\t\t\t\tthat.payment(data);\r\n\t\t\t\t});\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef MP\r\n\t\t\t\tthat.payment(data);\r\n\t\t\t\t// #endif\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.line2{\r\n\t\twidth: 504rpx;\r\n\t}\r\n\t.textR {\r\n\t\ttext-align: right;\r\n\t}\r\n\r\n\t.order-submission .line {\r\n\t\twidth: 100%;\r\n\t\theight: 3rpx;\r\n\t}\r\n\r\n\t.order-submission .line image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t.order-submission .address {\r\n\t\tpadding: 28rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.order-submission .address .addressCon {\r\n\t\twidth: 596rpx;\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.order-submission .address .addressCon .name {\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #282828;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-bottom: 10rpx;\r\n\t}\r\n\r\n\t.order-submission .address .addressCon .name .phone {\r\n\t\tmargin-left: 50rpx;\r\n\t}\r\n\r\n\t.order-submission .address .addressCon .default {\r\n\t\tmargin-right: 12rpx;\r\n\t}\r\n\r\n\t.order-submission .address .addressCon .setaddress {\r\n\t\tcolor: #333;\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n\r\n\t.order-submission .address .iconfont {\r\n\t\tfont-size: 35rpx;\r\n\t\tcolor: #707070;\r\n\t}\r\n\r\n\t.order-submission .allAddress {\r\n\t\twidth: 100%;\r\n\t\tbackground: linear-gradient(to bottom, #c9ab79 0%, #f5f5f5 100%);\r\n\t\t// background-image: linear-gradient(to bottom, #c9ab79 0%, #f5f5f5 100%);\r\n\t\t// background-image: -webkit-linear-gradient(to bottom, #c9ab79 0%, #f5f5f5 100%);\r\n\t\t// background-image: -moz-linear-gradient(to bottom, #c9ab79 0%, #f5f5f5 100%);\r\n\t\tpadding: 100rpx 30rpx 0 30rpx;\r\n\t}\r\n\r\n\t.order-submission .allAddress .nav {\r\n\t\twidth: 690rpx;\r\n\t\tmargin: 0 auto;\r\n\t}\r\n\r\n\t.order-submission .allAddress .nav .item {\r\n\t\twidth: 334rpx;\r\n\t}\r\n\r\n\t.order-submission .allAddress .nav .item.on {\r\n\t\tposition: relative;\r\n\t\twidth: 230rpx;\r\n\t}\r\n\r\n\t.order-submission .allAddress .nav .item.on::before {\r\n\t\tposition: absolute;\r\n\t\tbottom: 0;\r\n\t\tcontent: \"快递配送\";\r\n\t\tfont-size: 28rpx;\r\n\t\tdisplay: block;\r\n\t\theight: 0;\r\n\t\twidth: 336rpx;\r\n\t\tborder-width: 0 20rpx 80rpx 0;\r\n\t\tborder-style: none solid solid;\r\n\t\tborder-color: transparent transparent #fff;\r\n\t\tz-index: 2;\r\n\t\tborder-radius: 14rpx 36rpx 0 0;\r\n\t\ttext-align: center;\r\n\t\tline-height: 80rpx;\r\n\t}\r\n\r\n\t.order-submission .allAddress .nav .item:nth-of-type(2).on::before {\r\n\t\tcontent: \"到店自提\";\r\n\t\tborder-width: 0 0 80rpx 20rpx;\r\n\t\tborder-radius: 36rpx 14rpx 0 0;\r\n\t}\r\n\r\n\t.order-submission .allAddress .nav .item.on2 {\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.order-submission .allAddress .nav .item.on2::before {\r\n\t\tposition: absolute;\r\n\t\tbottom: 0;\r\n\t\tcontent: \"到店自提\";\r\n\t\tfont-size: 28rpx;\r\n\t\tdisplay: block;\r\n\t\theight: 0;\r\n\t\twidth: 401rpx;\r\n\t\tborder-width: 0 0 60rpx 60rpx;\r\n\t\tborder-style: none solid solid;\r\n\t\tborder-color: transparent transparent #f7c1bd;\r\n\t\tborder-radius: 36rpx 14rpx 0 0;\r\n\t\ttext-align: center;\r\n\t\tline-height: 60rpx;\r\n\t}\r\n\r\n\t.order-submission .allAddress .nav .item:nth-of-type(1).on2::before {\r\n\t\tcontent: \"快递配送\";\r\n\t\tborder-width: 0 60rpx 60rpx 0;\r\n\t\tborder-radius: 14rpx 36rpx 0 0;\r\n\t}\r\n\r\n\t.order-submission .allAddress .address {\r\n\t\twidth: 690rpx;\r\n\t\tmax-height: 180rpx;\r\n\t\tmargin: 0 auto;\r\n\t}\r\n\r\n\t.order-submission .allAddress .line {\r\n\t\twidth: 100%;\r\n\t\tmargin: 0 auto;\r\n\t}\r\n\r\n\t.order-submission .wrapper .item .discount .placeholder {\r\n\t\tcolor: #ccc;\r\n\t}\r\n\r\n\t.order-submission .wrapper {\r\n\t\tbackground-color: #fff;\r\n\t\tmargin-top: 15rpx;\r\n\t}\r\n\r\n\t.order-submission .wrapper .item {\r\n\t\tpadding: 27rpx 24rpx;\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #333333;\r\n\t\tborder-bottom: 1px solid #F5F5F5;\r\n\t}\r\n\r\n\t.order-submission .wrapper .item .discount {\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.order-submission .wrapper .item .discount .iconfont {\r\n\t\tcolor: #515151;\r\n\t\tfont-size: 30rpx;\r\n\t\tmargin-left: 15rpx;\r\n\t}\r\n\r\n\t.order-submission .wrapper .item .discount .num {\r\n\t\tfont-size: 32rpx;\r\n\t\tmargin-right: 20rpx;\r\n\t}\r\n\r\n\t.order-submission .wrapper .item .shipping {\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #999;\r\n\t\tposition: relative;\r\n\t\tpadding-right: 58rpx;\r\n\t}\r\n\r\n\t.order-submission .wrapper .item .shipping .iconfont {\r\n\t\tfont-size: 35rpx;\r\n\t\tcolor: #707070;\r\n\t\tposition: absolute;\r\n\t\tright: 0;\r\n\t\ttop: 50%;\r\n\t\ttransform: translateY(-50%);\r\n\t\tmargin-left: 30rpx;\r\n\t}\r\n\r\n\t.order-submission .wrapper .item textarea {\r\n\t\tbackground-color: #f9f9f9;\r\n\t\twidth: auto !important;\r\n\t\theight: 140rpx;\r\n\t\tborder-radius: 14rpx;\r\n\t\tmargin-top: 30rpx;\r\n\t\tpadding: 15rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tfont-weight: 400;\r\n\t}\r\n\r\n\t.order-submission .wrapper .item .placeholder {\r\n\t\tcolor: #ccc;\r\n\t}\r\n\r\n\t.order-submission .wrapper .item .list {\r\n\t\tmargin-top: 35rpx;\r\n\t}\r\n\r\n\t.order-submission .wrapper .item .list .payItem {\r\n\t\tborder: 1px solid #eee;\r\n\t\tborder-radius: 14rpx;\r\n\t\theight: 86rpx;\r\n\t\twidth: 100%;\r\n\t\tbox-sizing: border-box;\r\n\t\tmargin-top: 20rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #282828;\r\n\t}\r\n\r\n\t.order-submission .wrapper .item .list .payItem.on {\r\n\t\tborder-color: #fc5445;\r\n\t\tcolor: $theme-color;\r\n\t}\r\n\r\n\t.order-submission .wrapper .item .list .payItem .name {\r\n\t\twidth: 50%;\r\n\t\ttext-align: center;\r\n\t\tborder-right: 1px solid #eee;\r\n\t}\r\n\r\n\t.order-submission .wrapper .item .list .payItem .name .iconfont {\r\n\t\twidth: 44rpx;\r\n\t\theight: 44rpx;\r\n\t\tborder-radius: 50%;\r\n\t\ttext-align: center;\r\n\t\tline-height: 44rpx;\r\n\t\tbackground-color: #fe960f;\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 30rpx;\r\n\t\tmargin-right: 15rpx;\r\n\t}\r\n\r\n\t.order-submission .wrapper .item .list .payItem .name .iconfont.icon-weixin2 {\r\n\t\tbackground-color: #41b035;\r\n\t}\r\n\r\n\t.order-submission .wrapper .item .list .payItem .tip {\r\n\t\twidth: 49%;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #aaa;\r\n\t}\r\n\r\n\t.order-submission .moneyList {\r\n\t\tmargin-top: 15rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tpadding: 30rpx;\r\n\t}\r\n\r\n\t.order-submission .moneyList .item {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #282828;\r\n\t}\r\n\r\n\t.order-submission .moneyList .item~.item {\r\n\t\tmargin-top: 20rpx;\r\n\t}\r\n\r\n\t.order-submission .moneyList .item .money {\r\n\t\tcolor: #666666;\r\n\t}\r\n\r\n\t.order-submission .footer {\r\n\t\twidth: 100%;\r\n\t\theight: 100rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tpadding: 0 30rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #333;\r\n\t\tbox-sizing: border-box;\r\n\t\tposition: fixed;\r\n\t\tbottom: 0;\r\n\t\tleft: 0;\r\n\t}\r\n\r\n\t.order-submission .footer .settlement {\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #fff;\r\n\t\twidth: 240rpx;\r\n\t\theight: 70rpx;\r\n\t\tbackground-color: $theme-color;\r\n\t\tborder-radius: 50rpx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 70rpx;\r\n\t}\r\n\r\n\t.footer .transparent {\r\n\t\topacity: 0\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=064d693c&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=064d693c&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363903416\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}