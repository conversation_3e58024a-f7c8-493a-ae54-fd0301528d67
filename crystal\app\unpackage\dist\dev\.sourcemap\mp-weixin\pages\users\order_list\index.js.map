{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/order_list/index.vue?129e", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/order_list/index.vue?f868", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/order_list/index.vue?d821", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/order_list/index.vue?0617", "uni-app:///pages/users/order_list/index.vue", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/order_list/index.vue?d769", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/order_list/index.vue?1d18"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "payment", "home", "emptyPage", "authorize", "data", "loading", "loadend", "loadTitle", "orderList", "orderData", "orderStatus", "page", "limit", "payMode", "name", "icon", "value", "title", "number", "pay_close", "pay_order_id", "totalPrice", "isAuto", "isShowAuth", "computed", "onShow", "methods", "getMarbleStyle", "position", "left", "top", "width", "height", "transform", "background", "backgroundSize", "onLoadFun", "auth<PERSON><PERSON><PERSON>", "onChangeFun", "action", "payClose", "onLoad", "getOrderData", "that", "cancelOrder", "uni", "goPay", "pay_complete", "pay_fail", "goOrderDetails", "url", "statusClick", "getOrderList", "type", "delOrder", "onReachBottom"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACmM;AACnM,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1CA;AAAA;AAAA;AAAA;AAAkwB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACkItxB;AAMA;AAKA;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAOA;EACAC;IACAC;IACAC;IACAC;IAEAC;EAEA;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;QACAC;MACA,EACA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;EACAC;IACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;MACA;IACA;EACA;EACAC;IACAC;MAEA;MACA;MACA;MACA;MACA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;MACAC;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;QACAC;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;QACA3B;MACA;MACA4B;QACA5B;MACA;MACA;QACA4B;QACA;UACA5B;UACAF;QACA;UACA4B;UACAA;UACAA;UACAA;QACA;MACA;QACA;UACA1B;QACA;MACA;IACA;IACA;AACA;AACA;AACA;IACA6B;MACA;MACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;QACAhC;MACA;MAEA4B;QACA5B;MACA;MACA;QACA4B;QACAA;UACAK;QACA;MACA;QACAL;MACA;IAOA;IACA;AACA;AACA;IACAM;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA;MACA;MACAT;MACAA;MACA;QACAU;QACA1C;QACAC;MACA;QACA;QACA;QACA+B;QACAA;QACAA;QACAA;QACAA;QACAA;MACA;QACAA;QACAA;MACA;IACA;IAEA;AACA;AACA;IACAW;MACA;MACA;QACAX;QACAA;QACAA;QACAA;QACA;UACA1B;UACAF;QACA;MACA;QACA;UACAE;QACA;MACA;IACA;EACA;EACAsC;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1ZA;AAAA;AAAA;AAAA;AAAq8C,CAAgB,ovCAAG,EAAC,C;;;;;;;;;;;ACAz9C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/users/order_list/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/users/order_list/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=09744212&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=09744212&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"09744212\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/users/order_list/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=09744212&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = Number(_vm.orderData.sumPrice).toFixed(2) || 0\n  var l1 = _vm.__map(_vm.orderList, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var l0 =\n      item.type == 2\n        ? _vm.__map(item.orderInfoList, function (item1, index1) {\n            var $orig = _vm.__get_orig(item1)\n            var s0 = _vm.__get_style([_vm.getMarbleStyle(index, index1)])\n            return {\n              $orig: $orig,\n              s0: s0,\n            }\n          })\n        : null\n    return {\n      $orig: $orig,\n      l0: l0,\n    }\n  })\n  var g1 = _vm.orderList.length\n  var g2 = _vm.orderList.length\n  var g3 = _vm.orderList.length == 0 && _vm.page > 1\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l1: l1,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view class='my-order'>\r\n\t\t\t<view class='header bg-color'>\r\n\t\t\t\t<view class='picTxt acea-row row-between-wrapper'>\r\n\t\t\t\t\t<view class='text'>\r\n\t\t\t\t\t\t<view class='name'>订单信息</view>\r\n\t\t\t\t\t\t<view>消费订单：{{ orderData.orderCount || 0 }} 总消费：￥{{ Number(orderData.sumPrice).toFixed(2) || 0 }}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='pictrue'>\r\n\t\t\t\t\t\t<image src='../../../static/images/orderTime.png'></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class='nav acea-row row-around'>\r\n\t\t\t\t<view class='item' :class='orderStatus == 0 ? \"on\" : \"\"' @click=\"statusClick(0)\">\r\n\t\t\t\t\t<view>待付款</view>\r\n\t\t\t\t\t<view class='num'>{{ orderData.unPaidCount || 0 }}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='item' :class='orderStatus == 1 ? \"on\" : \"\"' @click=\"statusClick(1)\">\r\n\t\t\t\t\t<view>待发货</view>\r\n\t\t\t\t\t<view class='num'>{{ orderData.unShippedCount || 0 }}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='item' :class='orderStatus == 2 ? \"on\" : \"\"' @click=\"statusClick(2)\">\r\n\t\t\t\t\t<view>待收货</view>\r\n\t\t\t\t\t<view class='num '>{{ orderData.receivedCount || 0 }}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='item' :class='orderStatus == 3 ? \"on\" : \"\"' @click=\"statusClick(3)\">\r\n\t\t\t\t\t<view>待评价</view>\r\n\t\t\t\t\t<view class='num'>{{ orderData.evaluatedCount || 0 }}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='item' :class='orderStatus == 4 ? \"on\" : \"\"' @click=\"statusClick(4)\">\r\n\t\t\t\t\t<view>已完成</view>\r\n\t\t\t\t\t<view class='num'>{{ orderData.completeCount || 0 }}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class='list'>\r\n\t\t\t\t<view class='item' v-for=\"(item, index) in orderList\" :key=\"index\">\r\n\t\t\t\t\t<view @click='goOrderDetails(item.orderId)'>\r\n\t\t\t\t\t\t<view class='title acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t\t<view class=\"acea-row row-middle\">\r\n\t\t\t\t\t\t\t\t<text class=\"sign cart-color acea-row row-center-wrapper\"\r\n\t\t\t\t\t\t\t\t\tv-if=\"item.activityType !== '普通' && item.activityType !== '核销'\">{{ item.activityType }}</text>\r\n\t\t\t\t\t\t\t\t<!-- <text class=\"sign cart-color acea-row row-center-wrapper\" v-if=\"item.bargainId != 0\">砍价</text>\r\n\t\t\t\t\t\t\t\t<text class=\"sign cart-color acea-row row-center-wrapper\" v-else-if=\"item.storeOrder.combinationId != 0\">拼团</text>\r\n\t\t\t\t\t\t\t\t<text class=\"sign cart-color acea-row row-center-wrapper\" v-else-if=\"item.storeOrder.seckillId != 0\">秒杀</text> -->\r\n\t\t\t\t\t\t\t\t<view>{{ item.createTime }}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class='font-color'>{{ item.orderStatus }}</view>\r\n\t\t\t\t\t\t\t<!-- <view v-if=\"item.status?item.status.type == 0:0\" class='font-color'>待付款</view>\r\n\t\t\t\t\t\t\t<view v-else-if=\"item.status?item.status.type == 1:0 && item.storeOrder.shippingType==1\" class='font-color'>待发货</view>\r\n\t\t\t\t\t\t\t<view v-else-if=\"item.status?item.status.type == 2:0 && item.storeOrder.shippingType==1\" class='font-color'>待收货</view>\r\n\t\t\t\t\t\t\t<view v-else-if=\"item.status?item.status.type == 3:0 && item.storeOrder.shippingType==1\" class='font-color'>待评价</view>\r\n\t\t\t\t\t\t\t<view v-else-if=\"item.status?item.status.type == 4:0 && item.storeOrder.shippingType==1\" class='font-color'>已完成</view>\r\n\t\t\t\t\t\t\t<view v-else-if=\"item.storeOrder.shippingType==2\" class='font-color'>待核销</view> -->\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view v-if=\"item.type == 2\"  >\r\n\r\n\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t:style=\"'display: flex;justify-content: center;align-items: center;height: ' + (item.radius * 3) + 'rpx;position: relative;'\">\r\n\t\t\t\t\t\t\t<div class=\"circle-container\"\r\n\t\t\t\t\t\t\t\t:style=\"{ width: item.radius * 2 + 'rpx', height: item.radius * 2 + 'rpx' }\">\r\n\t\t\t\t\t\t\t\t<div v-for=\"(item1, index1) in item.orderInfoList\" :key=\"index1\" class=\"marble\"\r\n\t\t\t\t\t\t\t\t\t:style=\"[getMarbleStyle(index, index1)]\">\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view v-else class='item-info acea-row row-between row-top'\r\n\t\t\t\t\t\t\tv-for=\"(items, index1) in item.orderInfoList\" :key=\"index1\">\r\n\t\t\t\t\t\t\t<view class='pictrue'>\r\n\t\t\t\t\t\t\t\t<image :src='items.image'></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class='text acea-row row-between'>\r\n\t\t\t\t\t\t\t\t<view class='name line2'>{{ items.storeName }}</view>\r\n\t\t\t\t\t\t\t\t<view class='money'>\r\n\t\t\t\t\t\t\t\t\t<view>￥{{ items.price }}</view>\r\n\t\t\t\t\t\t\t\t\t<view>x{{ items.cartNum }}</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='totalPrice'>共{{ item.totalNum }}件商品，总金额\r\n\t\t\t\t\t\t\t<text class='money font-color'>￥{{ item.payPrice }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='bottom acea-row row-right row-middle'>\r\n\t\t\t\t\t\t<view class='bnt cancelBnt' v-if=\"!item.paid\" @click='cancelOrder(index, item.id)'>取消订单</view>\r\n\t\t\t\t\t\t<view class='bnt bg-color' v-if=\"!item.paid\" @click='goPay(item.payPrice, item.orderId)'>立即付款\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='bnt bg-color' v-else-if=\"item.status == 0 || item.status == 1 || item.status == 3\"\r\n\t\t\t\t\t\t\t@click='goOrderDetails(item.orderId)'>查看详情</view>\r\n\t\t\t\t\t\t<view class='bnt bg-color' v-else-if=\"item.status == 2\" @click='goOrderDetails(item.orderId)'>去评价\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='bnt cancelBnt' v-if=\"item.status == 3\" @click='delOrder(item.id, index)'>删除订单</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- <view class='bottom acea-row row-right row-middle'>\r\n\t\t\t\t\t\t<view class='bnt cancelBnt' v-if=\"item.status?item.status.type==0:0 || item.status?item.status.type == 9:0\" @click='cancelOrder(index,item.id)'>取消订单</view>\r\n\t\t\t\t\t\t<view class='bnt bg-color' v-if=\"item.status?item.status.type == 0:0\" @click='goPay(item.payPrice,item.orderId)'>立即付款</view>\r\n\t\t\t\t\t\t<view class='bnt bg-color' v-else-if=\"item.status?item.status.type == 1:0 || item.status?item.status.type == 9:0\" @click='goOrderDetails(item.orderId)'>查看详情</view>\r\n\t\t\t\t\t\t<view class='bnt bg-color' v-else-if=\"item.status?item.status.type == 2:0 && item.status.deliveryType\" @click='goOrderDetails(item.orderId)'>查看详情</view>\r\n\t\t\t\t\t\t<view class='bnt bg-color' v-else-if=\"item.status?item.status.type == 3:0\" @click='goOrderDetails(item.orderId)'>去评价</view>\r\n\t\t\t\t\t\t<view class='bnt bg-color' v-else-if=\"item.storeOrder.seckillId < 1 && item.storeOrder.bargainId < 1 && item.storeOrder.combinationId < 1 && item.status?item.status.type == 4:0\"\r\n\t\t\t\t\t\t @click='goOrderDetails(item.orderId)'>再次购买</view>\r\n\t\t\t\t\t\t<view class='bnt cancelBnt' v-if=\"item.status?item.status.type == 4:0\" @click='delOrder(item.id,index)'>删除订单</view>\r\n\t\t\t\t\t</view> -->\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class='loadingicon acea-row row-center-wrapper' v-if=\"orderList.length > 0\">\r\n\t\t\t\t<text class='loading iconfont icon-jiazai' :hidden='loading == false'></text>{{ loadTitle }}\r\n\t\t\t</view>\r\n\t\t\t<view v-if=\"orderList.length == 0\">\r\n\t\t\t\t<emptyPage title=\"暂无订单~\"></emptyPage>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class='noCart' v-if=\"orderList.length == 0 && page > 1\">\r\n\t\t\t<view class='pictrue'>\r\n\t\t\t\t<image src='/images/noOrder.png'></image>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- #ifdef MP -->\r\n\t\t<!-- <authorize @onLoadFun=\"onLoadFun\" :isAuto=\"isAuto\" :isShowAuth=\"isShowAuth\" @authColse=\"authColse\"></authorize> -->\r\n\t\t<!-- #endif -->\r\n\t\t<home></home>\r\n\t\t<payment :payMode='payMode' :pay_close=\"pay_close\" @onChangeFun='onChangeFun' :order_id=\"pay_order_id\"\r\n\t\t\t:totalPrice='totalPrice'></payment>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n\tgetOrderList,\r\n\torderData,\r\n\torderCancel,\r\n\torderDel\r\n} from '@/api/order.js';\r\nimport {\r\n\topenOrderSubscribe\r\n} from '@/utils/SubscribeMessage.js';\r\nimport home from '@/components/home';\r\nimport payment from '@/components/payment';\r\nimport {\r\n\ttoLogin\r\n} from '@/libs/login.js';\r\nimport {\r\n\tmapGetters\r\n} from \"vuex\";\r\n// #ifdef MP\r\nimport authorize from '@/components/Authorize';\r\n// #endif\r\nimport emptyPage from '@/components/emptyPage.vue'\r\nexport default {\r\n\tcomponents: {\r\n\t\tpayment,\r\n\t\thome,\r\n\t\temptyPage,\r\n\t\t// #ifdef MP\r\n\t\tauthorize\r\n\t\t// #endif\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tloading: false, //是否加载中\r\n\t\t\tloadend: false, //是否加载完毕\r\n\t\t\tloadTitle: '加载更多', //提示语\r\n\t\t\torderList: [], //订单数组\r\n\t\t\torderData: {}, //订单详细统计\r\n\t\t\torderStatus: 0, //订单状态\r\n\t\t\tpage: 1,\r\n\t\t\tlimit: 20,\r\n\t\t\tpayMode: [{\r\n\t\t\t\tname: \"微信支付\",\r\n\t\t\t\ticon: \"icon-weixinzhifu\",\r\n\t\t\t\tvalue: 'weixin',\r\n\t\t\t\ttitle: '微信快捷支付'\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"余额支付\",\r\n\t\t\t\ticon: \"icon-yuezhifu\",\r\n\t\t\t\tvalue: 'yue',\r\n\t\t\t\ttitle: '可用余额:',\r\n\t\t\t\tnumber: 0\r\n\t\t\t}\r\n\t\t\t],\r\n\t\t\tpay_close: false,\r\n\t\t\tpay_order_id: '',\r\n\t\t\ttotalPrice: '0',\r\n\t\t\tisAuto: false, //没有授权的不会自动授权\r\n\t\t\tisShowAuth: false //是否隐藏授权\r\n\t\t};\r\n\t},\r\n\tcomputed: mapGetters(['isLogin', 'userInfo']),\r\n\tonShow() {\r\n\t\tif (this.isLogin) {\r\n\t\t\tthis.loadend = false;\r\n\t\t\tthis.page = 1;\r\n\t\t\tthis.$set(this, 'orderList', []);\r\n\t\t\tthis.getOrderData();\r\n\t\t\tthis.getOrderList();\r\n\t\t\tthis.payMode[1].number = this.userInfo.nowMoney;\r\n\t\t\tthis.$set(this, 'payMode', this.payMode);\r\n\t\t} else {\r\n\t\t\ttoLogin();\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\tgetMarbleStyle(index, index1) {\r\n\r\n\t\t\tconst angle = (index1 * 2 * Math.PI) / this.orderList[index].orderInfoList.length;\r\n\t\t\tconst x = parseFloat(this.orderList[index].radius) + parseFloat(this.orderList[index].radius) * Math.cos(angle);\r\n\t\t\tconst y = parseFloat(this.orderList[index].radius) + parseFloat(this.orderList[index].radius) * Math.sin(angle);\r\n\t\t\tconst rotation = angle + Math.PI / 2; // 旋转角度，使珠子垂直于原点\r\n\t\t\treturn {\r\n\t\t\t\tposition: 'absolute',\r\n\t\t\t\tleft: (x - (parseFloat(this.orderList[index].orderInfoList[index1].width) * 3 / 2)) + 'rpx',\r\n\t\t\t\ttop: (y - (parseFloat(this.orderList[index].orderInfoList[index1].height) * 3 / 2)) + 'rpx',\r\n\t\t\t\twidth: (parseFloat(this.orderList[index].orderInfoList[index1].width) * 3) + 'rpx',\r\n\t\t\t\theight: (parseFloat(this.orderList[index].orderInfoList[index1].height) * 3) + 'rpx',\r\n\t\t\t\ttransform: `rotate(${rotation}rad)`,\r\n\t\t\t\tbackground: `url('${this.orderList[index].orderInfoList[index1].image}') no-repeat center`,\r\n\t\t\t\tbackgroundSize: `contain`\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoadFun() {\r\n\t\t\tthis.getOrderData();\r\n\t\t\tthis.getOrderList();\r\n\t\t},\r\n\t\t// 授权关闭\r\n\t\tauthColse: function (e) {\r\n\t\t\tthis.isShowAuth = e\r\n\t\t},\r\n\t\t/**\r\n\t\t * 事件回调\r\n\t\t * \r\n\t\t */\r\n\t\tonChangeFun: function (e) {\r\n\t\t\tlet opt = e;\r\n\t\t\tlet action = opt.action || null;\r\n\t\t\tlet value = opt.value != undefined ? opt.value : null;\r\n\t\t\t(action && this[action]) && this[action](value);\r\n\t\t},\r\n\t\t/**\r\n\t\t * 关闭支付组件\r\n\t\t * \r\n\t\t */\r\n\t\tpayClose: function () {\r\n\t\t\tthis.pay_close = false;\r\n\t\t},\r\n\t\t/**\r\n\t\t * 生命周期函数--监听页面加载\r\n\t\t */\r\n\t\tonLoad: function (options) {\r\n\t\t\tif (options.status) this.orderStatus = options.status;\r\n\t\t},\r\n\t\t/**\r\n\t\t * 获取订单统计数据\r\n\t\t * \r\n\t\t */\r\n\t\tgetOrderData: function () {\r\n\t\t\tlet that = this;\r\n\t\t\torderData().then(res => {\r\n\t\t\t\tthat.$set(that, 'orderData', res.data);\r\n\t\t\t})\r\n\t\t},\r\n\t\t/**\r\n\t\t * 取消订单\r\n\t\t * \r\n\t\t */\r\n\t\tcancelOrder: function (index, order_id) {\r\n\t\t\tlet that = this;\r\n\t\t\tif (!order_id) return that.$util.Tips({\r\n\t\t\t\ttitle: '缺少订单号无法取消订单'\r\n\t\t\t});\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '正在删除中'\r\n\t\t\t});\r\n\t\t\torderCancel(order_id).then(res => {\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\ttitle: '删除成功',\r\n\t\t\t\t\ticon: 'success'\r\n\t\t\t\t}, function () {\r\n\t\t\t\t\tthat.orderList.splice(index, 1);\r\n\t\t\t\t\tthat.$set(that, 'orderList', that.orderList);\r\n\t\t\t\t\tthat.$set(that.orderData, 'unpaid_count', that.orderData.unpaid_count - 1);\r\n\t\t\t\t\tthat.getOrderData();\r\n\t\t\t\t});\r\n\t\t\t}).catch(err => {\r\n\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\ttitle: err\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t},\r\n\t\t/**\r\n\t\t * 打开支付组件\r\n\t\t * \r\n\t\t */\r\n\t\tgoPay(pay_price, order_id) {\r\n\t\t\tthis.$set(this, 'pay_close', true);\r\n\t\t\tthis.$set(this, 'pay_order_id', order_id);\r\n\t\t\tthis.$set(this, 'totalPrice', pay_price);\r\n\t\t},\r\n\t\t/**\r\n\t\t * 支付成功回调\r\n\t\t * \r\n\t\t */\r\n\t\tpay_complete: function () {\r\n\t\t\tthis.loadend = false;\r\n\t\t\tthis.page = 1;\r\n\t\t\tthis.$set(this, 'orderList', []);\r\n\t\t\tthis.$set(this, 'pay_close', false);\r\n\t\t\tthis.getOrderData();\r\n\t\t\tthis.getOrderList();\r\n\t\t},\r\n\t\t/**\r\n\t\t * 支付失败回调\r\n\t\t * \r\n\t\t */\r\n\t\tpay_fail: function () {\r\n\t\t\tthis.pay_close = false;\r\n\t\t},\r\n\t\t/**\r\n\t\t * 去订单详情\r\n\t\t */\r\n\t\tgoOrderDetails: function (order_id) {\r\n\t\t\tif (!order_id) return that.$util.Tips({\r\n\t\t\t\ttitle: '缺少订单号无法查看订单详情'\r\n\t\t\t});\r\n\t\t\t// #ifdef MP\r\n\t\t\tuni.showLoading({\r\n\t\t\t\ttitle: '正在加载',\r\n\t\t\t})\r\n\t\t\topenOrderSubscribe().then(() => {\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/order_details/index?order_id=' + order_id\r\n\t\t\t\t})\r\n\t\t\t}).catch(() => {\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t})\r\n\t\t\t// #endif  \r\n\t\t\t// #ifndef MP\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: '/pages/order_details/index?order_id=' + order_id\r\n\t\t\t})\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\t/**\r\n\t\t * 切换类型\r\n\t\t */\r\n\t\tstatusClick: function (status) {\r\n\t\t\tif (status == this.orderStatus) return;\r\n\t\t\tthis.orderStatus = status;\r\n\t\t\tthis.loadend = false;\r\n\t\t\tthis.page = 1;\r\n\t\t\tthis.$set(this, 'orderList', []);\r\n\t\t\tthis.getOrderList();\r\n\t\t},\r\n\t\t/**\r\n\t\t * 获取订单列表\r\n\t\t */\r\n\t\tgetOrderList: function () {\r\n\t\t\tlet that = this;\r\n\t\t\tif (that.loadend) return;\r\n\t\t\tif (that.loading) return;\r\n\t\t\tthat.loading = true;\r\n\t\t\tthat.loadTitle = \"加载更多\";\r\n\t\t\tgetOrderList({\r\n\t\t\t\ttype: that.orderStatus,\r\n\t\t\t\tpage: that.page,\r\n\t\t\t\tlimit: that.limit,\r\n\t\t\t}).then(res => {\r\n\t\t\t\tlet list = res.data.list || [];\r\n\t\t\t\tlet loadend = list.length < that.limit;\r\n\t\t\t\tthat.orderList = that.$util.SplitArray(list, that.orderList);\r\n\t\t\t\tthat.$set(that, 'orderList', that.orderList);\r\n\t\t\t\tthat.loadend = loadend;\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tthat.loadTitle = loadend ? \"我也是有底线的\" : '加载更多';\r\n\t\t\t\tthat.page = that.page + 1;\r\n\t\t\t}).catch(err => {\r\n\t\t\t\tthat.loading = false;\r\n\t\t\t\tthat.loadTitle = \"加载更多\";\r\n\t\t\t})\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 删除订单\r\n\t\t */\r\n\t\tdelOrder: function (order_id, index) {\r\n\t\t\tlet that = this;\r\n\t\t\torderDel(order_id).then(res => {\r\n\t\t\t\tthat.orderList.splice(index, 1);\r\n\t\t\t\tthat.$set(that, 'orderList', that.orderList);\r\n\t\t\t\tthat.$set(that.orderData, 'unpaid_count', that.orderData.unpaid_count - 1);\r\n\t\t\t\tthat.getOrderData();\r\n\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\ttitle: '删除成功',\r\n\t\t\t\t\ticon: 'success'\r\n\t\t\t\t});\r\n\t\t\t}).catch(err => {\r\n\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\ttitle: err\r\n\t\t\t\t});\r\n\t\t\t})\r\n\t\t},\r\n\t},\r\n\tonReachBottom: function () {\r\n\t\tthis.getOrderList();\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.my-order .header {\r\n\theight: 250rpx;\r\n\tpadding: 0 30rpx;\r\n}\r\n\r\n.my-order .header .picTxt {\r\n\theight: 190rpx;\r\n}\r\n\r\n.my-order .header .picTxt .text {\r\n\tcolor: rgba(255, 255, 255, 0.8);\r\n\tfont-size: 26rpx;\r\n\tfont-family: 'Guildford Pro';\r\n}\r\n\r\n.my-order .header .picTxt .text .name {\r\n\tfont-size: 34rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #fff;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.my-order .header .picTxt .pictrue {\r\n\twidth: 122rpx;\r\n\theight: 109rpx;\r\n}\r\n\r\n.my-order .header .picTxt .pictrue image {\r\n\twidth: 100%;\r\n\theight: 100%;\r\n}\r\n\r\n.my-order .nav {\r\n\tbackground-color: #fff;\r\n\twidth: 690rpx;\r\n\theight: 140rpx;\r\n\tborder-radius: 14rpx;\r\n\tmargin: -60rpx auto 0 auto;\r\n}\r\n\r\n.my-order .nav .item {\r\n\ttext-align: center;\r\n\tfont-size: 26rpx;\r\n\tcolor: #282828;\r\n\tpadding: 26rpx 0;\r\n}\r\n\r\n.my-order .nav .item.on {\r\n\t// font-weight: bold;\r\n\t// border-bottom: 5rpx solid #c9ab79;\r\n\t/* #ifdef H5 || MP */\r\n\tfont-weight: bold;\r\n\t/* #endif */\r\n\tborder-bottom: 5rpx solid $theme-color;\r\n}\r\n\r\n.my-order .nav .item .num {\r\n\tmargin-top: 18rpx;\r\n}\r\n\r\n.my-order .list {\r\n\twidth: 690rpx;\r\n\tmargin: 14rpx auto 0 auto;\r\n}\r\n\r\n.my-order .list .item {\r\n\tbackground-color: #fff;\r\n\tborder-radius: 14rpx;\r\n\tmargin-bottom: 14rpx;\r\n}\r\n\r\n.my-order .list .item .title {\r\n\theight: 84rpx;\r\n\tpadding: 0 24rpx;\r\n\tborder-bottom: 1rpx solid #eee;\r\n\tfont-size: 28rpx;\r\n\tcolor: #282828;\r\n}\r\n\r\n.my-order .list .item .title .sign {\r\n\tfont-size: 24rpx;\r\n\tpadding: 0 13rpx;\r\n\theight: 36rpx;\r\n\tmargin-right: 15rpx;\r\n\tborder-radius: 18rpx;\r\n}\r\n\r\n.my-order .list .item .item-info {\r\n\tpadding: 0 24rpx;\r\n\tmargin-top: 22rpx;\r\n}\r\n\r\n.my-order .list .item .item-info .pictrue {\r\n\twidth: 120rpx;\r\n\theight: 120rpx;\r\n}\r\n\r\n.my-order .list .item .item-info .pictrue image {\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tborder-radius: 14rpx;\r\n}\r\n\r\n.my-order .list .item .item-info .text {\r\n\twidth: 500rpx;\r\n\tfont-size: 28rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n.my-order .list .item .item-info .text .name {\r\n\twidth: 350rpx;\r\n\tcolor: #282828;\r\n}\r\n\r\n.my-order .list .item .item-info .text .money {\r\n\ttext-align: right;\r\n}\r\n\r\n.my-order .list .item .totalPrice {\r\n\tfont-size: 26rpx;\r\n\tcolor: #282828;\r\n\ttext-align: right;\r\n\tmargin: 27rpx 0 0 30rpx;\r\n\tpadding: 0 30rpx 30rpx 0;\r\n\tborder-bottom: 1rpx solid #eee;\r\n}\r\n\r\n.my-order .list .item .totalPrice .money {\r\n\tfont-size: 28rpx;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.my-order .list .item .bottom {\r\n\theight: 107rpx;\r\n\tpadding: 0 30rpx;\r\n}\r\n\r\n.my-order .list .item .bottom .bnt {\r\n\twidth: 176rpx;\r\n\theight: 60rpx;\r\n\ttext-align: center;\r\n\tline-height: 60rpx;\r\n\tcolor: #fff;\r\n\tborder-radius: 50rpx;\r\n\tfont-size: 27rpx;\r\n}\r\n\r\n.my-order .list .item .bottom .bnt.cancelBnt {\r\n\tborder: 1rpx solid #ddd;\r\n\tcolor: #aaa;\r\n}\r\n\r\n.my-order .list .item .bottom .bnt~.bnt {\r\n\tmargin-left: 17rpx;\r\n}\r\n\r\n.noCart {\r\n\tmargin-top: 171rpx;\r\n\tpadding-top: 0.1rpx;\r\n}\r\n\r\n.noCart .pictrue {\r\n\twidth: 414rpx;\r\n\theight: 336rpx;\r\n\tmargin: 78rpx auto 56rpx auto;\r\n}\r\n\r\n.noCart .pictrue image {\r\n\twidth: 100%;\r\n\theight: 100%;\r\n}\r\n\r\n.circle-container {\r\n\tposition: relative;\r\n\tborder-radius: 50%;\r\n\tborder: gray 1px solid;\r\n\ttransition: all 1s;\r\n}\r\n\r\n.marble {\r\n\tposition: absolute;\r\n\ttransition: all 1s;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=09744212&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=09744212&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363903422\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}