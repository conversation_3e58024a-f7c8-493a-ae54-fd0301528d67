{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\distribution\\config\\index.vue?vue&type=template&id=596034c6&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\distribution\\config\\index.vue", "mtime": 1753666157867}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\n<div class=\"divBox\">\n  <el-card class=\"box-card\">\n    <el-form ref=\"promoterForm\" :model=\"promoterForm\" :rules=\"rules\" label-width=\"200px\" class=\"demo-promoterForm\"  v-loading=\"loading\">\n      <el-form-item prop=\"brokerageFuncStatus\">\n        <span slot=\"label\">\n          <span>分销启用：</span>\n          <el-tooltip class=\"item\" effect=\"dark\" content=\"商城分销功能开启关闭\" placement=\"top-start\">\n            <i class=\"el-icon-warning-outline\" />\n          </el-tooltip>\n        </span>\n        <el-radio-group v-model=\"promoterForm.brokerageFuncStatus\">\n          <el-radio label=\"1\">开启</el-radio>\n          <el-radio label=\"0\">关闭</el-radio>\n        </el-radio-group>\n      </el-form-item>\n      <el-form-item prop=\"storeBrokerageQuota\">\n        <span slot=\"label\">\n          <span>满额分销最低金额：</span>\n          <el-tooltip class=\"item\" effect=\"dark\" content=\"满额分销满足金额开通分销权限\" placement=\"top-start\">\n            <i class=\"el-icon-warning-outline\" />\n          </el-tooltip>\n        </span>\n        <el-input-number\n        v-model=\"promoterForm.storeBrokerageQuota\"\n        placeholder=\"满额分销满足金额开通分销权限\" :min=\"-1\"\n        :step=\"1\" class=\"selWidth\"\n        @keydown.native=\"channelInputLimit\"></el-input-number>\n      </el-form-item>\n      <!-- <el-form-item prop=\"storeBrokerageStatus\">\n        <span slot=\"label\">\n          <span>分销模式：</span>\n          <el-tooltip class=\"item\" effect=\"dark\" content=\"人人分销”默认每个人都可以分销，“指定分销”仅可后台手动设置推广员\" placement=\"top-start\">\n            <i class=\"el-icon-warning-outline\" />\n          </el-tooltip>\n        </span>\n        <el-radio-group v-model=\"promoterForm.storeBrokerageStatus\">\n          <el-radio label=\"1\">指定分销</el-radio>\n          <el-radio label=\"2\">人人分销</el-radio>\n        </el-radio-group>\n      </el-form-item> -->\n      <el-form-item prop=\"brokerageBindind\">\n        <span slot=\"label\">\n          <span>分销关系绑定：</span>\n          <el-tooltip class=\"item\" effect=\"dark\" content=\"所有用户”指所有没有上级推广人的用户，“新用户”指新注册的用户\" placement=\"top-start\">\n            <i class=\"el-icon-warning-outline\" />\n          </el-tooltip>\n        </span>\n        <el-radio-group v-model=\"promoterForm.brokerageBindind\">\n          <el-radio label=\"0\">所有用户</el-radio>\n          <el-radio label=\"1\">新用户</el-radio>\n        </el-radio-group>\n      </el-form-item>\n       <el-form-item prop=\"storeBrokerageIsBubble\">\n        <span slot=\"label\">\n          <span>分销气泡：</span>\n          <el-tooltip class=\"item\" effect=\"dark\" content=\"基础商品详情页分销气泡功能开启关闭\" placement=\"top-start\">\n            <i class=\"el-icon-warning-outline\" />\n          </el-tooltip>\n        </span>\n        <el-radio-group  v-model=\"promoterForm.storeBrokerageIsBubble\">\n          <el-radio label=\"1\">开启</el-radio>\n          <el-radio label=\"0\">关闭</el-radio>\n        </el-radio-group>\n      </el-form-item>\n      <el-form-item prop=\"storeBrokerageRatio\">\n        <span slot=\"label\">\n          <span>一级返佣比例：</span>\n          <el-tooltip class=\"item\" effect=\"dark\" content=\"订单交易成功后给上级返佣的比例0 - 100,例:5 = 反订单金额的5%\" placement=\"top-start\">\n            <i class=\"el-icon-warning-outline\" />\n          </el-tooltip>\n        </span>\n        <el-input-number v-model=\"promoterForm.storeBrokerageRatio\" step-strictly :min=\"0\" :max=\"100\" class=\"selWidth\" placeholder=\"订单交易成功后给上级返佣的比例0 - 100,例:5 = 反订单金额的5%\"></el-input-number>\n        <span>%</span>\n      </el-form-item>\n      <el-form-item prop=\"storeBrokerageTwo\">\n        <span slot=\"label\">\n          <span>二级返佣比例：</span>\n          <el-tooltip class=\"item\" effect=\"dark\" content=\"订单交易成功后给上级返佣的比例0 ~ 100,例:5 = 反订单金额的5%\" placement=\"top-start\">\n            <i class=\"el-icon-warning-outline\" />\n          </el-tooltip>\n        </span>\n        <el-input-number v-model=\"promoterForm.storeBrokerageTwo\" step-strictly :min=\"0\" :max=\"100\" class=\"selWidth\" placeholder=\"订单交易成功后给上级返佣的比例0 ~ 100,例:5 = 反订单金额的5%\"></el-input-number>\n        <span>%</span>\n      </el-form-item>\n      <el-form-item prop=\"userExtractMinPrice\">\n        <span slot=\"label\">\n          <span>提现最低金额：</span>\n          <el-tooltip class=\"item\" effect=\"dark\" content=\"用户提现最低金额\" placement=\"top-start\">\n            <i class=\"el-icon-warning-outline\" />\n          </el-tooltip>\n        </span>\n        <el-input-number v-model=\"promoterForm.userExtractMinPrice\" :min=\"0\" :step=\"1\" class=\"selWidth\" placeholder=\"用户提现最低金额\"></el-input-number>\n      </el-form-item>\n      <el-form-item prop=\"userExtractBank\">\n        <span slot=\"label\">\n          <span>提现银行卡：</span>\n          <el-tooltip class=\"item\" effect=\"dark\" content=\"提现银行卡，每个银行换行\" placement=\"top-start\">\n            <i class=\"el-icon-warning-outline\" />\n          </el-tooltip>\n        </span>\n        <el-input\n          type=\"textarea\"\n          :rows=\"4\"\n          placeholder=\"提现银行卡，每个银行换行\"\n          v-model=\"promoterForm.userExtractBank\">\n        </el-input>\n      </el-form-item>\n      <el-form-item prop=\"extractTime\">\n        <span slot=\"label\">\n          <span>冻结时间：</span>\n          <el-tooltip class=\"item\" effect=\"dark\" content=\"佣金冻结时间(天)\" placement=\"top-start\">\n            <i class=\"el-icon-warning-outline\" />\n          </el-tooltip>\n        </span>\n        <el-input-number v-model=\"promoterForm.extractTime\" :min=\"0\" class=\"selWidth\" placeholder=\"佣金冻结时间(天)\"></el-input-number>\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" :loading=\"loading\" @click=\"submitForm('promoterForm')\" v-hasPermi=\"['admin:retail:spread:manage:set']\">提交</el-button>\n      </el-form-item>\n    </el-form>\n  </el-card>\n</div>\n", null]}