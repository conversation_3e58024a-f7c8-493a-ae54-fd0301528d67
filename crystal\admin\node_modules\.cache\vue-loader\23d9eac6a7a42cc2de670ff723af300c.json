{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\FormGenerator\\index\\FormDrawer.vue", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\FormGenerator\\index\\FormDrawer.vue", "mtime": 1753666157769}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\eslint-loader\\index.js", "mtime": 1753666298172}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./FormDrawer.vue?vue&type=template&id=14fdd522&scoped=true\"\nimport script from \"./FormDrawer.vue?vue&type=script&lang=js\"\nexport * from \"./FormDrawer.vue?vue&type=script&lang=js\"\nimport style0 from \"./FormDrawer.vue?vue&type=style&index=0&id=14fdd522&lang=scss&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"14fdd522\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\crystal-mall\\\\crystal\\\\admin\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('14fdd522')) {\n      api.createRecord('14fdd522', component.options)\n    } else {\n      api.reload('14fdd522', component.options)\n    }\n    module.hot.accept(\"./FormDrawer.vue?vue&type=template&id=14fdd522&scoped=true\", function () {\n      api.rerender('14fdd522', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/components/FormGenerator/index/FormDrawer.vue\"\nexport default component.exports"]}