(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/promotionGood/index"],{"7aae":function(t,n,e){"use strict";e.r(n);var u=e("fe52"),i=e("d38d");for(var o in i)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(o);e("f07e");var a=e("828b"),c=Object(a["a"])(i["default"],u["b"],u["c"],!1,null,"1d785c8a",null,!1,u["a"],void 0);n["default"]=c.exports},b102:function(t,n,e){},d38d:function(t,n,e){"use strict";e.r(n);var u=e("dc5b"),i=e.n(u);for(var o in u)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return u[t]}))}(o);n["default"]=i.a},dc5b:function(t,n,e){"use strict";(function(t){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var u=e("8f59"),i=e("a014"),o={computed:(0,u.mapGetters)(["uid"]),props:{benefit:{type:Array,default:function(){return[]}}},data:function(){return{}},methods:{goDetail:function(n){(0,i.goShopDetail)(n,this.uid).then((function(e){t.navigateTo({url:"/pages/goods_details/index?id=".concat(n.id)})}))}}};n.default=o}).call(this,e("df3c")["default"])},f07e:function(t,n,e){"use strict";var u=e("b102"),i=e.n(u);i.a},fe52:function(t,n,e){"use strict";e.d(n,"b",(function(){return u})),e.d(n,"c",(function(){return i})),e.d(n,"a",(function(){}));var u=function(){var t=this.$createElement;this._self._c},i=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/promotionGood/index-create-component',
    {
        'components/promotionGood/index-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("7aae"))
        })
    },
    [['components/promotionGood/index-create-component']]
]);
