<view class="data-v-286e5497"><view class="{{['product-window','data-v-286e5497',(attr.cartAttr===true?'on':'')+' '+(iSbnt?'join':'')+' '+(iScart?'joinCart':'')]}}"><view class="textpic acea-row row-between-wrapper data-v-286e5497"><view class="pictrue data-v-286e5497"><image src="{{attr.productSelect.image}}" class="data-v-286e5497"></image></view><view class="text data-v-286e5497"><view class="line1 data-v-286e5497">{{''+attr.productSelect.storeName+''}}</view><view class="money font-color data-v-286e5497">￥<text class="num data-v-286e5497">{{attr.productSelect.price}}</text><block wx:if="{{isShow}}"><text class="stock data-v-286e5497">{{"库存: "+attr.productSelect.stock}}</text></block><block wx:if="{{limitNum}}"><text class="stock data-v-286e5497">{{"限量: "+attr.productSelect.quota}}</text></block></view></view><view data-event-opts="{{[['tap',[['closeAttr',['$event']]]]]}}" class="iconfont icon-guanbi data-v-286e5497" bindtap="__e"></view></view><view class="rollTop data-v-286e5497"><view class="productWinList data-v-286e5497"><block wx:for="{{attr.productAttr}}" wx:for-item="item" wx:for-index="indexw" wx:key="indexw"><view class="item data-v-286e5497"><view class="title data-v-286e5497">{{item.attrName}}</view><view class="listn acea-row row-middle data-v-286e5497"><block wx:for="{{item.attrValues}}" wx:for-item="itemn" wx:for-index="indexn" wx:key="indexn"><view data-event-opts="{{[['tap',[['tapAttr',[indexw,indexn]]]]]}}" class="{{['itemn','data-v-286e5497',item.index===itemn?'on':'']}}" bindtap="__e">{{''+itemn+''}}</view></block></view></view></block></view><view class="cart acea-row row-between-wrapper data-v-286e5497"><view class="title data-v-286e5497">数量</view><view class="carnum acea-row row-left data-v-286e5497"><view data-event-opts="{{[['tap',[['CartNumDes',['$event']]]]]}}" class="{{['item','reduce','data-v-286e5497',attr.productSelect.cart_num<=1?'on':'']}}" bindtap="__e">-</view><view class="item num data-v-286e5497"><input type="number" data-name="productSelect.cart_num" data-event-opts="{{[['input',[['__set_model',['$0','cart_num','$event',[]],['attr.productSelect']],['bindCode',['$0'],['attr.productSelect.cart_num']]]]]}}" value="{{attr.productSelect.cart_num}}" bindinput="__e" class="data-v-286e5497"/></view><block wx:if="{{iSplus}}"><view data-event-opts="{{[['tap',[['CartNumAdd',['$event']]]]]}}" class="{{['item','plus','data-v-286e5497',attr.productSelect.cart_num>=attr.productSelect.stock?'on':'']}}" bindtap="__e">+</view></block><block wx:else><view data-event-opts="{{[['tap',[['CartNumAdd',['$event']]]]]}}" class="{{['item','plus','data-v-286e5497',attr.productSelect.cart_num>=attr.productSelect.quota||attr.productSelect.cart_num>=attr.productSelect.stock||attr.productSelect.cart_num>=attr.productSelect.num?'on':'']}}" bindtap="__e">+</view></block></view></view></view><block wx:if="{{iSbnt&&attr.productSelect.stock>0&&attr.productSelect.quota>0}}"><view data-event-opts="{{[['tap',[['goCat',['$event']]]]]}}" class="joinBnt bg-color data-v-286e5497" bindtap="__e">我要参团</view></block><block wx:else><block wx:if="{{iSbnt&&attr.productSelect.quota<=0||iSbnt&&attr.productSelect.stock<=0}}"><view class="joinBnt on data-v-286e5497">已售罄</view></block></block><block wx:if="{{iScart&&attr.productSelect.stock}}"><view data-event-opts="{{[['tap',[['goCat',['$event']]]]]}}" class="joinBnt bg-color data-v-286e5497" bindtap="__e">确定</view></block><block wx:else><block wx:if="{{iScart&&!attr.productSelect.stock}}"><view class="joinBnt on data-v-286e5497">已售罄</view></block></block></view><view class="mask data-v-286e5497" hidden="{{attr.cartAttr===false}}" data-event-opts="{{[['touchmove',[['',['$event']]]],['tap',[['closeAttr',['$event']]]]]}}" bindtouchmove="__e" bindtap="__e"></view></view>