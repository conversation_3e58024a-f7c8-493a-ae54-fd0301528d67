{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\grade\\creatGrade.vue?vue&type=template&id=ff8c45c0&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\grade\\creatGrade.vue", "mtime": 1753666157938}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _vm.dialogVisible\n    ? _c(\n        \"el-dialog\",\n        {\n          attrs: {\n            title: \"用户等级\",\n            visible: _vm.dialogVisible,\n            width: \"500px\",\n            \"before-close\": _vm.handleClose,\n          },\n          on: {\n            \"update:visible\": function ($event) {\n              _vm.dialogVisible = $event\n            },\n          },\n        },\n        [\n          _c(\n            \"el-form\",\n            {\n              directives: [\n                {\n                  name: \"loading\",\n                  rawName: \"v-loading\",\n                  value: _vm.loading,\n                  expression: \"loading\",\n                },\n              ],\n              ref: \"user\",\n              staticClass: \"demo-ruleForm\",\n              attrs: {\n                model: _vm.user,\n                rules: _vm.rules,\n                \"label-width\": \"100px\",\n              },\n            },\n            [\n              _c(\n                \"el-form-item\",\n                { attrs: { label: \"等级名称\", prop: \"name\" } },\n                [\n                  _c(\"el-input\", {\n                    attrs: { placeholder: \"请输入等级名称\" },\n                    model: {\n                      value: _vm.user.name,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.user, \"name\", $$v)\n                      },\n                      expression: \"user.name\",\n                    },\n                  }),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                { attrs: { label: \"等级\", prop: \"grade\" } },\n                [\n                  _c(\"el-input\", {\n                    attrs: { placeholder: \"请输入等级\" },\n                    model: {\n                      value: _vm.user.grade,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.user, \"grade\", _vm._n($$v))\n                      },\n                      expression: \"user.grade\",\n                    },\n                  }),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                { attrs: { label: \"享受折扣(%)\", prop: \"discount\" } },\n                [\n                  _c(\"el-input-number\", {\n                    attrs: {\n                      min: 0,\n                      max: 100,\n                      \"step-strictly\": \"\",\n                      placeholder: \"请输入享受折扣\",\n                    },\n                    model: {\n                      value: _vm.user.discount,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.user, \"discount\", $$v)\n                      },\n                      expression: \"user.discount\",\n                    },\n                  }),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                { attrs: { label: \"经验\", prop: \"experience\" } },\n                [\n                  _c(\"el-input-number\", {\n                    attrs: {\n                      placeholder: \"请输入经验\",\n                      min: 0,\n                      \"step-strictly\": \"\",\n                    },\n                    model: {\n                      value: _vm.user.experience,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.user, \"experience\", _vm._n($$v))\n                      },\n                      expression: \"user.experience\",\n                    },\n                  }),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\"el-form-item\", { attrs: { label: \"图标\", prop: \"icon\" } }, [\n                _c(\n                  \"div\",\n                  {\n                    staticClass: \"upLoadPicBox\",\n                    on: {\n                      click: function ($event) {\n                        return _vm.modalPicTap(\"1\", \"icon\")\n                      },\n                    },\n                  },\n                  [\n                    _vm.user.icon\n                      ? _c(\"div\", { staticClass: \"pictrue\" }, [\n                          _c(\"img\", { attrs: { src: _vm.user.icon } }),\n                        ])\n                      : _vm.formValidate.icon\n                      ? _c(\"div\", { staticClass: \"pictrue\" }, [\n                          _c(\"img\", { attrs: { src: _vm.formValidate.icon } }),\n                        ])\n                      : _c(\"div\", { staticClass: \"upLoad\" }, [\n                          _c(\"i\", {\n                            staticClass: \"el-icon-camera cameraIconfont\",\n                          }),\n                        ]),\n                  ]\n                ),\n              ]),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"span\",\n            {\n              staticClass: \"dialog-footer\",\n              attrs: { slot: \"footer\" },\n              slot: \"footer\",\n            },\n            [\n              _c(\n                \"el-button\",\n                {\n                  on: {\n                    click: function ($event) {\n                      return _vm.resetForm(\"user\")\n                    },\n                  },\n                },\n                [_vm._v(\"取 消\")]\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-button\",\n                {\n                  directives: [\n                    {\n                      name: \"hasPermi\",\n                      rawName: \"v-hasPermi\",\n                      value: [\n                        \"admin:system:user:level:update\",\n                        \"admin:system:user:level:save\",\n                      ],\n                      expression:\n                        \"['admin:system:user:level:update','admin:system:user:level:save']\",\n                    },\n                  ],\n                  attrs: { type: \"primary\" },\n                  on: {\n                    click: function ($event) {\n                      return _vm.submitForm(\"formValidate\")\n                    },\n                  },\n                },\n                [_vm._v(\"确 定\")]\n              ),\n            ],\n            1\n          ),\n        ],\n        1\n      )\n    : _vm._e()\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}