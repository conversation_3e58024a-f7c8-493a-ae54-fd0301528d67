{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/goods_seckill_details/index.vue?61ae", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/goods_seckill_details/index.vue?2cec", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/goods_seckill_details/index.vue?55d1", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/goods_seckill_details/index.vue?76a2", "uni-app:///pages/activity/goods_seckill_details/index.vue", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/goods_seckill_details/index.vue?eb8e", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/goods_seckill_details/index.vue?7f69"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "bgColor", "dataShow", "id", "time", "countDownHour", "countDownMinute", "countDownSecond", "storeInfo", "imgUrls", "parameter", "attribute", "cartAttr", "productAttr", "productSelect", "productValue", "isOpen", "attr", "attrValue", "status", "isAuto", "isShowAuth", "iShidden", "limitNum", "personNum", "iSplus", "replyCount", "reply", "replyC<PERSON>ce", "navH", "navList", "opacity", "scrollY", "topArr", "to<PERSON>ie<PERSON>", "height", "heightArr", "lock", "scrollTop", "tagStyle", "img", "table", "video", "datatime", "navActive", "meun<PERSON><PERSON>ght", "backH", "posters", "weixin<PERSON>tatus", "posterImageStatus", "canvasStatus", "storeImage", "PromotionCode", "posterImage", "posterbackgd", "actionSheetHidden", "cart_num", "attrTxt", "qrcodeSize", "imagePath", "imgTop", "H5ShareBox", "sharePacket", "isState", "buyNum", "errT", "returnShow", "homeTop", "userCollect", "components", "shareRedPackets", "productConSwiper", "userEvaluation", "home", "countDown", "authorize", "computed", "watch", "is<PERSON>ogin", "handler", "deep", "onLoad", "that", "uni", "success", "statusBarHeight", "setTimeout", "app", "title", "url", "query", "select", "boundingClientRect", "exec", "methods", "kefuClick", "location", "closePosters", "getProductReplyList", "page", "limit", "type", "getProductReplyCount", "iptCartNum", "returns", "onLoadFun", "getSeckillDetail", "attrName", "attrV<PERSON>ues", "isDel", "productId", "tab", "setShare", "desc", "link", "imgUrl", "console", "DefaultSelect", "value", "self", "selecAttr", "onMyEvent", "ChangeCartNum", "num", "arrMin", "attrVal", "ChangeAttr", "scroll", "tap", "infoScroll", "setCollect", "openAlone", "goCat", "listenerActionSheet", "listenerActionClose", "posterImageClose", "setDomain", "downloadFilestoreImage", "fail", "downloadFilePromotionCode", "stop_time", "successFn", "getImageBase64", "goFriend", "go<PERSON><PERSON><PERSON>", "mask", "arrImagesUrlTop", "getQrcode", "pid", "path", "make", "uQRCode", "canvasId", "text", "size", "margin", "complete", "savePosterPath", "scope", "filePath", "icon", "setShareInfoStatus", "href", "onShareAppMessage", "imageUrl"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACmM;AACnM,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,0PAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9DA;AAAA;AAAA;AAAA;AAAkwB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACsKtxB;AACA;AAGA;AAIA;AAWA;AACA;AAQA;AAGA;AAGA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AApCA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAqCA;EACAC;IACA;MACAC;QACA;QACA;QACA;QACA;QACA;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACA;QACA;QACA;QACA;MACA;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;QACAC;MACA;;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;IACAC;IACA;IACAC;IACA;IACAC;IACAC;IAEAC;EAEA;EACAC;EACAC;IACAC;MACAC;QACA;UACA;QACA;MACA;MACAC;IACA;EACA;EACAC;IAAA;IACA;IACAC;IACA;IACA;IACA;IACAC;MACAC;QACAF;QACAG;QACA;MACA;IACA;;IAEA;IAMA;IACA;IACA;;IAGA;IACA;IACAC;MACA;QACAC;QACA;MACA;IACA;IAEAL;;IAEA;MACA;MACA;QACAM;MACA;QACAC;MACA;MACA;IACA;IACA;MACA;QAAA;QACA;QACA;QACAF;QACA;QACAD;UACA;QACA;MACA;QACA;MACA;IACA;IAEA;MACA;IACA;MACA,kCACA;MACA;IACA;IACA;MAEA;MACA;MACAI,MACAC,gBACAC;QACA;MACA,GACAC;IAEA;IACA;EACA;EACAC;IACAC;MACAC;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;QACAC;QACAC;QACAC;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;QACApB;QACAA;MACA;IACA;IACA;AACA;AACA;AACA;IACAqB;MACA;MACA;MACA;QACA;UACAf;QACA;MACA;IACA;IACA;IACAgB;MACArB;IACA;IACAsB;MACA;QACA;QACA;QACA;MACA;IACA;IACAC;MAAA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAEA;QACA;QACA;UACA;YACAC;YACAC;YACAzG;YACA0G;YACAC;YACAT;UACA;QACA;QACA;QAOAnB;QACAA;QAGAA;QACA;;QAEAA;QACAI;UACAJ;QACA;QACAK;MAEA;QACAL;UACAM;QACA;UACAuB;QACA;MACA;IACA;IACAC;MACA,2BACA,0BACA,6BACA,2BACA,yBACA,sBACA;QACAC;QACAzB;QACA0B;QACAC;MACA,wBACA;QACAC;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;MACA;QACA;UACAC;UACA;QACA;MACA;MACA;QACA;MACA;MACA;MACA;MACA;QACAC,UACAA,8BACA,aACAA,yBACA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;QACAA;MACA;QACAA,UACAA,8BACA,aACAA,yBACA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;MACA;QACAA,UACAA,8BACA,aACAA,yBACA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA,UACAA,8BACA,aACA;QACAA;QACAA;QACAA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;MACA;QACA5G;QACA;MACA;MACA;MACA,uEACAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;UACA;YACA0E;UACA;QACA;QACAmC;QACA;QACAC;QACAA;QACAA;QACA;QACA;UACA;UACA;QACA;QACA;QACA;MAEA;QACAD;QACA;UACA;UACA;QACA;QACA;QACA;MACA;IACA;IACAE;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MAEA;IACA;IACAC;MACA;QACA/F;MACA;MACAD;MACAmD;MACAA;MACA;QACAA;QACA;MACA;MACA;QACA;UACAA;UACA;QACA;MACA;IACA;IACA8C;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;QACAhG;QACAG;MACA;QAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACAsD;QACAA;UACA;UACA;UACAzD;UACAG;UACA8C;UACAA;QACA;MACA;MAAA;IACA;IACA;AACA;AACA;IACAgD;MACA;MACA;QACA;UACAhD;QACA;MACA;QACA;UACAA;QACA;MACA;IACA;IACA;AACA;AACA;IACAiD;MACAhD;QACAM;MACA;IACA;IACA;AACA;AACA;IACA2C;MACA;MACA;MACA;MACA;QACA;UACA5C;QACA;MACA;MACA;MACA,iBACA,oCAEA;MACA;MACA;MACA;MACA;QACAA;MACA;MAEA;QACA;QACA;QACA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;IACA6C;MACA;QACA;MACA;QAOA;MAEA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA/C;MACA;MACA,kDACA;IACA;IACA;IACAgD;MACA;MACAtD;QACAM;QACAL;UACAF;QACA;QACAwD;UACA;YACAlD;UACA;UACAN;QACA;MACA;IACA;IACA;AACA;AACA;AACA;AACA;IACAyD;MACA;MACA;QAAAC;MAAA;QACAzD;UACAM;UACAL;YACAF;YACA,oCACA2D,8CAEA3D;UACA;UACAwD;YACAxD;YACAA;UACA;QACA;MACA;QACAA;QACAA;MACA;IACA;IACA4D;MACA;MACA;QAAArD;MAAA;QACAP;MACA;IACA;IACA;IACA6D;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA7D;QACAK;QACAyD;MACA;MACA/D;MACA;MACA;MACA;QACAC;QACAD;UACAM;QACA;QACA;MACA;MACAF;QACA;UACAH;UACAD;YACAM;UACA;UACA;QACA;MACA;MACAL;QACAM;QAAA;QACAL;UACA8D;UACA;UACA;UACA;UACA5D;YACAJ;cACAA;cACAA;cACAC;YACA;UACA;QACA;MACA;IACA;IACA;IACAgE;MACA;MACA;QACAC;QACAjJ;QACAkJ;MACA;MACA;QACA;UACAnE;QACA;MACA;QACAA;MACA;IACA;IACA;IACAoE;MAAA;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAvE;UACA;QACA;QACAwE,kCACA;QACAlB;UACA;YACAlD;UACA;QACA;MACA;IACA;IAEA;AACA;AACA;;IAEAqE;MACA;MACA1E;QACAC;UACA;YACAD;cACA2E;cACA1E;gBACAD;kBACA4E;kBACA3E;oBACAF;oBACAA;sBACAM;sBACAwE;oBACA;kBACA;kBACAtB;oBACAxD;sBACAM;oBACA;kBACA;gBACA;cACA;YACA;UACA;YACAL;cACA4E;cACA3E;gBACAF;gBACAA;kBACAM;kBACAwE;gBACA;cACA;cACAtB;gBACAxD;kBACAM;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IAEAyE;MACA;MACA;MACA;QACAC,OACAA,2BACAA,+BACAA;QAEA;UACAjD;UACAzB;UACA0B;UACAC;QACA;QACA;MACA;IACA;EACA;EAEAgD;IACA;IACA;MACA3E;MACA6D;MACAe;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;ACjgCA;AAAA;AAAA;AAAA;AAAq8C,CAAgB,ovCAAG,EAAC,C;;;;;;;;;;;ACAz9C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/activity/goods_seckill_details/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/activity/goods_seckill_details/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=1620faf6&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=1620faf6&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1620faf6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/activity/goods_seckill_details/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=1620faf6&scoped=true&\"", "var components\ntry {\n  components = {\n    jyfParser: function () {\n      return import(\n        /* webpackChunkName: \"components/jyf-parser/jyf-parser\" */ \"@/components/jyf-parser/jyf-parser.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 =\n    parseFloat(_vm.storeInfo.sales) + parseFloat(_vm.storeInfo.ficti) || 0\n  var g0 = _vm.attribute.productAttr.length\n  var g1 =\n    _vm.status == 2 &&\n    _vm.attribute.productSelect.quota > 0 &&\n    _vm.datatime > new Date().getTime() / 1000\n  var g2 =\n    _vm.status == 2 &&\n    _vm.attribute.productSelect.quota <= 0 &&\n    _vm.datatime > new Date().getTime() / 1000\n  var g3 = _vm.status == 2 && new Date().getTime() / 1000 - _vm.datatime >= 0\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.H5ShareBox = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<!-- 头部 -->\r\n\t\t<view class='navbar' :style=\"{height:navH+'rpx',opacity:opacity}\">\r\n\t\t\t<view class='navbarH' :style='\"height:\"+navH+\"rpx;\"'>\r\n\t\t\t\t<view class='navbarCon acea-row row-center-wrapper'>\r\n\t\t\t\t\t<view class=\"header acea-row row-center-wrapper\">\r\n\t\t\t\t\t\t<view class=\"item\" :class=\"navActive === index ? 'on' : ''\" v-for=\"(item,index) in navList\" :key='index' @tap=\"tap(item,index)\">\r\n\t\t\t\t\t\t\t{{ item }}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view id=\"home\" class=\"home-nav acea-row row-center-wrapper iconfont icon-xiangzuo\" :class=\"opacity>0.5?'on':''\" :style=\"{ top: homeTop + 'rpx' }\" v-if=\"returnShow\" @tap=\"returns\">\r\n\t\t</view>\r\n\t\t<view class='product-con'>\r\n\t\t\t<scroll-view :scroll-top=\"scrollTop\" scroll-y='true' scroll-with-animation=\"true\" :style=\"'height:'+height+'px;'\"\r\n\t\t\t @scroll=\"scroll\">\r\n\t\t\t\t<view id=\"past0\">\r\n\t\t\t\t\t<productConSwiper :imgUrls='imgUrls'></productConSwiper>\r\n\t\t\t\t\t<view class='nav acea-row row-between-wrapper mb30'>\r\n\t\t\t\t\t\t<view class='money'>￥<text class='num'>{{storeInfo.price}}</text><text class='y-money'>￥{{storeInfo.otPrice}}</text></view>\r\n\t\t\t\t\t\t<view class='acea-row row-middle'>\r\n\t\t\t\t\t\t\t<view class='time' v-if=\"status == 2\">\r\n\t\t\t\t\t\t\t\t<view>距秒杀结束仅剩</view>\r\n\t\t\t\t\t\t\t\t<countDown :bgColor=\"bgColor\" :is-day=\"false\" :tip-text=\"' '\" :day-text=\"' '\" :hour-text=\"' : '\" :minute-text=\"' : '\" :second-text=\"' '\"\r\n\t\t\t\t\t\t\t\t :datatime=\"datatime\"></countDown>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"pad30 mb30\">\r\n\t\t\t\t\t\t<view class='wrapper borRadius14 mb30'>\r\n\t\t\t\t\t\t\t<view class='introduce acea-row row-between'>\r\n\t\t\t\t\t\t\t\t<view class='infor'> {{storeInfo.storeName}}</view>\r\n\t\t\t\t\t\t\t\t<view class='iconfont icon-fenxiang' @click=\"listenerActionSheet\"></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class='label acea-row row-middle'>\r\n\t\t\t\t\t\t\t\t<view class='stock'>累计销售：{{parseFloat(storeInfo.sales) + parseFloat(storeInfo.ficti) || 0}}{{storeInfo.unitName}}</view>\r\n\t\t\t\t\t\t\t\t<view>限量: {{ storeInfo.quota ? storeInfo.quota : 0 }} {{storeInfo.unitName}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='attribute acea-row row-between-wrapper mb30 borRadius14' @tap='selecAttr' v-if='attribute.productAttr.length'>\r\n\t\t\t\t\t\t\t<view class=\"line1\">{{attr}}：<text class='atterTxt'>{{attrValue}}</text></view>\r\n\t\t\t\t\t\t\t<view class='iconfont icon-jiantou'></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='userEvaluation' id=\"past1\">\r\n\t\t\t\t\t\t\t<view class='title acea-row row-between-wrapper' :style=\"replyCount==0?'border-bottom-left-radius:14rpx;border-bottom-right-radius:14rpx;':''\">\r\n\t\t\t\t\t\t\t\t<view>用户评价({{replyCount}})</view>\r\n\t\t\t\t\t\t\t\t<navigator class='praise' hover-class='none' :url=\"'/pages/users/goods_comment_list/index?productId='+ storeInfo.productId\">\r\n\t\t\t\t\t\t\t\t\t<text class='font-color'>{{replyChance}}%</text>好评率\r\n\t\t\t\t\t\t\t\t\t<text class='iconfont icon-jiantou'></text>\r\n\t\t\t\t\t\t\t\t</navigator>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<userEvaluation :reply=\"reply\"></userEvaluation>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='product-intro' id=\"past2\">\r\n\t\t\t\t\t<view class='title'>\r\n\t\t\t\t\t\t<image src=\"../../../static/images/xzuo.png\"></image>\r\n\t\t\t\t\t\t<span class=\"sp\">产品详情</span>\r\n\t\t\t\t\t\t<image src=\"../../../static/images/xyou.png\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='conter'>\r\n\t\t\t\t\t\t<jyf-parser :html=\"storeInfo.content\" ref=\"article\" :tag-style=\"tagStyle\"></jyf-parser>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view style='height:120rpx;'></view>\r\n\t\t\t</scroll-view>\r\n\t\t\t<view class='footer acea-row row-between-wrapper'>\r\n\t\t\t\t<!-- #ifdef MP -->\r\n\t\t\t\t<button open-type=\"contact\" hover-class='none' class='item'>\r\n\t\t\t\t\t<view class='iconfont icon-kefu'></view>\r\n\t\t\t\t\t<view>客服</view>\r\n\t\t\t\t</button>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t<!-- #ifndef MP -->\r\n\t\t\t\t<navigator hover-class=\"none\" class=\"item\" @click=\"kefuClick\">\r\n\t\t\t\t\t<view class=\"iconfont icon-kefu\"></view>\r\n\t\t\t\t\t<view>客服</view>\r\n\t\t\t\t</navigator>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t<view @tap='setCollect' class='item'>\r\n\t\t\t\t\t<view class='iconfont icon-shoucang1' v-if=\"userCollect\"></view>\r\n\t\t\t\t\t<view class='iconfont icon-shoucang' v-else></view>\r\n\t\t\t\t\t<view>收藏</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"bnt acea-row\" v-if=\"dataShow == 0\">\r\n\t\t\t\t\t<view class=\"joinCart bnts\" @tap=\"openAlone\">单独购买</view>\r\n\t\t\t\t\t<view class=\"buy bnts bg-color-hui\">立即购买</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"bnt acea-row\" v-if=\"status == 2 && attribute.productSelect.quota>0 &&  datatime > new Date().getTime()/1000\">\r\n\t\t\t\t\t<view class=\"joinCart bnts\" @tap=\"openAlone\">单独购买</view>\r\n\t\t\t\t\t<view class=\"buy bnts\" @tap=\"goCat\">立即购买</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"bnt acea-row\" v-if=\"status == 2 && (attribute.productSelect.quota <= 0) &&  datatime > new Date().getTime()/1000\">\r\n\t\t\t\t\t<view class=\"joinCart bnts\" @tap=\"openAlone\">单独购买</view>\r\n\t\t\t\t\t<view class=\"buy bnts bg-color-hui\">已售罄</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"bnt acea-row\" v-if=\"status == 0\">\r\n\t\t\t\t\t<view class=\"joinCart bnts\" @tap=\"openAlone\">单独购买</view>\r\n\t\t\t\t\t<view class=\"buy bnts bg-color-hui\">已关闭</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"bnt acea-row\" v-if=\"status == 1\">\r\n\t\t\t\t\t<view class=\"joinCart bnts\" @tap=\"openAlone\">单独购买</view>\r\n\t\t\t\t\t<view class=\"buy bnts bg-color-hui\">未开始</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"bnt acea-row\" v-if=\"status == 2 && new Date().getTime()/1000 - datatime >=0\">\r\n\t\t\t\t\t<view class=\"joinCart bnts\" @tap=\"openAlone\">单独购买</view>\r\n\t\t\t\t\t<view class=\"buy bnts bg-color-hui\">已结束</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<product-window :attr='attribute' :limitNum='1' @myevent=\"onMyEvent\" @ChangeAttr=\"ChangeAttr\" @ChangeCartNum=\"ChangeCartNum\"\r\n\t\t @attrVal=\"attrVal\" @iptCartNum=\"iptCartNum\"></product-window>\r\n\t\t<!-- #ifdef MP -->\r\n\t\t<!-- <authorize @onLoadFun=\"onLoadFun\" :isAuto=\"isAuto\" :isShowAuth=\"isShowAuth\"></authorize> -->\r\n\t\t<!-- #endif -->\r\n\t\t<home></home>\r\n\t\t<!-- 分享按钮 -->\r\n\t\t<view class=\"generate-posters acea-row row-middle\" :class=\"posters ? 'on' : ''\">\r\n\t\t\t<!-- #ifndef MP -->\r\n\t\t\t<button class=\"item\" hover-class='none' v-if=\"weixinStatus === true\" @click=\"H5ShareBox = true\">\r\n\t\t\t\t<view class=\"iconfont icon-weixin3\"></view>\r\n\t\t\t\t<view class=\"\">发送给朋友</view>\r\n\t\t\t</button>\r\n\t\t\t<!-- #endif -->\r\n\t\t\t<!-- #ifdef MP -->\r\n\t\t\t<button class=\"item\" open-type=\"share\" hover-class='none' @click=\"goFriend\">\r\n\t\t\t\t<view class=\"iconfont icon-weixin3\"></view>\r\n\t\t\t\t<view class=\"\">发送给朋友</view>\r\n\t\t\t</button>\r\n\t\t\t<!-- #endif -->\r\n\t\t\t<button class=\"item\" hover-class='none' @tap=\"goPoster\">\r\n\t\t\t\t<view class=\"iconfont icon-haibao\"></view>\r\n\t\t\t\t<view class=\"\">生成海报</view>\r\n\t\t\t</button>\r\n\t\t</view>\r\n\t\t<view class=\"mask\"  v-if=\"posters\" @click=\"closePosters\"></view>\r\n\t\t<view class=\"mask\"  v-if=\"canvasStatus\"  @click=\"listenerActionClose\"></view>\r\n\r\n\t\t<!-- 海报展示 -->\r\n\t\t<view class='poster-pop' v-if=\"canvasStatus\">\r\n\t\t\t<image src='/static/images/poster-close.png' class='close' @click=\"posterImageClose\"></image>\r\n\t\t\t<image :src='posterImage'></image>\r\n\t\t\t<!-- #ifndef H5  -->\r\n\t\t\t<view class='save-poster' @click=\"savePosterPath\">保存到手机</view>\r\n\t\t\t<!-- #endif -->\r\n\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t<view class=\"keep\">长按图片可以保存到手机</view>\r\n\t\t\t<!-- #endif -->\r\n\t\t</view>\r\n\t\t<view class=\"canvas\"  v-else>\r\n\t\t\t<canvas style=\"width:750px;height:1190px;\" canvas-id=\"firstCanvas\"></canvas>\r\n\t\t\t<canvas canvas-id=\"qrcode\" :style=\"{width: `${qrcodeSize}px`, height: `${qrcodeSize}px`}\"/>\r\n\t\t</view>\r\n\t\t<!-- 发送给朋友图片 -->\r\n\t\t<view class=\"share-box\" v-if=\"H5ShareBox\">\r\n\t\t\t<image src=\"/static/images/share-info.png\" @click=\"H5ShareBox = false\"></image>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tconst app = getApp();\r\n\timport uQRCode from '@/js_sdk/Sansnn-uQRCode/uqrcode.js'\r\n\timport {\r\n\t\tmapGetters\r\n\t} from \"vuex\";\r\n\timport {\r\n\t\tgetSeckillDetail,\r\n\t\tseckillCode\r\n\t} from '@/api/activity.js';\r\n\timport {\r\n\t\tcollectAdd,\r\n\t\tcollectDel,\r\n\t\tgetReplyList,\r\n\t\tgetReplyConfig\r\n\t} from '@/api/store.js';\r\n\timport productConSwiper from '@/components/productConSwiper/index.vue'\r\n\timport productWindow from '@/components/productWindow/index.vue'\r\n\timport userEvaluation from '@/components/userEvaluation/index.vue'\r\n\t// #ifdef MP\r\n\timport authorize from '@/components/Authorize';\r\n\timport { base64src } from '@/utils/base64src.js'\r\n\timport {\r\n\t\tgetQrcode\r\n\t} from '@/api/api.js';\r\n\t// #endif\r\n\timport parser from \"@/components/jyf-parser/jyf-parser\";\r\n\timport home from '@/components/home/<USER>'\r\n\timport countDown from '@/components/countDown';\r\n\timport shareRedPackets from '@/components/shareRedPackets';\r\n\timport {\r\n\t\timageBase64\r\n\t} from \"@/api/public\";\r\n\timport {\r\n\t\ttoLogin\r\n\t} from '@/libs/login.js';\r\n\timport { silenceBindingSpread } from \"@/utils\";\r\n\timport { spread } from \"@/api/user\";\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tbgColor: {\r\n\t\t\t\t\t'bgColor': '#333333',\r\n\t\t\t\t\t'Color': '#fff',\r\n\t\t\t\t\t'isDay': true,\r\n\t\t\t\t\t'width': '44rpx',\r\n\t\t\t\t\t'timeTxtwidth': '16rpx',\r\n\t\t\t\t},\r\n\t\t\t\tdataShow: 0,\r\n\t\t\t\tid: 0,\r\n\t\t\t\ttime: 0,\r\n\t\t\t\tcountDownHour: \"00\",\r\n\t\t\t\tcountDownMinute: \"00\",\r\n\t\t\t\tcountDownSecond: \"00\",\r\n\t\t\t\tstoreInfo: [],\r\n\t\t\t\timgUrls: [],\r\n\t\t\t\tparameter: {\r\n\t\t\t\t\t'navbar': '1',\r\n\t\t\t\t\t'return': '1',\r\n\t\t\t\t\t'title': '抢购详情页',\r\n\t\t\t\t\t'color': false\r\n\t\t\t\t},\r\n\t\t\t\tattribute: {\r\n\t\t\t\t\tcartAttr: false,\r\n\t\t\t\t\tproductAttr: [],\r\n\t\t\t\t\tproductSelect: {}\r\n\t\t\t\t},\r\n\t\t\t\tproductValue: [],\r\n\t\t\t\tisOpen: false,\r\n\t\t\t\tattr: '请选择',\r\n\t\t\t\tattrValue: '',\r\n\t\t\t\tstatus: 1,\r\n\t\t\t\tisAuto: false,\r\n\t\t\t\tisShowAuth: false,\r\n\t\t\t\tiShidden: false,\r\n\t\t\t\tlimitNum: 1, //限制本属性产品的个数；\r\n\t\t\t\tpersonNum: 0, //限制用户购买的个数；\r\n\t\t\t\tiSplus: false,\r\n\t\t\t\treplyCount: 0, //总评论数量\r\n\t\t\t\treply: [], //评论列表\r\n\t\t\t\treplyChance: 0,\r\n\t\t\t\tnavH: \"\",\r\n\t\t\t\tnavList: ['商品', '评价', '详情'],\r\n\t\t\t\topacity: 0,\r\n\t\t\t\tscrollY: 0,\r\n\t\t\t\ttopArr: [],\r\n\t\t\t\ttoView: '',\r\n\t\t\t\theight: 0,\r\n\t\t\t\theightArr: [],\r\n\t\t\t\tlock: false,\r\n\t\t\t\tscrollTop: 0,\r\n\t\t\t\ttagStyle: {\r\n\t\t\t\t\timg: 'width:100%;display:block;',\r\n\t\t\t\t\ttable: 'width:100%',\r\n\t\t\t\t\tvideo: 'width:100%'\r\n\t\t\t\t},\r\n\t\t\t\tdatatime: 0,\r\n\t\t\t\tnavActive: 0,\r\n\t\t\t\tmeunHeight: 0,\r\n\t\t\t\tbackH: '',\r\n\t\t\t\tposters: false,\r\n\t\t\t\tweixinStatus: false,\r\n\t\t\t\tposterImageStatus: false,\r\n\t\t\t\tcanvasStatus: false, //海报绘图标签\r\n\t\t\t\tstoreImage: '', //海报产品图\r\n\t\t\t\tPromotionCode: '', //二维码图片\r\n\t\t\t\tposterImage: '', //海报路径\r\n\t\t\t\tposterbackgd: '/static/images/posterbackgd.png',\r\n\t\t\t\tactionSheetHidden: false,\r\n\t\t\t\tcart_num:'',\r\n\t\t\t\tattrTxt: '',\r\n\t\t\t\tqrcodeSize: 600,\r\n\t\t\t\timagePath:'',//海报路径\r\n\t\t\t\timgTop:'',\r\n\t\t\t\tH5ShareBox: false, //公众号分享图片\r\n\t\t\t\tsharePacket: {\r\n\t\t\t\t\tisState: true, //默认不显示\r\n\t\t\t\t},\r\n\t\t\t\tbuyNum: 1,\r\n\t\t\t\terrT: '',\r\n\t\t\t\treturnShow: true,\r\n\t\t\t\thomeTop: 20,\r\n\t\t\t\tuserCollect: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomponents: {\r\n\t\t\tshareRedPackets,\r\n\t\t\tproductConSwiper,\r\n\t\t\t'productWindow': productWindow,\r\n\t\t\tuserEvaluation,\r\n\t\t\t\"jyf-parser\": parser,\r\n\t\t\thome,\r\n\t\t\tcountDown,\r\n\t\t\t// #ifdef MP\r\n\t\t\tauthorize\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tcomputed: mapGetters(['isLogin','uid','chatUrl']),\r\n\t\twatch:{\r\n\t\t\tisLogin:{\r\n\t\t\t\thandler:function(newV,oldV){\r\n\t\t\t\t\tif(newV){\r\n\t\t\t\t\t\tthis.getSeckillDetail();\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tdeep:true\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\tlet that = this;\r\n\t\t\tthat.$store.commit(\"PRODUCT_TYPE\", 'normal');\r\n\t\t\tlet statusBarHeight = '';\r\n\t\t\tvar pages = getCurrentPages();\r\n\t\t\t//设置商品列表高度\r\n\t\t\tuni.getSystemInfo({\r\n\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\tthat.height = res.windowHeight\r\n\t\t\t\t\tstatusBarHeight = res.statusBarHeight\r\n\t\t\t\t\t//res.windowHeight:获取整个窗口高度为px，*2为rpx；98为头部占据的高度；\r\n\t\t\t\t},\r\n\t\t\t});\r\n\t\t\t// #ifndef APP-PLUS\r\n\t\t\tthis.navH = app.globalData.navHeight\r\n\t\t\t// #endif\r\n\t\t\t// #ifdef APP-PLUS\r\n\t\t\tthis.navH = 90\r\n\t\t\t// #endif\r\n\t\t\t// #ifdef MP\r\n\t\t\tlet menuButtonInfo = uni.getMenuButtonBoundingClientRect()\r\n\t\t\tthis.meunHeight = menuButtonInfo.height\r\n\t\t\tthis.backH = (that.navH / 2) + (this.meunHeight / 2)\r\n\t\t\t\r\n\t\t\t// #ifdef MP || APP-NVUE\r\n\t\t\t// 小程序链接进入获取绑定关系id\r\n\t\t\t// if(options.spread) app.globalData.spread = options.spread; \r\n\t\t\tsetTimeout(()=>{\r\n\t\t\t\tif(options.spread){\r\n\t\t\t\t\tapp.globalData.spread = options.spread;\r\n\t\t\t\t\tspread(options.spread).then(res => {})\r\n\t\t\t\t}\r\n\t\t\t},2000)\r\n\t\t\t// #endif\r\n\t\t\tthat.$set(that,'theme',that.$Cache.get('theme')); //用户从分享卡片进入的场景下获取主题色配置\r\n\t\t\t// #endif\r\n\t\t\tif (!options.scene && !options.id){\r\n\t\t\t\tthis.showSkeleton = false;\r\n\t\t\t\tthis.$util.Tips({\r\n\t\t\t\t\ttitle: '缺少参数无法查看商品'\r\n\t\t\t\t}, {\r\n\t\t\t\t\turl: '/pages/index/index'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tif (options.hasOwnProperty('id') || options.scene){\r\n\t\t\t\tif (options.scene) { // 仅仅小程序扫码进入\r\n\t\t\t\t\tlet qrCodeValue = this.$util.getUrlParams(decodeURIComponent(options.scene));\r\n\t\t\t\t\tlet mapeMpQrCodeValue = this.$util.formatMpQrCodeData(qrCodeValue);\r\n\t\t\t\t    app.globalData.spread = mapeMpQrCodeValue.spread;\r\n\t\t\t\t    this.id = mapeMpQrCodeValue.id;\r\n\t\t\t\t    setTimeout(()=>{\r\n\t\t\t\t    \tspread(mapeMpQrCodeValue.spread).then(res => {}).catch(res => {})\r\n\t\t\t\t    },2000)\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthis.id = options.id;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (this.isLogin) {\r\n\t\t\t\tthis.getSeckillDetail();\r\n\t\t\t} else {\r\n\t\t\t\tthis.$Cache.set('login_back_url',\r\n\t\t\t\t\t'/pages/activity/goods_seckill_details/index?id=' + this.id + '&spread=' + app.globalData.spread?app.globalData.spread:0);\r\n\t\t\t\ttoLogin();\r\n\t\t\t}\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t// #ifdef MP\r\n\t\t\t\tconst menuButton = uni.getMenuButtonBoundingClientRect();\r\n\t\t\t\tconst query = uni.createSelectorQuery().in(this);\r\n\t\t\t\tquery\r\n\t\t\t\t\t.select('#home')\r\n\t\t\t\t\t.boundingClientRect(data => {\r\n\t\t\t\t\t\tthis.homeTop = menuButton.top * 2 + menuButton.height - data.height;\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.exec();\r\n\t\t\t\t// #endif\r\n\t\t\t})\r\n\t\t\tsilenceBindingSpread();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tkefuClick(){\r\n\t\t\t\tlocation.href = this.chatUrl;\r\n\t\t\t},\r\n\t\t\tclosePosters:function(){\r\n\t\t\t\tthis.posters = false;\r\n\t\t\t},\r\n\t\t\tgetProductReplyList: function() {\r\n\t\t\t\tgetReplyList(this.storeInfo.productId, {\r\n\t\t\t\t\tpage: 1,\r\n\t\t\t\t\tlimit: 3,\r\n\t\t\t\t\ttype: 0,\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tthis.reply = res.data.list;\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetProductReplyCount: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tgetReplyConfig(that.storeInfo.productId).then(res => {\r\n\t\t\t\t\tthat.$set(that, 'replyChance', res.data.replyChance * 100);\r\n\t\t\t\t\tthat.$set(that, 'replyCount', res.data.sumCount);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 购物车手动填写\r\n\t\t\t * \r\n\t\t\t*/\r\n\t\t\tiptCartNum: function (e) {\r\n\t\t\t\tthis.$set(this.attribute.productSelect, 'cart_num', e?e:1);\r\n\t\t\t\tthis.$set(this, \"cart_num\", e);\r\n\t\t\t\tif (e > 1) {\r\n\t\t\t\t\treturn this.$util.Tips({\r\n\t\t\t\t\t\ttitle: `该商品每次限购1${this.storeInfo.unitName}`\r\n\t\t\t\t\t}); \r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 后退\r\n\t\t\treturns: function() {\r\n\t\t\t\tuni.navigateBack()\r\n\t\t\t},\r\n\t\t\tonLoadFun: function(data) {\r\n\t\t\t\tif(this.isAuto){\r\n\t\t\t\t\tthis.isAuto = false;\r\n\t\t\t\t\tthis.isShowAuth = false;\r\n\t\t\t\t\tthis.getSeckillDetail();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgetSeckillDetail: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tgetSeckillDetail(that.id).then(res => {\r\n\t\t\t\t\tthis.dataShow = 1;\r\n\t\t\t\t\tthis.storeInfo = res.data.storeSeckill;\r\n\t\t\t\t\tthis.userCollect = res.data.userCollect;\r\n\t\t\t\t\tthis.status = this.storeInfo.seckillStatus;\r\n\t\t\t\t\tthis.datatime = Number(this.storeInfo.timeSwap);\r\n\t\t\t\t\tthis.imgUrls = JSON.parse(res.data.storeSeckill.sliderImage) || [];\r\n\t\t\t\t\tthis.attribute.productAttr = res.data.productAttr;\r\n\t\t\t\t\tthis.productValue = res.data.productValue;\r\n\t\t\t\t\tthis.personNum = res.data.storeSeckill.quota;\r\n\t\t\t\t\tthis.attribute.productSelect.num = res.data.storeSeckill.num;\r\n\t\t\t\t\t\r\n\t\t\t\t\tthis.getProductReplyList();\r\n\t\t\t\t\tthis.getProductReplyCount();\r\n\t\t\t\t\tlet productAttr = res.data.productAttr.map(item => {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tattrName : item.attrName,\r\n\t\t\t\t\t\tattrValues: item.attrValues.split(','),\r\n\t\t\t\t\t\tid:item.id,\r\n\t\t\t\t\t\tisDel:item.isDel,\r\n\t\t\t\t\t\tproductId:item.productId,\r\n\t\t\t\t\t\ttype:item.type\r\n\t\t\t\t\t }\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthis.$set(this.attribute,'productAttr',productAttr);\r\n\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\tthat.storeImage = that.storeInfo.image;\r\n\t\t\t\t\tthat.make();\r\n\t\t\t\t\tthat.setShare();\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifdef MP\r\n\t\t\t\t\tthat.getQrcode();\r\n\t\t\t\t\tthat.imgTop = res.data.storeSeckill.image\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifndef H5\r\n\t\t\t\t\tthat.downloadFilestoreImage();\r\n\t\t\t\t\t//that.downloadFilePromotionCode();\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\tthat.DefaultSelect();\r\n\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\tthat.infoScroll();\r\n\t\t\t\t\t}, 1000);\r\n\t\t\t\t\tapp.globalData.openPages = '/pages/activity/goods_seckill_details/index?id=' + that.id + '&spread=' + that.uid ;\r\n\t\t\t\t\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\ttitle:err\r\n\t\t\t\t\t},{\r\n\t\t\t\t\t\ttab:3\r\n\t\t\t\t\t})\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tsetShare: function() {\r\n\t\t\t\tthis.$wechat.isWeixin() &&\r\n\t\t\t\t\tthis.$wechat.wechatEvevt([\r\n\t\t\t\t\t\t\"updateAppMessageShareData\",\r\n\t\t\t\t\t\t\"updateTimelineShareData\",\r\n\t\t\t\t\t\t\"onMenuShareAppMessage\",\r\n\t\t\t\t\t\t\"onMenuShareTimeline\"\r\n\t\t\t\t\t], {\r\n\t\t\t\t\t\tdesc: this.storeInfo.info,\r\n\t\t\t\t\t\ttitle: this.storeInfo.title,\r\n\t\t\t\t\t\tlink: location.href,\r\n\t\t\t\t\t\timgUrl: this.storeInfo.image\r\n\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t}).catch(err => {\r\n\t\t\t\t\t\tconsole.log(err);\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 默认选中属性\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tDefaultSelect: function() {\r\n\t\t\t\tlet self = this\r\n\t\t\t\tlet productAttr = self.attribute.productAttr;\r\n\t\t\t\tlet value = [];\r\n\t\t\t\tfor (var key in self.productValue) {\r\n\t\t\t\t\tif (self.productValue[key].stock > 0) {\r\n\t\t\t\t\t\tvalue = self.attribute.productAttr.length ? key.split(\",\") : [];\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tfor (let i = 0; i < productAttr.length; i++) {\r\n\t\t\t\t\tthis.$set(productAttr[i], \"index\", value[i]);\r\n\t\t\t\t}\r\n\t\t\t\t//sort();排序函数:数字-英文-汉字；\r\n\t\t\t\tlet productSelect = this.productValue[value.join(\",\")];\r\n\t\t\t\tif (productSelect && productAttr.length) {\r\n\t\t\t\t\tself.$set(\r\n\t\t\t\t\t\tself.attribute.productSelect,\r\n\t\t\t\t\t\t\"storeName\",\r\n\t\t\t\t\t\tself.storeInfo.storeName\r\n\t\t\t\t\t);\r\n\t\t\t\t\tself.$set(self.attribute.productSelect, \"image\", productSelect.image);\r\n\t\t\t\t\tself.$set(self.attribute.productSelect, \"price\", productSelect.price);\r\n\t\t\t\t\tself.$set(self.attribute.productSelect, \"stock\", productSelect.stock);\r\n\t\t\t\t\tself.$set(self.attribute.productSelect, \"unique\", productSelect.id);\r\n                    self.$set(self.attribute.productSelect, \"quota\", productSelect.quota);\r\n\t\t\t\t\tself.$set(self.attribute.productSelect, \"quotaShow\", productSelect.quotaShow);\r\n\t\t\t\t\tself.$set(self.attribute.productSelect, \"cart_num\", 1);\r\n\t\t\t\t\tself.$set(self, \"attrValue\", value.join(\",\"));\r\n\t\t\t\t\tthis.$set(self, \"attrTxt\", \"已选择\")\r\n\t\t\t\t\t\tself.attrValue = value.join(\",\")\r\n\t\t\t\t} else if (!productSelect && productAttr.length) {\r\n\t\t\t\t\tself.$set(\r\n\t\t\t\t\t\tself.attribute.productSelect,\r\n\t\t\t\t\t\t\"storeName\",\r\n\t\t\t\t\t\tself.storeInfo.storeName\r\n\t\t\t\t\t);\r\n\t\t\t\t\tself.$set(self.attribute.productSelect, \"image\", self.storeInfo.image);\r\n\t\t\t\t\tself.$set(self.attribute.productSelect, \"price\", self.storeInfo.price);\r\n\t\t\t\t\tself.$set(self.attribute.productSelect, \"quota\", 0);\r\n\t\t\t\t\tself.$set(self.attribute.productSelect, \"quota\", 0);\r\n\t\t\t\t\tself.$set(self.attribute.productSelect, \"stock\", 0);\r\n\t\t\t\t\tself.$set(self.attribute.productSelect, \"unique\", \"\");\r\n\t\t\t\t\tself.$set(self.attribute.productSelect, \"cart_num\", 0);\r\n\t\t\t\t\tself.$set(self, \"attrValue\", \"\");\r\n\t\t\t\t\tself.$set(self, \"attrTxt\", \"请选择\");\r\n\t\t\t\t} else if (!productSelect && !productAttr.length) {\r\n\t\t\t\t\tself.$set(\r\n\t\t\t\t\t\tself.attribute.productSelect,\r\n\t\t\t\t\t\t\"storeName\",\r\n\t\t\t\t\t\tself.storeInfo.storeName\r\n\t\t\t\t\t);\r\n\t\t\t\t\tself.$set(self.attribute.productSelect, \"image\", self.storeInfo.image);\r\n\t\t\t\t\tself.$set(self.attribute.productSelect, \"price\", self.storeInfo.price);\r\n\t\t\t\t\tself.$set(self.attribute.productSelect, \"quota\", self.storeInfo.quota);\r\n\t\t\t\t\tself.$set(self.attribute.productSelect, \"quotaShow\", self.storeInfo.quotaShow);\r\n\t\t\t\t\tself.$set(self.attribute.productSelect, \"stock\", self.storeInfo.stock);\r\n\t\t\t\t\tself.$set(\r\n\t\t\t\t\t\tself.attribute.productSelect,\r\n\t\t\t\t\t\t\"unique\", \"\"\r\n\t\t\t\t\t);\r\n\t\t\t\t\tself.$set(self.attribute.productSelect, \"cart_num\", 1);\r\n\t\t\t\t\tself.$set(self, \"attrValue\", \"\");\r\n\t\t\t\t\tself.$set(self, \"attrTxt\", \"请选择\");\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tselecAttr: function() {\r\n\t\t\t\tthis.attribute.cartAttr = true\r\n\t\t\t},\r\n\t\t\tonMyEvent: function() {\r\n\t\t\t\tthis.$set(this.attribute, 'cartAttr', false);\r\n\t\t\t\tthis.$set(this, 'isOpen', false);\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 购物车数量加和数量减\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tChangeCartNum: function(changeValue) {\r\n\t\t\t\t//changeValue:是否 加|减\r\n\t\t\t\t//获取当前变动属性\r\n\t\t\t\tlet productSelect = this.productValue[this.attrValue];\r\n\t\t\t\tif (this.cart_num) {\r\n\t\t\t\t\tproductSelect.cart_num = this.cart_num;\r\n\t\t\t\t\tthis.attribute.productSelect.cart_num = this.cart_num;\r\n\t\t\t\t}\r\n\t\t\t\t//如果没有属性,赋值给商品默认库存\r\n\t\t\t\tif (productSelect === undefined && !this.attribute.productAttr.length)\r\n\t\t\t\t\tproductSelect = this.attribute.productSelect;\r\n\t\t\t\t//无属性值即库存为0；不存在加减；\r\n\t\t\t\tif (productSelect === undefined) return;\r\n\t\t\t\tlet stock = productSelect.stock || 0;\r\n\t\t\t\tlet quota = productSelect.quota || 0;\r\n\t\t\t\tlet num = this.attribute.productSelect;\r\n\t\t\t\tlet nums = this.storeInfo.num || 0;\r\n\t\t\t\t//设置默认数据\r\n\t\t\t\tif (productSelect.cart_num == undefined) productSelect.cart_num = 1;\r\n\t\t\t\tif (changeValue) {\r\n\t\t\t\t\tif (num.cart_num === 1) {\r\n\t\t\t\t\t\treturn this.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: `该商品每次限购1${this.storeInfo.unitName}`\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t\tnum.cart_num++;\r\n\t\t\t\t\tlet arrMin = [];\r\n\t\t\t\t\tarrMin.push(nums);\r\n\t\t\t\t\tarrMin.push(quota);\r\n\t\t\t\t\tarrMin.push(stock);\r\n\t\t\t\t\tlet minN = Math.min.apply(null, arrMin);\r\n\t\t\t\t\tif (num.cart_num >= minN) {\r\n\t\t\t\t\t\tthis.$set(this.attribute.productSelect, \"cart_num\", minN ? minN : 1);\r\n\t\t\t\t\t\tthis.$set(this, \"cart_num\", minN ? minN : 1);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.$set(this, \"cart_num\", num.cart_num);\r\n\t\t\t\t\tthis.$set(this.attribute.productSelect, \"cart_num\", num.cart_num);\r\n\t\t\t\r\n\t\t\t\t} else {\r\n\t\t\t\t\tnum.cart_num--;\r\n\t\t\t\t\tif (num.cart_num < 1) {\r\n\t\t\t\t\t\tthis.$set(this.attribute.productSelect, \"cart_num\", 1);\r\n\t\t\t\t\t\tthis.$set(this, \"cart_num\", 1);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.$set(this, \"cart_num\", num.cart_num);\r\n\t\t\t\t\tthis.$set(this.attribute.productSelect, \"cart_num\", num.cart_num);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tattrVal(val) {\r\n\t\t\t\tthis.attribute.productAttr[val.indexw].index = this.attribute.productAttr[val.indexw].attrValues[val.indexn];\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 属性变动赋值\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tChangeAttr: function(res) {\r\n\t\t\t\tthis.$set(this,'cart_num',1);\r\n\t\t\t\tlet productSelect = this.productValue[res];\r\n\t\t\t\tif (productSelect) {\r\n\t\t\t\t\tthis.$set(this.attribute.productSelect, \"image\", productSelect.image);\r\n\t\t\t\t\tthis.$set(this.attribute.productSelect, \"price\", productSelect.price);\r\n\t\t\t\t\tthis.$set(this.attribute.productSelect, \"stock\", productSelect.stock);\r\n\t\t\t\t\tthis.$set(this.attribute.productSelect, \"unique\", productSelect.id);\r\n\t\t\t\t\tthis.$set(this.attribute.productSelect, \"cart_num\", 1);\r\n\t\t\t\t\tthis.$set(this.attribute.productSelect, \"quota\", productSelect.quota);\r\n\t\t\t\t\tthis.$set(this.attribute.productSelect, \"quotaShow\", productSelect.quotaShow);\r\n\t\t\t\t\tthis.$set(this, \"attrValue\", res);\r\n\t\t\t\t\tthis.attrTxt = \"已选择\"\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$set(this.attribute.productSelect, \"image\", this.storeInfo.image);\r\n\t\t\t\t\tthis.$set(this.attribute.productSelect, \"price\", this.storeInfo.price);\r\n\t\t\t\t\tthis.$set(this.attribute.productSelect, \"stock\", 0);\r\n\t\t\t\t\tthis.$set(this.attribute.productSelect, \"unique\", \"\");\r\n\t\t\t\t\tthis.$set(this.attribute.productSelect, \"cart_num\", 0);\r\n\t\t\t\t\tthis.$set(this.attribute.productSelect, \"quota\", 0);\r\n\t\t\t\t\tthis.$set(this.attribute.productSelect, \"quotaShow\", 0);\r\n\t\t\t\t\tthis.$set(this, \"attrValue\", \"\");\r\n\t\t\t\t\tthis.attrTxt = \"已选择\"\r\n\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tscroll: function(e) {\r\n\t\t\t\tvar that = this,\r\n\t\t\t\t\tscrollY = e.detail.scrollTop;\r\n\t\t\t\tvar opacity = scrollY / 200;\r\n\t\t\t\topacity = opacity > 1 ? 1 : opacity;\r\n\t\t\t\tthat.opacity = opacity\r\n\t\t\t\tthat.scrollY = scrollY\r\n\t\t\t\tif (that.lock) {\r\n\t\t\t\t\tthat.lock = false\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tfor (var i = 0; i < that.topArr.length; i++) {\r\n\t\t\t\t\tif (scrollY < that.topArr[i] - (app.globalData.navHeight / 2) + that.heightArr[i]) {\r\n\t\t\t\t\t\tthat.navActive = i\r\n\t\t\t\t\t\tbreak\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\ttap: function(item, index) {\r\n\t\t\t\tvar id = item.id;\r\n\t\t\t\tvar index = index;\r\n\t\t\t\tvar that = this;\r\n\t\t\t\t// if (!this.data.good_list.length && id == \"past2\") {\r\n\t\t\t\t//   id = \"past3\"\r\n\t\t\t\t// }\r\n\t\t\t\tthis.toView = id;\r\n\t\t\t\tthis.navActive = index;\r\n\t\t\t\tthis.lock = true;\r\n\t\t\t\tthis.scrollTop = index > 0 ? that.topArr[index] - (app.globalData.navHeight / 2) : that.topArr[index]\r\n\t\t\t},\r\n\t\t\tinfoScroll: function() {\r\n\t\t\t\tvar that = this,\r\n\t\t\t\t\ttopArr = [],\r\n\t\t\t\t\theightArr = [];\r\n\t\t\t\tfor (var i = 0; i < that.navList.length; i++) { //productList\r\n\t\t\t\t\t//获取元素所在位置\r\n\t\t\t\t\tvar query = wx.createSelectorQuery().in(this);\r\n\t\t\t\t\tvar idView = \"#past\" + i;\r\n\t\t\t\t\t// if (!that.data.good_list.length && i == 2) {\r\n\t\t\t\t\t//   var idView = \"#past\" + 3;\r\n\t\t\t\t\t// }\r\n\t\t\t\t\tquery.select(idView).boundingClientRect();\r\n\t\t\t\t\tquery.exec(function(res) {\r\n\t\t\t\t\t\tvar top = res[0].top;\r\n\t\t\t\t\t\tvar height = res[0].height;\r\n\t\t\t\t\t\ttopArr.push(top);\r\n\t\t\t\t\t\theightArr.push(height);\r\n\t\t\t\t\t\tthat.topArr = topArr\r\n\t\t\t\t\t\tthat.heightArr = heightArr\r\n\t\t\t\t\t});\r\n\t\t\t\t};\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 收藏商品\r\n\t\t\t */\r\n\t\t\tsetCollect: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tif (this.userCollect) {\r\n\t\t\t\t\tcollectDel(this.storeInfo.productId).then(res => {\r\n\t\t\t\t\t\tthat.userCollect = !that.userCollect\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tcollectAdd(this.storeInfo.productId).then(res => {\r\n\t\t\t\t\t\tthat.userCollect = !that.userCollect\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t/*\r\n\t\t\t *  单独购买\r\n\t\t\t */\r\n\t\t\topenAlone: function() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/goods_details/index?id=${this.storeInfo.productId}`\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t/*\r\n\t\t\t *  下订单\r\n\t\t\t */\r\n\t\t\tgoCat: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar productSelect = this.productValue[this.attrValue];\r\n\t\t\t\tvar productSelect = this.productValue[this.attrValue];\r\n\t\t\t\tif (that.cart_num > 1) {\r\n\t\t\t\t\treturn this.$util.Tips({\r\n\t\t\t\t\t\ttitle: `该商品每人限购1${this.storeInfo.unitName}`\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\t//打开属性\r\n\t\t\t\tif (this.isOpen)\r\n\t\t\t\t\tthis.attribute.cartAttr = true\r\n\t\t\t\telse\r\n\t\t\t\t\tthis.attribute.cartAttr = !this.attribute.cartAttr\r\n\t\t\t\t//只有关闭属性弹窗时进行加入购物车\r\n\t\t\t\tif (this.attribute.cartAttr === true && this.isOpen == false) return this.isOpen = true\r\n\t\t\t\t//如果有属性,没有选择,提示用户选择\r\n\t\t\t\tif (this.attribute.productAttr.length && productSelect === undefined && this.isOpen == true) return app.$util.Tips({\r\n\t\t\t\t\ttitle: '请选择属性'\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\tthis.$Order.getPreOrder(\"buyNow\",[{\r\n\t\t\t\t\t\t\"attrValueId\": parseFloat(this.attribute.productSelect.unique),\r\n\t\t\t\t\t\t\"seckillId\": parseFloat(this.id),\r\n\t\t\t\t\t\t\"productNum\": parseFloat(this.cart_num ? this.cart_num : this.attribute.productSelect.cart_num),\r\n\t\t\t\t\t\t\"productId\": parseFloat(this.storeInfo.productId)\r\n\t\t\t\t\t}]);\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 分享打开\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tlistenerActionSheet: function() {\r\n\t\t\t\tif (this.isLogin === false) {\r\n\t\t\t\t\ttoLogin();\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\tif(!this.imgTop)this.getImageBase64(this.storeImage);\r\n\t\t\t\t\tif (this.$wechat.isWeixin() === true) {\r\n\t\t\t\t\t\tthis.weixinStatus = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\tthis.posters = true;\r\n\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 分享关闭\r\n\t\t\tlistenerActionClose: function() {\r\n\t\t\t\tthis.posters = false;\r\n\t\t\t},\r\n\t\t\t//隐藏海报\r\n\t\t\tposterImageClose: function() {\r\n\t\t\t\tthis.canvasStatus = false\r\n\t\t\t},\r\n\t\t\t//替换安全域名\r\n\t\t\tsetDomain: function(url) {\r\n\t\t\t\turl = url ? url.toString() : '';\r\n\t\t\t\t//本地调试打开,生产请注销\r\n\t\t\t\tif (url.indexOf(\"https://\") > -1) return url;\r\n\t\t\t\telse return url.replace('http://', 'https://');\r\n\t\t\t},\r\n\t\t\t//获取海报产品图\r\n\t\t\tdownloadFilestoreImage: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tuni.downloadFile({\r\n\t\t\t\t\turl: that.setDomain(that.storeInfo.image),\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tthat.storeImage = res.tempFilePath;\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: function() {\r\n\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: ''\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tthat.storeImage = '';\r\n\t\t\t\t\t},\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 获取产品分销二维码\r\n\t\t\t * @param function successFn 下载完成回调\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tdownloadFilePromotionCode: function(successFn) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tseckillCode(that.id,{stop_time:that.datatime}).then(res => {\r\n\t\t\t\t\tuni.downloadFile({\r\n\t\t\t\t\t\turl: that.setDomain(res.data.code),\r\n\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\tthat.$set(that, 'isDown', false);\r\n\t\t\t\t\t\t\tif (typeof successFn == 'function')\r\n\t\t\t\t\t\t\t\tsuccessFn && successFn(res.tempFilePath);\r\n\t\t\t\t\t\t\telse\r\n\t\t\t\t\t\t\t\tthat.$set(that, 'PromotionCode', res.tempFilePath);\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail: function() {\r\n\t\t\t\t\t\t\tthat.$set(that, 'isDown', false);\r\n\t\t\t\t\t\t\tthat.$set(that, 'PromotionCode', '');\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t});\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tthat.$set(that, 'isDown', false);\r\n\t\t\t\t\tthat.$set(that, 'PromotionCode', '');\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgetImageBase64:function(images){\r\n\t\t\t\tlet that = this;\r\n\t\t\t\timageBase64({url:images}).then(res=>{\r\n\t\t\t\t\tthat.imgTop = res.data.code\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 小程序关闭分享弹窗；\r\n\t\t\tgoFriend: function() {\r\n\t\t\t\tthis.posters = false;\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 生成海报\r\n\t\t\t */\r\n\t\t\tgoPoster: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '海报生成中',\r\n\t\t\t\t\tmask: true\r\n\t\t\t\t});\r\n\t\t\t\tthat.posters = false;\r\n\t\t\t\tlet arrImagesUrl = '';\r\n\t\t\t\tlet arrImagesUrlTop = '';\r\n\t\t\t\tif(!that.PromotionCode){\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\ttitle: that.errT\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn \r\n\t\t\t\t} \r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tif (!that.imgTop) {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: '无法生成商品海报！'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t}, 1000);\r\n\t\t\t\tuni.downloadFile({\r\n\t\t\t\t\turl: that.imgTop, //仅为示例，并非真实的资源\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tarrImagesUrlTop = res.tempFilePath;\r\n\t\t\t\t\t\tlet arrImages = [that.posterbackgd, arrImagesUrlTop, that.PromotionCode];\r\n\t\t\t\t\t\tlet storeName = that.storeInfo.storeName;\r\n\t\t\t\t\t\tlet price = that.storeInfo.price;\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tthat.$util.PosterCanvas(arrImages, storeName, price, that.storeInfo.otPrice,function(tempFilePath) {\r\n\t\t\t\t\t\t\t\tthat.posterImage = tempFilePath;\r\n\t\t\t\t\t\t\t\tthat.canvasStatus = true;\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t});\t\r\n\t\t\t\t\t\t}, 500);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\t\t\r\n\t\t\t},\r\n\t\t\t// 小程序二维码\r\n\t\t\tgetQrcode(){\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\tpid: that.uid,\r\n\t\t\t\t\tid: that.id,\r\n\t\t\t\t\tpath: 'pages/activity/goods_seckill_details/index'\r\n\t\t\t\t}\r\n\t\t\t\tgetQrcode(data).then(res=>{\r\n\t\t\t\t\tbase64src(res.data.code, res => {\r\n\t\t\t\t\t\tthat.PromotionCode = res;\r\n\t\t\t\t\t});\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tthat.errT = err;\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 生成二维码；\r\n\t\t\tmake() {\r\n\t\t\t\tlet href = location.href.split('?')[0] + \"?id=\"+ this.id + \"&spread=\"  + this.uid;\r\n\t\t\t\tuQRCode.make({\r\n\t\t\t\t\tcanvasId: 'qrcode',\r\n\t\t\t\t\ttext: href,\r\n\t\t\t\t\tsize: this.qrcodeSize,\r\n\t\t\t\t\tmargin: 10,\r\n\t\t\t\t\tsuccess: res => {\r\n\t\t\t\t\t\tthis.PromotionCode = res;\r\n\t\t\t\t\t},\r\n\t\t\t\t\tcomplete: (res) => {\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail:res=>{\r\n\t\t\t\t\t\tthis.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: '海报二维码生成失败！'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t/*\r\n\t\t\t * 保存到手机相册\r\n\t\t\t */\r\n\t\t\t// #ifdef MP\r\n\t\t\tsavePosterPath: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tuni.getSetting({\r\n\t\t\t\t\tsuccess(res) {\r\n\t\t\t\t\t\tif (!res.authSetting['scope.writePhotosAlbum']) {\r\n\t\t\t\t\t\t\tuni.authorize({\r\n\t\t\t\t\t\t\t\tscope: 'scope.writePhotosAlbum',\r\n\t\t\t\t\t\t\t\tsuccess() {\r\n\t\t\t\t\t\t\t\t\tuni.saveImageToPhotosAlbum({\r\n\t\t\t\t\t\t\t\t\t\tfilePath: that.posterImage,\r\n\t\t\t\t\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\t\t\t\t\tthat.posterImageClose();\r\n\t\t\t\t\t\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: '保存成功',\r\n\t\t\t\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\tfail: function(res) {\r\n\t\t\t\t\t\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: '保存失败'\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tuni.saveImageToPhotosAlbum({\r\n\t\t\t\t\t\t\t\tfilePath: that.posterImage,\r\n\t\t\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\t\t\tthat.posterImageClose();\r\n\t\t\t\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '保存成功',\r\n\t\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tfail: function(res) {\r\n\t\t\t\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '保存失败'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// #endif\r\n\t\t\tsetShareInfoStatus: function() {\r\n\t\t\t\tlet data = this.storeInfo;\r\n\t\t\t\tlet href = location.href;\r\n\t\t\t\tif (this.$wechat.isWeixin()) {\r\n\t\t\t\t\thref =\r\n\t\t\t\t\t\thref.indexOf(\"?\") === -1 ?\r\n\t\t\t\t\t\thref + \"?spread=\" + this.uid :\r\n\t\t\t\t\t\thref + \"&spread=\" + this.uid;\r\n\t\t\t\t\t\r\n\t\t\t\t\tlet configAppMessage = {\r\n\t\t\t\t\t\tdesc: data.storeInfo,\r\n\t\t\t\t\t\ttitle: data.storeName,\r\n\t\t\t\t\t\tlink: href,\r\n\t\t\t\t\t\timgUrl: data.image\r\n\t\t\t\t\t};\r\n\t\t\t\t\tthis.$wechat.wechatEvevt([\"updateAppMessageShareData\", \"updateTimelineShareData\"], configAppMessage)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t},\r\n\t    //#ifdef MP\r\n\t    onShareAppMessage() {\r\n\t    \tlet that = this;\r\n\t    \treturn {\r\n\t    \t\ttitle: that.storeInfo.title,\r\n\t    \t\tpath: app.globalData.openPages,\r\n\t    \t\timageUrl: that.storeInfo.image\r\n\t    \t};\r\n\t    },\r\n\t    //#endif\r\n\t}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t.userEvaluation{\r\n\t\ti{\r\n\t\t\tdisplay: inline-block;\r\n\t\t}\r\n\t}\r\n\t.product-con{\r\n\t\t.line1{\r\n\t\t\twidth: 600rpx;\r\n\t\t}\r\n\t}\r\n\t.share-box {\r\n\t\tz-index: 1000;\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\ttop: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\r\n\t\timage {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t}\r\n\t}\r\n\t.generate-posters {\r\n\t\twidth: 100%;\r\n\t\theight: 170rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\tbottom: 0;\r\n\t\tz-index: 99;\r\n\t\ttransform: translate3d(0, 100%, 0);\r\n\t\ttransition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);\r\n\t\tborder-top: 1rpx solid #eee;\r\n\t}\r\n\r\n\t.generate-posters.on {\r\n\t\ttransform: translate3d(0, 0, 0);\r\n\t}\r\n\r\n\t.generate-posters .item {\r\n\t\tflex: 50%;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 30rpx;\r\n\t}\r\n\r\n\t.generate-posters .item .iconfont {\r\n\t\tfont-size: 80rpx;\r\n\t\tcolor: #5eae72;\r\n\t}\r\n\r\n\t.generate-posters .item .iconfont.icon-haibao {\r\n\t\tcolor: #5391f1;\r\n\t}\r\n\r\n\t.navbar .header {\r\n\t\theight: 96rpx;\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #050505;\r\n\t\tbackground-color: #fff;\r\n\t\t/* #ifdef MP */\r\n\t\tpadding-right: 95rpx;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.icon-xiangzuo {\r\n\t\t/* #ifdef H5 */\r\n\t\ttop: 30rpx !important;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.navbar .header .item {\r\n\t\tposition: relative;\r\n\t\tmargin: 0 25rpx;\r\n\t}\r\n\r\n\t.navbar .header .item.on:before {\r\n\t\tposition: absolute;\r\n\t\twidth: 60rpx;\r\n\t\theight: 5rpx;\r\n\t\tbackground-repeat: no-repeat;\r\n\t\tcontent: \"\";\r\n\t\tbackground-image: linear-gradient(to right, #ff3366 0%, #ff6533 100%);\r\n\t\tbottom: -10rpx;\r\n\t\tleft: 50%;\r\n\t\tmargin-left: -28rpx;\r\n\t}\r\n\r\n\t.navbar {\r\n\t\tposition: fixed;\r\n\t\tbackground-color: #fff;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tz-index: 999;\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.navbar .navbarH {\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.navbar .navbarH .navbarCon {\r\n\t\tposition: absolute;\r\n\t\tbottom: 0;\r\n\t\theight: 100rpx;\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.icon-xiangzuo {\r\n\t\tcolor: #000;\r\n\t\tposition: fixed;\r\n\t\tfont-size: 40rpx;\r\n\t\twidth: 100rpx;\r\n\t\theight: 56rpx;\r\n\t\tline-height: 54rpx;\r\n\t\tz-index: 1000;\r\n\t\tleft: 33rpx;\r\n\t}\r\n\r\n\t.product-con .nav {\r\n\t\tbackground-image: url('data:image/png;base64,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');\r\n\t\tbackground-repeat: no-repeat;\r\n\t\tbackground-size: 100% 100%;\r\n\t\twidth: 100%;\r\n\t\theight: 110rpx;\r\n\t\tpadding: 0 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tbackground-color: #c9ab79;\r\n\t}\r\n\r\n\t.product-con .nav .money {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #fff;\r\n\t}\r\n\r\n\t.product-con .nav .money .num {\r\n\t\tfont-size: 48rpx;\r\n\t}\r\n\r\n\t.product-con .nav .money .y-money {\r\n\t\tfont-size: 26rpx;\r\n\t\tmargin-left: 10rpx;\r\n\t\ttext-decoration: line-through;\r\n\t}\r\n\r\n\t.product-con .nav .time {\r\n\t\tfont-size: 20rpx;\r\n\t\tcolor: #fff;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.product-con .nav .time .timeCon {\r\n\t\tmargin-top: 10rpx;\r\n\t}\r\n\r\n\t.product-con .nav .time .timeCon .num {\r\n\t\tpadding: 0 7rpx;\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #ff3d3d;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 2rpx;\r\n\t}\r\n\r\n\t.product-con .nav .timeState {\r\n\t\tfont-size: 28RPX;\r\n\t\tcolor: #FFF;\r\n\t}\r\n\r\n\t.product-con .nav .iconfont {\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 30rpx;\r\n\t\tmargin-left: 20rpx;\r\n\t}\r\n\r\n\t.product-con .wrapper .introduce {\r\n\t\tmargin: 0;\r\n\t}\r\n\r\n\t.product-con .wrapper .introduce .infor {\r\n\t\twidth: 570rpx;\r\n\t}\r\n\r\n\t.product-con .wrapper .introduce .iconfont {\r\n\t\tfont-size: 36rpx;\r\n\t\tcolor: #999999;\r\n\t}\r\n\r\n\t.product-con .wrapper .label {\r\n\t\tmargin: 18rpx 0 0 0;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #82848f;\r\n\t}\r\n\r\n\t.product-con .wrapper .label .stock {\r\n\t\twidth: 255rpx;\r\n\t\tmargin-right: 28rpx;\r\n\t}\r\n\r\n\t.product-con .footer {\r\n\t\tpadding: 0 20rpx 0 30rpx;\r\n\t\tposition: fixed;\r\n\t\tbottom: 0;\r\n\t\twidth: 100%;\r\n\t\tbox-sizing: border-box;\r\n\t\theight: 100rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tz-index: 99;\r\n\t\tborder-top: 1rpx solid #f0f0f0;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.product-con .footer .item {\r\n\t\tfont-size: 18rpx;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.product-con .footer .item .iconfont {\r\n\t\ttext-align: center;\r\n\t\tfont-size: 40rpx;\r\n\t}\r\n\r\n\t.product-con .footer .item .iconfont.icon-shoucang1 {\r\n\t\tcolor: #f00;\r\n\t}\r\n\r\n\t.product-con .footer .item .iconfont.icon-gouwuche1 {\r\n\t\tfont-size: 40rpx;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.product-con .footer .item .iconfont.icon-gouwuche1 .num {\r\n\t\tcolor: #fff;\r\n\t\tposition: absolute;\r\n\t\tfont-size: 18rpx;\r\n\t\tpadding: 2rpx 8rpx 3rpx;\r\n\t\tborder-radius: 200rpx;\r\n\t\ttop: -10rpx;\r\n\t\tright: -10rpx;\r\n\t}\r\n\r\n\t.product-con .footer .bnt {\r\n\t\twidth: 540rpx;\r\n\t\theight: 76rpx;\r\n\t}\r\n\r\n\t.product-con .footer .bnt .bnts {\r\n\t\twidth: 270rpx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 76rpx;\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n\r\n\t.product-con .footer .bnt .joinCart {\r\n\t\tborder-radius: 50rpx 0 0 50rpx;\r\n\t\tbackground-image: linear-gradient(to right, #fea10f 0%, #fa8013 100%);\r\n\t}\r\n\r\n\t.product-con .footer .bnt .buy {\r\n\t\tborder-radius: 0 50rpx 50rpx 0;\r\n\t\tbackground-image: linear-gradient(to right, #fa6514 0%, #c9ab79 100%);\r\n\t}\r\n\r\n\t.setCollectBox {\r\n\t\tfont-size: 18rpx;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.bg-color-hui {\r\n\t\tbackground: #bbbbbb !important;\r\n\t}\r\n\t.canvas {\r\n\t\tposition:fixed;\r\n\t\tz-index: -5;\r\n\t\topacity: 0;\r\n\t}\r\n\t\r\n\t.poster-pop {\r\n\t\twidth: 450rpx;\r\n\t\theight: 714rpx;\r\n\t\tposition: fixed;\r\n\t\tleft: 50%;\r\n\t\ttransform: translateX(-50%);\r\n\t\tz-index: 99;\r\n\t\ttop: 50%;\r\n\t\tmargin-top: -357rpx;\r\n\t}\r\n\t\r\n\t.poster-pop image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tdisplay: block;\r\n\t}\r\n\t\r\n\t.poster-pop .close {\r\n\t\twidth: 46rpx;\r\n\t\theight: 75rpx;\r\n\t\tposition: fixed;\r\n\t\tright: 0;\r\n\t\ttop: -73rpx;\r\n\t\tdisplay: block;\r\n\t}\r\n\t\r\n\t.poster-pop .save-poster {\r\n\t\tbackground-color: #df2d0a;\r\n\t\tfont-size: ：22rpx;\r\n\t\tcolor: #fff;\r\n\t\ttext-align: center;\r\n\t\theight: 76rpx;\r\n\t\tline-height: 76rpx;\r\n\t\twidth: 100%;\r\n\t}\r\n\t\r\n\t.poster-pop .keep {\r\n\t\tcolor: #fff;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 25rpx;\r\n\t\tmargin-top: 10rpx;\r\n\t}\r\n\t\r\n\t.mask {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.6);\r\n\t\tz-index: 9;\r\n\t}\r\n\t.home-nav {\r\n\t\t/* #ifdef H5 */\r\n\t\ttop: 20rpx !important;\r\n\t\t/* #endif */\r\n\t}\r\n\t\r\n\t.home-nav {\r\n\t\tcolor: #fff;\r\n\t\tposition: fixed;\r\n\t\tfont-size: 33rpx;\r\n\t\twidth: 56rpx;\r\n\t\theight: 56rpx;\r\n\t\tz-index: 999;\r\n\t\tleft: 33rpx;\r\n\t\tbackground: rgba(190, 190, 190, 0.5);\r\n\t\tborder-radius: 50%;\r\n\t\t&.on{\r\n\t\t\tbackground: unset;\r\n\t\t\tcolor: #333;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.home-nav .line {\r\n\t\twidth: 1rpx;\r\n\t\theight: 24rpx;\r\n\t\tbackground: rgba(255, 255, 255, 0.25);\r\n\t}\r\n\t\r\n\t.home-nav .icon-xiangzuo {\r\n\t\twidth: auto;\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=1620faf6&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=1620faf6&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363903322\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}