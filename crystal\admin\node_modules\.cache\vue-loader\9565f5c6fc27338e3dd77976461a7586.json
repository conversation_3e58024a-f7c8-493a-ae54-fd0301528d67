{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\userprocess.vue?vue&type=template&id=2370f41a", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\userprocess.vue", "mtime": 1753666157945}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"divBox relative\" },\n    [\n      _c(\n        \"el-form\",\n        { attrs: { inline: true, model: _vm.dataForm } },\n        [\n          _c(\n            \"el-form-item\",\n            [\n              _c(\n                \"el-button\",\n                {\n                  attrs: { type: \"primary\" },\n                  on: {\n                    click: function ($event) {\n                      return _vm.addOrUpdateHandle()\n                    },\n                  },\n                },\n                [_vm._v(\"新增\")]\n              ),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-form-item\",\n            { staticStyle: { float: \"right\" } },\n            [\n              _c(\n                \"el-button\",\n                {\n                  attrs: { type: \"primary\" },\n                  on: {\n                    click: function ($event) {\n                      return _vm.$router.go(-1)\n                    },\n                  },\n                },\n                [_vm._v(\"返回上一页\")]\n              ),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"div\",\n        { staticClass: \"block\" },\n        [\n          _c(\n            \"el-timeline\",\n            _vm._l(_vm.dataList, function (item) {\n              return _c(\n                \"el-timeline-item\",\n                {\n                  attrs: {\n                    timestamp: item.doTime || item.addTime,\n                    placement: \"top\",\n                  },\n                },\n                [\n                  _c(\"el-card\", [\n                    _c(\n                      \"div\",\n                      {\n                        staticStyle: {\n                          height: \"30px\",\n                          \"line-height\": \"30px\",\n                          \"font-size\": \"20px\",\n                          \"font-weight\": \"bold\",\n                        },\n                      },\n                      [_vm._v(_vm._s(item.name))]\n                    ),\n                    _vm._v(\" \"),\n                    _c(\n                      \"div\",\n                      {\n                        staticStyle: {\n                          height: \"30px\",\n                          \"line-height\": \"30px\",\n                          \"font-size\": \"18px\",\n                          \"font-weight\": \"bold\",\n                        },\n                      },\n                      [_vm._v(\"当前用户状态：\" + _vm._s(item.userStatus))]\n                    ),\n                    _vm._v(\" \"),\n                    _c(\"div\", {\n                      staticStyle: { \"margin-top\": \"10px\" },\n                      domProps: { innerHTML: _vm._s(item.remarks) },\n                    }),\n                  ]),\n                ],\n                1\n              )\n            }),\n            1\n          ),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _vm.addOrUpdateVisible\n        ? _c(\"add-or-update\", {\n            ref: \"addOrUpdate\",\n            on: { refreshDataList: _vm.getDataList },\n          })\n        : _vm._e(),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}