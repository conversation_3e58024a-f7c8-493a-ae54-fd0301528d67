{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\FormGenerator\\index\\JsonDrawer.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\FormGenerator\\index\\JsonDrawer.vue", "mtime": 1753666157770}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport { beautifierConf } from '../utils/index'\r\nimport ClipboardJS from 'clipboard'\r\nimport { saveAs } from 'file-saver'\r\nimport loadMonaco from '../utils/loadMonaco'\r\nimport loadBeautifier from '../utils/loadBeautifier'\r\n// import * as monaco from \"monaco-editor\";\r\n\r\nlet beautifier\r\nlet monaco\r\n\r\nexport default {\r\n  components: {},\r\n  props: {\r\n    jsonStr: {\r\n      type: String,\r\n      required: true,\r\n      beautifier: null,\r\n      jsonEditor: null\r\n    }\r\n  },\r\n  data() {\r\n    return {}\r\n  },\r\n  computed: {},\r\n  watch: {},\r\n  created() {},\r\n  mounted() {\r\n    window.addEventListener('keydown', this.preventDefaultSave)\r\n    const clipboard = new ClipboardJS('.copy-json-btn', {\r\n      text: trigger => {\r\n        this.$notify({\r\n          title: '成功',\r\n          message: '代码已复制到剪切板，可粘贴。',\r\n          type: 'success'\r\n        })\r\n        return this.beautifierJson\r\n      }\r\n    })\r\n    clipboard.on('error', e => {\r\n      this.$message.error('代码复制失败')\r\n    })\r\n  },\r\n  beforeDestroy() {\r\n    window.removeEventListener('keydown', this.preventDefaultSave)\r\n  },\r\n  methods: {\r\n    preventDefaultSave(e) {\r\n      if (e.key === 's' && (e.metaKey || e.ctrlKey)) {\r\n        e.preventDefault()\r\n      }\r\n    },\r\n    onOpen() {\r\n      loadBeautifier(btf => {\r\n        beautifier = btf\r\n        this.beautifierJson = beautifier.js(this.jsonStr, beautifierConf.js)\r\n\r\n        loadMonaco(val => {\r\n          monaco = val\r\n          this.setEditorValue('editorJson', this.beautifierJson)\r\n        })\r\n      })\r\n      // monaco.editor.create(document.getElementById(\"editorJson\"), {\r\n      //   value: [\r\n      //     \"function x() {\",\r\n      //     '\\tconsole.integralLog(\"Hello world!\");',\r\n      //     \"}\"].join(\r\n      //     \"\\n\"\r\n      //   ),\r\n      //   language: \"javascript\",\r\n      //   theme: 'vs-dark',\r\n      //   automaticLayout: true\r\n      // });\r\n    },\r\n    onClose() {},\r\n    setEditorValue(id, codeStr) {\r\n      if (this.jsonEditor) {\r\n        this.jsonEditor.setValue(codeStr)\r\n      } else {\r\n        this.jsonEditor = monaco.editor.create(document.getElementById(id), {\r\n          value: codeStr,\r\n          theme: 'vs-dark',\r\n          language: 'json',\r\n          automaticLayout: true\r\n        })\r\n        // ctrl + s 刷新\r\n        this.jsonEditor.onKeyDown(e => {\r\n          if (e.keyCode === 49 && (e.metaKey || e.ctrlKey)) {\r\n            this.refresh()\r\n          }\r\n        })\r\n      }\r\n    },\r\n    exportJsonFile() {\r\n      this.$prompt('文件名:', '导出文件', {\r\n        inputValue: `${+new Date()}.json`,\r\n        closeOnClickModal: false,\r\n        inputPlaceholder: '请输入文件名'\r\n      }).then(({ value }) => {\r\n        if (!value) value = `${+new Date()}.json`\r\n        const codeStr = this.jsonEditor.getValue()\r\n        const blob = new Blob([codeStr], { type: 'text/plain;charset=utf-8' })\r\n        saveAs(blob, value)\r\n      })\r\n    },\r\n    refresh() {\r\n      try {\r\n        this.$emit('refresh', JSON.parse(this.jsonEditor.getValue()))\r\n      } catch (error) {\r\n        this.$notify({\r\n          title: '错误',\r\n          message: 'JSON格式错误，请检查',\r\n          type: 'error'\r\n        })\r\n      }\r\n    }\r\n  }\r\n}\r\n", null]}