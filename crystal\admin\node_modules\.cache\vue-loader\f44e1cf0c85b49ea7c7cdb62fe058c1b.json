{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\Category\\edit.vue?vue&type=template&id=70f2820a&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\Category\\edit.vue", "mtime": 1753666157755}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\n<div>\n  <el-form ref=\"editPram\" :model=\"editPram\" label-width=\"130px\">\n    <el-form-item\n      label=\"分类名称\"\n      prop=\"name\"\n      :rules=\"[{ required:true,message:'请输入分类名称',trigger:['blur','change'] }]\"\n    >\n      <el-input v-model=\"editPram.name\"  :maxlength=\"biztype.value === 1 ? 8 : 20\" placeholder=\"分类名称\" />\n    </el-form-item>\n    <el-form-item label=\"URL\" v-if=\"biztype.value!==1 && biztype.value!==3 && biztype.value!==8\">\n      <el-input v-model=\"editPram.url\" placeholder=\"URL\" />\n    </el-form-item>\n    <el-form-item label=\"父级\" v-if=\"biztype.value!==3 \">\n      <el-cascader v-model=\"editPram.pid\" :disabled=\"isCreate ===1 && editPram.pid ===0\" :options=\"biztype.value === 5 ? allTreeList : parentOptions\" :props=\"categoryProps\" style=\"width:100%\" />\n    </el-form-item>\n    <el-form-item label=\"菜单图标\" v-if=\"biztype.value===5\">\n      <el-input placeholder=\"请选择菜单图标\" v-model=\"editPram.extra\">\n        <el-button slot=\"append\" icon=\"el-icon-circle-plus-outline\" @click=\"addIcon\"></el-button>\n      </el-input>\n    </el-form-item>\n    <el-form-item label=\"分类图标(180*180)\" v-if=\"biztype.value === 1 || biztype.value === 3\">\n      <div class=\"upLoadPicBox\" @click=\"modalPicTap('1')\">\n        <div v-if=\"editPram.extra\" class=\"pictrue\">\n          <img :src=\"editPram.extra\">\n        </div>\n        <div v-else class=\"upLoad\">\n          <i class=\"el-icon-camera cameraIconfont\" />\n        </div>\n      </div>\n    </el-form-item>\n    <el-form-item label=\"排序\">\n      <el-input-number v-model=\"editPram.sort\" :min=\"0\"/>\n    </el-form-item>\n    <el-form-item label=\"状态\">\n      <el-switch v-model=\"editPram.status\"  active-text=\"显示\"\n                 inactive-text=\"隐藏\" :active-value=\"true\" :inactive-value=\"false\" />\n    </el-form-item>\n    <el-form-item label=\"扩展字段\" v-if=\"biztype.value !== 1 && biztype.value !== 3 && biztype.value !== 5 && biztype.value!==8\">\n      <el-input v-model=\"editPram.extra\" type=\"textarea\" placeholder=\"扩展字段\" />\n    </el-form-item>\n    <el-form-item>\n      <el-button type=\"primary\" :loading=\"loadingBtn\" @click=\"handlerSubmit('editPram')\" v-hasPermi=\"['admin:category:update']\">确定</el-button>\n      <el-button @click=\"close\">取消</el-button>\n    </el-form-item>\n  </el-form>\n</div>\n", null]}