{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\order\\orderDetail.vue?vue&type=style&index=0&id=4328fb58&scoped=true&lang=scss", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\order\\orderDetail.vue", "mtime": 1753666157910}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\css-loader\\index.js", "mtime": 1753666298053}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753666301105}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753666299466}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1753666297707}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n.logistics {\n  align-items: center;\n  padding: 10px 0px;\n  .logistics_img {\n    width: 45px;\n    height: 45px;\n    margin-right: 12px;\n    img {\n      width: 100%;\n      height: 100%;\n    }\n  }\n  .logistics_cent {\n    span {\n      display: block;\n      font-size: 12px;\n    }\n  }\n}\n\n.trees-coadd {\n  width: 100%;\n  height: 400px;\n  border-radius: 4px;\n  overflow: hidden;\n  .scollhide {\n    width: 100%;\n    height: 100%;\n    overflow: auto;\n    margin-left: 18px;\n    padding: 10px 0 10px 0;\n    box-sizing: border-box;\n    .content {\n      font-size: 12px;\n    }\n\n    .time {\n      font-size: 12px;\n      color: #2d8cf0;\n    }\n\n  }\n}\n\n.title {\n  margin-bottom: 14px;\n  color: #303133;\n  font-weight: 500;\n  font-size: 14px;\n}\n\n.description {\n  &-term {\n    display: table-cell;\n    padding-bottom: 5px;\n    line-height: 20px;\n    width: 50%;\n    font-size: 12px;\n    color: #606266;\n  }\n  ::v-deep .el-divider--horizontal {\n    margin: 12px 0 !important;\n  }\n}\n\n", null]}