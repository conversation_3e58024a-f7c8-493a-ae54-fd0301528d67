{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/user_cash/index.vue?e1ed", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/user_cash/index.vue?83c4", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/user_cash/index.vue?698b", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/user_cash/index.vue?bcee", "uni-app:///pages/users/user_cash/index.vue", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/user_cash/index.vue?7b41", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/user_cash/index.vue?a315"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "authorize", "data", "navList", "currentTab", "index", "array", "minPrice", "userInfo", "isClone", "isAuto", "isShowAuth", "commission", "qrcodeUrlW", "qrcodeUrlZ", "isCommitted", "computed", "watch", "is<PERSON>ogin", "handler", "deep", "onLoad", "methods", "uploadpic", "that", "url", "name", "model", "pid", "DelPicW", "DelPicZ", "onLoadFun", "getExtractUser", "auth<PERSON><PERSON><PERSON>", "getUserExtractBank", "swichNav", "bindPickerChange", "moneyInput", "e", "subCash", "value", "title", "icon", "tab"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACmM;AACnM,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAkwB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACoHtxB;AAKA;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAMA;EACAC;IAEAC;EAEA;EACAC;IACA;MACAC;QACA;QACA;MACA,GACA;QACA;QACA;MACA,GACA;QACA;QACA;MACA,EACA;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;;EACAC;EACAC;IACAC;MACAC;QACA;UACA;UACA;QACA;MACA;MACAC;IACA;EACA;EACAC;IACA;MACA;MACA;IACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;QACA;UACAJ;QACA;UACAA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAK;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;QACA;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;QACA;QACA5B;QACAkB;MACA;IACA;IACAW;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;MACAC;MACA;MACA;QACA;MACA;IAEA;IACAC;MAAA;MACA;QACAC;MACA;QAAA;QACA;UACAC;QACA;QACA;UACAA;QACA;QACA;UACAA;QACA;QACAD;QACAA;MACA;QAAA;QACAA;QACA;UACAC;QACA;QACAD;QACAA;MACA;QAAA;QACAA;QACA;UACAC;QACA;QACAD;QACAA;MACA;MACA;QACAC;MACA;MACA;QACAA;MACA;MACA;QACAA;MACA;MACA;QACA;QACA;UACA;YACAA;YACAC;UACA;YAAAC;YAAAlB;UAAA;UACA;QACA;UACA;UACA;YACAgB;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC/SA;AAAA;AAAA;AAAA;AAA66C,CAAgB,4tCAAG,EAAC,C;;;;;;;;;;;ACAj8C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/users/user_cash/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/users/user_cash/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=8d40bc8c&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/users/user_cash/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=8d40bc8c&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view class='cash-withdrawal'>\r\n\t\t\t<view class='nav acea-row'>\r\n\t\t\t\t<view v-for=\"(item,index) in navList\" :key=\"index\" class='item font-color' @click=\"swichNav(index)\">\r\n\t\t\t\t\t<view class='line bg-color' :class='currentTab==index ? \"on\":\"\"'></view>\r\n\t\t\t\t\t<view class='iconfont' :class='item.icon+\" \"+(currentTab==index ? \"on\":\"\")'></view>\r\n\t\t\t\t\t<view>{{item.name}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class='wrapper'>\r\n\t\t\t\t<view :hidden='currentTab != 0' class='list'>\r\n\t\t\t\t\t<form @submit=\"subCash\" report-submit='true'>\r\n\t\t\t\t\t\t<view class='item acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t\t<view class='name'>持卡人</view>\r\n\t\t\t\t\t\t\t<view class='input'><input placeholder='请输入持卡人姓名' placeholder-class='placeholder' name=\"name\"></input></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='item acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t\t<view class='name'>卡号</view>\r\n\t\t\t\t\t\t\t<view class='input'><input type='number' placeholder='请填写卡号' placeholder-class='placeholder' name=\"cardum\"></input></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='item acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t\t<view class='name'>银行</view>\r\n\t\t\t\t\t\t\t<view class='input'>\r\n\t\t\t\t\t\t\t\t<picker @change=\"bindPickerChange\" :value=\"index\" :range=\"array\">\r\n\t\t\t\t\t\t\t\t\t<text class='Bank'>{{array[index]}}</text>\r\n\t\t\t\t\t\t\t\t\t<text class='iconfont icon-qiepian38'></text>\r\n\t\t\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='item acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t\t<view class='name'>提现</view>\r\n\t\t\t\t\t\t\t<view class='input'><input :placeholder='\"最低提现金额\"+minPrice' placeholder-class='placeholder' name=\"money\" type='digit'></input></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='tip'>\r\n\t\t\t\t\t\t\t当前可提现金额: <text class=\"price\">￥{{commission.commissionCount}},</text>冻结佣金：￥{{commission.brokenCommission}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='tip'>\r\n\t\t\t\t\t\t\t说明: 每笔佣金的冻结期为{{commission.brokenDay}}天，到期后可提现\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<button formType=\"submit\" class='bnt bg-color'>提现</button>\r\n\t\t\t\t\t</form>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view :hidden='currentTab != 1' class='list'>\r\n\t\t\t\t\t<form @submit=\"subCash\" report-submit='true'>\r\n\t\t\t\t\t\t<view class='item acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t\t<view class='name'>账号</view>\r\n\t\t\t\t\t\t\t<view class='input'><input placeholder='请填写您的微信账号' placeholder-class='placeholder' name=\"name\"></input></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='item acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t\t<view class='name'>提现</view>\r\n\t\t\t\t\t\t\t<view class='input'><input :placeholder='\"最低提现金额\"+minPrice' placeholder-class='placeholder' name=\"money\" type='digit'></input></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='item acea-row row-top row-between'>\r\n\t\t\t\t\t\t\t<view class='name'>收款码</view>\r\n\t\t\t\t\t\t\t<view class=\"input acea-row\">\r\n\t\t\t\t\t\t\t\t<view class=\"picEwm\" v-if=\"qrcodeUrlW\">\r\n\t\t\t\t\t\t\t\t\t<image :src=\"qrcodeUrlW\"></image>\r\n\t\t\t\t\t\t\t\t\t<text class='iconfont icon-guanbi1 font-color' @click='DelPicW'></text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class='pictrue acea-row row-center-wrapper row-column' @click='uploadpic(\"W\")' v-else>\r\n\t\t\t\t\t\t\t\t  <text class='iconfont icon-icon25201'></text>\r\n\t\t\t\t\t\t\t\t  <view>上传图片</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='tip'>\r\n\t\t\t\t\t\t\t当前可提现金额: <text class=\"price\">￥{{commission.commissionCount}},</text>冻结佣金：￥{{commission.brokenCommission}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='tip'>\r\n\t\t\t\t\t\t\t说明: 每笔佣金的冻结期为{{commission.brokenDay}}天，到期后可提现\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<button formType=\"submit\" class='bnt bg-color'>提现</button>\r\n\t\t\t\t\t</form>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view :hidden='currentTab != 2' class='list'>\r\n\t\t\t\t\t<form @submit=\"subCash\" report-submit='true'>\r\n\t\t\t\t\t\t<view class='item acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t\t<view class='name'>账号</view>\r\n\t\t\t\t\t\t\t<view class='input'><input placeholder='请填写您的支付宝账号' placeholder-class='placeholder' name=\"name\"></input></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='item acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t\t<view class='name'>提现</view>\r\n\t\t\t\t\t\t\t<view class='input'><input :placeholder='\"最低提现金额\"+minPrice' placeholder-class='placeholder' name=\"money\" type='digit'></input></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='item acea-row row-top row-between'>\r\n\t\t\t\t\t\t\t<view class='name'>收款码</view>\r\n\t\t\t\t\t\t\t<view class=\"input acea-row\">\r\n\t\t\t\t\t\t\t\t<view class=\"picEwm\" v-if=\"qrcodeUrlZ\">\r\n\t\t\t\t\t\t\t\t\t<image :src=\"qrcodeUrlZ\"></image>\r\n\t\t\t\t\t\t\t\t\t<text class='iconfont icon-guanbi1 font-color' @click='DelPicZ'></text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class='pictrue acea-row row-center-wrapper row-column' @click='uploadpic(\"Z\")' v-else>\r\n\t\t\t\t\t\t\t\t  <text class='iconfont icon-icon25201'></text>\r\n\t\t\t\t\t\t\t\t  <view>上传图片</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='tip'>\r\n\t\t\t\t\t\t\t当前可提现金额: <text class=\"price\">￥{{commission.commissionCount}},</text>冻结佣金：￥{{commission.brokenCommission}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='tip'>\r\n\t\t\t\t\t\t\t说明: 每笔佣金的冻结期为{{commission.brokenDay}}天，到期后可提现\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<button formType=\"submit\" class='bnt bg-color'>提现</button>\r\n\t\t\t\t\t</form>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- #ifdef MP -->\r\n\t\t<!-- <authorize @onLoadFun=\"onLoadFun\" :isAuto=\"isAuto\" :isShowAuth=\"isShowAuth\" @authColse=\"authColse\"></authorize> -->\r\n\t\t<!-- #endif -->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\textractCash,\r\n\t\textractBank,\r\n\t\textractUser\r\n\t} from '@/api/user.js';\r\n\timport {\r\n\t\ttoLogin\r\n\t} from '@/libs/login.js';\r\n\timport {\r\n\t\tmapGetters\r\n\t} from \"vuex\";\r\n\t// #ifdef MP\r\n\timport authorize from '@/components/Authorize';\r\n\t// #endif\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\t// #ifdef MP\r\n\t\t\tauthorize\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tnavList: [{\r\n\t\t\t\t\t\t'name': '银行卡',\r\n\t\t\t\t\t\t'icon': 'icon-yinhangqia'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\t'name': '微信',\r\n\t\t\t\t\t\t'icon': 'icon-weixin2'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\t'name': '支付宝',\r\n\t\t\t\t\t\t'icon': 'icon-icon34'\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\tcurrentTab: 0,\r\n\t\t\t\tindex: 0,\r\n\t\t\t\tarray: [], //提现银行\r\n\t\t\t\tminPrice: 0.00, //最低提现金额\r\n\t\t\t\tuserInfo: [],\r\n\t\t\t\tisClone: false,\r\n\t\t\t\tisAuto: false, //没有授权的不会自动授权\r\n\t\t\t\tisShowAuth: false, //是否隐藏授权\r\n\t\t\t\tcommission:{},\r\n\t\t\t\tqrcodeUrlW:\"\",\r\n\t\t\t\tqrcodeUrlZ:\"\",\r\n\t\t\t\tisCommitted: false //防止多次提交\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: mapGetters(['isLogin']),\r\n\t\twatch:{\r\n\t\t\tisLogin:{\r\n\t\t\t\thandler:function(newV,oldV){\r\n\t\t\t\t\tif(newV){\r\n\t\t\t\t\t\tthis.getUserExtractBank();\r\n\t\t\t\t\t\tthis.getExtractUser();\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tdeep:true\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tif (this.isLogin) {\r\n\t\t\t\tthis.getUserExtractBank();\r\n\t\t\t\tthis.getExtractUser();\r\n\t\t\t} else {\r\n\t\t\t\ttoLogin();\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tuploadpic: function (type) {\r\n\t\t\t  let that = this;\r\n\t\t\t  that.$util.uploadImageOne({\r\n\t\t\t  \turl: 'user/upload/image',\r\n\t\t\t  \tname: 'multipart',\r\n\t\t\t  \tmodel: \"user\",\r\n\t\t\t  \tpid: 1\r\n\t\t\t  }, function(res) {\r\n\t\t\t  \t if(type==='W'){\r\n\t\t\t  \t\t\tthat.qrcodeUrlW = res.data.url;\r\n\t\t\t  \t }else{\r\n\t\t\t  \t\t\tthat.qrcodeUrlZ = res.data.url;\r\n\t\t\t  \t }\r\n\t\t\t  });\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 删除图片\r\n\t\t\t * \r\n\t\t\t*/\r\n\t\t\tDelPicW: function () {\r\n\t\t\t  this.qrcodeUrlW = \"\";\r\n\t\t\t},\r\n\t\t\tDelPicZ: function () {\r\n\t\t\t  this.qrcodeUrlZ = \"\";\r\n\t\t\t},\r\n\t\t\tonLoadFun: function() {\r\n\t\t\t\tthis.getUserExtractBank();\r\n\t\t\t},\r\n\t\t\tgetExtractUser(){\r\n\t\t\t\textractUser().then(res=>{\r\n\t\t\t\t\tthis.commission = res.data;\r\n\t\t\t\t\tthis.minPrice = res.data.minPrice;\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 授权关闭\r\n\t\t\tauthColse: function(e) {\r\n\t\t\t\tthis.isShowAuth = e\r\n\t\t\t},\r\n\t\t\tgetUserExtractBank: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\textractBank().then(res => {\r\n\t\t\t\t\tlet array = res.data;\r\n\t\t\t\t\tarray.unshift(\"请选择银行\");\r\n\t\t\t\t\tthat.$set(that, 'array', array);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tswichNav: function(current) {\r\n\t\t\t\tthis.currentTab = current;\r\n\t\t\t},\r\n\t\t\tbindPickerChange: function(e) {\r\n\t\t\t\tthis.index = e.detail.value;\r\n\t\t\t},\r\n\t\t\tmoneyInput(e) {\r\n\t\t\t\t//正则表达试\r\n\t\t\t\t\t\t\t\te.target.value = (e.target.value.match(/^\\d*(\\.?\\d{0,2})/g)[0]) || null\r\n\t\t\t\t\t\t\t\t//重新赋值给input\r\n\t\t\t\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\t\t\t\tthis.money= e.target.value\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\tsubCash: function(e) {\r\n\t\t\t\tlet that = this,\r\n\t\t\t\t\tvalue = e.detail.value;\r\n\t\t\t\t\tif (that.currentTab == 0) { //银行卡\r\n\t\t\t\t\t\tif (value.name.length == 0) return this.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: '请填写持卡人姓名'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tif (value.cardum.length == 0) return this.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: '请填写卡号'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tif (that.index == 0) return this.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: \"请选择银行\"\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tvalue.extractType = 'bank';\r\n\t\t\t\t\t\tvalue.bankName = that.array[that.index];\r\n\t\t\t\t\t} else if (that.currentTab == 1) { //微信\r\n\t\t\t\t\t\tvalue.extractType = 'weixin';\r\n\t\t\t\t\t\tif (value.name.length == 0) return this.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: '请填写微信号'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tvalue.wechat = value.name;\r\n\t\t\t\t\t\tvalue.qrcodeUrl = that.qrcodeUrlW;\r\n\t\t\t\t\t} else if (that.currentTab == 2) { //支付宝\r\n\t\t\t\t\t\tvalue.extractType = 'alipay';\r\n\t\t\t\t\t\tif (value.name.length == 0) return this.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: '请填写账号'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tvalue.alipayCode = value.name;\r\n\t\t\t\t\t\tvalue.qrcodeUrl = that.qrcodeUrlZ;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (value.money.length == 0) return this.$util.Tips({\r\n\t\t\t\t\t\ttitle: '请填写提现金额'\r\n\t\t\t\t\t});\r\n\t\t\t\t\tif (!(/^(\\d?)+(\\.\\d{0,2})?$/.test(value.money))) return this.$util.Tips({\r\n\t\t\t\t\t\ttitle: '提现金额保留2位小数'\r\n\t\t\t\t\t});\r\n\t\t\t\t\tif (value.money < that.minPrice) return this.$util.Tips({\r\n\t\t\t\t\t\ttitle: '提现金额不能低于' + that.minPrice\r\n\t\t\t\t\t});\r\n\t\t\t\t\tif(this.isCommitted==false){\r\n\t\t\t\t\t\t  this.isCommitted=true;\r\n\t\t\t\t\textractCash(value).then(res => {\r\n\t\t\t\t\t\treturn this.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: \"提现成功\",\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t},{ tab: 2, url: '/pages/users/user_spread_user/index' });\r\n\t\t\t\t\t\tthis.isCommitted=false;\r\n\t\t\t\t\t}).catch(err => {\r\n\t\t\t\t\t\t this.isCommitted=false;\r\n\t\t\t\t\t\treturn this.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\tpage {\r\n\t\tbackground-color: #fff !important;\r\n\t}\r\n\t\r\n\t.cash-withdrawal .nav {\r\n\t\theight: 130rpx;\r\n\t\tbox-shadow: 0 10rpx 10rpx #f8f8f8;\r\n\t}\r\n\t\r\n\t.cash-withdrawal .nav .item {\r\n\t\tfont-size: 26rpx;\r\n\t\tflex: 1;\r\n\t\ttext-align: center;\r\n\t}\r\n\t\r\n\t.cash-withdrawal .nav .item~.item {\r\n\t\tborder-left: 1px solid #f0f0f0;\r\n\t}\r\n\t\r\n\t.cash-withdrawal .nav .item .iconfont {\r\n\t\twidth: 40rpx;\r\n\t\theight: 40rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tborder: 2rpx solid $theme-color;\r\n\t\ttext-align: center;\r\n\t\tline-height: 37rpx;\r\n\t\tmargin: 0 auto 6rpx auto;\r\n\t\tfont-size: 22rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\t\r\n\t.cash-withdrawal .nav .item .iconfont.on {\r\n\t\tbackground-color: $theme-color;\r\n\t\tcolor: #fff;\r\n\t\tborder-color: $theme-color;\r\n\t}\r\n\t\r\n\t.cash-withdrawal .nav .item .line {\r\n\t\twidth: 2rpx;\r\n\t\theight: 20rpx;\r\n\t\tmargin: 0 auto;\r\n\t\ttransition: height 0.3s;\r\n\t}\r\n\t\r\n\t.cash-withdrawal .nav .item .line.on {\r\n\t\theight: 39rpx;\r\n\t}\r\n\t\r\n\t.cash-withdrawal .wrapper .list {\r\n\t\tpadding: 0 30rpx;\r\n\t}\r\n\t\r\n\t.cash-withdrawal .wrapper .list .item {\r\n\t\tborder-bottom: 1rpx solid #eee;\r\n\t\tmin-height: 28rpx;\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #333;\r\n\t\tpadding: 39rpx 0;\r\n\t}\r\n\t\r\n\t.cash-withdrawal .wrapper .list .item .name {\r\n\t\twidth: 130rpx;\r\n\t}\r\n\t\r\n\t.cash-withdrawal .wrapper .list .item .input {\r\n\t\twidth: 505rpx;\r\n\t}\r\n\t\r\n\t.cash-withdrawal .wrapper .list .item .input .placeholder {\r\n\t\tcolor: #bbb;\r\n\t}\r\n\t\r\n\t.cash-withdrawal .wrapper .list .item .picEwm,.cash-withdrawal .wrapper .list .item .pictrue{\r\n\t\twidth:140rpx;\r\n\t\theight:140rpx;\r\n\t\tborder-radius:3rpx;\r\n\t\tposition: relative;\r\n\t\tmargin-right: 23rpx;\r\n\t}\r\n\t\r\n\t.cash-withdrawal .wrapper .list .item .picEwm image{\r\n\t\twidth:100%;\r\n\t\theight:100%;\r\n\t\tborder-radius:3rpx;\r\n\t}\r\n\t\r\n\t.cash-withdrawal .wrapper .list .item .picEwm .icon-guanbi1{\r\n\t\tposition:absolute;\r\n\t\tright: -14rpx;\r\n\t\ttop: -16rpx;\r\n\t\tfont-size:40rpx;\r\n\t}\r\n\t\r\n\t.cash-withdrawal .wrapper .list .item .pictrue{\r\n\t\tborder:1px solid rgba(221,221,221,1);\r\n\t\tfont-size:22rpx;\r\n\t\tcolor: #BBBBBB;\r\n\t}\r\n\t\r\n\t.cash-withdrawal .wrapper .list .item .pictrue .icon-icon25201{\r\n\t\tfont-size: 47rpx;\r\n\t\tcolor: #DDDDDD;\r\n\t\tmargin-bottom: 3px;\r\n\t}\r\n\t\r\n\t.cash-withdrawal .wrapper .list .tip {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #999;\r\n\t\tmargin-top: 25rpx;\r\n\t}\r\n\t\r\n\t.cash-withdrawal .wrapper .list .bnt {\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor: #fff;\r\n\t\twidth: 690rpx;\r\n\t\theight: 90rpx;\r\n\t\ttext-align: center;\r\n\t\tborder-radius: 50rpx;\r\n\t\tline-height: 90rpx;\r\n\t\tmargin: 64rpx auto;\r\n\t}\r\n\t\r\n\t.cash-withdrawal .wrapper .list .tip2 {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #999;\r\n\t\ttext-align: center;\r\n\t\tmargin: 44rpx 0 20rpx 0;\r\n\t}\r\n\t\r\n\t.cash-withdrawal .wrapper .list .value {\r\n\t\theight: 135rpx;\r\n\t\tline-height: 135rpx;\r\n\t\tborder-bottom: 1rpx solid #eee;\r\n\t\twidth: 690rpx;\r\n\t\tmargin: 0 auto;\r\n\t}\r\n\t\r\n\t.cash-withdrawal .wrapper .list .value input {\r\n\t\tfont-size: 80rpx;\r\n\t\tcolor: #282828;\r\n\t\theight: 135rpx;\r\n\t\ttext-align: center;\r\n\t}\r\n\t\r\n\t.cash-withdrawal .wrapper .list .value .placeholder2 {\r\n\t\tcolor: #bbb;\r\n\t}\r\n\t\r\n\t.price {\r\n\t\tcolor: $theme-color;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363903648\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}