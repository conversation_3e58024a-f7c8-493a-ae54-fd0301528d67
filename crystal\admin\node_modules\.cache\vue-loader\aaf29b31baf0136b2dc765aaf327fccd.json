{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\FormGenerator\\index\\FormDrawer.vue?vue&type=style&index=0&id=14fdd522&lang=scss&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\FormGenerator\\index\\FormDrawer.vue", "mtime": 1753666157769}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\css-loader\\index.js", "mtime": 1753666298053}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753666301105}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753666299466}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1753666297707}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\r\n@import '../styles/mixin';\r\n.tab-editor {\r\n  position: absolute;\r\n  top: 33px;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  font-size: 14px;\r\n}\r\n.left-editor {\r\n  position: relative;\r\n  height: 100%;\r\n  background: #1e1e1e;\r\n  overflow: hidden;\r\n}\r\n.setting{\r\n  position: absolute;\r\n  right: 15px;\r\n  top: 3px;\r\n  color: #a9f122;\r\n  font-size: 18px;\r\n  cursor: pointer;\r\n  z-index: 1;\r\n}\r\n.right-preview {\r\n  height: 100%;\r\n  .result-wrapper {\r\n    height: calc(100vh - 33px);\r\n    width: 100%;\r\n    overflow: auto;\r\n    padding: 12px;\r\n    box-sizing: border-box;\r\n  }\r\n}\r\n@include action-bar;\r\n::v-deep .el-drawer__header {\r\n  display: none;\r\n}\r\n", null]}