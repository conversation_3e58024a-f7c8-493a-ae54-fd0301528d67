{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\FormGenerator\\index\\RightPanel.vue", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\FormGenerator\\index\\RightPanel.vue", "mtime": 1753666157771}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\eslint-loader\\index.js", "mtime": 1753666298172}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./RightPanel.vue?vue&type=template&id=17795e15&scoped=true\"\nimport script from \"./RightPanel.vue?vue&type=script&lang=js\"\nexport * from \"./RightPanel.vue?vue&type=script&lang=js\"\nimport style0 from \"./RightPanel.vue?vue&type=style&index=0&id=17795e15&lang=scss&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"17795e15\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\crystal-mall\\\\crystal\\\\admin\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('17795e15')) {\n      api.createRecord('17795e15', component.options)\n    } else {\n      api.reload('17795e15', component.options)\n    }\n    module.hot.accept(\"./RightPanel.vue?vue&type=template&id=17795e15&scoped=true\", function () {\n      api.rerender('17795e15', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/components/FormGenerator/index/RightPanel.vue\"\nexport default component.exports"]}