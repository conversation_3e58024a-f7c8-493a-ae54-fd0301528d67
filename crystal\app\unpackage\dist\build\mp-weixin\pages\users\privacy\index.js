(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/users/privacy/index"],{"0c95":function(e,n,t){"use strict";t.r(n);var r=t("4e69"),c=t("1734");for(var o in c)["default"].indexOf(o)<0&&function(e){t.d(n,e,(function(){return c[e]}))}(o);t("c085");var u=t("828b"),a=Object(u["a"])(c["default"],r["b"],r["c"],!1,null,"9b037e02",null,!1,r["a"],void 0);n["default"]=a.exports},"14f7":function(e,n,t){},1734:function(e,n,t){"use strict";t.r(n);var r=t("fe0d"),c=t.n(r);for(var o in r)["default"].indexOf(o)<0&&function(e){t.d(n,e,(function(){return r[e]}))}(o);n["default"]=c.a},"4e69":function(e,n,t){"use strict";t.d(n,"b",(function(){return c})),t.d(n,"c",(function(){return o})),t.d(n,"a",(function(){return r}));var r={jyfParser:function(){return Promise.all([t.e("common/vendor"),t.e("components/jyf-parser/jyf-parser")]).then(t.bind(null,"b29e"))}},c=function(){var e=this.$createElement;this._self._c},o=[]},c085:function(e,n,t){"use strict";var r=t("14f7"),c=t.n(r);c.a},ede1:function(e,n,t){"use strict";(function(e,n){var r=t("47a9");t("5c2d");r(t("3240"));var c=r(t("0c95"));e.__webpack_require_UNI_MP_PLUGIN__=t,n(c.default)}).call(this,t("3223")["default"],t("df3c")["createPage"])},fe0d:function(e,n,t){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var r=t("5904"),c={components:{"jyf-parser":function(){Promise.all([t.e("common/vendor"),t.e("components/jyf-parser/jyf-parser")]).then(function(){return resolve(t("b29e"))}.bind(null,t)).catch(t.oe)}},data:function(){return{tagStyle:{img:"width:100%;display:block;",table:"width:100%",video:"width:100%"},content:""}},mounted:function(){var e=this;(0,r.getUserAgreement)().then((function(n){e.content=n.data.content})).catch((function(e){that.$util.Tips({title:e.msg})}))}};n.default=c}},[["ede1","common/runtime","common/vendor"]]]);