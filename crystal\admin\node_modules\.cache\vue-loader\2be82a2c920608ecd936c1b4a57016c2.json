{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\Category\\edit.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\Category\\edit.vue", "mtime": 1753666157755}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport * as categoryApi from '@/api/categoryApi.js'\r\nimport * as selfUtil from '@/utils/ZBKJIutil.js'\r\nexport default {\r\n  // name: \"edit\"\r\n  props: {\r\n    prent: {\r\n      type: Object,\r\n      required: true\r\n    },\r\n    isCreate: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    editData: {\r\n      type: Object\r\n    },\r\n    biztype: {\r\n      type: Object,\r\n      required: true\r\n    },\r\n    allTreeList: {\r\n      type: Array\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      loadingBtn: false,\r\n      constants: this.$constants,\r\n      editPram: {\r\n        extra: null,\r\n        name: null,\r\n        pid: null,\r\n        sort: 0,\r\n        status: true,\r\n        type: this.biztype.value,\r\n        url: null,\r\n        id: 0\r\n      },\r\n      categoryProps: {\r\n        value: 'id',\r\n        label: 'name',\r\n        children: 'child',\r\n        expandTrigger: 'hover',\r\n        checkStrictly: true,\r\n        emitPath: false\r\n      },\r\n      parentOptions: []\r\n    }\r\n  },\r\n  mounted() {\r\n    this.initEditData()\r\n  },\r\n  methods: {\r\n    // 点击图标\r\n    addIcon() {\r\n      const _this = this\r\n      _this.$modalIcon(function(icon) {\r\n        _this.editPram.extra = icon\r\n      })\r\n    },\r\n    // 点击商品图\r\n    modalPicTap (tit, num, i) {\r\n      const _this = this\r\n      const attr = []\r\n      this.$modalUpload(function(img) {\r\n        if(tit==='1'&& !num){\r\n          _this.editPram.extra = img[0].sattDir\r\n        }\r\n        if(tit==='2'&& !num){\r\n          img.map((item) => {\r\n            attr.push(item.attachment_src)\r\n            _this.formValidate.slider_image.push(item)\r\n          });\r\n        }\r\n      },tit, 'store')\r\n    },\r\n    close() {\r\n      this.$emit('hideEditDialog')\r\n    },\r\n    initEditData() {\r\n      this.parentOptions = [...this.allTreeList]\r\n      this.addTreeListLabelForCasCard(this.parentOptions, 'child')\r\n      const { extra, name, pid, sort, status, type, id, url } = this.editData\r\n      if(this.isCreate === 1){\r\n        this.editPram.extra = extra\r\n        this.editPram.name = name\r\n        this.editPram.pid = pid\r\n        this.editPram.sort = sort\r\n        this.editPram.status = status\r\n        this.editPram.type = type\r\n        this.editPram.url = url\r\n        this.editPram.id = id\r\n      }else{\r\n        this.editPram.pid = this.prent.id\r\n        this.editPram.type = this.biztype.value\r\n      }\r\n    },\r\n    addTreeListLabelForCasCard(arr, child) {\r\n      arr.forEach((o,i) => {\r\n        if (o.child && o.child.length) {\r\n          // o.disabled = true\r\n          o.child.forEach((j) => {\r\n            j.disabled = true\r\n          })\r\n        }\r\n      })\r\n    },\r\n    handlerSubmit(formName) {\r\n      this.$refs[formName].validate((valid) => {\r\n        if (!valid) return\r\n        this.handlerSaveOrUpdate(this.isCreate === 0)\r\n      })\r\n    },\r\n    handlerSaveOrUpdate(isSave) {\r\n      if (isSave) {\r\n       // this.editPram.pid = this.prent.id\r\n        this.loadingBtn = true\r\n        categoryApi.addCategroy(this.editPram).then(data => {\r\n          this.$emit('hideEditDialog')\r\n          this.$message.success('创建目录成功')\r\n          this.loadingBtn = false\r\n        }).catch(() => {\r\n          this.loadingBtn = false\r\n        })\r\n      } else {\r\n        this.editPram.pid = Array.isArray(this.editPram.pid) ? this.editPram.pid[0] : this.editPram.pid\r\n        this.loadingBtn = true\r\n        categoryApi.updateCategroy(this.editPram).then(data => {\r\n          this.$emit('hideEditDialog')\r\n          this.$message.success('更新目录成功')\r\n          this.loadingBtn = false\r\n        }).catch(() => {\r\n          this.loadingBtn = false\r\n        })\r\n      }\r\n    }\r\n  }\r\n}\r\n", null]}