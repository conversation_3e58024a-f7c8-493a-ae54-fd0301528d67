{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/uni_modules/shmily-drag-image/components/shmily-drag-image/shmily-drag-image.vue?3a17", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/uni_modules/shmily-drag-image/components/shmily-drag-image/shmily-drag-image.vue?c160", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/uni_modules/shmily-drag-image/components/shmily-drag-image/shmily-drag-image.vue?855b", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/uni_modules/shmily-drag-image/components/shmily-drag-image/shmily-drag-image.vue?cdc5", "uni-app:///uni_modules/shmily-drag-image/components/shmily-drag-image/shmily-drag-image.vue", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/uni_modules/shmily-drag-image/components/shmily-drag-image/shmily-drag-image.vue?bee3", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/uni_modules/shmily-drag-image/components/shmily-drag-image/shmily-drag-image.vue?473d"], "names": ["emits", "props", "value", "type", "default", "modelValue", "keyName", "number", "imageWidth", "cols", "borderRadius", "padding", "scale", "opacity", "addImage", "delImage", "data", "imageList", "width", "add", "x", "y", "colsValue", "viewWidth", "tempItem", "timer", "changeStatus", "preStatus", "first", "computed", "areaHeight", "height", "console", "<PERSON><PERSON><PERSON><PERSON>", "watch", "handler", "flag", "deep", "created", "mounted", "query", "list", "methods", "getSrc", "onChange", "item", "absY", "obj", "setTimeout", "change", "touchstart", "v", "clearTimeout", "touchend", "previewImage", "uni", "urls", "current", "success", "fail", "mouseenter", "mouseleave", "addImages", "count", "sourceType", "delImages", "delImageHandle", "delImageMp", "sortList", "result", "addProperties", "src", "oldX", "oldY", "absX", "zIndex", "index", "id", "disable", "offset", "moveEnd", "nothing", "rpx2px", "guid", "uuid"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0I;AAC1I;AACqE;AACL;AACsC;;;AAGtG;AACsM;AACtM,gBAAgB,2LAAU;AAC1B,EAAE,uFAAM;AACR,EAAE,wGAAM;AACR,EAAE,iHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA6xB,CAAgB,isBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBCqCjzB;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACA;IACAC;MACAF;MACAC;QACA;MACA;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;EACA;EACAY;IACA;MACAC;MACAC;MACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;QACAC;MACA;QACAA;MACA;MACAC;MACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACAhC;MACAiC;QACA;UACAH;UACA;UACA;YACA;cACA;cACA;YACA;YACA;cACAI;cACA;cACA;YACA;UACA;QACA;MACA;MACAC;IACA;IACAhC;MACA8B;QACA;UACAH;UACA;UACA;YACA;cACA;cACA;YACA;YACA;cACAI;cACA;cACA;YACA;UACA;QACA;MACA;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IAAA;IACA;IACAC;MACA;MACA;MACA;QACA;QACA;MACA;MACA;MAAA,2CAIAC;QAAA;MAAA;QAAA;UAAA;UACA;QACA;MAAA;QAAA;MAAA;QAAA;MAAA;MACA;IAEA;IACAD;EACA;EACAE;IACAC;MACA;QACA;MACA;MACA;IACA;IACAC;MAAA;MACA;MACAC;MACAA;MACA;QACA;UACAA,wGACAC;QACA;QACA;QACA;QACA;QACA;QACA;UACA;UAAA,4CACA;YAAA;UAAA;YAAA;cAAA;cACA;gBACA;cACA;gBACA;cACA;gBACAC;gBACAA;gBACAA;gBACAC;kBACA;oBACAD;oBACAA;kBACA;gBACA;cACA;YAAA;YAfA;cAAA;YAgBA;UAAA;YAAA;UAAA;YAAA;UAAA;UACAF;UACAA;UACAA;UACA;YACAG;cACA;gBACAH;gBACAA;cACA;YACA;UACA;UACA;UACA;QACA;MACA;IACA;IACAI;MAAA;MACAF;MACAA;MACAA;MACAA;MACAA;MACAA;MACAC;QACA;UACAD;UACAA;QACA;MACA;IACA;IACAG;MAAA;MACA;QACAC;MACA;MACAN;MACAA;MACA;MACA;QACAA;QACAA;QACAO;QACA;MACA;IACA;IACAC;MAAA;MACA;MACAR;MACAA;MACAA;MACAA;MACAA;MACAA;MACAG;QACA;UACAH;UACAA;UACA;UACA;QACA;QACA;MACA;MACA;IACA;IACAS;MAAA;MACA;QACAF;QACA;QACA;QACA;UAAA;QAAA;QACApB;QACAuB;UACAC;UACAC;UACAC;YACA;YACAV;cACA;YACA;UACA;UACAW;YACA3B;UACA;QACA;MACA;QACAoB;QACA;MACA;IACA;IACAQ,mCAOA;IACAC,mCA2BA;IACAC;MAAA;MACA;QACA;MACA;QACA;QACAP;UACAQ;UACAC;UACAN;YACA;YACA;cACA;YACA;YACA;UACA;QACA;MACA;IACA;IACAO;MAAA;MACA;QACA;UACA;QACA;MACA;QACA;MACA;IACA;IACAC;MAAA;MACA;MAAA,4CACA;QAAA;MAAA;QAAA;UAAA;UACA;YACAnB;YACAA;YACAA;YACAA;YACAA;YACA;cACAA;cACAA;YACA;UACA;QAAA;QAXA;UAAA;QAYA;MAAA;QAAA;MAAA;QAAA;MAAA;MACA;MACA;MACA;IACA;IACAoB;MAEA;IAEA;IACAC;MAAA;MACApC;MACA;MACA;MAKA;MACAS;QACA;MACA;MAAA,4CACAA;QAAA;MAAA;QAAA;UAAA;UACA;YAAA;UAAA;UACA;YACA4B;UACA;YACA;cACAA,8CACA,wBACA;YACA;cACAA;YACA;UACA;QAAA;QAZA;UAAA;QAaA;MAAA;QAAA;MAAA;QAAA;MAAA;MAEA;MACA;MACA;IACA;IACAC;MACAtC;MACA;MACA;MACA;MACA;MACA;QACAuC;QACAnD;QACAC;QACAmD;QACAC;QACAC;QACA5B;QACAlC;QACA+D;QACA9D;QACA+D;QACAC;QACAC;QACAC;QACAC;MACA;MACA;MACA;IACA;IACAC;IACAC;MACA;IACA;IACAC;MAAA;MACA;MACA;MACA;MACA;QAAAC;MAAA;MACAA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;ACzeA;AAAA;AAAA;AAAA;AAA4+C,CAAgB,gwCAAG,EAAC,C;;;;;;;;;;;ACAhgD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/shmily-drag-image/components/shmily-drag-image/shmily-drag-image.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./shmily-drag-image.vue?vue&type=template&id=39e8887d&scoped=true&\"\nvar renderjs\nimport script from \"./shmily-drag-image.vue?vue&type=script&lang=js&\"\nexport * from \"./shmily-drag-image.vue?vue&type=script&lang=js&\"\nimport style0 from \"./shmily-drag-image.vue?vue&type=style&index=0&id=39e8887d&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"39e8887d\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/shmily-drag-image/components/shmily-drag-image/shmily-drag-image.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shmily-drag-image.vue?vue&type=template&id=39e8887d&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shmily-drag-image.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shmily-drag-image.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"con\">\r\n    <template v-if=\"viewWidth\">\r\n      <movable-area class=\"area\" :style=\"{ height: areaHeight }\" @mouseenter=\"mouseenter\" @mouseleave=\"mouseleave\">\r\n        <movable-view v-for=\"(item, index) in imageList\" :key=\"item.id\" class=\"view\" direction=\"all\" :y=\"item.y\"\r\n          :x=\"item.x\" :damping=\"40\" :disabled=\"item.disable\" @change=\"onChange($event, item)\"\r\n          @touchstart=\"touchstart(item)\" @mousedown=\"touchstart(item)\" @touchend=\"touchend(item)\"\r\n          @mouseup=\"touchend(item)\" :style=\"{\r\n          width: viewWidth + 'px', \r\n          height: viewWidth + 'px', \r\n          'z-index': item.zIndex, \r\n          opacity: item.opacity \r\n        }\">\r\n          <view class=\"area-con\" :style=\"{\r\n            width: childWidth, \r\n            height: childWidth, \r\n            borderRadius: borderRadius + 'rpx',\r\n            transform: 'scale(' + item.scale + ')' \r\n          }\">\r\n            <image class=\"pre-image\" :src=\"item.src\" mode=\"widthFix\"></image>\r\n            <view class=\"del-con\" @click=\"delImages(item, index)\" @touchstart.stop=\"delImageMp(item, index)\"\r\n              @touchend.stop=\"nothing()\" @mousedown.stop=\"nothing()\" @mouseup.stop=\"nothing()\">\r\n              <view class=\"del-wrap\">\r\n                <image class=\"del-image\"\r\n                  src=\"https://mpjoy.oss-cn-beijing.aliyuncs.com/crmebimage/public/maintain/2024/11/22/264c59ab0f89409d91641278e6e07ec2be9a9yqbwu.png\">\r\n                </image>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </movable-view>\r\n      </movable-area>\r\n\r\n    </template>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  export default {\r\n    emits: ['input', 'update:modelValue'],\r\n    props: {\r\n      // 排序图片\r\n      value: {\r\n        type: Array,\r\n        default: function() {\r\n          return []\r\n        }\r\n      },\r\n      // 排序图片\r\n      modelValue: {\r\n        type: Array,\r\n        default: function() {\r\n          return []\r\n        }\r\n      },\r\n      // 从 list 元素对象中读取的键名\r\n      keyName: {\r\n        type: String,\r\n        default: null\r\n      },\r\n      // 选择图片数量限制\r\n      number: {\r\n        type: Number,\r\n        default: 6\r\n      },\r\n      // 图片父容器宽度（实际显示的图片宽度为 imageWidth / 1.1 ），单位 rpx\r\n      // imageWidth > 0 则 cols 无效\r\n      imageWidth: {\r\n        type: Number,\r\n        default: 0\r\n      },\r\n      // 图片列数\r\n      cols: {\r\n        type: Number,\r\n        default: 3\r\n      },\r\n      // 图片圆角，单位 rpx\r\n      borderRadius: {\r\n        type: Number,\r\n        default: 0\r\n      },\r\n      // 图片周围空白填充，单位 rpx\r\n      padding: {\r\n        type: Number,\r\n        default: 10\r\n      },\r\n      // 拖动图片时放大倍数 [0, ∞)\r\n      scale: {\r\n        type: Number,\r\n        default: 1.1\r\n      },\r\n      // 拖动图片时不透明度\r\n      opacity: {\r\n        type: Number,\r\n        default: 0.7\r\n      },\r\n      // 自定义添加\r\n      addImage: {\r\n        type: Function,\r\n        default: null\r\n      },\r\n      // 删除确认\r\n      delImage: {\r\n        type: Function,\r\n        default: null\r\n      }\r\n    },\r\n    data() {\r\n      return {\r\n        imageList: [],\r\n        width: 0,\r\n        add: {\r\n          x: 0,\r\n          y: 0\r\n        },\r\n        colsValue: 0,\r\n        viewWidth: 0,\r\n        tempItem: null,\r\n        timer: null,\r\n        changeStatus: true,\r\n        preStatus: true,\r\n        first: true,\r\n      }\r\n    },\r\n    computed: {\r\n      areaHeight() {\r\n        let height = ''\r\n        // return '355px'\r\n        if (this.imageList.length < this.number) {\r\n          height = (Math.ceil((this.imageList.length + 1) / this.colsValue) * this.viewWidth).toFixed() + 'px'\r\n        } else {\r\n          height = (Math.ceil(this.imageList.length / this.colsValue) * this.viewWidth).toFixed() + 'px'\r\n        }\r\n        console.log('areaHeight', height)\r\n        return height\r\n      },\r\n      childWidth() {\r\n        return this.viewWidth - this.rpx2px(this.padding) * 2 + 'px'\r\n      },\r\n    },\r\n    watch: {\r\n      value: {\r\n        handler(n) {\r\n          if (!this.first && this.changeStatus) {\r\n            console.log('watch', n)\r\n            let flag = false\r\n            for (let i = 0; i < n.length; i++) {\r\n              if (flag) {\r\n                this.addProperties(this.getSrc(n[i]))\r\n                continue\r\n              }\r\n              if (this.imageList.length === i || this.imageList[i].src !== this.getSrc(n[i])) {\r\n                flag = true\r\n                this.imageList.splice(i)\r\n                this.addProperties(this.getSrc(n[i]))\r\n              }\r\n            }\r\n          }\r\n        },\r\n        deep: true\r\n      },\r\n      modelValue: {\r\n        handler(n) {\r\n          if (!this.first && this.changeStatus) {\r\n            console.log('watch', n)\r\n            let flag = false\r\n            for (let i = 0; i < n.length; i++) {\r\n              if (flag) {\r\n                this.addProperties(this.getSrc(n[i]))\r\n                continue\r\n              }\r\n              if (this.imageList.length === i || this.imageList[i].src !== this.getSrc(n[i])) {\r\n                flag = true\r\n                this.imageList.splice(i)\r\n                this.addProperties(this.getSrc(n[i]))\r\n              }\r\n            }\r\n          }\r\n        },\r\n        deep: true\r\n      },\r\n    },\r\n    created() {\r\n      this.width = uni.getSystemInfoSync().windowWidth\r\n    },\r\n    mounted() {\r\n      const query = uni.createSelectorQuery().in(this)\r\n      query.select('.con').boundingClientRect(data => {\r\n        this.colsValue = this.cols\r\n        this.viewWidth = data.width / this.cols\r\n        if (this.imageWidth > 0) {\r\n          this.viewWidth = this.rpx2px(this.imageWidth)\r\n          this.colsValue = Math.floor(data.width / this.viewWidth)\r\n        }\r\n        let list = this.value\r\n        // #ifdef VUE3\r\n        list = this.modelValue\r\n        // #endif\r\n        for (let item of list) {\r\n          this.addProperties(this.getSrc(item))\r\n        }\r\n        this.first = false\r\n\r\n      })\r\n      query.exec()\r\n    },\r\n    methods: {\r\n      getSrc(item) {\r\n        if(this.keyName !== null) {\r\n          return item[this.keyName]\r\n        }\r\n        return item\r\n      },\r\n      onChange(e, item) {\r\n        if (!item) return\r\n        item.oldX = e.detail.x\r\n        item.oldY = e.detail.y\r\n        if (e.detail.source === 'touch') {\r\n          if (item.moveEnd) {\r\n            item.offset = Math.sqrt(Math.pow(item.oldX - item.absX * this.viewWidth, 2) + Math.pow(item.oldY - item\r\n              .absY * this.viewWidth, 2))\r\n          }\r\n          let x = Math.floor((e.detail.x + this.viewWidth / 2) / this.viewWidth)\r\n          if (x >= this.colsValue) return\r\n          let y = Math.floor((e.detail.y + this.viewWidth / 2) / this.viewWidth)\r\n          let index = this.colsValue * y + x\r\n          if (item.index != index && index < this.imageList.length) {\r\n            this.changeStatus = false\r\n            for (let obj of this.imageList) {\r\n              if (item.index > index && obj.index >= index && obj.index < item.index) {\r\n                this.change(obj, 1)\r\n              } else if (item.index < index && obj.index <= index && obj.index > item.index) {\r\n                this.change(obj, -1)\r\n              } else if (obj.id != item.id) {\r\n                obj.offset = 0\r\n                obj.x = obj.oldX\r\n                obj.y = obj.oldY\r\n                setTimeout(() => {\r\n                  this.$nextTick(() => {\r\n                    obj.x = obj.absX * this.viewWidth\r\n                    obj.y = obj.absY * this.viewWidth\r\n                  })\r\n                }, 0)\r\n              }\r\n            }\r\n            item.index = index\r\n            item.absX = x\r\n            item.absY = y\r\n            if (!item.moveEnd) {\r\n              setTimeout(() => {\r\n                this.$nextTick(() => {\r\n                  item.x = item.absX * this.viewWidth\r\n                  item.y = item.absY * this.viewWidth\r\n                })\r\n              }, 0)\r\n            }\r\n            // console.log('bbb', JSON.parse(JSON.stringify(item)));\r\n            this.sortList()\r\n          }\r\n        }\r\n      },\r\n      change(obj, i) {\r\n        obj.index += i\r\n        obj.offset = 0\r\n        obj.x = obj.oldX\r\n        obj.y = obj.oldY\r\n        obj.absX = obj.index % this.colsValue\r\n        obj.absY = Math.floor(obj.index / this.colsValue)\r\n        setTimeout(() => {\r\n          this.$nextTick(() => {\r\n            obj.x = obj.absX * this.viewWidth\r\n            obj.y = obj.absY * this.viewWidth\r\n          })\r\n        }, 0)\r\n      },\r\n      touchstart(item) {\r\n        this.imageList.forEach(v => {\r\n          v.zIndex = v.index + 9\r\n        })\r\n        item.zIndex = 99\r\n        item.moveEnd = true\r\n        this.tempItem = item\r\n        this.timer = setTimeout(() => {\r\n          item.scale = this.scale\r\n          item.opacity = this.opacity\r\n          clearTimeout(this.timer)\r\n          this.timer = null\r\n        }, 200)\r\n      },\r\n      touchend(item) {\r\n        this.previewImage(item)\r\n        item.scale = 1\r\n        item.opacity = 1\r\n        item.x = item.oldX\r\n        item.y = item.oldY\r\n        item.offset = 0\r\n        item.moveEnd = false\r\n        setTimeout(() => {\r\n          this.$nextTick(() => {\r\n            item.x = item.absX * this.viewWidth\r\n            item.y = item.absY * this.viewWidth\r\n            this.tempItem = null\r\n            this.changeStatus = true\r\n          })\r\n          // console.log('ccc', JSON.parse(JSON.stringify(item)));\r\n        }, 0)\r\n        // console.log('ddd', JSON.parse(JSON.stringify(item)));\r\n      },\r\n      previewImage(item) {\r\n        if (this.timer && this.preStatus && this.changeStatus && item.offset < 28.28) {\r\n          clearTimeout(this.timer)\r\n          this.timer = null\r\n          const list = this.value || this.modelValue\r\n          let srcList = list.map(v => this.getSrc(v))\r\n          console.log(list, srcList);\r\n          uni.previewImage({\r\n            urls: srcList,\r\n            current: item.src,\r\n            success: () => {\r\n              this.preStatus = false\r\n              setTimeout(() => {\r\n                this.preStatus = true\r\n              }, 600)\r\n            },\r\n            fail: (e) => {\r\n              console.log(e);\r\n            }\r\n          })\r\n        } else if (this.timer) {\r\n          clearTimeout(this.timer)\r\n          this.timer = null\r\n        }\r\n      },\r\n      mouseenter() {\r\n        //#ifdef H5\r\n        this.imageList.forEach(v => {\r\n          v.disable = false\r\n        })\r\n        //#endif\r\n\r\n      },\r\n      mouseleave() {\r\n        //#ifdef H5\r\n        if (this.tempItem) {\r\n          this.imageList.forEach(v => {\r\n            v.disable = true\r\n            v.zIndex = v.index + 9\r\n            v.offset = 0\r\n            v.moveEnd = false\r\n            if (v.id == this.tempItem.id) {\r\n              if (this.timer) {\r\n                clearTimeout(this.timer)\r\n                this.timer = null\r\n              }\r\n              v.scale = 1\r\n              v.opacity = 1\r\n              v.x = v.oldX\r\n              v.y = v.oldY\r\n              this.$nextTick(() => {\r\n                v.x = v.absX * this.viewWidth\r\n                v.y = v.absY * this.viewWidth\r\n                this.tempItem = null\r\n              })\r\n            }\r\n          })\r\n          this.changeStatus = true\r\n        }\r\n        //#endif\r\n      },\r\n      addImages() {\r\n        if (typeof this.addImage === 'function') {\r\n          this.addImage.bind(this.$parent)()\r\n        } else {\r\n          let checkNumber = this.number - this.imageList.length\r\n          uni.chooseImage({\r\n            count: checkNumber,\r\n            sourceType: ['album', 'camera'],\r\n            success: res => {\r\n              let count = checkNumber <= res.tempFilePaths.length ? checkNumber : res.tempFilePaths.length\r\n              for (let i = 0; i < count; i++) {\r\n                this.addProperties(res.tempFilePaths[i])\r\n              }\r\n              this.sortList()\r\n            }\r\n          })\r\n        }\r\n      },\r\n      delImages(item, index) {\r\n        if (typeof this.delImage === 'function') {\r\n          this.delImage.bind(this.$parent)(() => {\r\n            this.delImageHandle(item, index)\r\n          })\r\n        } else {\r\n          this.delImageHandle(item, index)\r\n        }\r\n      },\r\n      delImageHandle(item, index) {\r\n        this.imageList.splice(index, 1)\r\n        for (let obj of this.imageList) {\r\n          if (obj.index > item.index) {\r\n            obj.index -= 1\r\n            obj.x = obj.oldX\r\n            obj.y = obj.oldY\r\n            obj.absX = obj.index % this.colsValue\r\n            obj.absY = Math.floor(obj.index / this.colsValue)\r\n            this.$nextTick(() => {\r\n              obj.x = obj.absX * this.viewWidth\r\n              obj.y = obj.absY * this.viewWidth\r\n            })\r\n          }\r\n        }\r\n        this.add.x = (this.imageList.length % this.colsValue) * this.viewWidth + 'px'\r\n        this.add.y = Math.floor(this.imageList.length / this.colsValue) * this.viewWidth + 'px'\r\n        this.sortList()\r\n      },\r\n      delImageMp(item, index) {\r\n        //#ifdef MP\r\n        this.delImages(item, index)\r\n        //#endif\r\n      },\r\n      sortList() {\r\n        console.log('sortList');\r\n        const result = []\r\n        let source = this.value\r\n        // #ifdef VUE3\r\n        source = this.modelValue\r\n        // #endif\r\n        \r\n        let list = this.imageList.slice()\r\n        list.sort((a, b) => {\r\n          return a.index - b.index\r\n        })\r\n        for (let s of list) {\r\n          let item = source.find(d => this.getSrc(d) == s.src)\r\n          if (item) {\r\n            result.push(item)\r\n          } else {\r\n            if(this.keyName !== null) {\r\n              result.push({\r\n                [this.keyName]: s.src\r\n              })\r\n            } else {\r\n              result.push(s.src)\r\n            }\r\n          }\r\n        }\r\n        \r\n        this.$emit(\"input\", result);\r\n        this.$emit(\"update:modelValue\", result);\r\n        this.$emit('changeprice')\r\n      },\r\n      addProperties(item) {\r\n        console.log(item);\r\n        let absX = this.imageList.length % this.colsValue\r\n        let absY = Math.floor(this.imageList.length / this.colsValue)\r\n        let x = absX * this.viewWidth\r\n        let y = absY * this.viewWidth\r\n        this.imageList.push({\r\n          src: item,\r\n          x,\r\n          y,\r\n          oldX: x,\r\n          oldY: y,\r\n          absX,\r\n          absY,\r\n          scale: 1,\r\n          zIndex: 9,\r\n          opacity: 1,\r\n          index: this.imageList.length,\r\n          id: this.guid(16),\r\n          disable: false,\r\n          offset: 0,\r\n          moveEnd: false\r\n        })\r\n        this.add.x = (this.imageList.length % this.colsValue) * this.viewWidth + 'px'\r\n        this.add.y = Math.floor(this.imageList.length / this.colsValue) * this.viewWidth + 'px'\r\n      },\r\n      nothing() {},\r\n      rpx2px(v) {\r\n        return this.width * v / 750\r\n      },\r\n      guid(len = 32) {\r\n        const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('')\r\n        const uuid = []\r\n        const radix = chars.length\r\n        for (let i = 0; i < len; i++) uuid[i] = chars[0 | Math.random() * radix]\r\n        uuid.shift()\r\n        return `u${uuid.join('')}`\r\n      }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .con {\r\n    // padding: 30rpx;\r\n    background-color: white;\r\n    .area {\r\n      width: 100%;\r\n      background-color: white;\r\n\r\n      .view {\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n\r\n        .area-con {\r\n          position: relative;\r\n          overflow: hidden;\r\n\r\n          .pre-image {\r\n            width: 100%;\r\n          }\r\n\r\n          .del-con {\r\n            position: absolute;\r\n            top: 0rpx;\r\n            right: 0rpx;\r\n            padding: 0 0 20rpx 20rpx;\r\n\r\n            .del-wrap {\r\n              width: 20rpx;\r\n              height: 20rpx;\r\n              background-color: rgba(0, 0, 0, 0.4);\r\n              border-radius: 0 0 0 10rpx;\r\n              display: flex;\r\n              justify-content: center;\r\n              align-items: center;\r\n\r\n              .del-image {\r\n                width: 20rpx;\r\n                height: 20rpx;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .add {\r\n        position: absolute;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n\r\n        .add-wrap {\r\n          display: flex;\r\n          justify-content: center;\r\n          align-items: center;\r\n          background-color: #eeeeee;\r\n        }\r\n      }\r\n    }\r\n  }\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shmily-drag-image.vue?vue&type=style&index=0&id=39e8887d&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./shmily-drag-image.vue?vue&type=style&index=0&id=39e8887d&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363903555\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}