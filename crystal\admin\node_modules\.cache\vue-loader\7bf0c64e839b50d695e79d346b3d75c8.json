{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\distribution\\config\\index.vue?vue&type=template&id=596034c6&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\distribution\\config\\index.vue", "mtime": 1753666157867}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"divBox\" },\n    [\n      _c(\n        \"el-card\",\n        { staticClass: \"box-card\" },\n        [\n          _c(\n            \"el-form\",\n            {\n              directives: [\n                {\n                  name: \"loading\",\n                  rawName: \"v-loading\",\n                  value: _vm.loading,\n                  expression: \"loading\",\n                },\n              ],\n              ref: \"promoterForm\",\n              staticClass: \"demo-promoterForm\",\n              attrs: {\n                model: _vm.promoterForm,\n                rules: _vm.rules,\n                \"label-width\": \"200px\",\n              },\n            },\n            [\n              _c(\n                \"el-form-item\",\n                { attrs: { prop: \"brokerageFuncStatus\" } },\n                [\n                  _c(\n                    \"span\",\n                    { attrs: { slot: \"label\" }, slot: \"label\" },\n                    [\n                      _c(\"span\", [_vm._v(\"分销启用：\")]),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-tooltip\",\n                        {\n                          staticClass: \"item\",\n                          attrs: {\n                            effect: \"dark\",\n                            content: \"商城分销功能开启关闭\",\n                            placement: \"top-start\",\n                          },\n                        },\n                        [_c(\"i\", { staticClass: \"el-icon-warning-outline\" })]\n                      ),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-radio-group\",\n                    {\n                      model: {\n                        value: _vm.promoterForm.brokerageFuncStatus,\n                        callback: function ($$v) {\n                          _vm.$set(_vm.promoterForm, \"brokerageFuncStatus\", $$v)\n                        },\n                        expression: \"promoterForm.brokerageFuncStatus\",\n                      },\n                    },\n                    [\n                      _c(\"el-radio\", { attrs: { label: \"1\" } }, [\n                        _vm._v(\"开启\"),\n                      ]),\n                      _vm._v(\" \"),\n                      _c(\"el-radio\", { attrs: { label: \"0\" } }, [\n                        _vm._v(\"关闭\"),\n                      ]),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                { attrs: { prop: \"storeBrokerageQuota\" } },\n                [\n                  _c(\n                    \"span\",\n                    { attrs: { slot: \"label\" }, slot: \"label\" },\n                    [\n                      _c(\"span\", [_vm._v(\"满额分销最低金额：\")]),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-tooltip\",\n                        {\n                          staticClass: \"item\",\n                          attrs: {\n                            effect: \"dark\",\n                            content: \"满额分销满足金额开通分销权限\",\n                            placement: \"top-start\",\n                          },\n                        },\n                        [_c(\"i\", { staticClass: \"el-icon-warning-outline\" })]\n                      ),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\"el-input-number\", {\n                    staticClass: \"selWidth\",\n                    attrs: {\n                      placeholder: \"满额分销满足金额开通分销权限\",\n                      min: -1,\n                      step: 1,\n                    },\n                    nativeOn: {\n                      keydown: function ($event) {\n                        return _vm.channelInputLimit($event)\n                      },\n                    },\n                    model: {\n                      value: _vm.promoterForm.storeBrokerageQuota,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.promoterForm, \"storeBrokerageQuota\", $$v)\n                      },\n                      expression: \"promoterForm.storeBrokerageQuota\",\n                    },\n                  }),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                { attrs: { prop: \"brokerageBindind\" } },\n                [\n                  _c(\n                    \"span\",\n                    { attrs: { slot: \"label\" }, slot: \"label\" },\n                    [\n                      _c(\"span\", [_vm._v(\"分销关系绑定：\")]),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-tooltip\",\n                        {\n                          staticClass: \"item\",\n                          attrs: {\n                            effect: \"dark\",\n                            content:\n                              \"所有用户”指所有没有上级推广人的用户，“新用户”指新注册的用户\",\n                            placement: \"top-start\",\n                          },\n                        },\n                        [_c(\"i\", { staticClass: \"el-icon-warning-outline\" })]\n                      ),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-radio-group\",\n                    {\n                      model: {\n                        value: _vm.promoterForm.brokerageBindind,\n                        callback: function ($$v) {\n                          _vm.$set(_vm.promoterForm, \"brokerageBindind\", $$v)\n                        },\n                        expression: \"promoterForm.brokerageBindind\",\n                      },\n                    },\n                    [\n                      _c(\"el-radio\", { attrs: { label: \"0\" } }, [\n                        _vm._v(\"所有用户\"),\n                      ]),\n                      _vm._v(\" \"),\n                      _c(\"el-radio\", { attrs: { label: \"1\" } }, [\n                        _vm._v(\"新用户\"),\n                      ]),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                { attrs: { prop: \"storeBrokerageIsBubble\" } },\n                [\n                  _c(\n                    \"span\",\n                    { attrs: { slot: \"label\" }, slot: \"label\" },\n                    [\n                      _c(\"span\", [_vm._v(\"分销气泡：\")]),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-tooltip\",\n                        {\n                          staticClass: \"item\",\n                          attrs: {\n                            effect: \"dark\",\n                            content: \"基础商品详情页分销气泡功能开启关闭\",\n                            placement: \"top-start\",\n                          },\n                        },\n                        [_c(\"i\", { staticClass: \"el-icon-warning-outline\" })]\n                      ),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-radio-group\",\n                    {\n                      model: {\n                        value: _vm.promoterForm.storeBrokerageIsBubble,\n                        callback: function ($$v) {\n                          _vm.$set(\n                            _vm.promoterForm,\n                            \"storeBrokerageIsBubble\",\n                            $$v\n                          )\n                        },\n                        expression: \"promoterForm.storeBrokerageIsBubble\",\n                      },\n                    },\n                    [\n                      _c(\"el-radio\", { attrs: { label: \"1\" } }, [\n                        _vm._v(\"开启\"),\n                      ]),\n                      _vm._v(\" \"),\n                      _c(\"el-radio\", { attrs: { label: \"0\" } }, [\n                        _vm._v(\"关闭\"),\n                      ]),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                { attrs: { prop: \"storeBrokerageRatio\" } },\n                [\n                  _c(\n                    \"span\",\n                    { attrs: { slot: \"label\" }, slot: \"label\" },\n                    [\n                      _c(\"span\", [_vm._v(\"一级返佣比例：\")]),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-tooltip\",\n                        {\n                          staticClass: \"item\",\n                          attrs: {\n                            effect: \"dark\",\n                            content:\n                              \"订单交易成功后给上级返佣的比例0 - 100,例:5 = 反订单金额的5%\",\n                            placement: \"top-start\",\n                          },\n                        },\n                        [_c(\"i\", { staticClass: \"el-icon-warning-outline\" })]\n                      ),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\"el-input-number\", {\n                    staticClass: \"selWidth\",\n                    attrs: {\n                      \"step-strictly\": \"\",\n                      min: 0,\n                      max: 100,\n                      placeholder:\n                        \"订单交易成功后给上级返佣的比例0 - 100,例:5 = 反订单金额的5%\",\n                    },\n                    model: {\n                      value: _vm.promoterForm.storeBrokerageRatio,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.promoterForm, \"storeBrokerageRatio\", $$v)\n                      },\n                      expression: \"promoterForm.storeBrokerageRatio\",\n                    },\n                  }),\n                  _vm._v(\" \"),\n                  _c(\"span\", [_vm._v(\"%\")]),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                { attrs: { prop: \"storeBrokerageTwo\" } },\n                [\n                  _c(\n                    \"span\",\n                    { attrs: { slot: \"label\" }, slot: \"label\" },\n                    [\n                      _c(\"span\", [_vm._v(\"二级返佣比例：\")]),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-tooltip\",\n                        {\n                          staticClass: \"item\",\n                          attrs: {\n                            effect: \"dark\",\n                            content:\n                              \"订单交易成功后给上级返佣的比例0 ~ 100,例:5 = 反订单金额的5%\",\n                            placement: \"top-start\",\n                          },\n                        },\n                        [_c(\"i\", { staticClass: \"el-icon-warning-outline\" })]\n                      ),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\"el-input-number\", {\n                    staticClass: \"selWidth\",\n                    attrs: {\n                      \"step-strictly\": \"\",\n                      min: 0,\n                      max: 100,\n                      placeholder:\n                        \"订单交易成功后给上级返佣的比例0 ~ 100,例:5 = 反订单金额的5%\",\n                    },\n                    model: {\n                      value: _vm.promoterForm.storeBrokerageTwo,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.promoterForm, \"storeBrokerageTwo\", $$v)\n                      },\n                      expression: \"promoterForm.storeBrokerageTwo\",\n                    },\n                  }),\n                  _vm._v(\" \"),\n                  _c(\"span\", [_vm._v(\"%\")]),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                { attrs: { prop: \"userExtractMinPrice\" } },\n                [\n                  _c(\n                    \"span\",\n                    { attrs: { slot: \"label\" }, slot: \"label\" },\n                    [\n                      _c(\"span\", [_vm._v(\"提现最低金额：\")]),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-tooltip\",\n                        {\n                          staticClass: \"item\",\n                          attrs: {\n                            effect: \"dark\",\n                            content: \"用户提现最低金额\",\n                            placement: \"top-start\",\n                          },\n                        },\n                        [_c(\"i\", { staticClass: \"el-icon-warning-outline\" })]\n                      ),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\"el-input-number\", {\n                    staticClass: \"selWidth\",\n                    attrs: { min: 0, step: 1, placeholder: \"用户提现最低金额\" },\n                    model: {\n                      value: _vm.promoterForm.userExtractMinPrice,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.promoterForm, \"userExtractMinPrice\", $$v)\n                      },\n                      expression: \"promoterForm.userExtractMinPrice\",\n                    },\n                  }),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                { attrs: { prop: \"userExtractBank\" } },\n                [\n                  _c(\n                    \"span\",\n                    { attrs: { slot: \"label\" }, slot: \"label\" },\n                    [\n                      _c(\"span\", [_vm._v(\"提现银行卡：\")]),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-tooltip\",\n                        {\n                          staticClass: \"item\",\n                          attrs: {\n                            effect: \"dark\",\n                            content: \"提现银行卡，每个银行换行\",\n                            placement: \"top-start\",\n                          },\n                        },\n                        [_c(\"i\", { staticClass: \"el-icon-warning-outline\" })]\n                      ),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\"el-input\", {\n                    attrs: {\n                      type: \"textarea\",\n                      rows: 4,\n                      placeholder: \"提现银行卡，每个银行换行\",\n                    },\n                    model: {\n                      value: _vm.promoterForm.userExtractBank,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.promoterForm, \"userExtractBank\", $$v)\n                      },\n                      expression: \"promoterForm.userExtractBank\",\n                    },\n                  }),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                { attrs: { prop: \"extractTime\" } },\n                [\n                  _c(\n                    \"span\",\n                    { attrs: { slot: \"label\" }, slot: \"label\" },\n                    [\n                      _c(\"span\", [_vm._v(\"冻结时间：\")]),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-tooltip\",\n                        {\n                          staticClass: \"item\",\n                          attrs: {\n                            effect: \"dark\",\n                            content: \"佣金冻结时间(天)\",\n                            placement: \"top-start\",\n                          },\n                        },\n                        [_c(\"i\", { staticClass: \"el-icon-warning-outline\" })]\n                      ),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\"el-input-number\", {\n                    staticClass: \"selWidth\",\n                    attrs: { min: 0, placeholder: \"佣金冻结时间(天)\" },\n                    model: {\n                      value: _vm.promoterForm.extractTime,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.promoterForm, \"extractTime\", $$v)\n                      },\n                      expression: \"promoterForm.extractTime\",\n                    },\n                  }),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                [\n                  _c(\n                    \"el-button\",\n                    {\n                      directives: [\n                        {\n                          name: \"hasPermi\",\n                          rawName: \"v-hasPermi\",\n                          value: [\"admin:retail:spread:manage:set\"],\n                          expression: \"['admin:retail:spread:manage:set']\",\n                        },\n                      ],\n                      attrs: { type: \"primary\", loading: _vm.loading },\n                      on: {\n                        click: function ($event) {\n                          return _vm.submitForm(\"promoterForm\")\n                        },\n                      },\n                    },\n                    [_vm._v(\"提交\")]\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}