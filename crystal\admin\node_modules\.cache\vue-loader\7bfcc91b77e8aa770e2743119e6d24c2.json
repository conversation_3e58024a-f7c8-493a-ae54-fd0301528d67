{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\index.vue", "mtime": 1753666157923}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport {\r\n  productLstApi,\r\n  productDeleteApi,\r\n  categoryApi,\r\n  putOnShellApi,\r\n  offShellApi,\r\n  productHeadersApi,\r\n  productExportApi,\r\n  restoreApi,\r\n  productExcelApi,\r\n} from \"@/api/store\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport taoBao from \"./taoBao\";\r\nimport { checkPermi } from \"@/utils/permission\"; // 权限判断函数\r\nexport default {\r\n  name: \"ProductList\",\r\n  components: { taoBao },\r\n  data() {\r\n    return {\r\n      props: {\r\n        children: \"child\",\r\n        label: \"name\",\r\n        value: \"id\",\r\n        emitPath: false,\r\n      },\r\n      // roterPre: roterPre,\r\n      headeNum: [],\r\n      listLoading: true,\r\n      tableData: {\r\n        data: [],\r\n        total: 0,\r\n      },\r\n      tableFrom: {\r\n        page: 1,\r\n        limit: 20,\r\n        cateId: \"\",\r\n        keywords: \"\",\r\n        type: \"1\",\r\n        productType: \"0\",\r\n      },\r\n      categoryList: [],\r\n      merCateList: [],\r\n      objectUrl: process.env.VUE_APP_BASE_API,\r\n      dialogVisible: false,\r\n    };\r\n  },\r\n  mounted() {\r\n    this.goodHeade();\r\n    this.getList();\r\n    this.getCategorySelect();\r\n    this.checkedCities = this.$cache.local.has(\"goods_stroge\")\r\n      ? this.$cache.local.getJSON(\"goods_stroge\")\r\n      : this.checkedCities;\r\n  },\r\n  methods: {\r\n    checkPermi,\r\n    handleRestore(id) {\r\n      this.$modalSure(\"恢复商品\").then(() => {\r\n        restoreApi(id).then((res) => {\r\n          this.$message.success(\"操作成功\");\r\n          this.goodHeade();\r\n          this.getList();\r\n        });\r\n      });\r\n    },\r\n    seachList() {\r\n      this.tableFrom.page = 1;\r\n      this.getList();\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false;\r\n    },\r\n    handleCloseMod(item) {\r\n      this.dialogVisible = item;\r\n      this.goodHeade();\r\n      this.getList();\r\n    },\r\n    // 复制\r\n    onCopy() {\r\n      this.dialogVisible = true;\r\n    },\r\n    // 导出\r\n    exports() {\r\n      productExcelApi({\r\n        cateId: this.tableFrom.cateId,\r\n        keywords: this.tableFrom.keywords,\r\n        type: this.tableFrom.type,\r\n        productType: this.tableFrom.productType,\r\n      }).then((res) => {\r\n        window.location.href = res.fileName;\r\n      });\r\n    },\r\n    // 获取商品表单头数量\r\n    goodHeade() {\r\n      productHeadersApi({type : 0})\r\n        .then((res) => {\r\n          this.headeNum = res;\r\n        })\r\n        .catch((res) => {\r\n          this.$message.error(res.message);\r\n        });\r\n    },\r\n    // 商户分类；\r\n    getCategorySelect() {\r\n      categoryApi({ status: -1, type: 1 })\r\n        .then((res) => {\r\n          this.merCateList = res;\r\n        })\r\n        .catch((res) => {\r\n          this.$message.error(res.message);\r\n        });\r\n    },\r\n    // 列表\r\n    getList() {\r\n      this.listLoading = true;\r\n      productLstApi(this.tableFrom)\r\n        .then((res) => {\r\n          this.tableData.data = res.list;\r\n          this.tableData.total = res.total;\r\n          this.listLoading = false;\r\n        })\r\n        .catch((res) => {\r\n          this.listLoading = false;\r\n          this.$message.error(res.message);\r\n        });\r\n    },\r\n    pageChange(page) {\r\n      this.tableFrom.page = page;\r\n      this.getList();\r\n    },\r\n    handleSizeChange(val) {\r\n      this.tableFrom.limit = val;\r\n      this.getList();\r\n    },\r\n    // 删除\r\n    handleDelete(id, type) {\r\n      this.$modalSure(`删除 id 为 ${id} 的商品`).then(() => {\r\n        const deleteFlag = type == 5 ? \"delete\" : \"recycle\";\r\n        productDeleteApi(id, deleteFlag).then(() => {\r\n          this.$message.success(\"删除成功\");\r\n          this.getList();\r\n          this.goodHeade();\r\n        });\r\n      });\r\n    },\r\n    onchangeIsShow(row) {\r\n      row.isShow\r\n        ? putOnShellApi(row.id)\r\n          .then(() => {\r\n            this.$message.success(\"上架成功\");\r\n            this.getList();\r\n            this.goodHeade();\r\n          })\r\n          .catch(() => {\r\n            row.isShow = !row.isShow;\r\n          })\r\n        : offShellApi(row.id)\r\n          .then(() => {\r\n            this.$message.success(\"下架成功\");\r\n            this.getList();\r\n            this.goodHeade();\r\n          })\r\n          .catch(() => {\r\n            row.isShow = !row.isShow;\r\n          });\r\n    },\r\n  },\r\n};\r\n", null]}