{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\order\\index.vue?vue&type=template&id=007ed227&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\order\\index.vue", "mtime": 1753666157910}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"divBox relative\" },\n    [\n      _c(\"el-card\", { staticClass: \"box-card\" }, [\n        _c(\"div\", { staticClass: \"clearfix\" }, [\n          _c(\n            \"div\",\n            { staticClass: \"container\" },\n            [\n              _c(\n                \"el-form\",\n                { attrs: { size: \"small\", \"label-width\": \"100px\" } },\n                [\n                  _vm.checkPermi([\"admin:order:status:num\"])\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"订单状态：\" } },\n                        [\n                          _c(\n                            \"el-radio-group\",\n                            {\n                              attrs: { type: \"button\" },\n                              on: { change: _vm.seachList },\n                              model: {\n                                value: _vm.tableFrom.status,\n                                callback: function ($$v) {\n                                  _vm.$set(_vm.tableFrom, \"status\", $$v)\n                                },\n                                expression: \"tableFrom.status\",\n                              },\n                            },\n                            [\n                              _c(\n                                \"el-radio-button\",\n                                { attrs: { label: \"all\" } },\n                                [\n                                  _vm._v(\n                                    \"全部 \" +\n                                      _vm._s(\n                                        \"(\" + _vm.orderChartType.all\n                                          ? _vm.orderChartType.all\n                                          : 0 + \")\"\n                                      )\n                                  ),\n                                ]\n                              ),\n                              _vm._v(\" \"),\n                              _c(\n                                \"el-radio-button\",\n                                { attrs: { label: \"unPaid\" } },\n                                [\n                                  _vm._v(\n                                    \"未支付 \" +\n                                      _vm._s(\n                                        \"(\" + _vm.orderChartType.unPaid\n                                          ? _vm.orderChartType.unPaid\n                                          : 0 + \")\"\n                                      )\n                                  ),\n                                ]\n                              ),\n                              _vm._v(\" \"),\n                              _c(\n                                \"el-radio-button\",\n                                { attrs: { label: \"notShipped\" } },\n                                [\n                                  _vm._v(\n                                    \"未发货 \" +\n                                      _vm._s(\n                                        \"(\" + _vm.orderChartType.notShipped\n                                          ? _vm.orderChartType.notShipped\n                                          : 0 + \")\"\n                                      )\n                                  ),\n                                ]\n                              ),\n                              _vm._v(\" \"),\n                              _c(\n                                \"el-radio-button\",\n                                { attrs: { label: \"spike\" } },\n                                [\n                                  _vm._v(\n                                    \"待收货 \" +\n                                      _vm._s(\n                                        \"(\" + _vm.orderChartType.spike\n                                          ? _vm.orderChartType.spike\n                                          : 0 + \")\"\n                                      )\n                                  ),\n                                ]\n                              ),\n                              _vm._v(\" \"),\n                              _c(\n                                \"el-radio-button\",\n                                { attrs: { label: \"bargain\" } },\n                                [\n                                  _vm._v(\n                                    \"待评价 \" +\n                                      _vm._s(\n                                        \"(\" + _vm.orderChartType.bargain\n                                          ? _vm.orderChartType.bargain\n                                          : 0 + \")\"\n                                      )\n                                  ),\n                                ]\n                              ),\n                              _vm._v(\" \"),\n                              _c(\n                                \"el-radio-button\",\n                                { attrs: { label: \"complete\" } },\n                                [\n                                  _vm._v(\n                                    \"交易完成 \" +\n                                      _vm._s(\n                                        \"(\" + _vm.orderChartType.complete\n                                          ? _vm.orderChartType.complete\n                                          : 0 + \")\"\n                                      )\n                                  ),\n                                ]\n                              ),\n                              _vm._v(\" \"),\n                              _c(\n                                \"el-radio-button\",\n                                { attrs: { label: \"toBeWrittenOff\" } },\n                                [\n                                  _vm._v(\n                                    \"待核销 \" +\n                                      _vm._s(\n                                        \"(\" + _vm.orderChartType.toBeWrittenOff\n                                          ? _vm.orderChartType.toBeWrittenOff\n                                          : 0 + \")\"\n                                      )\n                                  ),\n                                ]\n                              ),\n                              _vm._v(\" \"),\n                              _c(\n                                \"el-radio-button\",\n                                { attrs: { label: \"refunding\" } },\n                                [\n                                  _vm._v(\n                                    \"退款中 \" +\n                                      _vm._s(\n                                        \"(\" + _vm.orderChartType.refunding\n                                          ? _vm.orderChartType.refunding\n                                          : 0 + \")\"\n                                      )\n                                  ),\n                                ]\n                              ),\n                              _vm._v(\" \"),\n                              _c(\n                                \"el-radio-button\",\n                                { attrs: { label: \"refunded\" } },\n                                [\n                                  _vm._v(\n                                    \"已退款 \" +\n                                      _vm._s(\n                                        \"(\" + _vm.orderChartType.refunded\n                                          ? _vm.orderChartType.refunded\n                                          : 0 + \")\"\n                                      )\n                                  ),\n                                ]\n                              ),\n                              _vm._v(\" \"),\n                              _c(\n                                \"el-radio-button\",\n                                { attrs: { label: \"deleted\" } },\n                                [\n                                  _vm._v(\n                                    \"已删除 \" +\n                                      _vm._s(\n                                        \"(\" + _vm.orderChartType.deleted\n                                          ? _vm.orderChartType.deleted\n                                          : 0 + \")\"\n                                      )\n                                  ),\n                                ]\n                              ),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-form-item\",\n                    { staticClass: \"width100\", attrs: { label: \"时间选择：\" } },\n                    [\n                      _c(\n                        \"el-radio-group\",\n                        {\n                          staticClass: \"mr20\",\n                          attrs: { type: \"button\", size: \"small\" },\n                          on: {\n                            change: function ($event) {\n                              return _vm.selectChange(_vm.tableFrom.dateLimit)\n                            },\n                          },\n                          model: {\n                            value: _vm.tableFrom.dateLimit,\n                            callback: function ($$v) {\n                              _vm.$set(_vm.tableFrom, \"dateLimit\", $$v)\n                            },\n                            expression: \"tableFrom.dateLimit\",\n                          },\n                        },\n                        _vm._l(_vm.fromList.fromTxt, function (item, i) {\n                          return _c(\n                            \"el-radio-button\",\n                            { key: i, attrs: { label: item.val } },\n                            [_vm._v(_vm._s(item.text))]\n                          )\n                        }),\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\"el-date-picker\", {\n                        staticStyle: { width: \"220px\" },\n                        attrs: {\n                          \"value-format\": \"yyyy-MM-dd\",\n                          format: \"yyyy-MM-dd\",\n                          size: \"small\",\n                          type: \"daterange\",\n                          placement: \"bottom-end\",\n                          placeholder: \"自定义时间\",\n                        },\n                        on: { change: _vm.onchangeTime },\n                        model: {\n                          value: _vm.timeVal,\n                          callback: function ($$v) {\n                            _vm.timeVal = $$v\n                          },\n                          expression: \"timeVal\",\n                        },\n                      }),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-form-item\",\n                    { staticClass: \"width100\", attrs: { label: \"订单号：\" } },\n                    [\n                      _c(\n                        \"el-input\",\n                        {\n                          staticClass: \"selWidth\",\n                          attrs: {\n                            placeholder: \"请输入订单号\",\n                            size: \"small\",\n                            clearable: \"\",\n                          },\n                          model: {\n                            value: _vm.tableFrom.orderNo,\n                            callback: function ($$v) {\n                              _vm.$set(_vm.tableFrom, \"orderNo\", $$v)\n                            },\n                            expression: \"tableFrom.orderNo\",\n                          },\n                        },\n                        [\n                          _c(\"el-button\", {\n                            attrs: {\n                              slot: \"append\",\n                              icon: \"el-icon-search\",\n                              size: \"small\",\n                            },\n                            on: { click: _vm.seachList },\n                            slot: \"append\",\n                          }),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-form-item\",\n                    { staticClass: \"width100\" },\n                    [\n                      _c(\n                        \"el-button\",\n                        {\n                          directives: [\n                            {\n                              name: \"hasPermi\",\n                              rawName: \"v-hasPermi\",\n                              value: [\"admin:export:excel:order\"],\n                              expression: \"['admin:export:excel:order']\",\n                            },\n                          ],\n                          attrs: { size: \"small\" },\n                          on: { click: _vm.exports },\n                        },\n                        [_vm._v(\"导出\")]\n                      ),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n        ]),\n      ]),\n      _vm._v(\" \"),\n      _c(\"div\", { staticClass: \"mt20\" }),\n      _vm._v(\" \"),\n      _c(\n        \"el-card\",\n        { staticClass: \"box-card\" },\n        [\n          _c(\n            \"el-table\",\n            {\n              directives: [\n                {\n                  name: \"loading\",\n                  rawName: \"v-loading\",\n                  value: _vm.listLoading,\n                  expression: \"listLoading\",\n                },\n              ],\n              staticClass: \"table\",\n              attrs: {\n                data: _vm.tableData.data,\n                size: \"mini\",\n                \"highlight-current-row\": \"\",\n                \"header-cell-style\": { fontWeight: \"bold\" },\n                \"row-key\": function (row) {\n                  return row.orderId\n                },\n              },\n            },\n            [\n              _c(\"el-table-column\", {\n                attrs: { label: \"订单号\", \"min-width\": \"210\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\"span\", {\n                          staticStyle: { display: \"block\" },\n                          domProps: { textContent: _vm._s(scope.row.orderId) },\n                        }),\n                        _vm._v(\" \"),\n                        _c(\n                          \"span\",\n                          {\n                            directives: [\n                              {\n                                name: \"show\",\n                                rawName: \"v-show\",\n                                value: scope.row.isDel,\n                                expression: \"scope.row.isDel\",\n                              },\n                            ],\n                            staticStyle: { color: \"#ED4014\", display: \"block\" },\n                          },\n                          [_vm._v(\"用户已删除\")]\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"orderType\",\n                  label: \"订单类型\",\n                  \"min-width\": \"110\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"realName\",\n                  label: \"收货人\",\n                  \"min-width\": \"100\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { label: \"商品信息\", \"min-width\": \"400\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\n                          \"el-popover\",\n                          {\n                            attrs: {\n                              trigger: \"hover\",\n                              placement: \"right\",\n                              \"open-delay\": 800,\n                            },\n                          },\n                          [\n                            scope.row.productList &&\n                            scope.row.productList.length\n                              ? _c(\n                                  \"div\",\n                                  {\n                                    attrs: { slot: \"reference\" },\n                                    slot: \"reference\",\n                                  },\n                                  _vm._l(\n                                    scope.row.productList,\n                                    function (val, i) {\n                                      return _c(\n                                        \"div\",\n                                        {\n                                          key: i,\n                                          staticClass:\n                                            \"tabBox acea-row row-middle\",\n                                          staticStyle: {\n                                            \"flex-wrap\": \"inherit\",\n                                          },\n                                        },\n                                        [\n                                          _c(\n                                            \"div\",\n                                            {\n                                              staticClass:\n                                                \"demo-image__preview mr10\",\n                                            },\n                                            [\n                                              _c(\"el-image\", {\n                                                attrs: {\n                                                  src: val.info.image,\n                                                  \"preview-src-list\": [\n                                                    val.info.image,\n                                                  ],\n                                                },\n                                              }),\n                                            ],\n                                            1\n                                          ),\n                                          _vm._v(\" \"),\n                                          _c(\n                                            \"div\",\n                                            { staticClass: \"text_overflow\" },\n                                            [\n                                              _c(\n                                                \"span\",\n                                                {\n                                                  staticClass:\n                                                    \"tabBox_tit mr10\",\n                                                },\n                                                [\n                                                  _vm._v(\n                                                    _vm._s(\n                                                      val.info.productName +\n                                                        \" | \"\n                                                    ) +\n                                                      _vm._s(\n                                                        (val.info.sku\n                                                          ? val.info.sku\n                                                          : \"-\") + \" | \"\n                                                      ) +\n                                                      _vm._s(\n                                                        scope.row.type == 2\n                                                          ? val.info.width +\n                                                              \"x\" +\n                                                              val.info.height +\n                                                              \"mm\"\n                                                          : \"\"\n                                                      )\n                                                  ),\n                                                ]\n                                              ),\n                                              _vm._v(\" \"),\n                                              _c(\n                                                \"span\",\n                                                { staticClass: \"tabBox_pice\" },\n                                                [\n                                                  _vm._v(\n                                                    _vm._s(\n                                                      \"￥\" + val.info.price\n                                                        ? val.info.price +\n                                                            \" x \" +\n                                                            val.info.payNum\n                                                        : \"-\"\n                                                    )\n                                                  ),\n                                                ]\n                                              ),\n                                            ]\n                                          ),\n                                        ]\n                                      )\n                                    }\n                                  ),\n                                  0\n                                )\n                              : _vm._e(),\n                            _vm._v(\" \"),\n                            scope.row.productList &&\n                            scope.row.productList.length\n                              ? _c(\n                                  \"div\",\n                                  { staticClass: \"pup_card\" },\n                                  _vm._l(\n                                    scope.row.productList,\n                                    function (val, i) {\n                                      return _c(\n                                        \"div\",\n                                        {\n                                          key: i,\n                                          staticClass:\n                                            \"tabBox acea-row row-middle\",\n                                          staticStyle: {\n                                            \"flex-wrap\": \"inherit\",\n                                          },\n                                        },\n                                        [\n                                          _c(\"div\", {}, [\n                                            _c(\n                                              \"span\",\n                                              {\n                                                staticClass: \"tabBox_tit mr10\",\n                                              },\n                                              [\n                                                _vm._v(\n                                                  _vm._s(\n                                                    val.info.productName + \" | \"\n                                                  ) +\n                                                    _vm._s(\n                                                      (val.info.sku\n                                                        ? val.info.sku\n                                                        : \"-\") + \" | \"\n                                                    ) +\n                                                    _vm._s(\n                                                      scope.row.type == 2\n                                                        ? val.info.width +\n                                                            \"x\" +\n                                                            val.info.height +\n                                                            \"mm\"\n                                                        : \"\"\n                                                    )\n                                                ),\n                                              ]\n                                            ),\n                                            _vm._v(\" \"),\n                                            _c(\n                                              \"span\",\n                                              { staticClass: \"tabBox_pice\" },\n                                              [\n                                                _vm._v(\n                                                  _vm._s(\n                                                    \"￥\" + val.info.price\n                                                      ? val.info.price +\n                                                          \" x \" +\n                                                          val.info.payNum\n                                                      : \"-\"\n                                                  )\n                                                ),\n                                              ]\n                                            ),\n                                          ]),\n                                        ]\n                                      )\n                                    }\n                                  ),\n                                  0\n                                )\n                              : _vm._e(),\n                          ]\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"payPrice\",\n                  label: \"实际支付\",\n                  \"min-width\": \"80\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { label: \"支付方式\", \"min-width\": \"80\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\"span\", [_vm._v(_vm._s(scope.row.payTypeStr))]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { label: \"订单状态\", \"min-width\": \"100\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\"div\", [\n                          scope.row.refundStatus === 1 ||\n                          scope.row.refundStatus === 2\n                            ? _c(\n                                \"div\",\n                                { staticClass: \"refunding\" },\n                                [\n                                  [\n                                    _c(\n                                      \"el-popover\",\n                                      {\n                                        attrs: {\n                                          trigger: \"hover\",\n                                          placement: \"left\",\n                                          \"open-delay\": 800,\n                                        },\n                                      },\n                                      [\n                                        _c(\n                                          \"b\",\n                                          {\n                                            staticStyle: { color: \"#f124c7\" },\n                                            attrs: { slot: \"reference\" },\n                                            slot: \"reference\",\n                                          },\n                                          [\n                                            _vm._v(\n                                              _vm._s(scope.row.statusStr.value)\n                                            ),\n                                          ]\n                                        ),\n                                        _vm._v(\" \"),\n                                        _c(\n                                          \"div\",\n                                          {\n                                            staticClass: \"pup_card flex-column\",\n                                          },\n                                          [\n                                            _c(\"span\", [\n                                              _vm._v(\n                                                \"退款原因：\" +\n                                                  _vm._s(\n                                                    scope.row.refundReasonWap\n                                                  )\n                                              ),\n                                            ]),\n                                            _vm._v(\" \"),\n                                            _c(\"span\", [\n                                              _vm._v(\n                                                \"备注说明：\" +\n                                                  _vm._s(\n                                                    scope.row\n                                                      .refundReasonWapExplain\n                                                  )\n                                              ),\n                                            ]),\n                                            _vm._v(\" \"),\n                                            _c(\"span\", [\n                                              _vm._v(\n                                                \"退款时间：\" +\n                                                  _vm._s(\n                                                    scope.row.refundReasonTime\n                                                  )\n                                              ),\n                                            ]),\n                                            _vm._v(\" \"),\n                                            _c(\n                                              \"span\",\n                                              { staticClass: \"acea-row\" },\n                                              [\n                                                _vm._v(\n                                                  \"\\n                          退款凭证：\\n                          \"\n                                                ),\n                                                scope.row.refundReasonWapImg\n                                                  ? _vm._l(\n                                                      scope.row.refundReasonWapImg.split(\n                                                        \",\"\n                                                      ),\n                                                      function (item, index) {\n                                                        return _c(\n                                                          \"div\",\n                                                          {\n                                                            key: index,\n                                                            staticClass:\n                                                              \"demo-image__preview\",\n                                                            staticStyle: {\n                                                              width: \"35px\",\n                                                              height: \"auto\",\n                                                              display:\n                                                                \"inline-block\",\n                                                            },\n                                                          },\n                                                          [\n                                                            _c(\"el-image\", {\n                                                              attrs: {\n                                                                src: item,\n                                                                \"preview-src-list\":\n                                                                  [item],\n                                                              },\n                                                            }),\n                                                          ],\n                                                          1\n                                                        )\n                                                      }\n                                                    )\n                                                  : _c(\n                                                      \"span\",\n                                                      {\n                                                        staticStyle: {\n                                                          display:\n                                                            \"inline-block\",\n                                                        },\n                                                      },\n                                                      [_vm._v(\"无\")]\n                                                    ),\n                                              ],\n                                              2\n                                            ),\n                                          ]\n                                        ),\n                                      ]\n                                    ),\n                                  ],\n                                ],\n                                2\n                              )\n                            : _c(\"span\", [\n                                _vm._v(_vm._s(scope.row.statusStr.value)),\n                              ]),\n                        ]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"createTime\",\n                  label: \"下单时间\",\n                  \"min-width\": \"150\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: \"操作\",\n                  \"min-width\": \"150\",\n                  fixed: \"right\",\n                  align: \"center\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        scope.row.paid === false\n                          ? _c(\n                              \"el-button\",\n                              {\n                                directives: [\n                                  {\n                                    name: \"hasPermi\",\n                                    rawName: \"v-hasPermi\",\n                                    value: [\"admin:order:update:price\"],\n                                    expression: \"['admin:order:update:price']\",\n                                  },\n                                ],\n                                staticClass: \"mr10\",\n                                attrs: { type: \"text\", size: \"small\" },\n                                on: {\n                                  click: function ($event) {\n                                    return _vm.edit(scope.row)\n                                  },\n                                },\n                              },\n                              [_vm._v(\"编辑\")]\n                            )\n                          : _vm._e(),\n                        _vm._v(\" \"),\n                        scope.row.statusStr.key === \"notShipped\" &&\n                        scope.row.refundStatus === 0\n                          ? _c(\n                              \"el-button\",\n                              {\n                                directives: [\n                                  {\n                                    name: \"hasPermi\",\n                                    rawName: \"v-hasPermi\",\n                                    value: [\"admin:order:send\"],\n                                    expression: \"['admin:order:send']\",\n                                  },\n                                ],\n                                staticClass: \"mr10\",\n                                attrs: { type: \"text\", size: \"small\" },\n                                on: {\n                                  click: function ($event) {\n                                    return _vm.sendOrder(scope.row)\n                                  },\n                                },\n                              },\n                              [_vm._v(\"发送货\")]\n                            )\n                          : _vm._e(),\n                        _vm._v(\" \"),\n                        scope.row.statusStr.key === \"toBeWrittenOff\" &&\n                        scope.row.paid == true &&\n                        scope.row.refundStatus === 0\n                          ? _c(\n                              \"el-button\",\n                              {\n                                directives: [\n                                  {\n                                    name: \"hasPermi\",\n                                    rawName: \"v-hasPermi\",\n                                    value: [\"admin:order:write:update\"],\n                                    expression: \"['admin:order:write:update']\",\n                                  },\n                                ],\n                                staticClass: \"mr10\",\n                                attrs: { type: \"text\", size: \"small\" },\n                                on: {\n                                  click: function ($event) {\n                                    return _vm.onWriteOff(scope.row)\n                                  },\n                                },\n                              },\n                              [_vm._v(\"立即核销\")]\n                            )\n                          : _vm._e(),\n                        _vm._v(\" \"),\n                        _c(\n                          \"el-dropdown\",\n                          { attrs: { trigger: \"click\" } },\n                          [\n                            _c(\"span\", { staticClass: \"el-dropdown-link\" }, [\n                              _vm._v(\"\\n              更多\"),\n                              _c(\"i\", {\n                                staticClass:\n                                  \"el-icon-arrow-down el-icon--right\",\n                              }),\n                            ]),\n                            _vm._v(\" \"),\n                            _c(\n                              \"el-dropdown-menu\",\n                              { attrs: { slot: \"dropdown\" }, slot: \"dropdown\" },\n                              [\n                                _vm.checkPermi([\"admin:order:info\"])\n                                  ? _c(\n                                      \"el-dropdown-item\",\n                                      {\n                                        nativeOn: {\n                                          click: function ($event) {\n                                            return _vm.onOrderDetails(\n                                              scope.row.orderId\n                                            )\n                                          },\n                                        },\n                                      },\n                                      [_vm._v(\"订单详情\")]\n                                    )\n                                  : _vm._e(),\n                                _vm._v(\" \"),\n                                _vm.checkPermi([\"admin:order:status:list\"])\n                                  ? _c(\n                                      \"el-dropdown-item\",\n                                      {\n                                        nativeOn: {\n                                          click: function ($event) {\n                                            return _vm.onOrderLog(\n                                              scope.row.orderId\n                                            )\n                                          },\n                                        },\n                                      },\n                                      [_vm._v(\"订单记录\")]\n                                    )\n                                  : _vm._e(),\n                                _vm._v(\" \"),\n                                _vm.checkPermi([\"admin:order:mark\"])\n                                  ? _c(\n                                      \"el-dropdown-item\",\n                                      {\n                                        nativeOn: {\n                                          click: function ($event) {\n                                            return _vm.onOrderMark(scope.row)\n                                          },\n                                        },\n                                      },\n                                      [_vm._v(\"订单备注\")]\n                                    )\n                                  : _vm._e(),\n                                _vm._v(\" \"),\n                                scope.row.refundStatus === 1 &&\n                                _vm.checkPermi([\"admin:order:refund:refuse\"])\n                                  ? _c(\n                                      \"el-dropdown-item\",\n                                      {\n                                        nativeOn: {\n                                          click: function ($event) {\n                                            return _vm.onOrderRefuse(scope.row)\n                                          },\n                                        },\n                                      },\n                                      [_vm._v(\"拒绝退款\")]\n                                    )\n                                  : _vm._e(),\n                                _vm._v(\" \"),\n                                scope.row.refundStatus === 1 &&\n                                _vm.checkPermi([\"admin:order:refund\"])\n                                  ? _c(\n                                      \"el-dropdown-item\",\n                                      {\n                                        nativeOn: {\n                                          click: function ($event) {\n                                            return _vm.onOrderRefund(scope.row)\n                                          },\n                                        },\n                                      },\n                                      [_vm._v(\"立即退款\")]\n                                    )\n                                  : _vm._e(),\n                                _vm._v(\" \"),\n                                scope.row.statusStr.key === \"deleted\" &&\n                                _vm.checkPermi([\"admin:order:delete\"])\n                                  ? _c(\n                                      \"el-dropdown-item\",\n                                      {\n                                        nativeOn: {\n                                          click: function ($event) {\n                                            return _vm.handleDelete(\n                                              scope.row,\n                                              scope.$index\n                                            )\n                                          },\n                                        },\n                                      },\n                                      [_vm._v(\"删除订单\")]\n                                    )\n                                  : _vm._e(),\n                              ],\n                              1\n                            ),\n                          ],\n                          1\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"div\",\n            { staticClass: \"block\" },\n            [\n              _c(\"el-pagination\", {\n                attrs: {\n                  \"page-sizes\": [20, 40, 60, 80],\n                  \"page-size\": _vm.tableFrom.limit,\n                  \"current-page\": _vm.tableFrom.page,\n                  layout: \"total, sizes, prev, pager, next, jumper\",\n                  total: _vm.tableData.total,\n                },\n                on: {\n                  \"size-change\": _vm.handleSizeChange,\n                  \"current-change\": _vm.pageChange,\n                },\n              }),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"el-dialog\",\n        {\n          attrs: {\n            title: \"编辑订单\",\n            visible: _vm.dialogVisible,\n            width: \"500px\",\n            \"before-close\": _vm.handleClose,\n          },\n          on: {\n            \"update:visible\": function ($event) {\n              _vm.dialogVisible = $event\n            },\n          },\n        },\n        [\n          _vm.dialogVisible\n            ? _c(\"zb-parser\", {\n                attrs: {\n                  \"form-id\": 104,\n                  \"is-create\": _vm.isCreate,\n                  \"edit-data\": _vm.editData,\n                },\n                on: { submit: _vm.handlerSubmit, resetForm: _vm.resetForm },\n              })\n            : _vm._e(),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"el-dialog\",\n        {\n          attrs: {\n            title: \"操作记录\",\n            visible: _vm.dialogVisibleJI,\n            width: \"700px\",\n          },\n          on: {\n            \"update:visible\": function ($event) {\n              _vm.dialogVisibleJI = $event\n            },\n          },\n        },\n        [\n          _c(\n            \"el-table\",\n            {\n              directives: [\n                {\n                  name: \"loading\",\n                  rawName: \"v-loading\",\n                  value: _vm.LogLoading,\n                  expression: \"LogLoading\",\n                },\n              ],\n              staticStyle: { width: \"100%\" },\n              attrs: { border: \"\", data: _vm.tableDataLog.data },\n            },\n            [\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"oid\",\n                  align: \"center\",\n                  label: \"ID\",\n                  \"min-width\": \"80\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"changeMessage\",\n                  label: \"操作记录\",\n                  align: \"center\",\n                  \"min-width\": \"280\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"createTime\",\n                  label: \"操作时间\",\n                  align: \"center\",\n                  \"min-width\": \"280\",\n                },\n              }),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"div\",\n            { staticClass: \"block\" },\n            [\n              _c(\"el-pagination\", {\n                attrs: {\n                  \"page-sizes\": [10, 20, 30, 40],\n                  \"page-size\": _vm.tableFromLog.limit,\n                  \"current-page\": _vm.tableFromLog.page,\n                  layout: \"total, sizes, prev, pager, next, jumper\",\n                  total: _vm.tableDataLog.total,\n                },\n                on: {\n                  \"size-change\": _vm.handleSizeChangeLog,\n                  \"current-change\": _vm.pageChangeLog,\n                },\n              }),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\"details-from\", {\n        ref: \"orderDetail\",\n        attrs: { orderId: _vm.orderId },\n      }),\n      _vm._v(\" \"),\n      _c(\"order-send\", {\n        ref: \"send\",\n        attrs: { orderId: _vm.orderId },\n        on: { submitFail: _vm.getList },\n      }),\n      _vm._v(\" \"),\n      _c(\"order-video-send\", {\n        ref: \"videoSend\",\n        attrs: { orderId: _vm.orderId },\n        on: { submitFail: _vm.getList },\n      }),\n      _vm._v(\" \"),\n      _vm.RefuseVisible\n        ? _c(\n            \"el-dialog\",\n            {\n              attrs: {\n                title: \"拒绝退款原因\",\n                visible: _vm.RefuseVisible,\n                width: \"500px\",\n                \"before-close\": _vm.RefusehandleClose,\n              },\n              on: {\n                \"update:visible\": function ($event) {\n                  _vm.RefuseVisible = $event\n                },\n              },\n            },\n            [\n              _c(\"zb-parser\", {\n                attrs: {\n                  \"form-id\": 106,\n                  \"is-create\": 1,\n                  \"edit-data\": _vm.RefuseData,\n                },\n                on: {\n                  submit: _vm.RefusehandlerSubmit,\n                  resetForm: _vm.resetFormRefusehand,\n                },\n              }),\n            ],\n            1\n          )\n        : _vm._e(),\n      _vm._v(\" \"),\n      _c(\n        \"el-dialog\",\n        {\n          attrs: {\n            title: \"退款处理\",\n            visible: _vm.refundVisible,\n            width: \"500px\",\n            \"before-close\": _vm.refundhandleClose,\n          },\n          on: {\n            \"update:visible\": function ($event) {\n              _vm.refundVisible = $event\n            },\n          },\n        },\n        [\n          _vm.refundVisible\n            ? _c(\"zb-parser\", {\n                attrs: {\n                  \"form-id\": 107,\n                  \"is-create\": 1,\n                  \"edit-data\": _vm.refundData,\n                },\n                on: {\n                  submit: _vm.refundhandlerSubmit,\n                  resetForm: _vm.resetFormRefundhandler,\n                },\n              })\n            : _vm._e(),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}