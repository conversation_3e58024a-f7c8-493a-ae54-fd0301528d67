{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/login_mobile/routine_phone.vue?7e2f", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/login_mobile/routine_phone.vue?662e", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/login_mobile/routine_phone.vue?0650", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/login_mobile/routine_phone.vue?1cf6", "uni-app:///components/login_mobile/routine_phone.vue", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/login_mobile/routine_phone.vue?a703", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/login_mobile/routine_phone.vue?3fba"], "names": ["name", "props", "isPhoneBox", "type", "default", "logoUrl", "auth<PERSON><PERSON>", "data", "keyCode", "account", "codeNum", "isStatus", "mounted", "methods", "getphonenumber", "uni", "title", "Routine", "then", "catch", "getUserPhoneNumber", "encryptedData", "iv", "code", "key", "token", "getUserInfo", "that", "close"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0H;AAC1H;AACiE;AACL;AACc;;;AAG1E;AACgM;AAChM,gBAAgB,2LAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,wFAAM;AACR,EAAE,iGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA2vB,CAAgB,6rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACe/wB;AACA;AAMA;;;;;;;;;;;;;;;AARA;AAAA,eASA;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;EACA;EACAG;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC,6BACA;EACAC;IAEA;IACAC;MAAA;MACAC;QAAAC;MAAA;MACAC,2BACAC;QACA;MACA,GACAC;QACAJ;MACA;IACA;IACA;IACAK;MAAA;MACA;QACAC;QACAC;QACAC;QACAC;QACArB;MACA,GACAe;QACA;UACAO;QACA;QACA;QACA;MACA,GACAN;QACAJ;QACA;UACAC;QACA;MACA;IACA;IACA;AACA;AACA;IACAU;MAAA;MACA;MACA;QACAX;QACAY;QACAA;QACAA;QACA;MACA;IACA;IAEAC;MACA;QAAAjB;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvGA;AAAA;AAAA;AAAA;AAA05C,CAAgB,ouCAAG,EAAC,C;;;;;;;;;;;ACA96C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/login_mobile/routine_phone.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./routine_phone.vue?vue&type=template&id=716eebd2&\"\nvar renderjs\nimport script from \"./routine_phone.vue?vue&type=script&lang=js&\"\nexport * from \"./routine_phone.vue?vue&type=script&lang=js&\"\nimport style0 from \"./routine_phone.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/login_mobile/routine_phone.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./routine_phone.vue?vue&type=template&id=716eebd2&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./routine_phone.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./routine_phone.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view v-if=\"isPhoneBox\">\r\n\t\t<view class=\"mobile-bg\" @click=\"close\"></view>\r\n\t\t<view class=\"mobile-mask animated\" :class=\"{slideInUp:isUp}\">\r\n\t\t\t<view class=\"info-box\">\r\n\t\t\t\t<image :src=\"logoUrl\"></image>\r\n\t\t\t\t<view class=\"title\">获取授权</view>\r\n\t\t\t\t<view class=\"txt\">获取微信的手机号授权</view>\r\n\t\t\t</view>\r\n\t\t\t<button class=\"sub_btn\" open-type=\"getPhoneNumber\" @getphonenumber=\"getphonenumber\">获取微信手机号</button>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n<script>\r\n\tconst app = getApp();\r\n\timport Routine from '@/libs/routine';\r\n\timport {\r\n\t\tloginMobile,\r\n\t\tregisterVerify,\r\n\t\tgetCodeApi,\r\n\t\tgetUserInfo\r\n\t} from \"@/api/user\";\r\n\timport { getLogo, getUserPhone } from '@/api/public';\r\n\texport default{\r\n\t\tname:'routine_phone',\r\n\t\tprops:{\r\n\t\t\tisPhoneBox:{\r\n\t\t\t\ttype:Boolean,\r\n\t\t\t\tdefault:false,\r\n\t\t\t},\r\n\t\t\tlogoUrl:{\r\n\t\t\t\ttype:String,\r\n\t\t\t\tdefault:'',\r\n\t\t\t},\r\n\t\t\tauthKey:{\r\n\t\t\t\ttype:String,\r\n\t\t\t\tdefault:'',\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata(){\r\n\t\t\treturn {\r\n\t\t\t\tkeyCode:'',\r\n\t\t\t\taccount:'',\r\n\t\t\t\tcodeNum:'',\r\n\t\t\t\tisStatus:false\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t},\r\n\t\tmethods:{\r\n\t\t\t// #ifdef MP\r\n\t\t\t// 小程序获取手机号码\r\n\t\t\tgetphonenumber(e){\r\n\t\t\t\tuni.showLoading({ title: '加载中' });\r\n\t\t\t\tRoutine.getCode()\r\n\t\t\t\t\t.then(code => {\r\n\t\t\t\t\t\tthis.getUserPhoneNumber(e.detail.encryptedData, e.detail.iv, code);\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(error => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 小程序获取手机号码回调\r\n\t\t\tgetUserPhoneNumber(encryptedData, iv, code) {\r\n\t\t\t\tgetUserPhone({\r\n\t\t\t\t\tencryptedData: encryptedData,\r\n\t\t\t\t\tiv: iv,\r\n\t\t\t\t\tcode: code,\r\n\t\t\t\t\tkey:this.authKey,\r\n\t\t\t\t\ttype: 'routine'\r\n\t\t\t\t})\r\n\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\tthis.$store.commit('LOGIN', {\r\n\t\t\t\t\t\t\ttoken: res.data.token\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tthis.$store.commit(\"SETUID\", res.data.uid);\r\n\t\t\t\t\t\tthis.getUserInfo();\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(res => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tthis.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: res\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 获取个人用户信息\r\n\t\t\t */\r\n\t\t\tgetUserInfo: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tgetUserInfo().then(res => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tthat.userInfo = res.data\r\n\t\t\t\t\tthat.$store.commit(\"UPDATE_USERINFO\", res.data);\r\n\t\t\t\t\tthat.isStatus = true\r\n\t\t\t\t\tthis.close()\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// #endif\r\n\t\t\tclose(){\r\n\t\t\t\tthis.$emit('close',{isStatus:this.isStatus})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.mobile-bg{\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\ttop: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tbackground: rgba(0,0,0,0.5);\r\n\t}\r\n\t.mobile-mask {\r\n\t\tz-index: 20;\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\tbottom: 0;\r\n\t\twidth: 100%;\r\n\t\tpadding: 67rpx 30rpx;\r\n\t\tbackground: #fff;\r\n\t\t.info-box{\r\n\t\t\tdisplay:flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\timage{\r\n\t\t\t\twidth: 150rpx;\r\n\t\t\t\theight: 150rpx;\r\n\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t}\r\n\t\t\t.title{\r\n\t\t\t\tmargin-top: 30rpx;\r\n\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\tfont-size: 36rpx;\r\n\t\t\t}\r\n\t\t\t.txt{\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tcolor: #868686;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.sub_btn{\r\n\t\t\twidth: 690rpx;\r\n\t\t\theight: 86rpx;\r\n\t\t\tline-height: 86rpx;\r\n\t\t\tmargin-top: 60rpx;\r\n\t\t\tbackground: $theme-color;\r\n\t\t\tborder-radius: 43rpx;\r\n\t\t\tcolor: #fff;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\ttext-align: center;\r\n\t\t}\r\n\t}\r\n\t.animated{\r\n\t\tanimation-duration:.4s\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./routine_phone.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./routine_phone.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363903534\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}