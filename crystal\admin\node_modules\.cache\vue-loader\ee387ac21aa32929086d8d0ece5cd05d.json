{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\taoBao.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\taoBao.vue", "mtime": 1753666157925}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport {\n  copyProductApi,\n  crawlFromApi,\n  treeListApi,\n  crawlSaveApi,\n  categoryApi,\n  importProductApi,\n  productCreateApi,\n  copyConfigApi\n} from '@/api/store';\nimport { goodDesignList } from \"@/api/systemGroup\";\nimport Tinymce from '@/components/Tinymce/index'\nimport {shippingTemplatesList} from '@/api/logistics'\nimport {Debounce} from '@/utils/validate'\nconst defaultObj = [{\n  image: '',\n  price: null,\n  cost: null,\n  otPrice: null,\n  stock: null,\n  barCode: '',\n  weight: 0,\n  volume: 0,\n}]\nconst objTitle = {\n  price: {\n    title: '售价'\n  },\n  cost: {\n    title: '成本价'\n  },\n  otPrice: {\n    title: '原价'\n  },\n  stock: {\n    title: '库存'\n  },\n  barCode: {\n    title: '商品编号'\n  },\n  weight: {\n    title: '重量（KG）'\n  },\n  volume: {\n    title: '体积(m³)'\n  }\n}\nexport default {\n  name: 'taoBao',\n  components: {Tinymce },\n  data() {\n    return {\n      loading: false,\n      formThead: Object.assign({}, objTitle),\n      manyTabTit: {},\n      manyTabDate: {},\n      formValidate: null,\n      form: 1,\n      props2: {\n        children: 'child',\n        label: 'name',\n        value: 'id',\n        multiple: true,\n        emitPath: false\n      },\n      checkboxGroup: [], //\n      recommend: [],\n      modal_loading: false,\n      ManyAttrValue: [Object.assign({}, defaultObj[0])], // 多规格\n      imgList: [],\n      tempData: {\n        page: 1,\n        limit: 9999\n      },\n      shippingList: [],\n      merCateList: [],\n      images: '',\n      url: '',\n      modalPic: false,\n      isChoice: '',\n      isDisabled:false,\n      ruleInline: {\n        storeName: [\n          {required: true, message: '请输入商品名称', trigger: 'blur'}\n        ],\n        cateIds: [\n          {required: true, message: '请选择商品分类', trigger: 'change', type: 'array', min: '1'}\n        ],\n        unitName: [\n          {required: true, message: '请输入单位', trigger: 'blur'}\n        ],\n        tempId: [\n          {required: true, message: '请选择运费模板', trigger: 'change', type: 'number'}\n        ],\n        keyword: [\n          {required: true, message: '请输入商品关键字', trigger: 'blur'}\n        ],\n        attrValue: [\n          { required: true, message: '请上传商品轮播图', type: 'array', trigger: 'change' }\n        ]\n      },\n      grid: {\n        xl: 12,\n        lg: 12,\n        md: 12,\n        sm: 24,\n        xs: 24\n      },\n      copyConfig: {}\n    }\n  },\n  created() {\n    this.goodsCategory();\n  },\n  computed: {\n    attrValue() {\n      const obj = Object.assign({}, defaultObj[0])\n      delete obj.image\n      return obj\n    },\n    oneFormBatch() {\n      const obj = [Object.assign({}, defaultObj[0])]\n      delete obj[0].barCode\n      return obj\n    }\n  },\n  watch: {\n    'formValidate.attr': {\n      handler: function (val) {\n        this.watCh(val)\n      },\n      immediate: false,\n      deep: true\n    }\n  },\n  mounted() {\n    this.productGetTemplate();\n    this.getCopyConfig();\n    this.getGoodsType()\n  },\n  methods: {\n    // 删除表格中的属性\n    delAttrTable(index) {\n      this.formValidate.attrValue.splice(index, 1)\n    },\n    getCopyConfig() {\n      copyConfigApi().then(res => {\n        this.copyConfig = res\n      })\n    },\n     onChangeGroup() {\n      this.checkboxGroup.includes('isGood') ? this.formValidate.isGood = true : this.formValidate.isGood = false\n      this.checkboxGroup.includes('isBenefit') ? this.formValidate.isBenefit = true : this.formValidate.isBenefit = false\n      this.checkboxGroup.includes('isBest') ? this.formValidate.isBest = true : this.formValidate.isBest = false\n      this.checkboxGroup.includes('isNew') ? this.formValidate.isNew = true : this.formValidate.isNew = false\n      this.checkboxGroup.includes('isHot') ? this.formValidate.isHot = true : this.formValidate.isHot = false\n    },\n    // 批量添加\n    batchAdd() {\n      // if (!this.oneFormBatch[0].pic || !this.oneFormBatch[0].price || !this.oneFormBatch[0].cost || !this.oneFormBatch[0].ot_price ||\n      //     !this.oneFormBatch[0].stock || !this.oneFormBatch[0].bar_code) return this.$Message.warning('请填写完整的批量设置内容！');\n      for (const val of this.formValidate.attrValue) {\n        this.$set(val, 'image', this.oneFormBatch[0].image)\n        this.$set(val, 'price', this.oneFormBatch[0].price)\n        this.$set(val, 'cost', this.oneFormBatch[0].cost)\n        this.$set(val, 'otPrice', this.oneFormBatch[0].otPrice)\n        this.$set(val, 'stock', this.oneFormBatch[0].stock)\n        this.$set(val, 'barCode', this.oneFormBatch[0].barCode)\n        this.$set(val, 'weight', this.oneFormBatch[0].weight)\n        this.$set(val, 'volume', this.oneFormBatch[0].volume)\n      }\n    },\n    watCh(val) {\n      const tmp = {}\n      const tmpTab = {}\n      this.formValidate.attr.forEach((o, i) => {\n        // tmp['value' + i] = {title: o.attrName}\n        // tmpTab['value' + i] = ''\n        tmp[o.attrName] = { title: o.attrName };\n        tmpTab[o.attrName] = '';\n      })\n      this.formValidate.attrValue = this.attrFormat(val)\n      this.manyTabTit = tmp\n      this.manyTabDate = tmpTab\n      this.formThead = Object.assign({}, this.formThead, tmp)\n    },\n    attrFormat(arr) {\n      let data = []\n      const res = []\n      return format(arr)\n\n      function format(arr) {\n        if (arr.length > 1) {\n          arr.forEach((v, i) => {\n            if (i === 0) data = arr[i]['attrValue']\n            const tmp = []\n            data.forEach(function (vv) {\n              arr[i + 1] && arr[i + 1]['attrValue'] && arr[i + 1]['attrValue'].forEach(g => {\n                const rep2 = (i !== 0 ? '' : arr[i]['attrName'] + '_') + vv + '$&' + arr[i + 1]['attrName'] + '_' + g\n                tmp.push(rep2)\n                if (i === (arr.length - 2)) {\n                  const rep4 = {\n                    image: '',\n                    price: 0,\n                    cost: 0,\n                    otPrice: 0,\n                    stock: 0,\n                    barCode: '',\n                    weight: 0,\n                    volume: 0,\n                    brokerage: 0,\n                    brokerage_two: 0\n                  }\n                  rep2.split('$&').forEach((h, k) => {\n                    const rep3 = h.split('_')\n                    if (!rep4['attrValue']) rep4['attrValue'] = {}\n                    rep4['attrValue'][rep3[0]] = rep3.length > 1 ? rep3[1] : ''\n                  })\n                  // Object.values(rep4.attrValue).forEach((v, i) => {\n                  //   rep4['value' + i] = v\n                  // })\n                  for (let attrValueKey in rep4.attrValue) {\n                    rep4[attrValueKey] = rep4.attrValue[attrValueKey];\n                  }\n                  res.push(rep4)\n                }\n              })\n            })\n            data = tmp.length ? tmp : []\n          })\n        } else {\n          const dataArr = []\n          arr.forEach((v, k) => {\n            v['attrValue'].forEach((vv, kk) => {\n              dataArr[kk] = v['attrName'] + '_' + vv\n              res[kk] = {\n                image: '',\n                price: 0,\n                cost: 0,\n                otPrice: 0,\n                stock: 0,\n                barCode: '',\n                weight: 0,\n                volume: 0,\n                brokerage: 0,\n                brokerage_two: 0,\n                attrValue: {[v['attrName']]: vv}\n              }\n              for (let attrValueKey in res[kk].attrValue) {\n                res[kk][attrValueKey] = res[kk].attrValue[attrValueKey];\n              }\n            })\n          })\n          data.push(dataArr.join('$&'))\n        }\n        return res\n      }\n    },\n    // 获取运费模板；\n    productGetTemplate() {\n      shippingTemplatesList(this.tempData).then(res => {\n        this.shippingList = res.list\n      })\n    },\n    // 删除图片\n    handleRemove(i) {\n      this.formValidate.sliderImages.splice(i, 1);\n      this.$forceUpdate();\n    },\n    // 选择主图\n    checked(item, index) {\n      this.formValidate.image = item;\n    },\n    // 商品分类；\n    goodsCategory() {\n      categoryApi({status: -1, type: 1}).then(res => {\n        this.merCateList = res\n      })\n    },\n    // 生成表单\n    add() {\n      if (this.url) {\n        // var reg = /(http|ftp|https):\\/\\/[\\w\\-_]+(\\.[\\w\\-_]+)+([\\w\\-\\.,@?^=%&:/~\\+#]*[\\w\\-\\@?^=%&/~\\+#])?/;\n        // if (!reg.test(this.soure_link)) {\n        //   return this.$message.warning('请输入以http开头的地址！');\n        // }\n        this.loading = true;\n        this.copyConfig.copyType == 1 ? copyProductApi({url: this.url}).then(res => {\n          let info = res.info;\n          this.formValidate = {\n            image: this.$selfUtil.setDomain(info.image),\n            sliderImage: info.sliderImage,\n            storeName: info.storeName,\n            storeInfo: info.storeInfo,\n            keyword: info.keyword,\n            cateIds: info.cateId ? info.cateId.split(',') : [], // 商品分类id\n            cateId: info.cateId,// 商品分类id传值\n            unitName: info.unitName,\n            sort: 0,\n            isShow: 0,\n            isBenefit: 0,\n            isNew: 0,\n            isGood: 0,\n            isHot: 0,\n            isBest: 0,\n            tempId: info.tempId,\n            attrValue: info.attrValue,\n            attr: info.attr || [],\n            selectRule: info.selectRule,\n            isSub: false,\n            content: this.$selfUtil.replaceImgSrcHttps(info.content),\n            specType: info.attr.length ? true : false,\n            id: info.id,\n            giveIntegral: info.giveIntegral,\n            ficti: info.ficti\n          }\n          if(info.isHot) this.checkboxGroup.push('isHot')\n          if(info.isGood) this.checkboxGroup.push('isGood')\n          if(info.isBenefit) this.checkboxGroup.push('isBenefit')\n          if(info.isBest) this.checkboxGroup.push('isBest')\n          if(info.isNew) this.checkboxGroup.push('isNew')\n            let imgs = JSON.parse(info.sliderImage)\n          let imgss = []\n          Object.keys(imgs).map(i => {\n            imgss.push(this.$selfUtil.setDomain(imgs[i]))\n          })\n          this.formValidate.sliderImages = imgss\n          if (this.formValidate.attr.length) {\n            this.oneFormBatch[0].image = this.$selfUtil.setDomain(info.image)\n            for (var i = 0; i < this.formValidate.attr.length; i++) {\n              this.formValidate.attr[i].attrValue = JSON.parse(this.formValidate.attr[i].attrValues)\n            }\n          }\n          this.loading = false;\n        }).catch(() => {\n          this.loading = false;\n        }) : importProductApi({url: this.url, form: this.form}).then(res => {\n          this.formValidate = {\n            image: this.$selfUtil.setDomain(res.image),\n            sliderImage: res.sliderImage,\n            storeName: res.storeName,\n            storeInfo: res.storeInfo,\n            keyword: res.keyword,\n            cateIds: res.cateId ? res.cateId.split(',') : [], // 商品分类id\n            cateId: res.cateId,// 商品分类id传值\n            unitName: res.unitName,\n            sort: 0,\n            isShow: 0,\n            isBenefit: 0,\n            isNew: 0,\n            isGood: 0,\n            isHot: 0,\n            isBest: 0,\n            tempId: res.tempId,\n            attrValue: res.attrValue,\n            attr: res.attr || [],\n            selectRule: res.selectRule,\n            isSub: false,\n            content: res.content,\n            specType: res.attr.length ? true : false,\n            id: res.id,\n            giveIntegral: res.giveIntegral,\n            ficti: res.ficti\n          }\n          let imgs = JSON.parse(res.sliderImage)\n          let imgss = []\n          Object.keys(imgs).map(i => {\n            imgss.push(this.$selfUtil.setDomain(imgs[i]))\n          })\n          this.formValidate.sliderImages = imgss\n          if (this.formValidate.attr.length) {\n            this.oneFormBatch[0].image = this.$selfUtil.setDomain(res.image)\n            for (var i = 0; i < this.formValidate.attr.length; i++) {\n              this.formValidate.attr[i].attrValue = JSON.parse(this.formValidate.attr[i].attrValues)\n            }\n          }\n          this.loading = false;\n        }).catch(() => {\n          this.loading = false;\n        })\n      } else {\n        this.$message.warning('请输入链接地址！');\n      }\n    },\n    // 提交\n    handleSubmit:Debounce(function(name) {\n      let pram = JSON.parse(JSON.stringify(this.formValidate));\n     // this.formValidate.attr.length ? this.formValidate.attrValue = this.ManyAttrValue : this.formValidate.attrValue = []\n      pram.attr.forEach(item => {\n        item.attrValues = item.attrValue.join(\",\");\n      });\n      pram.cateId = pram.cateIds.join(',')\n      pram.sliderImage = JSON.stringify(pram.sliderImages);\n      pram.attrValue.forEach(itemData => {\n        itemData.attrValue = JSON.stringify(itemData.attrValue);\n      });\n      this.$refs[name].validate((valid) => {\n        if (valid) {\n          this.modal_loading = true\n          productCreateApi(pram).then(async res => {\n            this.$message.success('新增成功');\n            this.$emit('handleCloseMod', false)\n            this.modal_loading = false\n          }).catch(() => {\n            this.modal_loading = false\n          })\n        } else {\n          if (!pram.storeName || !pram.cateId || !pram.keyword\n            || !pram.unitName || !pram.image) {\n            this.$message.warning(\"请填写完整商品信息！\");\n          }\n        }\n      })\n    }),\n    // 点击商品图\n    modalPicTap(tit, num, i) {\n      const _this = this\n      this.$modalUpload(function (img) {\n        if (tit === '1' && !num) {\n          _this.formValidate.image = img[0].sattDir\n          _this.OneattrValue[0].image = img[0].sattDir\n        }\n        if (tit === '2' && !num) {\n          if (img.length > 10) return this.$message.warning(\"最多选择10张图片！\");\n          if (img.length + _this.formValidate.sliderImages.length > 10) return this.$message.warning(\"最多选择10张图片！\");\n          img.map((item) => {\n            _this.formValidate.sliderImages.push(item.sattDir)\n          });\n        }\n        if (tit === '1' && num === 'dan') {\n          _this.OneattrValue[0].image = img[0].sattDir\n        }\n        if (tit === '1' && num === 'duo') {\n          _this.formValidate.attrValue[i].image = img[0].sattDir\n        }\n        if (tit === '1' && num === 'pi') {\n          _this.oneFormBatch[0].image = img[0].sattDir\n        }\n      }, tit, 'store')\n    },\n    handleDragStart(e, item) {\n      this.dragging = item;\n    },\n    handleDragEnd(e, item) {\n      this.dragging = null\n    },\n    // 首先把div变成可以放置的元素，即重写dragenter/dragover\n    handleDragOver(e) {\n      // e.dataTransfer.dropEffect=\"move\";//在dragenter中针对放置目标来设置!\n      e.dataTransfer.dropEffect = 'move'\n    },\n    handleDragEnter(e, item) {\n      // 为需要移动的元素设置dragstart事件\n      e.dataTransfer.effectAllowed = 'move'\n      if (item === this.dragging) {\n        return\n      }\n      const newItems = [...this.formValidate.slider_image]\n      const src = newItems.indexOf(this.dragging)\n      const dst = newItems.indexOf(item)\n      newItems.splice(dst, 0, ...newItems.splice(src, 1))\n      this.formValidate.slider_image = newItems\n    },\n    getGoodsType(){\n      /** 让商品推荐列表的name属性与页面设置tab的name匹配**/\n      goodDesignList({gid:70}).then((response)=>{\n        let list = response.list;\n        let arr = [];\n        let arr1 = [];\n        const listArr = [{ name: '是否热卖', value: 'isHot' }];\n        let typeLists = [ \n          { name: '', value: 'isGood',type:'2' },  //精品推荐1  热门榜单2 首发新品3 促销单品4 \n          { name: '', value: 'isBenefit' ,type:'4'}, \n          { name: '', value: 'isBest',type:'1' }, \n          { name: '', value: 'isNew',type:'3' }];\n        list.forEach((item)=>{\n          let obj = {};\n          obj.value = JSON.parse(item.value);\n          obj.id = item.id;\n          obj.gid = item.gid;\n          obj.status = item.status;\n          arr.push(obj);\n        })\n        arr.forEach((item1)=>{\n          let obj1 = {};\n          obj1.name = item1.value.fields[1].value;\n          obj1.status = item1.status;\n          obj1.type = item1.value.fields[3].value;\n          arr1.push(obj1);\n        })\n        typeLists.forEach((item)=>{\n          arr1.forEach((item1)=>{\n            if(item.type == item1.type){\n              listArr.push({\n                name:item1.name,\n                value:item.value,\n                type:item.type\n              })\n            }\n          })\n        })\n        this.recommend = listArr\n      })\n    },\n  }\n}\n", null]}