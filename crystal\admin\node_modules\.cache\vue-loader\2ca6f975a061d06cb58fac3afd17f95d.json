{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\list\\edit.vue?vue&type=template&id=01323e9e&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\list\\edit.vue", "mtime": 1753666157940}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"el-form\",\n    {\n      ref: \"ruleForm\",\n      staticClass: \"demo-ruleForm\",\n      attrs: { model: _vm.ruleForm, rules: _vm.rules, \"label-width\": \"100px\" },\n    },\n    [\n      _c(\n        \"el-form-item\",\n        { attrs: { label: \"用户编号：\" } },\n        [\n          _c(\"el-input\", {\n            staticClass: \"selWidth\",\n            attrs: { disabled: \"\" },\n            model: {\n              value: _vm.ruleForm.id,\n              callback: function ($$v) {\n                _vm.$set(_vm.ruleForm, \"id\", $$v)\n              },\n              expression: \"ruleForm.id\",\n            },\n          }),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"el-form-item\",\n        { attrs: { label: \"用户地址：\" } },\n        [\n          _c(\"el-input\", {\n            staticClass: \"selWidth\",\n            model: {\n              value: _vm.ruleForm.addres,\n              callback: function ($$v) {\n                _vm.$set(_vm.ruleForm, \"addres\", $$v)\n              },\n              expression: \"ruleForm.addres\",\n            },\n          }),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"el-form-item\",\n        { attrs: { label: \"用户备注：\" } },\n        [\n          _c(\"el-input\", {\n            staticClass: \"selWidth\",\n            attrs: { type: \"textarea\" },\n            model: {\n              value: _vm.ruleForm.mark,\n              callback: function ($$v) {\n                _vm.$set(_vm.ruleForm, \"mark\", $$v)\n              },\n              expression: \"ruleForm.mark\",\n            },\n          }),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"el-form-item\",\n        { attrs: { label: \"用户分组：\" } },\n        [\n          _c(\n            \"el-select\",\n            {\n              staticClass: \"selWidth\",\n              attrs: { placeholder: \"请选择\", clearable: \"\", filterable: \"\" },\n              model: {\n                value: _vm.ruleForm.groupId,\n                callback: function ($$v) {\n                  _vm.$set(_vm.ruleForm, \"groupId\", $$v)\n                },\n                expression: \"ruleForm.groupId\",\n              },\n            },\n            _vm._l(_vm.groupList, function (item, index) {\n              return _c(\"el-option\", {\n                key: index,\n                attrs: { value: item.id, label: item.groupName },\n              })\n            }),\n            1\n          ),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"el-form-item\",\n        { attrs: { label: \"用户标签：\" } },\n        [\n          _c(\n            \"el-select\",\n            {\n              staticClass: \"selWidth\",\n              attrs: {\n                placeholder: \"请选择\",\n                clearable: \"\",\n                filterable: \"\",\n                multiple: \"\",\n              },\n              model: {\n                value: _vm.labelData,\n                callback: function ($$v) {\n                  _vm.labelData = $$v\n                },\n                expression: \"labelData\",\n              },\n            },\n            _vm._l(_vm.labelLists, function (item, index) {\n              return _c(\"el-option\", {\n                key: index,\n                attrs: { value: item.id, label: item.name },\n              })\n            }),\n            1\n          ),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"el-form-item\",\n        { attrs: { label: \"推广员\" } },\n        [\n          _c(\n            \"el-radio-group\",\n            {\n              model: {\n                value: _vm.ruleForm.isPromoter,\n                callback: function ($$v) {\n                  _vm.$set(_vm.ruleForm, \"isPromoter\", $$v)\n                },\n                expression: \"ruleForm.isPromoter\",\n              },\n            },\n            [\n              _c(\"el-radio\", { attrs: { label: true } }, [_vm._v(\"开启\")]),\n              _vm._v(\" \"),\n              _c(\"el-radio\", { attrs: { label: false } }, [_vm._v(\"关闭\")]),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"el-form-item\",\n        { attrs: { label: \"状态\" } },\n        [\n          _c(\n            \"el-radio-group\",\n            {\n              model: {\n                value: _vm.ruleForm.status,\n                callback: function ($$v) {\n                  _vm.$set(_vm.ruleForm, \"status\", $$v)\n                },\n                expression: \"ruleForm.status\",\n              },\n            },\n            [\n              _c(\"el-radio\", { attrs: { label: true } }, [_vm._v(\"开启\")]),\n              _vm._v(\" \"),\n              _c(\"el-radio\", { attrs: { label: false } }, [_vm._v(\"关闭\")]),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"el-form-item\",\n        [\n          _c(\n            \"el-button\",\n            {\n              directives: [\n                {\n                  name: \"hasPermi\",\n                  rawName: \"v-hasPermi\",\n                  value: [\"admin:user:update\"],\n                  expression: \"['admin:user:update']\",\n                },\n              ],\n              attrs: { type: \"primary\" },\n              on: {\n                click: function ($event) {\n                  return _vm.submitForm(\"ruleForm\")\n                },\n              },\n            },\n            [_vm._v(\"提交\")]\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-button\",\n            {\n              on: {\n                click: function ($event) {\n                  return _vm.resetForm(\"ruleForm\")\n                },\n              },\n            },\n            [_vm._v(\"取消\")]\n          ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}