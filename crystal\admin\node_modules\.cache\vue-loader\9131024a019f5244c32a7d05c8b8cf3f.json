{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\order\\orderDetail.vue?vue&type=template&id=4328fb58&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\order\\orderDetail.vue", "mtime": 1753666157910}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    [\n      _vm.orderDatalist\n        ? _c(\n            \"el-dialog\",\n            {\n              attrs: {\n                title: \"订单信息\",\n                visible: _vm.dialogVisible,\n                width: \"700px\",\n              },\n              on: {\n                \"update:visible\": function ($event) {\n                  _vm.dialogVisible = $event\n                },\n              },\n            },\n            [\n              _c(\n                \"div\",\n                {\n                  directives: [\n                    {\n                      name: \"loading\",\n                      rawName: \"v-loading\",\n                      value: _vm.loading,\n                      expression: \"loading\",\n                    },\n                  ],\n                  staticClass: \"description\",\n                },\n                [\n                  _c(\"div\", { staticClass: \"title\" }, [_vm._v(\"用户信息\")]),\n                  _vm._v(\" \"),\n                  _c(\"div\", { staticClass: \"acea-row\" }, [\n                    _c(\"div\", { staticClass: \"description-term\" }, [\n                      _vm._v(\"用户昵称：\" + _vm._s(_vm.orderDatalist.nikeName)),\n                    ]),\n                    _vm._v(\" \"),\n                    _c(\"div\", { staticClass: \"description-term\" }, [\n                      _vm._v(\n                        \"绑定电话：\" +\n                          _vm._s(\n                            _vm.orderDatalist.phone\n                              ? _vm.orderDatalist.phone\n                              : \"无\"\n                          )\n                      ),\n                    ]),\n                  ]),\n                  _vm._v(\" \"),\n                  _c(\"el-divider\"),\n                  _vm._v(\" \"),\n                  _c(\"div\", { staticClass: \"title\" }, [\n                    _vm._v(\n                      _vm._s(\n                        _vm.orderDatalist.statusStr.key === \"toBeWrittenOff\"\n                          ? \"提货信息\"\n                          : \"收货信息\"\n                      )\n                    ),\n                  ]),\n                  _vm._v(\" \"),\n                  _c(\"div\", { staticClass: \"acea-row\" }, [\n                    _c(\"div\", { staticClass: \"description-term\" }, [\n                      _vm._v(\n                        _vm._s(\n                          _vm.orderDatalist.statusStr.key === \"toBeWrittenOff\"\n                            ? \"提货人\"\n                            : \"收货人\"\n                        ) +\n                          \"：\" +\n                          _vm._s(_vm.orderDatalist.realName)\n                      ),\n                    ]),\n                    _vm._v(\" \"),\n                    _c(\"div\", { staticClass: \"description-term\" }, [\n                      _vm._v(\n                        _vm._s(\n                          _vm.orderDatalist.statusStr.key === \"toBeWrittenOff\"\n                            ? \"提货电话\"\n                            : \"收货电话\"\n                        ) +\n                          \"：\" +\n                          _vm._s(_vm.orderDatalist.userPhone)\n                      ),\n                    ]),\n                    _vm._v(\" \"),\n                    _vm.orderDatalist.statusStr.key !== \"toBeWrittenOff\"\n                      ? _c(\"div\", { staticClass: \"description-term\" }, [\n                          _vm._v(\n                            _vm._s(\n                              _vm.orderDatalist.statusStr.key ===\n                                \"toBeWrittenOff\"\n                                ? \"提货地址\"\n                                : \"收货地址\"\n                            ) +\n                              \"：\" +\n                              _vm._s(_vm.orderDatalist.userAddress)\n                          ),\n                        ])\n                      : _vm._e(),\n                  ]),\n                  _vm._v(\" \"),\n                  _c(\"el-divider\"),\n                  _vm._v(\" \"),\n                  _c(\"div\", { staticClass: \"title\" }, [_vm._v(\"订单信息\")]),\n                  _vm._v(\" \"),\n                  _c(\n                    \"div\",\n                    { staticClass: \"acea-row\" },\n                    [\n                      _c(\"div\", { staticClass: \"description-term\" }, [\n                        _vm._v(\n                          \"订单编号：\" + _vm._s(_vm.orderDatalist.orderId)\n                        ),\n                      ]),\n                      _vm._v(\" \"),\n                      _c(\n                        \"div\",\n                        {\n                          staticClass: \"description-term\",\n                          staticStyle: { color: \"red\" },\n                        },\n                        [\n                          _vm._v(\n                            \"订单状态：\" +\n                              _vm._s(_vm.orderDatalist.statusStr.value)\n                          ),\n                        ]\n                      ),\n                      _vm._v(\" \"),\n                      _c(\"div\", { staticClass: \"description-term\" }, [\n                        _vm._v(\n                          \"商品总数：\" + _vm._s(_vm.orderDatalist.totalNum)\n                        ),\n                      ]),\n                      _vm._v(\" \"),\n                      _c(\"div\", { staticClass: \"description-term\" }, [\n                        _vm._v(\n                          \"商品总价：\" + _vm._s(_vm.orderDatalist.proTotalPrice)\n                        ),\n                      ]),\n                      _vm._v(\" \"),\n                      _c(\"div\", { staticClass: \"description-term\" }, [\n                        _vm._v(\n                          \"支付邮费：\" + _vm._s(_vm.orderDatalist.payPostage)\n                        ),\n                      ]),\n                      _vm._v(\" \"),\n                      _c(\"div\", { staticClass: \"description-term\" }, [\n                        _vm._v(\n                          \"优惠券金额：\" + _vm._s(_vm.orderDatalist.couponPrice)\n                        ),\n                      ]),\n                      _vm._v(\" \"),\n                      _c(\"div\", { staticClass: \"description-term\" }, [\n                        _vm._v(\n                          \"实际支付：\" + _vm._s(_vm.orderDatalist.payPrice)\n                        ),\n                      ]),\n                      _vm._v(\" \"),\n                      _c(\"div\", { staticClass: \"description-term\" }, [\n                        _vm._v(\n                          \"抵扣金额：\" +\n                            _vm._s(_vm.orderDatalist.deductionPrice)\n                        ),\n                      ]),\n                      _vm._v(\" \"),\n                      _vm.orderDatalist.refundPrice\n                        ? _c(\n                            \"div\",\n                            { staticClass: \"description-term fontColor3\" },\n                            [\n                              _vm._v(\n                                \"退款金额：\" +\n                                  _vm._s(_vm.orderDatalist.refundPrice)\n                              ),\n                            ]\n                          )\n                        : _vm._e(),\n                      _vm._v(\" \"),\n                      _vm.orderDatalist.useIntegral\n                        ? _c(\"div\", { staticClass: \"description-term\" }, [\n                            _vm._v(\n                              \"使用积分：\" +\n                                _vm._s(_vm.orderDatalist.useIntegral)\n                            ),\n                          ])\n                        : _vm._e(),\n                      _vm._v(\" \"),\n                      _vm.orderDatalist.backIntegral\n                        ? _c(\"div\", { staticClass: \"description-term\" }, [\n                            _vm._v(\n                              \"退回积分：\" +\n                                _vm._s(_vm.orderDatalist.backIntegral)\n                            ),\n                          ])\n                        : _vm._e(),\n                      _vm._v(\" \"),\n                      _c(\"div\", { staticClass: \"description-term\" }, [\n                        _vm._v(\n                          \"创建时间：\" + _vm._s(_vm.orderDatalist.createTime)\n                        ),\n                      ]),\n                      _vm._v(\" \"),\n                      _vm.orderDatalist.refundReasonTime\n                        ? _c(\"div\", { staticClass: \"description-term\" }, [\n                            _vm._v(\n                              \"退款时间：\" +\n                                _vm._s(_vm.orderDatalist.refundReasonTime)\n                            ),\n                          ])\n                        : _vm._e(),\n                      _vm._v(\" \"),\n                      _c(\"div\", { staticClass: \"description-term\" }, [\n                        _vm._v(\n                          \"支付方式：\" + _vm._s(_vm.orderDatalist.payTypeStr)\n                        ),\n                      ]),\n                      _vm._v(\" \"),\n                      _c(\"div\", { staticClass: \"description-term\" }, [\n                        _vm._v(\n                          \"推广人：\" +\n                            _vm._s(\n                              _vm._f(\"filterEmpty\")(\n                                _vm.orderDatalist.spreadName\n                              )\n                            )\n                        ),\n                      ]),\n                      _vm._v(\" \"),\n                      _vm.orderDatalist.shippingType === 2 &&\n                      _vm.orderDatalist.statusStr.key === \"notShipped\"\n                        ? _c(\"div\", { staticClass: \"description-term\" }, [\n                            _vm._v(\n                              \"门店名称：\" + _vm._s(_vm.orderDatalist.storeName)\n                            ),\n                          ])\n                        : _vm._e(),\n                      _vm._v(\" \"),\n                      _vm.orderDatalist.shippingType === 2 &&\n                      _vm.orderDatalist.statusStr.key === \"notShipped\"\n                        ? _c(\"div\", { staticClass: \"description-term\" }, [\n                            _vm._v(\n                              \"核销码：\" + _vm._s(_vm.orderDatalist.user_phone)\n                            ),\n                          ])\n                        : _vm._e(),\n                      _vm._v(\" \"),\n                      _c(\"div\", { staticClass: \"description-term\" }, [\n                        _vm._v(\"商家备注：\" + _vm._s(_vm.orderDatalist.remark)),\n                      ]),\n                      _vm._v(\" \"),\n                      _vm.orderDatalist.statusStr.key === \"toBeWrittenOff\" &&\n                      _vm.orderDatalist.systemStore\n                        ? [\n                            _c(\"div\", { staticClass: \"description-term\" }, [\n                              _vm._v(\n                                \"提货码：\" +\n                                  _vm._s(_vm.orderDatalist.verifyCode)\n                              ),\n                            ]),\n                            _vm._v(\" \"),\n                            _c(\"div\", { staticClass: \"description-term\" }, [\n                              _vm._v(\n                                \"门店名称：\" +\n                                  _vm._s(_vm.orderDatalist.systemStore.name)\n                              ),\n                            ]),\n                            _vm._v(\" \"),\n                            _c(\"div\", { staticClass: \"description-term\" }, [\n                              _vm._v(\n                                \"门店电话：\" +\n                                  _vm._s(_vm.orderDatalist.systemStore.phone)\n                              ),\n                            ]),\n                            _vm._v(\" \"),\n                            _c(\"div\", { staticClass: \"description-term\" }, [\n                              _vm._v(\n                                \"门店地址：\" +\n                                  _vm._s(\n                                    _vm.orderDatalist.systemStore.address +\n                                      _vm.orderDatalist.systemStore\n                                        .detailedAddress\n                                  )\n                              ),\n                            ]),\n                          ]\n                        : _vm._e(),\n                    ],\n                    2\n                  ),\n                  _vm._v(\" \"),\n                  _vm.orderDatalist.deliveryType === \"express\"\n                    ? [\n                        _c(\"el-divider\"),\n                        _vm._v(\" \"),\n                        _c(\"div\", { staticClass: \"title\" }, [\n                          _vm._v(\"物流信息\"),\n                        ]),\n                        _vm._v(\" \"),\n                        _c(\"div\", { staticClass: \"acea-row\" }, [\n                          _c(\"div\", { staticClass: \"description-term\" }, [\n                            _vm._v(\n                              \"快递公司：\" +\n                                _vm._s(_vm.orderDatalist.deliveryName)\n                            ),\n                          ]),\n                          _vm._v(\" \"),\n                          _c(\n                            \"div\",\n                            { staticClass: \"description-term\" },\n                            [\n                              _vm._v(\n                                \"快递单号：\" +\n                                  _vm._s(_vm.orderDatalist.deliveryId) +\n                                  \"\\n            \"\n                              ),\n                              _c(\n                                \"el-button\",\n                                {\n                                  directives: [\n                                    {\n                                      name: \"hasPermi\",\n                                      rawName: \"v-hasPermi\",\n                                      value: [\"admin:order:logistics:info\"],\n                                      expression:\n                                        \"['admin:order:logistics:info']\",\n                                    },\n                                  ],\n                                  staticStyle: { \"margin-left\": \"5px\" },\n                                  attrs: { type: \"primary\", size: \"mini\" },\n                                  on: { click: _vm.openLogistics },\n                                },\n                                [_vm._v(\"物流查询\")]\n                              ),\n                            ],\n                            1\n                          ),\n                        ]),\n                      ]\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.orderDatalist.deliveryType === \"send\"\n                    ? [\n                        _c(\"el-divider\"),\n                        _vm._v(\" \"),\n                        _c(\"div\", { staticClass: \"title\" }, [\n                          _vm._v(\"配送信息\"),\n                        ]),\n                        _vm._v(\" \"),\n                        _c(\"div\", { staticClass: \"acea-row\" }, [\n                          _c(\"div\", { staticClass: \"description-term\" }, [\n                            _vm._v(\n                              \"送货人姓名：\" +\n                                _vm._s(_vm.orderDatalist.deliveryName)\n                            ),\n                          ]),\n                          _vm._v(\" \"),\n                          _c(\"div\", { staticClass: \"description-term\" }, [\n                            _vm._v(\n                              \"送货人电话：\" +\n                                _vm._s(_vm.orderDatalist.deliveryId)\n                            ),\n                          ]),\n                        ]),\n                      ]\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.orderDatalist.mark\n                    ? [\n                        _c(\"el-divider\"),\n                        _vm._v(\" \"),\n                        _c(\"div\", { staticClass: \"title\" }, [\n                          _vm._v(\"用户备注\"),\n                        ]),\n                        _vm._v(\" \"),\n                        _c(\"div\", { staticClass: \"acea-row\" }, [\n                          _c(\"div\", { staticClass: \"description-term\" }, [\n                            _vm._v(_vm._s(_vm.orderDatalist.mark)),\n                          ]),\n                        ]),\n                      ]\n                    : _vm._e(),\n                ],\n                2\n              ),\n            ]\n          )\n        : _vm._e(),\n      _vm._v(\" \"),\n      _vm.orderDatalist\n        ? _c(\n            \"el-dialog\",\n            {\n              attrs: { title: \"提示\", visible: _vm.modal2, width: \"30%\" },\n              on: {\n                \"update:visible\": function ($event) {\n                  _vm.modal2 = $event\n                },\n              },\n            },\n            [\n              _c(\"div\", { staticClass: \"logistics acea-row row-top\" }, [\n                _c(\"div\", { staticClass: \"logistics_img\" }, [\n                  _c(\"img\", {\n                    attrs: { src: require(\"@/assets/imgs/expressi.jpg\") },\n                  }),\n                ]),\n                _vm._v(\" \"),\n                _c(\"div\", { staticClass: \"logistics_cent\" }, [\n                  _c(\"span\", { staticClass: \"mb10\" }, [\n                    _vm._v(\n                      \"物流公司：\" + _vm._s(_vm.orderDatalist.deliveryName)\n                    ),\n                  ]),\n                  _vm._v(\" \"),\n                  _c(\"span\", [\n                    _vm._v(\"物流单号：\" + _vm._s(_vm.orderDatalist.deliveryId)),\n                  ]),\n                ]),\n              ]),\n              _vm._v(\" \"),\n              _c(\n                \"div\",\n                { staticClass: \"acea-row row-column-around trees-coadd\" },\n                [\n                  _c(\n                    \"div\",\n                    { staticClass: \"scollhide\" },\n                    [\n                      _c(\n                        \"el-timeline\",\n                        { attrs: { reverse: _vm.reverse } },\n                        _vm._l(_vm.result, function (item, i) {\n                          return _c(\"el-timeline-item\", { key: i }, [\n                            _c(\"p\", {\n                              staticClass: \"time\",\n                              domProps: { textContent: _vm._s(item.time) },\n                            }),\n                            _vm._v(\" \"),\n                            _c(\"p\", {\n                              staticClass: \"content\",\n                              domProps: { textContent: _vm._s(item.status) },\n                            }),\n                          ])\n                        }),\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                ]\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"span\",\n                {\n                  staticClass: \"dialog-footer\",\n                  attrs: { slot: \"footer\" },\n                  slot: \"footer\",\n                },\n                [\n                  _c(\n                    \"el-button\",\n                    {\n                      attrs: { type: \"primary\" },\n                      on: {\n                        click: function ($event) {\n                          _vm.modal2 = false\n                        },\n                      },\n                    },\n                    [_vm._v(\"关闭\")]\n                  ),\n                ],\n                1\n              ),\n            ]\n          )\n        : _vm._e(),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}