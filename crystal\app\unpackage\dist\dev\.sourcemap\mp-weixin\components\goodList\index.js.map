{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/goodList/index.vue?3c2b", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/goodList/index.vue?3343", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/goodList/index.vue?3708", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/goodList/index.vue?de4f", "uni-app:///components/goodList/index.vue", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/goodList/index.vue?ee22", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/goodList/index.vue?4834"], "names": ["computed", "props", "status", "type", "default", "bastList", "data", "methods", "goDetail", "uni", "url"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACgM;AAChM,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/BA;AAAA;AAAA;AAAA;AAAmvB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;AC2BvwB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;gBACA;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;QACA;MACA;IACA;EACA;EACAE;IACA,QAEA;EACA;EACAC;IACAC;MACA;QACAC;UACAC;QACA;MACA;IACA;EAEA;AACA;AAAA,4B;;;;;;;;;;;;;AC1DA;AAAA;AAAA;AAAA;AAA06C,CAAgB,ovCAAG,EAAC,C;;;;;;;;;;;ACA97C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/goodList/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=11dfe3b9&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=11dfe3b9&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"11dfe3b9\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/goodList/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=11dfe3b9&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.bastList, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var m0 =\n      item.vip_price && item.vip_price > 0\n        ? Number(item.sales) + Number(item.ficti) || 0\n        : null\n    var m1 = !(item.vip_price && item.vip_price > 0)\n      ? Number(item.sales) + Number(item.ficti) || 0\n      : null\n    return {\n      $orig: $orig,\n      m0: m0,\n      m1: m1,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class='goodList'>\r\n\t\t<block v-for=\"(item,index) in bastList\" :key=\"index\">\r\n\t\t\t<view @click=\"goDetail(item)\" class='item acea-row row-between-wrapper' hover-class=\"none\">\r\n\t\t\t\t<view class='pictrue'>\r\n\t\t\t\t\t<image :src='item.image'></image>\r\n\t\t\t\t\t<span class=\"pictrue_log pictrue_log_class\" v-if=\"item.activityH5 && item.activityH5.type === '1'\">秒杀</span>\r\n\t\t\t\t\t<span class=\"pictrue_log pictrue_log_class\" v-if=\"item.activityH5 && item.activityH5.type === '2'\">砍价</span>\r\n\t\t\t\t\t<span class=\"pictrue_log pictrue_log_class\" v-if=\"item.activityH5 && item.activityH5.type === '3'\">拼团</span>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='underline'>\r\n\t\t\t\t\t<view class='text'>\r\n\t\t\t\t\t\t<view class='line1'>{{item.storeName}}</view>\r\n\t\t\t\t\t\t<view class='money font-color'>￥<text class='num'>{{item.price}}</text></view>\r\n\t\t\t\t\t\t<view class='vip-money acea-row row-middle' v-if=\"item.vip_price && item.vip_price > 0\">￥{{item.vip_price || 0}}\r\n\t\t\t\t\t\t\t<image src='../../static/images/vip.png'></image><text class='num'>已售{{Number(item.sales) + Number(item.ficti) || 0}}{{item.unitName}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='vip-money acea-row row-middle' v-else><text class='num'>已售{{Number(item.sales) + Number(item.ficti) || 0}}{{item.unitName}}</text></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='iconfont icon-gouwuche cart-color acea-row row-center-wrapper'></view>\r\n\t\t\t</view>\r\n\t\t</block>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {mapGetters} from \"vuex\";\r\n\timport { goShopDetail } from '@/libs/order.js'\r\n\texport default {\r\n\t\tcomputed: mapGetters(['uid']),\r\n\t\tprops: {\r\n\t\t\tstatus: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 0,\r\n\t\t\t},\r\n\t\t\tbastList: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault: function() {\r\n\t\t\t\t\treturn [];\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\r\n\t\t\t};\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgoDetail(item){\r\n\t\t\t\tgoShopDetail(item,this.uid).then(res=>{\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl:`/pages/goods_details/index?id=${item.id}`\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\t\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped lang='scss'>\r\n\t.goodList .item {\r\n\t\tposition: relative;\r\n\t\tpadding-left: 30rpx;\r\n\t}\r\n\r\n\t.goodList .item .pictrue {\r\n\t\twidth: 180rpx;\r\n\t\theight: 180rpx;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.goodList .item .pictrue image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 6rpx;\r\n\t}\r\n\r\n\t.goodList .item .pictrue .numPic {\r\n\t\tposition: absolute;\r\n\t\tleft: 7rpx;\r\n\t\ttop: 7rpx;\r\n\t\twidth: 50rpx;\r\n\t\theight: 50rpx;\r\n\t\tborder-radius: 50%;\r\n\t}\r\n\r\n\t.goodList .item .underline {\r\n\t\tpadding: 30rpx 30rpx 30rpx 0;\r\n\t\tborder-bottom: 1px solid #f5f5f5;\r\n\t}\r\n\r\n\t.goodList .item:nth-last-child(1) .underline {\r\n\t\tborder-bottom: 0;\r\n\t}\r\n\r\n\t.goodList .item .text {\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #222;\r\n\t\twidth: 489rpx;\r\n\t}\r\n\r\n\t.goodList .item .text .money {\r\n\t\tfont-size: 26rpx;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-top: 50rpx;\r\n\t}\r\n\r\n\t.goodList .item .text .money .num {\r\n\t\tfont-size: 34rpx;\r\n\t}\r\n\r\n\t.goodList .item .text .vip-money {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #282828;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-top: 15rpx;\r\n\t}\r\n\r\n\t.goodList .item .text .vip-money image {\r\n\t\twidth: 46rpx;\r\n\t\theight: 21rpx;\r\n\t\tmargin: 0 22rpx 0 5rpx;\r\n\t}\r\n\r\n\t.goodList .item .text .vip-money .num {\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #aaa;\r\n\t\tfont-weight: normal;\r\n\t\tmargin: -2rpx 0 0 0;\r\n\t}\r\n\r\n\t.goodList .item .iconfont {\r\n\t\tposition: absolute;\r\n\t\tright: 30rpx;\r\n\t\twidth: 50rpx;\r\n\t\theight: 50rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tfont-size: 30rpx;\r\n\t\tbottom: 38rpx;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=11dfe3b9&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=11dfe3b9&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363904114\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}