{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\Category\\edit.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\Category\\edit.vue", "mtime": 1753666157755}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\"use strict\";\n\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar categoryApi = _interopRequireWildcard(require(\"@/api/categoryApi.js\"));\nvar selfUtil = _interopRequireWildcard(require(\"@/utils/ZBKJIutil.js\"));\nfunction _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != _typeof(e) && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\nfunction _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; } //\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  // name: \"edit\"\n  props: {\n    prent: {\n      type: Object,\n      required: true\n    },\n    isCreate: {\n      type: Number,\n      default: 0\n    },\n    editData: {\n      type: Object\n    },\n    biztype: {\n      type: Object,\n      required: true\n    },\n    allTreeList: {\n      type: Array\n    }\n  },\n  data: function data() {\n    return {\n      loadingBtn: false,\n      constants: this.$constants,\n      editPram: {\n        extra: null,\n        name: null,\n        pid: null,\n        sort: 0,\n        status: true,\n        type: this.biztype.value,\n        url: null,\n        id: 0\n      },\n      categoryProps: {\n        value: 'id',\n        label: 'name',\n        children: 'child',\n        expandTrigger: 'hover',\n        checkStrictly: true,\n        emitPath: false\n      },\n      parentOptions: []\n    };\n  },\n  mounted: function mounted() {\n    this.initEditData();\n  },\n  methods: {\n    // 点击图标\n    addIcon: function addIcon() {\n      var _this = this;\n      _this.$modalIcon(function (icon) {\n        _this.editPram.extra = icon;\n      });\n    },\n    // 点击商品图\n    modalPicTap: function modalPicTap(tit, num, i) {\n      var _this = this;\n      var attr = [];\n      this.$modalUpload(function (img) {\n        if (tit === '1' && !num) {\n          _this.editPram.extra = img[0].sattDir;\n        }\n        if (tit === '2' && !num) {\n          img.map(function (item) {\n            attr.push(item.attachment_src);\n            _this.formValidate.slider_image.push(item);\n          });\n        }\n      }, tit, 'store');\n    },\n    close: function close() {\n      this.$emit('hideEditDialog');\n    },\n    initEditData: function initEditData() {\n      this.parentOptions = _toConsumableArray(this.allTreeList);\n      this.addTreeListLabelForCasCard(this.parentOptions, 'child');\n      var _this$editData = this.editData,\n        extra = _this$editData.extra,\n        name = _this$editData.name,\n        pid = _this$editData.pid,\n        sort = _this$editData.sort,\n        status = _this$editData.status,\n        type = _this$editData.type,\n        id = _this$editData.id,\n        url = _this$editData.url;\n      if (this.isCreate === 1) {\n        this.editPram.extra = extra;\n        this.editPram.name = name;\n        this.editPram.pid = pid;\n        this.editPram.sort = sort;\n        this.editPram.status = status;\n        this.editPram.type = type;\n        this.editPram.url = url;\n        this.editPram.id = id;\n      } else {\n        this.editPram.pid = this.prent.id;\n        this.editPram.type = this.biztype.value;\n      }\n    },\n    addTreeListLabelForCasCard: function addTreeListLabelForCasCard(arr, child) {\n      arr.forEach(function (o, i) {\n        if (o.child && o.child.length) {\n          // o.disabled = true\n          o.child.forEach(function (j) {\n            j.disabled = true;\n          });\n        }\n      });\n    },\n    handlerSubmit: function handlerSubmit(formName) {\n      var _this2 = this;\n      this.$refs[formName].validate(function (valid) {\n        if (!valid) return;\n        _this2.handlerSaveOrUpdate(_this2.isCreate === 0);\n      });\n    },\n    handlerSaveOrUpdate: function handlerSaveOrUpdate(isSave) {\n      var _this3 = this;\n      if (isSave) {\n        // this.editPram.pid = this.prent.id\n        this.loadingBtn = true;\n        categoryApi.addCategroy(this.editPram).then(function (data) {\n          _this3.$emit('hideEditDialog');\n          _this3.$message.success('创建目录成功');\n          _this3.loadingBtn = false;\n        }).catch(function () {\n          _this3.loadingBtn = false;\n        });\n      } else {\n        this.editPram.pid = Array.isArray(this.editPram.pid) ? this.editPram.pid[0] : this.editPram.pid;\n        this.loadingBtn = true;\n        categoryApi.updateCategroy(this.editPram).then(function (data) {\n          _this3.$emit('hideEditDialog');\n          _this3.$message.success('更新目录成功');\n          _this3.loadingBtn = false;\n        }).catch(function () {\n          _this3.loadingBtn = false;\n        });\n      }\n    }\n  }\n};", null]}