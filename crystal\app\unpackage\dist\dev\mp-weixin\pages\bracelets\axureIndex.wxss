@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.ai-recommend {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 40rpx;
}
/* 步骤指示器样式 */
.steps-container {
  padding: 30rpx 0;
  background-color: #fff;
  margin-bottom: 20rpx;
}
.steps {
  display: flex;
  justify-content: space-between;
  padding: 0 40rpx;
}
.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
}
.step-number {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  background-color: #ddd;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  margin-bottom: 10rpx;
  z-index: 2;
}
.step.active .step-number {
  background-color: #c9ab79;
}
.step-text {
  font-size: 24rpx;
  color: #666;
}
.step.active .step-text {
  color: #c9ab79;
}
.step-line {
  position: absolute;
  top: 25rpx;
  right: -50%;
  width: 100%;
  height: 2rpx;
  background-color: #ddd;
  z-index: 1;
}
.step.active .step-line {
  background-color: #c9ab79;
}
/* 表单样式 */
.form-container {
  background-color: #fff;
  padding: 30rpx;
  border-radius: 20rpx;
  margin: 0 20rpx;
}
.form-section {
  margin-bottom: 40rpx;
}
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.form-item {
  margin-bottom: 30rpx;
}
.form-label {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
}
.form-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}
.radio-group {
  display: flex;
  gap: 30rpx;
}
.radio-item {
  padding: 15rpx 40rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 10rpx;
  font-size: 28rpx;
  color: #666;
}
.radio-item.active {
  background-color: #c9ab79;
  color: #fff;
  border-color: #c9ab79;
}
.form-picker {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}
.picker-value {
  color: #333;
}
.budget-slider {
  padding: 20rpx 0;
}
.budget-value {
  text-align: center;
  font-size: 32rpx;
  color: #c9ab79;
  margin-top: 10rpx;
  font-weight: bold;
}
.color-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}
.color-item {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  border: 1rpx solid #e0e0e0;
  box-sizing: border-box;
}
.color-item.active {
  border: 3rpx solid #c9ab79;
  -webkit-transform: scale(1.1);
          transform: scale(1.1);
}
.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}
.checkbox-item {
  padding: 10rpx 20rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 10rpx;
  font-size: 26rpx;
  color: #666;
}
.checkbox-item.active {
  background-color: #c9ab79;
  color: #fff;
  border-color: #c9ab79;
}
.form-textarea {
  width: 100%;
  height: 200rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}
.chakra-selector {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.chakra-item {
  display: flex;
  align-items: center;
  padding: 15rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 10rpx;
  gap: 20rpx;
}
.chakra-item.active {
  background-color: rgba(201, 171, 121, 0.1);
  border-color: #c9ab79;
}
.chakra-color {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
}
.chakra-name {
  font-size: 28rpx;
  color: #333;
}
.submit-button {
  width: 100%;
  height: 90rpx;
  background-color: #c9ab79;
  color: #fff;
  font-size: 32rpx;
  border-radius: 45rpx;
  margin-top: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
/* 生成中样式 */
.generating {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 80vh;
  background-color: #fff;
  margin: 0 20rpx;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
}
.loading-animation {
  display: flex;
  gap: 20rpx;
  margin-bottom: 40rpx;
}
.loading-circle {
  width: 30rpx;
  height: 30rpx;
  border-radius: 50%;
  background-color: #c9ab79;
  -webkit-animation: loading 1.5s infinite ease-in-out;
          animation: loading 1.5s infinite ease-in-out;
}
.loading-circle:nth-child(2) {
  -webkit-animation-delay: 0.5s;
          animation-delay: 0.5s;
}
.loading-circle:nth-child(3) {
  -webkit-animation-delay: 1s;
          animation-delay: 1s;
}
@-webkit-keyframes loading {
0%,
  100% {
    -webkit-transform: scale(0.5);
            transform: scale(0.5);
    opacity: 0.5;
}
50% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 1;
}
}
@keyframes loading {
0%,
  100% {
    -webkit-transform: scale(0.5);
            transform: scale(0.5);
    opacity: 0.5;
}
50% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 1;
}
}
.generating-text {
  font-size: 36rpx;
  color: #333;
  margin-bottom: 20rpx;
}
.generating-subtext {
  font-size: 28rpx;
  color: #999;
}
/* AI生成进度条样式 */
.progress-container {
  width: 90%;
  margin: 30rpx 0;
}
.progress-bar {
  height: 20rpx;
  background-color: #f0f0f0;
  border-radius: 10rpx;
  overflow: hidden;
}
.progress-fill {
  height: 100%;
  background-color: #c9ab79;
  border-radius: 10rpx;
  transition: width 0.3s ease;
}
.progress-text {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}
/* AI输出显示 */
.ai-output {
  width: 100%;
  margin-top: 30rpx;
  padding: 20rpx;
  background-color: #f9f6f0;
  border-radius: 10rpx;
  max-height: 40vh;
  overflow-y: auto;
}
.output-title {
  font-size: 28rpx;
  color: #c9ab79;
  margin-bottom: 10rpx;
  font-weight: bold;
}
.output-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  white-space: pre-wrap;
  position: relative;
}
.typing-cursor {
  display: inline-block;
  width: 2px;
  height: 18px;
  background-color: #666;
  margin-left: 2px;
  vertical-align: middle;
  -webkit-animation: cursor-blink 0.8s infinite;
          animation: cursor-blink 0.8s infinite;
}
@-webkit-keyframes cursor-blink {
0%,
  100% {
    opacity: 0;
}
50% {
    opacity: 1;
}
}
@keyframes cursor-blink {
0%,
  100% {
    opacity: 0;
}
50% {
    opacity: 1;
}
}
/* 结果展示样式 */
.result-display {
  background-color: #fff;
  border-radius: 20rpx;
  margin: 0 20rpx;
  padding: 30rpx;
}
.bracelet-prediv {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 40rpx 0;
  position: relative;
}
.circle-container {
  position: relative;
  border-radius: 50%;
  border: 1rpx solid #e0e0e0;
}
.marble {
  position: absolute;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}
.recommendation-details {
  margin-top: 40rpx;
}
.recommendation-header {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}
.recommendation-body {
  height: 700rpx;
  width: 100%;
}
.ai-generation-status {
  padding: 20rpx;
  background-color: #f9f5eb;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
}
.loading-animation {
  display: flex;
  justify-content: center;
  margin-bottom: 10rpx;
}
.loading-animation .dot {
  width: 12rpx;
  height: 12rpx;
  background-color: #c9ab79;
  border-radius: 50%;
  margin: 0 6rpx;
  -webkit-animation: bounce 1.4s infinite ease-in-out both;
          animation: bounce 1.4s infinite ease-in-out both;
}
.loading-animation .dot:nth-child(1) {
  -webkit-animation-delay: -0.32s;
          animation-delay: -0.32s;
}
.loading-animation .dot:nth-child(2) {
  -webkit-animation-delay: -0.16s;
          animation-delay: -0.16s;
}
@-webkit-keyframes bounce {
0%,
  80%,
  100% {
    -webkit-transform: scale(0);
            transform: scale(0);
}
40% {
    -webkit-transform: scale(1);
            transform: scale(1);
}
}
@keyframes bounce {
0%,
  80%,
  100% {
    -webkit-transform: scale(0);
            transform: scale(0);
}
40% {
    -webkit-transform: scale(1);
            transform: scale(1);
}
}
.generation-text {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 15rpx;
  display: block;
}
.progress-bar {
  height: 10rpx;
  background-color: #e0e0e0;
  border-radius: 10rpx;
  overflow: hidden;
  margin-bottom: 10rpx;
}
.progress-filled {
  height: 100%;
  background-color: #c9ab79;
  border-radius: 10rpx;
  transition: width 0.3s;
}
.progress-text {
  font-size: 24rpx;
  color: #888;
  text-align: center;
  display: block;
}
.ai-output-container {
  padding: 15rpx;
  background-color: #fff;
  border-radius: 8rpx;
  margin: 10rpx 0;
}
.action-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 30rpx;
  padding: 0 20rpx;
}
.action-buttons-row {
  padding: 0 20rpx 30rpx;
}
.action-buttons-fixed {
  display: flex;
  justify-content: space-between;
  margin-top: 30rpx;
  padding: 0 20rpx 30rpx;
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  border-top: 1px solid #f0f0f0;
  z-index: 100;
}
.action-button {
  flex: 1;
  margin: 0 10rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 28rpx;
  background-color: #c9ab79;
  color: #fff;
}
.action-button.outline {
  background-color: transparent;
  border: 1px solid #c9ab79;
  color: #c9ab79;
}
.edit-button,
.confirm-button {
  flex: 1;
  margin: 0 10rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 28rpx;
}
.edit-button {
  background-color: #f8f8f8;
  color: #666;
  border: 1px solid #e0e0e0;
}
.confirm-button {
  background-color: #c9ab79;
  color: #fff;
}
.error-message {
  padding: 20rpx;
  color: #ff4d4f;
  text-align: center;
  font-size: 28rpx;
}
/* 优化Markdown样式 - 确保在微信小程序环境中正确渲染 */
.markdown-content {
  font-size: 28rpx;
  line-height: 1.5;
  white-space: pre-wrap;
  color: #333;
  padding: 10rpx 0;
}
/* 修复微信小程序不支持 >>> 选择器的问题 */
.h1,
rich-text .h1 {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin: 10rpx 0;
  border-bottom: 1px solid #eee;
  padding-bottom: 10rpx;
}
.h2,
rich-text .h2 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin: 10rpx 0;
  border-bottom: 1px solid #f3f3f3;
  padding-bottom: 8rpx;
}
.h3,
rich-text .h3 {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin: 10rpx 0;
}
.p,
rich-text .p {
  margin: 8rpx 0;
  line-height: 1.5;
}
.ul,
.ol,
rich-text .ul,
rich-text .ol {
  padding-left: 30rpx;
  margin: 8rpx 0 15rpx 0;
}
.li,
rich-text .li {
  margin: 5rpx 0;
}
.blockquote,
rich-text .blockquote {
  border-left: 4rpx solid #c9ab79;
  padding: 8rpx 16rpx;
  margin: 10rpx 0;
  background-color: #f9f5eb;
  color: #666;
}
.strong,
rich-text .strong {
  font-weight: bold;
  color: #333;
}
.em,
rich-text .em {
  font-style: italic;
}
/* 修复图片和其他元素的样式 */
.img,
rich-text .img {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 10rpx auto;
  border-radius: 6rpx;
}
/* 确保珠子列表样式正确 */
.beads-list {
  margin-top: 20rpx;
  border-top: 1px solid #eee;
  padding-top: 20rpx;
  background-color: #fff;
  border-radius: 8rpx;
}
.beads-section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
}
.bead-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1px solid #f0f0f0;
}
.bead-item image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  background-color: #f5f5f5;
  /* 占位背景色，防止图片加载失败时显示空白 */
}
.bead-info {
  flex: 1;
}
.bead-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 6rpx;
  font-weight: bold;
}
.bead-effect {
  font-size: 24rpx;
  color: #666;
}
.total-price {
  text-align: right;
  padding: 20rpx;
  font-size: 30rpx;
  font-weight: bold;
  color: #c9ab79;
}

