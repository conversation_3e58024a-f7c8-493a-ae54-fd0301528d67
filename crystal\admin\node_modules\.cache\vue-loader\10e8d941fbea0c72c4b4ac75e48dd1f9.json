{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\storeComment\\creatComment.vue?vue&type=template&id=24e2766f&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\storeComment\\creatComment.vue", "mtime": 1753666157925}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\n<el-form :model=\"formValidate\" :rules=\"rules\" ref=\"formValidate\" label-width=\"100px\" class=\"demo-formValidate\"  v-loading=\"loading\">\n  <el-form-item label=\"商品：\" prop=\"productId\">\n    <div class=\"upLoadPicBox\" @click=\"changeGood\">\n      <div v-if=\"formValidate.productId\" class=\"pictrue\"><img :src=\"image\"></div>\n      <div v-else class=\"upLoad\">\n        <i class=\"el-icon-camera cameraIconfont\"/>\n      </div>\n    </div>\n  </el-form-item>\n  <el-form-item label=\"用户名称：\" prop=\"nickname\">\n    <el-input type=\"text\" v-model=\"formValidate.nickname \"></el-input>\n  </el-form-item>\n  <el-form-item label=\"评价文字：\" prop=\"comment\">\n    <el-input type=\"textarea\" v-model=\"formValidate.comment \"></el-input>\n  </el-form-item>\n  <el-form-item label=\"商品分数：\" prop=\"productScore\" class=\"productScore\">\n    <el-rate v-model=\"formValidate.productScore\"></el-rate>\n    <!--<el-input-number type=\"textarea\" v-model=\"formValidate.productScore \"></el-input-number>-->\n  </el-form-item>\n  <el-form-item label=\"服务分数：\" prop=\"serviceScore\" class=\"productScore\">\n    <el-rate v-model=\"formValidate.serviceScore\"></el-rate>\n    <!--<el-input-number type=\"textarea\" v-model=\"formValidate.serviceScore \"></el-input-number>-->\n  </el-form-item>\n  <el-form-item label=\"用户头像：\" prop=\"avatar\">\n    <div class=\"upLoadPicBox\" @click=\"modalPicTap('1')\">\n      <div v-if=\"formValidate.avatar\" class=\"pictrue\"><img :src=\"formValidate.avatar\"></div>\n      <div v-else class=\"upLoad\">\n        <i class=\"el-icon-camera cameraIconfont\"/>\n      </div>\n    </div>\n  </el-form-item>\n  <el-form-item label=\"评价图片：\">\n    <div class=\"acea-row\">\n      <div\n        v-for=\"(item,index) in pics\"\n        :key=\"index\"\n        class=\"pictrue\"\n        draggable=\"false\"\n        @dragstart=\"handleDragStart($event, item)\"\n        @dragover.prevent=\"handleDragOver($event, item)\"\n        @dragenter=\"handleDragEnter($event, item)\"\n        @dragend=\"handleDragEnd($event, item)\"\n      >\n        <img :src=\"item\">\n        <i class=\"el-icon-error btndel\" @click=\"handleRemove(index)\" />\n        <!--<Button shape=\"circle\" icon=\"md-close\" class=\"btndel\" @click.native=\"handleRemove(index)\" />-->\n      </div>\n      <div v-if=\"pics<10\" class=\"upLoadPicBox\" @click=\"modalPicTap('2')\">\n        <div class=\"upLoad\">\n          <i class=\"el-icon-camera cameraIconfont\" />\n        </div>\n      </div>\n    </div>\n  </el-form-item>\n  <el-form-item>\n    <el-button size=\"mini\" type=\"primary\" @click=\"submitForm('formValidate')\"  :loading=\"loadingbtn\">提交</el-button>\n    <el-button size=\"mini\" @click=\"resetForm('formValidate')\">重置</el-button>\n  </el-form-item>\n</el-form>\n", null]}