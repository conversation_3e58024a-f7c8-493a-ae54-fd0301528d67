{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\marketing\\coupon\\list\\creatCoupon.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\marketing\\coupon\\list\\creatCoupon.vue", "mtime": 1753666157891}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _marketing = require(\"@/api/marketing\");\nvar _store = require(\"@/api/store\");\nvar _validate = require(\"@/utils/validate\");\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  name: \"creatCoupon\",\n  data: function data() {\n    return {\n      pickerOptions: {\n        disabledDate: function disabledDate(time) {\n          // return time.getTime() < new Date().setTime(new Date().getTime() - 3600 * 1000 * 24); //不限制未来时间\n          return time.getTime() < Date.now() - 8.64e7 || time.getTime() > Date.now() + 600 * 8.64e7; //限制未来时间\n        }\n      },\n      loading: false,\n      threshold: false,\n      termTime: [],\n      props2: {\n        children: 'child',\n        label: 'name',\n        value: 'id',\n        checkStrictly: true,\n        emitPath: false\n      },\n      couponType: 0,\n      term: 'termday',\n      merCateList: [],\n      // 商户分类筛选\n      ruleForm: {\n        useType: 1,\n        isFixedTime: false,\n        name: '',\n        money: 1,\n        minPrice: 1,\n        day: null,\n        isForever: false,\n        primaryKey: '',\n        type: 2,\n        isLimited: false,\n        useStartTime: '',\n        // 使用\n        useEndTime: '',\n        // 结束\n        receiveStartTime: '',\n        //领取\n        receiveEndTime: '',\n        sort: 0,\n        total: 1,\n        status: false,\n        checked: []\n      },\n      isForeverTime: [],\n      rules: {\n        name: [{\n          required: true,\n          message: '请输入优惠券名称',\n          trigger: 'blur'\n        }],\n        day: [{\n          required: true,\n          message: '请输入使用有效期限（天）',\n          trigger: 'blur'\n        }],\n        money: [{\n          required: true,\n          message: '请输入优惠券面值',\n          trigger: 'blur'\n        }],\n        primaryKey: [{\n          required: true,\n          message: '请选择品类',\n          trigger: 'change'\n        }],\n        checked: [{\n          required: true,\n          message: '请至少选择一个商品',\n          trigger: 'change',\n          type: 'array'\n        }],\n        isForeverTime: [{\n          required: true,\n          message: '请选择领取时间',\n          trigger: 'change',\n          type: 'array'\n        }],\n        total: [{\n          required: true,\n          message: '请输入发布数量',\n          trigger: 'blur'\n        }],\n        minPrice: [{\n          required: true,\n          message: '请输入最低消费',\n          trigger: 'blur'\n        }]\n      }\n    };\n  },\n  mounted: function mounted() {\n    this.getCategorySelect();\n    if (this.$route.params.id) this.getInfo();\n  },\n  methods: {\n    handleTimestamp: function handleTimestamp() {},\n    // 商品分类；\n    getCategorySelect: function getCategorySelect() {\n      var _this2 = this;\n      (0, _store.categoryApi)({\n        status: -1,\n        type: 1\n      }).then(function (res) {\n        _this2.merCateList = res;\n        _this2.merCateList.map(function (item) {\n          _this2.$set(item, 'disabled', true);\n        });\n      });\n    },\n    getInfo: function getInfo() {\n      var _this3 = this;\n      this.loading = true;\n      (0, _marketing.couponInfoApi)({\n        id: this.$route.params.id\n      }).then(function (res) {\n        var info = res.coupon;\n        _this3.ruleForm = {\n          useType: info.useType,\n          isFixedTime: info.isFixedTime,\n          isForever: info.isForever,\n          name: info.name,\n          money: info.money,\n          minPrice: info.minPrice,\n          day: info.day,\n          type: info.type,\n          isLimited: info.isLimited,\n          sort: info.sort,\n          total: info.total,\n          status: info.status,\n          primaryKey: Number(info.primaryKey),\n          checked: res.product || []\n        };\n        info.minPrice == 0 ? _this3.threshold = false : _this3.threshold = true;\n        info.isForever ? _this3.isForeverTime = [info.receiveStartTime, info.receiveEndTime] : _this3.isForeverTime = [];\n        info.isFixedTime && info.useStartTime && info.useEndTime ? _this3.termTime = [info.useStartTime, info.useEndTime] : _this3.termTime = [];\n        _this3.loading = false;\n      }).catch(function (res) {\n        _this3.loading = false;\n        _this3.$message.error(res.message);\n      });\n    },\n    handleRemove: function handleRemove(i) {\n      this.ruleForm.checked.splice(i, 1);\n    },\n    changeGood: function changeGood() {\n      var _this = this;\n      this.$modalGoodList(function (row) {\n        _this.ruleForm.checked = row;\n      }, 'many', _this.ruleForm.checked);\n    },\n    submitForm: (0, _validate.Debounce)(function (formName) {\n      var _this4 = this;\n      if (this.ruleForm.isFixedTime && !this.termTime || this.ruleForm.isFixedTime && !this.termTime.length) return this.$message.warning(\"请选择使用有效期限\");\n      if (this.ruleForm.isForever && !this.isForeverTime || this.ruleForm.isForever && !this.isForeverTime.length) return this.$message.warning(\"请选择请选择领取时间\");\n      if (this.ruleForm.useType === 2) this.ruleForm.primaryKey = this.ruleForm.checked.map(function (item) {\n        return item.id;\n      }).join(',');\n      if (this.ruleForm.useType === 1) this.ruleForm.primaryKey = '';\n      if (!this.threshold) this.ruleForm.minPrice = 0;\n      if (!this.ruleForm.isLimited) this.ruleForm.total = 0;\n      this.ruleForm.isFixedTime && this.termTime.length ? (this.ruleForm.useStartTime = this.termTime[0], this.ruleForm.day = null) : this.ruleForm.useStartTime = '';\n      this.ruleForm.isFixedTime && this.termTime.length ? (this.ruleForm.useEndTime = this.termTime[1], this.ruleForm.day = null) : this.ruleForm.useEndTime = '';\n      this.ruleForm.isForever && this.isForeverTime.length ? this.ruleForm.receiveStartTime = this.isForeverTime[0] : this.ruleForm.receiveStartTime = '';\n      this.ruleForm.isForever && this.isForeverTime.length ? this.ruleForm.receiveEndTime = this.isForeverTime[1] : this.ruleForm.receiveEndTime = '';\n      this.$refs[formName].validate(function (valid) {\n        if (valid) {\n          _this4.loading = true;\n          (0, _marketing.couponSaveApi)(_this4.ruleForm).then(function () {\n            _this4.$message.success(\"新增成功\");\n            _this4.loading = false;\n            setTimeout(function () {\n              _this4.$router.push({\n                path: \"/marketing/coupon/list\"\n              });\n            }, 200);\n          }).catch(function () {\n            _this4.loading = false;\n          });\n        } else {\n          _this4.loading = false;\n          return false;\n        }\n      });\n    })\n  }\n};", null]}