<view class="productSort data-v-a187a476"><movable-area style="width:100vw;height:100vh;position:fixed;top:0;left:0;z-index:999;pointer-events:none;" class="data-v-a187a476"><block wx:if="{{isDragging}}"><movable-view style="pointer-events:auto;" x="{{dragPosition.x}}" y="{{dragPosition.y}}" direction="all" animation="{{false}}" out-of-bounds="{{true}}" data-event-opts="{{[['change',[['onChange',['$event']]]],['touchend',[['onTouchEnd',['$event']]]]]}}" bindchange="__e" bindtouchend="__e" class="data-v-a187a476"><image style="{{'width:'+(dragProduct.width*3+'rpx')+';'+('height:'+(dragProduct.height*3+'rpx')+';')}}" src="{{dragProduct.image}}" mode="aspectFit" class="data-v-a187a476"></image></movable-view></block></movable-area><movable-area style="width:100vw;height:100vh;position:fixed;top:0;left:0;z-index:999;pointer-events:none;" class="data-v-a187a476"><block wx:if="{{isDraggingMarble}}"><movable-view style="pointer-events:auto;" x="{{dragPosition.x}}" y="{{dragPosition.y}}" direction="all" animation="{{false}}" out-of-bounds="{{true}}" data-event-opts="{{[['change',[['onChange2',['$event']]]],['touchend',[['onMarbleTouchEnd',['$event']]]]]}}" bindchange="__e" bindtouchend="__e" class="data-v-a187a476"><image style="{{'width:'+(dragProduct.width*3+'rpx')+';'+('height:'+(dragProduct.height*3+'rpx')+';')}}" src="{{dragProduct.image}}" mode="aspectFit" class="data-v-a187a476"></image></movable-view></block></movable-area><view class="top data-v-a187a476"><view class="left-bbb data-v-a187a476"><block wx:if="{{showDropdown}}"><view class="specifications-dropdown data-v-a187a476" style="{{'left:'+(dropdownLeft+'rpx')+';'}}"><view class="data-v-a187a476"><view class="specs-header data-v-a187a476" style="justify-content:center;"><text class="specs-title data-v-a187a476">{{productList[scrollIndex].name}}</text></view><view class="specs-list data-v-a187a476"><block wx:for="{{productList[scrollIndex].child}}" wx:for-item="spec" wx:for-index="specIndex" wx:key="specIndex"><view data-event-opts="{{[['tap',[['scrollToProduct',['$0',specIndex],['scrollIndex']]]]]}}" class="specs-item data-v-a187a476" bindtap="__e"><view class="specs-info data-v-a187a476"><text class="specs-size data-v-a187a476">{{spec.name}}</text></view></view></block></view></view></view></block><view class="left-bottom data-v-a187a476">点击宝石或长按拖动宝石，进行手串DIY定制</view><block wx:if="{{$root.g0}}"><scroll-view style="white-space:nowrap;border-top-left-radius:16rpx;border-top-right-radius:16rpx;" scroll-x="true" scroll-into-view="{{scrollIntoViewId}}" class="data-v-a187a476"><view class="left-child data-v-a187a476"><view style="text-align:center;display:flex;align-items:center;" class="data-v-a187a476"><view class="bigtitle data-v-a187a476" style="border-top-left-radius:16rpx;" id="{{'producttitle-'+index}}">{{''+productList[0].name}}</view><block wx:for="{{productList[0].child}}" wx:for-item="child" wx:for-index="index2" wx:key="index2"><view style="text-align:center;display:flex;align-items:center;flex:1;" class="data-v-a187a476"><view class="{{['smalltitle','data-v-a187a476',(indexOneProductIndex===index2)?'active':'']}}" id="{{'producttitle-'+index+index2}}" data-event-opts="{{[['tap',[['handleOneTabClick',[index2]]]]]}}" bindtap="__e">{{child.name+''}}</view></view></block></view></view></scroll-view></block><scroll-view style="white-space:nowrap;width:710rpx;" scroll-x="true" class="data-v-a187a476"><view class="left data-v-a187a476" style="display:flex;min-width:max-content;"><block wx:for="{{indexOneProduct}}" wx:for-item="product" wx:for-index="index1" wx:key="index1"><view class="product-item data-v-a187a476" style="margin:10rpx;display:flex;padding:10rpx;position:relative;align-items:center;flex-shrink:0;" id="{{'product-'+index}}" data-event-opts="{{[['tap',[['addProduct',['$0'],[[['indexOneProduct','',index1]]]]]]]}}" bindtap="__e"><image style="{{('height: 50rpx;')}}" mode="heightFix" src="{{product.image}}" data-event-opts="{{[['longpress',[['onLongPress',['$event','$0'],[[['indexOneProduct','',index1]]]]]],['touchmove',[['onTouchMove',['$event']]]],['touchend',[['onTouchEnd',['$event']]]]]}}" bindlongpress="__e" catchtouchmove="__e" catchtouchend="__e" class="data-v-a187a476"></image><view style="padding:0 10rpx;display:flex;flex-direction:column;align-items:center;justify-content:space-around;" class="_div data-v-a187a476"><view style="width:100%;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;" class="data-v-a187a476">{{''+product.storeName+''}}</view><view style="color:#c9ab79;font-weight:600;font-size:20rpx;" class="data-v-a187a476">{{''+product.width+"x"+product.height+'mm'}}</view></view><block wx:if="{{product.showSpecs}}"><view class="specifications-dropdown data-v-a187a476" style="{{'left:'+(specLeft+'rpx')+';'}}"><view class="specs-header data-v-a187a476"><text class="specs-title data-v-a187a476">规格选择</text><text data-event-opts="{{[['tap',[['toggleSpecifications',['$0','$event'],[[['indexOneProduct','',index1]]]]]]]}}" class="close-btn data-v-a187a476" catchtap="__e">×</text></view><view class="specs-list data-v-a187a476"><block wx:for="{{product.productValue}}" wx:for-item="spec" wx:for-index="specIndex" wx:key="specIndex"><view data-event-opts="{{[['tap',[['selectSpecification',['$0','$1'],[[['indexOneProduct','',index1]],[['indexOneProduct','',index1],['productValue','',specIndex]]]]]]]}}" class="specs-item data-v-a187a476" catchtap="__e"><view class="specs-info data-v-a187a476"><text class="specs-size data-v-a187a476">{{spec.width+"×"+spec.height+"mm"}}</text></view></view></block></view></view></block></view></block></view></scroll-view><block wx:if="{{$root.g1}}"><scroll-view style="white-space:nowrap;" scroll-x="true" scroll-into-view="{{scrollIntoViewId}}" class="data-v-a187a476"><view class="left-child data-v-a187a476"><view style="text-align:center;display:flex;align-items:center;width:710rpx;" class="data-v-a187a476"><view class="bigtitle data-v-a187a476" id="{{'producttitle-'+index}}">{{''+productList[1].name}}</view><block wx:for="{{productList[1].child}}" wx:for-item="child" wx:for-index="index2" wx:key="index2"><view style="text-align:center;display:flex;align-items:center;flex:1;" class="data-v-a187a476"><view class="{{['smalltitle','data-v-a187a476',(indexTwoProductIndex===index2)?'active':'']}}" id="{{'producttitle-'+index+index2}}" data-event-opts="{{[['tap',[['handleTwoTabClick',[index2]]]]]}}" bindtap="__e">{{child.name+''}}</view></view></block></view></view></scroll-view></block><scroll-view style="white-space:nowrap;width:710rpx;" scroll-x="true" class="data-v-a187a476"><view class="left data-v-a187a476" style="display:flex;min-width:max-content;"><block wx:for="{{indexTwoProduct}}" wx:for-item="product" wx:for-index="index1" wx:key="index1"><view class="product-item data-v-a187a476" style="margin:10rpx;display:flex;padding:10rpx;position:relative;align-items:center;flex-shrink:0;" id="{{'product-'+index}}" data-event-opts="{{[['tap',[['addProduct',['$0'],[[['indexTwoProduct','',index1]]]]]]]}}" bindtap="__e"><image style="{{('height: 50rpx;')}}" mode="heightFix" src="{{product.image}}" data-event-opts="{{[['longpress',[['onLongPress',['$event','$0'],[[['indexTwoProduct','',index1]]]]]],['touchmove',[['onTouchMove',['$event']]]],['touchend',[['onTouchEnd',['$event']]]]]}}" bindlongpress="__e" catchtouchmove="__e" catchtouchend="__e" class="data-v-a187a476"></image><view style="padding:0 10rpx;display:flex;flex-direction:column;align-items:center;justify-content:space-around;" class="_div data-v-a187a476"><view style="width:100%;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;" class="data-v-a187a476">{{''+product.storeName+''}}</view><view style="color:#c9ab79;font-weight:600;font-size:20rpx;" class="data-v-a187a476">{{''+product.width+"x"+product.height+'mm'}}</view></view><block wx:if="{{product.showSpecs}}"><view class="specifications-dropdown data-v-a187a476" style="{{'left:'+(specLeft+'rpx')+';'}}"><view class="specs-header data-v-a187a476"><text class="specs-title data-v-a187a476">规格选择</text><text data-event-opts="{{[['tap',[['toggleSpecifications',['$0','$event'],[[['indexTwoProduct','',index1]]]]]]]}}" class="close-btn data-v-a187a476" catchtap="__e">×</text></view><view class="specs-list data-v-a187a476"><block wx:for="{{product.productValue}}" wx:for-item="spec" wx:for-index="specIndex" wx:key="specIndex"><view data-event-opts="{{[['tap',[['selectSpecification',['$0','$1'],[[['indexTwoProduct','',index1]],[['indexTwoProduct','',index1],['productValue','',specIndex]]]]]]]}}" class="specs-item data-v-a187a476" catchtap="__e"><view class="specs-info data-v-a187a476"><text class="specs-size data-v-a187a476">{{spec.width+"×"+spec.height+"mm"}}</text></view></view></block></view></view></block></view></block></view></scroll-view></view><view class="right data-v-a187a476"><view style="display:flex;justify-content:center;align-items:center;height:700rpx;border-bottom:#c9ab79 1px dashed;position:relative;" class="_div data-v-a187a476"><view class="circle-container _div data-v-a187a476" style="{{'width:'+(radius*2+'rpx')+';'+('height:'+(radius*2+'rpx')+';')}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['longpress',[['onMarbleLongPress',[index,'$event']]]],['touchmove',[['onMarbleTouchMove',[index,'$event']]]],['touchend',[['onMarbleTouchEnd',[index,'$event']]]]]}}" class="marble _div data-v-a187a476" style="{{item.s0}}" bindlongpress="__e" catchtouchmove="__e" catchtouchend="__e"></view></block></view><view style="position:absolute;top:10rpx;left:10rpx;text-align:left;" class="_div data-v-a187a476"><block wx:if="{{userInfo.hand>0}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="hand-text-1 _div data-v-a187a476" bindtap="__e">您的手围<label style="color:#DD5C5F;font-size:26rpx;" class="_span data-v-a187a476">{{userInfo.hand+"mm"}}</label>。当前手串预估手围<label style="color:#DD5C5F;font-size:26rpx;" class="_span data-v-a187a476">{{realRadius+"mm"}}</label></view></block><block wx:else><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="hand-text-1 _div data-v-a187a476" bindtap="__e">当前手串预估手围<label style="color:#DD5C5F;font-size:26rpx;" class="_span data-v-a187a476">{{realRadius+"mm"}}</label></view></block></view><view style="position:absolute;top:10rpx;right:20rpx;display:flex;gap:10rpx;flex-direction:column;" class="_div data-v-a187a476"><image style="width:60rpx;" src="https://mpjoy.oss-cn-beijing.aliyuncs.com/crmebimage/public/maintain/2024/12/20/865abacc296140f79dcaa89b3d047e77r1bpnw5pwp.png" mode="widthFix" data-event-opts="{{[['tap',[['reset',['$event']]]]]}}" bindtap="__e" class="data-v-a187a476"></image><view class="hand-text-text data-v-a187a476">重置</view><image style="width:60rpx;" src="https://mpjoy.oss-cn-beijing.aliyuncs.com/crmebimage/public/maintain/2024/12/20/680c1002ea76449190e861e02b25d4bffvx635j582.png" mode="widthFix" data-event-opts="{{[['tap',[['backOne',['$event']]]]]}}" bindtap="__e" class="data-v-a187a476"></image><view class="hand-text-text data-v-a187a476">撤回</view><block wx:if="{{$root.g2>5}}"><image style="width:60rpx;" src="https://mpjoy.oss-cn-beijing.aliyuncs.com/crmebimage/public/maintain/2024/12/20/d93a44342db34f0ca5e3aa008079d160cpo5l4eqxt.png" mode="widthFix" data-event-opts="{{[['tap',[['userBraceletsSubmit',['$event']]]]]}}" bindtap="__e" class="data-v-a187a476"></image></block><block wx:if="{{$root.g3>5}}"><view class="hand-text-text data-v-a187a476">保存</view></block><block wx:if="{{userInfo.hand<=0}}"><image style="width:60rpx;" src="https://mpjoy.oss-cn-beijing.aliyuncs.com/crmebimage/public/maintain/2024/12/21/b06ed367abb24cc89f04c44718deac50t7lyqkalkg.png" mode="widthFix" data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" bindtap="__e" class="data-v-a187a476"></image></block><block wx:if="{{userInfo.hand<=0}}"><view class="hand-text-text data-v-a187a476">手围</view></block></view><view style="position:absolute;bottom:10rpx;left:10rpx;text-align:left;" class="_div data-v-a187a476"><block wx:if="{{$root.g4>0}}"><view data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" class="hand-text-1 _div data-v-a187a476" bindtap="__e">长按手串上面的珠子，可以灵活调整位置</view></block></view></view><view style="height:100rpx;display:flex;align-items:center;padding:0 10rpx;" class="_div data-v-a187a476"><block wx:if="{{!changeFlag}}"><view style="width:60%;overflow-x:scroll;display:flex;gap:10rpx;align-items:center;" class="_div data-v-a187a476"><block wx:for="{{selectList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="product-spec-item _div data-v-a187a476" style="position:relative;"><view class="image-wrapper _div data-v-a187a476"><image class="product-image data-v-a187a476" style="{{('height: 50rpx;')}}" mode="heightFix" src="{{item.image}}" data-event-opts="{{[['tap',[['_previewImage',['$0'],[[['selectList','',index,'image']]]]]]]}}" catchtap="__e"></image><image class="delete-icon data-v-a187a476" mode="heightFix" src="https://mpjoy.oss-cn-beijing.aliyuncs.com/crmebimage/public/maintain/2024/11/22/264c59ab0f89409d91641278e6e07ec2be9a9yqbwu.png" data-event-opts="{{[['tap',[['delProduct',[index]]]]]}}" bindtap="__e"></image></view><view class="price-tag _div data-v-a187a476"><text class="price-symbol data-v-a187a476">¥</text><text class="price-value data-v-a187a476">{{item.price}}</text></view></view></block></view></block><block wx:else><view style="width:60%;" class="_div data-v-a187a476"><shmily-drag-image vue-id="384a0be1-1" keyName="image" value="{{selectList}}" data-event-opts="{{[['^changeprice',[['dualPrice']]],['^input',[['__set_model',['','selectList','$event',[]]]]]]}}" bind:changeprice="__e" bind:input="__e" class="data-v-a187a476" bind:__l="__l"></shmily-drag-image></view></block><view style="width:20%;font-size:22rpx;" class="_div data-v-a187a476"><view class="_div data-v-a187a476">总价</view><view class="_div data-v-a187a476">￥<label style="color:#c9ab79;font-weight:600;" class="_span data-v-a187a476">{{price}}</label>元</view></view><view style="width:20%;" class="_div data-v-a187a476"><view data-event-opts="{{[['tap',[['toBuy',['$event']]]]]}}" class="botton_2 _div data-v-a187a476" bindtap="__e">去结算</view></view></view></view></view><view class="content data-v-a187a476"><view style="padding:10rpx;" class="_div data-v-a187a476"><image style="width:100%;" src="https://mpjoy.oss-cn-beijing.aliyuncs.com/crmebimage/public/maintain/2024/11/22/dde1678a2a4f48f4ad694a26563b7eb2or11mp8qem.jpg" mode="widthFix" class="data-v-a187a476"></image></view></view><ljs-dialog bind:input="__e" class="comTc data-v-a187a476" vue-id="384a0be1-2" title="编辑我的手围" value="{{handVisible}}" data-event-opts="{{[['^input',[['__set_model',['','handVisible','$event',[]]]]]]}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="comForm data-v-a187a476"><view class="form-group _div data-v-a187a476"><input type="text" placeholder="请输入手围" data-event-opts="{{[['input',[['__set_model',['','hand','$event',[]]]]]]}}" value="{{hand}}" bindinput="__e" class="data-v-a187a476"/><label class="unit _span data-v-a187a476">mm</label></view></view><view class="operate data-v-a187a476" style="margin-top:0;"><view data-event-opts="{{[['tap',[['fromSubmit',['$event']]]]]}}" class="but data-v-a187a476" bindtap="__e">确定</view><view data-event-opts="{{[['tap',[['e4',['$event']]]]]}}" class="but grey data-v-a187a476" bindtap="__e">取消</view></view></ljs-dialog><view data-event-opts="{{[['tap',[['goMyBracelets',['$event']]]]]}}" class="lianying data-v-a187a476" bindtap="__e"><view class="data-v-a187a476">我的</view><view class="data-v-a187a476">手串</view></view></view>