@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.bigtitle.data-v-098d4cba {
  height: 80rpx;
  line-height: 80rpx;
  min-width: 120rpx;
  font-size: 36rpx;
  font-weight: 600;
  text-align: center;
  color: #c9ab79;
  border-right: #c9ab79 1rpx dashed;
  position: -webkit-sticky;
  position: sticky;
  /* 吸顶效果 */
  left: 0;
  /* 距离顶部的距离 */
  z-index: 99;
  /* 确保在其他元素之上 */
  background-color: white;
}
.smalltitle.data-v-098d4cba {
  min-width: 110rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 32rpx;
  font-weight: 500;
  position: -webkit-sticky;
  position: sticky;
  /* 吸顶效果 */
  left: 0;
  /* 距离顶部的距离 */
  z-index: 10;
  /* 确保在其他元素之上 */
  transition: all 0.3s ease;
  cursor: pointer;
}
.smalltitle.active.data-v-098d4cba {
  color: #c9ab79;
  font-weight: 600;
  position: relative;
}
.smalltitle.active.data-v-098d4cba::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  width: 40rpx;
  height: 5rpx;
  background-color: #c9ab79;
  border-radius: 4rpx;
}
.circle-container.data-v-098d4cba {
  position: relative;
  border-radius: 50%;
  border: gray 1px solid;
}
.marble.data-v-098d4cba {
  position: absolute;
}
.content.data-v-098d4cba {
  width: 710rpx;
  margin-left: 20rpx;
  margin-top: 20rpx;
  margin-bottom: 20rpx;
  background-color: white;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}
.top.data-v-098d4cba {
  width: 710rpx;
  margin-left: 20rpx;
  margin-top: 20rpx;
}
.top .left-bbb.data-v-098d4cba {
  background-color: white;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}
.top .left-bbb .left.data-v-098d4cba {
  height: 80rpx;
  width: 710rpx;
  display: flex;
}
.top .left-bbb .left-child.data-v-098d4cba {
  height: 80rpx;
  display: flex;
}
.top .left-bbb .left-bottom.data-v-098d4cba {
  text-align: center;
  color: #DD5C5F;
  font-size: 26rpx;
  font-weight: 500;
  padding: 20rpx 0;
  background: #fff5f5;
  border-radius: 8rpx;
  margin-top: 20rpx;
  border-top-left-radius: 16rpx;
  border-top-right-radius: 16rpx;
}
.top .right.data-v-098d4cba {
  margin-top: 20rpx;
  background-color: white;
  border-radius: 16rpx;
  text-align: center;
  height: 900rpx;
  position: relative;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}
.botton.data-v-098d4cba {
  background-color: #c9ab79;
  color: #fff;
  font-size: 22rpx;
  height: 65rpx;
  border-radius: 50rpx;
  text-align: center;
  line-height: 65rpx;
}
.botton_1.data-v-098d4cba {
  background-color: #c9ab79;
  padding: 0 20rpx;
  color: #fff;
  font-size: 22rpx;
  height: 65rpx;
  border-radius: 50rpx;
  text-align: center;
  line-height: 65rpx;
}
.botton_2.data-v-098d4cba {
  background-color: #DD5C5F;
  padding: 0 20rpx;
  color: #fff;
  font-size: 22rpx;
  height: 65rpx;
  border-radius: 50rpx;
  text-align: center;
  line-height: 65rpx;
}
.botton_3.data-v-098d4cba {
  background-color: #398ade;
  padding: 0 20rpx;
  color: #fff;
  font-size: 22rpx;
  height: 65rpx;
  border-radius: 50rpx;
  text-align: center;
  line-height: 65rpx;
}
.comTc .comForm.data-v-098d4cba {
  width: 100%;
  height: calc(100% - 100rpx);
  overflow-y: auto;
}
.comTc .operate.data-v-098d4cba {
  height: 100rpx;
  padding: 0 30rpx;
  margin-top: 20rpx;
  display: flex;
  align-items: center;
}
.comTc .operate .but.data-v-098d4cba {
  flex: 1;
  height: 56rpx;
  line-height: 56rpx;
  text-align: center;
  border-radius: 10rpx;
  border: 2rpx solid #084AA1;
  background-color: #084AA1;
  color: #FFF;
  margin-right: 20rpx;
}
.comTc .operate .but.data-v-098d4cba:last-child {
  margin-right: 0;
}
.comTc .operate .but.grey.data-v-098d4cba {
  border: 2rpx solid #eee;
  background-color: #FFF;
  color: #666;
}
.form-group.data-v-098d4cba {
  position: relative;
  margin-bottom: 20px;
}
.form-group input.data-v-098d4cba {
  padding: 10px;
  margin-bottom: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
}
.unit.data-v-098d4cba {
  position: absolute;
  right: 10px;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  color: red;
  font-weight: 600;
}
.hand-text-1.data-v-098d4cba {
  font-size: 22rpx;
  font-weight: 600;
  color: #c9ab79;
  margin: 10rpx 0;
}
.hand-text-2.data-v-098d4cba {
  font-size: 24rpx;
  font-weight: 600;
  color: #888888;
  margin: 10rpx 0;
}
.lianying.data-v-098d4cba {
  position: fixed;
  bottom: 100rpx;
  right: 0;
  width: 100rpx;
  height: 100rpx;
  background-color: #c9ab79;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-direction: column;
  font-size: 28rpx;
  font-weight: 600;
  line-height: 36rpx;
}
.movable-area.data-v-098d4cba {
  pointer-events: none;
}
.movable-view.data-v-098d4cba {
  pointer-events: auto;
  position: fixed;
}
.product-item.data-v-098d4cba {
  position: relative;
  background: white;
  color: black;
  font-weight: 400;
  font-size: 28rpx;
}
.dropdown-menu.data-v-098d4cba {
  position: fixed;
  top: 120rpx;
  left: 0;
  width: 100rpx;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 11;
  border-radius: 0 0 8rpx 8rpx;
}
.dropdown-item.data-v-098d4cba {
  padding: 20rpx 0;
  text-align: center;
  font-size: 28rpx;
  border-bottom: 1px solid #eee;
}
.dropdown-item.data-v-098d4cba:last-child {
  border-bottom: none;
}
.dropdown-item.data-v-098d4cba:active {
  background-color: #f5f5f5;
}
.specifications-dropdown.data-v-098d4cba {
  position: fixed;
  top: 150rpx;
  left: 30rpx;
  width: 200rpx;
  background: #fff;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  z-index: 999;
  border-radius: 12rpx;
  overflow: hidden;
}
.specs-header.data-v-098d4cba {
  padding: 20rpx 24rpx;
  border-bottom: 1rpx solid #f5f5f5;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fafafa;
}
.specs-title.data-v-098d4cba {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  height: 40rpx;
  line-height: 40rpx;
}
.close-btn.data-v-098d4cba {
  font-size: 32rpx;
  color: #999;
  padding: 0 10rpx;
  line-height: 1;
}
.specs-list.data-v-098d4cba {
  max-height: 400rpx;
  overflow-y: auto;
}
.specs-item.data-v-098d4cba {
  padding: 20rpx 24rpx;
  border-bottom: 1rpx solid #f8f8f8;
  transition: background-color 0.2s;
}
.specs-item.data-v-098d4cba:active {
  background-color: #f9f9f9;
}
.specs-item.data-v-098d4cba:last-child {
  border-bottom: none;
}
.specs-info.data-v-098d4cba {
  display: flex;
  justify-content: center;
  align-items: center;
}
.specs-size.data-v-098d4cba {
  font-size: 24rpx;
  color: #c9ab79;
  font-weight: 500;
  height: 40rpx;
  line-height: 40rpx;
}
.specs-price.data-v-098d4cba {
  font-size: 22rpx;
  color: #c9ab79;
  font-weight: 500;
}
.specs-price.data-v-098d4cba::before {
  content: '￥';
  font-size: 20rpx;
  margin-right: 2rpx;
}
.product-spec-item.data-v-098d4cba {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  border-radius: 8rpx;
  padding: 4rpx;
}
.image-wrapper.data-v-098d4cba {
  position: relative;
}
.product-image.data-v-098d4cba {
  border-radius: 6rpx;
  object-fit: cover;
}
.delete-icon.data-v-098d4cba {
  height: 30rpx;
  position: absolute;
  right: -6rpx;
  top: -6rpx;
  background: #fff;
  border-radius: 50%;
  padding: 2rpx;
}
.price-tag.data-v-098d4cba {
  margin-top: 6rpx;
  font-size: 24rpx;
  color: #333;
  display: flex;
  align-items: baseline;
}
.price-symbol.data-v-098d4cba {
  font-size: 20rpx;
  color: #ff4d4f;
  margin-right: 2rpx;
}
.price-value.data-v-098d4cba {
  color: #ff4d4f;
  font-weight: 500;
}
.hand-text-text.data-v-098d4cba {
  font-weight: 500;
  font-size: 26rpx;
  color: #c9ab79;
}

