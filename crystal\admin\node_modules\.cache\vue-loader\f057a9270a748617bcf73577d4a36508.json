{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\mailun\\question-user-record-detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\mailun\\question-user-record-detail.vue", "mtime": 1753666157878}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport { questionuserDetailApi } from '@/api/questionuser'\r\nimport * as XLSX from 'xlsx'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      visible: false,\r\n      loading: false,\r\n      userInfo: {},\r\n      questionOptions: [],\r\n      chakraData: [\r\n        { name: \"海底轮\", enName: \"Root Chakra\", value: 0, color: \"#993734\" },\r\n        { name: \"脐轮\", enName: \"Sacral Chakra\", value: 0, color: \"#be6f2a\" },\r\n        { name: \"太阳轮\", enName: \"Solar Plexus\", value: 0, color: \"#d7c34a\" },\r\n        { name: \"心轮\", enName: \"Heart Chakra\", value: 0, color: \"#5f9057\" },\r\n        { name: \"喉轮\", enName: \"Throat Chakra\", value: 0, color: \"#5b8aa4\" },\r\n        { name: \"眉心轮\", enName: \"Third Eye\", value: 0, color: \"#2c3485\" },\r\n        { name: \"顶轮\", enName: \"Crown Chakra\", value: 0, color: \"#7e4997\" },\r\n      ]\r\n    }\r\n  },\r\n  methods: {\r\n    init(id) {\r\n      this.visible = true\r\n      this.loading = true\r\n      this.userInfo = {}\r\n      this.questionOptions = []\r\n      \r\n      questionuserDetailApi(id).then(res => {\r\n        console.log(res)\r\n        this.userInfo = res || {}\r\n        this.questionOptions = res.questionUserOptionEntities || []\r\n        \r\n        // 更新脉轮数据\r\n        if (this.userInfo.status == 1) {\r\n          this.chakraData[0].value = this.userInfo.root || 0\r\n          this.chakraData[1].value = this.userInfo.sacral || 0\r\n          this.chakraData[2].value = this.userInfo.navel || 0\r\n          this.chakraData[3].value = this.userInfo.heart || 0\r\n          this.chakraData[4].value = this.userInfo.throat || 0\r\n          this.chakraData[5].value = this.userInfo.thirdEye || 0\r\n          this.chakraData[6].value = this.userInfo.crown || 0\r\n        }\r\n        \r\n        this.loading = false\r\n      }).catch((e) => {\r\n        this.loading = false\r\n        console.log(e)\r\n        this.$message.error('获取详情失败')\r\n      })\r\n    },\r\n\r\n    // 计算进度条百分比（假设最大值为100）\r\n    getProgressPercentage(value) {\r\n      const maxValue = 100\r\n      return Math.min((value / maxValue) * 100, 100)\r\n    },\r\n\r\n    // 复制脉轮数据\r\n    copyChakraData() {\r\n      const text = this.chakraData.map(chakra => `${chakra.name}: ${chakra.value || '0'}`).join('\\n');\r\n\r\n      // 创建临时文本区域来复制文本\r\n      const textArea = document.createElement('textarea');\r\n      textArea.value = text;\r\n      document.body.appendChild(textArea);\r\n      textArea.select();\r\n\r\n      try {\r\n        document.execCommand('copy');\r\n        this.$message.success('脉轮数据已复制到剪贴板');\r\n      } catch (err) {\r\n        this.$message.error('复制失败，请手动复制');\r\n      }\r\n\r\n      document.body.removeChild(textArea);\r\n    },\r\n\r\n    // 导出详情\r\n    exportDetail() {\r\n      if (!this.userInfo.id) {\r\n        this.$message.error('没有可导出的数据');\r\n        return;\r\n      }\r\n\r\n      // 准备用户基本信息\r\n      const userBasicInfo = {\r\n        '用户名': this.userInfo.username || '-',\r\n        '手机号': this.userInfo.mobile || '-',\r\n        '测试状态': this.userInfo.status == 1 ? '已提交' : '未提交',\r\n        '总分': this.userInfo.points || 0,\r\n        '创建时间': this.userInfo.addTime || '-',\r\n        '更新时间': this.userInfo.updateTime || '-'\r\n      };\r\n\r\n      // 准备脉轮数据\r\n      const chakraInfo = {};\r\n      this.chakraData.forEach(chakra => {\r\n        chakraInfo[chakra.name] = chakra.value || '0';\r\n      });\r\n\r\n      // 准备答题详情\r\n      const questionDetails = this.questionOptions.map((item, index) => ({\r\n        '题目序号': index + 1,\r\n        '题目内容': item.questionName || '题目' + (index + 1),\r\n        '选择答案': item.optionName || '-'\r\n      }));\r\n\r\n      // 创建工作簿\r\n      const wb = XLSX.utils.book_new();\r\n\r\n      // 添加用户基本信息工作表\r\n      const userWs = XLSX.utils.json_to_sheet([userBasicInfo]);\r\n      XLSX.utils.book_append_sheet(wb, userWs, '用户信息');\r\n\r\n      // 添加脉轮数据工作表\r\n      const chakraWs = XLSX.utils.json_to_sheet([chakraInfo]);\r\n      XLSX.utils.book_append_sheet(wb, chakraWs, '脉轮数据');\r\n\r\n      // 添加答题详情工作表\r\n      if (questionDetails.length > 0) {\r\n        const questionWs = XLSX.utils.json_to_sheet(questionDetails);\r\n        XLSX.utils.book_append_sheet(wb, questionWs, '答题详情');\r\n      }\r\n\r\n      // 下载文件\r\n      const fileName = `脉轮测试详情_${this.userInfo.username || this.userInfo.id}_${new Date().toISOString().slice(0, 10)}.xlsx`;\r\n      XLSX.writeFile(wb, fileName);\r\n\r\n      this.$message.success('导出成功');\r\n    }\r\n  }\r\n}\r\n", null]}