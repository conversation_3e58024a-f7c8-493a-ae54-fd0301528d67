<template>
  <view class="mailun-test-container">
    <div class="nav-title">
      <div class="color"></div>
      <div class="text">开始您的脉轮测试</div>
    </div>
    <div class="list">
      <div class="box" v-for="(item, index) in questionEntities" :key="item.id" :id="'question-' + item.id" :class="{'box-error': item.hasError}">
        <div class="title" :style="'color: ' + color[item.mailun]" v-html="item.name"></div>
        <div class="radio-select-vertical" :style="'color: ' + color[item.mailun]">
          <div class="radio-group-vertical" v-if="item.type == 0">
            <div
              class="radio-option"
              v-for="(radio,index2) in item.questionOptionEntities"
              :key="index2"
              :class="{'option-selected': questionEntities[index].selectId === radio.id}"
              @click="choose(radio.id, index)"
            >
              <div class="radio-indicator" :class="{'radio-selected': questionEntities[index].selectId === radio.id}"></div>
              <div class="option-text">{{radio.name}}</div>
            </div>
          </div>
        </div>
        <div class="error-tip" v-if="item.hasError">请选择一个选项</div>
      </div>
      <div class="bottom">
        <view class="botton_1" hover-class="button-hover" @click="saveExam">
          暂存
        </view>
        <view class="botton_2" hover-class="button-hover" @click="submitExam">
          提交
        </view>

      </div>
    </div>
  </view>
</template>

<script>
import {
  questionDetail,
  questionSubmitExam,
  questionSaveExam,
} from '@/api/question.js';
export default {
  components: {
  },
  data() {
    return {
      // color: ["#FF0000","#FFA500","#dcc500" , "#008000" ,"#00a8a8" , "#0000FF" ,"#800080"],
      color: ["#000000","#000000","#000000" , "#000000" ,"#000000" , "#000000" ,"#000000"],
      token: '',
      questionUserId: '',
      questionEntities: [],
      questionUserEntity: {},
    };
  },
  /**
    * 生命周期函数--监听页面加载
    */
  onLoad: function (options) {
    this.token = options.token;
    this.questionUserId = options.questionUserId;
  },
  onShow: function () {
    var that = this;
    questionDetail({ questionUserId: that.questionUserId }).then(res => {
      that.$set(that, "questionEntities", res.data.questionEntities);
      that.$set(that, "questionUserEntity", res.data.questionUserEntity);

    }).catch(res=> {
      if(res == '您已经参与过答题') {

        uni.showModal({
          title: '提示',
          content: '您已经参与过答题',
          showCancel: false,
          success: function (res) {
            if (res.confirm) {
              wx.redirectTo({ url: '/pages/mailun/child/detail?questionUserId=' + that.questionUserId })
            }
          }
        });
      } else {

        that.$util.Tips({
          title: res
        });
      }
    })
  },
  /**
   * 用户点击右上角分享
   */
  // #ifdef MP
  onShareAppMessage: function () {
    // return {
    // 	title: this.articleInfo.title,
    // 	imageUrl: this.articleInfo.imageInput.length ? this.articleInfo.imageInput[0] : "",
    // 	desc: this.articleInfo.synopsis,
    // 	path: '/pages/news_details/index?id=' + this.id
    // };
  },
  // #endif
  methods: {

    // 重置错误提示
    resetErrors() {
      this.questionEntities.forEach(item => {
        this.$set(item, 'hasError', false);
      });
    },

    // 验证表单
    validateForm() {
      this.resetErrors();
      let isValid = true;
      let firstErrorId = null;
      
      for (let i = 0; i < this.questionEntities.length; i++) {
        const question = this.questionEntities[i];
        if (!question.selectId) {
          this.$set(question, 'hasError', true);
          if (!firstErrorId) {
            firstErrorId = question.id;
          }
          isValid = false;
        }
      }
      
      if (!isValid && firstErrorId) {
        // 使用uniapp API实现滚动定位
        setTimeout(() => {
          // 创建选择器查询对象
          const query = uni.createSelectorQuery();
          // 选择id为question-x的节点
          query.select('#question-' + firstErrorId).boundingClientRect();
          query.selectViewport().scrollOffset();
          // 执行查询
          query.exec((res) => {
            if (res && res[0]) {
              // 计算需要滚动的位置
              const top = res[0].top + res[1].scrollTop - 20; // 减去一些偏移量，使其更居中
              // 使用pageScrollTo方法滚动到指定位置
              uni.pageScrollTo({
                scrollTop: top,
                duration: 300
              });
            }
          });
        }, 100);
      }
      
      return isValid;
    },

    // 单选选择
    choose(v, index) {
      this.questionEntities[index].selectId = v;
      if (this.questionEntities[index].hasError) {
        this.$set(this.questionEntities[index], 'hasError', false);
      }
    },
    // 多选选择
    toggle(event) {
      const index = event.currentTarget.dataset.index;
      this.$refs.checkboxes.filter((item) => item.name == index)[0].toggle();
    },

    // 提交试卷
    submitExam() {
      var that = this;
      
      // 验证表单
      if (!this.validateForm()) {
        uni.showToast({
          title: '请完成所有问题',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      
      that.questionEntities.forEach((e) => {
        if (e.type == 1) {
          e.selectId = e.selectId ? e.selectId.toString() : '';
        }
      });
      uni.showLoading({
        title:'提交中',
        mask:true,
      })
      questionSubmitExam({
        token: that.token,
        questionUserId: that.questionUserId,
        questionEntities: that.questionEntities,
      }).then(res => {
        uni.hideLoading();
        wx.redirectTo({ url: '/pages/mailun/child/detail?questionUserId=' + that.questionUserId })
      }).catch(res => {
        uni.hideLoading();
        this.$util.Tips({
          title: res
        });
      })
    },
    // 提交试卷
    saveExam() {
      var that = this;
      // 验证表单
      if (!this.validateForm()) {
        uni.showToast({
          title: '请完成所有问题',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      that.questionEntities.forEach((e) => {
        if (e.type == 1) {
          e.selectId = e.selectId ? e.selectId.toString() : '';
        }
      });
      uni.showLoading({
        title:'提交中',
        mask:true,
      })
      questionSaveExam({
        token: that.token,
        questionUserId: that.questionUserId,
        questionEntities: that.questionEntities,
      }).then(res => {
        uni.hideLoading();
        uni.showModal({
          title: '提示',
          content: '暂存成功',
          showCancel: false,
          success: function (res) {
            if (res.confirm) {
              wxuni.navigateBack({
                delta: 1
              });
            }
          }
        });
      }).catch(res => {
        uni.hideLoading();
        that.$util.Tips({
          title: res
        });

      })
    },
  }
}
</script>

<style lang="scss" scoped>
.mailun-test-container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 40rpx;
}

.radio-select-vertical {
  padding: 20rpx 30rpx;
  font-weight: 500;
  font-size: 28rpx;
}

.radio-group-vertical {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.radio-option {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #fafafa;
  border-radius: 12rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  position: relative;
  cursor: pointer;
}

.radio-option:hover {
  background-color: #f0f0f0;
}

.option-selected {
  background-color: #fff8f0;
  border-color: #c9ab79;
  box-shadow: 0 2rpx 8rpx rgba(201, 171, 121, 0.2);
}

.radio-indicator {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 3rpx solid #c9ab79;
  background-color: white;
  position: relative;
  transition: all 0.3s ease;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.radio-selected {
  background-color: #c9ab79;
  border-color: #c9ab79;
  box-shadow: 0 0 8rpx rgba(201, 171, 121, 0.4);
}

.radio-selected:after {
  content: '';
  position: absolute;
  width: 16rpx;
  height: 8rpx;
  border-left: 3rpx solid white;
  border-bottom: 3rpx solid white;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -70%) rotate(-45deg);
}

.option-text {
  flex: 1;
  font-size: 30rpx;
  line-height: 1.4;
  color: #333;
  font-weight: 500;
}



.title {
  padding: 30rpx 30rpx;
  font-weight: 600;
  font-size: 32rpx;
  letter-spacing: 1rpx;
  border-bottom: 1rpx solid rgba(0,0,0,0.05);
}

.nav-title {
  padding: 20rpx 40rpx;
  display: flex;
  align-items: center;
  height: 80rpx;
  line-height: 80rpx;

  .color {
    width: 10rpx;
    height: 40rpx;
    background: #c9ab79;
    border-radius: 6rpx;
    margin-right: 15rpx;
  }
  
  .text {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
  }
}

.botton_1 {
  width: 45%;
  background-color: #c9ab79;
  color: #fff;
  font-size: 32rpx;
  font-weight: 600;
  height: 90rpx;
  border-radius: 50rpx;
  text-align: center;
  line-height: 90rpx;
  box-shadow: 0 4rpx 12rpx rgba(201, 171, 121, 0.3);
  transition: all 0.2s;
}

.botton_2 {
  width: 45%;
  background-color: #DD5C5F;
  color: #fff;
  font-size: 32rpx;
  font-weight: 600;
  height: 90rpx;
  border-radius: 50rpx;
  text-align: center;
  line-height: 90rpx;
  box-shadow: 0 4rpx 12rpx rgba(221, 92, 95, 0.3);
  transition: all 0.2s;
}

.button-hover {
  transform: translateY(3rpx);
  box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.2);
}

.box {
  width: 92%;
  margin-left: 4%;
  background: white;
  margin-bottom: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.bottom {
  display: flex;
  padding: 40rpx 20rpx;
  justify-content: space-around;
}

.box-error {
  border: 2rpx solid #ff6b6b;
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  20%, 60% { transform: translateX(-5rpx); }
  40%, 80% { transform: translateX(5rpx); }
}

.error-tip {
  color: #ff6b6b;
  font-size: 24rpx;
  padding: 0 30rpx 20rpx;
  text-align: center;
}
</style>
