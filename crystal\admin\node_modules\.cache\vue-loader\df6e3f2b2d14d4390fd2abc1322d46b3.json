{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\grade\\creatGrade.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\grade\\creatGrade.vue", "mtime": 1753666157938}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { levelSaveApi, levelInfoApi, levelUpdateApi } from '@/api/user'\nimport {Debounce} from '@/utils/validate'\nconst obj = {\n  name:'',\n  grade: 1,\n  discount: '',\n  icon: '',\n  image: '',\n  id: null\n}\nexport default {\n  name: \"CreatGrade\",\n  props:{\n    'user':Object\n  },\n  data() {\n    return {\n      dialogVisible: false,\n      formValidate: Object.assign({},obj),\n      loading: false,\n      rules:{\n        name: [\n          {  required: true, message: '请输入等级名称', trigger: 'blur' }\n        ],\n        grade: [\n          {  required: true, message: '请输入等级', trigger: 'blur' },\n          { type: 'number', message: '等级必须为数字值'}\n        ],\n        discount: [\n          {  required: true, message: '请输入折扣', trigger: 'blur'},\n        ],\n        experience: [\n          {  required: true, message: '请输入经验', trigger: 'blur'},\n          { type: 'number', message: '经验必须为数字值'}\n        ],\n        icon: [\n          {  required: true, message: '请上传图标', trigger: 'change' }\n        ],\n        image: [\n          {  required: true, message: '请上传用户背景', trigger: 'change' }\n        ],\n      }\n    }\n  },\n  methods:{\n    // 点击商品图\n    modalPicTap (tit, num) {\n       const _this = this\n      this.$modalUpload(function(img) {\n        tit==='1'&& num === 'icon' ? _this.formValidate.icon = img[0].sattDir : _this.formValidate.image = img[0].sattDir\n        this.$set(_this.user,'icon', _this.formValidate.icon);\n        this.$set(_this.user,'isShow', false);\n      },tit , 'user')\n    },\n    info(id) {\n      this.loading = true\n      levelInfoApi({id: id}).then(res => {\n        this.formValidate = res\n        this.loading = false\n      }).catch(() => {\n        this.loading = false\n      })\n    },\n    handleClose() {\n       this.$nextTick(() => {\n        this.$refs.user.resetFields();\n      })\n      this.dialogVisible = false;\n      // this.user = Object.assign({}, '')\n    },\n    submitForm:Debounce(function(formName) {\n      this.$refs.user.validate((valid) => {\n        if (valid) {\n          this.loading = true\n          let data = {\n            discount:this.user.discount,\n            experience:this.user.experience,\n            grade:this.user.grade,\n            icon:this.user.icon,\n            id:this.user.id,\n            isShow:this.user.isShow,\n            name:this.user.name\n          };\n          this.user.id ? levelUpdateApi(this.user.id, data).then(res => {\n            this.$message.success('编辑成功')\n            this.loading = false\n            this.handleClose()\n            this.formValidate = Object.assign({},obj)\n            this.$parent.getList()\n          }).catch(() => {\n            this.loading = false\n          }): levelSaveApi(this.user).then(res => {\n            this.$message.success('添加成功')\n            this.loading = false\n            this.handleClose()\n            this.formValidate = Object.assign({},obj)\n            this.$parent.getList()\n          }).catch(() => {\n            this.loading = false\n            this.formValidate = Object.assign({},obj)\n          })\n        } else {\n          return false;\n        }\n      });\n    }),\n    resetForm(formName) {\n      \n      // this[formName] = {};\n       this.$nextTick(() => {\n        this.$refs.user.resetFields();\n      })\n      this.dialogVisible = false;\n    }\n  }\n}\n", null]}