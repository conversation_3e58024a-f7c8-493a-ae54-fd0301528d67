{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\group\\index.vue?vue&type=template&id=fdc6b3d0&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\group\\index.vue", "mtime": 1753666157940}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\n<div class=\"divBox\">\n  <el-card class=\"box-card\">\n    <div slot=\"header\" class=\"clearfix\">\n      <el-button size=\"small\" type=\"primary\" @click=\"onAdd(null)\" v-hasPermi=\"['admin:user:group:save','admin:user:tag:save']\">{{$route.path.indexOf('group') !== -1?'添加用户分组':'添加用户标签'}}</el-button>\n    </div>\n    <el-table\n      v-loading=\"listLoading\"\n      :data=\"tableData.data\"\n      style=\"width: 100%\"\n      size=\"small\"\n    >\n      <el-table-column\n        label=\"ID\"\n        min-width=\"80\"\n        prop=\"id\"\n      />\n      <el-table-column\n        :label=\"$route.path.indexOf('group') !== -1 ? '分组名称' : '标签名称'\"\n        min-width=\"180\"\n      >\n        <template slot-scope=\"{row}\">\n          <span v-text=\"$route.path.indexOf('group') !== -1?row.groupName:row.name\"></span>\n        </template>\n      </el-table-column>\n      <!--<el-table-column-->\n        <!--prop=\"create_time\"-->\n        <!--label=\"创建时间\"-->\n        <!--min-width=\"150\"-->\n      <!--/>-->\n      <el-table-column label=\"操作\" min-width=\"120\" fixed=\"right\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <el-button class=\"mr10\" type=\"text\" size=\"small\" @click=\"onAdd(scope.row)\" v-hasPermi=\"['admin:user:group:update','admin:user:tag:update']\">编辑</el-button>\n          <el-button type=\"text\" size=\"small\" @click=\"handleDelete(scope.row.id, scope.$index)\" disable v-hasPermi=\"['admin:user:group:delete','admin:user:tag:delete']\">删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <div class=\"block\">\n      <el-pagination\n        :page-sizes=\"[20, 40, 60, 80]\"\n        :page-size=\"tableFrom.limit\"\n        :current-page=\"tableFrom.page\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        :total=\"tableData.total\"\n        @size-change=\"handleSizeChange\"\n        @current-change=\"pageChange\"\n      />\n    </div>\n  </el-card>\n</div>\n", null]}