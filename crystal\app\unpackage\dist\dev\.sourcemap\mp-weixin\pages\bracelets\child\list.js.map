{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/bracelets/child/list.vue?af05", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/bracelets/child/list.vue?cdd1", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/bracelets/child/list.vue?135a", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/bracelets/child/list.vue?e63a", "uni-app:///pages/bracelets/child/list.vue", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/bracelets/child/list.vue?87dc", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/bracelets/child/list.vue?c87f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "hand", "changeFlag", "handVisible", "radius", "productList", "price", "computed", "onLoad", "onShow", "onShareAppMessage", "title", "path", "methods", "turnDetail", "uni", "url", "_previewImage", "imgArr", "urls", "current", "getMarbleStyle", "position", "left", "top", "width", "height", "transform", "background", "backgroundSize", "getAllCategory", "userId", "type", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACwD;AACL;AACsC;;;AAGzF;AACmM;AACnM,gBAAgB,2LAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AClCA;AAAA;AAAA;AAAA;AAAiwB,CAAgB,orBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACgCrxB;AAGA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;IACA;EACA;EACAC;EACAC;IACA;EAAA,CACA;EACAC;IACA;IACA;EACA;EACA;AACA;AACA;;EAEAC;IACA;IACA;MACAC;MACA;MACAC;IACA;EACA;EAEAC;IACAC;MACAC;QACAC;MACA;IACA;IACAC;MACA;MACAC;MACA;MACAH;QACAI;QACAC;MACA;IAEA;IACAC;MAEA;MACA;MACA;MACA;MACA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;IACAC;MAAA;MACA;QAAAC;QAAAC;MAAA;QACA;MACA;IACA;IACAC;MAEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChHA;AAAA;AAAA;AAAA;AAAo8C,CAAgB,mvCAAG,EAAC,C;;;;;;;;;;;ACAx9C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/bracelets/child/list.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/bracelets/child/list.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./list.vue?vue&type=template&id=0b75b564&scoped=true&\"\nvar renderjs\nimport script from \"./list.vue?vue&type=script&lang=js&\"\nexport * from \"./list.vue?vue&type=script&lang=js&\"\nimport style0 from \"./list.vue?vue&type=style&index=0&id=0b75b564&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0b75b564\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/bracelets/child/list.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=template&id=0b75b564&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l1 = _vm.__map(_vm.productList, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var l0 = _vm.__map(\n      item.userBraceletsItemEntities,\n      function (item1, index1) {\n        var $orig = _vm.__get_orig(item1)\n        var s0 = _vm.__get_style([_vm.getMarbleStyle(index, index1)])\n        return {\n          $orig: $orig,\n          s0: s0,\n        }\n      }\n    )\n    return {\n      $orig: $orig,\n      l0: l0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class='productSort'>\r\n\t\t<view class=\"top\" v-for=\"(item, index) in productList\" :key=\"index\">\r\n\t\t\t<div style=\"height: 100rpx;display: flex;align-items: center;justify-content: space-between;padding: 0 20rpx;\">\r\n\t\t\t\t<view>\r\n\t\t\t\t\t<text>名称：{{ item.name }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view style=\"display: flex;gap: 10rpx;\">\r\n\t\t\t\t\t<view @click=\"turnDetail(item.id)\"  class=\"botton_1\">\r\n\t\t\t\t\t\t编辑\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<button class=\"botton_2\" open-type=\"share\" hover-class='none' :data-id=\"item.id\">\r\n\t\t\t\t\t\t\t<view class=\"\">分享手串</view>\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t  \r\n\t\t\t\t</view>\r\n\t\t\t\t  \r\n\t\t\t</div>\r\n\t\t\t<div\r\n\t\t\t\t:style=\"'display: flex;justify-content: center;align-items: center;height: ' + (item.radius * 3) + 'rpx;border-top: #c9ab79 1px dashed ;position: relative;'\">\r\n\t\t\t\t<div class=\"circle-container\"\r\n\t\t\t\t\t:style=\"{ width: item.radius * 2 + 'rpx', height: item.radius * 2 + 'rpx' }\">\r\n\t\t\t\t\t<div v-for=\"(item1, index1) in item.userBraceletsItemEntities\" :key=\"index1\" class=\"marble\"\r\n\t\t\t\t\t\t:style=\"[getMarbleStyle(index, index1)]\">\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n\tuserEditHand, userBraceletsFindByUserId\r\n} from '@/api/user.js';\r\nimport { mapGetters } from \"vuex\";\r\nimport { toLogin } from '@/libs/login.js';\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\thand: 0,\r\n\t\t\tchangeFlag: false,\r\n\t\t\thandVisible: false,\r\n\t\t\tradius: 100, // 圆弧的半径\r\n\t\t\tproductList: [],\r\n\t\t\tprice: 0\r\n\t\t}\r\n\t},\r\n\tcomputed: mapGetters(['isLogin', 'chatUrl', 'userInfo', 'uid']),\r\n\tonLoad(options) {\r\n\t\t// this.generateMarbles();\r\n\t},\r\n\tonShow() {\r\n\t\tthis.hand = this.userInfo.hand;\r\n\t\tthis.getAllCategory();\r\n\t},\r\n\t/**\r\n\t * 用户点击右上角分享\r\n\t */\r\n\t// #ifdef MP\r\n\tonShareAppMessage: function (e) {\r\n\t\tlet that = this;\r\n\t\treturn {\r\n\t\t\ttitle: '快来看看我自定义的手串吧~',\r\n\t\t\t// imageUrl:  '',\r\n\t\t\tpath: '/pages/bracelets/child/edit?id=' + e.target.dataset.id + '&spread=' + that.uid,\r\n\t\t}\r\n\t},\r\n\t// #endif\r\n\tmethods: {\r\n\t\tturnDetail(e) {\r\n\t\t\tuni.navigateTo({\r\n\t\t\t\turl: '/pages/bracelets/child/edit?id=' + e\r\n\t\t\t});\r\n\t\t},\r\n\t\t_previewImage(image) {\r\n\t\t\tvar imgArr = [];\r\n\t\t\timgArr.push(image);\r\n\t\t\t//预览图片\r\n\t\t\tuni.previewImage({\r\n\t\t\t\turls: imgArr,\r\n\t\t\t\tcurrent: imgArr[0]\r\n\t\t\t});\r\n\r\n\t\t},\r\n\t\tgetMarbleStyle(index, index1) {\r\n\r\n\t\t\tconst angle = (index1 * 2 * Math.PI) / this.productList[index].userBraceletsItemEntities.length;\r\n\t\t\tconst x = parseFloat(this.productList[index].radius) + (parseFloat(this.productList[index].radius) * Math.cos(angle));\r\n\t\t\tconst y = parseFloat(this.productList[index].radius) + (parseFloat(this.productList[index].radius) * Math.sin(angle));\r\n\t\t\tconst rotation = angle + (Math.PI / 2); // 旋转角度，使珠子垂直于原点\r\n\t\t\treturn {\r\n\t\t\t\tposition: 'absolute',\r\n\t\t\t\tleft: (x - (parseFloat(this.productList[index].userBraceletsItemEntities[index1].width) * 3 / 2)) + 'rpx',\r\n\t\t\t\ttop: (y - (parseFloat(this.productList[index].userBraceletsItemEntities[index1].height) * 3 / 2)) + 'rpx',\r\n\t\t\t\twidth: (parseFloat(this.productList[index].userBraceletsItemEntities[index1].width) * 3) + 'rpx',\r\n\t\t\t\theight: (parseFloat(this.productList[index].userBraceletsItemEntities[index1].height) * 3) + 'rpx',\r\n\t\t\t\ttransform: `rotate(${rotation}rad)`,\r\n\t\t\t\tbackground: `url('${this.productList[index].userBraceletsItemEntities[index1].image}') no-repeat center`,\r\n\t\t\t\tbackgroundSize: `contain`\r\n\t\t\t};\r\n\t\t},\r\n\t\tgetAllCategory() {\r\n\t\t\tuserBraceletsFindByUserId({ userId: this.uid,type : 0 }).then(res => {\r\n\t\t\t\tthis.productList = res.data;\r\n\t\t\t})\r\n\t\t},\r\n\t\ttoLogin() {\r\n\r\n\t\t\ttoLogin()\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.bigtitle {\r\n\r\n\theight: 80rpx;\r\n\tline-height: 80rpx;\r\n\tbackground-color: #ffe4b7;\r\n\tfont-size: 32rpx;\r\n\tfont-weight: 600;\r\n}\r\n\r\n.circle-container {\r\n\tposition: relative;\r\n\tborder-radius: 50%;\r\n\tborder: gray 1px solid;\r\n\ttransition: all 1s;\r\n}\r\n\r\n.marble {\r\n\tposition: absolute;\r\n\ttransition: all 1s;\r\n}\r\n\r\n.content {\r\n\twidth: 710rpx;\r\n\tmargin-left: 20rpx;\r\n\tmargin-top: 20rpx;\r\n\tmargin-bottom: 20rpx;\r\n\tbackground-color: white;\r\n\tborder-radius: 16rpx;\r\n}\r\n\r\n.top {\r\n\tgap: 10rpx;\r\n\twidth: 710rpx;\r\n\tmargin-left: 20rpx;\r\n\tmargin-top: 20rpx;\r\n\tbackground-color: white;\r\n\tborder-radius: 16rpx;\r\n\r\n}\r\n\r\n.botton {\r\n\tbackground-color: #c9ab79;\r\n\r\n\tcolor: #fff;\r\n\tfont-size: 22rpx;\r\n\theight: 65rpx;\r\n\tborder-radius: 50rpx;\r\n\ttext-align: center;\r\n\tline-height: 65rpx;\r\n}\r\n\r\n.botton_1 {\r\n\tbackground-color: #c9ab79;\r\n\tpadding: 0 20rpx;\r\n\tcolor: #fff;\r\n\tfont-size: 22rpx;\r\n\theight: 65rpx;\r\n\tborder-radius: 50rpx;\r\n\ttext-align: center;\r\n\tline-height: 65rpx;\r\n}\r\n\r\n.botton_2 {\r\n\tbackground-color: #DD5C5F;\r\n\tpadding: 0 20rpx;\r\n\tcolor: #fff;\r\n\tfont-size: 22rpx;\r\n\theight: 65rpx;\r\n\tborder-radius: 50rpx;\r\n\ttext-align: center;\r\n\tline-height: 65rpx;\r\n}\r\n\r\n.botton_3 {\r\n\tbackground-color: #398ade;\r\n\tpadding: 0 20rpx;\r\n\tcolor: #fff;\r\n\tfont-size: 22rpx;\r\n\theight: 65rpx;\r\n\tborder-radius: 50rpx;\r\n\ttext-align: center;\r\n\tline-height: 65rpx;\r\n}\r\n\r\n.comTc {\r\n\t.comForm {\r\n\t\twidth: 100%;\r\n\t\theight: calc(100% - 100upx);\r\n\t\toverflow-y: auto;\r\n\t}\r\n\r\n\t.operate {\r\n\t\theight: 100upx;\r\n\t\tpadding: 0 30upx;\r\n\t\tmargin-top: 20upx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\r\n\t\t.but {\r\n\t\t\tflex: 1;\r\n\t\t\theight: 56upx;\r\n\t\t\tline-height: 56upx;\r\n\t\t\ttext-align: center;\r\n\t\t\tborder-radius: 10upx;\r\n\t\t\tborder: 2upx solid #084AA1;\r\n\t\t\tbackground-color: #084AA1;\r\n\t\t\tcolor: #FFF;\r\n\t\t\tmargin-right: 20upx;\r\n\r\n\t\t\t&:last-child {\r\n\t\t\t\tmargin-right: 0;\r\n\t\t\t}\r\n\r\n\t\t\t&.grey {\r\n\t\t\t\tborder: 2upx solid #eee;\r\n\t\t\t\tbackground-color: #FFF;\r\n\t\t\t\tcolor: #666;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.form-group {\r\n\tposition: relative;\r\n\tmargin-bottom: 20px;\r\n}\r\n\r\n.form-group input {\r\n\tpadding: 10px;\r\n\tmargin-bottom: 10px;\r\n\tborder: 1px solid #ddd;\r\n\tborder-radius: 4px;\r\n}\r\n\r\n.unit {\r\n\tposition: absolute;\r\n\tright: 10px;\r\n\ttop: 50%;\r\n\ttransform: translateY(-50%);\r\n\tcolor: #888;\r\n}\r\n\r\n.hand-text-1 {\r\n\tfont-size: 24rpx;\r\n\tfont-weight: 600;\r\n\tcolor: #888888;\r\n\tmargin: 10rpx 0;\r\n}\r\n\r\n.hand-text-2 {\r\n\tfont-size: 24rpx;\r\n\tfont-weight: 600;\r\n\tcolor: #888888;\r\n\tmargin: 10rpx 0;\r\n}\r\n\r\n.lianying {\r\n\tposition: fixed;\r\n\tbottom: 100rpx;\r\n\tright: 0;\r\n\twidth: 100rpx;\r\n\theight: 100rpx;\r\n\tbackground-color: #c9ab79;\r\n\tborder-radius: 50%;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tcolor: white;\r\n\tflex-direction: column;\r\n\tfont-size: 28rpx;\r\n\tfont-weight: 600;\r\n\tline-height: 36rpx;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=style&index=0&id=0b75b564&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./list.vue?vue&type=style&index=0&id=0b75b564&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363903372\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}