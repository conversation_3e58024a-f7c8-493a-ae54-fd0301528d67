@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.mengceng.data-v-a1cf24e2 {
  width: 38rpx;
  height: 38rpx;
  line-height: 36rpx;
  background: rgba(51, 51, 51, 0.6);
  text-align: center;
  border-radius: 50%;
  opacity: 1;
  position: absolute;
  left: 0px;
  top: 2rpx;
  color: #FFF;
}
.mengceng ._i.data-v-a1cf24e2 {
  font-style: normal;
  font-size: 20rpx;
}
.activity_pic.data-v-a1cf24e2 {
  margin-left: 28rpx;
  padding-left: 20rpx;
}
.activity_pic .picture.data-v-a1cf24e2 {
  display: inline-block;
}
.activity_pic .avatar.data-v-a1cf24e2 {
  width: 38rpx;
  height: 38rpx;
  display: inline-table;
  vertical-align: middle;
  -webkit-user-select: none;
  user-select: none;
  border-radius: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: 0 0;
  margin-right: -10rpx;
  box-shadow: 0 0 0 1px #fff;
}
.activity_pic .pic_count.data-v-a1cf24e2 {
  margin-left: 30rpx;
  color: #c9ab79;
  font-size: 22rpx;
  font-weight: 500;
}
.default.data-v-a1cf24e2 {
  width: 690rpx;
  height: 300rpx;
  border-radius: 14rpx;
  margin: 26rpx auto 0 auto;
  background-color: #ccc;
  text-align: center;
  line-height: 300rpx;
}
.default .iconfont.data-v-a1cf24e2 {
  font-size: 80rpx;
}
.combination.data-v-a1cf24e2 {
  width: auto;
  background-color: #fff;
  border-radius: 14rpx;
  margin: 0 auto 30rpx auto;
  padding: 16rpx 24rpx 24rpx 24rpx;
  background-image: url(data:image/png;base64,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);
  background-repeat: no-repeat;
  background-size: 100%;
}
.combination .title.data-v-a1cf24e2 {
  width: 80%;
  margin-left: 128rpx;
}
.combination .title .sign.data-v-a1cf24e2 {
  width: 40rpx;
  height: 40rpx;
}
.combination .title .sign image.data-v-a1cf24e2 {
  width: 100%;
  height: 100%;
}
.combination .title .name.data-v-a1cf24e2 {
  font-size: 32rpx;
  color: #282828;
  margin-left: 12rpx;
  font-weight: bold;
}
.combination .title .name text.data-v-a1cf24e2 {
  color: #797979;
  font-size: 24rpx;
  font-weight: 400;
  margin-left: 14rpx;
}
.combination .title .more.data-v-a1cf24e2 {
  width: 86rpx;
  height: 40rpx;
  background: linear-gradient(142deg, #FFE9CE 0%, #FFD6A7 100%);
  opacity: 1;
  border-radius: 18px;
  font-size: 22rpx;
  color: #FE960F;
  padding-left: 8rpx;
  font-weight: 800;
}
.combination .title .more .iconfont.data-v-a1cf24e2 {
  font-size: 21rpx;
}
.combination .conter.data-v-a1cf24e2 {
  margin-top: 24rpx;
}
.combination .conter .itemCon.data-v-a1cf24e2 {
  display: inline-block;
  width: 220rpx;
  margin-right: 24rpx;
}
.combination .conter .item.data-v-a1cf24e2 {
  width: 100%;
}
.combination .conter .item .pictrue.data-v-a1cf24e2 {
  width: 100%;
  height: 220rpx;
  border-radius: 6rpx;
}
.combination .conter .item .pictrue image.data-v-a1cf24e2 {
  width: 100%;
  height: 100%;
  border-radius: 6rpx;
}
.combination .conter .item .text.data-v-a1cf24e2 {
  margin-top: 4rpx;
}
.combination .conter .item .text .y_money.data-v-a1cf24e2 {
  font-size: 24rpx;
  color: #999999;
  text-decoration: line-through;
}
.combination .conter .item .text .name.data-v-a1cf24e2 {
  font-size: 24rpx;
  color: #000;
  margin-top: 14rpx;
}
.combination .conter .item .text .money.data-v-a1cf24e2 {
  color: #FD502F;
  font-size: 28rpx;
  height: 100%;
  font-weight: bold;
  margin: 10rpx 0 0rpx 0;
}
.combination .conter .item .text .money .num.data-v-a1cf24e2 {
  font-size: 28rpx;
}

