{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\grade\\creatGrade.vue?vue&type=template&id=ff8c45c0&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\grade\\creatGrade.vue", "mtime": 1753666157938}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\n<el-dialog\n  v-if=\"dialogVisible\"\n  title=\"用户等级\"\n  :visible.sync=\"dialogVisible\"\n  width=\"500px\"\n  :before-close=\"handleClose\" >\n  <el-form :model=\"user\" :rules=\"rules\" ref=\"user\" label-width=\"100px\" class=\"demo-ruleForm\" v-loading=\"loading\">\n    <el-form-item label=\"等级名称\" prop=\"name\">\n      <el-input v-model=\"user.name\" placeholder=\"请输入等级名称\"></el-input>\n    </el-form-item>\n    <el-form-item label=\"等级\" prop=\"grade\">\n      <el-input  v-model.number=\"user.grade\" placeholder=\"请输入等级\"></el-input>\n    </el-form-item>\n    <el-form-item label=\"享受折扣(%)\" prop=\"discount\">\n      <el-input-number  :min=\"0\" :max=\"100\" step-strictly  v-model=\"user.discount\" placeholder=\"请输入享受折扣\"></el-input-number>\n    </el-form-item>\n    <el-form-item label=\"经验\" prop=\"experience\">\n      <el-input-number  v-model.number=\"user.experience\" placeholder=\"请输入经验\" :min=\"0\" step-strictly></el-input-number>\n    </el-form-item>\n    <el-form-item label=\"图标\" prop=\"icon\">\n      <div class=\"upLoadPicBox\" @click=\"modalPicTap('1', 'icon')\">\n        <div v-if=\"user.icon\" class=\"pictrue\"><img :src=\"user.icon\"></div>\n        <div v-else-if=\"formValidate.icon\" class=\"pictrue\"><img :src=\"formValidate.icon\"></div>\n        <div v-else class=\"upLoad\">\n          <i class=\"el-icon-camera cameraIconfont\" />\n        </div>\n      </div>\n    </el-form-item>\n  </el-form>\n  <span slot=\"footer\" class=\"dialog-footer\">\n    <el-button @click=\"resetForm('user')\">取 消</el-button>\n    <el-button type=\"primary\" @click=\"submitForm('formValidate')\" v-hasPermi=\"['admin:system:user:level:update','admin:system:user:level:save']\">确 定</el-button>\n  </span>\n</el-dialog>\n", null]}