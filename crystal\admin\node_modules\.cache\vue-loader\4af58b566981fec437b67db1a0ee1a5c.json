{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\ThemePicker\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\ThemePicker\\index.vue", "mtime": 1753666157781}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nconst version = require('element-ui/package.json').version // element-ui version from node_modules\r\nconst ORIGINAL_THEME = '#409EFF' // default color\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      chalk: '', // content of theme-chalk css\r\n      theme: ''\r\n    }\r\n  },\r\n  computed: {\r\n    defaultTheme() {\r\n      return this.$store.state.settings.theme\r\n    }\r\n  },\r\n  watch: {\r\n    defaultTheme: {\r\n      handler: function(val, oldVal) {\r\n        this.theme = val\r\n      },\r\n      immediate: true\r\n    },\r\n    async theme(val) {\r\n      const oldVal = this.chalk ? this.theme : ORIGINAL_THEME\r\n      if (typeof val !== 'string') return\r\n      const themeCluster = this.getThemeCluster(val.replace('#', ''))\r\n      const originalCluster = this.getThemeCluster(oldVal.replace('#', ''))\r\n\r\n      const $message = this.$message({\r\n        message: '  Compiling the theme',\r\n        customClass: 'theme-message',\r\n        type: 'success',\r\n        duration: 0,\r\n        iconClass: 'el-icon-loading'\r\n      })\r\n\r\n      const getHandler = (variable, id) => {\r\n        return () => {\r\n          const originalCluster = this.getThemeCluster(ORIGINAL_THEME.replace('#', ''))\r\n          const newStyle = this.updateStyle(this[variable], originalCluster, themeCluster)\r\n\r\n          let styleTag = document.getElementById(id)\r\n          if (!styleTag) {\r\n            styleTag = document.createElement('style')\r\n            styleTag.setAttribute('id', id)\r\n            document.head.appendChild(styleTag)\r\n          }\r\n          styleTag.innerText = newStyle\r\n        }\r\n      }\r\n\r\n      if (!this.chalk) {\r\n        const url = `https://unpkg.com/element-ui@${version}/lib/theme-chalk/index.css`\r\n        await this.getCSSString(url, 'chalk')\r\n      }\r\n\r\n      const chalkHandler = getHandler('chalk', 'chalk-style')\r\n\r\n      chalkHandler()\r\n\r\n      const styles = [].slice.call(document.querySelectorAll('style'))\r\n        .filter(style => {\r\n          const text = style.innerText\r\n          return new RegExp(oldVal, 'i').test(text) && !/Chalk Variables/.test(text)\r\n        })\r\n      styles.forEach(style => {\r\n        const { innerText } = style\r\n        if (typeof innerText !== 'string') return\r\n        style.innerText = this.updateStyle(innerText, originalCluster, themeCluster)\r\n      })\r\n\r\n      this.$emit('change', val)\r\n\r\n      $message.close()\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    updateStyle(style, oldCluster, newCluster) {\r\n      let newStyle = style\r\n      oldCluster.forEach((color, index) => {\r\n        newStyle = newStyle.replace(new RegExp(color, 'ig'), newCluster[index])\r\n      })\r\n      return newStyle\r\n    },\r\n\r\n    getCSSString(url, variable) {\r\n      return new Promise(resolve => {\r\n        const xhr = new XMLHttpRequest()\r\n        xhr.onreadystatechange = () => {\r\n          if (xhr.readyState === 4 && xhr.status == 200) {\r\n            this[variable] = xhr.responseText.replace(/@font-face{[^}]+}/, '')\r\n            resolve()\r\n          }\r\n        }\r\n        xhr.open('GET', url)\r\n        xhr.send()\r\n      })\r\n    },\r\n\r\n    getThemeCluster(theme) {\r\n      const tintColor = (color, tint) => {\r\n        let red = parseInt(color.slice(0, 2), 16)\r\n        let green = parseInt(color.slice(2, 4), 16)\r\n        let blue = parseInt(color.slice(4, 6), 16)\r\n\r\n        if (tint === 0) { // when primary color is in its rgb space\r\n          return [red, green, blue].join(',')\r\n        } else {\r\n          red += Math.round(tint * (255 - red))\r\n          green += Math.round(tint * (255 - green))\r\n          blue += Math.round(tint * (255 - blue))\r\n\r\n          red = red.toString(16)\r\n          green = green.toString(16)\r\n          blue = blue.toString(16)\r\n\r\n          return `#${red}${green}${blue}`\r\n        }\r\n      }\r\n\r\n      const shadeColor = (color, shade) => {\r\n        let red = parseInt(color.slice(0, 2), 16)\r\n        let green = parseInt(color.slice(2, 4), 16)\r\n        let blue = parseInt(color.slice(4, 6), 16)\r\n\r\n        red = Math.round((1 - shade) * red)\r\n        green = Math.round((1 - shade) * green)\r\n        blue = Math.round((1 - shade) * blue)\r\n\r\n        red = red.toString(16)\r\n        green = green.toString(16)\r\n        blue = blue.toString(16)\r\n\r\n        return `#${red}${green}${blue}`\r\n      }\r\n\r\n      const clusters = [theme]\r\n      for (let i = 0; i <= 9; i++) {\r\n        clusters.push(tintColor(theme, Number((i / 10).toFixed(2))))\r\n      }\r\n      clusters.push(shadeColor(theme, 0.1))\r\n      return clusters\r\n    }\r\n  }\r\n}\r\n", null]}