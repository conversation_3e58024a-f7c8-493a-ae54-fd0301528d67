@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  margin-top: 25px;
  background-color: #fff !important;
}
.searchGood .search {
  padding-left: 30rpx;
  background-color: #fff !important;
}
.searchGood .search {
  padding-top: 20rpx;
}
.searchGood .search .input {
  width: 598rpx;
  background-color: #f7f7f7;
  border-radius: 33rpx;
  padding: 0 35rpx;
  box-sizing: border-box;
  height: 66rpx;
}
.searchGood .search .input input {
  width: 472rpx;
  font-size: 26rpx;
}
.searchGood .search .input .placeholder {
  color: #bbb;
}
.searchGood .search .input .iconfont {
  color: #000;
  font-size: 35rpx;
}
.searchGood .search .bnt {
  width: 120rpx;
  text-align: center;
  height: 66rpx;
  line-height: 66rpx;
  font-size: 30rpx;
  color: #282828;
}
.searchGood .title {
  font-size: 28rpx;
  color: #999;
  margin: 50rpx 30rpx 25rpx 30rpx;
}
.searchGood .list {
  padding-left: 10rpx;
}
.searchGood .list .item {
  font-size: 26rpx;
  color: #454545;
  padding: 0 21rpx;
  height: 60rpx;
  border-radius: 30rpx;
  line-height: 60rpx;
  border: 1rpx solid #aaa;
  margin: 0 0 20rpx 20rpx;
}
.searchGood .line {
  border-bottom: 1rpx solid #eee;
  margin: 20rpx 30rpx 0 30rpx;
}

