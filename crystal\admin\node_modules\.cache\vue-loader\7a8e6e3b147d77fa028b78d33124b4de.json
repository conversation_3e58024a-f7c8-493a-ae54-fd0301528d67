{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\userprocess-add-and-update.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\userprocess-add-and-update.vue", "mtime": 1753666157945}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport { userprocessCreateApi, userprocessUpdateApi, userprocessDetailApi, userprocessDeleteApi, userprocessListApi, } from '@/api/userprocess'\r\n  import * as systemConfigApi from '@/api/systemConfig.js'\r\nimport Tinymce from '@/components/Tinymce/index'\r\nexport default {\r\n  data() {\r\n    return {\r\n      visible: false,\r\n      userStatus: [],\r\n      dataForm: {\r\n        id: 0,\r\n        userId: '',\r\n        doTime: '',\r\n        name: '',\r\n        userStatus: '',\r\n        remarks: '',\r\n      },\r\n      dataRule: {\r\n        doTime: [\r\n          { required: true, message: '记录时间不能为空', trigger: 'blur' }\r\n        ],\r\n        userStatus: [\r\n          { required: true, message: '记录状态不能为空', trigger: 'blur' }\r\n        ],\r\n      }\r\n    }\r\n  },\r\n  components: {\r\n    Tinymce\r\n  },\r\n  methods: {\r\n    init(id,userId) {\r\n      this.dataForm.id = id || 0\r\n      this.visible = true\r\n      this.$nextTick(() => {\r\n        this.$refs['dataForm'].resetFields()\r\n      this.dataForm.remarks = ''\r\n        if (this.dataForm.id) {\r\n\r\n          userprocessDetailApi(this.dataForm.id).then((data) => {\r\n            this.dataForm.name = data.name\r\n            this.dataForm.type = data.type\r\n            this.dataForm.userStatus = data.userStatus\r\n            this.dataForm.userId = data.userId\r\n            this.dataForm.remarks = data.remarks\r\n          }).catch((res) => {\r\n            this.$message.error(res.message)\r\n          });\r\n        } else {\r\n          this.dataForm.userId = userId\r\n        }\r\n        this.getUserStatus();\r\n      })\r\n    },\r\n      getUserStatus() {\r\n        systemConfigApi.configGetUniq({key: \"userstatus\"}).then(data => {\r\n          this.userStatus = data ? data.split(',') : []\r\n        })\r\n      },\r\n    // 表单提交\r\n    dataFormSubmit() {\r\n      this.$refs['dataForm'].validate((valid) => {\r\n        if (valid) {\r\n          if (!this.dataForm.id) {\r\n\r\n            userprocessCreateApi(this.dataForm).then(() => {\r\n              this.$message({\r\n                message: '操作成功',\r\n                type: 'success',\r\n                duration: 1500,\r\n                onClose: () => {\r\n                  this.visible = false\r\n                  this.$emit('refreshDataList')\r\n                }\r\n              })\r\n            }).catch((res) => {\r\n              this.$message.error(res.message)\r\n            });\r\n          } else {\r\n\r\n            userprocessUpdateApi(this.dataForm).then(() => {\r\n              this.$message({\r\n                message: '操作成功',\r\n                type: 'success',\r\n                duration: 1500,\r\n                onClose: () => {\r\n                  this.visible = false\r\n                  this.$emit('refreshDataList')\r\n                }\r\n              })\r\n            }).catch((res) => {\r\n              this.$message.error(res.message)\r\n            });\r\n          }\r\n        }\r\n      })\r\n    }\r\n  }\r\n}\r\n", null]}