{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/tabNav.vue?9889", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/tabNav.vue?10a2", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/tabNav.vue?2a8b", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/tabNav.vue?4e31", "uni-app:///components/tabNav.vue", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/tabNav.vue?1236", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/tabNav.vue?695c"], "names": ["name", "props", "tabTitle", "type", "default", "data", "tabClick", "isLeft", "isWidth", "tabLeft", "swiperIndex", "childIndex", "childID", "created", "uni", "success", "that", "methods", "longClick", "tempIndex", "index", "childTab", "parentIndex", "parentEmit"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACc;;;AAGnE;AAC6L;AAC7L,gBAAgB,2LAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpBA;AAAA;AAAA;AAAA;AAAquB,CAAgB,srBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACmCzvB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAIA;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;EAEA;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAEA;IACA;IACAC;MACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;QACA;QACAC;QACA;MACA;;MACA;MACA;MACA;QACAhB;QAAA;QACAiB;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACAC;QACAX;QACAR;MACA;;MACA;IACA;IACAoB;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrGA;AAAA;AAAA;AAAA;AAAw3C,CAAgB,6tCAAG,EAAC,C;;;;;;;;;;;ACA54C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/tabNav.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./tabNav.vue?vue&type=template&id=c8b3116e&\"\nvar renderjs\nimport script from \"./tabNav.vue?vue&type=script&lang=js&\"\nexport * from \"./tabNav.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tabNav.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/tabNav.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tabNav.vue?vue&type=template&id=c8b3116e&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 =\n    _vm.tabClick > 0 && _vm.tabTitle[_vm.tabClick].child\n      ? _vm.tabTitle[_vm.tabClick].child.length\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tabNav.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tabNav.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"navTabBox\">\r\n\t\t<view class=\"longTab\">\r\n\t\t\t<scroll-view scroll-x=\"true\" style=\"white-space: nowrap; display: flex;\" scroll-with-animation :scroll-left=\"tabLeft\" show-scrollbar=\"true\">\r\n\t\t\t\t<view class=\"longItem line1\" :style='\"width:\"+isWidth+\"px\"' :data-index=\"index\" :class=\"index===tabClick?'click':''\" v-for=\"(item,index) in tabTitle\" :key=\"index\" :id=\"'id'+index\" @click=\"longClick(index)\">{{item.name}}</view>\r\n\t\t\t\t<view class=\"underlineBox\" :style='\"transform:translateX(\"+isLeft+\"px);width:\"+isWidth+\"px\"'>\r\n\t\t\t\t\t<view class=\"underline\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t</scroll-view>\r\n\t\t</view>\r\n\t\t<view class=\"child-box\" v-if=\"tabClick>0 && tabTitle[tabClick].child?tabTitle[tabClick].child.length>0:0\">\r\n\t\t\t<scroll-view scroll-x=\"true\">\r\n\t\t\t\t<view class=\"wrapper\">\r\n\t\t\t\t\t<view v-for=\"(item,index) in tabTitle[tabClick].child?tabTitle[tabClick].child:[]\" :key=\"index\" class=\"child-item\" :class=\"{on:index == childIndex}\" @click=\"childTab(tabClick,index)\">\r\n\t\t\t\t\t\t<image :src=\"item.extra\" mode=\"\" :style=\"{'background-color':item.extra?'none':'#f7f7f7'}\"></image>\r\n\t\t\t\t\t\t<view class=\"txt line1\">{{item.name}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</scroll-view>\r\n\t\t</view>\r\n\t\t\t<!-- <image :src=\"item.extra\" mode=\"\" :style=\"{'background-color':(item.extra&&item.extra.indexOf('https://') > -1) || (item.extra&&item.extra.indexOf('http://') > -1)?'none':'#f7f7f7'}\"></image> -->\r\n\t\t<!-- <view class=\"child-box\" v-if=\"tabClick>0 && tabTitle[tabClick].child?tabTitle[tabClick].child.length>0:0\">\r\n\t\t\t<scroll-view scroll-x=\"true\" style=\"white-space: nowrap; display: flex;align-items: center; height: 100%;\" scroll-with-animation :scroll-left=\"tabLeft\" show-scrollbar=\"false\">\r\n\t\t\t\t<view class=\"wrapper\">\r\n\t\t\t\t\t<view v-for=\"(item,index) in tabTitle[tabClick].child?tabTitle[tabClick].child:[]\" :key=\"index\" class=\"child-item\" :class=\"{on:index == childIndex}\" @click=\"childTab(tabClick,index)\">\r\n\t\t\t\t\t\t<image :src=\"item.extra\" mode=\"\" :style=\"{'background-color':item.extra?'none':'#f7f7f7'}\"></image>\r\n\t\t\t\t\t\t<view class=\"txt line1\">{{item.name}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</scroll-view>\r\n\t\t</view> -->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tgetProductslist,\r\n\t\tgetProductHot\r\n\t} from '@/api/store.js';\r\n\texport default {\r\n\t\tname: 'navTab',\r\n\t\tprops: {\r\n\t\t\ttabTitle: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault: []\r\n\t\t\t}\r\n\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\ttabClick: 0, //导航栏被点击\r\n\t\t\t\tisLeft: 0, //导航栏下划线位置\r\n\t\t\t\tisWidth: 0, //每个导航栏占位\r\n\t\t\t\ttabLeft:0,\r\n\t\t\t\tswiperIndex:0,\r\n\t\t\t\tchildIndex:0,\r\n\t\t\t\tchildID:0\r\n\t\t\t};\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\t\r\n\t\t\tvar that = this\r\n\t\t\t// 获取设备宽度\r\n\t\t\tuni.getSystemInfo({\r\n\t\t\t\tsuccess(e) {\r\n\t\t\t\t\tthat.isWidth = e.windowWidth / 5 \r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 导航栏点击\r\n\t\t\tlongClick(index){\r\n\t\t\t\tthis.childIndex = 0;\r\n\t\t\t\tif(this.tabTitle.length>5){\r\n\t\t\t\t\tvar tempIndex = index - 2;\r\n\t\t\t\t\ttempIndex = tempIndex<=0 ? 0 : tempIndex;\r\n\t\t\t\t\tthis.tabLeft = (index-2) * this.isWidth //设置下划线位置\r\n\t\t\t\t}\r\n\t\t\t\tthis.tabClick = index //设置导航点击了哪一个\r\n\t\t\t\tthis.isLeft = index * this.isWidth //设置下划线位置\r\n\t\t\t\tlet obj = {\r\n\t\t\t\t\ttype:'big',  //大标题\r\n\t\t\t\t\tindex:index\r\n\t\t\t\t}\r\n\t\t\t\tthis.parentEmit(obj)\r\n\t\t\t\tthis.$parent.currentTab = index //设置swiper的第几页\r\n\t\t\t},\r\n\t\t\t// 导航子类点击\r\n\t\t\tchildTab(tabClick,index){\r\n\t\t\t\tthis.childIndex = index\r\n\t\t\t\tlet obj = {\r\n\t\t\t\t\tparentIndex:tabClick,\r\n\t\t\t\t\tchildIndex:index,\r\n\t\t\t\t\ttype:'small' //小标题\r\n\t\t\t\t}\r\n\t\t\t\tthis.parentEmit(obj)\r\n\t\t\t},\r\n\t\t\tparentEmit(data){\r\n\t\t\t\tthis.$emit('changeTab', data);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.navTabBox {\r\n\t\twidth: 100%;\r\n\t\tcolor: rgba(255, 255, 255, 1);\r\n\t\t.click {\r\n\t\t\tcolor: white;\r\n\t\t}\r\n\t\t.longTab {\r\n\t\t\twidth: 100%;\r\n\t\t\t/* #ifdef H5 */\r\n\t\t\tpadding-bottom: 20rpx;\r\n\t\t\t/* #endif */\r\n\t\t\t/* #ifdef MP */\r\n\t\t\tpadding-top: 12rpx;\r\n\t\t\tpadding-bottom: 12rpx;\r\n\t\t\t/* #endif */\r\n\t\t\t.longItem{ \r\n\t\t\t\theight: 50upx; \r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tline-height: 50upx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\t&.click{\r\n\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.underlineBox {\r\n\t\t\t\theight: 3px;\r\n\t\t\t\twidth: 20%;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-content: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\ttransition: .5s;\r\n\t\t\t\t.underline {\r\n\t\t\t\t\twidth: 33rpx;\r\n\t\t\t\t\theight: 4rpx;\r\n\t\t\t\t\tbackground-color: white;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.child-box{\r\n\t\twidth: 100%;\r\n\t\tposition: relative;\r\n\t\t// height: 152rpx;\r\n\t\tbackground-color: #fff;\r\n\t\t/* #ifdef H5 */\r\n\t\tbox-shadow: 0 2px 5px 1px rgba(0, 0, 0, 0.02);\r\n\t\t/* #endif */\r\n\t\t/* #ifdef MP */\r\n\t\tbox-shadow: 0 2rpx 3rpx 1rpx #f9f9f9;\r\n\t\t/* #endif */\r\n\t\t\r\n\t\t.wrapper{\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tpadding: 20rpx 0;\r\n\t\t\tbackground: #fff;\r\n\t\t\t/* #ifdef H5 */\r\n\t\t\t//box-shadow: 0 2px 5px 1px rgba(0, 0, 0, 0.06);\r\n\t\t\t/* #endif */\r\n\t\t}\r\n\t\t.child-item{\r\n\t\t\tflex-shrink: 0;\r\n\t\t\twidth:140rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tmargin-left: 10rpx;\r\n\t\t\timage{\r\n\t\t\t\twidth: 90rpx;\r\n\t\t\t\theight: 90rpx;\r\n\t\t\t\tborder-radius: 50%;\r\n\t\t\t}\r\n\t\t\t.txt{\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tcolor: #282828;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tmargin-top: 10rpx;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t}\r\n\t\t\t&.on{\r\n\t\t\t\timage{\r\n\t\t\t\t\tborder: 1px solid $theme-color-opacity;\r\n\t\t\t\t}\r\n\t\t\t\t.txt{\r\n\t\t\t\t\tcolor: $theme-color;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tabNav.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tabNav.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363904058\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}