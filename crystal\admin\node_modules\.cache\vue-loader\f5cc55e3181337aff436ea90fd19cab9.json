{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\list\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\list\\index.vue", "mtime": 1753666157941}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { userListApi, groupListApi, levelListApi, tagListApi, groupPiApi, tagPiApi, foundsApi, updateSpreadApi, updatePhoneApi } from '@/api/user'\nimport { spreadClearApi } from '@/api/distribution'\nimport editFrom from './edit'\nimport userDetails from './userDetails'\nimport levelEdit from './level'\nimport userList from '@/components/userList'\nimport * as logistics from '@/api/logistics.js'\nimport Cookies from 'js-cookie'\nimport { checkPermi } from \"@/utils/permission\"; // 权限判断函数\nimport {Debounce} from '@/utils/validate'\nexport default {\n  name: 'UserIndex',\n  components:{ editFrom, userDetails,userList ,levelEdit},\n  filters: {\n    sexFilter(status) {\n      const statusMap = {\n        0: '未知',\n        1: '男',\n        2: '女',\n        3: '保密'\n      }\n      return statusMap[status]\n    }\n  },\n  data() {\n    return {\n      formExtension: {\n        image: '',\n        spreadUid: '',\n        userId: ''\n      },\n      ruleInline: {},\n      extensionVisible: false,\n      userVisible: false,\n      levelInfo:'',\n      pickerOptions: this.$timeOptions,\n      loadingBtn: false,\n      PointValidateForm: {\n        integralType: 2,\n        integralValue: 0,\n        moneyType: 2,\n        moneyValue: 0,\n        uid: ''\n      },\n      loadingPoint: false,\n      VisiblePoint: false,\n      visible: false,\n      userIds: '',\n      dialogVisible: false,\n      levelVisible:false,\n      levelData: [],\n      groupData: [],\n      labelData: [],\n      selData:[],\n      labelPosition:'right',\n      collapse: false,\n      props: {\n        children: 'child',\n        label: 'name',\n        value: 'name',\n        emitPath: false\n      },\n      propsCity: {\n        children: 'child',\n        label: 'name',\n        value: 'name'\n      },\n      headeNum: [\n        { 'type': '', 'name': '全部用户' },\n        { 'type': 'wechat', 'name': '微信公众号用户' },\n        { 'type': 'routine', 'name': '微信小程序用户' },\n        { 'type': 'h5', 'name': 'H5用户' }\n      ],\n      listLoading: true,\n      tableData: {\n        data: [],\n        total: 0\n      },\n      loginType: '',\n      userFrom: {\n        labelId: '',\n        userType: '',\n        sex: '',\n        isPromoter: '',\n        country: '',\n        payCount: '',\n        accessType: 0,\n        dateLimit: '',\n        keywords: '',\n        province: '',\n        city: '',\n        page: 1,\n        limit: 15,\n        level: '',\n        groupId: ''\n      },\n      grid: {\n        xl: 8,\n        lg: 12,\n        md: 12,\n        sm: 24,\n        xs: 24\n      },\n      levelList: [],\n      labelLists: [],\n      groupList: [],\n      selectedData: [],\n      timeVal: [],\n      addresData: [],\n      dynamicValidateForm:{\n        groupId: []\n      },\n      loading: false,\n      groupIdFrom: [],\n      selectionList: [],\n      batchName: '',\n      uid: 0,\n      Visible: false,\n      keyNum: 0,\n      address: [],\n      multipleSelectionAll: [],\n      idKey:'uid',\n      uid:'',\n    }\n  },\n  activated(){\n    this.userFrom.keywords = '';\n    this.loginType = '0';\n    this.getList(1);\n  },\n  mounted() {\n    this.getList()\n    this.groupLists()\n    this.levelLists()\n    this.getTagList()\n    this.getCityList()\n  },\n  methods: {\n    checkPermi,\n    setPhone(row) {\n      this.$prompt('修改手机号', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        inputErrorMessage: '请输入修改手机号',\n        inputType: 'text',\n        inputValue: row.phone,\n        inputPlaceholder: '请输入手机号',\n        closeOnClickModal: false,\n        inputValidator: (value) => {\n          if (!value) return '请填写手机号'\n          // if (!/^1[3456789]\\d{9}$/.test(value)) return '手机号格式不正确!'\n          // if(!value) return '输入不能为空'\n        }\n      }).then(({value}) => {\n        updatePhoneApi({id: row.uid,phone: value}).then(() => {\n          this.$message.success('编辑成功')\n          this.getList();\n        })\n      }).catch(() => {\n        this.$message.info('取消输入')\n      })\n    },\n    // 清除\n    clearSpread(row) {\n      this.$modalSure('解除【' + row.nickname + '】的上级推广人吗').then(() => {\n        spreadClearApi(row.uid).then((res) => {\n          this.$message.success('清除成功')\n          this.getList()\n        })\n      })\n    },\n    onSubExtension(formName){\n      this.$refs[formName].validate((valid) => {\n        if (valid) {\n          updateSpreadApi(this.formExtension).then(res => {\n            this.$message.success('设置成功')\n            this.extensionVisible = false\n            this.getList()\n          })\n        } else {\n          return false;\n        }\n      });\n    },\n    getTemplateRow(row){\n       this.formExtension.image = row.avatar\n       this.formExtension.spreadUid = row.uid\n    },\n    setExtension(row){\n      this.formExtension = {\n          image: '',\n          spreadUid: '',\n          userId: row.uid\n      };\n      this.extensionVisible = true\n    },\n    handleCloseExtension(){\n      this.extensionVisible = false\n    },\n    modalPicTap(){\n      this.userVisible = true\n    },\n    resetForm(){\n      this.visible = false;\n    },\n    reset(formName) {\n      this.userFrom = {\n          labelId: '',\n          userType: '',\n          sex: '',\n          isPromoter: '',\n          country: '',\n          payCount: '',\n          accessType: 0,\n          dateLimit: '',\n          keywords: '',\n          province: '',\n          city: '',\n          page: 1,\n          limit: 15,\n          level: '',\n          groupId: ''\n      }\n      this.levelData = []\n      this.groupData = []\n      this.labelData = []\n      this.timeVal = []\n      this.getList()\n    },\n    // 列表\n    async getCityList() {\n      let res = await logistics.cityListTree();\n      //res.forEach((el, index) => {\n      //     el.child.forEach((cel, j) => {\n      //       delete cel.child\n      //     })\n      //   }) \n        this.addresData = res\n      // })\n    },\n    // 发送文章\n    sendNews() {\n      if (this.selectionList.length === 0) return this.$message.warning('请先选择用户')\n      const _this = this\n      this.$modalArticle(function(row) {\n      },'send')\n    },\n    // 发送优惠劵\n    onSend(){\n      if (this.selectionList.length === 0) return this.$message.warning('请选择要设置的用户');\n      const _this = this\n      this.$modalCoupon('send', this.keyNum += 1, [],function(row) {\n        _this.formValidate.give_coupon_ids = []\n        _this.couponData = []\n        row.map((item) => {\n          _this.formValidate.give_coupon_ids.push(item.coupon_id)\n          _this.couponData.push(item.title)\n        })\n        _this.selectionList = []\n      },this.userIds,'user')\n    },\n    Close() {\n      this.Visible = false\n      this.levelVisible = false;\n    },\n    // 账户详情\n    onDetails(id){\n      this.uid = id\n      this.Visible = true\n    },\n    // 等级\n    onLevel(id,level){\n      var userLevel = new Object();\n      this.levelList.forEach(item=>{\n        if(item.id == level){\n          userLevel.gradeLevel = item.grade;\n        }\n      })\n      userLevel.uid = id;\n      userLevel.level = level;\n      this.levelInfo = userLevel;\n      this.levelVisible = true;\n    },\n    // 积分余额\n    editPoint(id) {\n      this.uid = id\n      this.VisiblePoint = true\n    },\n    // 积分余额\n    submitPointForm:Debounce(function(formName){\n      this.$refs[formName].validate((valid) => {\n        if (valid) {\n          this.PointValidateForm.uid = this.uid\n          this.loadingBtn = true\n          foundsApi(this.PointValidateForm).then(res => {\n            this.$message.success('设置成功')\n            this.loadingBtn = false\n            this.handlePointClose()\n            this.getList()\n          }).catch(() => {\n            this.loadingBtn = false\n          })\n        } else {\n          return false\n        }\n      })\n    }),\n    // 积分余额\n    handlePointClose() {\n      this.VisiblePoint = false\n      this.PointValidateForm = {\n        integralType: 2,\n        integralValue: 0,\n        moneyType: 2,\n        moneyValue: 0,\n        uid: ''\n      }\n    },\n    editUser(id) {\n      this.uid = id\n      this.visible = true\n    },\n    goprocess(id) {\n      this.$router.push({path: '/user/process', query: {id: id}})\n    },\n    submitForm(formName) {\n      // let data = [];\n      // if(!this.userIds){\n      //   if(this.multipleSelectionAll.length){\n      //     this.multipleSelectionAll.map((item) => {\n      //       data.push(item.uid)\n      //     });\n      //     this.userIds = data.join(',');\n      //   }\n      // }\n      this.$refs[formName].validate((valid) => {\n        if (valid) {\n          this.loading = true\n          this.batchName ==='group' ? groupPiApi({groupId: this.dynamicValidateForm.groupId, id: this.userIds}).then(res => {\n            this.$message.success('设置成功')\n            this.loading = false\n            this.handleClose()\n            this.getList()\n          }).catch(() => {\n            this.loading = false\n          }) : tagPiApi({tagId: this.dynamicValidateForm.groupId.join(','), id: this.userIds}).then(res => {\n            this.$message.success('设置成功')\n            this.loading = false\n            this.handleClose()\n            this.getList()\n          }).catch(() => {\n            this.loading = false\n          })\n        } else {\n          return false;\n        }\n      });\n    },\n    setBatch(name, row){\n      this.batchName = name\n      if(row){\n        this.userIds = row.uid\n        if(this.batchName ==='group'){\n          this.dynamicValidateForm.groupId = row.groupId?Number(row.groupId):''\n        }else{\n          this.dynamicValidateForm.groupId = row.tagId?row.tagId.split(',').map(Number):[]\n        }\n      }else{\n        this.dynamicValidateForm.groupId = ''\n      }\n      if (this.multipleSelectionAll.length === 0 && !row) return this.$message.warning('请选择要设置的用户')\n      this.dialogVisible = true\n    },\n    handleClose(){\n      this.dialogVisible = false\n      this.$refs['dynamicValidateForm'].resetFields();\n    },\n    // 全选\n    onSelectTab (selection) {\n      this.selectionList = selection;\n      setTimeout(() => {\n        this.changePageCoreRecordData()\n        let data = [];\n        if(this.multipleSelectionAll.length){\n          this.multipleSelectionAll.map((item) => {\n            data.push(item.uid)\n          });\n          this.userIds = data.join(',');\n        }\n      }, 50)\n    },\n    // 搜索\n    userSearchs () {\n      this.userFrom.page = 1;\n      this.getList();\n    },\n    // 选择国家\n    changeCountry () {\n      if (this.userFrom.country === 'OTHER' || !this.userFrom.country) {\n        this.selectedData = [];\n        this.userFrom.province = '';\n        this.userFrom.city = '';\n        this.address = [];\n      }\n    },\n    // 选择地址\n    handleChange (value) {\n      this.userFrom.province = value[0];\n      this.userFrom.city = value[1];\n    },\n    // 具体日期\n    onchangeTime (e) {\n      this.timeVal = e;\n      this.userFrom.dateLimit = e ? this.timeVal.join(',') : '';\n    },\n    // 分组列表\n    groupLists () {\n      groupListApi({ page: 1, limit: 9999}).then(async res => {\n        this.groupList = res.list\n      })\n    },\n    //标签列表\n    getTagList () {\n      tagListApi({ page: 1, limit: 9999}).then(res => {\n        this.labelLists = res.list\n      })\n    },\n    // 等级列表\n    levelLists () {\n      levelListApi().then(async res => {\n        this.levelList = res\n       localStorage.setItem('levelKey', JSON.stringify(res))\n      })\n    },\n    // 列表\n    getList(num) {\n      this.listLoading = true\n      this.userFrom.page = num ? num : this.userFrom.page;\n      this.userFrom.userType = this.loginType\n      if(this.loginType == 0) this.userFrom.userType =''\n      this.userFrom.level = this.levelData.join(',')\n      this.userFrom.groupId = this.groupData.join(',')\n      this.userFrom.labelId = this.labelData.join(',')\n      userListApi(this.userFrom).then(res => {\n        this.tableData.data = res.list\n        this.tableData.total = res.total\n        this.$nextTick(function() {\n          this.setSelectRow()// 调用跨页选中方法\n        })\n        this.listLoading = false\n      }).catch(() => {\n        this.listLoading = false\n      })\n      this.checkedCities = this.$cache.local.has('user_stroge') ? this.$cache.local.getJSON('user_stroge') : this.checkedCities;\n    },\n    // 设置选中的方法\n    setSelectRow() {\n      if (!this.multipleSelectionAll || this.multipleSelectionAll.length <= 0) {\n        return\n      }\n      // 标识当前行的唯一键的名称\n      const idKey = this.idKey\n      const selectAllIds = []\n      this.multipleSelectionAll.forEach(row => {\n        selectAllIds.push(row[idKey])\n      })\n      this.$refs.table.clearSelection()\n      for (var i = 0; i < this.tableData.data.length; i++) {\n        if (selectAllIds.indexOf(this.tableData.data[i][idKey]) >= 0) {\n          // 设置选中，记住table组件需要使用ref=\"table\"\n          this.$refs.table.toggleRowSelection(this.tableData.data[i], true)\n        }\n      }\n    },\n    // 记忆选择核心方法\n    changePageCoreRecordData() {\n      // 标识当前行的唯一键的名称\n      const idKey = this.idKey\n      const that = this\n      // 如果总记忆中还没有选择的数据，那么就直接取当前页选中的数据，不需要后面一系列计算\n      if (this.multipleSelectionAll.length <= 0) {\n        this.multipleSelectionAll = this.selectionList\n        return\n      }\n      // 总选择里面的key集合\n      const selectAllIds = []\n      this.multipleSelectionAll.forEach(row => {\n        selectAllIds.push(row[idKey])\n      })\n      const selectIds = []\n      // 获取当前页选中的id\n      this.selectionList.forEach(row => {\n        selectIds.push(row[idKey])\n        // 如果总选择里面不包含当前页选中的数据，那么就加入到总选择集合里\n        if (selectAllIds.indexOf(row[idKey]) < 0) {\n          that.multipleSelectionAll.push(row)\n        }\n      })\n      const noSelectIds = []\n      // 得到当前页没有选中的id\n      this.tableData.data.forEach(row => {\n        if (selectIds.indexOf(row[idKey]) < 0) {\n          noSelectIds.push(row[idKey])\n        }\n      })\n      noSelectIds.forEach(uid => {\n        if (selectAllIds.indexOf(uid) >= 0) {\n          for (let i = 0; i < that.multipleSelectionAll.length; i++) {\n            if (that.multipleSelectionAll[i][idKey] == uid) {\n              // 如果总选择中有未被选中的，那么就删除这条\n              that.multipleSelectionAll.splice(i, 1)\n              break\n            }\n          }\n        }\n      })\n    },\n    pageChange(page) {\n      this.changePageCoreRecordData()\n      this.userFrom.page = page\n      this.getList()\n    },\n    handleSizeChange(val) {\n      this.changePageCoreRecordData()\n      this.userFrom.limit = val\n      this.getList()\n    },\n    // 删除\n    handleDelete(id, idx) {\n      this.$modalSure().then(() => {\n        productDeleteApi(id).then(() => {\n          this.$message.success('删除成功')\n          this.getList()\n        })\n      })\n    },\n    onchangeIsShow(row) {\n      row.isShow\n        ? putOnShellApi( row.id ).then(() => {\n          this.$message.success('上架成功')\n          this.getList()\n        }).catch(()=>{\n          row.isShow = !row.isShow\n        }) : offShellApi(row.id).then(() => {\n          this.$message.success('下架成功')\n          this.getList()\n        }).catch(()=>{\n          row.isShow = !row.isShow\n        })\n    },\n  }\n}\n", null]}