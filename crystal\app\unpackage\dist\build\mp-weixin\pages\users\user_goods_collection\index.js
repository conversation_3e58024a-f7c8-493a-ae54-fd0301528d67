(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/users/user_goods_collection/index"],{"0978":function(t,e,i){"use strict";var o=i("5f20"),c=i.n(o);c.a},"179b":function(t,e,i){"use strict";(function(t,e){var o=i("47a9");i("5c2d");o(i("3240"));var c=o(i("383a"));t.__webpack_require_UNI_MP_PLUGIN__=i,e(c.default)}).call(this,i("3223")["default"],i("df3c")["createPage"])},"383a":function(t,e,i){"use strict";i.r(e);var o=i("4f6b"),c=i("c693");for(var l in c)["default"].indexOf(l)<0&&function(t){i.d(e,t,(function(){return c[t]}))}(l);i("0978");var n=i("828b"),s=Object(n["a"])(c["default"],o["b"],o["c"],!1,null,"7ae09b1e",null,!1,o["a"],void 0);e["default"]=s.exports},"4f6b":function(t,e,i){"use strict";i.d(e,"b",(function(){return o})),i.d(e,"c",(function(){return c})),i.d(e,"a",(function(){}));var o=function(){var t=this,e=t.$createElement,i=(t._self._c,t.collectProductList.length),o=i?t.__map(t.collectProductList,(function(e,i){var o=t.__get_orig(e),c=t.footerswitch?null:e.id.toString();return{$orig:o,g1:c}})):null,c=i?null:!t.collectProductList.length&&t.page>1;t.$mp.data=Object.assign({},{$root:{g0:i,l0:o,g2:c}})},c=[]},"5f20":function(t,e,i){},"7b49":function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=i("1932"),c=i("8f59"),l=i("cda4"),n={components:{recommend:function(){Promise.all([i.e("common/vendor"),i.e("components/recommend/index")]).then(function(){return resolve(i("e032"))}.bind(null,i)).catch(i.oe)},authorize:function(){Promise.all([i.e("common/vendor"),i.e("components/Authorize")]).then(function(){return resolve(i("cf49"))}.bind(null,i)).catch(i.oe)},home:function(){i.e("components/home/<USER>").then(function(){return resolve(i("bc9e"))}.bind(null,i)).catch(i.oe)}},data:function(){return{footerswitch:!0,hostProduct:[],loadTitle:"加载更多",loading:!1,loadend:!1,collectProductList:[],limit:8,page:1,isAuto:!1,isShowAuth:!1,hotScroll:!1,hotPage:1,hotLimit:10,isAllSelect:!1,selectValue:[],delBtnWidth:80,totals:0}},computed:(0,c.mapGetters)(["isLogin"]),onLoad:function(){this.isLogin?(this.loadend=!1,this.page=1,this.collectProductList=[],this.get_user_collect_product()):(0,l.toLogin)()},onShow:function(){this.loadend=!1,this.page=1,this.collectProductList=[],this.get_user_collect_product()},methods:{drawStart:function(t){var e=t.touches[0];this.startX=e.clientX},drawMove:function(t){var e=t.touches[0],i=(this.collectProductList[t.currentTarget.dataset.index],this.startX-e.clientX);i>=20?(i>this.delBtnWidth&&(i=this.delBtnWidth),this.$set(this.collectProductList[t.currentTarget.dataset.index],"right",i)):this.$set(this.collectProductList[t.currentTarget.dataset.index],"right",0)},drawEnd:function(t){var e=this.collectProductList[t.currentTarget.dataset.index];e.right>=this.delBtnWidth/2?this.$set(this.collectProductList[t.currentTarget.dataset.index],"right",this.delBtnWidth):this.$set(this.collectProductList[t.currentTarget.dataset.index],"right",0)},manage:function(){this.footerswitch=!this.footerswitch},checkboxChange:function(t){for(var e=this.collectProductList,i=t.detail.value,o=0,c=e.length;o<c;++o){var l=e[o];i.includes(l.id.toString())?this.$set(l,"checked",!0):this.$set(l,"checked",!1)}this.selectValue=i.toString(),this.isAllSelect=e.length===i.length},checkboxAllChange:function(t){var e=t.detail.value;e.length>0?this.setAllSelectValue(1):this.setAllSelectValue(0)},setAllSelectValue:function(t){var e=this,i=[];this.collectProductList.length>0&&(this.collectProductList.map((function(o){t?(e.$set(o,"checked",!0),i.push(o.id),e.isAllSelect=!0):(e.$set(o,"checked",!1),e.isAllSelect=!1)})),this.selectValue=i.toString())},onLoadFun:function(){this.get_user_collect_product(),this.get_host_product()},authColse:function(t){this.isShowAuth=t},get_user_collect_product:function(){var t=this;this.loading||this.loadend||(t.loading=!0,t.loadTitle="",(0,o.getCollectUserList)({page:t.page,limit:t.limit}).then((function(e){e.data.list.map((function(e){t.$set(e,"right",0)})),t.totals=e.data.total;var i=e.data.list,o=i.length<t.limit;t.collectProductList=t.$util.SplitArray(i,t.collectProductList),t.$set(t,"collectProductList",t.collectProductList),0===t.collectProductList.length&&t.get_host_product(),t.loadend=o,t.loadTitle=o?"我也是有底线的":"加载更多",t.page=t.page+1,t.loading=!1})).catch((function(e){t.loading=!1,t.loadTitle="加载更多"})))},delCollection:function(t,e){this.selectValue=t,this.del({ids:this.selectValue.toString()})},delCollectionAll:function(){if(!this.selectValue||0==this.selectValue.length)return this.$util.Tips({title:"请选择商品"});this.del({ids:this.selectValue})},del:function(t){var e=this;(0,o.collectDelete)(t).then((function(t){e.$util.Tips({title:"取消收藏成功",icon:"success"}),e.collectProductList=[],e.loadend=!1,e.page=1,e.get_user_collect_product()})).catch((function(t){return e.$util.Tips({title:t})}))},get_host_product:function(){var t=this;t.hotScroll||(0,o.getProductHot)(t.hotPage,t.hotLimit).then((function(e){t.hotPage++,t.hotScroll=e.data.list.length<t.hotLimit,t.hostProduct=t.hostProduct.concat(e.data.list)}))}},onReachBottom:function(){this.get_user_collect_product(),this.get_host_product()}};e.default=n},c693:function(t,e,i){"use strict";i.r(e);var o=i("7b49"),c=i.n(o);for(var l in o)["default"].indexOf(l)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(l);e["default"]=c.a}},[["179b","common/runtime","common/vendor"]]]);