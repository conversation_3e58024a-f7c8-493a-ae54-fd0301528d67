<view class="data-v-7abd4a12"><view class="newsDetail data-v-7abd4a12"><view class="title data-v-7abd4a12">{{articleInfo.title}}</view><view class="list acea-row row-middle data-v-7abd4a12"><view class="label data-v-7abd4a12">{{articleInfo.author}}</view><view class="item data-v-7abd4a12">{{articleInfo.createTime}}</view><view class="item data-v-7abd4a12"><text class="iconfont icon-liulan data-v-7abd4a12"></text>{{articleInfo.visit}}</view></view><view class="conters data-v-7abd4a12"><jyf-parser vue-id="46270d98-1" html="{{content}}" tag-style="{{tagStyle}}" data-ref="article" class="data-v-7abd4a12 vue-ref" bind:__l="__l"></jyf-parser></view><block wx:if="{{store_info.id}}"><view class="picTxt acea-row row-between-wrapper data-v-7abd4a12"><view class="pictrue data-v-7abd4a12"><image src="{{store_info.image}}" class="data-v-7abd4a12"></image></view><view class="text data-v-7abd4a12"><view class="name line1 data-v-7abd4a12">{{store_info.storeName}}</view><view class="money font-color data-v-7abd4a12">￥<text class="num data-v-7abd4a12">{{store_info.price}}</text></view><view class="y_money data-v-7abd4a12">{{"￥"+store_info.otPrice}}</view></view><navigator class="label data-v-7abd4a12" url="{{'/pages/goods_details/index?id='+store_info.id}}" hover-class="none"><text class="span data-v-7abd4a12">查看商品</text></navigator></view></block><button class="bnt bg-color data-v-7abd4a12" open-type="share" hover-class="none">和好友一起分享</button></view><share-info vue-id="46270d98-2" shareInfoStatus="{{shareInfoStatus}}" data-event-opts="{{[['^setShareInfoStatus',[['setShareInfoStatus']]]]}}" bind:setShareInfoStatus="__e" class="data-v-7abd4a12" bind:__l="__l"></share-info><home vue-id="46270d98-3" class="data-v-7abd4a12" bind:__l="__l"></home></view>