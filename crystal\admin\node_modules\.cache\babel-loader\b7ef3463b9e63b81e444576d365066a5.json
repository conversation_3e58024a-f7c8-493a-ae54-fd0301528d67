{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\storeComment\\creatComment.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\storeComment\\creatComment.vue", "mtime": 1753666157925}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _store = require(\"@/api/store\");\nvar _validate = require(\"@/utils/validate\");\nfunction _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; } //\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar defaultObj = {\n  avatar: '',\n  comment: '',\n  nickname: '',\n  pics: '',\n  productId: '',\n  productScore: null,\n  serviceScore: null,\n  sku: ''\n};\nvar _default = exports.default = {\n  name: \"creatComment\",\n  props: {\n    num: {\n      type: Number,\n      required: 0\n    }\n  },\n  data: function data() {\n    var checkProductScore = function checkProductScore(rule, value, callback) {\n      if (!value) {\n        return callback(new Error('商品分数不能为空'));\n      } else {\n        callback();\n      }\n    };\n    var checkServiceScore = function checkServiceScore(rule, value, callback) {\n      if (!value) {\n        return callback(new Error('服务分数不能为空'));\n      } else {\n        callback();\n      }\n    };\n    return {\n      loadingbtn: false,\n      loading: false,\n      pics: [],\n      image: '',\n      formValidate: Object.assign({}, defaultObj),\n      rules: {\n        avatar: [{\n          required: true,\n          message: '请选择用户头像',\n          trigger: 'change'\n        }],\n        productId: [{\n          required: true,\n          message: '请选择商品',\n          trigger: 'change'\n        }],\n        comment: [{\n          required: true,\n          message: '请填写评价内容',\n          trigger: 'blur'\n        }],\n        nickname: [{\n          required: true,\n          message: '请填写用户名称',\n          trigger: 'blur'\n        }],\n        pics: [{\n          required: true,\n          message: '请选择评价图片',\n          trigger: 'change'\n        }],\n        productScore: [{\n          required: true,\n          validator: checkProductScore,\n          trigger: 'blur'\n        }],\n        serviceScore: [{\n          required: true,\n          validator: checkServiceScore,\n          trigger: 'change'\n        }]\n      }\n    };\n  },\n  watch: {\n    num: {\n      handler: function handler(val) {\n        this.resetForm('formValidate');\n      },\n      deep: true\n    }\n  },\n  methods: {\n    changeGood: function changeGood() {\n      var _this = this;\n      this.$modalGoodList(function (row) {\n        _this.image = row.image;\n        _this.formValidate.productId = row.id;\n        _this.formValidate.sku = row.attrValue[0].suk;\n      });\n    },\n    // 点击商品图\n    modalPicTap: function modalPicTap(tit) {\n      var _this = this;\n      _this.$modalUpload(function (img) {\n        tit === '1' ? _this.formValidate.avatar = img[0].sattDir : img.map(function (item) {\n          _this.pics.push(item.sattDir);\n        });\n      }, tit, 'store');\n    },\n    handleRemove: function handleRemove(i) {\n      this.pics.splice(i, 1);\n    },\n    submitForm: (0, _validate.Debounce)(function (formName) {\n      var _this2 = this;\n      this.formValidate.pics = this.pics.length > 0 ? JSON.stringify(this.pics) : '';\n      this.$refs[formName].validate(function (valid) {\n        if (valid) {\n          _this2.loadingbtn = true;\n          (0, _store.replyCreatApi)(_this2.formValidate).then(function () {\n            _this2.$message.success(\"新增成功\");\n            setTimeout(function () {\n              // this.clear();\n              _this2.$emit('getList');\n            }, 600);\n            _this2.loadingbtn = false;\n          }).catch(function () {\n            _this2.loadingbtn = false;\n          });\n        } else {\n          return false;\n        }\n      });\n    }),\n    resetForm: function resetForm(formName) {\n      this.$refs[formName].resetFields();\n      this.pics = [];\n      this.formValidate.pics = '';\n    },\n    info: function info() {\n      var _this3 = this;\n      this.loading = true;\n      (0, _store.replyInfoApi)(this.formValidate).then(function () {\n        _this3.formValidate = res;\n        _this3.loading = false;\n      }).catch(function () {\n        _this3.loading = false;\n      });\n    },\n    // 移动\n    handleDragStart: function handleDragStart(e, item) {\n      this.dragging = item;\n    },\n    handleDragEnd: function handleDragEnd(e, item) {\n      this.dragging = null;\n    },\n    handleDragOver: function handleDragOver(e) {\n      e.dataTransfer.dropEffect = 'move';\n    },\n    handleDragEnter: function handleDragEnter(e, item) {\n      e.dataTransfer.effectAllowed = 'move';\n      if (item === this.dragging) {\n        return;\n      }\n      var newItems = _toConsumableArray(this.pics);\n      var src = newItems.indexOf(this.dragging);\n      var dst = newItems.indexOf(item);\n      newItems.splice.apply(newItems, [dst, 0].concat(_toConsumableArray(newItems.splice(src, 1))));\n      this.pics = newItems;\n    }\n  }\n};", null]}