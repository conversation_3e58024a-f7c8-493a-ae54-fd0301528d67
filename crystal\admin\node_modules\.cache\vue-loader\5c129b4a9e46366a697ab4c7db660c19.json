{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\index.vue?vue&type=template&id=3fb1a7fa&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\index.vue", "mtime": 1753666157923}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"divBox relative\" },\n    [\n      _c(\n        \"el-card\",\n        { staticClass: \"box-card\" },\n        [\n          _c(\n            \"div\",\n            {\n              staticClass: \"clearfix\",\n              attrs: { slot: \"header\" },\n              slot: \"header\",\n            },\n            [\n              _vm.checkPermi([\"admin:product:tabs:headers\"])\n                ? _c(\n                    \"el-tabs\",\n                    {\n                      on: { \"tab-click\": _vm.seachList },\n                      model: {\n                        value: _vm.tableFrom.type,\n                        callback: function ($$v) {\n                          _vm.$set(_vm.tableFrom, \"type\", $$v)\n                        },\n                        expression: \"tableFrom.type\",\n                      },\n                    },\n                    _vm._l(_vm.headeNum, function (item, index) {\n                      return _c(\"el-tab-pane\", {\n                        key: index,\n                        attrs: {\n                          label: item.name + \"(\" + item.count + \")\",\n                          name: item.type.toString(),\n                        },\n                      })\n                    }),\n                    1\n                  )\n                : _vm._e(),\n              _vm._v(\" \"),\n              _c(\n                \"div\",\n                { staticClass: \"container mt-1\" },\n                [\n                  _c(\n                    \"el-form\",\n                    { attrs: { inline: \"\", size: \"small\" } },\n                    [\n                      _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"商品分类：\" } },\n                        [\n                          _c(\"el-cascader\", {\n                            staticClass: \"selWidth mr20\",\n                            attrs: {\n                              options: _vm.merCateList,\n                              props: _vm.props,\n                              clearable: \"\",\n                              size: \"small\",\n                            },\n                            on: { change: _vm.seachList },\n                            model: {\n                              value: _vm.tableFrom.cateId,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.tableFrom, \"cateId\", $$v)\n                              },\n                              expression: \"tableFrom.cateId\",\n                            },\n                          }),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"商品搜索：\" } },\n                        [\n                          _c(\n                            \"el-input\",\n                            {\n                              staticClass: \"selWidth\",\n                              attrs: {\n                                placeholder: \"请输入商品名称，关键字，商品ID\",\n                                size: \"small\",\n                                clearable: \"\",\n                              },\n                              model: {\n                                value: _vm.tableFrom.keywords,\n                                callback: function ($$v) {\n                                  _vm.$set(_vm.tableFrom, \"keywords\", $$v)\n                                },\n                                expression: \"tableFrom.keywords\",\n                              },\n                            },\n                            [\n                              _c(\"el-button\", {\n                                directives: [\n                                  {\n                                    name: \"hasPermi\",\n                                    rawName: \"v-hasPermi\",\n                                    value: [\"admin:product:list\"],\n                                    expression: \"['admin:product:list']\",\n                                  },\n                                ],\n                                attrs: {\n                                  slot: \"append\",\n                                  icon: \"el-icon-search\",\n                                  size: \"small\",\n                                },\n                                on: { click: _vm.seachList },\n                                slot: \"append\",\n                              }),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"router-link\",\n                { attrs: { to: { path: \"/store/list/creatProduct\" } } },\n                [\n                  _c(\n                    \"el-button\",\n                    {\n                      directives: [\n                        {\n                          name: \"hasPermi\",\n                          rawName: \"v-hasPermi\",\n                          value: [\"admin:product:save\"],\n                          expression: \"['admin:product:save']\",\n                        },\n                      ],\n                      staticClass: \"mr10\",\n                      attrs: { size: \"small\", type: \"primary\" },\n                    },\n                    [_vm._v(\"添加商品\")]\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-table\",\n            {\n              directives: [\n                {\n                  name: \"loading\",\n                  rawName: \"v-loading\",\n                  value: _vm.listLoading,\n                  expression: \"listLoading\",\n                },\n              ],\n              staticStyle: { width: \"100%\" },\n              attrs: {\n                data: _vm.tableData.data,\n                size: \"mini\",\n                \"highlight-current-row\": true,\n                \"header-cell-style\": { fontWeight: \"bold\" },\n              },\n            },\n            [\n              _c(\"el-table-column\", {\n                attrs: { type: \"expand\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (props) {\n                      return [\n                        _c(\n                          \"el-form\",\n                          {\n                            staticClass: \"demo-table-expand\",\n                            attrs: { \"label-position\": \"left\", inline: \"\" },\n                          },\n                          [\n                            _c(\n                              \"el-form-item\",\n                              { attrs: { label: \"商品分类：\" } },\n                              _vm._l(\n                                props.row.cateValues.split(\",\"),\n                                function (item, index) {\n                                  return _c(\n                                    \"span\",\n                                    { key: index, staticClass: \"mr10\" },\n                                    [_vm._v(_vm._s(item))]\n                                  )\n                                }\n                              ),\n                              0\n                            ),\n                            _vm._v(\" \"),\n                            _c(\n                              \"el-form-item\",\n                              { attrs: { label: \"市场价：\" } },\n                              [_c(\"span\", [_vm._v(_vm._s(props.row.otPrice))])]\n                            ),\n                            _vm._v(\" \"),\n                            _c(\n                              \"el-form-item\",\n                              { attrs: { label: \"成本价：\" } },\n                              [_c(\"span\", [_vm._v(_vm._s(props.row.cost))])]\n                            ),\n                            _vm._v(\" \"),\n                            _c(\"el-form-item\", { attrs: { label: \"收藏：\" } }, [\n                              _c(\"span\", [\n                                _vm._v(_vm._s(props.row.collectCount)),\n                              ]),\n                            ]),\n                            _vm._v(\" \"),\n                            _c(\n                              \"el-form-item\",\n                              { attrs: { label: \"虚拟销量：\" } },\n                              [_c(\"span\", [_vm._v(_vm._s(props.row.ficti))])]\n                            ),\n                          ],\n                          1\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { prop: \"id\", label: \"ID\", \"min-width\": \"50\" },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { label: \"商品图\", \"min-width\": \"80\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\n                          \"div\",\n                          { staticClass: \"demo-image__preview\" },\n                          [\n                            _c(\"el-image\", {\n                              staticStyle: { width: \"36px\", height: \"36px\" },\n                              attrs: {\n                                src: scope.row.image,\n                                \"preview-src-list\": [scope.row.image],\n                              },\n                            }),\n                          ],\n                          1\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: \"商品名称\",\n                  prop: \"storeName\",\n                  \"min-width\": \"300\",\n                  \"show-overflow-tooltip\": true,\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"price\",\n                  label: \"商品售价\",\n                  \"min-width\": \"90\",\n                  align: \"center\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"sales\",\n                  label: \"销量\",\n                  \"min-width\": \"90\",\n                  align: \"center\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"stock\",\n                  label: \"库存\",\n                  \"min-width\": \"90\",\n                  align: \"center\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"sort\",\n                  label: \"排序\",\n                  \"min-width\": \"70\",\n                  align: \"center\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: \"添加时间\",\n                  \"min-width\": \"120\",\n                  align: \"center\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\"span\", [\n                          _vm._v(\n                            _vm._s(_vm._f(\"formatDate\")(scope.row.addTime))\n                          ),\n                        ]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { label: \"状态\", \"min-width\": \"80\", fixed: \"right\" },\n                scopedSlots: _vm._u(\n                  [\n                    {\n                      key: \"default\",\n                      fn: function (scope) {\n                        return _vm.checkPermi([\n                          \"admin:product:up\",\n                          \"admin:product:down\",\n                        ])\n                          ? [\n                              _c(\"el-switch\", {\n                                attrs: {\n                                  disabled: Number(_vm.tableFrom.type) > 2,\n                                  \"active-value\": true,\n                                  \"inactive-value\": false,\n                                  \"active-text\": \"上架\",\n                                  \"inactive-text\": \"下架\",\n                                },\n                                on: {\n                                  change: function ($event) {\n                                    return _vm.onchangeIsShow(scope.row)\n                                  },\n                                },\n                                model: {\n                                  value: scope.row.isShow,\n                                  callback: function ($$v) {\n                                    _vm.$set(scope.row, \"isShow\", $$v)\n                                  },\n                                  expression: \"scope.row.isShow\",\n                                },\n                              }),\n                            ]\n                          : undefined\n                      },\n                    },\n                  ],\n                  null,\n                  true\n                ),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: \"操作\",\n                  \"min-width\": \"150\",\n                  fixed: \"right\",\n                  align: \"center\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\n                          \"router-link\",\n                          {\n                            attrs: {\n                              to: {\n                                path:\n                                  \"/store/list/creatProduct/\" +\n                                  scope.row.id +\n                                  \"/1\",\n                              },\n                            },\n                          },\n                          [\n                            _c(\n                              \"el-button\",\n                              {\n                                directives: [\n                                  {\n                                    name: \"hasPermi\",\n                                    rawName: \"v-hasPermi\",\n                                    value: [\"admin:product:info\"],\n                                    expression: \"['admin:product:info']\",\n                                  },\n                                ],\n                                staticClass: \"mr10\",\n                                attrs: { type: \"text\", size: \"small\" },\n                              },\n                              [_vm._v(\"详情\")]\n                            ),\n                          ],\n                          1\n                        ),\n                        _vm._v(\" \"),\n                        _c(\n                          \"router-link\",\n                          {\n                            attrs: {\n                              to: {\n                                path:\n                                  \"/store/list/creatProduct/\" + scope.row.id,\n                              },\n                            },\n                          },\n                          [\n                            _vm.tableFrom.type !== \"5\" &&\n                            _vm.tableFrom.type !== \"1\"\n                              ? _c(\n                                  \"el-button\",\n                                  {\n                                    directives: [\n                                      {\n                                        name: \"hasPermi\",\n                                        rawName: \"v-hasPermi\",\n                                        value: [\"admin:product:update\"],\n                                        expression: \"['admin:product:update']\",\n                                      },\n                                    ],\n                                    staticClass: \"mr10\",\n                                    attrs: { type: \"text\", size: \"small\" },\n                                  },\n                                  [_vm._v(\"编辑\")]\n                                )\n                              : _vm._e(),\n                          ],\n                          1\n                        ),\n                        _vm._v(\" \"),\n                        _vm.tableFrom.type === \"5\"\n                          ? _c(\n                              \"el-button\",\n                              {\n                                directives: [\n                                  {\n                                    name: \"hasPermi\",\n                                    rawName: \"v-hasPermi\",\n                                    value: [\"admin:product:restore\"],\n                                    expression: \"['admin:product:restore']\",\n                                  },\n                                ],\n                                attrs: { type: \"text\", size: \"small\" },\n                                on: {\n                                  click: function ($event) {\n                                    return _vm.handleRestore(\n                                      scope.row.id,\n                                      scope.$index\n                                    )\n                                  },\n                                },\n                              },\n                              [_vm._v(\"恢复商品\")]\n                            )\n                          : _vm._e(),\n                        _vm._v(\" \"),\n                        _c(\n                          \"el-button\",\n                          {\n                            directives: [\n                              {\n                                name: \"hasPermi\",\n                                rawName: \"v-hasPermi\",\n                                value: [\"admin:product:delete\"],\n                                expression: \"['admin:product:delete']\",\n                              },\n                            ],\n                            attrs: { type: \"text\", size: \"small\" },\n                            on: {\n                              click: function ($event) {\n                                return _vm.handleDelete(\n                                  scope.row.id,\n                                  _vm.tableFrom.type\n                                )\n                              },\n                            },\n                          },\n                          [\n                            _vm._v(\n                              _vm._s(\n                                _vm.tableFrom.type === \"5\"\n                                  ? \"删除\"\n                                  : \"加入回收站\"\n                              )\n                            ),\n                          ]\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"div\",\n            { staticClass: \"block\" },\n            [\n              _c(\"el-pagination\", {\n                attrs: {\n                  \"page-sizes\": [20, 40, 60, 80],\n                  \"page-size\": _vm.tableFrom.limit,\n                  \"current-page\": _vm.tableFrom.page,\n                  layout: \"total, sizes, prev, pager, next, jumper\",\n                  total: _vm.tableData.total,\n                },\n                on: {\n                  \"size-change\": _vm.handleSizeChange,\n                  \"current-change\": _vm.pageChange,\n                },\n              }),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"el-dialog\",\n        {\n          staticClass: \"taoBaoModal\",\n          attrs: {\n            title: \"复制淘宝、天猫、京东、苏宁\",\n            visible: _vm.dialogVisible,\n            \"close-on-click-modal\": false,\n            width: \"1200px\",\n            \"before-close\": _vm.handleClose,\n          },\n          on: {\n            \"update:visible\": function ($event) {\n              _vm.dialogVisible = $event\n            },\n          },\n        },\n        [\n          _vm.dialogVisible\n            ? _c(\"tao-bao\", { on: { handleCloseMod: _vm.handleCloseMod } })\n            : _vm._e(),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}