{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\marketing\\coupon\\record\\index.vue?vue&type=template&id=4307e9ca&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\marketing\\coupon\\record\\index.vue", "mtime": 1753666157892}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"divBox\" },\n    [\n      _c(\n        \"el-card\",\n        { staticClass: \"box-card\" },\n        [\n          _c(\n            \"div\",\n            {\n              staticClass: \"clearfix\",\n              attrs: { slot: \"header\" },\n              slot: \"header\",\n            },\n            [\n              _c(\n                \"div\",\n                { staticClass: \"filter-container\" },\n                [\n                  _c(\n                    \"el-form\",\n                    { attrs: { inline: true } },\n                    [\n                      _c(\n                        \"el-form-item\",\n                        { staticClass: \"mr10\", attrs: { label: \"使用状态：\" } },\n                        [\n                          _c(\n                            \"el-select\",\n                            {\n                              staticClass: \"selWidth\",\n                              attrs: {\n                                placeholder: \"请选择使用状态\",\n                                clearable: \"\",\n                              },\n                              on: { change: _vm.seachList },\n                              model: {\n                                value: _vm.tableFromIssue.status,\n                                callback: function ($$v) {\n                                  _vm.$set(_vm.tableFromIssue, \"status\", $$v)\n                                },\n                                expression: \"tableFromIssue.status\",\n                              },\n                            },\n                            [\n                              _c(\"el-option\", {\n                                attrs: { label: \"已使用\", value: \"1\" },\n                              }),\n                              _vm._v(\" \"),\n                              _c(\"el-option\", {\n                                attrs: { label: \"未使用\", value: \"0\" },\n                              }),\n                              _vm._v(\" \"),\n                              _c(\"el-option\", {\n                                attrs: { label: \"已过期\", value: \"2\" },\n                              }),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"领取人：\" } },\n                        [\n                          _c(\n                            \"el-select\",\n                            {\n                              staticClass: \"selWidth\",\n                              attrs: {\n                                \"reserve-keyword\": \"\",\n                                remote: \"\",\n                                filterable: \"\",\n                                \"remote-method\": _vm.remoteMethod,\n                                loading: _vm.loading,\n                                placeholder: \"请输入领取人\",\n                                clearable: \"\",\n                              },\n                              on: { change: _vm.seachList },\n                              model: {\n                                value: _vm.tableFromIssue.uid,\n                                callback: function ($$v) {\n                                  _vm.$set(_vm.tableFromIssue, \"uid\", $$v)\n                                },\n                                expression: \"tableFromIssue.uid\",\n                              },\n                            },\n                            _vm._l(_vm.options, function (item) {\n                              return _c(\"el-option\", {\n                                key: item.uid,\n                                attrs: {\n                                  label: item.nickname,\n                                  value: item.uid,\n                                },\n                              })\n                            }),\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-form-item\",\n                        { staticClass: \"mr10\", attrs: { label: \"优惠劵：\" } },\n                        [\n                          _c(\n                            \"el-input\",\n                            {\n                              staticClass: \"selWidth\",\n                              attrs: {\n                                placeholder: \"请输入优惠劵\",\n                                clearable: \"\",\n                              },\n                              model: {\n                                value: _vm.tableFromIssue.name,\n                                callback: function ($$v) {\n                                  _vm.$set(_vm.tableFromIssue, \"name\", $$v)\n                                },\n                                expression: \"tableFromIssue.name\",\n                              },\n                            },\n                            [\n                              _c(\"el-button\", {\n                                attrs: {\n                                  slot: \"append\",\n                                  icon: \"el-icon-search\",\n                                },\n                                on: { click: _vm.seachList },\n                                slot: \"append\",\n                              }),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n            ]\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-table\",\n            {\n              directives: [\n                {\n                  name: \"loading\",\n                  rawName: \"v-loading\",\n                  value: _vm.Loading,\n                  expression: \"Loading\",\n                },\n              ],\n              staticStyle: { width: \"100%\" },\n              attrs: {\n                data: _vm.issueData.data,\n                \"header-cell-style\": { fontWeight: \"bold\" },\n              },\n            },\n            [\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"couponId\",\n                  label: \"优惠券ID\",\n                  \"min-width\": \"80\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"name\",\n                  label: \"优惠券名称\",\n                  \"min-width\": \"150\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"nickname\",\n                  label: \"领取人\",\n                  \"min-width\": \"130\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { prop: \"money\", label: \"面值\", \"min-width\": \"100\" },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"minPrice\",\n                  label: \"最低消费额\",\n                  \"min-width\": \"120\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"startTime\",\n                  label: \"开始使用时间\",\n                  \"min-width\": \"150\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"endTime\",\n                  label: \"结束使用时间\",\n                  \"min-width\": \"150\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { label: \"获取方式\", \"min-width\": \"150\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\"span\", [\n                          _vm._v(_vm._s(_vm._f(\"failFilter\")(scope.row.type))),\n                        ]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"is_fail\",\n                  label: \"是否可用\",\n                  \"min-width\": \"100\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        scope.row.status === 0\n                          ? _c(\"i\", {\n                              staticClass: \"el-icon-check\",\n                              staticStyle: {\n                                \"font-size\": \"14px\",\n                                color: \"#0092DC\",\n                              },\n                            })\n                          : _c(\"i\", {\n                              staticClass: \"el-icon-close\",\n                              staticStyle: {\n                                \"font-size\": \"14px\",\n                                color: \"#ed5565\",\n                              },\n                            }),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { label: \"使用状态\", \"min-width\": \"100\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\"span\", [\n                          _vm._v(\n                            _vm._s(_vm._f(\"statusFilter\")(scope.row.status))\n                          ),\n                        ]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"div\",\n            { staticClass: \"block\" },\n            [\n              _c(\"el-pagination\", {\n                attrs: {\n                  \"page-sizes\": [20, 40, 60, 80],\n                  \"page-size\": _vm.tableFromIssue.limit,\n                  \"current-page\": _vm.tableFromIssue.page,\n                  layout: \"total, sizes, prev, pager, next, jumper\",\n                  total: _vm.issueData.total,\n                },\n                on: {\n                  \"size-change\": _vm.handleSizeChangeIssue,\n                  \"current-change\": _vm.pageChangeIssue,\n                },\n              }),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}