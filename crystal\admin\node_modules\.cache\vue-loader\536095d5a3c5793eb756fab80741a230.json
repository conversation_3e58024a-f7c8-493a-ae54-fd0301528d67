{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\marketing\\coupon\\list\\creatCoupon.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\marketing\\coupon\\list\\creatCoupon.vue", "mtime": 1753666157891}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { couponSaveApi, couponInfoApi } from '@/api/marketing'\nimport { categoryApi } from '@/api/store'\nimport {Debounce} from '@/utils/validate'\nexport default {\n  name: \"creatCoupon\",\n  data() {\n    return {\n      pickerOptions: {\n        disabledDate(time) {\n          // return time.getTime() < new Date().setTime(new Date().getTime() - 3600 * 1000 * 24); //不限制未来时间\n          return time.getTime() < Date.now() - 8.64e7 || time.getTime() > Date.now() + 600 * 8.64e7; //限制未来时间\n        }\n      },\n      loading: false,\n      threshold: false,\n      termTime: [],\n      props2: {\n        children: 'child',\n        label: 'name',\n        value: 'id',\n        checkStrictly: true,\n        emitPath: false\n      },\n      couponType: 0,\n      term: 'termday',\n      merCateList: [], // 商户分类筛选\n      ruleForm: {\n        useType: 1,\n        isFixedTime: false,\n        name: '',\n        money: 1,\n        minPrice: 1,\n        day: null,\n        isForever: false,\n        primaryKey: '',\n        type: 2,\n        isLimited: false,\n        useStartTime: '', // 使用\n        useEndTime: '', // 结束\n        receiveStartTime: '', //领取\n        receiveEndTime: '',\n        sort: 0,\n        total: 1,\n        status: false,\n        checked: []\n      },\n      isForeverTime: [],\n      rules: {\n        name: [\n          { required: true, message: '请输入优惠券名称', trigger: 'blur' }\n        ],\n        day: [\n          { required: true, message: '请输入使用有效期限（天）', trigger: 'blur' }\n        ],\n        money: [\n          { required: true, message: '请输入优惠券面值', trigger: 'blur' }\n        ],\n        primaryKey: [\n          { required: true, message: '请选择品类', trigger: 'change' }\n        ],\n        checked:  [\n          { required: true, message: '请至少选择一个商品', trigger: 'change', type: 'array' }\n        ],\n        isForeverTime: [\n          { required: true, message: '请选择领取时间', trigger: 'change', type: 'array' }\n        ],\n        total: [\n          { required: true, message: '请输入发布数量', trigger: 'blur' }\n        ],\n        minPrice: [\n          { required: true, message: '请输入最低消费', trigger: 'blur' }\n        ]\n      }\n    }\n  },\n  mounted() {\n    this.getCategorySelect()\n    if( this.$route.params.id ) this.getInfo()\n  },\n  methods: {\n    handleTimestamp(){\n\n    },\n    // 商品分类；\n    getCategorySelect() {\n      categoryApi({ status: -1, type: 1 }).then(res => {\n        this.merCateList = res\n        this.merCateList.map(item => {\n          this.$set(item , 'disabled', true)\n        })\n      })\n    },\n    getInfo(){\n      this.loading = true\n      couponInfoApi({id: this.$route.params.id}).then(res => {\n        const info = res.coupon\n        this.ruleForm = {\n          useType: info.useType,\n          isFixedTime: info.isFixedTime,\n          isForever: info.isForever,\n          name: info.name,\n          money: info.money,\n          minPrice: info.minPrice,\n          day: info.day,\n          type: info.type,\n          isLimited: info.isLimited,\n          sort: info.sort,\n          total: info.total,\n          status: info.status,\n          primaryKey: Number(info.primaryKey),\n          checked: res.product || []\n        }\n        info.minPrice == 0 ? this.threshold = false : this.threshold = true\n        info.isForever ? this.isForeverTime = [info.receiveStartTime, info.receiveEndTime] : this.isForeverTime = []\n        info.isFixedTime && info.useStartTime && info.useEndTime ? this.termTime = [info.useStartTime, info.useEndTime] : this.termTime = []\n        this.loading = false\n      }).catch(res => {\n        this.loading = false\n        this.$message.error(res.message)\n      })\n    },\n    handleRemove (i) {\n      this.ruleForm.checked.splice(i, 1)\n    },\n    changeGood(){\n      const _this = this\n      this.$modalGoodList(function(row) {\n        _this.ruleForm.checked = row\n      },'many',_this.ruleForm.checked)\n    },\n    submitForm:Debounce(function(formName) {\n     if( (this.ruleForm.isFixedTime && !this.termTime) || this.ruleForm.isFixedTime && !this.termTime.length) return this.$message.warning(\"请选择使用有效期限\")\n     if( (this.ruleForm.isForever && !this.isForeverTime) || (this.ruleForm.isForever && !this.isForeverTime.length)) return this.$message.warning(\"请选择请选择领取时间\")\n      if( this.ruleForm.useType === 2 ) this.ruleForm.primaryKey = this.ruleForm.checked.map(item => {return item.id}).join(',')\n      if( this.ruleForm.useType === 1 ) this.ruleForm.primaryKey = ''\n      if( !this.threshold ) this.ruleForm.minPrice = 0\n      if( !this.ruleForm.isLimited ) this.ruleForm.total = 0\n      this.ruleForm.isFixedTime && this.termTime.length ? (this.ruleForm.useStartTime = this.termTime[0], this.ruleForm.day = null) : this.ruleForm.useStartTime = ''\n      this.ruleForm.isFixedTime && this.termTime.length ? (this.ruleForm.useEndTime = this.termTime[1], this.ruleForm.day = null) : this.ruleForm.useEndTime = ''\n      this.ruleForm.isForever && this.isForeverTime.length ? this.ruleForm.receiveStartTime = this.isForeverTime[0] : this.ruleForm.receiveStartTime = ''\n      this.ruleForm.isForever && this.isForeverTime.length ? this.ruleForm.receiveEndTime = this.isForeverTime[1] : this.ruleForm.receiveEndTime = ''\n      this.$refs[formName].validate((valid) => {\n        if (valid) {\n         this.loading = true\n          couponSaveApi(this.ruleForm).then(() => {\n            this.$message.success(\"新增成功\")\n            this.loading = false\n            setTimeout(() => {\n              this.$router.push({ path: `/marketing/coupon/list` })\n            }, 200);\n          }).catch(() => {\n            this.loading = false\n          })\n        } else {\n          this.loading = false\n          return false;\n        }\n      });\n    }),\n    \n  }\n}\n", null]}