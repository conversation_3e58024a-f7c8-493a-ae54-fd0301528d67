{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/news_list/index.vue?8df1", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/news_list/index.vue?c69f", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/news_list/index.vue?29dc", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/news_list/index.vue?46de", "uni-app:///pages/news_list/index.vue", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/news_list/index.vue?a9ed", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/news_list/index.vue?3bf3"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "home", "data", "imgUrls", "articleList", "indicatorDots", "circular", "autoplay", "interval", "duration", "navList", "active", "page", "limit", "status", "scrollLeft", "onShow", "onReachBottom", "methods", "getArticleHot", "that", "getArticleBanner", "getCidArticle", "articleListNew", "getArticleCate", "list", "id", "name", "tabSelect"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACgM;AAChM,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrBA;AAAA;AAAA;AAAA;AAAmvB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACiDvwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAOA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACA;AACA;AACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EACA;AACA;AACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;QACAC;MACA;IACA;IACAC;MACA;MACA;QACAD;MACA;IACA;IACAE;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAV;QACAC;MACA;QACA;QACA;QACAU;QACAH;QACAA;QACAA;QACAA;MACA;IACA;IACAI;MACA;MACA;QACA;QACAC;UAAAC;UAAAC;QAAA;QACAP;MACA;IACA;IACAQ;MACA;MACA;MACA;MACA,gDACA;QACA;QACA;QACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACrJA;AAAA;AAAA;AAAA;AAAk5C,CAAgB,4tCAAG,EAAC,C;;;;;;;;;;;ACAt6C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/news_list/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/news_list/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=e8bc39b8&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/news_list/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=e8bc39b8&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.imgUrls.length\n  var g1 = _vm.navList.length\n  var g2 = _vm.articleList.length == 0 && (_vm.page != 1 || _vm.active == 0)\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view class='newsList'>\r\n\t\t\t<view class='swiper' v-if=\"imgUrls.length > 0\">\r\n\t\t\t\t<swiper indicator-dots=\"true\" :autoplay=\"autoplay\" :circular=\"circular\" :interval=\"interval\" :duration=\"duration\"\r\n\t\t\t\t indicator-color=\"rgba(102,102,102,0.3)\" indicator-active-color=\"#666\">\r\n\t\t\t\t\t<block v-for=\"(item,index) in imgUrls\" :key=\"index\">\r\n\t\t\t\t\t\t<swiper-item>\r\n\t\t\t\t\t\t\t<navigator :url=\"'/pages/news_details/index?id='+item.id\">\r\n\t\t\t\t\t\t\t\t<image :src=\"item.imageInput\" class=\"slide-image\" />\r\n\t\t\t\t\t\t\t</navigator>\r\n\t\t\t\t\t\t</swiper-item>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</swiper>\r\n\t\t\t</view>\r\n\t\t\t<view class='nav' v-if=\"navList.length > 0\">\r\n\t\t\t\t<scroll-view class=\"scroll-view_x\" scroll-x scroll-with-animation :scroll-left=\"scrollLeft\" style=\"width:auto;overflow:hidden;\">\r\n\t\t\t\t\t<block v-for=\"(item,index) in navList\" :key=\"index\">\r\n\t\t\t\t\t\t<view class='item borRadius14' :class='active==item.id?\"on\":\"\"' @click='tabSelect(item.id, index)'>\r\n\t\t\t\t\t\t\t<view>{{item.name}}</view>\r\n\t\t\t\t\t\t\t<view class='line bg-color' v-if=\"active==item.id\"></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</scroll-view>\r\n\t\t\t</view>\r\n\t\t\t<view class='list'>\r\n\t\t\t\t<block v-for=\"(item,index) in articleList\" :key=\"index\">\r\n\t\t\t\t\t<navigator :url='\"/pages/news_details/index?id=\"+item.id' hover-class='none' class='item acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t<view class='text acea-row row-column-between'>\r\n\t\t\t\t\t\t\t<view class='name line2'>{{item.title}}</view>\r\n\t\t\t\t\t\t\t<view>{{item.createTime}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='pictrue'>\r\n\t\t\t\t\t\t\t<image :src='item.imageInput'></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</navigator>\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class='noCommodity' v-if=\"articleList.length == 0 && (page != 1 || active== 0)\">\r\n\t\t\t<view class='pictrue'>\r\n\t\t\t\t<image src='../../static/images/noNews.png'></image>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<home></home>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tgetArticleCategoryList,\r\n\t\tgetArticleList,\r\n\t\tgetArticleHotList,\r\n\t\tgetArticleBannerList\r\n\t} from '@/api/api.js';\r\n\timport home from '@/components/home';\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\thome\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\timgUrls: [],\r\n\t\t\t\tarticleList: [],\r\n\t\t\t\tindicatorDots: false,\r\n\t\t\t\tcircular: true,\r\n\t\t\t\tautoplay: true,\r\n\t\t\t\tinterval: 3000,\r\n\t\t\t\tduration: 500,\r\n\t\t\t\tnavList: [],\r\n\t\t\t\tactive: 0,\r\n\t\t\t\tpage: 1,\r\n\t\t\t\tlimit: 8,\r\n\t\t\t\tstatus: false,\r\n\t\t\t\tscrollLeft: 0\r\n\t\t\t};\r\n\t\t},\r\n\t\t/**\r\n\t\t * 生命周期函数--监听页面显示\r\n\t\t */\r\n\t\tonShow: function() {\r\n\t\t\tthis.getArticleHot();\r\n\t\t\tthis.getArticleBanner();\r\n\t\t\tthis.getArticleCate();\r\n\t\t\tthis.status = false;\r\n\t\t\tthis.page = 1;\r\n\t\t\tthis.articleList = [];\r\n\t\t\tthis.getCidArticle();\r\n\t\t},\r\n\t\t  /**\r\n\t\t   * 页面上拉触底事件的处理函数\r\n\t\t   */\r\n\t\t  onReachBottom: function () {\r\n\t\t    this.getCidArticle();\r\n\t\t  },\r\n\t\tmethods: {\r\n\t\t\tgetArticleHot: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tgetArticleHotList().then(res => {\r\n\t\t\t\t\tthat.$set(that, 'articleList', res.data.list);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgetArticleBanner: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tgetArticleBannerList().then(res => {\r\n\t\t\t\t\tthat.imgUrls = res.data.list;\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgetCidArticle: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif (that.active == 0) return;\r\n\t\t\t\tlet limit = that.limit;\r\n\t\t\t\tlet page = that.page;\r\n\t\t\t\tlet articleList = that.articleList;\r\n\t\t\t\tif (that.status) return;\r\n\t\t\t\tgetArticleList(that.active, {\r\n\t\t\t\t\tpage: page,\r\n\t\t\t\t\tlimit: limit\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tlet articleListNew = [];\r\n\t\t\t\t\tlet len = res.data.list.length;\r\n\t\t\t\t\tarticleListNew = articleList.concat(res.data.list);\r\n\t\t\t\t\tthat.page++;\r\n\t\t\t\t\tthat.$set(that, 'articleList', articleListNew);\r\n\t\t\t\t\tthat.status = limit > len;\r\n\t\t\t\t\tthat.page = that.page;\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgetArticleCate: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tgetArticleCategoryList().then(res => {\r\n\t\t\t\t\tlet list = res.data.list;\r\n\t\t\t\t\tlist.unshift({id:0,name:'热门'});\r\n\t\t\t\t\tthat.$set(that, 'navList', list);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\ttabSelect(active,e) {\r\n\t\t\t\tthis.active = active;\r\n\t\t\t\tthis.scrollLeft =  e * 60;\r\n\t\t\t\t// this.scrollLeft = (active - 1) * 50;\r\n\t\t\t\tif (this.active == 0) this.getArticleHot();\r\n\t\t\t\telse {\r\n\t\t\t\t\tthis.$set(this, 'articleList', []);\r\n\t\t\t\t\tthis.page = 1;\r\n\t\t\t\t\tthis.status = false;\r\n\t\t\t\t\tthis.getCidArticle();\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\tpage {\r\n\t\tbackground-color: #fff !important;\r\n\t}\r\n\r\n\t.newsList .swiper {\r\n\t\twidth: 100%;\r\n\t\tposition: relative;\r\n\t\tbox-sizing: border-box;\r\n\t\tpadding: 0 30rpx;\r\n\t}\r\n\r\n\t.newsList .swiper swiper {\r\n\t\twidth: 100%;\r\n\t\theight: 365rpx;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.newsList .swiper .slide-image {\r\n\t\twidth: 100%;\r\n\t\theight: 335rpx;\r\n\t\tborder-radius: 14rpx;\r\n\t}\r\n\t// #ifdef MP-WEIXIN\r\n\t.newsList .swiper .wx-swiper-dot {\r\n\t\twidth: 12rpx !important;\r\n\t\theight: 12rpx !important;\r\n\t\tborder-radius: 0;\r\n\t\ttransform: rotate(-45deg);\r\n\t\ttransform-origin: 0 100%;\r\n\t}\r\n\t\r\n\t.newsList .swiper .wx-swiper-dot~.wx-swiper-dot {\r\n\t\tmargin-left: 5rpx;\r\n\t}\r\n\r\n\t.newsList .swiper .wx-swiper-dots.wx-swiper-dots-horizontal {\r\n\t\tmargin-bottom: -15rpx;\r\n\t}\r\n\t// #endif\r\n\t.newsList .swiper .uni-swiper-dot {\r\n\t\t\twidth: 12rpx !important;\r\n\t\t\theight: 12rpx !important;\r\n\t\t\tborder-radius: 0;\r\n\t\t\ttransform: rotate(-45deg);\r\n\t\t\ttransform-origin: 0 100%;\r\n\t}\r\n\t\r\n\t.newsList .swiper .uni-swiper-dot~.uni-swiper-dot {\r\n\t\tmargin-left: 5rpx;\r\n\t}\r\n\t\r\n\t.newsList .swiper .uni-swiper-dots.uni-swiper-dots-horizontal {\r\n\t\tmargin-bottom: -15rpx;\r\n\t}\r\n\t.newsList .nav {\r\n\t\tpadding: 0 24rpx;\r\n\t\twidth: 100%;\r\n\t\twhite-space: nowrap;\r\n\t\tbox-sizing: border-box;\r\n\t\tmargin-top: 43rpx;\r\n\t}\r\n\r\n\t.newsList .nav .item {\r\n\t\tdisplay: inline-block;\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.newsList .nav .item.on {\r\n\t\tcolor: #282828;\r\n\t}\r\n\r\n\t.newsList .nav .item~.item {\r\n\t\tmargin-left: 46rpx;\r\n\t}\r\n\r\n\t.newsList .nav .item .line {\r\n\t\twidth: 24rpx;\r\n\t\theight: 4rpx;\r\n\t\tborder-radius: 2rpx;\r\n\t\tmargin: 10rpx auto 0 auto;\r\n\t}\r\n\r\n\t.newsList .list .item {\r\n\t\tmargin: 0 24rpx;\r\n\t\tborder-bottom: 1rpx solid #f0f0f0;\r\n\t\tpadding: 35rpx 0;\r\n\t}\r\n\r\n\t.newsList .list .item .pictrue {\r\n\t\twidth: 250rpx;\r\n\t\theight: 156rpx;\r\n\t}\r\n\r\n\t.newsList .list .item .pictrue image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 14rpx;\r\n\t}\r\n\r\n\t.newsList .list .item .text {\r\n\t\twidth: 420rpx;\r\n\t\theight: 156rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.newsList .list .item .text .name {\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #282828;\r\n\t}\r\n\r\n\t.newsList .list .item .picList .pictrue {\r\n\t\twidth: 335rpx;\r\n\t\theight: 210rpx;\r\n\t\tmargin-top: 30rpx;\r\n\t}\r\n\r\n\t.newsList .list .item .picList.on .pictrue {\r\n\t\twidth: 217rpx;\r\n\t\theight: 136rpx;\r\n\t}\r\n\r\n\t.newsList .list .item .picList .pictrue image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 6rpx;\r\n\t}\r\n\r\n\t.newsList .list .item .time {\r\n\t\ttext-align: right;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999;\r\n\t\tmargin-top: 22rpx;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363904125\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}