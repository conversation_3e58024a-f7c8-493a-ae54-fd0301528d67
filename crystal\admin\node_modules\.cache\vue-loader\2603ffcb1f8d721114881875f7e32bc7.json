{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\FormGenerator\\index\\FormDrawer.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\FormGenerator\\index\\FormDrawer.vue", "mtime": 1753666157769}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport { parse } from '@babel/parser'\r\nimport ClipboardJS from 'clipboard'\r\nimport { saveAs } from 'file-saver'\r\nimport {\r\n  makeUpHtml, vueTemplate, vueScript, cssStyle\r\n} from '@/components/FormGenerator/components/generator/html'\r\nimport { makeUpJs } from '@/components/FormGenerator/components/generator/js'\r\nimport { makeUpCss } from '@/components/FormGenerator/components/generator/css'\r\nimport { exportDefault, beautifierConf, titleCase } from '../utils/index'\r\nimport ResourceDialog from './ResourceDialog'\r\nimport loadMonaco from '../utils/loadMonaco'\r\nimport loadBeautifier from '../utils/loadBeautifier'\r\n\r\nconst editorObj = {\r\n  html: null,\r\n  js: null,\r\n  css: null\r\n}\r\nconst mode = {\r\n  html: 'html',\r\n  js: 'javascript',\r\n  css: 'css'\r\n}\r\nlet beautifier\r\nlet monaco\r\n\r\nexport default {\r\n  components: { ResourceDialog },\r\n  props: ['formData', 'generateConf'],\r\n  data() {\r\n    return {\r\n      activeTab: 'html',\r\n      htmlCode: '',\r\n      jsCode: '',\r\n      cssCode: '',\r\n      codeFrame: '',\r\n      isIframeLoaded: false,\r\n      isInitcode: false, // 保证open后两个异步只执行一次runcode\r\n      isRefreshCode: false, // 每次打开都需要重新刷新代码\r\n      resourceVisible: false,\r\n      scripts: [],\r\n      links: [],\r\n      monaco: null\r\n    }\r\n  },\r\n  computed: {\r\n    resources() {\r\n      return this.scripts.concat(this.links)\r\n    }\r\n  },\r\n  watch: {},\r\n  created() {\r\n  },\r\n  mounted() {\r\n    window.addEventListener('keydown', this.preventDefaultSave)\r\n    const clipboard = new ClipboardJS('.copy-btn', {\r\n      text: trigger => {\r\n        const codeStr = this.generateCode()\r\n        this.$notify({\r\n          title: '成功',\r\n          message: '代码已复制到剪切板，可粘贴。',\r\n          type: 'success'\r\n        })\r\n        return codeStr\r\n      }\r\n    })\r\n    clipboard.on('error', e => {\r\n      this.$message.error('代码复制失败')\r\n    })\r\n  },\r\n  beforeDestroy() {\r\n    window.removeEventListener('keydown', this.preventDefaultSave)\r\n  },\r\n  methods: {\r\n    preventDefaultSave(e) {\r\n      if (e.key === 's' && (e.metaKey || e.ctrlKey)) {\r\n        e.preventDefault()\r\n      }\r\n    },\r\n    onOpen() {\r\n      const { type } = this.generateConf\r\n      this.htmlCode = makeUpHtml(this.formData, type)\r\n      this.jsCode = makeUpJs(this.formData, type)\r\n      this.cssCode = makeUpCss(this.formData)\r\n\r\n      loadBeautifier(btf => {\r\n        beautifier = btf\r\n        this.htmlCode = beautifier.html(this.htmlCode, beautifierConf.html)\r\n        this.jsCode = beautifier.js(this.jsCode, beautifierConf.js)\r\n        this.cssCode = beautifier.css(this.cssCode, beautifierConf.html)\r\n\r\n        loadMonaco(val => {\r\n          monaco = val\r\n          this.setEditorValue('editorHtml', 'html', this.htmlCode)\r\n          this.setEditorValue('editorJs', 'js', this.jsCode)\r\n          this.setEditorValue('editorCss', 'css', this.cssCode)\r\n          if (!this.isInitcode) {\r\n            this.isRefreshCode = true\r\n            this.isIframeLoaded && (this.isInitcode = true) && this.runCode()\r\n          }\r\n        })\r\n      })\r\n    },\r\n    onClose() {\r\n      this.isInitcode = false\r\n      this.isRefreshCode = false\r\n      this.isIframeLoaded = false\r\n    },\r\n    iframeLoad() {\r\n      if (!this.isInitcode) {\r\n        this.isIframeLoaded = true\r\n        this.isRefreshCode && (this.isInitcode = true) && this.runCode()\r\n      }\r\n    },\r\n    setEditorValue(id, type, codeStr) {\r\n      if (editorObj[type]) {\r\n        editorObj[type].setValue(codeStr)\r\n      } else {\r\n        editorObj[type] = monaco.editor.create(document.getElementById(id), {\r\n          value: codeStr,\r\n          theme: 'vs-dark',\r\n          language: mode[type],\r\n          automaticLayout: true\r\n        })\r\n      }\r\n      // ctrl + s 刷新\r\n      editorObj[type].onKeyDown(e => {\r\n        if (e.keyCode === 49 && (e.metaKey || e.ctrlKey)) {\r\n          this.runCode()\r\n        }\r\n      })\r\n    },\r\n    runCode() {\r\n      const jsCodeStr = editorObj.js.getValue()\r\n      try {\r\n        const ast = parse(jsCodeStr, { sourceType: 'module' })\r\n        const astBody = ast.program.body\r\n        if (astBody.length > 1) {\r\n          this.$confirm(\r\n            'js格式不能识别，仅支持修改export default的对象内容',\r\n            '提示',\r\n            {\r\n              type: 'warning'\r\n            }\r\n          )\r\n          return\r\n        }\r\n        if (astBody[0].type === 'ExportDefaultDeclaration') {\r\n          const postData = {\r\n            type: 'refreshFrame',\r\n            data: {\r\n              generateConf: this.generateConf,\r\n              html: editorObj.html.getValue(),\r\n              js: jsCodeStr.replace(exportDefault, ''),\r\n              css: editorObj.css.getValue(),\r\n              scripts: this.scripts,\r\n              links: this.links\r\n            }\r\n          }\r\n\r\n          this.$refs.previewPage.contentWindow.postMessage(\r\n            postData,\r\n            location.origin\r\n          )\r\n        } else {\r\n          this.$message.error('请使用export default')\r\n        }\r\n      } catch (err) {\r\n        this.$message.error(`js错误：${err}`)\r\n      }\r\n    },\r\n    generateCode() {\r\n      const html = vueTemplate(editorObj.html.getValue())\r\n      const script = vueScript(editorObj.js.getValue())\r\n      const css = cssStyle(editorObj.css.getValue())\r\n      return beautifier.html(html + script + css, beautifierConf.html)\r\n    },\r\n    exportFile() {\r\n      this.$prompt('文件名:', '导出文件', {\r\n        inputValue: `${+new Date()}.vue`,\r\n        closeOnClickModal: false,\r\n        inputPlaceholder: '请输入文件名'\r\n      }).then(({ value }) => {\r\n        if (!value) value = `${+new Date()}.vue`\r\n        const codeStr = this.generateCode()\r\n        const blob = new Blob([codeStr], { type: 'text/plain;charset=utf-8' })\r\n        saveAs(blob, value)\r\n      })\r\n    },\r\n    showResource() {\r\n      this.resourceVisible = true\r\n    },\r\n    setResource(arr) {\r\n      const scripts = []; const\r\n        links = []\r\n      if (Array.isArray(arr)) {\r\n        arr.forEach(item => {\r\n          if (item.endsWith('.css')) {\r\n            links.push(item)\r\n          } else {\r\n            scripts.push(item)\r\n          }\r\n        })\r\n        this.scripts = scripts\r\n        this.links = links\r\n      } else {\r\n        this.scripts = []\r\n        this.links = []\r\n      }\r\n    }\r\n  }\r\n}\r\n", null]}