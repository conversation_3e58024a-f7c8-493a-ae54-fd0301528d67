{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/goods_seckill/index.vue?69e1", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/goods_seckill/index.vue?1bf3", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/goods_seckill/index.vue?55db", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/goods_seckill/index.vue?97e8", "uni-app:///pages/activity/goods_seckill/index.vue", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/goods_seckill/index.vue?66a8", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/goods_seckill/index.vue?8789", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/goods_seckill/index.vue?71ae", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/goods_seckill/index.vue?be49"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "home", "data", "circular", "autoplay", "interval", "topImage", "seckillList", "timeList", "active", "scrollLeft", "onLoad", "methods", "goBack", "uni", "getSeckillConfig", "res", "item", "that", "getSeckillList", "page", "limit", "settimeList", "clearInterval", "goDetails", "url", "onReachBottom"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACa;AACyB;;;AAG1F;AACmM;AACnM,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9BA;AAAA;AAAA;AAAA;AAAkwB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACsEtxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA;AAAA,eACA;EACAC;IACAC;EACA;EACAC;IAAA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IAAA,mDACA,kDACA,yDACA,8DACA,8DACA,mDACA,iDACA,mDACA,uDACA,2DACA,wDACA,uDACA,mDACA;EAEA;EACAC;IACA;IACA;IAIA;EACA;EACAC;IACAC;MACAC;IACA;IACAC;MACA;MACA;QACAC;UACAC;QACA;QACAC;QACAA;QACAA;QACAA;QACAA;QACAA;MAEA;IACA;IACAC;MACA;MACA;QACAC;QACAC;MACA;MACA;MACA;MACA;MACA;QACA;QACA;QACAH;QACAA,yDACAA;QACAA;QACAA;MACA;QACAA;MACA;IACA;IACAI;MACA;MACA;MACA;QACAC;QACAL;MACA;MACAA,mBACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACA;MACAA;IACA;IACAM;MACAV;QACAW;MACA;IACA;EACA;EACA;AACA;AACA;EACAC;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvLA;AAAA;AAAA;AAAA;AAAqlC,CAAgB,s8BAAG,EAAC,C;;;;;;;;;;;ACAzmC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAq8C,CAAgB,ovCAAG,EAAC,C;;;;;;;;;;;ACAz9C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/activity/goods_seckill/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/activity/goods_seckill/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=4f2faf7c&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./index.vue?vue&type=style&index=1&id=4f2faf7c&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4f2faf7c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/activity/goods_seckill/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=4f2faf7c&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.dataList.length\n  var l0 = _vm.__map(_vm.dataList, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var g1 = item.time.split(\",\")\n    return {\n      $orig: $orig,\n      g1: g1,\n    }\n  })\n  var g2 = _vm.seckillList.length\n  var g3 = _vm.seckillList.length == 0 && (_vm.page != 1 || _vm.active == 0)\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        g2: g2,\n        g3: g3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<div>\r\n\t\t<view class='flash-sale'>\r\n\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t<view class='iconfont icon-xiang<PERSON>o' @tap='goBack' :style=\"'top:'+ (navH/2) +'rpx'\" v-if=\"returnShow\"></view>\r\n\t\t\t<!-- #endif -->\r\n\t\t\t<view class=\"saleBox\"></view>\r\n\t\t\t<view class=\"header\" v-if=\"dataList.length\">\r\n\t\t\t\t<swiper indicator-dots=\"true\" autoplay=\"true\" :circular=\"circular\" interval=\"3000\" duration=\"1500\"\r\n\t\t\t\t\tindicator-color=\"rgba(255,255,255,0.6)\" indicator-active-color=\"#fff\">\r\n\t\t\t\t\t<block v-for=\"(items,index) in dataList[active].slide\" :key=\"index\">\r\n\t\t\t\t\t\t<swiper-item class=\"borRadius14\">\r\n\t\t\t\t\t\t\t<image :src=\"items.sattDir\" class=\"slide-image borRadius14\" lazy-load></image>\r\n\t\t\t\t\t\t</swiper-item>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</swiper>\r\n\r\n\t\t\t</view>\r\n\t\t\t<view class=\"seckillList acea-row row-between-wrapper\">\r\n\t\t\t\t<view class=\"priceTag\">\r\n\t\t\t\t\t<image src=\"/static/images/priceTag.png\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='timeLsit'>\r\n\t\t\t\t\t<scroll-view class=\"scroll-view_x\" scroll-x scroll-with-animation :scroll-left=\"scrollLeft\"\r\n\t\t\t\t\t\tstyle=\"width:auto;overflow:hidden;\">\r\n\t\t\t\t\t\t<block v-for=\"(item,index) in dataList\" :key='index'>\r\n\t\t\t\t\t\t\t<view @tap='settimeList(item,index)' class='item' :class=\"active == index?'on':''\">\r\n\t\t\t\t\t\t\t\t<view class='time'>{{item.time.split(',')[0]}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"state\">{{item.statusName}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</scroll-view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class='list pad30' v-if='seckillList.length>0'>\r\n\t\t\t\t<block v-for=\"(item,index) in seckillList\" :key='index'>\r\n\t\t\t\t\t<view class='item acea-row row-between-wrapper' @tap='goDetails(item)'>\r\n\t\t\t\t\t\t<view class='pictrue'>\r\n\t\t\t\t\t\t\t<image :src='item.image'></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='text acea-row row-column-around'>\r\n\t\t\t\t\t\t\t<view class='name line1'>{{item.title}}</view>\r\n\t\t\t\t\t\t\t<view class='money'><text class=\"font-color\">￥</text>\r\n\t\t\t\t\t\t\t\t<text class='num font-color'>{{item.price}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"y_money\">￥{{item.otPrice}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"limit\">限量 <text class=\"limitPrice\">{{item.quota}} {{item.unitName}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"progress\">\r\n\t\t\t\t\t\t\t\t<view class='bg-reds' :style=\"'width:'+item.percent+'%;'\"></view>\r\n\t\t\t\t\t\t\t\t<view class='piece'>已抢{{item.percent}}%</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='grab bg-color' v-if=\"status == 2\">马上抢</view>\r\n\t\t\t\t\t\t<view class='grab bg-color' v-else-if=\"status == 1\">未开始</view>\r\n\t\t\t\t\t\t<view class='grab bg-color-hui' v-else>已结束</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class='noCommodity' v-if=\"seckillList.length == 0 && (page != 1 || active== 0)\">\r\n\t\t\t<view class='pictrue'>\r\n\t\t\t\t<image src='../../../static/images/noShopper.png'></image>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<home></home>\r\n\t</div>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tgetSeckillHeaderApi,\r\n\t\tgetSeckillList\r\n\t} from '../../../api/activity.js';\r\n\timport home from '@/components/home/<USER>';\r\n\tlet app = getApp();\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\thome\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tcircular: true,\r\n\t\t\t\tautoplay: true,\r\n\t\t\t\tinterval: 500,\r\n\t\t\t\ttopImage: '',\r\n\t\t\t\tseckillList: [],\r\n\t\t\t\ttimeList: [],\r\n\t\t\t\tactive: 0,\r\n\t\t\t\tscrollLeft: 0,\r\n\t\t\t\tinterval: 0,\r\n\t\t\t\tstatus: 1,\r\n\t\t\t\tcountDownHour: \"00\",\r\n\t\t\t\tcountDownMinute: \"00\",\r\n\t\t\t\tcountDownSecond: \"00\",\r\n\t\t\t\tpage: 1,\r\n\t\t\t\tlimit: 4,\r\n\t\t\t\tloading: false,\r\n\t\t\t\tloadend: false,\r\n\t\t\t\tpageloading: false,\r\n\t\t\t\tdataList: [],\r\n\t\t\t\treturnShow: true,\r\n\t\t\t\tnavH: ''\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tvar pages = getCurrentPages();\r\n\t\t\tthis.returnShow = pages.length===1?false:true;\r\n\t\t\t// #ifdef H5\r\n\t\t\tthis.navH = app.globalData.navHeight-18;\r\n\t\t\t// #endif\r\n\t\t\tthis.getSeckillConfig();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgoBack: function() {\r\n\t\t\t\tuni.navigateBack();\r\n\t\t\t},\r\n\t\t\tgetSeckillConfig: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tgetSeckillHeaderApi().then(res => {\r\n\t\t\t\t\tres.data.map(item => {\r\n\t\t\t\t\t\titem.slide = JSON.parse(item.slide)\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthat.dataList = res.data;\r\n\t\t\t\t\tthat.getSeckillList();\r\n\t\t\t\t\tthat.seckillList = [];\r\n\t\t\t\t\tthat.page = 1;\r\n\t\t\t\t\tthat.status = that.dataList[that.active].status;\r\n\t\t\t\t\tthat.getSeckillList();\r\n\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgetSeckillList: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar data = {\r\n\t\t\t\t\tpage: that.page,\r\n\t\t\t\t\tlimit: that.limit\r\n\t\t\t\t};\r\n\t\t\t\tif (that.loadend) return;\r\n\t\t\t\tif (that.pageloading) return;\r\n\t\t\t\tthis.pageloading = true\r\n\t\t\t\tgetSeckillList(that.dataList[that.active].id, data).then(res => {\r\n\t\t\t\t\tvar seckillList = res.data.list;\r\n\t\t\t\t\tvar loadend = seckillList.length < that.limit;\r\n\t\t\t\t\tthat.page++;\r\n\t\t\t\t\tthat.seckillList = that.seckillList.concat(seckillList),\r\n\t\t\t\t\t\tthat.page = that.page;\r\n\t\t\t\t\tthat.pageloading = false;\r\n\t\t\t\t\tthat.loadend = loadend;\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tthat.pageloading = false\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tsettimeList: function(item, index) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tthis.active = index\r\n\t\t\t\tif (that.interval) {\r\n\t\t\t\t\tclearInterval(that.interval);\r\n\t\t\t\t\tthat.interval = null\r\n\t\t\t\t}\r\n\t\t\t\tthat.interval = 0,\r\n\t\t\t\t\tthat.countDownHour = \"00\";\r\n\t\t\t\tthat.countDownMinute = \"00\";\r\n\t\t\t\tthat.countDownSecond = \"00\";\r\n\t\t\t\tthat.status = that.dataList[that.active].status;\r\n\t\t\t\tthat.loadend = false;\r\n\t\t\t\tthat.page = 1;\r\n\t\t\t\tthat.seckillList = [];\r\n\t\t\t\t// wxh.time(e.currentTarget.dataset.stop, that);\r\n\t\t\t\tthat.getSeckillList();\r\n\t\t\t},\r\n\t\t\tgoDetails(item) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/activity/goods_seckill_details/index?id=' + item.id\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\t/**\r\n\t\t * 页面上拉触底事件的处理函数\r\n\t\t */\r\n\t\tonReachBottom: function() {\r\n\t\t\tthis.getSeckillList();\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\tpage {\r\n\t\tbackground-color: #F5F5F5 !important;\r\n\t}\r\n</style>\r\n<style scoped lang=\"scss\">\r\n\r\n   .icon-xiangzuo {\r\n\t\tfont-size: 40rpx;\r\n\t\tcolor: #fff;\r\n\t\tposition: fixed;\r\n\t\tleft: 30rpx;\r\n\t\tz-index: 99;\r\n\t\ttransform: translateY(-20%);\r\n\t}\r\n\t.flash-sale .header {\r\n\t\twidth: 710rpx;\r\n\t\theight: 330rpx;\r\n\t\tmargin: -276rpx auto 0 auto;\r\n\t\tborder-radius: 14rpx;\r\n\t\toverflow: hidden;\r\n\t\tswiper{\r\n\t\t\theight: 330rpx !important;\r\n\t\t\tborder-radius: 14rpx;\r\n\t\t\toverflow: hidden;\r\n\t\t}\r\n\t}\r\n\r\n\t.flash-sale .header image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 14rpx;\r\n\t\toverflow: hidden;\r\n\t\timg{\r\n\t\t\tborder-radius: 14rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.flash-sale .seckillList {\r\n\t\tpadding: 25rpx;\r\n\t}\r\n\r\n\t.flash-sale .seckillList .priceTag {\r\n\t\twidth: 75rpx;\r\n\t\theight: 70rpx;\r\n\t}\r\n\r\n\t.flash-sale .seckillList .priceTag image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.flash-sale .timeLsit {\r\n\t\twidth: 596rpx;\r\n\t\twhite-space: nowrap;\r\n\t}\r\n\r\n\t.flash-sale .timeLsit .item {\r\n\t\tdisplay: inline-block;\r\n\t\tfont-size: 20rpx;\r\n\t\tcolor: #666;\r\n\t\ttext-align: center;\r\n\t\tbox-sizing: border-box;\r\n\t\tmargin-right: 30rpx;\r\n\t\twidth: 130rpx;\r\n\t}\r\n\r\n\t.flash-sale .timeLsit .item .time {\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.flash-sale .timeLsit .item.on .time {\r\n\t\tcolor: $theme-color;\r\n\t}\r\n\r\n\t.flash-sale .timeLsit .item.on .state {\r\n\t\theight: 30rpx;\r\n\t\tline-height: 30rpx;\r\n\t\tborder-radius: 15rpx;\r\n\t\twidth: 128rpx;\r\n\t\t/* padding: 0 12rpx; */\r\n\t\tbackground: linear-gradient(90deg, rgba(252, 25, 75, 1) 0%, rgba(252, 60, 32, 1) 100%);\r\n\t\tcolor: #fff;\r\n\t}\r\n\r\n\t.flash-sale .countDown {\r\n\t\theight: 92rpx;\r\n\t\tborder-bottom: 1rpx solid #f0f0f0;\r\n\t\tmargin-top: -14rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #282828;\r\n\t}\r\n\r\n\t.flash-sale .countDown .num {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t\tbackground-color: #ffcfcb;\r\n\t\tpadding: 4rpx 7rpx;\r\n\t\tborder-radius: 3rpx;\r\n\t}\r\n\r\n\t.flash-sale .countDown .text {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #282828;\r\n\t\tmargin-right: 13rpx;\r\n\t}\r\n\r\n\t.flash-sale .list .item {\r\n\t\theight: 230rpx;\r\n\t\tposition: relative;\r\n\t\t/* width: 710rpx; */\r\n\t\tmargin: 0 auto 20rpx auto;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 14rpx;\r\n\t\tpadding: 25rpx 24rpx;\r\n\t}\r\n\r\n\t.flash-sale .list .item .pictrue {\r\n\t\twidth: 180rpx;\r\n\t\theight: 180rpx;\r\n\t\tborder-radius: 10rpx;\r\n\t\tbackground-color: #F5F5F5;\r\n\t}\r\n\r\n\t.flash-sale .list .item .pictrue image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 10rpx;\r\n\t}\r\n\r\n\t.flash-sale .list .item .text {\r\n\t\twidth: 440rpx;\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #333;\r\n\t\theight: 166rpx;\r\n\t}\r\n\r\n\t.flash-sale .list .item .text .name {\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.flash-sale .list .item .text .money {\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: $theme-color;\r\n\t}\r\n\r\n\t.flash-sale .list .item .text .money .num {\r\n\t\tfont-size: 40rpx;\r\n\t\tfont-weight: 500;\r\n\t\tfont-family: 'Guildford Pro';\r\n\t}\r\n\r\n\t.flash-sale .list .item .text .money .y_money {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999;\r\n\t\ttext-decoration-line: line-through;\r\n\t\tmargin-left: 15rpx;\r\n\t}\r\n\r\n\t.flash-sale .list .item .text .limit {\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #999;\r\n\t\tmargin-bottom: 5rpx;\r\n\t}\r\n\r\n\t.flash-sale .list .item .text .limit .limitPrice {\r\n\t\tmargin-left: 10rpx;\r\n\t}\r\n\r\n\t.flash-sale .list .item .text .progress {\r\n\t\toverflow: hidden;\r\n\t\tbackground-color: #EEEEEE;\r\n\t\twidth: 260rpx;\r\n\t\tborder-radius: 18rpx;\r\n\t\theight: 18rpx;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.flash-sale .list .item .text .progress .bg-reds {\r\n\t\twidth: 0;\r\n\t\theight: 100%;\r\n\t\ttransition: width 0.6s ease;\r\n\t\tbackground: linear-gradient(90deg, rgba(233, 51, 35, 1) 0%, rgba(255, 137, 51, 1) 100%);\r\n\t}\r\n\r\n\t.flash-sale .list .item .text .progress .piece {\r\n\t\tposition: absolute;\r\n\t\tleft: 8%;\r\n\t\ttransform: translate(0%, -50%);\r\n\t\ttop: 49%;\r\n\t\tfont-size: 16rpx;\r\n\t\tcolor: #FFB9B9;\r\n\t}\r\n\r\n\t.flash-sale .list .item .grab {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #fff;\r\n\t\twidth: 150rpx;\r\n\t\theight: 54rpx;\r\n\t\tborder-radius: 27rpx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 54rpx;\r\n\t\tposition: absolute;\r\n\t\tright: 30rpx;\r\n\t\tbottom: 30rpx;\r\n\t\tbackground: #bbbbbb;\r\n\t}\r\n\r\n\t.flash-sale .saleBox {\r\n\t\twidth: 100%;\r\n\t\theight: 298rpx;\r\n\t\t/* #ifdef MP */\r\n\t\theight: 300rpx;\r\n\t\t/* #endif */\r\n\t\tbackground: linear-gradient(180deg, #c9ab79 0%, #F5F5F5 100%);\r\n\t\tborder-radius: 0 0 50rpx 50rpx;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363899439\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=1&id=4f2faf7c&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=1&id=4f2faf7c&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363903268\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}