{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\marketing\\coupon\\record\\index.vue?vue&type=template&id=4307e9ca&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\marketing\\coupon\\record\\index.vue", "mtime": 1753666157892}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\n<div class=\"divBox\">\n  <el-card class=\"box-card\">\n    <div slot=\"header\" class=\"clearfix\">\n      <div class=\"filter-container\">\n        <el-form :inline=\"true\">\n          <el-form-item label=\"使用状态：\" class=\"mr10\">\n            <el-select v-model=\"tableFromIssue.status\" placeholder=\"请选择使用状态\" clearable  class=\"selWidth\" @change=\"seachList\">\n              <el-option label=\"已使用\" value=\"1\" />\n              <el-option label=\"未使用\" value=\"0\" />\n              <el-option label=\"已过期\" value=\"2\" />\n            </el-select>\n          </el-form-item>\n          <el-form-item label=\"领取人：\">\n            <el-select v-model=\"tableFromIssue.uid\" class=\"selWidth\" reserve-keyword remote filterable\n                       :remote-method=\"remoteMethod\" :loading=\"loading\" placeholder=\"请输入领取人\" clearable @change=\"seachList\">\n              <el-option\n                v-for=\"item in options\"\n                :key=\"item.uid\"\n                :label=\"item.nickname\"\n                :value=\"item.uid\">\n              </el-option>\n            </el-select>\n          </el-form-item>\n          <!--<el-form-item label=\"领取人：\" class=\"mr10\">-->\n            <!--<el-input v-model=\"tableFromIssue.uid\" placeholder=\"请输入领取人\" class=\"selWidth\">-->\n              <!--<el-button size=\"mini\" slot=\"append\" icon=\"el-icon-search\" @click=\"seachList\"/>-->\n            <!--</el-input>-->\n          <!--</el-form-item>-->\n          <el-form-item label=\"优惠劵：\" class=\"mr10\">\n            <el-input v-model=\"tableFromIssue.name\" placeholder=\"请输入优惠劵\" class=\"selWidth\" clearable>\n              <el-button slot=\"append\" icon=\"el-icon-search\"  @click=\"seachList\"/>\n            </el-input>\n          </el-form-item>\n        </el-form>\n      </div>\n    </div>\n    <el-table\n      v-loading=\"Loading\"\n      :data=\"issueData.data\"\n      style=\"width: 100%\"\n      :header-cell-style=\" {fontWeight:'bold'}\"\n    >\n      <el-table-column\n        prop=\"couponId\"\n        label=\"优惠券ID\"\n        min-width=\"80\"\n      />\n      <el-table-column\n        prop=\"name\"\n        label=\"优惠券名称\"\n        min-width=\"150\"\n      />\n      <el-table-column\n        prop=\"nickname\"\n        label=\"领取人\"\n        min-width=\"130\"\n      />\n      <el-table-column\n        prop=\"money\"\n        label=\"面值\"\n        min-width=\"100\"\n      />\n      <el-table-column\n        prop=\"minPrice\"\n        label=\"最低消费额\"\n        min-width=\"120\"\n      />\n      <el-table-column\n        prop=\"startTime\"\n        label=\"开始使用时间\"\n        min-width=\"150\"\n      />\n      <el-table-column\n        prop=\"endTime\"\n        label=\"结束使用时间\"\n        min-width=\"150\"\n      />\n      <el-table-column\n        label=\"获取方式\"\n        min-width=\"150\"\n      >\n        <template slot-scope=\"scope\">\n          <span>{{scope.row.type | failFilter}}</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        prop=\"is_fail\"\n        label=\"是否可用\"\n        min-width=\"100\"\n      >\n        <template slot-scope=\"scope\">\n          <i v-if=\"scope.row.status === 0\" class=\"el-icon-check\" style=\"font-size: 14px;color: #0092DC;\" />\n          <i v-else class=\"el-icon-close\" style=\"font-size: 14px;color: #ed5565;\" />\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"使用状态\"\n        min-width=\"100\"\n      >\n        <template slot-scope=\"scope\">\n          <span>{{scope.row.status | statusFilter}}</span>\n        </template>\n      </el-table-column>\n    </el-table>\n    <div class=\"block\">\n      <el-pagination\n        :page-sizes=\"[20, 40, 60, 80]\"\n        :page-size=\"tableFromIssue.limit\"\n        :current-page=\"tableFromIssue.page\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        :total=\"issueData.total\"\n        @size-change=\"handleSizeChangeIssue\"\n        @current-change=\"pageChangeIssue\"\n      />\n    </div>\n  </el-card>\n</div>\n", null]}