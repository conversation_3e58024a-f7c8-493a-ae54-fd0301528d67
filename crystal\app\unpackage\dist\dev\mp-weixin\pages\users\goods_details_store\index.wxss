
.geoPage {
	position: fixed;
	width: 100%;
	height: 100%;
	top: 0;
	z-index: 10000;
}
.storeBox {
	width: 100%;
	background-color: #fff;
	padding: 0 30rpx;
}
.storeBox-box {
	width: 100%;
	height: auto;
	display: flex;
	align-items: center;
	padding: 23rpx 0;
	justify-content: space-between;
	border-bottom: 1px solid #eee;
}
.store-cent {
	display: flex;
	align-items: center;
	width: 80%;
}
.store-cent-left {
	width: 45%;
}
.store-img {
	width: 120rpx;
	height: 120rpx;
	border-radius: 6rpx;
	margin-right: 22rpx;
}
.store-img ._img {
	width: 100%;
	height: 100%;
}
.store-name {
	color: #282828;
	font-size: 30rpx;
	margin-bottom: 22rpx;
	font-weight: 800;
}
.store-address {
	color: #666666;
	font-size: 24rpx;
}
.store-phone {
	width: 50rpx;
	height: 50rpx;
	color: #fff;
	border-radius: 50%;
	display: block;
	text-align: center;
	line-height: 48rpx;
	background-color: #e83323;
	margin-bottom: 22rpx;
	text-decoration: none;
}
.store-distance {
	font-size: 22rpx;
	color: #e83323;
}
.iconfont {
	font-size: 20rpx;
}
.row-right {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	width: 33.5%;
}

