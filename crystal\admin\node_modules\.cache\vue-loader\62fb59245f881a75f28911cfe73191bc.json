{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\Category\\list.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\Category\\list.vue", "mtime": 1753666157756}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport * as categoryApi from '@/api/categoryApi.js'\r\nimport info from './info'\r\nimport edit from './edit'\r\nimport * as selfUtil from '@/utils/ZBKJIutil.js'\r\nimport { checkPermi, checkRole } from \"@/utils/permission\";\r\nexport default {\r\n  // name: \"list\"\r\n  components: { info, edit },\r\n  props: {\r\n    biztype: { // 类型，1 产品分类，2 附件分类，3 文章分类， 4 设置分类， 5 菜单分类， 6 配置分类， 7 秒杀配置， 8 手串分类\r\n      type: Object,\r\n      default: { value: -1 },\r\n      validator: (obj) => {\r\n        return obj.value > 0\r\n      }\r\n    },\r\n    pid: {\r\n      type: Number,\r\n      default: 0,\r\n      validator: (value) => {\r\n        return value >= 0\r\n      }\r\n    },\r\n    selectModel: { // 是否选择模式\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    selectModelKeys: {\r\n      type: Array\r\n    },\r\n    rowSelect: {}\r\n  },\r\n  data() {\r\n    return {\r\n      selectModelKeysNew: this.selectModelKeys,\r\n      loading: false,\r\n      constants: this.$constants,\r\n      treeProps: {\r\n        label: 'name',\r\n        children: 'child',\r\n        // expandTrigger: 'hover',\r\n        // checkStrictly: false,\r\n        // emitPath: false\r\n      },\r\n      // treeCheckedKeys:[],// 选择模式下的属性结构默认选中\r\n      multipleSelection: [],\r\n      editDialogConfig: {\r\n        visible: false,\r\n        isCreate: 0, // 0=创建，1=编辑\r\n        prent: {}, // 父级对象\r\n        data: {},\r\n        biztype: this.biztype // 统一主业务中的目录类型\r\n      },\r\n      dataList: [],\r\n      treeList: [],\r\n      listPram: {\r\n        pid: this.pid,\r\n        type: this.biztype.value,\r\n        status: -1,\r\n        name: '',\r\n        page: this.$constants.page.page,\r\n        limit: this.$constants.page.limit[0]\r\n      },\r\n      viewInfoConfig: {\r\n        data: null,\r\n        visible: false\r\n      },\r\n      defaultImg: require('@/assets/imgs/moren.jpg')\r\n    }\r\n  },\r\n  mounted() {\r\n    /* if(this.biztype.value === 3){\r\n       this.listPram.pageSize = constants.page.pageSize[4]\r\n       this.handlerGetList()\r\n     }else{*/\r\n    this.handlerGetTreeList()\r\n    // }\r\n  },\r\n  methods: {\r\n    checkPermi, //权限控制\r\n    onchangeIsShow(row) {\r\n      categoryApi.categroyUpdateStatus(row.id).then(() => {\r\n        this.$message.success('修改成功')\r\n        this.handlerGetTreeList()\r\n      }).catch(() => {\r\n        row.status = !row.status\r\n      })\r\n    },\r\n    handleEditMenu(rowData) {\r\n      this.editDialogConfig.isCreate = 1\r\n      this.editDialogConfig.data = rowData\r\n      this.editDialogConfig.prent = rowData\r\n      this.editDialogConfig.visible = true\r\n    },\r\n    handleAddMenu(rowData) {\r\n      this.editDialogConfig.isCreate = 0\r\n      this.editDialogConfig.prent = rowData\r\n      this.editDialogConfig.data = {}\r\n      this.editDialogConfig.biztype = this.biztype\r\n      this.editDialogConfig.visible = true\r\n    },\r\n    getCurrentNode(data) {\r\n      let node = this.$refs.tree.getNode(data);\r\n      this.childNodes(node);\r\n      // this.parentNodes(node);\r\n      //是否编辑的表示\r\n      // this.ruleForm.isEditorFlag = true;\r\n      //编辑时候使用\r\n      this.$emit('rulesSelect', this.$refs.tree.getCheckedKeys());\r\n      // this.selectModelKeys = this.$refs.tree.getCheckedKeys();\r\n      //无论编辑和新增点击了就传到后台这个值\r\n      // this.$emit('rulesSelect', this.$refs.tree.getCheckedKeys().concat(this.$refs.tree.getHalfCheckedKeys()));\r\n      // this.ruleForm.menuIdsisEditor = this.$refs.tree.getCheckedKeys().concat(this.$refs.tree.getHalfCheckedKeys());\r\n    },\r\n    //具体方法可以看element官网api\r\n    childNodes(node) {\r\n      let len = node.childNodes.length;\r\n      for (let i = 0; i < len; i++) {\r\n        node.childNodes[i].checked = node.checked;\r\n        this.childNodes(node.childNodes[i]);\r\n      }\r\n    },\r\n    parentNodes(node) {\r\n      if (node.parent) {\r\n        for (let key in node) {\r\n          if (key == \"parent\") {\r\n            node[key].checked = true;\r\n            this.parentNodes(node[key]);\r\n          }\r\n        }\r\n      }\r\n    },\r\n    handleDelMenu(rowData) {\r\n      this.$confirm('确定删除当前数据?').then(() => {\r\n        categoryApi.deleteCategroy(rowData).then(data => {\r\n          this.handlerGetTreeList()\r\n          this.$message.success('删除成功')\r\n        })\r\n      })\r\n    },\r\n    handlerGetList() {\r\n      this.handlerGetTreeList();\r\n      // categoryApi.listCategroy({status:this.listPram.status, type: 1 }).then(data => {\r\n      //   this.treeList = data.list\r\n      // })\r\n    },\r\n    handlerGetTreeList() {\r\n      // this.biztype.value === 5 && !this.selectModel) ?  -1 : 1\r\n      // const _pram = { type: this.biztype.value, status: !this.selectModel ? -1 : (this.biztype.value === 5 ? -1 : 1) }\r\n      const _pram = { type: this.biztype.value, status: this.listPram.status, name: this.listPram.name }\r\n      this.loading = true\r\n      this.biztype.value !== 3 ? categoryApi.treeCategroy(_pram).then(data => {\r\n        this.treeList = this.handleAddArrt(data)\r\n        this.loading = false\r\n      }).catch(() => {\r\n        this.loading = false\r\n      }) : categoryApi.listCategroy({ type: 3, status: this.listPram.status, pid: this.listPram.pid, name: this.listPram.name }).then(data => {\r\n        this.treeList = data.list\r\n      })\r\n    },\r\n    handlerGetInfo(id) {\r\n      this.viewInfoConfig.data = id\r\n      this.viewInfoConfig.visible = true\r\n    },\r\n    handleNodeClick(data) {\r\n      console.log('data:', data)\r\n    },\r\n    handleAddArrt(treeData) {\r\n      const _result = selfUtil.addTreeListLabel(treeData)\r\n      return _result\r\n    },\r\n    hideEditDialog() {\r\n      setTimeout(() => {\r\n        this.editDialogConfig.prent = {}\r\n        this.editDialogConfig.type = 0\r\n        this.editDialogConfig.visible = false\r\n        this.handlerGetTreeList()\r\n      }, 200)\r\n    },\r\n    handleSelectionChange(d1, { checkedNodes, checkedKeys, halfCheckedNodes, halfCheckedKeys }) {\r\n      // this.multipleSelection =  checkedKeys.concat(halfCheckedKeys)\r\n      this.multipleSelection = checkedKeys\r\n      this.$emit('rulesSelect', this.multipleSelection)\r\n    },\r\n  }\r\n}\r\n", null]}