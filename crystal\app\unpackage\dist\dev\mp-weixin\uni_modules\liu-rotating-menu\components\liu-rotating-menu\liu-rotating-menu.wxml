<view class="data-v-cc32306a"><movable-area class="movable-area data-v-cc32306a" scale-area="{{false}}"><movable-view class="{{['movable-view','data-v-cc32306a',!isRemove?'animation-info':'']}}" style="pointer-events:auto;" direction="all" inertia="true" x="{{x}}" y="{{y}}" disabled="{{disabled}}" out-of-bounds="{{true}}" damping="{{200}}" friction="{{100}}" data-event-opts="{{[['touchstart',[['touchstart',['$event']]]],['touchend',[['touchend',['$event']]]],['change',[['onChange',['$event']]]]]}}" bindtouchstart="__e" bindtouchend="__e" bindchange="__e"><view data-event-opts="{{[['tap',[['clickBtn',['$event']]]]]}}" bindtap="__e" class="data-v-cc32306a">{{btnObj.name}}</view><block wx:for="{{btnObj.childs}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{showBtn}}"><view data-event-opts="{{[['tap',[['click',['$0'],[[['btnObj.childs','',index]]]]]]]}}" class="{{['item-main','data-v-cc32306a',!isLeft?'item-main'+(index+1)+' toOut'+(index+1):'item-main1'+(index+1)+' toOut1'+(index+1)]}}" bindtap="__e">{{''+item.name+''}}</view></block></block></movable-view></movable-area></view>