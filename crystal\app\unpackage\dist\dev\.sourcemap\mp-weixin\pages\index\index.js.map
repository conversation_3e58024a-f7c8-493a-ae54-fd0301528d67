{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/index/index.vue?0d57", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/index/index.vue?bb36", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/index/index.vue?9827", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/index/index.vue?eeb5", "uni-app:///pages/index/index.vue", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/index/components/a_seckill.vue?5fbd", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/index/components/a_seckill.vue?d9c4", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/index/components/a_seckill.vue?141f", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/index/components/a_seckill.vue?6f40", "uni-app:///pages/index/components/a_seckill.vue", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/index/components/a_seckill.vue?0384", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/index/components/a_seckill.vue?2ee9", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/index/components/b_combination.vue?0778", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/index/components/b_combination.vue?2ab5", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/index/components/b_combination.vue?c117", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/index/components/b_combination.vue?2774", "uni-app:///pages/index/components/b_combination.vue", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/index/components/b_combination.vue?8836", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/index/components/b_combination.vue?f14b", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/index/index.vue?f773", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/index/index.vue?ba5c", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/index/index.vue?8482", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/index/index.vue?61a5"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "computed", "components", "tabNav", "goodList", "promotionGood", "couponWindow", "countDown", "a_seckill", "b_combination", "c_bargain", "recommend", "authorize", "Loading", "data", "pageHeight", "loaded", "loading", "isAuto", "isShowAuth", "statusBarHeight", "navIndex", "navTop", "followUrl", "followHid", "followCode", "logoUrl", "imgUrls", "itemNew", "menus", "bastInfo", "fastInfo", "fastList", "firstInfo", "salesInfo", "indicatorDots", "circular", "autoplay", "interval", "duration", "window", "iShidden", "navH", "newGoodsBananr", "couponList", "liveList", "hotList", "pic", "ProductNavindex", "marTop", "childID", "loadend", "loadTitle", "sortProduct", "where", "cid", "page", "limit", "is_switch", "hotPage", "hotLimit", "hotScroll", "explosiveMoney", "prodeuctTop", "searchH", "isFixed", "goodType", "goodScroll", "params", "tempArr", "roll", "site_name", "iSshowH", "config<PERSON>pi", "spikeList", "point", "privacyStatus", "tabsScrollLeft", "scrollLeft", "lineColor", "lineStyle", "listActive", "watch", "mounted", "onLoad", "uni", "success", "that", "type", "altitude", "geocode", "info", "self", "onShow", "title", "methods", "getCoupon", "clickSort", "scroll", "setTabList", "scrollIntoView", "lineLeft", "setLine", "lineWidth", "width", "transform", "transitionDuration", "getElementData", "callback", "xieyiApp", "url", "getTemlIds", "getTem", "getIndexConfig", "getcouponList", "shareApi", "getChatUrL", "kefuConfig", "<PERSON><PERSON>", "setOpenShare", "desc", "link", "imgUrl", "configAppMessage", "auth<PERSON><PERSON><PERSON>", "onLoadFun", "ProductNavTab", "goDetail", "<PERSON><PERSON><PERSON><PERSON>", "godDetail", "getGroomList", "get_host_product", "imageUrl", "path", "name", "bgColor", "datatime", "status", "created", "getSeckillIndexTime", "combinationList", "isBorader", "assistUserList", "assistUserCount", "getCombinationList"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;AACC;;;AAGlE;AACgM;AAChM,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjDA;AAAA;AAAA;AAAA;AAAmvB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACyJvwB;AACA;AAGA;AAmBA;AAGA;AACA;AAKA;AACA;AAGA;AAKA;AAaA;AAEA;AAtDA;AACA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AA+DA;AAAA;EAEAC;EACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IAEAC;IAEAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;MACA;QACAA;MACA;QACAA;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;QAAA;QACAZ;QACAC;MACA;MACAY;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MAAA;MACAC;IAAA,eAEA;EAEA;EACAC;IACAlC;MAAA;MACA;IACA;IACAiC;MAAA;MACA;IACA;EACA;EACAE;IACA;EACA;EACAC;IACA;IACA;IACAC;MACAC;QACAC;MACA;IACA;IACAF;MACAG;MACAC;MACAC;MACAJ;QACA;UACAD;UACAA;QACA;MACA;IACA;IACA;;IAEA;IACA;IACA;IACAM;MACAC;MACAA;IACA;IAKA;IACA;IACA;IACA;IACA;IAEA;EAEA;EACAC;IACA;IACAR;MACAS;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;QACAT;QACAA;UACAO;QACA;MACA;QACA;UACAA;QACA;MACA;IACA;IACAG;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;QACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MAAA;MACA;MACA;QACA;QACA;UACA;UACAC;UACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACAD;MACA;QACA;QACAE;QACA;QACAF;QACA;UACAG;UACAC;UACAC;QACA;MACA;IACA;IACAC;MACAtB;QACAuB;MACA;IACA;IACAC;MACAxB;QACAyB;MACA;IACA;IAEAC;MACA;QACA;MACA;IACA;IACAC;MACA;QACAxB;MACA;QACA;UACA;YACA;UACA;UACA5F;QACA;MACA;IACA;IAEA;IACAqH;MAAA;MACA;MACA;QACA5B;UACAS;QACA;QACAP;QACAA;QACAA;QACAA;QACAA;QAKAA;QACAA;QACA;QACA;QACA;MAKA;IACA;IACA2B;MAAA;MACA;MACA;QACA1D;QACAC;MACA;QACA8B;QACA;;QAEAF;UACAC;YACA;cACAC;YACA;cACAA;cACAA;YACA;UACA;QACA;MASA;QACA;UACAO;QACA;MACA;IACA;IACAqB;MAAA;MACA;QACA;MAIA;IACA;IACAC;MAAA;MACAC;QACA;QACA;QACAC;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;UACAC;UACA1B;UACA2B;UACAC;QACA;QACAnC,mFACAoC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC,iCAEA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;QACAC;MACA;QACA;UACA3C;YACAyB;UACA;QACA;MACA;IACA;IACA;IACAmB;MACA;QACA5C;UACAyB;QACA;MACA;IACA;IACA;IACAoB;MAAA;MACA;MACA;MACA;MACA;QACA;MACA;MACA,kEAEA;QAAA,IADApH;QAEA;QACA;QACA;QACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAqH;MACA;MACA5C;MAEA;MACA,0BACAA,cACAA,cACA;QACAA;QACAA;QACAA;MACA;IACA;EACA;AAAA,sFACA;EACA;AASA,8EAKA;EACA;IACAO;IACAsC;IACAZ;IACAa;EACA;AACA,mGAEA;EACA;IACA;IACA;MACA;IACA;EACA;IACA;IACA;MACA;IAAA,CACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzpBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACsC;;;AAG9F;AACmM;AACnM,gBAAgB,2LAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAswB,CAAgB,yrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACmC1xB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAFA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAKA;EACAC;EACApI;IACAK;EACA;EACAO;IACA;MACAyH;QACA;QACA;QACA;QACA;QACA;MACA;MACA7D;MAAA;MACAC;MACA6D;MACAC;IACA;EACA;EACAC;IACA;EACA;EACA3C;IACA4C;MAAA;MACA;QAAA;QACA;QACA;QACA;QACA;MACA;IACA;IACAZ;MACA1C;QACAyB;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5EA;AAAA;AAAA;AAAA;AAAy8C,CAAgB,wvCAAG,EAAC,C;;;;;;;;;;;ACA79C;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AACmM;AACnM,gBAAgB,2LAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/BA;AAAA;AAAA;AAAA;AAA0wB,CAAgB,6rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACmD9xB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AADA;AAAA,eAIA;EACAwB;EACAxH;IACA;MACA8H;MACAC;MACAC;MACAC;IACA;EACA;EACAL;IACA;EACA;EACAvD;EACAY;IACA;IACAiD;MACA;MACA;QACAzD;QACAA;QACAA;MACA;QACA;UACAO;QACA;MACA;IACA;IACAiC;MACA1C;QACAyB;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxFA;AAAA;AAAA;AAAA;AAA68C,CAAgB,4vCAAG,EAAC,C;;;;;;;;;;;ACAj+C;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAgkC,CAAgB,s8BAAG,EAAC,C;;;;;;;;;;;ACAplC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAk5C,CAAgB,4tCAAG,EAAC,C;;;;;;;;;;;ACAt6C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./index.vue?vue&type=style&index=1&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=57280228&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.navIndex == 0 ? _vm.imgUrls.length : null\n  var g1 = _vm.navIndex == 0 ? _vm.roll.length : null\n  var g2 = _vm.navIndex == 0 ? _vm.menus.length : null\n  var g3 = _vm.navIndex == 0 ? _vm.couponList.length : null\n  var l0 =\n    _vm.navIndex == 0 && g3 > 0\n      ? _vm.__map(_vm.couponList.slice(0, 2), function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m0 = item.money ? Number(item.money) : null\n          var m1 = item.minPrice ? Number(item.minPrice) : null\n          return {\n            $orig: $orig,\n            m0: m0,\n            m1: m1,\n          }\n        })\n      : null\n  var g4 = _vm.navIndex == 0 ? _vm.tempArr.length : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event, item, index) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        item = _temp2.item,\n        index = _temp2.index\n      var _temp, _temp2\n      return _vm.getCoupon(item.id, index)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        l0: l0,\n        g4: g4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view class=\"page-index\" :class=\"{'bgf':navIndex >0}\">\r\n\t\t\t<!-- #ifdef H5||APP||MP-WEIXIN -->\r\n\t\t\t<view class=\"header\">\r\n\t\t\t\t<view class=\"serch-wrapper flex\">\r\n\t\t\t\t\t<view class=\"logo\">\r\n\t\t\t\t\t\t<image :src=\"logoUrl\" mode=\"\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<navigator url=\"/pages/goods_search/index\" class=\"input\" hover-class=\"none\"><text\r\n\t\t\t\t\t\t\tclass=\"iconfont icon-xiazai5\"></text>\r\n\t\t\t\t\t\t搜索商品</navigator>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<!-- #endif -->\r\n\t\t\t<!-- 首页展示 -->\r\n\t\t\t<view class=\"page_content\" :style=\"'margin-top:'+(marTop)+'px;'\" v-if=\"navIndex == 0\">\r\n\t\t\t\t<view class=\"mp-bg\"></view>\r\n\t\t\t\t<!-- banner -->\r\n\t\t\t\t<view class=\"swiper\" v-if=\"imgUrls.length\">\r\n\t\t\t\t\t<swiper indicator-dots=\"true\" :autoplay=\"true\" :circular=\"circular\" :interval=\"interval\"\r\n\t\t\t\t\t\t:duration=\"duration\" indicator-color=\"rgba(255,255,255,0.6)\" indicator-active-color=\"#fff\">\r\n\t\t\t\t\t\t<block v-for=\"(item,index) in imgUrls\" :key=\"index\">\r\n\t\t\t\t\t\t\t<swiper-item>\r\n\t\t\t\t\t\t\t\t<navigator :url='item.url' class='slide-navigator acea-row row-between-wrapper'\r\n\t\t\t\t\t\t\t\t\thover-class='none'>\r\n\t\t\t\t\t\t\t\t\t<image :src=\"item.pic\" class=\"slide-image\" lazy-load></image>\r\n\t\t\t\t\t\t\t\t</navigator>\r\n\t\t\t\t\t\t\t</swiper-item>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</swiper>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 新闻简报 -->\r\n\t\t\t\t<view class='notice acea-row row-middle row-between' v-if=\"roll.length\">\r\n\t\t\t\t\t<view class=\"pic\">\r\n\t\t\t\t\t\t<image src=\"/static/images/xinjian.png\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<text class='line'>|</text>\r\n\t\t\t\t\t<view class='swipers'>\r\n\t\t\t\t\t\t<swiper :indicator-dots=\"indicatorDots\" :autoplay=\"autoplay\" interval=\"2500\" duration=\"500\"\r\n\t\t\t\t\t\t\tvertical=\"true\" circular=\"true\">\r\n\t\t\t\t\t\t\t<block v-for=\"(item,index) in roll\" :key='index'>\r\n\t\t\t\t\t\t\t\t<swiper-item>\r\n\t\t\t\t\t\t\t\t\t<navigator class='item' :url='item.url' hover-class='none'>\r\n\t\t\t\t\t\t\t\t\t\t<view class='line1'>{{item.info}}</view>\r\n\t\t\t\t\t\t\t\t\t</navigator>\r\n\t\t\t\t\t\t\t\t</swiper-item>\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</swiper>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"iconfont icon-xiangyou\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- menu -->\r\n\t\t\t\t<view class='nav acea-row' v-if=\"menus.length\">\r\n\t\t\t\t\t<block v-for=\"(item,index) in menus\" :key=\"index\">\r\n\t\t\t\t\t\t<navigator class='item' v-if=\"item.show == '1'\" :url='item.url' open-type='switchTab'\r\n\t\t\t\t\t\t\thover-class='none'>\r\n\t\t\t\t\t\t\t<view class='pictrue'>\r\n\t\t\t\t\t\t\t\t<image :src='item.pic'></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"menu-txt\">{{item.name}}</view>\r\n\t\t\t\t\t\t</navigator>\r\n\t\t\t\t\t\t<navigator class='item' v-else :url='item.url' hover-class='none'>\r\n\t\t\t\t\t\t\t<view class='pictrue'>\r\n\t\t\t\t\t\t\t\t<image :src='item.pic'></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"menu-txt\">{{item.name}}</view>\r\n\t\t\t\t\t\t</navigator>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 优惠券 -->\r\n\t\t\t\t<view class=\"couponIndex\" v-if=\"couponList.length>0\">\r\n\t\t\t\t\t<view class=\"acea-row\" style=\"height: 100%;\">\r\n\t\t\t\t\t\t<view class=\"titBox\">\r\n\t\t\t\t\t\t\t<view class=\"tit1\">领取优惠券</view>\r\n\t\t\t\t\t\t\t<view class=\"tit2\">福利大礼包，省了又省</view>\r\n\t\t\t\t\t\t\t<navigator class='item' url='/pages/users/user_get_coupon/index' hover-class='none'>\r\n\t\t\t\t\t\t\t\t<view class=\"tit3\">查看全部 <text class=\"iconfont icon-xiangyou\"></text></view>\r\n\t\t\t\t\t\t\t</navigator>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"listBox acea-row\">\r\n\t\t\t\t\t\t\t<view class=\"list\" :class='item.isUse ? \"listHui\" : \"listActive\" '\r\n\t\t\t\t\t\t\t\tv-for=\"(item, index) in couponList.slice(0,2)\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t<view class=\"tit line1\" :class='item.isUse ? \"pricehui\" : \"titActive\" '>{{item.name}}\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"price\" :class='item.isUse ? \"pricehui\" : \"icon-color\" '>\r\n\t\t\t\t\t\t\t\t\t{{item.money?Number(item.money):''}}<text class=\"yuan\">元</text></view>\r\n\t\t\t\t\t\t\t\t<view class=\"ling\" v-if=\"!item.isUse\" :class='item.isUse ? \"pricehui\" : \"icon-color\" '\r\n\t\t\t\t\t\t\t\t\t@click=\"getCoupon(item.id,index)\">领取</view>\r\n\t\t\t\t\t\t\t\t<view class=\"ling\" v-else :class='item.isUse ? \"pricehui fonthui\" : \"icon-color\" '>已领取\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"priceM\">满{{item.minPrice?Number(item.minPrice):''}}元可用</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- 活动-->\r\n\t\t\t\t<a_seckill></a_seckill>\r\n\t\t\t\t<b_combination></b_combination>\r\n\t\t\t\t<c_bargain></c_bargain>\r\n\r\n\t\t\t\t<!-- 首页推荐 -->\r\n\t\t\t\t<!-- :class=\"iSshowH?'on':''\" -->\r\n\t\t\t\t<view class=\"sticky-box\" :style=\"'top:'+(marTop)+'px;'\">\r\n\t\t\t\t\t<scroll-view class=\"scroll-view_H\" style=\"width: 100%;\" scroll-x=\"true\" scroll-with-animation\r\n\t\t\t\t\t\t:scroll-left=\"tabsScrollLeft\" @scroll=\"scroll\">\r\n\t\t\t\t\t\t<view class=\"tab nav-bd\" id=\"tab_list\">\r\n\t\t\t\t\t\t\t<view id=\"tab_item\" :class=\"{ 'active': listActive == index}\" class=\"item\"\r\n\t\t\t\t\t\t\t\tv-for=\"(item, index) in explosiveMoney\" :key=\"index\" @click=\"ProductNavTab(item,index)\">\r\n\t\t\t\t\t\t\t\t<view class=\"txt\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"label\">{{item.info}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</scroll-view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 首发新品 -->\r\n\t\t\t\t<view class=\"index-product-wrapper\" :class=\"iSshowH?'on':''\">\r\n\t\t\t\t\t<view class=\"list-box animated\" :class='tempArr.length > 0?\"fadeIn on\":\"\"'>\r\n\t\t\t\t\t\t<view class=\"item\" v-for=\"(item,index) in tempArr\" :key=\"index\" @click=\"goDetail(item)\">\r\n\t\t\t\t\t\t\t<view class=\"pictrue\">\r\n\t\t\t\t\t\t\t\t<span class=\"pictrue_log pictrue_log_class\"\r\n\t\t\t\t\t\t\t\t\tv-if=\"item.activityH5 && item.activityH5.type === '1'\">秒杀</span>\r\n\t\t\t\t\t\t\t\t<span class=\"pictrue_log pictrue_log_class\"\r\n\t\t\t\t\t\t\t\t\tv-if=\"item.activityH5 && item.activityH5.type === '2'\">砍价</span>\r\n\t\t\t\t\t\t\t\t<span class=\"pictrue_log pictrue_log_class\"\r\n\t\t\t\t\t\t\t\t\tv-if=\"item.activityH5 && item.activityH5.type === '3'\">拼团</span>\r\n\t\t\t\t\t\t\t\t<image :src=\"item.image\" mode=\"\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"text-info\">\r\n\t\t\t\t\t\t\t\t<view class=\"title line1\">{{item.storeName}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"old-price\"><text>¥{{item.otPrice}}</text></view>\r\n\t\t\t\t\t\t\t\t<view class=\"price\">\r\n\t\t\t\t\t\t\t\t\t<text>￥</text>{{item.price}}\r\n\t\t\t\t\t\t\t\t\t<view class=\"txt\" v-if=\"item.checkCoupon\">券</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='loadingicon acea-row row-center-wrapper' v-if=\"goodScroll\">\r\n\t\t\t\t\t\t<text class='loading iconfont icon-jiazai' :hidden='loading==false'></text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"mores-txt flex\" v-if=\"!goodScroll\">\r\n\t\t\t\t\t\t<text>我是有底线的</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport Auth from '@/libs/wechat';\r\n\timport Cache from '../../utils/cache';\r\n\tvar statusBarHeight = uni.getSystemInfoSync().statusBarHeight + 'px';\r\n\tlet app = getApp();\r\n\timport {\r\n\t\tgetIndexData,\r\n\t\tgetCoupons,\r\n\t\tsetCouponReceive\r\n\t} from '@/api/api.js';\r\n\t// #ifdef MP-WEIXIN\r\n\timport {\r\n\t\tgetTemlIds\r\n\t} from '@/api/api.js';\r\n\t// import {\r\n\t// \tSUBSCRIBE_MESSAGE,\r\n\t// \tTIPS_KEY\r\n\t// } from '@/config/cache';\r\n\t// #endif\r\n\t// #ifdef H5  \r\n\timport {\r\n\t\tfollow\r\n\t} from '@/api/public.js';\r\n\t// #endif\r\n\timport {\r\n\t\tgetShare\r\n\t} from '@/api/public.js';\r\n\timport a_seckill from './components/a_seckill';\r\n\timport b_combination from './components/b_combination';\r\n\timport c_bargain from './components/c_bargain';\r\n\timport goodList from '@/components/goodList';\r\n\timport promotionGood from '@/components/promotionGood';\r\n\timport couponWindow from '@/components/couponWindow';\r\n\timport ClipboardJS from \"@/plugin/clipboard/clipboard.js\";\r\n\timport {\r\n\t\tgoShopDetail\r\n\t} from '@/libs/order.js'\r\n\timport {\r\n\t\tmapGetters\r\n\t} from \"vuex\";\r\n\timport tabNav from '@/components/tabNav.vue'\r\n\timport countDown from '@/components/countDown';\r\n\timport {\r\n\t\tgetCategoryList,\r\n\t\tgetProductslist,\r\n\t\tgetProductHot,\r\n\t\tgetGroomList\r\n\t} from '@/api/store.js';\r\n\t// import {\r\n\t// \tsetVisit\r\n\t// } from '@/api/user.js'\r\n\timport recommend from '@/components/recommend';\r\n\t// #ifdef MP\r\n\timport authorize from '@/components/Authorize';\r\n\t// #endif\r\n\timport {\r\n\t\tsilenceBindingSpread\r\n\t} from '@/utils';\r\n\t// #ifndef MP\r\n\timport {\r\n\t\tkefuConfig\r\n\t} from \"@/api/public\";\r\n\timport {\r\n\t\tgetWechatConfig\r\n\t} from \"@/api/public\";\r\n\t// #endif\r\n\timport Loading from '@/components/Loading/index.vue';\r\n\tconst arrTemp = [\"beforePay\", \"afterPay\", \"refundApply\", \"beforeRecharge\", \"createBargain\", \"pink\"];\r\n\texport default {\r\n\t\tcomputed: mapGetters(['isLogin', 'uid']),\r\n\t\tcomponents: {\r\n\t\t\ttabNav,\r\n\t\t\tgoodList,\r\n\t\t\tpromotionGood,\r\n\t\t\tcouponWindow,\r\n\t\t\tcountDown,\r\n\t\t\ta_seckill,\r\n\t\t\tb_combination,\r\n\t\t\tc_bargain,\r\n\t\t\trecommend,\r\n\t\t\t// #ifdef MP\r\n\t\t\tauthorize,\r\n\t\t\t// #endif\r\n\t\t\tLoading\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tpageHeight: 0,\r\n\t\t\t\tloaded: false,\r\n\t\t\t\tloading: false,\r\n\t\t\t\tisAuto: false, //没有授权的不会自动授权\r\n\t\t\t\tisShowAuth: false, //是否隐藏授权\r\n\t\t\t\tstatusBarHeight: statusBarHeight,\r\n\t\t\t\tnavIndex: 0,\r\n\t\t\t\tnavTop: [],\r\n\t\t\t\tfollowUrl: \"\",\r\n\t\t\t\tfollowHid: true,\r\n\t\t\t\tfollowCode: false,\r\n\t\t\t\tlogoUrl: \"\",\r\n\t\t\t\timgUrls: [],\r\n\t\t\t\titemNew: [],\r\n\t\t\t\tmenus: [],\r\n\t\t\t\tbastInfo: '',\r\n\t\t\t\tfastInfo: '',\r\n\t\t\t\tfastList: [],\r\n\t\t\t\tfirstInfo: '',\r\n\t\t\t\tsalesInfo: '',\r\n\t\t\t\tindicatorDots: false,\r\n\t\t\t\tcircular: true,\r\n\t\t\t\tautoplay: true,\r\n\t\t\t\tinterval: 3000,\r\n\t\t\t\tduration: 500,\r\n\t\t\t\twindow: false,\r\n\t\t\t\tiShidden: false,\r\n\t\t\t\tnavH: \"\",\r\n\t\t\t\tnewGoodsBananr: '',\r\n\t\t\t\tcouponList: [],\r\n\t\t\t\tliveList: [],\r\n\t\t\t\thotList: [{\r\n\t\t\t\t\tpic: '/static/images/hot_001.png'\r\n\t\t\t\t}, {\r\n\t\t\t\t\tpic: '/static/images/hot_002.png'\r\n\t\t\t\t}, {\r\n\t\t\t\t\tpic: '/static/images/hot_003.png'\r\n\t\t\t\t}],\r\n\t\t\t\tProductNavindex: 0,\r\n\t\t\t\tmarTop: 0,\r\n\t\t\t\tchildID: 0,\r\n\t\t\t\tloadend: false,\r\n\t\t\t\tloadTitle: '加载更多',\r\n\t\t\t\tsortProduct: [],\r\n\t\t\t\twhere: {\r\n\t\t\t\t\tcid: 0,\r\n\t\t\t\t\tpage: 1,\r\n\t\t\t\t\tlimit: 10,\r\n\t\t\t\t},\r\n\t\t\t\tis_switch: true,\r\n\t\t\t\thotPage: 1,\r\n\t\t\t\thotLimit: 10,\r\n\t\t\t\thotScroll: false,\r\n\t\t\t\texplosiveMoney: [],\r\n\t\t\t\tprodeuctTop: 0,\r\n\t\t\t\tsearchH: 0,\r\n\t\t\t\tisFixed: false,\r\n\t\t\t\tgoodType: 0, //精品推荐Type\r\n\t\t\t\tgoodScroll: true, //精品推荐开关\r\n\t\t\t\tparams: { //精品推荐分页\r\n\t\t\t\t\tpage: 1,\r\n\t\t\t\t\tlimit: 10,\r\n\t\t\t\t},\r\n\t\t\t\ttempArr: [], //精品推荐临时数组\r\n\t\t\t\troll: [], // 新闻简报\r\n\t\t\t\tsite_name: '', //首页title\r\n\t\t\t\tiSshowH: false,\r\n\t\t\t\tconfigApi: {}, //分享类容配置\r\n\t\t\t\tspikeList: [], // 秒杀\r\n\t\t\t\tpoint: '',\r\n\t\t\t\tprivacyStatus: false, // 隐私政策是否同意过\r\n\t\t\t\ttabsScrollLeft: 0, // tabs当前偏移量\r\n\t\t\t\tscrollLeft: 0,\r\n\t\t\t\tlineColor: 'red',\r\n\t\t\t\tlineStyle: {}, // 下划线位置--动态甲酸\r\n\t\t\t\tlistActive: 0, // 当前选中项\r\n\r\n\t\t\t\tduration: 0.2 // 下划线动画时长\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tProductNavindex(newVal) { // 监听当前选中项\r\n\t\t\t\tthis.setTabList()\r\n\t\t\t},\r\n\t\t\tlistActive(newVal) { // 监听当前选中项\r\n\t\t\t\tthis.setTabList()\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tthis.setTabList()\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tvar that = this;\r\n\t\t\t// 获取系统信息\r\n\t\t\tuni.getSystemInfo({\r\n\t\t\t\tsuccess(res) {\r\n\t\t\t\t\tthat.$store.commit(\"SYSTEM_PLATFORM\", res.platform);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\tuni.getLocation({\r\n\t\t\t\ttype: 'gcj02',\r\n\t\t\t\taltitude: true,\r\n\t\t\t\tgeocode: true,\r\n\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\tuni.setStorageSync('user_latitude', res.latitude);\r\n\t\t\t\t\t\tuni.setStorageSync('user_longitude', res.longitude);\r\n\t\t\t\t\t} catch {}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\tlet self = this\r\n\t\t\t// #ifdef MP\r\n\t\t\t// 获取小程序头部高度\r\n\t\t\tthis.navH = app.globalData.navHeight;\r\n\t\t\tlet info = uni.createSelectorQuery().select(\".mp-header\");\r\n\t\t\tinfo.boundingClientRect(function(data) {\r\n\t\t\t\tself.marTop = data ? data.height : 0\r\n\t\t\t\tself.poTop = Number(data ? data.height : 0) + 84\r\n\t\t\t}).exec()\r\n\t\t\t// #endif\r\n\t\t\t// #ifndef MP\r\n\t\t\tthis.navH = 0;\r\n\t\t\t// #endif\r\n\t\t\tthis.isLogin && silenceBindingSpread();\r\n\t\t\t// Promise.all([this.getAllCategory(), this.getIndexConfig()\r\n\t\t\t// \t// , this.setVisit()\r\n\t\t\t// ]);\r\n\t\t\tthis.getIndexConfig();\r\n\t\t\t// #ifdef MP\r\n\t\t\tthis.getTemlIds()\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tlet self = this\r\n\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\ttitle: self.site_name\r\n\t\t\t})\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetCoupon: function(id, index) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\t//领取优惠券\r\n\t\t\t\tsetCouponReceive(id).then(function(res) {\r\n\t\t\t\t\tthat.$set(that.couponList[index], 'isUse', true);\r\n\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\ttitle: '领取成功'\r\n\t\t\t\t\t});\r\n\t\t\t\t}, function(res) {\r\n\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\ttitle: res\r\n\t\t\t\t\t});\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tclickSort(index) {\r\n\t\t\t\tthis.listActive = index\r\n\t\t\t},\r\n\t\t\t// scroll-view滑动事件\r\n\t\t\tscroll(e) {\r\n\t\t\t\tthis.scrollLeft = e.detail.scrollLeft;\r\n\t\t\t},\r\n\t\t\tsetTabList() {\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t//this.setLine()\r\n\t\t\t\t\tthis.scrollIntoView()\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 计算tabs位置\r\n\t\t\tscrollIntoView() { // item滚动\r\n\t\t\t\tlet lineLeft = 0;\r\n\t\t\t\tthis.getElementData('#tab_list', (data) => {\r\n\t\t\t\t\tlet list = data[0]\r\n\t\t\t\t\tthis.getElementData(`#tab_item`, (data) => {\r\n\t\t\t\t\t\tlet el = data[this.listActive]\r\n\t\t\t\t\t\tlineLeft = el.width / 2 + (-list.left) + el.left - list.width / 2 - this.scrollLeft\r\n\t\t\t\t\t\tthis.tabsScrollLeft = this.scrollLeft + lineLeft\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//  计算下划线位置\r\n\t\t\tsetLine() {\r\n\t\t\t\tlet lineWidth = 0,\r\n\t\t\t\t\tlineLeft = 0\r\n\t\t\t\tthis.getElementData(`#tab_item`, (data) => {\r\n\t\t\t\t\tlet el = data[this.listActive]\r\n\t\t\t\t\tlineWidth = el.width / 2\r\n\t\t\t\t\t// lineLeft = el.width * (this.currentIndex + 0.5)  // 此种只能针对每个item长度一致的\r\n\t\t\t\t\tlineLeft = el.width / 2 + (-data[0].left) + el.left\r\n\t\t\t\t\tthis.lineStyle = {\r\n\t\t\t\t\t\twidth: `${lineWidth}px`,\r\n\t\t\t\t\t\ttransform: `translateX(${lineLeft}px) translateX(-50%)`,\r\n\t\t\t\t\t\ttransitionDuration: `${this.duration}s`\r\n\t\t\t\t\t};\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetElementData(el, callback) {\r\n\t\t\t\tuni.createSelectorQuery().in(this).selectAll(el).boundingClientRect().exec((data) => {\r\n\t\t\t\t\tcallback(data[0]);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\txieyiApp() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/users/web_page/index?webUel=https://admin.java.crmeb.net/useragreement/xieyi.html&title=协议内容'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// #ifdef MP\r\n\t\t\tgetTemlIds() {\r\n\t\t\t\tfor (var i in arrTemp) {\r\n\t\t\t\t\tthis.getTem(arrTemp[i]);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgetTem(data) {\r\n\t\t\t\tgetTemlIds({\r\n\t\t\t\t\ttype: data\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tif (res.data) {\r\n\t\t\t\t\t\tlet arr = res.data.map((item) => {\r\n\t\t\t\t\t\t\treturn item.tempId\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\twx.setStorageSync('tempID' + data, arr);\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// #endif\r\n\t\t\t// 首页数据\r\n\t\t\tgetIndexConfig: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tgetIndexData().then(res => {\r\n\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\ttitle: '首页'\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthat.$set(that, \"logoUrl\", res.data.logoUrl);\r\n\t\t\t\t\tthat.$set(that, \"site_name\", '首页');\r\n\t\t\t\t\tthat.$set(that, \"imgUrls\", res.data.banner);\r\n\t\t\t\t\tthat.$set(that, \"menus\", res.data.menus);\r\n\t\t\t\t\tthat.$set(that, \"roll\", res.data.roll ? res.data.roll : []);\r\n\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\tthat.$store.commit(\"SET_CHATURL\", res.data.yzfUrl);\r\n\t\t\t\t\tCache.set('chatUrl', res.data.yzfUrl);\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\tthat.$set(that, \"explosiveMoney\", res.data.explosiveMoney);\r\n\t\t\t\t\tthat.goodType = res.data.explosiveMoney[0].type\r\n\t\t\t\t\tthis.getGroomList();\r\n\t\t\t\t\tthis.shareApi();\r\n\t\t\t\t\tthis.getcouponList();\r\n\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\t// that.subscribe = res.data.subscribe;\r\n\t\t\t\t\t// #endif\r\n\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetcouponList() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tgetCoupons({\r\n\t\t\t\t\tpage: 1,\r\n\t\t\t\t\tlimit: 6\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tthat.$set(that, \"couponList\", res.data);\r\n\t\t\t\t\t// 小程序判断用户是否授权；\r\n\t\t\t\t\t// #ifdef MP\r\n\t\t\t\t\tuni.getSetting({\r\n\t\t\t\t\t\tsuccess(res) {\r\n\t\t\t\t\t\t\tif (!res.authSetting['scope.userInfo']) {\r\n\t\t\t\t\t\t\t\tthat.window = that.couponList.length ? true : false;\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tthat.window = false;\r\n\t\t\t\t\t\t\t\tthat.iShidden = true;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifndef MP\r\n\t\t\t\t\tif (that.isLogin) {\r\n\t\t\t\t\t\tthat.window = false;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthat.window = res.data.length ? true : false;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\treturn this.$util.Tips({\r\n\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tshareApi: function() {\r\n\t\t\t\tgetShare().then(res => {\r\n\t\t\t\t\tthis.$set(this, 'configApi', res.data);\r\n\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\tthis.setOpenShare(res.data);\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetChatUrL() {\r\n\t\t\t\tkefuConfig().then(res => {\r\n\t\t\t\t\tlet data = res.data;\r\n\t\t\t\t\tthis.$store.commit(\"SET_CHATURL\", data.yzfUrl);\r\n\t\t\t\t\tCache.set('chatUrl', data.yzfUrl);\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 微信分享；\r\n\t\t\tsetOpenShare: function(data) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif (that.$wechat.isWeixin()) {\r\n\t\t\t\t\tlet configAppMessage = {\r\n\t\t\t\t\t\tdesc: data.synopsis,\r\n\t\t\t\t\t\ttitle: data.title,\r\n\t\t\t\t\t\tlink: location.href,\r\n\t\t\t\t\t\timgUrl: data.img\r\n\t\t\t\t\t};\r\n\t\t\t\t\tthat.$wechat.wechatEvevt([\"updateAppMessageShareData\", \"updateTimelineShareData\"],\r\n\t\t\t\t\t\tconfigAppMessage);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 授权关闭\r\n\t\t\tauthColse: function(e) {\r\n\t\t\t\tthis.isShowAuth = e\r\n\t\t\t},\r\n\t\t\t// 授权回调\r\n\t\t\tonLoadFun() {\r\n\r\n\t\t\t},\r\n\t\t\t// 首发新品切换\r\n\t\t\tProductNavTab(item, index) {\r\n\t\t\t\tthis.listActive = index\r\n\t\t\t\tthis.goodType = item.type\r\n\t\t\t\tthis.listActive = index\r\n\t\t\t\tthis.ProductNavindex = index\r\n\t\t\t\tthis.tempArr = []\r\n\t\t\t\tthis.params.page = 1\r\n\t\t\t\tthis.goodScroll = true\r\n\t\t\t\tlet onloadH = true\r\n\t\t\t\tthis.getGroomList(onloadH)\r\n\t\t\t},\r\n\t\t\t// 首发新品详情\r\n\t\t\tgoDetail(item) {\r\n\t\t\t\tif (item.activityH5 && item.activityH5.type === \"2\" && !this.isLogin) {\r\n\t\t\t\t\ttoLogin();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tgoShopDetail(item, this.uid).then(res => {\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl: `/pages/goods_details/index?id=${item.id}`\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 分类详情\r\n\t\t\tgodDetail(item) {\r\n\t\t\t\tgoShopDetail(item, this.uid).then(res => {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: `/pages/goods_details/index?id=${item.id}`\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 精品推荐\r\n\t\t\tgetGroomList(onloadH) {\r\n\t\t\t\tthis.loading = true\r\n\t\t\t\tlet type = this.goodType;\r\n\t\t\t\tif (!this.goodScroll) return\r\n\t\t\t\tif (onloadH) {\r\n\t\t\t\t\tthis.iSshowH = true\r\n\t\t\t\t}\r\n\t\t\t\tgetGroomList(type, this.params).then(({\r\n\t\t\t\t\tdata\r\n\t\t\t\t}) => {\r\n\t\t\t\t\tthis.iSshowH = false\r\n\t\t\t\t\tthis.loading = false\r\n\t\t\t\t\tthis.goodScroll = data.list.length >= this.params.limit\r\n\t\t\t\t\tthis.params.page++\r\n\t\t\t\t\tthis.tempArr = this.tempArr.concat(data.list)\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 获取我的推荐\r\n\t\t\t */\r\n\t\t\tget_host_product: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tthat.loading = true;\r\n\r\n\t\t\t\tif (that.hotScroll) return\r\n\t\t\t\tgetProductHot(\r\n\t\t\t\t\tthat.hotPage,\r\n\t\t\t\t\tthat.hotLimit,\r\n\t\t\t\t).then(res => {\r\n\t\t\t\t\tthat.hotPage++\r\n\t\t\t\t\tthat.hotScroll = res.data.list.length < that.hotLimit\r\n\t\t\t\t\tthat.hostProduct = that.hostProduct.concat(res.data.list)\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tlet self = this\r\n\t\t\t// #ifdef H5\r\n\t\t\t//self.getChatUrL();\r\n\t\t\t// 获取H5 搜索框高度\r\n\t\t\tlet appSearchH = uni.createSelectorQuery().select(\".serch-wrapper\");\r\n\t\t\tappSearchH.boundingClientRect(function(data) {\r\n\t\t\t\tself.searchH = data.height\r\n\t\t\t}).exec()\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\t/**\r\n\t\t * 用户点击右上角分享\r\n\t\t */\r\n\t\t// #ifdef MP\r\n\t\tonShareAppMessage: function() {\r\n\t\t\treturn {\r\n\t\t\t\ttitle: this.configApi.title,\r\n\t\t\t\timageUrl: this.configApi.img,\r\n\t\t\t\tdesc: this.configApi.synopsis,\r\n\t\t\t\tpath: '/pages/index/index'\r\n\t\t\t};\r\n\t\t},\r\n\t\t// #endif\r\n\t\tonReachBottom() {\r\n\t\t\tif (this.navIndex == 0) {\r\n\t\t\t\t// 首页加载更多\r\n\t\t\t\tif (this.params.page != 1) {\r\n\t\t\t\t\tthis.getGroomList();\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\t// 分类栏目加载更多\r\n\t\t\t\tif (this.sortProduct.length > 0) {\r\n\t\t\t\t\t//this.get_product_list();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.get_host_product();\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t}\r\n</script>\r\n<style>\r\n\tpage {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\theight: 100%;\r\n\t\t/* #ifdef H5 */\r\n\t\tbackground-color: #fff;\r\n\t\t/* #endif */\r\n\r\n\t}\r\n</style>\r\n<style lang=\"scss\">\r\n\t.notice {\r\n\t\twidth: 100%;\r\n\t\theight: 70rpx;\r\n\t\tborder-radius: 10rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tmargin-bottom: 25rpx;\r\n\t\tline-height: 70rpx;\r\n\t\tpadding: 0 14rpx;\r\n\r\n\t\t.line {\r\n\t\t\tcolor: #CCCCCC;\r\n\t\t}\r\n\r\n\t\t.pic {\r\n\t\t\twidth: 130rpx;\r\n\t\t\theight: 36rpx;\r\n\r\n\t\t\timage {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 100%;\r\n\t\t\t\tdisplay: block !important;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.swipers {\r\n\t\t\theight: 100%;\r\n\t\t\twidth: 444rpx;\r\n\t\t\toverflow: hidden;\r\n\r\n\t\t\tswiper {\r\n\t\t\t\theight: 100%;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\tcolor: #333333;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.iconfont {\r\n\t\t\tcolor: #999999;\r\n\t\t\tfont-size: 20rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.couponIndex {\r\n\t\twidth: auto;\r\n\t\theight: 238rpx;\r\n\t\tbackground-image: url('~@/static/images/yhjsy.png');\r\n\t\tbackground-size: 100% 100%;\r\n\t\tpadding-left: 42rpx;\r\n\t\tmargin-bottom: 30rpx;\r\n\r\n\t\t.titBox {\r\n\t\t\tpadding: 47rpx 0;\r\n\t\t\ttext-align: center;\r\n\t\t\theight: 100%;\r\n\r\n\t\t\t.tit1 {\r\n\t\t\t\tcolor: #FFEBD2;\r\n\t\t\t\tfont-size: 34rpx;\r\n\t\t\t\tfont-weight: 600;\r\n\t\t\t}\r\n\r\n\t\t\t.tit2 {\r\n\t\t\t\tcolor: #FFEBD2;\r\n\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\tmargin: 10rpx 0 26rpx 0;\r\n\t\t\t}\r\n\r\n\t\t\t.tit3 {\r\n\t\t\t\tcolor: #FFDAAF;\r\n\t\t\t\tfont-size: 24rpx;\r\n\r\n\t\t\t\t.iconfont {\r\n\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.listBox {\r\n\t\t\tpadding: 14rpx 0;\r\n\r\n\t\t\t.listActive {\r\n\t\t\t\tbackground-image: url('~@/static/images/lingyhj.png');\r\n\t\t\t\tbackground-size: 100% 100%;\r\n\t\t\t}\r\n\r\n\t\t\t.listHui {\r\n\t\t\t\tbackground-image: url('~@/static/images/weiling.png');\r\n\t\t\t\tbackground-size: 100% 100%;\r\n\t\t\t}\r\n\r\n\t\t\t.list {\r\n\t\t\t\twidth: 170rpx;\r\n\t\t\t\theight: 210rpx;\r\n\t\t\t\tpadding: 16rpx 0;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tmargin-left: 24rpx;\r\n\r\n\t\t\t\t.tit {\r\n\t\t\t\t\tfont-size: 18rpx;\r\n\t\t\t\t\tpadding: 0 26rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.titActive {\r\n\t\t\t\t\tcolor: #C99959;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.price {\r\n\t\t\t\t\tfont-size: 46rpx;\r\n\t\t\t\t\tfont-weight: 900;\r\n\t\t\t\t\tmargin-top: 4rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.pricehui {\r\n\t\t\t\t\tcolor: #B2B2B2;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.fonthui {\r\n\t\t\t\t\tbackground-color: #F5F5F5 !important;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.yuan {\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.ling {\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tmargin-top: 9.5rpx;\r\n\t\t\t\t\twidth: 102rpx;\r\n\t\t\t\t\theight: 36rpx;\r\n\t\t\t\t\tline-height: 36rpx;\r\n\t\t\t\t\tbackground-color: #FFE5C7;\r\n\t\t\t\t\tborder-radius: 28rpx;\r\n\t\t\t\t\tmargin: auto;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.priceM {\r\n\t\t\t\t\tcolor: #FFDAAF;\r\n\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\tmargin-top: 14rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.sticky-box {\r\n\t\t/* #ifndef APP-PLUS-NVUE */\r\n\t\tdisplay: flex;\r\n\t\tposition: -webkit-sticky;\r\n\t\t/* #endif */\r\n\t\tposition: sticky;\r\n\t\t/* #ifdef H5*/\r\n\t\ttop: var(--window-top);\r\n\t\t/* #endif */\r\n\r\n\t\tz-index: 99;\r\n\t\tflex-direction: row;\r\n\t\tmargin: 0px;\r\n\t\tbackground: #f5f5f5;\r\n\t\tpadding: 30rpx 0;\r\n\t}\r\n\r\n\t.listAll {\r\n\t\twidth: 20%;\r\n\t\ttext-indent: 62rpx;\r\n\t\tfont-size: 30rpx;\r\n\t\tborder-left: 1px #eee solid;\r\n\t\tmargin: 1% 0;\r\n\t\tpadding: 5rpx;\r\n\t\tposition: relative;\r\n\r\n\t\timage {\r\n\t\t\tposition: absolute;\r\n\t\t\tleft: 20rpx;\r\n\t\t\ttop: 8rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.tab {\r\n\t\tposition: relative;\r\n\t\tdisplay: flex;\r\n\t\tfont-size: 28rpx;\r\n\t\twhite-space: nowrap;\r\n\r\n\t\t&__item {\r\n\t\t\tflex: 1;\r\n\t\t\tpadding: 0 20rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\theight: 60rpx;\r\n\t\t\tline-height: 60rpx;\r\n\t\t\tcolor: #666;\r\n\r\n\t\t\t&.active {\r\n\t\t\t\tcolor: #09C2C9;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.tab__line {\r\n\t\tdisplay: block;\r\n\t\theight: 6rpx;\r\n\t\tposition: absolute;\r\n\t\tbottom: 0;\r\n\t\tleft: 0;\r\n\t\tz-index: 1;\r\n\t\tborder-radius: 3rpx;\r\n\t\tposition: relative;\r\n\t\tbackground: #2FC6CD;\r\n\t}\r\n\r\n\t.scroll-view_H {\r\n\t\t/* 文本不会换行，文本会在在同一行上继续，直到遇到 <br> 标签为止。 */\r\n\t\twhite-space: nowrap;\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\r\n\t.privacy-wrapper {\r\n\t\tz-index: 999;\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\ttop: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tbackground: #7F7F7F;\r\n\r\n\t\t.privacy-box {\r\n\t\t\tposition: absolute;\r\n\t\t\tleft: 50%;\r\n\t\t\ttop: 50%;\r\n\t\t\ttransform: translate(-50%, -50%);\r\n\t\t\twidth: 560rpx;\r\n\t\t\tpadding: 50rpx 45rpx 0;\r\n\t\t\tbackground: #fff;\r\n\t\t\tborder-radius: 20rpx;\r\n\r\n\t\t\t.title {\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tcolor: #333;\r\n\t\t\t\tfont-weight: 700;\r\n\t\t\t}\r\n\r\n\t\t\t.content {\r\n\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t\tline-height: 1.5;\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tcolor: #666;\r\n\t\t\t\ttext-indent: 54rpx;\r\n\r\n\t\t\t\ti {\r\n\t\t\t\t\tfont-style: normal;\r\n\t\t\t\t\tcolor: $theme-color;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.btn-box {\r\n\t\t\t\tmargin-top: 40rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tfont-size: 30rpx;\r\n\r\n\t\t\t\t.btn-item {\r\n\t\t\t\t\theight: 82rpx;\r\n\t\t\t\t\tline-height: 82rpx;\r\n\t\t\t\t\tbackground: linear-gradient(90deg, #F67A38 0%, #F11B09 100%);\r\n\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\tborder-radius: 41rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.btn {\r\n\t\t\t\t\tpadding: 30rpx 0;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.page-index {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tmin-height: 100%;\r\n\t\tbackground: linear-gradient(180deg, #fff 0%, #f5f5f5 100%);\r\n\r\n\t\t.header {\r\n\t\t\tposition: sticky;\r\n\t\t\ttop: 0;\r\n\t\t\tz-index: 200000;\r\n\t\t\twidth: 100%;\r\n\t\t\tbackground-color: $theme-color;\r\n\t\t\tpadding: 28rpx 30rpx;\r\n\r\n\t\t\t.serch-wrapper {\r\n\t\t\t\tmargin-top: var(--status-bar-height);\r\n\t\t\t\talign-items: center;\r\n\t\t\t\t\r\n\t\t\t\t/* #ifdef MP-WEIXIN */\r\n\t\t\t\twidth: 75%;\r\n\t\t\t\t/* #endif */\r\n\r\n\r\n\t\t\t\t.logo {\r\n\t\t\t\t\twidth: 168rpx;\r\n\t\t\t\t\theight: 60rpx;\r\n\t\t\t\t\tmargin-right: 24rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\timage {\r\n\t\t\t\t\twidth: 168rpx;\r\n\t\t\t\t\theight: 60rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.input {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\twidth: 546rpx;\r\n\t\t\t\t\theight: 58rpx;\r\n\t\t\t\t\tpadding: 0 0 0 30rpx;\r\n\t\t\t\t\tbackground: rgba(247, 247, 247, 1);\r\n\t\t\t\t\tborder: 1px solid rgba(241, 241, 241, 1);\r\n\t\t\t\t\tborder-radius: 29rpx;\r\n\t\t\t\t\tcolor: #BBBBBB;\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\r\n\t\t\t\t\t.iconfont {\r\n\t\t\t\t\t\tmargin-right: 20rpx;\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\tcolor: #666666;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.tabNav {\r\n\t\t\t\tpadding-top: 24rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t/* #ifdef MP||APP */\r\n\t\t.mp-header {\r\n\t\t\tz-index: 999;\r\n\t\t\tposition: fixed;\r\n\t\t\tleft: 0;\r\n\t\t\ttop: 0;\r\n\t\t\twidth: 100%;\r\n\t\t\t/* #ifdef H5||APP */\r\n\t\t\tpadding-bottom: 20rpx;\r\n\t\t\t/* #endif */\r\n\t\t\tbackground-color: $theme-color;\r\n\r\n\t\t\t.serch-wrapper {\r\n\t\t\t\theight: 100%;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tpadding: 0 50rpx 0 53rpx;\r\n\r\n\t\t\t\timage {\r\n\t\t\t\t\twidth: 118rpx;\r\n\t\t\t\t\theight: 42rpx;\r\n\t\t\t\t\tmargin-right: 30rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.input {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t/* #ifdef MP||APP */\r\n\t\t\t\t\twidth: 305rpx;\r\n\t\t\t\t\t/* #endif */\r\n\t\t\t\t\theight: 50rpx;\r\n\t\t\t\t\tpadding: 0 0 0 30rpx;\r\n\t\t\t\t\tbackground: rgba(247, 247, 247, 1);\r\n\t\t\t\t\tborder: 1px solid rgba(241, 241, 241, 1);\r\n\t\t\t\t\tborder-radius: 29rpx;\r\n\t\t\t\t\tcolor: #BBBBBB;\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\r\n\t\t\t\t\t.iconfont {\r\n\t\t\t\t\t\tmargin-right: 20rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t/* #endif */\r\n\r\n\t\t.page_content {\r\n\t\t\tbackground-color: #f5f5f5;\r\n\t\t\t/* #ifdef H5 */\r\n\t\t\t// margin-top: 20rpx !important;\r\n\t\t\t/* #endif */\r\n\t\t\tpadding: 0 30rpx;\r\n\r\n\t\t\t.swiper {\r\n\t\t\t\tposition: relative;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 280rpx;\r\n\t\t\t\tmargin: 0 auto;\r\n\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\tmargin-bottom: 25rpx;\r\n\t\t\t\t/* #ifdef MP */\r\n\t\t\t\tz-index: 10;\r\n\t\t\t\tmargin-top: 20rpx;\r\n\r\n\t\t\t\t/* #endif */\r\n\t\t\t\tswiper,\r\n\t\t\t\t.swiper-item,\r\n\t\t\t\timage {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 280rpx;\r\n\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.nav {\r\n\t\t\t\tpadding-bottom: 26rpx;\r\n\t\t\t\tbackground: #fff;\r\n\t\t\t\topacity: 1;\r\n\t\t\t\tborder-radius: 14rpx;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tmargin-bottom: 30rpx;\r\n\r\n\t\t\t\t.item {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\twidth: 25%;\r\n\t\t\t\t\tmargin-top: 30rpx;\r\n\r\n\t\t\t\t\timage {\r\n\t\t\t\t\t\twidth: 82rpx;\r\n\t\t\t\t\t\theight: 82rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\r\n\t\t\t.nav-bd {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\t.item {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-direction: column;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: center;\r\n\r\n\t\t\t\t\t.txt {\r\n\t\t\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\t\t\tcolor: #282828;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.label {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t\twidth: 124rpx;\r\n\t\t\t\t\t\theight: 32rpx;\r\n\t\t\t\t\t\tmargin-top: 5rpx;\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t&.active {\r\n\t\t\t\t\t\tcolor: $theme-color;\r\n\r\n\t\t\t\t\t\t.txt {\r\n\t\t\t\t\t\t\tcolor: $theme-color;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.label {\r\n\t\t\t\t\t\t\tbackground: linear-gradient(90deg, $bg-star 0%, $bg-end 100%);\r\n\t\t\t\t\t\t\tborder-radius: 16rpx;\r\n\t\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.index-product-wrapper {\r\n\t\t\t\tmargin-bottom: 110rpx;\r\n\r\n\t\t\t\t&.on {\r\n\t\t\t\t\tmin-height: 1500rpx;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.list-box {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\tflex-wrap: wrap;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\t\t.item {\r\n\t\t\t\t\t\twidth: 335rpx;\r\n\t\t\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\t\t\tbackground-color: #fff;\r\n\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t\toverflow: hidden;\r\n\r\n\t\t\t\t\t\timage {\r\n\t\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\t\theight: 330rpx;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.text-info {\r\n\t\t\t\t\t\t\tpadding: 10rpx 20rpx 15rpx;\r\n\r\n\t\t\t\t\t\t\t.title {\r\n\t\t\t\t\t\t\t\tcolor: #222222;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t.old-price {\r\n\t\t\t\t\t\t\t\tmargin-top: 8rpx;\r\n\t\t\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\t\t\tcolor: #AAAAAA;\r\n\t\t\t\t\t\t\t\ttext-decoration: line-through;\r\n\r\n\t\t\t\t\t\t\t\ttext {\r\n\t\t\t\t\t\t\t\t\tmargin-right: 2px;\r\n\t\t\t\t\t\t\t\t\tfont-size: 20rpx;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t.price {\r\n\t\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\t\talign-items: flex-end;\r\n\t\t\t\t\t\t\t\tcolor: $theme-color;\r\n\t\t\t\t\t\t\t\tfont-size: 34rpx;\r\n\t\t\t\t\t\t\t\tfont-weight: 800;\r\n\r\n\t\t\t\t\t\t\t\ttext {\r\n\t\t\t\t\t\t\t\t\tpadding-bottom: 4rpx;\r\n\t\t\t\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\t\t\t\tfont-weight: 800;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t.txt {\r\n\t\t\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\t\t\t\t\twidth: 28rpx;\r\n\t\t\t\t\t\t\t\t\theight: 28rpx;\r\n\t\t\t\t\t\t\t\t\tmargin-left: 15rpx;\r\n\t\t\t\t\t\t\t\t\tmargin-bottom: 10rpx;\r\n\t\t\t\t\t\t\t\t\tborder: 1px solid $theme-color;\r\n\t\t\t\t\t\t\t\t\tborder-radius: 4rpx;\r\n\t\t\t\t\t\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\t\t\t\t\t\tfont-weight: normal;\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t&.on {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.productList {\r\n\t\t/* #ifdef H5 */\r\n\t\tpadding-bottom: 140rpx;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.productList .list {\r\n\t\tpadding: 0 20rpx;\r\n\t}\r\n\r\n\t.productList .list.on {\r\n\t\tbackground-color: #fff;\r\n\t\tborder-top: 1px solid #f6f6f6;\r\n\t}\r\n\r\n\t.productList .list .item {\r\n\t\twidth: 345rpx;\r\n\t\tmargin-top: 20rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 10rpx;\r\n\t}\r\n\r\n\t.productList .list .item.on {\r\n\t\twidth: 100%;\r\n\t\tdisplay: flex;\r\n\t\tborder-bottom: 1rpx solid #f6f6f6;\r\n\t\tpadding: 30rpx 0;\r\n\t\tmargin: 0;\r\n\t}\r\n\r\n\t.productList .list .item .pictrue {\r\n\t\tposition: relative;\r\n\t\twidth: 100%;\r\n\t\theight: 345rpx;\r\n\t}\r\n\r\n\t.productList .list .item .pictrue.on {\r\n\t\twidth: 180rpx;\r\n\t\theight: 180rpx;\r\n\t}\r\n\r\n\t.productList .list .item .pictrue image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 20rpx 20rpx 0 0;\r\n\t}\r\n\r\n\t.productList .list .item .pictrue image.on {\r\n\t\tborder-radius: 6rpx;\r\n\t}\r\n\r\n\t.productList .list .item .text {\r\n\t\tpadding: 20rpx 17rpx 26rpx 17rpx;\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #222;\r\n\t}\r\n\r\n\t.productList .list .item .text.on {\r\n\t\twidth: 508rpx;\r\n\t\tpadding: 0 0 0 22rpx;\r\n\t}\r\n\r\n\t.productList .list .item .text .money {\r\n\t\tfont-size: 26rpx;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-top: 8rpx;\r\n\t}\r\n\r\n\t.productList .list .item .text .money.on {\r\n\t\tmargin-top: 50rpx;\r\n\t}\r\n\r\n\t.productList .list .item .text .money .num {\r\n\t\tfont-size: 34rpx;\r\n\t}\r\n\r\n\t.productList .list .item .text .vip {\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #aaa;\r\n\t\tmargin-top: 7rpx;\r\n\t}\r\n\r\n\t.productList .list .item .text .vip.on {\r\n\t\tmargin-top: 12rpx;\r\n\t}\r\n\r\n\t.productList .list .item .text .vip .vip-money {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #282828;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.productList .list .item .text .vip .vip-money image {\r\n\t\twidth: 46rpx;\r\n\t\theight: 21rpx;\r\n\t\tmargin-left: 4rpx;\r\n\t}\r\n\r\n\t.pictrue {\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.fixed {\r\n\t\tz-index: 100;\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\ttop: 0;\r\n\t\tbackground: linear-gradient(90deg, red 50%, #ff5400 100%);\r\n\r\n\t}\r\n\r\n\t.mores-txt {\r\n\t\twidth: 100%;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\theight: 70rpx;\r\n\t\tcolor: #999;\r\n\t\tfont-size: 24rpx;\r\n\r\n\t\t.iconfont {\r\n\t\t\tmargin-top: 2rpx;\r\n\t\t\tfont-size: 20rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.menu-txt {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #454545;\r\n\t}\r\n\r\n\t.mp-bg {\r\n\t\tposition: absolute;\r\n\t\tleft: 0;\r\n\t\t/* #ifdef H5 */\r\n\t\ttop: 98rpx;\r\n\t\t/* #endif */\r\n\t\twidth: 100%;\r\n\t\theight: 304rpx;\r\n\t\tbackground: linear-gradient(180deg, #c9ab79 0%, #F5F5F5 100%);\r\n\t\t// border-radius: 0 0 30rpx 30rpx;\r\n\r\n\r\n\t}\r\n\r\n\t.stats {\r\n\t\tposition: absolute;\r\n\t\tleft: 0px;\r\n\t\ttop: 0px;\r\n\t\tz-index: 2000000;\r\n\t\twidth: 750rpx;\r\n\t\theight: var(--status-bar-height);\r\n\t\tbackground: #ffffff;\r\n\t}\r\n</style>", "import { render, staticRenderFns, recyclableRender, components } from \"./a_seckill.vue?vue&type=template&id=7d45776e&scoped=true&\"\nvar renderjs\nimport script from \"./a_seckill.vue?vue&type=script&lang=js&\"\nexport * from \"./a_seckill.vue?vue&type=script&lang=js&\"\nimport style0 from \"./a_seckill.vue?vue&type=style&index=0&id=7d45776e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7d45776e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/components/a_seckill.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./a_seckill.vue?vue&type=template&id=7d45776e&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.spikeList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./a_seckill.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./a_seckill.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<!-- 秒杀 -->\r\n\t<view class=\"seckill\" v-if=\"spikeList.length\">\r\n\t\t<view class=\"title acea-row row-between-wrapper\">\r\n\t\t\t<view class=\"acea-row row-middle\">\r\n\t\t\t\t<view class=\"pictrue\">\r\n\t\t\t\t\t<image src=\"/static/images/seckillTitle.png\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"lines\"></view>\r\n\t\t\t\t<view class=\"point\">{{point}} 场</view>\r\n\t\t\t\t<countDown :is-day=\"false\" :tip-text=\"' '\" :day-text=\"' '\" :hour-text=\"' : '\" :minute-text=\"' : '\" :second-text=\"' '\"\r\n\t\t\t\t :datatime=\"datatime\" :is-col=\"true\" :bgColor=\"bgColor\"></countDown>\r\n\t\t\t</view>\r\n\t\t\t<navigator url=\"/pages/activity/goods_seckill/index\" hover-class=\"none\" class=\"more acea-row row-center-wrapper\">GO<text class=\"iconfont icon-xiangyou\"></text></navigator>\r\n\t\t</view>\r\n\t\t<view class=\"conter\">\r\n\t\t\t<scroll-view scroll-x=\"true\" style=\"white-space: nowrap; vertical-align: middle;\" show-scrollbar=\"false\">\r\n\t\t\t\t<view class=\"itemCon\" v-for=\"(item, index) in spikeList\" :key=\"index\" @click=\"goDetail(item)\">\r\n\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t<view class=\"pictrue\">\r\n\t\t\t\t\t\t\t<image :src=\"item.image\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"name line1\">{{item.title}}</view>\r\n\t\t\t\t\t\t<view class=\"x_money line1\">¥<text class=\"num\">{{item.price}}</text></view>\r\n\t\t\t\t\t\t<view class=\"y_money line1\">¥{{item.otPrice}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</scroll-view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tlet app = getApp();\r\n\timport countDown from \"@/components/countDown\";\r\n\timport {\r\n\t\tgetSeckillIndexApi\r\n\t} from '@/api/activity.js';\r\n\texport default {\r\n\t\tname: 'a_seckill',\r\n\t\tcomponents: {\r\n\t\t\tcountDown\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tbgColor: {\r\n\t\t\t\t\t'bgColor': '#fff',\r\n\t\t\t\t\t'Color': '#c9ab79',\r\n\t\t\t\t\t'width': '44rpx',\r\n\t\t\t\t\t'timeTxtwidth': '16rpx',\r\n\t\t\t\t\t'isDay': true\r\n\t\t\t\t},\r\n\t\t\t\tspikeList: [], // 秒杀\r\n\t\t\t\tpoint: '',\r\n\t\t\t\tdatatime: 0,\r\n\t\t\t\tstatus: 0\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.getSeckillIndexTime();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetSeckillIndexTime() {\r\n\t\t\t\tgetSeckillIndexApi().then(({data}) => {\r\n\t\t\t\t\tthis.spikeList = data ? data.productList : [];\r\n\t\t\t\t\tthis.point = data ? data.secKillResponse.time.split(',')[0] : '';\r\n\t\t\t\t\tthis.datatime = data ? parseFloat(data.secKillResponse.timeSwap) : '';\r\n\t\t\t\t\tthis.status =  data ? data.secKillResponse.status : 0;\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgoDetail(item){\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/activity/goods_seckill_details/index?id=' + item.id\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.default{\r\n\t\twidth: 690rpx;\r\n\t\theight: 300rpx;\r\n\t\tborder-radius: 14rpx;\r\n\t\tmargin: 26rpx auto 0 auto;\r\n\t\tbackground-color: #ccc;\r\n\t\ttext-align: center;\r\n\t\tline-height: 300rpx;\r\n\t\t.iconfont{\r\n\t\t\tfont-size: 80rpx;\r\n\t\t}\r\n\t}\r\n\t.seckill {\r\n\t\twidth: auto;\r\n\t\theight: 420rpx;\r\n\t\tbackground-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAArIAAAGkCAMAAAD6wDapAAAC/VBMVEUAAAD/8e3/////////////rZX/////bF//////cGP/em3/////lID/noD/oYT/////qpD/qZD/qpH//////////////////////Pz/MTH/dk7/+vr/9/f/bk3/Zkr/aUz/cU7/9fT/e1b/Y0n/dlL/YEj/8/L/XUb/7u3/V0T/ST3/2NT/3tv/zMf/RTv/0s7/WkX/eFT/a0z/UUH/TD7/uK7/TkD/8O//6Ob/Pzj/7Or/o5P/29n/yML/clH/6uj/v7f/0Mv/ysT/5OL/tKj/Qjn/zsn/5eT/tqv/4N3/vbb/w73/2tb/wrv/sab/rqH/vLP/qp3/NjP/VEP/fFb/x8D/wbn/knn/r6P/rJ//oI7/oZH/OTX/pZX/j3b/PDb/ubD/xb7/p5j/U0P/urL/qZr/m4f/nIr/4t//mYX/1dL/1ND/loD/nov/ln3/mIL/lHv/jXT/hWf/1dD/gF7/jHD/i3P/hWT/iGr/h3D/im//iGz/iHv/knz/fGf/hHD/b1z/j3n/f2r/gmz/ta3/gmH/bVH/s6v/fVv/hG3/iXH/pZj/aVL/jnP/kof/oJb/bVn/d2T/nIL/san/nZH/fmj/i3j/Wkr/lor/jH//k3//dVb/mX//gG//cln/rqb/ZlX/n4T/cV3/lob/fVj/V0n/dVv/nY3/dmL/c1//emD/cFX/YVL/oob/koL/mYv/j4P/fW//gHP/lYP/cWD/Xkv/gmT/fV7/hHf/pYn/j3//mYj/pZv/h3f/fGz/alj/emT/rKP/bVX/bF3/hHT/eFj/eWf/jHv/Zk7/sJL/VEf/p53/dGH/aVb/fWP/Y0//gWb/qIv/tJX/bmD/rY//elv/m47/hWr/alv/ZVL/XU//YUv/o5n/YE//f1v/eFv/UET/d1//qqH/f2L/W03/iHT/j3z/cmT/eWz/eGn/Y1X/aFn/qJ//uJj/mY//opb/qo3/dGX/nZP/dWj/gmn/iGf/imz/ZVj/eFD/u5r/gl7/kW3/mnf/oX7/lHNPmOzmAAAAF3RSTlMAOU/hz4XZx8K1hWcg1cvIpaOcmJCMiMnv6MkAAEZ+SURBVHja7J3Pa1xVFMfHhYiiou4SmDTElGIX0sQiuJmd/0AbyEbSRfchCU2bNhBTS4e0dCAViosZQi3jkB9mZxO6MgRGKsQ4ikOswsQSXEnBRRNq2ga/596bue/XnfvevPem96X53tv33hh/QP3w7Zlz3jk3Vdcbvx8/c6rNIJ3y0Gd2nfnsjEWfnPnEpk+x7frQQ8c/PO7SyeMn7frYqo+w7Tr70Vmu02dPYzN9gO2hEyc+wLLpGNaJY146euyop94/+j62Wx3s0oHt1hEsutH9iEu92NAR2lalsXrTuCrVWd8QLi61h1bfhdzbr6aceu2zNqN0CsS2OXB1EmullXTGzqseV/DqpZOS2I+xIBuvH3nyeppd9vUBLZvAKbu5dUyBK/GqJNaTVxDrxaok9oi3eiWxktV9YjVirGKrlenMtEeg3XdtwL7+SZtJOuUpO65YsfgrlstfT2r9FbL6qwTWya2WV6FGvMbkr7jbpfdXkuA1reSVViTQbr9qIdakgICZawB/PePyVyw9rtgaf4Xs/orl5BWLggFcpFy4qvz1mNJfFcSqwoEOlcM6Se1w4SqJlWrGX9MKXoWiYbbr1TqxbQaJEG3TR7CSWCevnsB+6gLWXwQr/ZW2i9eP3P6qiGAD+as6glX7ayOHldTigxVYCLR68Iqtk/RXJa9YBCtWRMw+Ecy+YYrHBvJXSLIaPoIFoC5eNRFsDP4KYGPxVynBJ67yyYGrT3/VRQRSHNvuSHz2lRTJlDiW+WuYDAHkBvbTeDMEUmH9FQrqr9oIFsA6uQWP2d5slsBs2l87/furWN2kSOJZlitoM0GM14T5K26N/PWEp7/SDuiv3sTq/RV3KTyDUMCaySwsLOQBFLh1+yuuGn8FptpvXKR26a9C7VHonVTqTQOyW3H4KxSzv4bKwMbvr864AMCmAWw+X96EyuU8sAWkzu9bvVp/xVX5fcvmsJxXOGyUzO6++eJN1g0rOWxr/NWegQ3hr3FkYIFrE/6qzMD2wmEX8qXN5eWtratbW8vL1XI+05lNywysXpJUvb/Sg8A1Upt9L/VH2wuUoLXNgazZFa6z+gpXFBnY9yPOwPZmMwulzfmtpZGNMWhkZAnQwmizvipcUAh/jRLZt1LHQwDX4gwsFDwDS9JWuHxnYCFNhiCYv0IBIwIVq3RpUOEii10oVdcrG+OXRnOk0R1AC6NNR5mBdfhrDMw+yaXOtLVekte2kBUufQYWS1/hOtlMheu0SRUuC7WeQUEvggJYbGX8Um6gf2ho8NnQ0N7o2Mi5KpjVSFvh0vsrFGHOwC9iCczAKitcRmRgW1ThkiZbml9/PH59eKB/cLBQ+PLLQgHQMmY74/VXqZ4ITLa96BtZsypcuGj8FbzqK1zueEBf4YJaX+HC8lXhcrNa/+Y1szlfuXLx/EB//yCA/frrr78sPBPMRlLhwuL+qiY2Epd90nJkX6oK14lWVbgavKPFBGJhsl9cu3h++PIQR3YR0A6C2eWyy2bld62AFa5GuPZAUbhsux7Zwwxs4ipc1ne06iZLcQGQRWDAmV1cXCRmd5YoNAhZ4dLwSsgytYeXBtnY/RW4tjoDCxmYgQ1f4VK+SyCQLT1cmQCy188PXLYy+yy3fa7cnXFmtIJVuPCgwzWJyDJ/TVqPwekD0mMgkP3iCpgdFswiNEBsUIDNTuUzktewFS43sMlE1keF67OY/NWZgcXW+2vSewxoywuLC+7Mza7ccjNbGLJFs53BK1x6XJOHrKhwJbnHwEsmVrgcPQYS2dWFGSA7AWY/v/i3ZJa+geXGkDTgzgrhHqm/JhBZYypcUMwVrkh7DLCD9xj0KnoMsrDZyTtrXszSF7Cr+AIWZQZW4po8ZE9F0WMARdNjoPXXmHoMoIh6DLT+KnsMnMiuTk/Ord0kZq99ztIG/UOoJ0D7OQPpr17AhvZXUpfpyLaox8DoCldMPQYKYhU9BoTsjZ+mJ+94MVsgZKd6Mhp/DZyBdfNKy2xkg2YI9BUurx4DBLBRVrigF1zhaioD27iHiyP7PZj9btbO7GABVdscSxmEysDq/ZUvk5FNYo/BaU0GNsIKV0wZWEWPAZ5v3ODMzjFmr1z7/LpgdnBwKDd2FchGV+FSE2suskmtcMkINoC/njCiwqXs4aILVcCA7D6zP9iYHeofHduqdndGkiFQ00oyFdlTp4zoMTCxwkU7Fn91DSWy9RjgA5B9cPu2B7NQ7tIIlb9CZmA1/moustxf25JV4Qo4RUu2HZqWgfWcooU7/U337z94cPunf34XzFJJgZgdGMiNjuFFb7hsOtoMLGiVvJqKLEc1/ilaHx6sKVqaHoPQU7TwWSD7gPusZPb68PDw6PjIFnJckWdgJa+mInuqLfEVLpN6DKKdotWrYBZGe2kc3TS2uCBafzUV2RimaH0a4RSt+DOwwNXoKVr0BuL9n392xrOAdnwDTYs9QDZyf8UyF9noegzCV7js8etB7TEIPOWlF8iC2W8ls/Ra15UrG48rW1WYbHM9Bpp4wExkX64pWq3qMVCp6Sla4JmQrfvs3Brz2Vu3KutoDBcmG02FS01sH/aLRzapGdgE9xho/FXjs/V4dm12dmVlfX19ebOc7wag0VW4lOrDajmy+gys6f6qzcBiGzNFy7vHIPAULYEsY/Y+Q5bFBmsPH87Pb9aJJbWHr3CpcAWvtKJB9rDHwNwpWhp/9TtFi0swizrY6jSYLZVKDwFsvrsbxHZGnyGQ4rRGGMsmfYpWsnsMGK56fw15jkHdZwEtXjdYXV2dgfIAFsRGXeFyR7B8RYds0qdonTy4U7Q0Fa4AU7Rkfha1W3p7luZ1QsRrS/wVN9ITrvYnoZBNZo/BSzBFC5xGd47BPsTZdJYJxGby+84aRwaWKBXMFvuK++LEhgQ3FW0EG8sULSjOKVonjJyihQ2FP8eAJMEV2FpTWcxgn0Sdge3bh7YI1aamarVzUJHUB+FHHNsmuE0lIgNrRo/BMRN6DEJOKUzbMrBx+yvEYN3dvXr1ArQrVKvViFzCtglkzalwMXs9WFO03m9hhUs/5UXKGhJE7q98YQteidbt7e2xnZ2dbSHQy8Cth7eBkD2cohV9hSumHgNsqSanaNHCjikDS6iKG3idgr0yXAHrUyi391SKgQtsi/BaUgBkD3iF64ODVOHSZmCxNT1c/ExZ4a8hegw0GQIQC38VvI4+zeX29vYw0vbZvvYgxi1RK6zWN7KHPQaGTdHq9apwaePX4FNeGtAaqsKFRREBDJbzSrj2Dz0rQF9SG/pzLg4up1ZA6xfZwwqXkVO06sBqQoKAU7Tiz8BCRCyAHRnbwYz7AfDKZzCz6TSLdX3NuQW1dWjDIavPEJyJ6aTuk0nuMQgxRStohSuuKVqhKlzYLIa9ujQyNk7A9g9JYInXe0y/kO7dW1wkagW0xT6fqdrUAT2pO8FTtKI8qbu1/iqIBbAXxsZ2CFjhsJJYweu/XOBWUCuc1hey5la4AkzRMuGk7hBTtGI7qTv+DCy2WILYmrBYAGu1WDuyoPU3LkYtoIXT7u4WfUUHKU+HTchJ3cmfohUmAxv/FK1g/ookK4JYAnaEWayY7VHgzHoi++OPP4JaCW2NZbx0yBpT4XpJTuqOqcKln6IVo79KYqeIWDpabJhN9pDIMpeFXMhyagEtjw6KfQGQ5bxGN0ULimaK1oHsMYizwtWqHoM+R36rOLVsIRbI8sBAbbM/1gVonz+H0fphNpXkCldTU7TMycDiHstJ3Vzt8VW43P6KS3VqanlriRF7noi9LJFV2Kxg9leIQ0tGu1vTBbSpOq8JrHAFn6KFZe4UrVhO6tZUuEJnCMTnYh+IPbcFYq9dui6R1dssESug/eX5c86sBtnDCteLnqKVph19hSv+HgN8gCzELlUeb2AKjQJZlc3+KsSNVu+zKTCasAqXnx6DJE/RUvFqVIXLQi82IxZhweONcRDLkA1sswQtY3bnQmOfTSVmitZH2goXFNxfk9hjEGsGFvKXIZCf0HdAxFYqG9eucWQD2KxA9tGjR2S0vyCg3W7MbCq5Fa4kndQdXY+BCRlY6a/Y9KvIPfYxiGXINmGzjwhawSz32ScaZHVTtJyKZYoWdPCmaDXIwBrmr74zsJC4CZelZMFW5THGfH1OzJ7X26wbWa5fGbMNv4OljDqp+3CKVtiTumPqMXDlt/Akv3uRya5XbklkPWz2S43NPrIy+3S7QX42lYwMrOlTtLDDT9EKf1J3K/2VxNAtViksuIUjxQSyOpt1I+vJbJcK2WRkYBNZ4eo1psLVHVWFS2BMkoFstbq8vn7rC0JWMhvEZiWz//1HAS1nVhHOAlmz/dXHFC2jewyOhOgxCHlSd/gpWm5/xRIST/ghiJ0HsnDZ8DYLZrnP7j2lr2DeLmtyBvYFTtGCYpqipYkKjOsxsGcIBK3SZ8vVanV+fn1i4gtms4GiWVlOkMhCYPbec/4VzBPZwylaJk7RMrHC1eeQqCIQsusrE3BZYbNN57nqyD768d8GoUEqCRlYI3oMmJvihl8vZYWL5IoMurqYya5M0IBlnza7qLVZEc4qKgqpwylafipcHEfikH/XSniFqydghctL4qcgFsiCWTuyf4exWREaKDJdKYNO6j5rbI8BcOTT1/CLwFX564HqMVD7KyS/e21akOV5LofNKl/1VtkshNBgEaFBzRPZwylaugws8chmBeZLmG+5kMlmX3yPQcsqXEpxusvVzYcPH66s/CCRDR/Nknho4PkNLHV4UrduihYDdqYEN1lfn1/eLOUX4LQ2WhPeY6CocOnIZchuAtnZWRez0mYvN2ezCA3IZne9kDUmA2vqFC1GLIDdqoxsbIxUlpY3iVm/U7QMqHDpegy0/trlHclykyVkb95kyIaOZiWyYFbYrLsGljKnwmXmSd2c2Pn1ysb46GhudHQHh7uV8xTSRjdFK/4eAyhIhgDbSqsa2TKIZciCWVU029+szd4TiS4nsi9Pj0FTFS4IUcHD9cr4JTb6pH9vdLyyXFoAs4opWgnqMfBT4VKLEbtZYsjeFMhGarP/ks0W3ci+8ArXWYOnaDGTXZ0pzVc2rp8f6CenGOzPjV9YzmeyaTlFK2gGFjsBGVipLtWXrzKOtVlbm2WRgc1mL1qj2f5oo9lUcH89WCd1N5qiBQmTfXzt+jAGSeB3vFAYyo0v4XDtdDRTtMzoMZD+6lf7yM7NgVkZGUQcze55RAYpYypcJvUYIO8q01sw2QlCFi7LTKIwNLqxVc6kew/IFC1NhctbIpIlZOeEzU542OxwUzb7n7RZjxJYyneF63iUU7ROmzBFS9djgCeO7OzEF+hpGr5MNgsN7u3AZrPpg1Lh0mRg1cSSyUpklTZ7uUmbFSWwPqfLtnKKFlYsU7SwopyiRayKKwtl52ZXcBT8RQSznNlC/+gIotl0rD0GUPwVLnePgW9ke4BsvjRzh5C12+yVsDYL1b+A7e3sulw2QRnYFk/R4k83VqfvrN3EUfCfX99nFtHsGCKDbOAKl+k9BngO5LKELGN21p3nCtdRY48MHMj6mqL1kvYYdDBkVyfvzFqZLTBkN/PpJFW4ND0GWn91vsGF/xIOFJ25MwlmI7dZWbW99/zpthtZVGKFfH7dIjWMXyFPf23orqr2AuiEOw6Agr1LKOIABakNdINsds7K7OBgP0M267+yBSlJdSoApEI9ilOMXMQV+cKG+LMQnlyy/RQLwoWe2L+9B8TOgNjvvqsjq6naBnvVW+YMik9sSllw1SMr3fVjTTTg8lcNr9pslqSV4AwYDWgmZjRG9sb0JJhdqTOLesLoBkNWf36BT1q72XJiKpeXpTZUsYuz1iUWOHPxGEySXOHKPXlCdhLIUmQQh80iMqBqwoVilx3ZANlXx4GcdlgpO+AVCTh6ubH95LG+YoRKYgHo3aPH7lrg/JMWbtDR93HxlXelo7I7sOiOC24N3iHE5sj+9L2VWfzu5y4B2YUMY5Zfsmlandks89UMNmNSJxWnXHksyIpiub5I1bJYRGUVna7VKcnXVHFKqMiWQ/a/VuOfa9j0TLdirYgL+2c9RDlZigsmZWTgjGZDv+oti7Y9GmTBq66+5QTWzSt+Ea86d1VN1HS878JplQKrglbi1QvY+1jOaOAIaMWFePWiFQKr2V7rm1kgFvp+eloyOzwwfH58ZH4z3ylsltOKC65pW66qMwCtUl4RqkRVolsVnwWv2JIowakHrfIn2KSa+AxM5bOk1Vt9lOKCyULkswGi2UIAm30kIgOy2Z76Svnz1+O+/fUv6a9YWn+1AfsV+SsnltnsXbrgdpeIvcuoBaVscQFXIaKWk9vxsy0OYK7KVH9izLqV5aTinu3NkkAsIfvg9k8//b7vsxfx/2B0fGOpSu8gMqXpAn/NZLKcUb2/dmPhpO5utoR69m/cX8suCT9ly6YpvgBYM6qJG23xLJ6k6nzvU4yOLxBbRxY261W11b6DqLdZQpZe5+qxKBWsFusQsaqgVZPJIkCtsBKt0lbBKC4QSAWr0lMJTckoPXvkWu9baO0QjHpSCiLpIhiFcHeKMXtbxgaYOjW+Udmq4l3vTNrPn/x0cUaqHFVceuwq9wjjxA2S3gpMi3xxKfiU7GHFJ8QMRQQGMzPT09MUGfiNZof0I5Ils5ZgFgcsWcKjlJpWMteG7kqUYtHt7F/gdV82d7XrGx4HfCNAZReI/PWrr+CtjFO6SB398+6fNrn9tePPn4lO6aRucWZvsIXNVxa4Qrg0EEP29vdgdu0mMXvlyuPK1jJMVsequNYhXRCOihuT9Neyh6pW+fVKPEDnasvnls/V6gtbSHyG3J9rfGGLZ7XIb6ul0szkNBTCZjWRgXw1BoGIHdnjmncJiVa3v0pWBa3671rEKi6SVsGq/PNfQ6vU/7ydTa9LURSG738xYiz+ih9gbtD4KCYmBCEpiRiQhptoKGY+YkQqEhqtEkKvUA0jJgYYIOJZe6+edc7+OA4tz1pd+/QiBh5vV6u3Hhcne8Djx6Zm3lZuDDkkV5tyXJyV3UBfN+CNzJN3/dn9nLJ8+Syeoqg88nN6V81PKSBRqbKjpbLH+7yjlF4stMPTI+rgMrypGu2aMtzvLMoi7D0fs9eJ2ZVvs6YsMWusxc+1tlZW14Svkq3buMGOt4iaeJfLMMhWyqvKcUaqwg06BFnppKy0DM8DKmfsceereOpuFA/zYmEzbDNwq4FzlqfF435/NuObwJDT9VkkBRl65rnvC0JX61P1Vrlg16L+H2YwzqLsDGXh3sF/E7Ng32kbKBuhwm7bsi329a26CjtoiJ5pDfE1sPXiRVxFzdBVFTYmY+sDXDVwNW8qg5YlAJyrf2LrFSnpQlpe6hqIs/N5H2N72IerV/D2bDMaPPhbJZR1NPL0UOUO7evbrm/0oW+HuObgmpLBfTlo/ZpdgZwROGvKlrbZk3HMLvXZnSj7EGW/7t3F8lwQKCuqFquAHAayYiu+xunqQdUh+eoOGGIqtp7hQNckN5K6xmAqrWySgawZY/E0wWEadGQ9FdxpDK4M+MUw4KWu7npPmHXPNtY1TlWY3Z+lnvVbpAY0MRYXtei/QHT2qMUy3KnuGizLLAZdlL2DtLUxu9Rbvf1HyomyRxLKEq0MtZUhihrOVkaQr7a/DoeFq2arusoIaL4L6AZgO4DaGoGoGVvBBWXO0wETMfPg7ABjodPt9hxm5JWcqdwiTZ2qs1SyQkNHLUXVTI5/CSY7RGDNX+HW85lX9qVts/Ux2/it3uqsKcv/BkasF6zZUy1U5VYRlSJWlThdnaewWcqNi8oZqSSYSjfYA1ywGjWpysijoco0cNAd5GkzBgJnF2fP9tJZygBGQNLTW89tMw0lFd65MjQ/G3CT8ux1dXPvzb0g13wJ3DWDWb4CuwLOrMGqbAdl74iyGrOXVv0dNcUHdx4qP/tbI12r30rwVl3lzNhKsKqr2EpTFwlWRfdVjma2vn716jU3wFHvamTrNBR1OpVUrWOApWomhjI8V2hg5Dl7pXu26zlLcV9BWcnZ+87RXuxmcWp5VY2ax/t3t97BrqIOacVCGv7OXlcyHHKxag5JV23e9XwuIQudTMwu/9mdpuze8lqy5p5nbX27JUxXWQVMV3wtdDVfkZV5EWMtXZvvAearGisVInoWnvpxXHzNGXvYdSVLy76KsXm61NnC2LO0IndEWL7QoxB2YSyHGSvDu4qtvgyCNY0YCi5VVddkdlJ0GVWVWrGg3Cg9YmPhFrusKGsxK8qu9Dtq8spGrkaor8PhDkpsNXy8mq90YCojzesbr0s8pmOmj6dT0RRwFVXpDIPBcYozCa7WmhrgHKU4ZXHV+z25Q8j2hNmMzvJbUd8tqoImK2aG5E1tUQUt2hft0Pt0cU/RawZdi/6N0DTHWZTtu5QduZi9F8Xs6SU+u9OcNWUPlZXN+Wr5Onw7dMZCxVdkpavGxq8IvE/qGvia1FVRY8XXpKpUYWzSVdGVzrga+oqnVWOBTAV0lZN9dVYnK2W+5mSNUVWpGlH1wmHGmofmqFE22H6uXpvlf2bsrlv9maYsMfvn/2q7s9m/2j59+iyvLLYmGJbzNSDwlQ5RWzn+wlfayOXrgHa25hFbmYGqtIwkIiqq0jJiZnWorn0+wvJWP5B1bMkacdNXnsnNCbQmrdZNVyvFkphOW2t/b+T53TtRFmNHI9tmLwcxu/SHylWUPVSwJrom0xXwNEZNNVlT6fo+UvW1G8BQYlGlAjCVzuRrDWZqyU+5yqJ+MmNmdFJQcdRNUEmbBetvBRUvJ67+ki92RclhV5UfzTisSZy2911/3juIsaNSzF5f9Vu9uT277ZUFU1Yd5TRsF6hJVlylAlcrtm7YoaKarXS4tTaztZqqo8ze2jFbIW8pZC2dGXlbefTvu0JNc7Y/vkWpnE1VxYbJXhxtaqO69+JLq91qc7S/QIujTbtS9JpR/mprcVKuKfNaMWc5TdtDTtnOCGybXXnMssqi7M/vbX5DYy1eBYZUNluNcAug378PZSVTa7aAKR1zPr+10nW56jwdiKedOktFUTf+xNQ+JY6KnehJ913r4MBTMZVEberpxJdKypEHKR0v1MZl8WLrVR61V0/dicVZNgNT1mJ2Zdts5T9ibleivqwstr6lIJeuhuXrezeAcN24sbGxocriakzZ1rSv588fPx+4OqIGo9GAGzNNh4YrXQa+Smd8BUaaGT3ng9Fmc5jN6FkfVxngTgVDgcFNinaxSsW2mp20lpLX9EXrRUEbYV+4+jd8MY9p+d1cBFvqBsiTsLEoe2d0Xpx92emsPmZV2WukbF7ZlKsRpiowlI0NuiBW9fXVx4+vclNfA0/dEMROGHEyAmwN6OCnjAIz9KA/YL3YUNexkUGnmRtO1j7dp+sZSwGW6lGWdBIRKGkV0dYqKXqgfYC2olN81YOmPHrV9tf2qxlUQ1raLYVltrfeGfFHFsTsSt/q/fDhk2vnfu7++oVwL1gzX+nI1+xzLbHVEF+NOl+n2W2AeDVbOUPw1YSlBuYrwlJiq471cqrSZm2JOeWm9Ey6wHzNM4ayqnpOfKSGtHx9XpSJm0Ej1R/mKnBAJO3XdmhuxdFlbG2prYZ7/rXuUtY5u4jZy6uNWf7j8As/fu4m9EusJdM1dnVhasgGMGJZr7qh4CrOTqd0oShNeQJBeYWaAQxeSRE76TL6fvgOJWDqOqLKTaacQm/9cg9KQ+ycX56nIVmzeaptmLGTd64iPkspNW7CgUXVst8Xrej9JF8puy59VTmgV97uxdnWpnwHvNDJp5lPxvN1XjIgZZeM2QvZmNVVlpRtV5T9o21AdgGqwS4Alq1TYBjOVg6Qv6Yx2Dq4M8BWTdUQclU8hW6BF1ZyFVlT4GuCfi2hp+9oreLVVFtPLVOxNd5L4yA1so/0lJjJUWj4Vcvul4h/ZHfVVq5qaH9tAzPJFz8lZ1lmRVnQbXblMcutUPaLsVbvquwB7+kIU3XDFKWlKlQsLTGiLVVBMhU6bnTogIMgQ+GjdQou68Ejv6bpZU4mqdo4T41xFt1aNVLjx37y9IWvJJJeNflZy25qcUFLNYOfSVfu60nRajStZvvWBBaHNXkrELOyGaiyErP3Vh2z8OzTtVM/TqBsmbWabeAMDbW+loWlOEosFgHLVMNsdThf1dakrx3KbGVAd13hCm8LYWkgVS8nt4A+XeMrXUNmC4DPHvM1TlVVNqPt71D5VFolZ645aT9H74S2UnqWQFbvKyQ2BF6SG8syex5GH1Mxu/z/q4Syt6+d255QFlNjbHPNP8vacJ5KyzA+SE8/fAgtTewBdwrwlBFRyVUZ1XS9vABJjUdNTb17t3+X/4B1zDd0MZNMxhPQaXr6UhLZevTF0bKQ+yhzM52qlqKOYybfnsWx58SeExxSep9xYvcJ/yMnuKKAkb6qT2NgGhq+Xt8DZUTacf9ysRng7DLvQYxjVkP29gWn7IEya2lf6Xy4Wr6qrRwGtjKcr1Mq8DUhbP02YGBrxVcIfZ3rnEfcTWwD3leE7ed9RVU6xnxNhKs9kzoq1XAL0ETUMgo7xU+zl6a8rYzIUdCrPKnUNVlp2xAC2r+YO5vXpqIgjua/zC5LQRBtFhaeYmyTlQXBCOmiWFBBSEUXYjZurFDRTRRFweBOMH6uxEK3nrl38ib3S/uMimemc5+KGuX057wY45vDF4/2NnQzsG3W3u0o2Wabv3fn65d3ef/py+ciZTO+pusrd1pUmK90DKbSjlK+BnuAGVt4LsB0pUP2MryTfJ2+gzElg5Z8Hd8Gps9VhlLM1564OtIGhj9Gh6NEVUqDtcZErazOVee2Ki3DJ5tcEazOxi0MlAq47EoGTXW57l7udrnCA7lmcNiVtFwBV/Ldwrwtp22E/rkQx+yL52O3GUDxNYgrvdRbQvbqWucsP3Ve2ffYas+3GqmrkLoqz2Axrye2IioNua01YKN4j1XWdIqohoq6+IKlqRvSZUtf6BQ7C1uAEd1WRaCn5mlFebYo9ExTVOXEJ28ixrkS6kv78p/iLO3PrMN2j5aF5OUOTDaD67bN+pd6598iWZ1tErOv3V6wxkOUvDdUWXFVyGwCTK+rkGwBFL4q19G1+KxAcQ9IU/VmZm+NPGVM96gpoUqWUqC5CreHdZoyjZ4ztveoR4wKo+cj0EmKjjRUpZF0FLkKg8PBYlddylNGBRqnIKpqitYF61qWnQ5EdciFKgsc5qxeMDruWzoUMPSKg6YWVw0QY93MkpeWt3nq6XMG+jxX8OKYlWMWY9kLrrU78QNo+VcLFPZWw9kakrfVyGwAs8U9FkMppaqpSk9rV6fKOGY4pIVHeXpY6loZjegM/cNRfzRKNaUuDS4Z1aUqZsuXkTpqdPN0KH8IHH8S+bGx2XVgtV8x9Fzv6vYBujtnRa7ejB7pDZjFbPG9O5tvs24vON3uxJ9Irfd+cb2Vs9V0zRpreysd8jWzs+IrtSQrbcZmZKWBUadqTSCrGZuXlV7WdUQDI+TQG0tHiK1vXJmwGbZiTFRJl+Uq0vFF/wW6dMlY2owFM7bI+UujR2NuwOJtdu8P/YsaQpa9oH05UbawtpqtWVPTdD26fnTE/MonHOzv8+Emqs7oGU+CwAGTjw35xSm2AUxo1nd+zRNLVVotpZfBUeURbTyhe0+eoGmMWdrX7itLetIDWsHOTLCWU3W+Pl8PyPppKbpdkHOT9kUDB6W0N+2Kbrc77fiqw5X/zlwB509Nvszs2u5xDPg81JhVZW2bbR6z6Xt3wssHPF+w1umw4Ae0VFY6UJXO3GehK63E0Yqs1796U5UZ7TnI7QGQSVWYhrImrpqpKb0UM9VO0ZQO8a6+OZTRzFVLVMhmaWdRZcRKVAPRz+B6RcRbplSWy9qXm8Cd/GFvmt1mnbKr/f/hGMvNl+wFMa3MKnDn7Z3EVcN2gSMariMq7dBsnamq/CJmB8IV96uhY1t9qsIeU20VxpRzlR7SYKmKqlGqRoxoI8hTGdgqaZoQbKtVfl1N9tTGjm4q7qLtii7bubqvJTraceoeB1kezl3qxzHb8J3oy/+i5qWErCgr95MBLZ4SCG11xpb2AGSlPW4PwFfD9gBkdeGKsbYHMBRRlVJjzVcpNZZS1FjVFYama4+OGQXG9gNIVnGVTl1lmq2psYUNQFVNdDVjN+sCjhoM1aL/HR06MbY5bBBb1aA3vnmwn8bsin9rCxhLyK61yf6IVmgrlG0FLK35moCrygEwHBshuBoS3lmZq5artgWwo1KOxFSzVPdU81QY9M1PEVQmMIyLUtVFi9TKHN2aQ7ynSpmm267UzIQoSy/UBRzCWnuNKRe0FB1dJwM4gcPRpv3Zpt35UzZ1uuRvQhdn+0+mG/tfxdhgmz3m/6tUjlmMJWRFWcgri6HqbDFdjyRXC7rO6FpX2hNtAugao3tAdH81lBFtAmpsztd4C4jA2MGAjoyNwdbKdIVgVU3vq5ZxoqqqJV3pGgylrUClayArgzZjFdG1ibGgxvLIm9Fdrwaj4d6V+DWIq2+zLLLe2Kyy6TMCVMDRl4Wo0qGp5qqkqvHKUpXKuDpZTlVzdbiMOeqILKVrSWkDPxkRoZ00qxUNjBqLVCq4pbI8tUQVOYthanZmMcs0Hk/zsbO2c1quTnPsMLhqwtripGg5HZwRbe1Vd4wuq8HwJs5GMXucbbZdjFlgLfDKprRiYUGGgqx0IVrxNd0DYMN85dGnulqqMpb3AKpG7qqA8bNcxVVtRVSNjNUdQOH3jQY1NtDVjC1vANtdi9SysXV+tjEyBUsNbym14zylKXd1+v82ti052+9NN2bPwhfHqLK2zTaIWTP22toxlMXWXLqC5qtRztdXGzQwJ68mhXxVW6ffxuNv38TWkCchvQi1NQJXY4q2Qi5f5471AByFrivN180ilqpldi7soKwI6hBXQTXV41Qt4qn4moPesa8LED+ZUoa71mXCNl/dnHnIKzi7dbE/3MNZew3iz7bZ4713J3vsDQlZHmte2S90xFFAaWdNZAWGEKv6cTL9iKUfGfCNFobfliSloaDqQzfE0oeuGe4Y0LsDepem5GQMLu3uXkzB0cDUrWpekaiKrqvb69sLRY2cpB+ECx/UQ7vY8VXkFOWPfwQ/lWu/afCZwqNTd1dlc7M7x9nJxv5++BrEVWKWD4w9c+p0/vG1ju8r4CvtwVZTdiGqG3G2TmmHT1XJ1bHZ+qRkq2F/VxUxyGBpapinta303BeqUqDCIissVM0n6odNkbWMKes8YWCMq8Zci69Syt/iApmmAnRzXhGkb3fWt1hnJwczXvf0NfukQa/BWyTfVWQtOOV+gpRWvAXYHmCWRp4uIalqLKfqR4YLVlpTlTGk1VQ3Yj7jqk/Vh+Ai1YbeWZGm4HPVTtOzZOqcntOGuGqeUuYpFRoKiaQ7WmmKUnSe+4vj/pn7Z05RfwZ+pFN6GkWT1efVwHuCdluenZ1s4KwqexAo2zhmEVZC9hoPMEvL+0pHpM8JQOxrXlixFV8nsge4D2criK+1spKuka0M7yvCekdt0M7WgRdWVdUzVjV/f4WvIXWwOrrma0FYKpunKeorR0FazOLALjNuJVsZZuuvrSXzjVWdXffO7n+lgvfubBqzNxQxFmGLyh6lshomK4RbQFlWz7dvtEK6mqeISX+WweF5+PnTw5R79G6WixSiZnl8sXr8eD5/PGfm2BZJY8xPqQCzswiKanDqUXOGznCSYgrM3+RMfZ6h5Sw7rWb/BXZ8zg732A1Qdravm0Hz/z8cFsae5NGWaP1E2FkuXfUOq7wNeEJfRdjvlAYqujJotZUBLAMR/d3+7u5DGXwYYmvJ1wplH1f4CswUtTUR9qlJa1i2apwWfVXMVw4Rtmgripmzf9NW+m/Zas5e3H3CPRjS4mwQs43eiR6csFfLwkKrNpVOorW8tdK1p4Z4agyhTlX8ZJqnD+ks95A0oSRp5XO10lxN2QZGytPtp2jazFDT876WekmFUsIxbTyhh/QJGpIrw77FvgPNJVyVNqu1Y4P/ME7aD9vVbm84vXlwhZRt8re2acxSV+VBl2lF91rAiIUFPE2fFdBUnQTC2tb6ffFcgEtV7yutqdrDWkZN3+VqcRXIUrEFiK9VwVc6tTbeUw21VeuXeaq2wpKvNNXAVsFMpOzKlA0srR33XzJPl7NX5r/A52x3/oO9c1d1IorCcN7Lp7CN2AgaBC+1cOK1FNRCggZsTBFtctSxkAmxCqaxE8QX8FaJ5AX81t5rZs2+ZSJKVPBb6+y9x6CFfvmzJicq37p9y03YBxlmNWZ/9j+2VfS5VVbWqyqVvcsqzAFhtgaybhkCtmqn8YVmXv1CJTwXcpbSCes1rYTJulq5L/RcTTIQq1rIWfA0L2klFSCp2u/mOV/0gTCR3fkwiF/T8YT3Z998f8kt+T3s+flpVnMW+l6eBuaq2VrwFXQWyNu6pb5vha/bryFfIGPrc2z1lCaBF4GtXVZr/BRR2YDVk/N1o3yS6lLX43oMdV1P66mymC5gWk2rPPgqps7Oz4At5JzWksWxpNw2otjkIGfdwG10e3bo4zTlmvKMys4e0leztpp+uvHx2msG2sfXkdYpq87+3L9Ej7F9DFpj1VG/JabSgam0mSrLluaVPzb1az5VH3lN6d25uqaFNFc1UWFCp9yQUAVurPTVPw3WCFTF01RPmF3GzejJn6ao+QlmmHmpXKTsrJv8qEcvaEovousYs/qgWLZfloF2/YjPIiJt85loU3ZnzOowe9kpe66XgX1cEF9pKpkF3jGz5m2lQaM1hlylC1OAWBvbyvJCGtaoGqWqBqpbdJ/IHswBG12bWI2pXa4SqSaq2oqsOWEpFdZYnl+Cl9NObJgj1cvFYHWlrYx07bXV0KT+I9ZKpouzLmj5Zqd+ImqfabZJ2QtO2f4RyitrUwBLCK6+y74nYMaWdMVXjI11pUv5qrrCWspcdYsaa7rSEzpkQ2/o1NjaFbaCGKuiqrHVoopQVVVXGpa+HN5SMxZb1Nh+Wy/qqm6ql0Zz8c8Y65xlONh8vEbQirSPQZR92huzOsyi7OVmqNnNoHU1nQY+v/sc0JlZO6SuCuHEqqLSsa0vHomo+rWOcG7qUphUzc+Op7SZSqy6WlBeVCXWdFbNjPNaqihaUnpSUDSxUQ/0nALbKL+xgz02pCmHnuyi+0gJ8/rPIc9fkXaCtJK0WOvA2FLM2jArIcv01co62slA8zWM1neJrHQ4B7xJZfW6JndYJmuE3l+Jq0GuWqgGsgKLIXmaSVRDXY1urah0AvDjKthLvy8ENTRF6QwYYx6mmITtyqInSq5dU3YBXIdWl7218+FxUXt5OmaiddI+eYu2T1kwtvT3w1XZMRlLxKqtfPUoG7jqqwOq0sI2IExVFsdeM8ALWljTCasUPA0tTWkTla7pNljbQTWTqjNfYHkaQ7KmYdrKOR/hZ5yY8yGFXywHoPU86/BBGQHSVuPNRMaDq1iLti5kNWU1ZjHWlJW7L95H9ML6OYivnQwiYbOjQCDrNjsHfInnAAVHU2Ov4WsiK7rmfD3q+lrQ1Yw1VFS2nLFqK5y3YE10xVYph02gtFMUWLyoFD+kqkodBDP2b2Dkpa0WSLtG2tdYq9hgYMp+xFi+qTMeXxJjUXYEZmyfsqjKl/GdtqnVMFGN17SmaknVFx0CW19R61erV4mpR0cTXKU9WVXpOmERUFH5WXXZVMzx6DiJVGTM4e2UQ8wpSg9y1g1ko3sYdk5DLqT1yvdfijyTRdqK8WDlrb36xmPGoqwai7CfxlMvbGPqsB+UTe6yRNdthDdVOpoD8JRFSSaBkqmyrOiMqbDTVNK0IOqUMleh4GoMQRozH+UMLcSnerg/Jyg9uQvan/xulK4ai/9Ce8nJ5XIm7x6ItXyCCU/ln5a4KUPBTY3YK3w6ySXsVHxVYYf7gbKBqxquIblofU1bqsa2omrRVgaAyNYjXFVbJ8LRJqXWUTXwtKYwFVFppSvqblmPSVRfLZandoLYVE3OflOx0Xw8YXARnQrX9qNmOB3w15l7cY61VVWLtR+dtrdQgnzlfhth/afpJGCnbcAOf4JBZ2z9nLx/RXdSNQhXMzZ4/yowFRpTaZBFjA04aphsjjasXWMf6lorkbGGZatZWpoAcBVaVd/P389jCrrun6UnfitY2u2/maFjPhoRtfwRcb88WSGugKyNrp8kYKvLvCMjOfFzv7mNsrgap+sXjdYQey/AbO2bAtRVYLFsNVcpbMXVwNZPDx/mPNU2TyEaAdJsPdbqqoqsxQl1f0c1Qn+B05Rd0FSPwbTydxosWYu1ou20rrnvEHV1sJN4nVb4iq4j8nUfU0NlxVQ6HVwhngQSnKmBrMACaqojayowBKippquZ2heqNrNmLIXQVVBLC6LS9prfI2mPg95Dukj5weBnqr3s2if+BdzTSLJ27rWtXKwgqg8X3oOVeL1ouv7Uc37QZyuUbFVXzVaIbM1OAJPQVuNhnc/ViIqGPWS1AWDuKtZUa3ekulDTisEiLXofzpz+JdRcTn+/v6fUWy+ucN5/71CG17nqmgzv/QxsDti69wRycwAV8gKiaIUXeGq5GoVrRDdXH3rqjK7AauDqnWpxx2aAsqswot4fv4d4XDVR7w/vy/3LfVcmqR5izNCY28mZjb7t0dMZf9KLM6fPtBegP0H23fwTxpq3XlyDy18Z+E1ZfM1MA6mtzwNbIZhalfAu6yhGfX3ofBVl98tXtVV8LQp7bIxCW+0EqCoF6useL/7fKKftt9PfnDNG3lYa9BTaepu8DWylem39x+aDht97g4qy2UkgHQNQNUQtTTxlUU/pkCZUpQu5+uzZAqJcRdCUeFoFidR5UyHtoBoRveJ/My8TF7VoLx2lhwNwm5ZUFrD9X8TN+GynfxFRNvb1SxqsLJGvtNjauccyWyHrqwpbzNXE1zuL2UIH1tDaB5lgxVdlnmJ5Su3KU7P1GwcFTXVz4Ixq+4vCnv0JW+l/1tbfyqAr6/NE1hSJVmAxsrlq4GlAV1IaFgvaRBVmkalLevmAluXYMFfnlFs991sKhn676zIVTvsSzEujJ0/PUm6lfxP8atru/J8uTtnn9J62xjSuUpmJ9WHJVlxFVGQNVaWrSm2FO42tWOr6OJK1TdQ2W+/Ph25OpUnVcpwiq1laNjUJUvzZaeZJ3WiqPfnWC3tEm+rnv7k/2DFX3TaiKIrOTwWVGhg5KktaElXqXxRGMqhkgwS4KKAf4D5I1IYYmxsVGUe25C/ouveezJ778MxYjqqCWWd3Zq6bBq3unIxRfU9k/dFfVudrr14FmQp1r8pUYqZCZCosQbZGS2vuKry/j0SVpFapBGw3lZ/1g2jrTrPPbmC3c2haPVCmim3NjX388Zjzyc0xvnwhMbI1EHzF0xoU5cLNs2C+LhaLJUnYNMHZ+wj5On8/DwRVhTQV0pYEU5n1x/VHg0cs4uaM+ifo/8Hgb1FZTCUZJVndr1ZlX3NTidtWRV2sxs+gK/GYqbBMiTxt7KrvgqWMXFWlhlZVn4pypQoztEvQa8LUTwTck8XQIft7/Xse/Wmgjaqo6yMp6IqtfXqVUs1sNVVJ3auyNfSqIVuzFwF+7jemqkBTxq0A8zfzRp8yn/cnm9qF5HxN5PVgbjvVjz6yhncBj5mqTHEJkKkMukamEkhNzVqVOEsDYVvNfvyDuUqvGvWiGksKn/2YpLjZU9I+bl4y9fMlcRc7FM92Ugb6UqW2psZK1HKvinwLIDMCEtbutgBIWFK3KpcIZI1409xV9xGFVn36/PQhzLqxnq7h7fq4piT0Xs5lfZOPDOlB+mWDs6dStVVrTt6qv156lWgHMFPVq1/Vq78X8BtHVa0FU2lVhgWAUavarsobVT9UKlPw9KnwE//0Ii1LeMUAF54MPWVcZmeLnS5DBvqCsoVuDUvAY25rzrcEbJ0ZWBtvASSwdMm21XKtmrAq1v1cjVpuVTWqjeq02KVCxZn5aoJyk6yy8HTkLJcg8WBuH6rHTFYGuMrWMKTkqpiRnMVMS4CMNTbAZedzX3hldWC8q9JVxv6xwdOa9dPaRK3JK5WIrVeVWV2vJOoVqRv1VCbxgWTkzTt4e6qyklSUV9ZCtaZQrvZ2VaLWOEvVqIVePdwf6i0AS0Xhd3/tqaFPbTJN8y61JpUjbX0p+SbEbuQc9D389xzoRaVFALinbwO4q1i/9OlVTC30qnQV6tXIVhXrHsq6vlRqE4kqVf20r6ctniaGvg5X5U8Ha/srK1NlKxEn2SqycpWniJrYSq3i6vxFVRtbAOJFFdZ+jsqaVSppCEr8HLXTP/dhlH8wIvpbnfVRh88DHVQoerKss4KsIutVYhRMpVW5Iiq2Hnf1Sa62iIqqCY0uJSkT5qFFz1GiHujpHCYW+44Dp1DJViYGV8+ydZPaGnNwrQrZL1blYoXerdr6Y/+4o/JTmg78bzhlS7KWdO2UFTB1t4TdbhOyIejqol71zA/zmr3xZ28vAZ5Eouvz+jlu1a0b/9v+6pKsAlcrOUqVPjxMHiZd3E3uRnDnxl0ypqMpYYAnF+A8toOD02jsY59ytvjTwHlU3bbO+si69OwoVa8qNyj1KhF7oVINoxUgegNwfUqjtnSpgZYlTLExmH+vxijKaOBkqqKrokezBll3SwZZwblKxMHLmmCtymTFWvuqN1bPzPbt9lrQqmFM1JUv07CbUqmlCgVuowK033g6JnYb+H+p3MYa6zoLycgWVoKrhtmqjfVgzEmkKkFTka8Az/AWuG2ft47wup/barsyrsIgaUCWylExujMziRi7jG+R1N+IG3HL8eKWjC/GF+HpgkRw7mLMWGA8cBZV2q4zJpM161bXqaarjE2XAOcqiX39E+uadqvtqsRdzFhAWbeuSleQrhCpajfpKmObyFJvrE4gXZES13JjRbexpiwZjD2Xqlmt3TvADlPRNWCtykWWBualXiWBhqTSlF5lwDSlT1HU7aplTdWqmZ8wNfwDPoJcZDAH/zpa8ubi5sZf7UACdiAx9pG+ilOci4GzqcxY/mTNSlSrJHTrTuAqaffV6xohW/0Arhr0KpixwKoqXaFT17RMtZiqQbnhabuxNzI2M9QOHSS63gzGvhKVNgG5mjTsjlkktt47YXeyVb4mRLKS5taqfoWoXyO0sUrYfFe9m95NU26ntymdtkqxsxj69S87Z4zbRBCF4bkPICo6kBKKCAWCQoFrR3LllByCjoaGLkfgDNRcgSMgkDgB/+787L/z3jyPrQTHhvne88a7Dt2nf96Mgb9FioYBRitUNROrcnW2wzJjwEzUYljFS6LyEIBgZwV4AsCdlc9TFs9OISkjdcpSOMksHZb+XAXLaaWvsEKjAH6gN3O5ukSbRxPLopeduyLNXf1gZEW8ji+CUEXLVu6vcBXfUBK2enA1RSqKKFgHV2++5NMqCMtyu38bqbUsRQHJWq7yYKUrBW0omhulG9yZX+j8ZdKHT9L1B0dW9OciV9FEps4Zz1d/BtGqCeANit8G6MAKjrJgaTCo6vQ0o1TNp08oG6dxkM5CsojL23F+2dkP6ZOgryW/ZsdWJBxXObB6XUk+YGWuFphtlUvV94JHqWMpUsVSWcr1Pdeynpo7+njOK7vt7Oqyc1dIWWeqxoBfMtUOrcEE8BXFgVVoADCeisHTAi392ke9L9d9SRpmKal7eI4Gw7uNnJh3J/4X0J39kDgIgPGHkjVffLTKVmF8la3YX/lxdeTGBmtN1hdjwVSmamPx3yZIlZKBnegTNArwBp3R54IP1Pw9cHneuXNSMQQgWjefshaeytQJxaoP1vKoivv/AkVq/VhqmYuYPZNfg2MvUeN1BxbsJjK581dIVlikqxsEJCyatn7bVVicA4jXWVmTrJGyc/ziv8xxGiz73ldl6U4s3H1n30hZSCpTw2ilq5wAvKbCfgdASwtDWcSmKvU0BLt7r2ZkJDxbLKZrzAX7Ao1b3aABnwI+42+Kk0Xnb5F+0FbhzwN8ssrWfNXAiq7urxSqzVTVrspF6naLf7AqL0pnFyGUUb5KSz5V81lnb6Tm0VU29RtNRW829cZMAHRVngo/scrQXBmqCjeNpFzgUdZNllBW8rqJly/HRgFc0XwEhnv29Piisz8SZ9ZI1wkNAkS+inJ/5cZVqRqcVclWn6qyVcKyxGLyFU1NJWsA/aOlvOG9zBzpzt4/KZoD/AaLyWrCldyUfLyJT1jjCcAeUIXrvsev86GlUm1XrspbOdzZI6lqq9gmWgvsoZUw2SpTNQIUwdraTzFSxUXWFC2Ktd1yhUZN79gjfFTgfw23nT2TrKzoTJmrbVlfx6kqV/2mSqr6QZVVokXfGEpqCfpHMr7pHDPJfn+FhqhydbJVnqKrE0Bkahypl7lqps4xW3lBOxWhFDLQ8i0aBfADDXjlux3Rn+zsj+T2WEGuWl2Hb1hf3yhYo1nVpyolraRqEKeiEaShp7CKklqeoc0NOzN+qsd4wGegu3ovpGzqd7RsFbuMq/O/tLIUPlWbpgozl2oH5EbNIDvponzLHt6Ot+VNZ69A2XISqIar19Vnq1gOxuJijG0EK6GoPJkqdAVXrGjR99BVGnundF3vheSTVaKGqnpTYegTH6wUFNVMVb+L4mzalJM+1v15Prx4Hd7ckmdDd+6VFOgaR6t0tbsr4s5VUTVdi32/RNWZFHV1xroQjaFkY92ta93d+yL5EUAEuUq0t7J/ByBI1QWr1PTlHJulqJadm2V8NNZwQd+G5+zOfZNkrDu7EtTVnbBKV5+qxESqS1WrK6lNpIGxITBMxt6KrusBkWpzK5q8R5PSVhRYzWjkaySrTgD8vj8O1nAwfZRfXbF/l2RMpaYmV5f8J6zuEGDyFMhTP6hKUukp4tU/2O9sY+QZ+wx9Gx6xOwdC8qOAYrVQ1ger/XdUStULn6qxsM+uqot/FKS0te3sXZrWnT0ckvEUpgr+3zw+WE2qhqdVMtSlKt0UxlAYma8bQjTm9OyUTXizG/rDncMhZV/Rg63oicFWVPXUKhwDgFJ1wwzglPUDQCSs1vvYV2Mr7neka3q4JBuuxb8NmKCrJlwXKH9gRVWDVI2n1Ghrf8YyXo4u4jJeYx6fPmYX2Ef+E913DopEV8nS6wrONQnYMcB8qbp9qlpTUe3F/5RXdOAnr/yB9srFDtLurulBk5SsK/vd1So8sArOVSWpV3S7SA3m0mp25usuXJd3RXeOhURb0SvZ6vZXPlT9AKA8bcnqwtQIylW/omkMzdO7a/a764h3uVHkMbtzyCSoitLEGmytLuQqabgqUR/F6347S0PopUUeoieu13hUsFaDa3Xn0EnKVtpKTuirZA2/qoqDtb34n+YypjZk3QTsfLe+Xg/9bg1wz0cR1+VN58BJq/PzlUbWarAqVaNxVYrK0ufxjl+Sai7Fq56lRYLanFzr3dAP2A/QqFnXWKP5jt05ApI2WPRVtr6sBKtSFa92qtZNPYvyVEs+VX1crPc0swrNRJPRWrWna3qcJIWrdM1YV1lvTba2XdX5fqkqyriaC8jTIlUNilRLd/HfJWVXp2AVdlwlu6eq4OIfr/wYJaGpDdNt/HvlH7xSC9wVbT7rHD7pRMGqf1od6BoEq1yNx9VAVG7thwLrsdCTq6SpKw1EgVBKGdv1PF4SZaWpKJp6N6kamUpB/SrfkLPC09wo3eMObdAn6s7xkbS9UrAGJ6xGV3f+T2qr/5y1g676pf0pJcV7I+mc7t5/BZSlqaOoklWaooJUbS3+2uorUiWoVxTtIpTdtPLh1HPwYGrc8SG7c5SkKxAfsgZDQBGp6E0bqoaq2ilRUhQNfRXqqWusq8ifPO2u/hskI2trZ9VMVb/4tyN1m7lSsuFnl+8/JllXS6qpGqrKb0k3jqdik6Vaypv8ZtcMchoJYijqWxCps8kGZTH7IKQRiyyiSByE+59gPJ0i36m2y1WZaBD0f/4gNWyffrkrOV9jufztXALmZ/Kdkd/3turNJ6jNw//dgD5dnPKhkrN8nw90bu0ITM1fqnBHZVfUsS79VSbSsy3kx/njErJaZHFZFctaPuZHmXaZilNefzJNYeb8W2kIejya6JhU/yQ/CFl8A9BZVIuoULVT1OYGWkuq+WhB/ciMQNXxVh00Fe8+qaboSpdTSfU35GRCfhYCV82uij3VFxWSgp4udfZQFGfsJr0jQHD461zfquLDv1mnZ52oSNGdxyWhmS9/83IJ0AeyVgStigUgOvpjUWtsmfqWFkOPnqQ0ksQIVB1qVZgKYdM+TYq0zdvyueTNhPx4pFoClq1axp791X1+s1FPStClMUsDXygluSB+q8LXulmDw7/SVAMSTdGYnbzavJqQNSDvXetq8EJ1c1OKK6WexTSsTBio80YpSYU0W/VsqQ5/mFpbGuymrqNq5ZiMe5u9CVkJV2X90/+2UjX+ndRgl8aK7m8fqCNZIGjWxa5aqE29DAgqVRObCierB/pJEmS5q2bHP1R1LK08XR76d7fmweZgQtaFxK0KThb/dT/R9HXETMpIGohp1TLuDUD6NuVYqknprMvnkmdN4fJAVof496qBqUmfppriVG+YSRlJC7Gt2nihajdqrOdlNIGh1JMMIrAVwoaHf16ph2oj9VXNTd3Z7G5DVo2MLAH565T6GLrafeLvELpKagSqjpsKwvW0z08KSbqRxoVV36uV06rPaNVOXTXDbDmrHIGs7j3Avt2tms63Kjia+7mdfSxRrg+EbCVZBfqKNW3VXZ+uW6RYSl1JhVxNBTA1rNRcUwjqmkkZyZ1Ix3cAfFc1UZ263Uk9yaOUXfZrXKzx3b9fqTji72CaZw6H8zkCUZN1daxVez2d5p85kwnQB0IMsvT1AIJiRau6svZS7Fw4S01JA2npClGB26pbTe4npSQPUvawzytVM6TpNIdmkocjXcXaf/xPMPZf2HA4wcgBOM0ayppvqLmUCJg2hDQRfxXYDewB4xvARlPrOlFX0oXUpjqytkCfBmZSRvJYpFmseatSV/KfEVyydqwBE3bVzNc7hH3icNLZSN2tAYmrjp7jmhLSIYyk71dT37oa82TzZGPRZ0I6EBjrf/qf6KppVqimoevGjPLlhw7nG4ygXx94HZBX7DwGtizpRDr3gA0kbahJ8f6wc0epjQNBAET7FvL9T7oQRBTDTmR/WFHBe8kRivZ4WjIfN69P1XWt2x7s8ekuXT5lzr9nnZwAfnzub6YsHzfHl62TS9b/2efr0/HU6YCPmrMTwTrX/TpAo1xqlhdZJ1dYzzcAwuUq8+s11jLXvVHHAC43i1pP77OOWkXLlWZZ7HK6Pp7mq4sCrjVvztf9/9i4bnrlUvPyhuv7aYGfGwO9crV5a8P11avdFn9p3tpwbTvDlT8zL2y49ilrdcAdzHrD9aNUN7DcxuxHgvMlly9b3MKsDq/7nyUXN3Mke/bQtmi5hVlN2ONE4JFt7mRW91kq5Z5mUeyx4fJgIbcyj4MNFwHjHQNa5vHNOwYUjHcMaJnH5h0DSsZClpbZHtZblMzXfPWOARmzQcr43TdaTFliJEuMZImRLDGSJUayxEiWGMkSI1liJEuMZImRLDGSJUayxEiWGMkSI1liJEuMZImRLDGSJUayxEiWGMkSI1liJEuMZImRLDGSJUayxEiWGMkSI1liJEuMZImRLDGSJUayxEiWGMkSI1liJEuMZImRLDGSJUayxEiWGMkSI1liJEuMZImRLDGSJUayxEiWGMkSI1liJMu/9uwtpYEgiqLo7U4IgXz44aMaMv95iiAqjqA3rDWGzeFSFSNZYiRLjGSJkSwxkiVGssRIlhjJEiNZYiRLjGSJkSwxkiVGssRIlhjJEiNZYiRLjGSJkSwxkiVGssRIlhjJEiNZYiRLjGSJkSwxkiVGssRIlhjJEiNZYiRLjGSJkSwxkiVGssRIlhjJEiNZYiRLjGSJkSwxkiVGssRIlhjJEiNZYiRLjGSJkSwxcywIOeayIOQ2LwtC7vO6IORt9gUh28x1QcZ1xsxSss+YWUKu82XzNEvEsc2M04COfb7tzwWnd+zzY7stOLnbNn89fNxyapfH/Le93y8OBE7oebl//C7sJwuSoXz9JIvHAAAAAElFTkSuQmCC');\r\n\t\tbackground-repeat: no-repeat;\r\n\t\tbackground-size: 100% 100%;\r\n\t\tborder-radius: 14rpx;\r\n\t\tmargin: 0 auto 30rpx auto;\r\n\t\tpadding: 24rpx;\r\n        box-sizing: border-box;\r\n\t\t.title {\r\n\t\t\t.pictrue {\r\n\t\t\t\twidth: 148rpx;\r\n\t\t\t\theight: 40rpx;\r\n\r\n\t\t\t\timage {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 100%;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.lines {\r\n\t\t\t\twidth: 1rpx;\r\n\t\t\t\theight: 24rpx;\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\topacity: 0.6;\r\n\t\t\t\tmargin-left: 16rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.point {\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tmargin-left: 21rpx;\r\n\t\t\t\tmargin-right: 4rpx;\r\n\t\t\t\tfont-weight: 800;\r\n\t\t\t}\r\n\t\t\t\t.styleAll {\r\n\t\t\t\t\twidth: 35rpx;\r\n\t\t\t\t\theight: 35rpx;\r\n\t\t\t\t\tbackground-color: #2F2F2F;\r\n\t\t\t\t\tborder-radius: 6rpx;\r\n\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t}\r\n\r\n\t\t\t.more {\r\n\t\t\t\twidth: 86rpx;\r\n\t\t\t\theight: 40rpx;\r\n\t\t\t\tbackground: linear-gradient(142deg, #FFE9CE 0%, #FFD6A7 100%);\r\n\t\t\t\topacity: 1;\r\n\t\t\t\tborder-radius: 18px;\r\n\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\tcolor: #FE960F;\r\n\t\t\t\tpadding-left: 8rpx;\r\n                font-weight: 800;\r\n\t\t\t\t.iconfont {\r\n\t\t\t\t\tfont-size: 21rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.conter {\r\n\t\t\twidth: 666rpx;\r\n\t\t\theight: 320rpx;\r\n\t\t\tborder-radius: 12px;\r\n\t\t\tmargin-top: 24rpx;\r\n\r\n\t\t\t.itemCon {\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\twidth: 186rpx;\r\n\t\t\t\tmargin-right: 24rpx;\r\n\r\n\t\t\t\t.item {\r\n\t\t\t\t\twidth: 100%;\r\n\r\n\t\t\t\t\t.pictrue {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 186rpx;\r\n\t\t\t\t\t\tborder-radius: 6rpx;\r\n\r\n\t\t\t\t\t\timage {\r\n\t\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\t\tborder-radius: 6rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.name {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: #000;\r\n\t\t\t\t\t\tmargin-top: 14rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.y_money {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\t\ttext-decoration: line-through;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t.x_money {\r\n\t\t\t\t\t\tcolor: #FD502F;\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t    margin: 2rpx 0;\r\n\t\t\t\t\t\t.num {\r\n\t\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.money {\r\n\t\t\t\t\t\t// background: url(\"data:image/png;base64,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\") no-repeat;\r\n\t\t\t\t\t\tmargin-top: 14rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./a_seckill.vue?vue&type=style&index=0&id=7d45776e&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./a_seckill.vue?vue&type=style&index=0&id=7d45776e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363903772\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import { render, staticRenderFns, recyclableRender, components } from \"./b_combination.vue?vue&type=template&id=a1cf24e2&scoped=true&\"\nvar renderjs\nimport script from \"./b_combination.vue?vue&type=script&lang=js&\"\nexport * from \"./b_combination.vue?vue&type=script&lang=js&\"\nimport style0 from \"./b_combination.vue?vue&type=style&index=0&id=a1cf24e2&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"a1cf24e2\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/components/b_combination.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./b_combination.vue?vue&type=template&id=a1cf24e2&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.combinationList.length\n  var g1 = g0 ? _vm.assistUserList.length : null\n  var l0 =\n    g0 && g1 > 0\n      ? _vm.__map(_vm.assistUserList, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m0 = index === 2 && Number(_vm.assistUserCount) > 3\n          return {\n            $orig: $orig,\n            m0: m0,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./b_combination.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./b_combination.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view :class=\"{borderShow:isBorader}\">\r\n\t\t<view class=\"combination\" v-if=\"combinationList.length\">\r\n\t\t\t<view class=\"title acea-row row-between\">\r\n\t\t\t\t<view class=\"spike-bd\">\r\n\t\t\t\t\t<view v-if=\"assistUserList.length > 0\" class=\"activity_pic\">\r\n\t\t\t\t\t\t<view v-for=\"(item,index) in assistUserList\" :key=\"index\" class=\"picture\"\r\n\t\t\t\t\t\t\t:style='index===2?\"position: relative\":\"position: static\"'>\r\n\t\t\t\t\t\t\t<span class=\"avatar\" :style='\"background-image: url(\"+item+\")\"'></span>\r\n\t\t\t\t\t\t\t<span v-if=\"index===2 && Number(assistUserCount) > 3\" class=\"mengceng\">\r\n\t\t\t\t\t\t\t\t<i>···</i>\r\n\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<text class=\"pic_count\">{{assistUserCount}}人参与</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<navigator url=\"/pages/activity/goods_combination/index\" hover-class=\"none\"\r\n\t\t\t\t\tclass=\"more acea-row row-center-wrapper\">GO<text class=\"iconfont icon-xiangyou\"></text></navigator>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"conter acea-row\">\r\n\t\t\t\t<scroll-view scroll-x=\"true\" style=\"white-space: nowrap; vertical-align: middle;\"\r\n\t\t\t\t\tshow-scrollbar=\"false\">\r\n\t\t\t\t\t<view class=\"itemCon\" v-for=\"(item, index) in combinationList\" :key=\"index\" @click=\"goDetail(item)\">\r\n\t\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t\t<view class=\"pictrue\">\r\n\t\t\t\t\t\t\t\t<image :src=\"item.image\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"text lines1\">\r\n\t\t\t\t\t\t\t\t<view class=\"name line1\">{{item.title}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"money\">¥<text class=\"num\">{{item.price}}</text></view>\r\n\t\t\t\t\t\t\t\t<view class=\"y_money\">¥{{item.otPrice}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- <navigator :url=\"`/pages/activity/goods_combination_details/index?id=${item.id}`\" hover-class=\"none\" class=\"item\" v-for=\"(item, index) in combinationList\" :key=\"index\">\r\n\t\t\t\t\t\t<view class=\"pictrue\">\r\n\t\t\t\t\t\t\t<image :src=\"item.image\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"text lines1\">\r\n\t\t\t\t\t\t\t<text class=\"money\">¥<text class=\"num\">{{item.price}}</text></text>\r\n\t\t\t\t\t\t\t<text class=\"y_money\">¥{{item.otPrice}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</navigator> -->\r\n\t\t\t\t</scroll-view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tlet app = getApp();\r\n\timport {\r\n\t\tgetCombinationIndexApi\r\n\t} from '@/api/activity.js';\r\n\texport default {\r\n\t\tname: 'b_combination',\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tcombinationList: [],\r\n\t\t\t\tisBorader: false,\r\n\t\t\t\tassistUserList: [],\r\n\t\t\t\tassistUserCount: 0\r\n\t\t\t};\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.getCombinationList();\r\n\t\t},\r\n\t\tmounted() {},\r\n\t\tmethods: {\r\n\t\t\t// 拼团列表\r\n\t\t\tgetCombinationList: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tgetCombinationIndexApi().then(function(res) {\r\n\t\t\t\t\tthat.combinationList = res.data.productList;\r\n\t\t\t\t\tthat.assistUserList = res.data.avatarList;\r\n\t\t\t\t\tthat.assistUserCount = res.data.totalPeople;\r\n\t\t\t\t}).catch((res) => {\r\n\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\ttitle: res\r\n\t\t\t\t\t});\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgoDetail(item) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/activity/goods_combination_details/index?id=${item.id}`\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.mengceng {\r\n\t\twidth: 38rpx;\r\n\t\theight: 38rpx;\r\n\t\tline-height: 36rpx;\r\n\t\tbackground: rgba(51, 51, 51, 0.6);\r\n\t\ttext-align: center;\r\n\t\tborder-radius: 50%;\r\n\t\topacity: 1;\r\n\t\tposition: absolute;\r\n\t\tleft: 0px;\r\n\t\ttop: 2rpx;\r\n\t\tcolor: #FFF;\r\n\t\ti{\r\n\t\t\tfont-style: normal;\r\n\t\t\tfont-size: 20rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.activity_pic {\r\n\t\tmargin-left: 28rpx;\r\n\t\tpadding-left: 20rpx;\r\n\r\n\t\t.picture {\r\n\t\t\tdisplay: inline-block;\r\n\t\t}\r\n\r\n\t\t.avatar {\r\n\t\t\twidth: 38rpx;\r\n\t\t\theight: 38rpx;\r\n\t\t\tdisplay: inline-table;\r\n\t\t\tvertical-align: middle;\r\n\t\t\t-webkit-user-select: none;\r\n\t\t\t-moz-user-select: none;\r\n\t\t\t-ms-user-select: none;\r\n\t\t\tuser-select: none;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\tbackground-repeat: no-repeat;\r\n\t\t\tbackground-size: cover;\r\n\t\t\tbackground-position: 0 0;\r\n\t\t\tmargin-right: -10rpx;\r\n\t\t\tbox-shadow: 0 0 0 1px #fff;\r\n\t\t}\r\n\r\n\t\t.pic_count {\r\n\t\t\tmargin-left: 30rpx;\r\n\t\t\tcolor: $theme-color;\r\n\t\t\tfont-size: 22rpx;\r\n\t\t\tfont-weight: 500;\r\n\t\t}\r\n\t}\r\n\r\n\t.default {\r\n\t\twidth: 690rpx;\r\n\t\theight: 300rpx;\r\n\t\tborder-radius: 14rpx;\r\n\t\tmargin: 26rpx auto 0 auto;\r\n\t\tbackground-color: #ccc;\r\n\t\ttext-align: center;\r\n\t\tline-height: 300rpx;\r\n\r\n\t\t.iconfont {\r\n\t\t\tfont-size: 80rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.combination {\r\n\t\twidth: auto;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 14rpx;\r\n\t\tmargin: 0 auto 30rpx auto;\r\n\t\tpadding: 16rpx 24rpx 24rpx 24rpx;\r\n\t\tbackground-image: url(../../../static/images/pth.png);\r\n\t\tbackground-repeat: no-repeat;\r\n\t\tbackground-size: 100%;\r\n\r\n\t\t.title {\r\n\t\t\twidth: 80%;\r\n\t\t\tmargin-left: 128rpx;\r\n\r\n\t\t\t.sign {\r\n\t\t\t\twidth: 40rpx;\r\n\t\t\t\theight: 40rpx;\r\n\r\n\t\t\t\timage {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 100%;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.name {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tcolor: #282828;\r\n\t\t\t\tmargin-left: 12rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\r\n\t\t\t\ttext {\r\n\t\t\t\t\tcolor: #797979;\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t\tmargin-left: 14rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.more {\r\n\t\t\t\twidth: 86rpx;\r\n\t\t\t\theight: 40rpx;\r\n\t\t\t\tbackground: linear-gradient(142deg, #FFE9CE 0%, #FFD6A7 100%);\r\n\t\t\t\topacity: 1;\r\n\t\t\t\tborder-radius: 18px;\r\n\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\tcolor: #FE960F;\r\n\t\t\t\tpadding-left: 8rpx;\r\n                 font-weight: 800;\r\n\t\t\t\t.iconfont {\r\n\t\t\t\t\tfont-size: 21rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.conter {\r\n\t\t\tmargin-top: 24rpx;\r\n\r\n\t\t\t.itemCon {\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\twidth: 220rpx;\r\n\t\t\t\tmargin-right: 24rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.item {\r\n\t\t\t\twidth: 100%;\r\n\r\n\t\t\t\t.pictrue {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 220rpx;\r\n\t\t\t\t\tborder-radius: 6rpx;\r\n\r\n\t\t\t\t\timage {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\tborder-radius: 6rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.text {\r\n\t\t\t\t\tmargin-top: 4rpx;\r\n\r\n\t\t\t\t\t.y_money {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\t\ttext-decoration: line-through;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.name {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: #000;\r\n\t\t\t\t\t\tmargin-top: 14rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.money {\r\n\t\t\t\t\t\tcolor: #FD502F;\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\tmargin: 10rpx 0 0rpx 0;\r\n\r\n\t\t\t\t\t\t.num {\r\n\t\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./b_combination.vue?vue&type=style&index=0&id=a1cf24e2&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./b_combination.vue?vue&type=style&index=0&id=a1cf24e2&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363903790\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363899433\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=1&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=1&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363903080\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}