{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\element-ui\\lib\\utils\\aria-dialog.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\element-ui\\lib\\utils\\aria-dialog.js", "mtime": 1753666304622}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}], "contextDependencies": [], "result": ["'use strict';\n\nfunction _typeof2(o) { \"@babel/helpers - typeof\"; return _typeof2 = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof2(o); }\nexports.__esModule = true;\nvar _typeof = typeof Symbol === \"function\" && _typeof2(Symbol.iterator) === \"symbol\" ? function (obj) {\n  return _typeof2(obj);\n} : function (obj) {\n  return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : _typeof2(obj);\n};\nvar _ariaUtils = require('./aria-utils');\nvar _ariaUtils2 = _interopRequireDefault(_ariaUtils);\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\n\n/**\n * @constructor\n * @desc Dialog object providing modal focus management.\n *\n * Assumptions: The element serving as the dialog container is present in the\n * DOM and hidden. The dialog container has role='dialog'.\n *\n * @param dialogId\n *          The ID of the element serving as the dialog container.\n * @param focusAfterClosed\n *          Either the DOM node or the ID of the DOM node to focus when the\n *          dialog closes.\n * @param focusFirst\n *          Optional parameter containing either the DOM node or the ID of the\n *          DOM node to focus when the dialog opens. If not specified, the\n *          first focusable element in the dialog will receive focus.\n */\nvar aria = aria || {};\nvar tabEvent;\naria.Dialog = function (dialog, focusAfterClosed, focusFirst) {\n  var _this = this;\n  this.dialogNode = dialog;\n  if (this.dialogNode === null || this.dialogNode.getAttribute('role') !== 'dialog') {\n    throw new Error('Dialog() requires a DOM element with ARIA role of dialog.');\n  }\n  if (typeof focusAfterClosed === 'string') {\n    this.focusAfterClosed = document.getElementById(focusAfterClosed);\n  } else if ((typeof focusAfterClosed === 'undefined' ? 'undefined' : _typeof(focusAfterClosed)) === 'object') {\n    this.focusAfterClosed = focusAfterClosed;\n  } else {\n    this.focusAfterClosed = null;\n  }\n  if (typeof focusFirst === 'string') {\n    this.focusFirst = document.getElementById(focusFirst);\n  } else if ((typeof focusFirst === 'undefined' ? 'undefined' : _typeof(focusFirst)) === 'object') {\n    this.focusFirst = focusFirst;\n  } else {\n    this.focusFirst = null;\n  }\n  if (this.focusFirst) {\n    this.focusFirst.focus();\n  } else {\n    _ariaUtils2.default.focusFirstDescendant(this.dialogNode);\n  }\n  this.lastFocus = document.activeElement;\n  tabEvent = function tabEvent(e) {\n    _this.trapFocus(e);\n  };\n  this.addListeners();\n};\naria.Dialog.prototype.addListeners = function () {\n  document.addEventListener('focus', tabEvent, true);\n};\naria.Dialog.prototype.removeListeners = function () {\n  document.removeEventListener('focus', tabEvent, true);\n};\naria.Dialog.prototype.closeDialog = function () {\n  var _this2 = this;\n  this.removeListeners();\n  if (this.focusAfterClosed) {\n    setTimeout(function () {\n      _this2.focusAfterClosed.focus();\n    });\n  }\n};\naria.Dialog.prototype.trapFocus = function (event) {\n  if (_ariaUtils2.default.IgnoreUtilFocusChanges) {\n    return;\n  }\n  if (this.dialogNode.contains(event.target)) {\n    this.lastFocus = event.target;\n  } else {\n    _ariaUtils2.default.focusFirstDescendant(this.dialogNode);\n    if (this.lastFocus === document.activeElement) {\n      _ariaUtils2.default.focusLastDescendant(this.dialogNode);\n    }\n    this.lastFocus = document.activeElement;\n  }\n};\nexports.default = aria.Dialog;", null]}