{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\order\\orderVideoSend.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\order\\orderVideoSend.vue", "mtime": 1753666157911}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport {videoSendApi, sheetInfoApi, companyGetListApi} from '@/api/order'\nimport {Debounce} from '@/utils/validate'\nconst validatePhone = (rule, value, callback) => {\n  if (!value) {\n    return callback(new Error('请填写手机号'));\n  } else if (!/^1[3456789]\\d{9}$/.test(value)) {\n    callback(new Error('手机号格式不正确!'));\n  } else {\n    callback();\n  }\n};\nexport default {\n  name: 'orderSend',\n  props: {\n    orderId: String\n  },\n  data() {\n    return {\n      formItem: {\n        deliveryId: '',\n        orderNo: '',\n        waybillId: ''\n      },\n      modals: false,\n      express: [],\n      exportTempList: [],\n      tempImg: '',\n      rules: {\n        deliveryId: [\n          {required: true, message: '请选择快递公司', trigger: 'change'}\n        ],\n        waybillId: [\n          {required: true, message: '请输入快递单号', trigger: 'blur'}\n        ]\n      },\n      expressType: 'normal'\n    }\n  },\n  mounted() {\n    this.express = JSON.parse(sessionStorage.getItem('videoExpress'));\n  },\n  methods: {\n    // 视频号快递公司\n    companyGetList() {\n      companyGetListApi().then(async res => {\n        this.express = res;\n        sessionStorage.setItem('videoExpress', JSON.stringify(res))\n      })\n    },\n    // 提交\n    putSend:Debounce(function(name) {\n      this.formItem.orderNo = this.orderId;\n      this.$refs[name].validate((valid) => {\n        if (valid) {\n          videoSendApi(this.formItem).then(async => {\n            this.$message.success('发送货成功');\n            this.modals = false;\n            this.$refs[name].resetFields();\n            this.$emit('submitFail')\n          })\n        } else {\n          this.$message.error('请填写信息');\n        }\n      })\n    }),\n    handleClose() {\n      this.cancel('formItem');\n    },\n    cancel(name) {\n      this.modals = false;\n      this.$refs[name].resetFields();\n      this.formItem.type = '1';\n      this.formItem.expressRecordType = '1';\n    }\n  }\n}\n", null]}