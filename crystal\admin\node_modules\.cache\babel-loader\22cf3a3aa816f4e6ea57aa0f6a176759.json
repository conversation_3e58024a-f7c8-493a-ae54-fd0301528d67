{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\element-ui\\lib\\option.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\element-ui\\lib\\option.js", "mtime": 1753666303533}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}], "contextDependencies": [], "result": ["\"use strict\";\n\nfunction _typeof2(o) { \"@babel/helpers - typeof\"; return _typeof2 = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof2(o); }\nmodule.exports = /******/function (modules) {\n  // webpackBootstrap\n  /******/ // The module cache\n  /******/\n  var installedModules = {};\n  /******/\n  /******/ // The require function\n  /******/\n  function __webpack_require__(moduleId) {\n    /******/\n    /******/ // Check if module is in cache\n    /******/if (installedModules[moduleId]) {\n      /******/return installedModules[moduleId].exports;\n      /******/\n    }\n    /******/ // Create a new module (and put it into the cache)\n    /******/\n    var module = installedModules[moduleId] = {\n      /******/i: moduleId,\n      /******/l: false,\n      /******/exports: {}\n      /******/\n    };\n    /******/\n    /******/ // Execute the module function\n    /******/\n    modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n    /******/\n    /******/ // Flag the module as loaded\n    /******/\n    module.l = true;\n    /******/\n    /******/ // Return the exports of the module\n    /******/\n    return module.exports;\n    /******/\n  }\n  /******/\n  /******/\n  /******/ // expose the modules object (__webpack_modules__)\n  /******/\n  __webpack_require__.m = modules;\n  /******/\n  /******/ // expose the module cache\n  /******/\n  __webpack_require__.c = installedModules;\n  /******/\n  /******/ // define getter function for harmony exports\n  /******/\n  __webpack_require__.d = function (exports, name, getter) {\n    /******/if (!__webpack_require__.o(exports, name)) {\n      /******/Object.defineProperty(exports, name, {\n        enumerable: true,\n        get: getter\n      });\n      /******/\n    }\n    /******/\n  };\n  /******/\n  /******/ // define __esModule on exports\n  /******/\n  __webpack_require__.r = function (exports) {\n    /******/if (typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n      /******/Object.defineProperty(exports, Symbol.toStringTag, {\n        value: 'Module'\n      });\n      /******/\n    }\n    /******/\n    Object.defineProperty(exports, '__esModule', {\n      value: true\n    });\n    /******/\n  };\n  /******/\n  /******/ // create a fake namespace object\n  /******/ // mode & 1: value is a module id, require it\n  /******/ // mode & 2: merge all properties of value into the ns\n  /******/ // mode & 4: return value when already ns object\n  /******/ // mode & 8|1: behave like require\n  /******/\n  __webpack_require__.t = function (value, mode) {\n    /******/if (mode & 1) value = __webpack_require__(value);\n    /******/\n    if (mode & 8) return value;\n    /******/\n    if (mode & 4 && _typeof2(value) === 'object' && value && value.__esModule) return value;\n    /******/\n    var ns = Object.create(null);\n    /******/\n    __webpack_require__.r(ns);\n    /******/\n    Object.defineProperty(ns, 'default', {\n      enumerable: true,\n      value: value\n    });\n    /******/\n    if (mode & 2 && typeof value != 'string') for (var key in value) __webpack_require__.d(ns, key, function (key) {\n      return value[key];\n    }.bind(null, key));\n    /******/\n    return ns;\n    /******/\n  };\n  /******/\n  /******/ // getDefaultExport function for compatibility with non-harmony modules\n  /******/\n  __webpack_require__.n = function (module) {\n    /******/var getter = module && module.__esModule ? /******/function getDefault() {\n      return module['default'];\n    } : /******/function getModuleExports() {\n      return module;\n    };\n    /******/\n    __webpack_require__.d(getter, 'a', getter);\n    /******/\n    return getter;\n    /******/\n  };\n  /******/\n  /******/ // Object.prototype.hasOwnProperty.call\n  /******/\n  __webpack_require__.o = function (object, property) {\n    return Object.prototype.hasOwnProperty.call(object, property);\n  };\n  /******/\n  /******/ // __webpack_public_path__\n  /******/\n  __webpack_require__.p = \"/dist/\";\n  /******/\n  /******/\n  /******/ // Load entry module and return exports\n  /******/\n  return __webpack_require__(__webpack_require__.s = 53);\n  /******/\n}\n/************************************************************************/\n/******/({\n  /***/0: (/***/function _(module, __webpack_exports__, __webpack_require__) {\n    \"use strict\";\n\n    /* harmony export (binding) */\n    __webpack_require__.d(__webpack_exports__, \"a\", function () {\n      return normalizeComponent;\n    });\n    /* globals __VUE_SSR_CONTEXT__ */\n\n    // IMPORTANT: Do NOT use ES2015 features in this file (except for modules).\n    // This module is a runtime utility for cleaner component module output and will\n    // be included in the final webpack user bundle.\n\n    function normalizeComponent(scriptExports, render, staticRenderFns, functionalTemplate, injectStyles, scopeId, moduleIdentifier, /* server only */\n    shadowMode /* vue-cli only */) {\n      // Vue.extend constructor export interop\n      var options = typeof scriptExports === 'function' ? scriptExports.options : scriptExports;\n\n      // render functions\n      if (render) {\n        options.render = render;\n        options.staticRenderFns = staticRenderFns;\n        options._compiled = true;\n      }\n\n      // functional template\n      if (functionalTemplate) {\n        options.functional = true;\n      }\n\n      // scopedId\n      if (scopeId) {\n        options._scopeId = 'data-v-' + scopeId;\n      }\n      var hook;\n      if (moduleIdentifier) {\n        // server build\n        hook = function hook(context) {\n          // 2.3 injection\n          context = context ||\n          // cached call\n          this.$vnode && this.$vnode.ssrContext ||\n          // stateful\n          this.parent && this.parent.$vnode && this.parent.$vnode.ssrContext; // functional\n          // 2.2 with runInNewContext: true\n          if (!context && typeof __VUE_SSR_CONTEXT__ !== 'undefined') {\n            context = __VUE_SSR_CONTEXT__;\n          }\n          // inject component styles\n          if (injectStyles) {\n            injectStyles.call(this, context);\n          }\n          // register component module identifier for async chunk inferrence\n          if (context && context._registeredComponents) {\n            context._registeredComponents.add(moduleIdentifier);\n          }\n        };\n        // used by ssr in case component is cached and beforeCreate\n        // never gets called\n        options._ssrRegister = hook;\n      } else if (injectStyles) {\n        hook = shadowMode ? function () {\n          injectStyles.call(this, this.$root.$options.shadowRoot);\n        } : injectStyles;\n      }\n      if (hook) {\n        if (options.functional) {\n          // for template-only hot-reload because in that case the render fn doesn't\n          // go through the normalizer\n          options._injectStyles = hook;\n          // register for functioal component in vue file\n          var originalRender = options.render;\n          options.render = function renderWithStyleInjection(h, context) {\n            hook.call(context);\n            return originalRender(h, context);\n          };\n        } else {\n          // inject component registration as beforeCreate hook\n          var existing = options.beforeCreate;\n          options.beforeCreate = existing ? [].concat(existing, hook) : [hook];\n        }\n      }\n      return {\n        exports: scriptExports,\n        options: options\n      };\n    }\n\n    /***/\n  }),\n  /***/3: (/***/function _(module, exports) {\n    module.exports = require(\"element-ui/lib/utils/util\");\n\n    /***/\n  }),\n  /***/34: (/***/function _(module, __webpack_exports__, __webpack_require__) {\n    \"use strict\";\n\n    // CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/vue-loader/lib??vue-loader-options!./packages/select/src/option.vue?vue&type=template&id=7a44c642&\n    var render = function render() {\n      var _vm = this;\n      var _h = _vm.$createElement;\n      var _c = _vm._self._c || _h;\n      return _c(\"li\", {\n        directives: [{\n          name: \"show\",\n          rawName: \"v-show\",\n          value: _vm.visible,\n          expression: \"visible\"\n        }],\n        staticClass: \"el-select-dropdown__item\",\n        class: {\n          selected: _vm.itemSelected,\n          \"is-disabled\": _vm.disabled || _vm.groupDisabled || _vm.limitReached,\n          hover: _vm.hover\n        },\n        on: {\n          mouseenter: _vm.hoverItem,\n          click: function click($event) {\n            $event.stopPropagation();\n            return _vm.selectOptionClick($event);\n          }\n        }\n      }, [_vm._t(\"default\", [_c(\"span\", [_vm._v(_vm._s(_vm.currentLabel))])])], 2);\n    };\n    var staticRenderFns = [];\n    render._withStripped = true;\n\n    // CONCATENATED MODULE: ./packages/select/src/option.vue?vue&type=template&id=7a44c642&\n\n    // EXTERNAL MODULE: external \"element-ui/lib/mixins/emitter\"\n    var emitter_ = __webpack_require__(4);\n    var emitter_default = /*#__PURE__*/__webpack_require__.n(emitter_);\n\n    // EXTERNAL MODULE: external \"element-ui/lib/utils/util\"\n    var util_ = __webpack_require__(3);\n\n    // CONCATENATED MODULE: ./node_modules/babel-loader/lib!./node_modules/vue-loader/lib??vue-loader-options!./packages/select/src/option.vue?vue&type=script&lang=js&\n    var _typeof = typeof Symbol === \"function\" && _typeof2(Symbol.iterator) === \"symbol\" ? function (obj) {\n      return _typeof2(obj);\n    } : function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : _typeof2(obj);\n    };\n\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n\n    /* harmony default export */\n    var optionvue_type_script_lang_js_ = {\n      mixins: [emitter_default.a],\n      name: 'ElOption',\n      componentName: 'ElOption',\n      inject: ['select'],\n      props: {\n        value: {\n          required: true\n        },\n        label: [String, Number],\n        created: Boolean,\n        disabled: {\n          type: Boolean,\n          default: false\n        }\n      },\n      data: function data() {\n        return {\n          index: -1,\n          groupDisabled: false,\n          visible: true,\n          hitState: false,\n          hover: false\n        };\n      },\n      computed: {\n        isObject: function isObject() {\n          return Object.prototype.toString.call(this.value).toLowerCase() === '[object object]';\n        },\n        currentLabel: function currentLabel() {\n          return this.label || (this.isObject ? '' : this.value);\n        },\n        currentValue: function currentValue() {\n          return this.value || this.label || '';\n        },\n        itemSelected: function itemSelected() {\n          if (!this.select.multiple) {\n            return this.isEqual(this.value, this.select.value);\n          } else {\n            return this.contains(this.select.value, this.value);\n          }\n        },\n        limitReached: function limitReached() {\n          if (this.select.multiple) {\n            return !this.itemSelected && (this.select.value || []).length >= this.select.multipleLimit && this.select.multipleLimit > 0;\n          } else {\n            return false;\n          }\n        }\n      },\n      watch: {\n        currentLabel: function currentLabel() {\n          if (!this.created && !this.select.remote) this.dispatch('ElSelect', 'setSelected');\n        },\n        value: function value(val, oldVal) {\n          var _select = this.select,\n            remote = _select.remote,\n            valueKey = _select.valueKey;\n          if (!this.created && !remote) {\n            if (valueKey && (typeof val === 'undefined' ? 'undefined' : _typeof(val)) === 'object' && (typeof oldVal === 'undefined' ? 'undefined' : _typeof(oldVal)) === 'object' && val[valueKey] === oldVal[valueKey]) {\n              return;\n            }\n            this.dispatch('ElSelect', 'setSelected');\n          }\n        }\n      },\n      methods: {\n        isEqual: function isEqual(a, b) {\n          if (!this.isObject) {\n            return a === b;\n          } else {\n            var valueKey = this.select.valueKey;\n            return Object(util_[\"getValueByPath\"])(a, valueKey) === Object(util_[\"getValueByPath\"])(b, valueKey);\n          }\n        },\n        contains: function contains() {\n          var arr = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n          var target = arguments[1];\n          if (!this.isObject) {\n            return arr && arr.indexOf(target) > -1;\n          } else {\n            var valueKey = this.select.valueKey;\n            return arr && arr.some(function (item) {\n              return Object(util_[\"getValueByPath\"])(item, valueKey) === Object(util_[\"getValueByPath\"])(target, valueKey);\n            });\n          }\n        },\n        handleGroupDisabled: function handleGroupDisabled(val) {\n          this.groupDisabled = val;\n        },\n        hoverItem: function hoverItem() {\n          if (!this.disabled && !this.groupDisabled) {\n            this.select.hoverIndex = this.select.options.indexOf(this);\n          }\n        },\n        selectOptionClick: function selectOptionClick() {\n          if (this.disabled !== true && this.groupDisabled !== true) {\n            this.dispatch('ElSelect', 'handleOptionClick', [this, true]);\n          }\n        },\n        queryChange: function queryChange(query) {\n          this.visible = new RegExp(Object(util_[\"escapeRegexpString\"])(query), 'i').test(this.currentLabel) || this.created;\n          if (!this.visible) {\n            this.select.filteredOptionsCount--;\n          }\n        }\n      },\n      created: function created() {\n        this.select.options.push(this);\n        this.select.cachedOptions.push(this);\n        this.select.optionsCount++;\n        this.select.filteredOptionsCount++;\n        this.$on('queryChange', this.queryChange);\n        this.$on('handleGroupDisabled', this.handleGroupDisabled);\n      },\n      beforeDestroy: function beforeDestroy() {\n        var _select2 = this.select,\n          selected = _select2.selected,\n          multiple = _select2.multiple;\n        var selectedOptions = multiple ? selected : [selected];\n        var index = this.select.cachedOptions.indexOf(this);\n        var selectedIndex = selectedOptions.indexOf(this);\n\n        // if option is not selected, remove it from cache\n        if (index > -1 && selectedIndex < 0) {\n          this.select.cachedOptions.splice(index, 1);\n        }\n        this.select.onOptionDestroy(this.select.options.indexOf(this));\n      }\n    };\n    // CONCATENATED MODULE: ./packages/select/src/option.vue?vue&type=script&lang=js&\n    /* harmony default export */\n    var src_optionvue_type_script_lang_js_ = optionvue_type_script_lang_js_;\n    // EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js\n    var componentNormalizer = __webpack_require__(0);\n\n    // CONCATENATED MODULE: ./packages/select/src/option.vue\n\n    /* normalize component */\n\n    var component = Object(componentNormalizer[\"a\" /* default */])(src_optionvue_type_script_lang_js_, render, staticRenderFns, false, null, null, null);\n\n    /* hot reload */\n    if (false) {\n      var api;\n    }\n    component.options.__file = \"packages/select/src/option.vue\";\n    /* harmony default export */\n    var src_option = __webpack_exports__[\"a\"] = component.exports;\n\n    /***/\n  }),\n  /***/4: (/***/function _(module, exports) {\n    module.exports = require(\"element-ui/lib/mixins/emitter\");\n\n    /***/\n  }),\n  /***/53: (/***/function _(module, __webpack_exports__, __webpack_require__) {\n    \"use strict\";\n\n    __webpack_require__.r(__webpack_exports__);\n    /* harmony import */\n    var _select_src_option__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(34);\n\n    /* istanbul ignore next */\n    _select_src_option__WEBPACK_IMPORTED_MODULE_0__[/* default */\"a\"].install = function (Vue) {\n      Vue.component(_select_src_option__WEBPACK_IMPORTED_MODULE_0__[/* default */\"a\"].name, _select_src_option__WEBPACK_IMPORTED_MODULE_0__[/* default */\"a\"]);\n    };\n\n    /* harmony default export */\n    __webpack_exports__[\"default\"] = _select_src_option__WEBPACK_IMPORTED_MODULE_0__[/* default */\"a\"];\n\n    /***/\n  })\n\n  /******/\n});", null]}