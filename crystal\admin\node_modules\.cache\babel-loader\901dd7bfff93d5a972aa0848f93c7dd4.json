{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\order\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\order\\index.vue", "mtime": 1753666157910}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _order = require(\"@/api/order\");\nvar _index = _interopRequireDefault(require(\"@/components/cards/index\"));\nvar _ZBParser = _interopRequireDefault(require(\"@/components/FormGenerator/components/parser/ZBParser\"));\nvar _orderDetail = _interopRequireDefault(require(\"./orderDetail\"));\nvar _orderSend = _interopRequireDefault(require(\"./orderSend\"));\nvar _orderVideoSend = _interopRequireDefault(require(\"./orderVideoSend\"));\nvar _storePoint = require(\"@/api/storePoint\");\nvar _jsCookie = _interopRequireDefault(require(\"js-cookie\"));\nvar _utils = require(\"@/utils\");\nvar _store = require(\"@/api/store\");\nvar _permission = require(\"@/utils/permission\");\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n// 权限判断函数\nvar _default = exports.default = {\n  name: 'orderlistDetails',\n  components: {\n    cardsData: _index.default,\n    zbParser: _ZBParser.default,\n    detailsFrom: _orderDetail.default,\n    orderSend: _orderSend.default,\n    orderVideoSend: _orderVideoSend.default\n  },\n  data: function data() {\n    return {\n      RefuseVisible: false,\n      RefuseData: {},\n      orderId: '',\n      refundVisible: false,\n      refundData: {},\n      dialogVisibleJI: false,\n      tableDataLog: {\n        data: [],\n        total: 0\n      },\n      tableFromLog: {\n        page: 1,\n        limit: 10,\n        orderNo: 0\n      },\n      LogLoading: false,\n      isCreate: 1,\n      editData: null,\n      dialogVisible: false,\n      tableData: {\n        data: [],\n        total: 0\n      },\n      listLoading: true,\n      tableFrom: {\n        status: 'all',\n        dateLimit: '',\n        orderNo: '',\n        page: 1,\n        limit: 10,\n        type: 0\n      },\n      orderChartType: {},\n      timeVal: [],\n      fromList: this.$constants.fromList,\n      fromType: [{\n        value: 'all',\n        text: '全部'\n      }, {\n        value: 'info',\n        text: '普通'\n      }, {\n        value: 'pintuan',\n        text: '拼团'\n      }, {\n        value: 'bragin',\n        text: '砍价'\n      }, {\n        value: 'miaosha',\n        text: '秒杀'\n      }],\n      selectionList: [],\n      ids: '',\n      orderids: '',\n      cardLists: [],\n      isWriteOff: (0, _utils.isWriteOff)(),\n      proType: 0,\n      active: false\n    };\n  },\n  mounted: function mounted() {\n    this.getList();\n    this.getOrderStatusNum();\n    // this.getOrderListData();\n  },\n  methods: {\n    checkPermi: _permission.checkPermi,\n    resetFormRefundhandler: function resetFormRefundhandler() {\n      this.refundVisible = false;\n    },\n    resetFormRefusehand: function resetFormRefusehand() {\n      this.RefuseVisible = false;\n    },\n    resetForm: function resetForm(formValue) {\n      this.dialogVisible = false;\n    },\n    // 核销订单\n    onWriteOff: function onWriteOff(row) {\n      var _this = this;\n      this.$modalSure('核销订单吗').then(function () {\n        (0, _order.writeUpdateApi)(row.verifyCode).then(function () {\n          _this.$message.success('核销成功');\n          _this.tableFrom.page = 1;\n          _this.getList();\n        });\n      });\n    },\n    seachList: function seachList() {\n      this.tableFrom.page = 1;\n      this.getList();\n      this.getOrderStatusNum();\n    },\n    // 拒绝退款\n    RefusehandleClose: function RefusehandleClose() {\n      this.RefuseVisible = false;\n    },\n    onOrderRefuse: function onOrderRefuse(row) {\n      this.orderids = row.orderId;\n      this.RefuseData = {\n        orderId: row.orderId,\n        reason: ''\n      };\n      this.RefuseVisible = true;\n    },\n    RefusehandlerSubmit: function RefusehandlerSubmit(formValue) {\n      var _this2 = this;\n      (0, _order.orderRefuseApi)({\n        orderNo: this.orderids,\n        reason: formValue.reason\n      }).then(function (data) {\n        _this2.$message.success('操作成功');\n        _this2.RefuseVisible = false;\n        _this2.getList();\n      });\n    },\n    // 立即退款\n    refundhandleClose: function refundhandleClose() {\n      this.refundVisible = false;\n    },\n    onOrderRefund: function onOrderRefund(row) {\n      this.refundData = {\n        orderId: row.orderId,\n        amount: row.payPrice,\n        type: ''\n      };\n      this.orderids = row.orderId;\n      this.refundVisible = true;\n    },\n    refundhandlerSubmit: function refundhandlerSubmit(formValue) {\n      var _this3 = this;\n      (0, _order.orderRefundApi)({\n        amount: formValue.amount,\n        orderNo: this.orderids\n      }).then(function (data) {\n        _this3.$message.success('操作成功');\n        _this3.refundVisible = false;\n        _this3.getList();\n      });\n    },\n    // 发送\n    sendOrder: function sendOrder(row) {\n      if (row.type === 0 || row.type === 2) {\n        this.$refs.send.modals = true;\n        this.$refs.send.getList();\n        this.$refs.send.sheetInfo();\n      } else {\n        this.$refs.videoSend.modals = true;\n        if (!JSON.parse(sessionStorage.getItem('videoExpress'))) this.$refs.videoSend.companyGetList();\n      }\n      this.orderId = row.orderId;\n    },\n    // 订单删除\n    handleDelete: function handleDelete(row, idx) {\n      var _this4 = this;\n      if (row.isDel) {\n        this.$modalSure().then(function () {\n          (0, _order.orderDeleteApi)({\n            orderNo: row.orderId\n          }).then(function () {\n            _this4.$message.success('删除成功');\n            _this4.tableData.data.splice(idx, 1);\n          });\n        });\n      } else {\n        this.$confirm('您选择的的订单存在用户未删除的订单，无法删除用户未删除的订单！', '提示', {\n          confirmButtonText: '确定',\n          type: 'error'\n        });\n      }\n    },\n    // 详情\n    onOrderDetails: function onOrderDetails(id) {\n      this.orderId = id;\n      this.$refs.orderDetail.getDetail(id);\n      this.$refs.orderDetail.dialogVisible = true;\n    },\n    // 订单记录\n    onOrderLog: function onOrderLog(id) {\n      var _this5 = this;\n      this.dialogVisibleJI = true;\n      this.LogLoading = true;\n      this.tableFromLog.orderNo = id;\n      (0, _order.orderLogApi)(this.tableFromLog).then(function (res) {\n        _this5.tableDataLog.data = res.list;\n        _this5.tableDataLog.total = res.total;\n        _this5.LogLoading = false;\n      }).catch(function () {\n        _this5.LogLoading = false;\n      });\n    },\n    pageChangeLog: function pageChangeLog(page) {\n      this.tableFromLog.page = page;\n      this.onOrderLog();\n    },\n    handleSizeChangeLog: function handleSizeChangeLog(val) {\n      this.tableFromLog.limit = val;\n      this.onOrderLog();\n    },\n    handleClose: function handleClose() {\n      this.dialogVisible = false;\n    },\n    // 备注\n    onOrderMark: function onOrderMark(row) {\n      var _this6 = this;\n      this.$prompt('订单备注', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        inputErrorMessage: '请输入订单备注',\n        inputType: 'textarea',\n        inputValue: row.remark,\n        inputPlaceholder: '请输入订单备注',\n        inputValidator: function inputValidator(value) {\n          if (!value) return '输入不能为空';\n        }\n      }).then(function (_ref) {\n        var value = _ref.value;\n        (0, _order.orderMarkApi)({\n          mark: value,\n          orderNo: row.orderId\n        }).then(function () {\n          _this6.$message.success('操作成功');\n          _this6.getList();\n        });\n      }).catch(function () {\n        _this6.$message.info('取消输入');\n      });\n    },\n    handleSelectionChange: function handleSelectionChange(val) {\n      this.selectionList = val;\n      var data = [];\n      this.selectionList.map(function (item) {\n        data.push(item.orderId);\n      });\n      this.ids = data.join(',');\n    },\n    // 选择时间\n    selectChange: function selectChange(tab) {\n      this.timeVal = [];\n      this.tableFrom.page = 1;\n      this.getList();\n      this.getOrderStatusNum();\n      // this.getOrderListData();\n    },\n    // 具体日期\n    onchangeTime: function onchangeTime(e) {\n      this.timeVal = e;\n      this.tableFrom.dateLimit = e ? this.timeVal.join(',') : '';\n      this.tableFrom.page = 1;\n      this.getList();\n      this.getOrderStatusNum();\n      // this.getOrderListData();\n    },\n    // 编辑\n    edit: function edit(row) {\n      this.orderId = row.orderId;\n      this.editData = {\n        orderId: row.orderId,\n        totalPrice: row.totalPrice,\n        totalPostage: row.totalPostage,\n        payPrice: row.payPrice,\n        payPostage: row.payPostage,\n        gainIntegral: row.gainIntegral\n      };\n      this.dialogVisible = true;\n    },\n    handlerSubmit: function handlerSubmit(formValue) {\n      var _this7 = this;\n      var data = {\n        orderNo: formValue.orderId,\n        payPrice: formValue.payPrice\n      };\n      (0, _order.updatePriceApi)(data).then(function (data) {\n        _this7.$message.success('编辑数据成功');\n        _this7.dialogVisible = false;\n        _this7.getList();\n      });\n    },\n    // 列表\n    getList: function getList() {\n      var _this8 = this;\n      this.listLoading = true;\n      (0, _order.orderListApi)(this.tableFrom).then(function (res) {\n        _this8.tableData.data = res.list || [];\n        _this8.tableData.total = res.total;\n        _this8.listLoading = false;\n        _this8.checkedCities = _this8.$cache.local.has('order_stroge') ? _this8.$cache.local.getJSON('order_stroge') : _this8.checkedCities;\n      }).catch(function () {\n        _this8.listLoading = false;\n      });\n    },\n    // 数据统计\n    getOrderListData: function getOrderListData() {\n      var _this9 = this;\n      (0, _order.orderListDataApi)({\n        dateLimit: this.tableFrom.dateLimit\n      }).then(function (res) {\n        _this9.cardLists = [{\n          name: '订单数量',\n          count: res.count,\n          color: '#1890FF',\n          class: 'one',\n          icon: 'icondingdan'\n        }, {\n          name: '订单金额',\n          count: res.amount,\n          color: '#A277FF',\n          class: 'two',\n          icon: 'icondingdanjine'\n        }, {\n          name: '微信支付金额',\n          count: res.weChatAmount,\n          color: '#EF9C20',\n          class: 'three',\n          icon: 'iconweixinzhifujine'\n        }, {\n          name: '余额支付金额',\n          count: res.yueAmount,\n          color: '#1BBE6B',\n          class: 'four',\n          icon: 'iconyuezhifujine2'\n        }];\n      });\n    },\n    // 获取各状态数量\n    getOrderStatusNum: function getOrderStatusNum() {\n      var _this0 = this;\n      (0, _order.orderStatusNumApi)({\n        dateLimit: this.tableFrom.dateLimit,\n        type: this.tableFrom.type\n      }).then(function (res) {\n        _this0.orderChartType = res;\n      });\n    },\n    pageChange: function pageChange(page) {\n      this.tableFrom.page = page;\n      this.getList();\n    },\n    handleSizeChange: function handleSizeChange(val) {\n      this.tableFrom.limit = val;\n      this.getList();\n    },\n    exports: function exports() {\n      var data = {\n        dateLimit: this.tableFrom.dateLimit,\n        orderNo: this.tableFrom.orderNo,\n        status: this.tableFrom.status,\n        type: this.tableFrom.type\n      };\n      (0, _store.orderExcelApi)(data).then(function (res) {\n        window.open(res.fileName);\n      });\n    },\n    //打印小票\n    onOrderPrint: function onOrderPrint(data) {\n      var _this1 = this;\n      (0, _order.orderPrint)(data.orderId).then(function (res) {\n        _this1.$modal.msgSuccess('打印成功');\n      }).catch(function (error) {\n        _this1.$modal.msgError(error.message);\n      });\n    }\n  }\n};", null]}