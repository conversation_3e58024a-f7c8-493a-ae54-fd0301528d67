<view><view class="{{['page-index',(navIndex>0)?'bgf':'']}}"><view class="header"><view class="serch-wrapper flex"><view class="logo"><image src="{{logoUrl}}" mode></image></view><navigator class="input" url="/pages/goods_search/index" hover-class="none"><text class="iconfont icon-xiazai5"></text>搜索商品</navigator></view></view><block wx:if="{{navIndex==0}}"><view class="page_content" style="{{('margin-top:'+marTop+'px;')}}"><view class="mp-bg"></view><block wx:if="{{$root.g0}}"><view class="swiper"><swiper indicator-dots="true" autoplay="{{true}}" circular="{{circular}}" interval="{{interval}}" duration="{{duration}}" indicator-color="rgba(255,255,255,0.6)" indicator-active-color="#fff"><block wx:for="{{imgUrls}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><swiper-item><navigator class="slide-navigator acea-row row-between-wrapper" url="{{item.url}}" hover-class="none"><image class="slide-image" src="{{item.pic}}" lazy-load="{{true}}"></image></navigator></swiper-item></block></block></swiper></view></block><block wx:if="{{$root.g1}}"><view class="notice acea-row row-middle row-between"><view class="pic"><image src="/static/images/xinjian.png"></image></view><text class="line">|</text><view class="swipers"><swiper indicator-dots="{{indicatorDots}}" autoplay="{{autoplay}}" interval="2500" duration="500" vertical="true" circular="true"><block wx:for="{{roll}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><swiper-item><navigator class="item" url="{{item.url}}" hover-class="none"><view class="line1">{{item.info}}</view></navigator></swiper-item></block></block></swiper></view><view class="iconfont icon-xiangyou"></view></view></block><block wx:if="{{$root.g2}}"><view class="nav acea-row"><block wx:for="{{menus}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><block wx:if="{{item.show=='1'}}"><navigator class="item" url="{{item.url}}" open-type="switchTab" hover-class="none"><view class="pictrue"><image src="{{item.pic}}"></image></view><view class="menu-txt">{{item.name}}</view></navigator></block><block wx:else><navigator class="item" url="{{item.url}}" hover-class="none"><view class="pictrue"><image src="{{item.pic}}"></image></view><view class="menu-txt">{{item.name}}</view></navigator></block></block></block></view></block><block wx:if="{{$root.g3>0}}"><view class="couponIndex"><view class="acea-row" style="height:100%;"><view class="titBox"><view class="tit1">领取优惠券</view><view class="tit2">福利大礼包，省了又省</view><navigator class="item" url="/pages/users/user_get_coupon/index" hover-class="none"><view class="tit3">查看全部<text class="iconfont icon-xiangyou"></text></view></navigator></view><view class="listBox acea-row"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['list',item.$orig.isUse?'listHui':'listActive']}}"><view class="{{['tit','line1',item.$orig.isUse?'pricehui':'titActive']}}">{{item.$orig.name+''}}</view><view class="{{['price',item.$orig.isUse?'pricehui':'icon-color']}}">{{''+(item.$orig.money?item.m0:'')}}<text class="yuan">元</text></view><block wx:if="{{!item.$orig.isUse}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({item:item.$orig,index})}}" class="{{['ling',item.$orig.isUse?'pricehui':'icon-color']}}" bindtap="__e">领取</view></block><block wx:else><view class="{{['ling',item.$orig.isUse?'pricehui fonthui':'icon-color']}}">已领取</view></block><view class="priceM">{{"满"+(item.$orig.minPrice?item.m1:'')+"元可用"}}</view></view></block></view></view></view></block><a_seckill vue-id="8dd740cc-1" bind:__l="__l"></a_seckill><b_combination vue-id="8dd740cc-2" bind:__l="__l"></b_combination><c_bargain vue-id="8dd740cc-3" bind:__l="__l"></c_bargain><view class="sticky-box" style="{{('top:'+marTop+'px;')}}"><scroll-view class="scroll-view_H" style="width:100%;" scroll-x="true" scroll-with-animation="{{true}}" scroll-left="{{tabsScrollLeft}}" data-event-opts="{{[['scroll',[['scroll',['$event']]]]]}}" bindscroll="__e"><view class="tab nav-bd" id="tab_list"><block wx:for="{{explosiveMoney}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['item',(listActive==index)?'active':'']}}" id="tab_item" data-event-opts="{{[['tap',[['ProductNavTab',['$0',index],[[['explosiveMoney','',index]]]]]]]}}" bindtap="__e"><view class="txt">{{item.name}}</view><view class="label">{{item.info}}</view></view></block></view></scroll-view></view><view class="{{['index-product-wrapper',iSshowH?'on':'']}}"><view class="{{['list-box','animated',$root.g4>0?'fadeIn on':'']}}"><block wx:for="{{tempArr}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['goDetail',['$0'],[[['tempArr','',index]]]]]]]}}" class="item" bindtap="__e"><view class="pictrue"><block wx:if="{{item.activityH5&&item.activityH5.type==='1'}}"><label class="pictrue_log pictrue_log_class _span">秒杀</label></block><block wx:if="{{item.activityH5&&item.activityH5.type==='2'}}"><label class="pictrue_log pictrue_log_class _span">砍价</label></block><block wx:if="{{item.activityH5&&item.activityH5.type==='3'}}"><label class="pictrue_log pictrue_log_class _span">拼团</label></block><image src="{{item.image}}" mode></image></view><view class="text-info"><view class="title line1">{{item.storeName}}</view><view class="old-price"><text>{{"¥"+item.otPrice}}</text></view><view class="price"><text>￥</text>{{item.price+''}}<block wx:if="{{item.checkCoupon}}"><view class="txt">券</view></block></view></view></view></block></view><block wx:if="{{goodScroll}}"><view class="loadingicon acea-row row-center-wrapper"><text class="loading iconfont icon-jiazai" hidden="{{loading==false}}"></text></view></block><block wx:if="{{!goodScroll}}"><view class="mores-txt flex"><text>我是有底线的</text></view></block></view></view></block></view></view>