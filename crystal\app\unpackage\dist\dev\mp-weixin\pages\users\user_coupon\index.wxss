@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.navbar.data-v-fe8ae026 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 106rpx;
  background-color: #FFFFFF;
  z-index: 9;
}
.navbar .item.data-v-fe8ae026 {
  border-top: 5rpx solid transparent;
  border-bottom: 5rpx solid transparent;
  font-size: 30rpx;
  color: #999999;
}
.navbar .item.on.data-v-fe8ae026 {
  border-bottom-color: #c9ab79;
  color: #282828;
}
.money.data-v-fe8ae026 {
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.pic-num.data-v-fe8ae026 {
  color: #ffffff;
  font-size: 24rpx;
}
.coupon-list.data-v-fe8ae026 {
  margin-top: 122rpx;
}
.coupon-list .item .text.data-v-fe8ae026 {
  height: 100%;
}
.coupon-list .item .text .condition.data-v-fe8ae026 {
  /* display: flex;
	align-items: center; */
}
.condition .line-title.data-v-fe8ae026 {
  width: 90rpx;
  height: 40rpx !important;
  line-height: 40rpx !important;
  padding: 2rpx 10rpx;
  box-sizing: border-box;
  background: #fff7f7;
  border: 1px solid #e83323;
  opacity: 1;
  border-radius: 20rpx;
  font-size: 18rpx !important;
  color: #e83323;
  margin-right: 12rpx;
}
.noCommodity.data-v-fe8ae026 {
  margin-top: 300rpx;
}

