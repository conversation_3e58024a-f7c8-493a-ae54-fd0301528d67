<view class="page-wrapper _div data-v-5d5dcf67"><view class="content-container _div data-v-5d5dcf67"><block wx:if="{{$root.g0>0}}"><view class="list-content _div data-v-5d5dcf67"><block wx:for="{{questionList}}" wx:for-item="item" wx:for-index="__i0__" wx:key="id"><view data-event-opts="{{[['tap',[['turnDetail',['$0'],[[['questionList','id',item.id]]]]]]]}}" class="question-card _div data-v-5d5dcf67" bindtap="__e"><view class="card-header _div data-v-5d5dcf67"><view class="date-info _div data-v-5d5dcf67"><uni-icons vue-id="{{'4e5c9e7f-1-'+__i0__}}" type="calendar" size="16" color="#666" class="data-v-5d5dcf67" bind:__l="__l"></uni-icons><text class="data-v-5d5dcf67">{{item.addTime}}</text></view><view class="{{['status-tag','_div','data-v-5d5dcf67',(item.status==0)?'status-pending':'',(item.status==1)?'status-submitted':'']}}">{{''+(item.status==0?'未提交':'已提交')+''}}</view></view><view class="card-content _div data-v-5d5dcf67"><block wx:if="{{item.status==1}}"><view class="chakra-preview _div data-v-5d5dcf67"><view class="chakra-indicators _div data-v-5d5dcf67"><block wx:for="{{chakraColors}}" wx:for-item="color" wx:for-index="index" wx:key="index"><view class="chakra-item _div data-v-5d5dcf67"><view class="chakra-dot _div data-v-5d5dcf67" style="{{'background-color:'+(color)+';'}}"></view><block wx:if="{{item.chakraData&&item.chakraData[index]}}"><view class="chakra-value _div data-v-5d5dcf67">{{''+item.chakraData[index].value+''}}</view></block><block wx:else><view class="chakra-value _div data-v-5d5dcf67">-</view></block><view class="chakra-name _div data-v-5d5dcf67">{{chakraNames[index]}}</view></view></block></view><view class="preview-hint _div data-v-5d5dcf67">点击查看详细结果</view></view></block><block wx:else><view class="test-preview _div data-v-5d5dcf67"><view class="preview-icon _div data-v-5d5dcf67"><uni-icons vue-id="{{'4e5c9e7f-2-'+__i0__}}" type="checkbox" size="24" color="#c9ab79" class="data-v-5d5dcf67" bind:__l="__l"></uni-icons></view><view class="preview-text _div data-v-5d5dcf67">继续进行脉轮测试</view></view></block></view><view class="card-footer _div data-v-5d5dcf67"><view data-event-opts="{{[['tap',[['turnDetail',['$0'],[[['questionList','id',item.id]]]]]]]}}" class="action-button detail-btn _div data-v-5d5dcf67" catchtap="__e"><uni-icons vue-id="{{'4e5c9e7f-3-'+__i0__}}" type="eye" size="14" color="#fff" class="data-v-5d5dcf67" bind:__l="__l"></uni-icons><text class="data-v-5d5dcf67">{{item.status==0?'继续测试':'查看详情'}}</text></view><view data-event-opts="{{[['tap',[['deleteQuestion',['$0'],[[['questionList','id',item.id,'id']]]]]]]}}" class="action-button delete-btn _div data-v-5d5dcf67" catchtap="__e"><uni-icons vue-id="{{'4e5c9e7f-4-'+__i0__}}" type="trash" size="14" color="#fff" class="data-v-5d5dcf67" bind:__l="__l"></uni-icons><text class="data-v-5d5dcf67">删除</text></view></view></view></block></view></block><block wx:else><view class="empty-state _div data-v-5d5dcf67"><image class="empty-image data-v-5d5dcf67" src="/static/images/empty-list.png" mode="aspectFit"></image><text class="empty-text data-v-5d5dcf67">暂无脉轮测试记录</text><view data-event-opts="{{[['tap',[['createNewTest',['$event']]]]]}}" class="action-button create-btn _div data-v-5d5dcf67" bindtap="__e"><uni-icons vue-id="4e5c9e7f-5" type="plusempty" size="14" color="#fff" class="data-v-5d5dcf67" bind:__l="__l"></uni-icons><text class="data-v-5d5dcf67">创建新测试</text></view></view></block></view></view>