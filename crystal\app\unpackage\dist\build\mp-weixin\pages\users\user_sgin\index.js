(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/users/user_sgin/index"],{3380:function(t,n,i){"use strict";i.d(n,"b",(function(){return e})),i.d(n,"c",(function(){return s})),i.d(n,"a",(function(){}));var e=function(){var t=this.$createElement,n=(this._self._c,this.signSystemList.length),i=this.signSystemList.length,e=this.signList.length;this._isMounted||(this.e0=function(t){return t.stopPropagation(),t.preventDefault(),(!1)(t)}),this.$mp.data=Object.assign({},{$root:{g0:n,g1:i,g2:e}})},s=[]},"555f":function(t,n,i){"use strict";(function(t,n){var e=i("47a9");i("5c2d");e(i("3240"));var s=e(i("9c5c"));t.__webpack_require_UNI_MP_PLUGIN__=i,n(s.default)}).call(this,i("3223")["default"],i("df3c")["createPage"])},8693:function(t,n,i){},"8bc2":function(t,n,i){"use strict";var e=i("8693"),s=i.n(e);s.a},"9c5c":function(t,n,i){"use strict";i.r(n);var e=i("3380"),s=i("a585");for(var a in s)["default"].indexOf(a)<0&&function(t){i.d(n,t,(function(){return s[t]}))}(a);i("8bc2");var o=i("828b"),r=Object(o["a"])(s["default"],e["b"],e["c"],!1,null,"2494a870",null,!1,e["a"],void 0);n["default"]=r.exports},a585:function(t,n,i){"use strict";i.r(n);var e=i("ce35"),s=i.n(e);for(var a in e)["default"].indexOf(a)<0&&function(t){i.d(n,t,(function(){return e[t]}))}(a);n["default"]=s.a},ce35:function(t,n,i){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var e=i("cda4"),s=i("8f59"),a=i("5904"),o=(i("fdf2"),{components:{authorize:function(){Promise.all([i.e("common/vendor"),i.e("components/Authorize")]).then(function(){return resolve(i("cf49"))}.bind(null,i)).catch(i.oe)}},data:function(){return{active:!1,signCount:[],signSystemList:[],signList:[],signInfo:{},integral:0,isAuto:!1,isShowAuth:!1,day:0,sign_index:0}},computed:(0,s.mapGetters)(["isLogin","userInfo"]),watch:{isLogin:{handler:function(t,n){t&&(this.getUserInfo(),this.getSignSysteam(),this.getSignList())},deep:!0}},onLoad:function(){this.isLogin?(this.getUserInfo(),this.getSignSysteam(),this.getSignList()):(0,e.toLogin)()},methods:{onLoadFun:function(){this.getUserInfo(),this.getSignSysteam(),this.getSignList()},authColse:function(t){this.isShowAuth=t},getSignSysteam:function(){var t=this;(0,a.getSignConfig)().then((function(n){t.$set(t,"signSystemList",n.data.list),t.day=t.Rp(n.data.list.length)}))},goSignList:function(){return this.$util.Tips("/pages/users/user_sgin_list/index")},getUserInfo:function(){var t=this;(0,a.postSignUser)({all:0,integral:0,sign:1}).then((function(n){n.data.integral=parseInt(n.data.integral);var i=n.data.sumSignDay;t.$set(t,"signInfo",n.data),t.signCount=t.PrefixInteger(i,4),t.sign_index=n.data.signNum}))},getSignList:function(){var t=this;(0,a.getSignList)({page:1,limit:3}).then((function(n){t.$set(t,"signList",n.data.list)}))},Rp:function(t){var n=["零","一","二","三","四","五","六","七","八","九"],i="";t=""+t;for(var e=0;e<t.length;e++)i+=n[parseInt(t.charAt(e))];return i},PrefixInteger:function(t,n){return(Array(n).join("0")+t).slice(-n).split("")},goSign:function(t){var n=this,i=this,e=i.signInfo.sumSignDay;if(i.signInfo.isDaySign)return this.$util.Tips({title:"您今日已签到!"});(0,a.setSignIntegral)().then((function(t){i.active=!0,i.integral=t.data.integral,i.sign_index=i.sign_index+1>i.signSystemList.length?1:i.sign_index+1,i.signCount=i.PrefixInteger(e+1,4),i.$set(i.signInfo,"isDaySign",!0),i.$store.commit("changInfo",{amount1:"integral",amount2:i.$util.$h.Add(i.signInfo.integral,t.data.integral)}),i.getSignList()})).catch((function(t){return n.$util.Tips({title:t})}))},close:function(){this.active=!1}}});n.default=o}},[["555f","common/runtime","common/vendor"]]]);