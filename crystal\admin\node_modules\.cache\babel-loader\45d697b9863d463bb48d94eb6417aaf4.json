{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\FormGenerator\\index\\RightPanel.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\FormGenerator\\index\\RightPanel.vue", "mtime": 1753666157771}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _util = require(\"util\");\nvar _TreeNodeDialog = _interopRequireDefault(require(\"./TreeNodeDialog\"));\nvar _index = require(\"../utils/index\");\nvar _IconsDialog = _interopRequireDefault(require(\"./IconsDialog\"));\nvar _config = require(\"@/components/FormGenerator/components/generator/config\");\nvar _db = require(\"../utils/db\");\nvar _index2 = _interopRequireDefault(require(\"../../../views/appSetting/wxAccount/wxTemplate/index\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nvar dateTimeFormat = {\n  date: 'yyyy-MM-dd',\n  week: 'yyyy 第 WW 周',\n  month: 'yyyy-MM',\n  year: 'yyyy',\n  datetime: 'yyyy-MM-dd HH:mm:ss',\n  daterange: 'yyyy-MM-dd',\n  monthrange: 'yyyy-MM',\n  datetimerange: 'yyyy-MM-dd HH:mm:ss'\n};\nvar _default = exports.default = {\n  components: {\n    Templates: _index2.default,\n    TreeNodeDialog: _TreeNodeDialog.default,\n    IconsDialog: _IconsDialog.default\n  },\n  props: ['showField', 'activeData', 'formConf'],\n  data: function data() {\n    return {\n      currentTab: 'field',\n      currentNode: null,\n      dialogVisible: false,\n      iconsVisible: false,\n      currentIconModel: null,\n      dateTypeOptions: [{\n        label: '日(date)',\n        value: 'date'\n      }, {\n        label: '周(week)',\n        value: 'week'\n      }, {\n        label: '月(month)',\n        value: 'month'\n      }, {\n        label: '年(year)',\n        value: 'year'\n      }, {\n        label: '日期时间(datetime)',\n        value: 'datetime'\n      }],\n      dateRangeTypeOptions: [{\n        label: '日期范围(daterange)',\n        value: 'daterange'\n      }, {\n        label: '月范围(monthrange)',\n        value: 'monthrange'\n      }, {\n        label: '日期时间范围(datetimerange)',\n        value: 'datetimerange'\n      }],\n      colorFormatOptions: [{\n        label: 'hex',\n        value: 'hex'\n      }, {\n        label: 'rgb',\n        value: 'rgb'\n      }, {\n        label: 'rgba',\n        value: 'rgba'\n      }, {\n        label: 'hsv',\n        value: 'hsv'\n      }, {\n        label: 'hsl',\n        value: 'hsl'\n      }],\n      justifyOptions: [{\n        label: 'start',\n        value: 'start'\n      }, {\n        label: 'end',\n        value: 'end'\n      }, {\n        label: 'center',\n        value: 'center'\n      }, {\n        label: 'space-around',\n        value: 'space-around'\n      }, {\n        label: 'space-between',\n        value: 'space-between'\n      }],\n      layoutTreeProps: {\n        label: function label(data, node) {\n          var config = data.__config__;\n          return data.componentName || \"\".concat(config.label, \": \").concat(data.__vModel__);\n        }\n      }\n    };\n  },\n  computed: {\n    // documentLink() {\n    //   return (\n    //     this.activeData.__config__.document\n    //     || 'https://element.eleme.cn/#/zh-CN/component/installation'\n    //   )\n    // },\n    dateOptions: function dateOptions() {\n      if (this.activeData.type !== undefined && this.activeData.__config__.tag === 'el-date-picker') {\n        if (this.activeData['start-placeholder'] === undefined) {\n          return this.dateTypeOptions;\n        }\n        return this.dateRangeTypeOptions;\n      }\n      return [];\n    },\n    tagList: function tagList() {\n      return [{\n        label: '输入型组件',\n        options: _config.inputComponents\n      }, {\n        label: '选择型组件',\n        options: _config.selectComponents\n      }];\n    },\n    activeTag: function activeTag() {\n      return this.activeData.__config__.tag;\n    },\n    isShowMin: function isShowMin() {\n      return ['el-input-number', 'el-slider'].indexOf(this.activeTag) > -1;\n    },\n    isShowMax: function isShowMax() {\n      return ['el-input-number', 'el-slider', 'el-rate'].indexOf(this.activeTag) > -1;\n    },\n    isShowStep: function isShowStep() {\n      return ['el-input-number', 'el-slider'].indexOf(this.activeTag) > -1;\n    }\n  },\n  watch: {\n    formConf: {\n      handler: function handler(val) {\n        (0, _db.saveFormConf)(val);\n      },\n      deep: true\n    }\n  },\n  mounted: function mounted() {\n    (0, _db.saveFormConf)(this.formConf);\n  },\n  methods: {\n    addReg: function addReg() {\n      this.activeData.__config__.regList.push({\n        pattern: '',\n        message: ''\n      });\n    },\n    addSelectItem: function addSelectItem() {\n      this.activeData.__slot__.options.push({\n        label: '',\n        value: ''\n      });\n    },\n    addTreeItem: function addTreeItem() {\n      ++this.idGlobal;\n      this.dialogVisible = true;\n      this.currentNode = this.activeData.options;\n    },\n    renderContent: function renderContent(h, _ref) {\n      var _this = this;\n      var node = _ref.node,\n        data = _ref.data,\n        store = _ref.store;\n      return h(\"div\", {\n        \"class\": 'custom-tree-node'\n      }, [h(\"span\", [node.label]), h(\"span\", {\n        \"class\": 'node-operation'\n      }, [h(\"i\", {\n        \"on\": {\n          \"click\": function click() {\n            return _this.append(data);\n          }\n        },\n        \"class\": 'el-icon-plus',\n        \"attrs\": {\n          \"title\": '添加'\n        }\n      }), h(\"i\", {\n        \"on\": {\n          \"click\": function click() {\n            return _this.remove(node, data);\n          }\n        },\n        \"class\": 'el-icon-delete',\n        \"attrs\": {\n          \"title\": '删除'\n        }\n      })])]);\n    },\n    append: function append(data) {\n      if (!data.children) {\n        this.$set(data, 'children', []);\n      }\n      this.dialogVisible = true;\n      this.currentNode = data.children;\n    },\n    remove: function remove(node, data) {\n      this.activeData.__config__.defaultValue = []; // 避免删除时报错\n      var parent = node.parent;\n      var children = parent.data.children || parent.data;\n      var index = children.findIndex(function (d) {\n        return d.id === data.id;\n      });\n      children.splice(index, 1);\n    },\n    addNode: function addNode(data) {\n      this.currentNode.push(data);\n    },\n    setOptionValue: function setOptionValue(item, val) {\n      item.value = (0, _index.isNumberStr)(val) ? +val : val;\n    },\n    setDefaultValue: function setDefaultValue(val) {\n      if (Array.isArray(val)) {\n        return val.join(',');\n      }\n      // if (['string', 'number'].indexOf(typeof val) > -1) {\n      //   return val\n      // }\n      if (typeof val === 'boolean') {\n        return \"\".concat(val);\n      }\n      return val;\n    },\n    onDefaultValueInput: function onDefaultValueInput(str) {\n      if ((0, _util.isArray)(this.activeData.__config__.defaultValue)) {\n        // 数组\n        this.$set(this.activeData.__config__, 'defaultValue', str.split(',').map(function (val) {\n          return (0, _index.isNumberStr)(val) ? +val : val;\n        }));\n      } else if (['true', 'false'].indexOf(str) > -1) {\n        // 布尔\n        this.$set(this.activeData.__config__, 'defaultValue', JSON.parse(str));\n      } else {\n        // 字符串和数字\n        this.$set(this.activeData.__config__, 'defaultValue', (0, _index.isNumberStr)(str) ? +str : str);\n      }\n    },\n    onSwitchValueInput: function onSwitchValueInput(val, name) {\n      if (['true', 'false'].indexOf(val) > -1) {\n        this.$set(this.activeData, name, JSON.parse(val));\n      } else {\n        this.$set(this.activeData, name, (0, _index.isNumberStr)(val) ? +val : val);\n      }\n    },\n    setTimeValue: function setTimeValue(val, type) {\n      var valueFormat = type === 'week' ? dateTimeFormat.date : val;\n      this.$set(this.activeData.__config__, 'defaultValue', null);\n      this.$set(this.activeData, 'value-format', valueFormat);\n      this.$set(this.activeData, 'format', val);\n    },\n    spanChange: function spanChange(val) {\n      this.formConf.span = val;\n    },\n    multipleChange: function multipleChange(val) {\n      this.$set(this.activeData.__config__, 'defaultValue', val ? [] : '');\n    },\n    dateTypeChange: function dateTypeChange(val) {\n      this.setTimeValue(dateTimeFormat[val], val);\n    },\n    rangeChange: function rangeChange(val) {\n      this.$set(this.activeData.__config__, 'defaultValue', val ? [this.activeData.min, this.activeData.max] : this.activeData.min);\n    },\n    rateTextChange: function rateTextChange(val) {\n      if (val) this.activeData['show-score'] = false;\n    },\n    rateScoreChange: function rateScoreChange(val) {\n      if (val) this.activeData['show-text'] = false;\n    },\n    colorFormatChange: function colorFormatChange(val) {\n      this.activeData.__config__.defaultValue = null;\n      this.activeData['show-alpha'] = val.indexOf('a') > -1;\n      this.activeData.__config__.renderKey = +new Date(); // 更新renderKey,重新渲染该组件\n    },\n    openIconsDialog: function openIconsDialog(model) {\n      this.iconsVisible = true;\n      this.currentIconModel = model;\n    },\n    setIcon: function setIcon(val) {\n      this.activeData[this.currentIconModel] = val;\n    },\n    tagChange: function tagChange(tagIcon) {\n      var target = _config.inputComponents.find(function (item) {\n        return item.__config__.tagIcon === tagIcon;\n      });\n      if (!target) target = _config.selectComponents.find(function (item) {\n        return item.__config__.tagIcon === tagIcon;\n      });\n      this.$emit('tag-change', target);\n    },\n    changeRenderKey: function changeRenderKey() {\n      this.activeData.__config__.renderKey = +new Date();\n    }\n  }\n};", null]}