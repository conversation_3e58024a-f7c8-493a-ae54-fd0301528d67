{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/mailun/child/mailundakai.vue?ed4a", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/mailun/child/mailundakai.vue?7fcd", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/mailun/child/mailundakai.vue?84ec", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/mailun/child/mailundakai.vue?4cdb", "uni-app:///pages/mailun/child/mailundakai.vue", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/mailun/child/mailundakai.vue?7288", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/mailun/child/mailundakai.vue?a3b7"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "data", "mailundakai", "onLoad", "onShow", "uni", "title", "onShareAppMessage", "methods"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACsC;;;AAGhG;AACmM;AACnM,gBAAgB,2LAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAwwB,CAAgB,2rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACS5xB;;;;;;;;;eAGA;EACAC,aACA;EACAC;IACA;MACAC;IACA;EACA;EACA;AACA;AACA;EACAC,kCACA;EACAC;IAAA;IAEAC;MACAC;IACA;IACA;MACA;IAEA;EACA;EACA;AACA;AACA;;EAEAC;IACA;IACA;IACA;IACA;IACA;IACA;EAAA,CACA;EAEAC,UAEA;AACA;AAAA,2B;;;;;;;;;;;;;ACnDA;AAAA;AAAA;AAAA;AAA28C,CAAgB,0vCAAG,EAAC,C;;;;;;;;;;;ACA/9C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mailun/child/mailundakai.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mailun/child/mailundakai.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./mailundakai.vue?vue&type=template&id=38519226&scoped=true&\"\nvar renderjs\nimport script from \"./mailundakai.vue?vue&type=script&lang=js&\"\nexport * from \"./mailundakai.vue?vue&type=script&lang=js&\"\nimport style0 from \"./mailundakai.vue?vue&type=style&index=0&id=38519226&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"38519226\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mailun/child/mailundakai.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mailundakai.vue?vue&type=template&id=38519226&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mailundakai.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mailundakai.vue?vue&type=script&lang=js&\"", "<template>\r\n    <view>\r\n        <image class=\"\" :src=\"mailundakai\" mode=\"widthFix\" style=\"width: 100%;\" lazy-load=\"false\" binderror=\"\"\r\n            bindload=\"\" />\r\n\r\n    </view>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n    mailunConfig,\r\n} from '@/api/api.js';\r\nexport default {\r\n    components: {\r\n    },\r\n    data() {\r\n        return {\r\n            mailundakai: ''\r\n        };\r\n    },\r\n    /**\r\n      * 生命周期函数--监听页面加载\r\n      */\r\n    onLoad: function (options) {\r\n    },\r\n    onShow: function () {\r\n\r\n        uni.setNavigationBarTitle({\r\n            title: '平衡脉轮'\r\n        })\r\n        mailunConfig().then(res => {\r\n            this.$set(this, \"mailundakai\", res.data.mailundakai);\r\n\r\n        })\r\n    },\r\n    /**\r\n     * 用户点击右上角分享\r\n     */\r\n    // #ifdef MP\r\n    onShareAppMessage: function () {\r\n        // return {\r\n        // \ttitle: this.articleInfo.title,\r\n        // \timageUrl: this.articleInfo.imageInput.length ? this.articleInfo.imageInput[0] : \"\",\r\n        // \tdesc: this.articleInfo.synopsis,\r\n        // \tpath: '/pages/news_details/index?id=' + this.id\r\n        // };\r\n    },\r\n    // #endif\r\n    methods: {\r\n\r\n    }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.botton_1 {\r\n    margin-left: 20rpx;\r\n    width: 710rpx;\r\n    background-color: #c9ab79;\r\n    color: #fff;\r\n    font-size: 28rpx;\r\n    font-weight: 600;\r\n    height: 88rpx;\r\n    border-radius: 50rpx;\r\n    text-align: center;\r\n    line-height: 88rpx;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mailundakai.vue?vue&type=style&index=0&id=38519226&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mailundakai.vue?vue&type=style&index=0&id=38519226&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363903397\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}