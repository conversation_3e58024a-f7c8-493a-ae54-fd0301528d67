<view class="navTabBox"><view class="longTab"><scroll-view style="white-space:nowrap;display:flex;" scroll-x="true" scroll-with-animation="{{true}}" scroll-left="{{tabLeft}}" show-scrollbar="true"><block wx:for="{{tabTitle}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['longItem','line1',index===tabClick?'click':'']}}" style="{{('width:'+isWidth+'px')}}" data-index="{{index}}" id="{{'id'+index}}" data-event-opts="{{[['tap',[['longClick',[index]]]]]}}" bindtap="__e">{{item.name}}</view></block><view class="underlineBox" style="{{('transform:translateX('+isLeft+'px);width:'+isWidth+'px')}}"><view class="underline"></view></view></scroll-view></view><block wx:if="{{tabClick>0&&tabTitle[tabClick].child?$root.g0>0:0}}"><view class="child-box"><scroll-view scroll-x="true"><view class="wrapper"><block wx:for="{{tabTitle[tabClick].child?tabTitle[tabClick].child:[]}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['childTab',['$0',index],['tabClick']]]]]}}" class="{{['child-item',(index==childIndex)?'on':'']}}" bindtap="__e"><image style="{{'background-color:'+(item.extra?'none':'#f7f7f7')+';'}}" src="{{item.extra}}" mode></image><view class="txt line1">{{item.name}}</view></view></block></view></scroll-view></view></block></view>