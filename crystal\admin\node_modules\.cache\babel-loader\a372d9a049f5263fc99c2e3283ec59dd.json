{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\api\\distribution.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\api\\distribution.js", "mtime": 1753666157716}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\eslint-loader\\index.js", "mtime": 1753666298172}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.configApi = configApi;\nexports.configUpdateApi = configUpdateApi;\nexports.promoterListApi = promoterListApi;\nexports.spreadClearApi = spreadClearApi;\nexports.spreadListApi = spreadListApi;\nexports.spreadOrderListApi = spreadOrderListApi;\nexports.spreadStatisticsApi = spreadStatisticsApi;\nvar _request = _interopRequireDefault(require(\"@/utils/request\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\n/**\r\n * @description 分销设置 -- 详情\r\n */\nfunction configApi() {\n  return (0, _request.default)({\n    url: '/admin/store/retail/spread/manage/get',\n    method: 'get'\n  });\n}\n\n/**\r\n * @description 分销设置 -- 表单提交\r\n */\nfunction configUpdateApi(data) {\n  return (0, _request.default)({\n    url: '/admin/store/retail/spread/manage/set',\n    method: 'post',\n    data: data\n  });\n}\n\n/**\r\n * @description 分销员 -- 列表\r\n */\nfunction promoterListApi(params) {\n  return (0, _request.default)({\n    url: '/admin/store/retail/list',\n    method: 'get',\n    params: params\n  });\n}\n\n/**\r\n * @description 推广人 -- 列表\r\n */\nfunction spreadListApi(params, data) {\n  return (0, _request.default)({\n    url: '/admin/store/retail/spread/userlist',\n    method: 'post',\n    params: params,\n    data: data\n  });\n}\n\n/**\r\n * @description 推广人订单 -- 列表\r\n */\nfunction spreadOrderListApi(params, data) {\n  return (0, _request.default)({\n    url: '/admin/store/retail/spread/orderlist',\n    method: 'post',\n    params: params,\n    data: data\n  });\n}\n\n/**\r\n * @description 推广人 -- 清除上级推广人\r\n */\nfunction spreadClearApi(id) {\n  return (0, _request.default)({\n    url: \"/admin/store/retail/spread/clean/\".concat(id),\n    method: 'get'\n  });\n}\n\n/**\r\n * @description 分销统计\r\n */\nfunction spreadStatisticsApi(params) {\n  return (0, _request.default)({\n    url: \"/admin/store/retail/statistics\",\n    method: 'get',\n    params: params\n  });\n}", null]}