(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/users/user_pwd_edit/index"],{"162d":function(t,e,n){"use strict";(function(t,e){var i=n("47a9");n("5c2d");i(n("3240"));var u=i(n("8e12"));t.__webpack_require_UNI_MP_PLUGIN__=n,e(u.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},"378a":function(t,e,n){"use strict";n.r(e);var i=n("5388"),u=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);e["default"]=u.a},5388:function(t,e,n){"use strict";var i=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var u=i(n("7eb4")),r=i(n("ee10")),o=i(n("74cc")),a=n("fdf2"),s=n("5904"),c=n("cda4"),f=n("8f59"),l={mixins:[o.default],components:{authorize:function(){Promise.all([n.e("common/vendor"),n.e("components/Authorize")]).then(function(){return resolve(n("cf49"))}.bind(null,n)).catch(n.oe)}},data:function(){return{userInfo:{},phone:"",password:"",captcha:"",qr_password:"",isAuto:!1,isShowAuth:!1}},computed:(0,f.mapGetters)(["isLogin"]),watch:{isLogin:{handler:function(t,e){t&&this.getUserInfo()},deep:!0}},onLoad:function(){this.isLogin?this.getUserInfo():(0,c.toLogin)()},methods:{onLoadFun:function(t){this.getUserInfo()},authColse:function(t){this.isShowAuth=t},getUserInfo:function(){var t=this;(0,s.getUserInfo)().then((function(e){var n=e.data.phone,i=n.substr(0,3)+"****"+n.substr(7);t.$set(t,"userInfo",e.data),t.phone=i}))},code:function(){var t=this;return(0,r.default)(u.default.mark((function e(){var n;return u.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=t,n.userInfo.phone){e.next=3;break}return e.abrupt("return",n.$util.Tips({title:"手机号码不存在,无法发送验证码！"}));case 3:return e.next=5,(0,a.registerVerify)(n.userInfo.phone).then((function(t){n.$util.Tips({title:t.message}),n.sendCode()})).catch((function(t){return n.$util.Tips({title:t})}));case 5:case"end":return e.stop()}}),e)})))()},editPwd:function(t){var e=this,n=t.detail.value.password,i=t.detail.value.qr_password,u=t.detail.value.captcha;return n?/^[a-zA-Z]\w{5,17}$/i.test(n)?i!=n?e.$util.Tips({title:"两次输入的密码不一致！"}):u?void(0,a.phoneRegisterReset)({account:e.userInfo.phone,captcha:u,password:n}).then((function(t){return e.$util.Tips({title:t.message},{tab:3,url:1})})).catch((function(t){return e.$util.Tips({title:t})})):e.$util.Tips({title:"请输入验证码"}):e.$util.Tips({title:"以字母开头，长度在6~18之间，只能包含字符、数字和下划线"}):e.$util.Tips({title:"请输入新密码"})}}};e.default=l},"5e69":function(t,e,n){"use strict";var i=n("9b95"),u=n.n(i);u.a},"6c8a":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return u})),n.d(e,"a",(function(){}));var i=function(){var t=this.$createElement;this._self._c},u=[]},"8e12":function(t,e,n){"use strict";n.r(e);var i=n("6c8a"),u=n("378a");for(var r in u)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return u[t]}))}(r);n("5e69");var o=n("828b"),a=Object(o["a"])(u["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);e["default"]=a.exports},"9b95":function(t,e,n){}},[["162d","common/runtime","common/vendor"]]]);