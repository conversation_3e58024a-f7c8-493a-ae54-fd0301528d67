{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\order\\index.vue?vue&type=template&id=007ed227&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\order\\index.vue", "mtime": 1753666157910}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\n<div class=\"divBox relative\">\n  <el-card class=\"box-card\">\n    <div  class=\"clearfix\">\n      <div class=\"container\">\n        <el-form size=\"small\" label-width=\"100px\">\n          <el-form-item label=\"订单状态：\" v-if=\"checkPermi(['admin:order:status:num'])\">\n            <el-radio-group v-model=\"tableFrom.status\" type=\"button\" @change=\"seachList\">\n              <el-radio-button label=\"all\">全部 {{ '(' +orderChartType.all?orderChartType.all:0 + ')' }}</el-radio-button>\n              <el-radio-button label=\"unPaid\">未支付 {{ '(' +orderChartType.unPaid?orderChartType.unPaid:0+ ')' }}</el-radio-button>\n              <el-radio-button label=\"notShipped\">未发货 {{ '(' +orderChartType.notShipped?orderChartType.notShipped:0+ ')' }}</el-radio-button>\n              <el-radio-button label=\"spike\">待收货 {{ '(' +orderChartType.spike?orderChartType.spike:0+ ')' }}</el-radio-button>\n              <el-radio-button label=\"bargain\">待评价 {{ '(' +orderChartType.bargain?orderChartType.bargain:0+ ')' }}</el-radio-button>\n              <el-radio-button label=\"complete\">交易完成 {{ '(' +orderChartType.complete?orderChartType.complete:0+ ')' }}</el-radio-button>\n              <el-radio-button label=\"toBeWrittenOff\">待核销 {{ '(' +orderChartType.toBeWrittenOff?orderChartType.toBeWrittenOff:0+ ')' }}</el-radio-button>\n              <el-radio-button label=\"refunding\">退款中 {{ '(' +orderChartType.refunding?orderChartType.refunding:0+ ')' }}</el-radio-button>\n              <el-radio-button label=\"refunded\">已退款 {{ '(' +orderChartType.refunded?orderChartType.refunded:0+ ')' }}</el-radio-button>\n              <el-radio-button label=\"deleted\">已删除 {{ '(' +orderChartType.deleted?orderChartType.deleted:0+ ')' }}</el-radio-button>\n            </el-radio-group>\n          </el-form-item>\n          <el-form-item label=\"时间选择：\" class=\"width100\">\n            <el-radio-group v-model=\"tableFrom.dateLimit\" type=\"button\" class=\"mr20\" size=\"small\" @change=\"selectChange(tableFrom.dateLimit)\">\n              <el-radio-button v-for=\"(item,i) in fromList.fromTxt\" :key=\"i\" :label=\"item.val\">{{ item.text }}</el-radio-button>\n            </el-radio-group>\n            <el-date-picker v-model=\"timeVal\" value-format=\"yyyy-MM-dd\" format=\"yyyy-MM-dd\" size=\"small\" type=\"daterange\" placement=\"bottom-end\" placeholder=\"自定义时间\" style=\"width: 220px;\" @change=\"onchangeTime\" />\n          </el-form-item>\n          <!-- <el-form-item label=\"订单类型：\" class=\"width100\">\n            <el-select v-model=\"tableFrom.type\" clearable placeholder=\"请选择\" class=\"selWidth\" @change=\"seachList\">\n              <el-option\n                v-for=\"item in options\"\n                :key=\"item.value\"\n                :label=\"item.label\"\n                :value=\"item.value\">\n              </el-option>\n            </el-select>\n          </el-form-item> -->\n           <el-form-item label=\"订单号：\" class=\"width100\">\n            <el-input v-model=\"tableFrom.orderNo\" placeholder=\"请输入订单号\" class=\"selWidth\" size=\"small\" clearable>\n              <el-button slot=\"append\" icon=\"el-icon-search\" size=\"small\" @click=\"seachList\" />\n            </el-input>\n          </el-form-item>\n          <el-form-item class=\"width100\">\n            <el-button size=\"small\" @click=\"exports\" v-hasPermi=\"['admin:export:excel:order']\">导出</el-button>\n          </el-form-item>\n        </el-form>\n      </div>\n    </div>\n  </el-card>\n  <div class=\"mt20\">\n    <!-- <cards-data :cardLists=\"cardLists\" v-if=\"checkPermi(['admin:order:list:data'])\"></cards-data> -->\n  </div>\n  <el-card class=\"box-card\">\n     <el-table\n      v-loading=\"listLoading\"\n      :data=\"tableData.data\"\n      size=\"mini\"\n      class=\"table\"\n      highlight-current-row\n      :header-cell-style=\" {fontWeight:'bold'}\"\n      :row-key=\"(row)=>{ return row.orderId}\"\n    >\n    <!-- @selection-change=\"handleSelectionChange\" -->\n      <!-- <el-table-column\n        type=\"selection\"\n        :reserve-selection=\"true\"\n        width=\"55\"\n      /> -->\n      <el-table-column\n        label=\"订单号\"\n        min-width=\"210\"\n      >\n        <template slot-scope=\"scope\">\n          <span style=\"display: block;\" v-text=\"scope.row.orderId\" />\n          <span v-show=\"scope.row.isDel\" style=\"color: #ED4014;display: block;\">用户已删除</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n      prop=\"orderType\"\n      label=\"订单类型\"\n      min-width=\"110\"\n      />\n      <el-table-column\n        prop=\"realName\"\n        label=\"收货人\"\n        min-width=\"100\"\n      />\n      <el-table-column\n        label=\"商品信息\"\n        min-width=\"400\"\n      >\n        <template slot-scope=\"scope\">\n          <el-popover trigger=\"hover\" placement=\"right\" :open-delay=\"800\">\n            <div v-if=\" scope.row.productList && scope.row.productList.length\" slot=\"reference\">\n              <div v-for=\"(val, i ) in scope.row.productList\" :key=\"i\" class=\"tabBox acea-row row-middle\" style=\"flex-wrap: inherit;\">\n                <div class=\"demo-image__preview mr10\">\n                  <el-image\n                    :src=\"val.info.image\"\n                    :preview-src-list=\"[val.info.image]\"\n                  />\n                </div>\n                <div class=\"text_overflow\">\n                  <span class=\"tabBox_tit mr10\">{{ val.info.productName + ' | ' }}{{ (val.info.sku ? val.info.sku:'-' ) + ' | ' }}{{ scope.row.type == 2 ?( val.info.width + 'x' + val.info.height + \"mm\"):''  }}</span>\n                  <span class=\"tabBox_pice\">{{ '￥'+ val.info.price ? val.info.price + ' x '+ val.info.payNum : '-' }}</span>\n                </div>\n              </div>\n            </div>\n            <div class=\"pup_card\" v-if=\" scope.row.productList && scope.row.productList.length\">\n              <div v-for=\"(val, i ) in scope.row.productList\" :key=\"i\" class=\"tabBox acea-row row-middle\" style=\"flex-wrap: inherit;\">\n                <div class=\"\">\n                  <span class=\"tabBox_tit mr10\">{{ val.info.productName + ' | ' }}{{ (val.info.sku ? val.info.sku:'-' ) + ' | ' }}{{ scope.row.type == 2 ?( val.info.width + 'x' + val.info.height + \"mm\"):''  }}</span>\n                  <span class=\"tabBox_pice\">{{ '￥'+ val.info.price ? val.info.price + ' x '+ val.info.payNum : '-' }}</span>\n                </div>\n              </div>\n            </div>\n          </el-popover>\n        </template>\n      </el-table-column>\n      <el-table-column\n        prop=\"payPrice\"\n        label=\"实际支付\"\n        min-width=\"80\"\n      />\n      <el-table-column\n        label=\"支付方式\"\n        min-width=\"80\"\n      >\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.payTypeStr }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"订单状态\"\n        min-width=\"100\"\n      >\n        <template slot-scope=\"scope\">\n            <div>\n              <div v-if=\"scope.row.refundStatus === 1 || scope.row.refundStatus === 2\" class=\"refunding\" >\n                <template>\n                   <el-popover trigger=\"hover\" placement=\"left\" :open-delay=\"800\">\n                     <b style=\"color: #f124c7;\" slot=\"reference\">{{scope.row.statusStr.value}}</b>\n                     <div class=\"pup_card flex-column\">\n                        <span>退款原因：{{scope.row.refundReasonWap}}</span>\n                        <span>备注说明：{{scope.row.refundReasonWapExplain}}</span>\n                        <span>退款时间：{{scope.row.refundReasonTime}}</span>\n                        <span class=\"acea-row\">\n                          退款凭证：\n                          <template v-if=\"scope.row.refundReasonWapImg\">\n                            <div v-for=\"(item, index) in scope.row.refundReasonWapImg.split(',')\" :key=\"index\" class=\"demo-image__preview\" style=\"width: 35px;height: auto;display: inline-block;\">\n                            <el-image\n                              :src=\"item\"\n                              :preview-src-list=\"[item]\"\n                            />\n                          </div>\n                          </template>\n                          <span v-else style=\"display: inline-block\">无</span>\n                        </span>\n                     </div>\n                   </el-popover>\n                </template>\n              </div>\n              <span v-else>{{ scope.row.statusStr.value }}</span>\n            </div>\n        </template>\n      </el-table-column>\n      <el-table-column\n        prop=\"createTime\"\n        label=\"下单时间\"\n        min-width=\"150\"\n      />\n      <el-table-column label=\"操作\" min-width=\"150\" fixed=\"right\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <el-button v-if=\"scope.row.paid === false\" type=\"text\" size=\"small\" @click=\"edit(scope.row)\" class=\"mr10\" v-hasPermi=\"['admin:order:update:price']\">编辑</el-button> \n          <el-button v-if=\"scope.row.statusStr.key === 'notShipped' && scope.row.refundStatus ===0\" type=\"text\" size=\"small\" class=\"mr10\" @click=\"sendOrder(scope.row)\" v-hasPermi=\"['admin:order:send']\">发送货</el-button>\n          <el-button v-if=\" scope.row.statusStr.key === 'toBeWrittenOff'  && scope.row.paid == true && scope.row.refundStatus === 0 \" type=\"text\" size=\"small\" class=\"mr10\" v-hasPermi=\"['admin:order:write:update']\" @click=\"onWriteOff(scope.row)\">立即核销</el-button>\n          <el-dropdown trigger=\"click\">\n            <span class=\"el-dropdown-link\">\n              更多<i class=\"el-icon-arrow-down el-icon--right\" />\n            </span>\n            <el-dropdown-menu slot=\"dropdown\">\n              <el-dropdown-item @click.native=\"onOrderDetails(scope.row.orderId)\" v-if=\"checkPermi(['admin:order:info'])\">订单详情</el-dropdown-item>\n              <el-dropdown-item @click.native=\"onOrderLog(scope.row.orderId)\" v-if=\"checkPermi(['admin:order:status:list'])\">订单记录</el-dropdown-item>\n              <el-dropdown-item @click.native=\"onOrderMark(scope.row)\" v-if=\"checkPermi(['admin:order:mark'])\">订单备注</el-dropdown-item>\n              <el-dropdown-item v-if=\"scope.row.refundStatus === 1 && checkPermi(['admin:order:refund:refuse'])\" @click.native=\"onOrderRefuse(scope.row)\">拒绝退款</el-dropdown-item>\n              <!--v-show=\"((scope.row.statusStr.key !== 'refunded' && scope.row.statusStr.key !== 'unPaid') && (parseFloat(scope.row.payPrice) >= parseFloat(scope.row.refundPrice))) || (scope.row.payPrice == 0 && [0,1].indexOf(scope.row.refundStatus) !== -1)\"-->\n              <el-dropdown-item v-if=\"scope.row.refundStatus === 1 && checkPermi(['admin:order:refund'])\" @click.native=\"onOrderRefund(scope.row)\" >立即退款</el-dropdown-item>\n              <el-dropdown-item v-if=\"scope.row.statusStr.key === 'deleted' && checkPermi(['admin:order:delete'])\" @click.native=\"handleDelete(scope.row, scope.$index)\">删除订单</el-dropdown-item>\n              <!-- <el-dropdown-item v-if=\"scope.row.statusStr.key !== 'unPaid'\" @click.native=\"onOrderPrint(scope.row)\" >打印小票</el-dropdown-item> -->\n            </el-dropdown-menu>\n          </el-dropdown>\n        </template>\n      </el-table-column>\n    </el-table>\n    <div class=\"block\">\n      <el-pagination\n        :page-sizes=\"[20, 40, 60, 80]\"\n        :page-size=\"tableFrom.limit\"\n        :current-page=\"tableFrom.page\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        :total=\"tableData.total\"\n        @size-change=\"handleSizeChange\"\n        @current-change=\"pageChange\"\n      />\n    </div>\n  </el-card>\n  <!--编辑-->\n  <el-dialog\n    title=\"编辑订单\"\n    :visible.sync=\"dialogVisible\"\n    width=\"500px\"\n    :before-close=\"handleClose\">\n      <zb-parser\n      v-if=\"dialogVisible\"\n      :form-id=\"104\"\n      :is-create=\"isCreate\"\n      :edit-data=\"editData\"\n      @submit=\"handlerSubmit\"\n      @resetForm=\"resetForm\"\n    />\n  </el-dialog>\n\n  <!--记录-->\n  <el-dialog\n    title=\"操作记录\"\n    :visible.sync=\"dialogVisibleJI\"\n    width=\"700px\"\n  >\n    <el-table\n      v-loading=\"LogLoading\"\n      border\n      :data=\"tableDataLog.data\"\n      style=\"width: 100%\"\n    >\n      <el-table-column\n        prop=\"oid\"\n        align=\"center\"\n        label=\"ID\"\n        min-width=\"80\"\n      />\n      <el-table-column\n        prop=\"changeMessage\"\n        label=\"操作记录\"\n        align=\"center\"\n        min-width=\"280\"\n      />\n      <el-table-column\n        prop=\"createTime\"\n        label=\"操作时间\"\n        align=\"center\"\n        min-width=\"280\"\n      />\n    </el-table>\n    <div class=\"block\">\n      <el-pagination\n        :page-sizes=\"[10, 20, 30, 40]\"\n        :page-size=\"tableFromLog.limit\"\n        :current-page=\"tableFromLog.page\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        :total=\"tableDataLog.total\"\n        @size-change=\"handleSizeChangeLog\"\n        @current-change=\"pageChangeLog\"\n      />\n    </div>\n  </el-dialog>\n\n  <!--详情-->\n  <details-from ref=\"orderDetail\" :orderId=\"orderId\"/>\n\n  <!-- 发送货 -->\n  <order-send ref=\"send\" :orderId=\"orderId\" @submitFail=\"getList\"></order-send>\n\n  <!-- 发送货视频号商品 -->\n  <order-video-send ref=\"videoSend\" :orderId=\"orderId\" @submitFail=\"getList\"></order-video-send>\n\n  <!--拒绝退款-->\n  <el-dialog\n    title=\"拒绝退款原因\"\n    v-if=\"RefuseVisible\"\n    :visible.sync=\"RefuseVisible\"\n    width=\"500px\"\n    :before-close=\"RefusehandleClose\">\n    <zb-parser\n      :form-id=\"106\"\n      :is-create=\"1\"\n      :edit-data=\"RefuseData\"\n      @submit=\"RefusehandlerSubmit\"\n      @resetForm=\"resetFormRefusehand\"\n    />\n  </el-dialog>\n\n  <!--立即退款-->\n  <el-dialog\n    title=\"退款处理\"\n    :visible.sync=\"refundVisible\"\n    width=\"500px\"\n    :before-close=\"refundhandleClose\">\n    <zb-parser\n      :form-id=\"107\"\n      :is-create=\"1\"\n      :edit-data=\"refundData\"\n      @submit=\"refundhandlerSubmit\"\n      v-if=\"refundVisible\"\n      @resetForm=\"resetFormRefundhandler\"\n    />\n  </el-dialog>\n</div>\n", null]}