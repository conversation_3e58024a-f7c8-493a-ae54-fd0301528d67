{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\element-ui\\lib\\utils\\after-leave.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\element-ui\\lib\\utils\\after-leave.js", "mtime": 1753666304618}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}], "contextDependencies": [], "result": ["'use strict';\n\nexports.__esModule = true;\nexports.default = function (instance, callback) {\n  var speed = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 300;\n  var once = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;\n  if (!instance || !callback) throw new Error('instance & callback is required');\n  var called = false;\n  var afterLeaveCallback = function afterLeaveCallback() {\n    if (called) return;\n    called = true;\n    if (callback) {\n      callback.apply(null, arguments);\n    }\n  };\n  if (once) {\n    instance.$once('after-leave', afterLeaveCallback);\n  } else {\n    instance.$on('after-leave', afterLeaveCallback);\n  }\n  setTimeout(function () {\n    afterLeaveCallback();\n  }, speed + 100);\n};\n; /**\n   * Bind after-leave event for vue instance. Make sure after-leave is called in any browsers.\n   *\n   * @param {Vue} instance Vue instance.\n   * @param {Function} callback callback of after-leave event\n   * @param {Number} speed the speed of transition, default value is 300ms\n   * @param {Boolean} once weather bind after-leave once. default value is false.\n   */", null]}