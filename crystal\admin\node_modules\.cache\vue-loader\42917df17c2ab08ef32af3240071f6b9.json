{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\userprocess.vue?vue&type=template&id=2370f41a", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\userprocess.vue", "mtime": 1753666157945}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\n<div class=\"divBox relative\">\n  <el-form :inline=\"true\" :model=\"dataForm\">\n    <el-form-item>\n      <el-button type=\"primary\" @click=\"addOrUpdateHandle()\">新增</el-button>\n    </el-form-item>\n    <el-form-item style=\"float:right\">\n      <el-button @click=\"$router.go(-1)\" type=\"primary\">返回上一页</el-button>\n    </el-form-item>\n  </el-form>\n  <div class=\"block\">\n    <el-timeline>\n      <el-timeline-item v-for=\"item in dataList\" :timestamp=\"item.doTime || item.addTime\" placement=\"top\">\n        <el-card>\n          <div style=\"height: 30px;line-height: 30px;font-size: 20px;font-weight: bold;\">{{ item.name }}</div>\n          <div style=\"height: 30px;line-height: 30px;font-size: 18px;font-weight: bold;\">当前用户状态：{{ item.userStatus }}</div>\n          <div  style=\"margin-top: 10px;\" v-html=\"item.remarks\"></div>\n        </el-card>\n      </el-timeline-item>\n    </el-timeline>\n  </div>\n  <!-- 弹窗, 新增 / 修改 -->\n  <add-or-update v-if=\"addOrUpdateVisible\" ref=\"addOrUpdate\" @refreshDataList=\"getDataList\"></add-or-update>\n</div>\n", null]}