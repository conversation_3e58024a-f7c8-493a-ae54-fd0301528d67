{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\FormGenerator\\utils\\loadMonaco.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\FormGenerator\\utils\\loadMonaco.js", "mtime": 1753666157775}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\eslint-loader\\index.js", "mtime": 1753666298172}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = loadMonaco;\nvar _loadScript = require(\"./loadScript\");\nvar _elementUi = _interopRequireDefault(require(\"element-ui\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\n// monaco-editor单例\nvar monacoEidtor;\n\n/**\r\n * 动态加载monaco-editor cdn资源\r\n * @param {Function} cb 回调，必填\r\n */\nfunction loadMonaco(cb) {\n  if (monacoEidtor) {\n    cb(monacoEidtor);\n    return;\n  }\n  var vs = 'https://cdn.bootcss.com/monaco-editor/0.18.0/min/vs';\n\n  // 使用element ui实现加载提示\n  var loading = _elementUi.default.Loading.service({\n    fullscreen: true,\n    lock: true,\n    text: '编辑器资源初始化中...',\n    spinner: 'el-icon-loading',\n    background: 'rgba(255, 255, 255, 0.5)'\n  });\n  !window.require && (window.require = {});\n  !window.require.paths && (window.require.paths = {});\n  window.require.paths.vs = vs;\n  (0, _loadScript.loadScriptQueue)([\"\".concat(vs, \"/loader.js\"), \"\".concat(vs, \"/editor/editor.main.nls.js\"), \"\".concat(vs, \"/editor/editor.main.js\")], function () {\n    loading.close();\n    // eslint-disable-next-line no-undef\n    monacoEidtor = monaco;\n    cb(monacoEidtor);\n  });\n}", null]}