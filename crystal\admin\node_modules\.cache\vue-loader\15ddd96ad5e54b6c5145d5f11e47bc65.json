{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\FormGenerator\\index\\RightPanel.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\FormGenerator\\index\\RightPanel.vue", "mtime": 1753666157771}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport { isArray } from 'util'\r\nimport TreeNodeDialog from './TreeNodeDialog'\r\nimport { isNumberStr } from '../utils/index'\r\nimport IconsDialog from './IconsDialog'\r\nimport {\r\n  inputComponents, selectComponents, layoutComponents\r\n} from '@/components/FormGenerator/components/generator/config'\r\nimport { saveFormConf } from '../utils/db'\r\nimport Templates from \"../../../views/appSetting/wxAccount/wxTemplate/index\";\r\n\r\nconst dateTimeFormat = {\r\n  date: 'yyyy-MM-dd',\r\n  week: 'yyyy 第 WW 周',\r\n  month: 'yyyy-MM',\r\n  year: 'yyyy',\r\n  datetime: 'yyyy-MM-dd HH:mm:ss',\r\n  daterange: 'yyyy-MM-dd',\r\n  monthrange: 'yyyy-MM',\r\n  datetimerange: 'yyyy-MM-dd HH:mm:ss'\r\n}\r\n\r\nexport default {\r\n  components: {\r\n    Templates,\r\n    TreeNodeDialog,\r\n    IconsDialog\r\n  },\r\n  props: ['showField', 'activeData', 'formConf'],\r\n  data() {\r\n    return {\r\n      currentTab: 'field',\r\n      currentNode: null,\r\n      dialogVisible: false,\r\n      iconsVisible: false,\r\n      currentIconModel: null,\r\n      dateTypeOptions: [\r\n        {\r\n          label: '日(date)',\r\n          value: 'date'\r\n        },\r\n        {\r\n          label: '周(week)',\r\n          value: 'week'\r\n        },\r\n        {\r\n          label: '月(month)',\r\n          value: 'month'\r\n        },\r\n        {\r\n          label: '年(year)',\r\n          value: 'year'\r\n        },\r\n        {\r\n          label: '日期时间(datetime)',\r\n          value: 'datetime'\r\n        }\r\n      ],\r\n      dateRangeTypeOptions: [\r\n        {\r\n          label: '日期范围(daterange)',\r\n          value: 'daterange'\r\n        },\r\n        {\r\n          label: '月范围(monthrange)',\r\n          value: 'monthrange'\r\n        },\r\n        {\r\n          label: '日期时间范围(datetimerange)',\r\n          value: 'datetimerange'\r\n        }\r\n      ],\r\n      colorFormatOptions: [\r\n        {\r\n          label: 'hex',\r\n          value: 'hex'\r\n        },\r\n        {\r\n          label: 'rgb',\r\n          value: 'rgb'\r\n        },\r\n        {\r\n          label: 'rgba',\r\n          value: 'rgba'\r\n        },\r\n        {\r\n          label: 'hsv',\r\n          value: 'hsv'\r\n        },\r\n        {\r\n          label: 'hsl',\r\n          value: 'hsl'\r\n        }\r\n      ],\r\n      justifyOptions: [\r\n        {\r\n          label: 'start',\r\n          value: 'start'\r\n        },\r\n        {\r\n          label: 'end',\r\n          value: 'end'\r\n        },\r\n        {\r\n          label: 'center',\r\n          value: 'center'\r\n        },\r\n        {\r\n          label: 'space-around',\r\n          value: 'space-around'\r\n        },\r\n        {\r\n          label: 'space-between',\r\n          value: 'space-between'\r\n        }\r\n      ],\r\n      layoutTreeProps: {\r\n        label(data, node) {\r\n          const config = data.__config__\r\n          return data.componentName || `${config.label}: ${data.__vModel__}`\r\n        }\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    // documentLink() {\r\n    //   return (\r\n    //     this.activeData.__config__.document\r\n    //     || 'https://element.eleme.cn/#/zh-CN/component/installation'\r\n    //   )\r\n    // },\r\n    dateOptions() {\r\n      if (\r\n        this.activeData.type !== undefined &&\r\n        this.activeData.__config__.tag === 'el-date-picker'\r\n      ) {\r\n        if (this.activeData['start-placeholder'] === undefined) {\r\n          return this.dateTypeOptions\r\n        }\r\n        return this.dateRangeTypeOptions\r\n      }\r\n      return []\r\n    },\r\n    tagList() {\r\n      return [\r\n        {\r\n          label: '输入型组件',\r\n          options: inputComponents\r\n        },\r\n        {\r\n          label: '选择型组件',\r\n          options: selectComponents\r\n        }\r\n      ]\r\n    },\r\n    activeTag() {\r\n      return this.activeData.__config__.tag\r\n    },\r\n    isShowMin() {\r\n      return ['el-input-number', 'el-slider'].indexOf(this.activeTag) > -1\r\n    },\r\n    isShowMax() {\r\n      return ['el-input-number', 'el-slider', 'el-rate'].indexOf(this.activeTag) > -1\r\n    },\r\n    isShowStep() {\r\n      return ['el-input-number', 'el-slider'].indexOf(this.activeTag) > -1\r\n    }\r\n  },\r\n  watch: {\r\n    formConf: {\r\n      handler(val) {\r\n        saveFormConf(val)\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  mounted() {\r\n    saveFormConf(this.formConf)\r\n  },\r\n  methods: {\r\n    addReg() {\r\n      this.activeData.__config__.regList.push({\r\n        pattern: '',\r\n        message: ''\r\n      })\r\n    },\r\n    addSelectItem() {\r\n      this.activeData.__slot__.options.push({\r\n        label: '',\r\n        value: ''\r\n      })\r\n    },\r\n    addTreeItem() {\r\n      ++this.idGlobal\r\n      this.dialogVisible = true\r\n      this.currentNode = this.activeData.options\r\n    },\r\n    renderContent(h, { node, data, store }) {\r\n      return (\r\n        <div class='custom-tree-node'>\r\n          <span>{node.label}</span>\r\n          <span class='node-operation'>\r\n            <i on-click={() => this.append(data)}\r\n              class='el-icon-plus'\r\n              title='添加'\r\n            ></i>\r\n            <i on-click={() => this.remove(node, data)}\r\n              class='el-icon-delete'\r\n              title='删除'\r\n            ></i>\r\n          </span>\r\n        </div>\r\n      )\r\n    },\r\n    append(data) {\r\n      if (!data.children) {\r\n        this.$set(data, 'children', [])\r\n      }\r\n      this.dialogVisible = true\r\n      this.currentNode = data.children\r\n    },\r\n    remove(node, data) {\r\n      this.activeData.__config__.defaultValue = [] // 避免删除时报错\r\n      const { parent } = node\r\n      const children = parent.data.children || parent.data\r\n      const index = children.findIndex(d => d.id === data.id)\r\n      children.splice(index, 1)\r\n    },\r\n    addNode(data) {\r\n      this.currentNode.push(data)\r\n    },\r\n    setOptionValue(item, val) {\r\n      item.value = isNumberStr(val) ? +val : val\r\n    },\r\n    setDefaultValue(val) {\r\n      if (Array.isArray(val)) {\r\n        return val.join(',')\r\n      }\r\n      // if (['string', 'number'].indexOf(typeof val) > -1) {\r\n      //   return val\r\n      // }\r\n      if (typeof val === 'boolean') {\r\n        return `${val}`\r\n      }\r\n      return val\r\n    },\r\n    onDefaultValueInput(str) {\r\n      if (isArray(this.activeData.__config__.defaultValue)) {\r\n        // 数组\r\n        this.$set(\r\n          this.activeData.__config__,\r\n          'defaultValue',\r\n          str.split(',').map(val => (isNumberStr(val) ? +val : val))\r\n        )\r\n      } else if (['true', 'false'].indexOf(str) > -1) {\r\n        // 布尔\r\n        this.$set(this.activeData.__config__, 'defaultValue', JSON.parse(str))\r\n      } else {\r\n        // 字符串和数字\r\n        this.$set(\r\n          this.activeData.__config__,\r\n          'defaultValue',\r\n          isNumberStr(str) ? +str : str\r\n        )\r\n      }\r\n    },\r\n    onSwitchValueInput(val, name) {\r\n      if (['true', 'false'].indexOf(val) > -1) {\r\n        this.$set(this.activeData, name, JSON.parse(val))\r\n      } else {\r\n        this.$set(this.activeData, name, isNumberStr(val) ? +val : val)\r\n      }\r\n    },\r\n    setTimeValue(val, type) {\r\n      const valueFormat = type === 'week' ? dateTimeFormat.date : val\r\n      this.$set(this.activeData.__config__, 'defaultValue', null)\r\n      this.$set(this.activeData, 'value-format', valueFormat)\r\n      this.$set(this.activeData, 'format', val)\r\n    },\r\n    spanChange(val) {\r\n      this.formConf.span = val\r\n    },\r\n    multipleChange(val) {\r\n      this.$set(this.activeData.__config__, 'defaultValue', val ? [] : '')\r\n    },\r\n    dateTypeChange(val) {\r\n      this.setTimeValue(dateTimeFormat[val], val)\r\n    },\r\n    rangeChange(val) {\r\n      this.$set(\r\n        this.activeData.__config__,\r\n        'defaultValue',\r\n        val ? [this.activeData.min, this.activeData.max] : this.activeData.min\r\n      )\r\n    },\r\n    rateTextChange(val) {\r\n      if (val) this.activeData['show-score'] = false\r\n    },\r\n    rateScoreChange(val) {\r\n      if (val) this.activeData['show-text'] = false\r\n    },\r\n    colorFormatChange(val) {\r\n      this.activeData.__config__.defaultValue = null\r\n      this.activeData['show-alpha'] = val.indexOf('a') > -1\r\n      this.activeData.__config__.renderKey = +new Date() // 更新renderKey,重新渲染该组件\r\n    },\r\n    openIconsDialog(model) {\r\n      this.iconsVisible = true\r\n      this.currentIconModel = model\r\n    },\r\n    setIcon(val) {\r\n      this.activeData[this.currentIconModel] = val\r\n    },\r\n    tagChange(tagIcon) {\r\n      let target = inputComponents.find(item => item.__config__.tagIcon === tagIcon)\r\n      if (!target) target = selectComponents.find(item => item.__config__.tagIcon === tagIcon)\r\n      this.$emit('tag-change', target)\r\n    },\r\n    changeRenderKey() {\r\n      this.activeData.__config__.renderKey = +new Date()\r\n    }\r\n  }\r\n}\r\n", null]}