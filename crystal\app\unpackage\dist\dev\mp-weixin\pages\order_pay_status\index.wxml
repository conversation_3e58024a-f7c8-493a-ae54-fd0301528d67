<view><view class="payment-status"><block wx:if="{{order_pay_info.paid||order_pay_info.payType=='offline'}}"><view class="iconfont icons icon-duihao2 bg-color"></view></block><block wx:else><view class="iconfont icons icon-iconfontguanbi"></view></block><block wx:if="{{order_pay_info.payType!='offline'}}"><view class="status">{{order_pay_info.paid?'订单支付成功':'订单支付失败'}}</view></block><block wx:else><view class="status">订单创建成功</view></block><view class="wrapper"><view class="item acea-row row-between-wrapper"><view>订单编号</view><view class="itemCom">{{order_pay_info.orderId}}</view></view><view class="item acea-row row-between-wrapper"><view>下单时间</view><view class="itemCom">{{order_pay_info.createTime?order_pay_info.createTime:'-'}}</view></view><view class="item acea-row row-between-wrapper"><view>支付方式</view><block wx:if="{{order_pay_info.payType=='weixin'}}"><view class="itemCom">微信支付</view></block><block wx:else><block wx:if="{{order_pay_info.payType=='yue'}}"><view class="itemCom">余额支付</view></block><block wx:else><block wx:if="{{order_pay_info.payType=='offline'}}"><view class="itemCom">线下支付</view></block><block wx:else><block wx:if="{{order_pay_info.payType=='alipay'}}"><view class="itemCom">支付宝支付</view></block></block></block></block></view><view class="item acea-row row-between-wrapper"><view>支付金额</view><view class="itemCom">{{order_pay_info.payPrice}}</view></view><block wx:if="{{order_pay_info.paid==0&&order_pay_info.payType!='offline'}}"><view class="item acea-row row-between-wrapper"><view>失败原因</view><view class="itemCom">{{status==2?'取消支付':msg}}</view></view></block></view><view data-event-opts="{{[['tap',[['goOrderDetails',['$event']]]]]}}" bindtap="__e"><button class="returnBnt bg-color" formType="submit" hover-class="none">查看订单</button></view><block wx:if="{{order_pay_info.pinkId&&order_pay_info.paid!=0&&status!=2&&status!=1}}"><button class="returnBnt cart-color" formType="submit" hover-class="none" data-event-opts="{{[['tap',[['goPink',['$0'],['order_pay_info.pinkId']]]]]}}" bindtap="__e">邀请好友参团</button></block><block wx:else><button class="returnBnt cart-color" formType="submit" hover-class="none" data-event-opts="{{[['tap',[['goIndex',['$event']]]]]}}" bindtap="__e">返回首页</button></block></view></view>