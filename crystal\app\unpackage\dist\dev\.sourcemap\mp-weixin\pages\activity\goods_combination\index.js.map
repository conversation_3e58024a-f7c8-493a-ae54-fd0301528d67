{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/goods_combination/index.vue?31db", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/goods_combination/index.vue?95ab", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/goods_combination/index.vue?8cef", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/goods_combination/index.vue?ae19", "uni-app:///pages/activity/goods_combination/index.vue", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/goods_combination/index.vue?498f", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/goods_combination/index.vue?806e", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/goods_combination/index.vue?935a", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/goods_combination/index.vue?5fe2"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "home", "data", "indicatorDots", "circular", "autoplay", "interval", "duration", "navH", "combinationList", "limit", "page", "loading", "loadend", "returnShow", "loadTitle", "avatarList", "bannerList", "totalPeople", "onShow", "onLoad", "uni", "title", "methods", "goBack", "openSubcribe", "url", "getCombinationHeader", "getCombinationList", "that", "onReachBottom"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACc;AACwB;;;AAG1F;AACmM;AACnM,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAkwB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACyEtxB;AAIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA;AAAA,eACA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACA;IACAC;MACAC;IACA;IAEA;IAKA;IACA;EACA;EACAC;IACAC;MACAH;IACA;IACAI;MACA;MAOAJ;QACAC;MACA;MACA;QACAD;QACAA;UACAK;QACA;MACA;QACAL;MACA;IAEA;IACAM;MAAA;MACA;QACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACAC;MACA;QACAlB;QACAD;MACA;MACA;MACA;QACA;QACA;QACA;QACAmB;QACAA;QACAA;QACAA;QACAA;MACA;QACAA;QACAA;MACA;IACA;EACA;EACAC;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3LA;AAAA;AAAA;AAAA;AAA66C,CAAgB,4tCAAG,EAAC,C;;;;;;;;;;;ACAj8C;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAq8C,CAAgB,ovCAAG,EAAC,C;;;;;;;;;;;ACAz9C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/activity/goods_combination/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/activity/goods_combination/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=e6e4503c&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\nimport style1 from \"./index.vue?vue&type=style&index=1&id=e6e4503c&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"e6e4503c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/activity/goods_combination/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=e6e4503c&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.bannerList.length\n  var g1 = _vm.avatarList.length\n  var l0 =\n    g1 > 0\n      ? _vm.__map(_vm.avatarList, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m0 = index === 6 && Number(_vm.avatarList.length) > 3\n          return {\n            $orig: $orig,\n            m0: m0,\n          }\n        })\n      : null\n  var g2 = _vm.combinationList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        l0: l0,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<div>\r\n\t\t<view class=\"combinationBj\"></view>\r\n\t\t<div class=\"combinationList\">\r\n\t\t\t<view class='group-list'>\r\n\t\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t\t<view class='iconfont icon-xiangzuo' @tap='goBack' :style=\"'top:'+ (navH/2) +'rpx'\" v-if=\"returnShow\"></view>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t<!-- banner -->\r\n\t\t\t\t<view class=\"swiper\" v-if=\"bannerList.length\">\r\n\t\t\t\t\t<swiper indicator-dots=\"true\" :autoplay=\"true\" :circular=\"circular\" :interval=\"interval\"\r\n\t\t\t\t\t\t:duration=\"duration\" indicator-color=\"rgba(255,255,255,0.6)\" indicator-active-color=\"#fff\">\r\n\t\t\t\t\t\t<block v-for=\"(item,index) in bannerList\" :key=\"index\">\r\n\t\t\t\t\t\t\t<swiper-item>\r\n\t\t\t\t\t\t\t\t<navigator :url='item.value' class='slide-navigator acea-row row-between-wrapper'\r\n\t\t\t\t\t\t\t\t\thover-class='none'>\r\n\t\t\t\t\t\t\t\t\t<image :src=\"item.value\" class=\"slide-image\" lazy-load></image>\r\n\t\t\t\t\t\t\t\t</navigator>\r\n\t\t\t\t\t\t\t</swiper-item>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</swiper>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"nav acea-row row-between-wrapper\">\r\n\t\t\t\t\t<image src=\"../static/zuo.png\"></image>\r\n\t\t\t\t\t<view class=\"title acea-row row-center\">\r\n\t\t\t\t\t\t<view class=\"spike-bd\">\r\n\t\t\t\t\t\t\t<view v-if=\"avatarList.length > 0\" class=\"activity_pic\">\r\n\t\t\t\t\t\t\t\t<view v-for=\"(item,index) in avatarList\" :key=\"index\" class=\"picture\"\r\n\t\t\t\t\t\t\t\t\t:style='index===6?\"position: relative\":\"position: static\"'>\r\n\t\t\t\t\t\t\t\t\t<span class=\"avatar\" :style='\"background-image: url(\"+item+\")\"'></span>\r\n\t\t\t\t\t\t\t\t\t<span v-if=\"index===6 && Number(avatarList.length) > 3\" class=\"mengceng\">\r\n\t\t\t\t\t\t\t\t\t\t<i>···</i>\r\n\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<text class=\"pic_count\">{{totalPeople}}人参与</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<image src=\"../static/you.png\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='list'>\r\n\t\t\t\t\t<block v-for=\"(item,index) in combinationList\" :key='index'>\r\n\t\t\t\t\t\t<view class='item acea-row row-between-wrapper' @tap=\"openSubcribe(item)\"\r\n\t\t\t\t\t\t data-url=''>\r\n\t\t\t\t\t\t\t<view class='pictrue'>\r\n\t\t\t\t\t\t\t\t<image :src='item.image'></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class='text'>\r\n\t\t\t\t\t\t\t\t<view class='line2'>{{item.title}}</view>\r\n\t\t\t\t\t\t\t\t<text class='y-money'>￥{{item.otPrice}}</text>\r\n\t\t\t\t\t\t\t\t<view class='bottom acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t\t\t\t<view class='money'>￥<text class='num'>{{item.price}}</text></view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"btn acea-row\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"num\">{{item.people}}人团</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"goBye\">去拼团</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<!-- <view class=\"nothing\">已售罄</view> -->\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</block>\r\n\t\t\t\t\t<view class='loadingicon acea-row row-center-wrapper' v-if='combinationList.length > 0'>\r\n\t\t\t\t\t\t<text class='loading iconfont icon-jiazai' :hidden='loading==false'></text>{{loadTitle}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<home></home>\r\n\t\t</div>\r\n\t</div>\r\n\t\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tgetCombinationList,\r\n\t\tcombinationHeaderApi\r\n\t} from '@/api/activity.js';\r\n\timport {\r\n\t\topenPinkSubscribe\r\n\t} from '../../../utils/SubscribeMessage.js';\r\n\timport home from '@/components/home/<USER>'\r\n\tlet app = getApp();\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\thome\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tindicatorDots: false,\r\n\t\t\t\tcircular: true,\r\n\t\t\t\tautoplay: true,\r\n\t\t\t\tinterval: 3000,\r\n\t\t\t\tduration: 500,\r\n\t\t\t\tnavH: '',\r\n\t\t\t\tcombinationList: [],\r\n\t\t\t\tlimit: 10,\r\n\t\t\t\tpage: 1,\r\n\t\t\t\tloading: false,\r\n\t\t\t\tloadend: false,\r\n\t\t\t\treturnShow: true,\r\n\t\t\t\tloadTitle: '',\r\n\t\t\t\tavatarList: [],\r\n\t\t\t\tbannerList: [],\r\n\t\t\t\ttotalPeople: 0\r\n\t\t\t}\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tthis.getCombinationList();\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tvar pages = getCurrentPages();\r\n\t\t\tthis.returnShow = pages.length===1?false:true;\r\n\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\ttitle:\"拼团列表\"\r\n\t\t\t})\r\n\t\t\t// #ifdef MP\r\n\t\t\tthis.navH = app.globalData.navH;\r\n\t\t\t// #endif\r\n\t\t\t// #ifdef H5\r\n\t\t\tthis.navH = app.globalData.navHeight;\r\n\t\t\t// #endif\r\n\t\t\tthis.getCombinationList();\r\n\t\t\tthis.getCombinationHeader();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgoBack: function() {\r\n\t\t\t\tuni.navigateBack();\r\n\t\t\t},\r\n\t\t\topenSubcribe: function(item) {\r\n\t\t\t\tlet page = item;\r\n\t\t\t\t// #ifndef MP\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/activity/goods_combination_details/index?id=${item.id}`\r\n\t\t\t\t});\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef MP\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '正在加载',\r\n\t\t\t\t})\r\n\t\t\t\topenPinkSubscribe().then(res => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: `/pages/activity/goods_combination_details/index?id=${item.id}`\r\n\t\t\t\t\t});\r\n\t\t\t\t}).catch(() => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t});\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tgetCombinationHeader: function() {\r\n\t\t\t\tcombinationHeaderApi().then(res => {\r\n\t\t\t\t\tthis.avatarList = res.data.avatarList || [];\r\n\t\t\t\t\tthis.bannerList = res.data.bannerList || [];\r\n\t\t\t\t\tthis.totalPeople = res.data.totalPeople;\r\n\t\t\t\t}).catch(() => {\r\n\t\t\t\t\tthis.loading = false;\r\n\t\t\t\t\tthis.loadTitle = '加载更多';\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetCombinationList: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tif (that.loadend) return;\r\n\t\t\t\tif (that.loading) return;\r\n\t\t\t\tthat.loadTitle = '';\r\n\t\t\t\tvar data = {\r\n\t\t\t\t\tpage: that.page,\r\n\t\t\t\t\tlimit: that.limit\r\n\t\t\t\t};\r\n\t\t\t\tthis.loading = true\r\n\t\t\t\tgetCombinationList(data).then(function(res) {\r\n\t\t\t\t\tlet list = res.data.list;\r\n\t\t\t\t\tlet combinationList = that.$util.SplitArray(list, that.combinationList);\r\n\t\t\t\t\tlet loadend = list.length < that.limit;\r\n\t\t\t\t\tthat.loadend = loadend;\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tthat.loadTitle = loadend ? '已全部加载' : '加载更多';\r\n\t\t\t\t\tthat.$set(that, 'combinationList', combinationList);\r\n\t\t\t\t\tthat.$set(that, 'page', that.page + 1);\r\n\t\t\t\t}).catch(() => {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tthat.loadTitle = '加载更多';\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t},\r\n\t\tonReachBottom: function() {\r\n\t\t\tthis.getCombinationList();\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\tpage {\r\n\t\tbackground-color: #c9ab79 !important;\r\n\t}\r\n</style>\r\n<style lang=\"scss\" scoped>\r\n\t.combinationBj{\r\n\t\tposition: absolute;\r\n\t\twidth: 100%;\r\n\t\theight: 990rpx;\r\n\t\tbackground: url(../static/pinbei.png) no-repeat;\r\n\t\tbackground-size: 100% 100%;\r\n\t}\r\n\t.mengceng {\r\n\t\twidth: 40rpx;\r\n\t\theight: 40rpx;\r\n\t\tline-height: 36rpx;\r\n\t\tbackground: rgba(51, 51, 51, 0.6);\r\n\t\ttext-align: center;\r\n\t\tborder-radius: 50%;\r\n\t\topacity: 1;\r\n\t\tposition: absolute;\r\n\t\tleft: -2rpx;\r\n\t\tcolor: #FFF;\r\n\t\ttop: 2rpx;\r\n\t\ti{\r\n\t\t\tfont-style: normal;\r\n\t\t\tfont-size: 20rpx;\r\n\t\t}\r\n\t}\r\n\t.activity_pic {\r\n\t\r\n\t\t.picture {\r\n\t\t\tdisplay: inline-table;\r\n\t\t}\r\n\t\r\n\t\t.avatar {\r\n\t\t\twidth: 38rpx;\r\n\t\t\theight: 38rpx;\r\n\t\t\tdisplay: inline-table;\r\n\t\t\tvertical-align: middle;\r\n\t\t\t-webkit-user-select: none;\r\n\t\t\t-moz-user-select: none;\r\n\t\t\t-ms-user-select: none;\r\n\t\t\tuser-select: none;\r\n\t\t\tborder-radius: 50%;\r\n\t\t\tbackground-repeat: no-repeat;\r\n\t\t\tbackground-size: cover;\r\n\t\t\tbackground-position: 0 0;\r\n\t\t\tmargin-right: -10rpx;\r\n\t\t\tbox-shadow: 0 0 0 1px #fff;\r\n\t\t}\r\n\t}\r\n    .combinationList{\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tpadding: 25rpx 30rpx;\r\n\t\t.swiper{\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 300rpx;\r\n\t\t\tborder-radius: 14rpx;\r\n\t\t\tmargin-bottom: 34rpx;\r\n\t\t\tswiper,\r\n\t\t\t.swiper-item,\r\n\t\t\timage {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 300rpx;\r\n\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.nav{\r\n\t\t\twidth: 100%;\r\n\t\t\tmargin-bottom: 34rpx;\r\n\t\t\timage{\r\n\t\t\t\twidth: 102rpx;\r\n\t\t\t\theight: 4rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.title {\r\n\t\t\twidth: 68%;\r\n\t\t\t.pic_count {\r\n\t\t\t\tmargin-left: 30rpx;\r\n\t\t\t\tcolor: $theme-color;\r\n\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\twidth: auto;\r\n\t\t\t\theight: auto;\r\n\t\t\t\tbackground: #C6483C;\r\n\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\tborder-radius: 19rpx;\r\n\t\t\t\tpadding: 4rpx 14rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.icon-xiangzuo {\r\n\t\tfont-size: 40rpx;\r\n\t\tcolor: #fff;\r\n\t\tposition: fixed;\r\n\t\tleft: 30rpx;\r\n\t\tz-index: 99;\r\n\t\ttransform: translateY(-20%);\r\n\t}\r\n\r\n\t.group-list .list .item {\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 14rpx;\r\n\t\tpadding: 22rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tmargin: 0 auto 20rpx auto;\r\n\t}\r\n\r\n\t.group-list .list .item .pictrue {\r\n\t\twidth: 186rpx;\r\n\t\theight: 186rpx;\r\n\t}\r\n\r\n\t.group-list .list .item .pictrue image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 6rpx;\r\n\t}\r\n\r\n\t.group-list .list .item .text {\r\n\t\twidth: 440rpx;\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #333333;\r\n\t\tmargin-left: 20rpx;\r\n\t\t.line2{\r\n\t\t\theight: 86rpx;\r\n\t\t}\r\n\t\t.btn{\r\n\t\t\t.num{\r\n\t\t\t\twidth: auto;\r\n\t\t\t\theight: 58rpx;\r\n\t\t\t\tline-height: 58rpx;\r\n\t\t\t\tbackground-color:#FFEFDB;\r\n\t\t\t\tpadding-left: 22rpx;\r\n\t\t\t\tpadding-right: 36rpx;\r\n\t\t\t\tcolor: $theme-color;\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t-webkit-border-top-left-radius: 15px;\r\n\t\t\t\t-webkit-border-bottom-left-radius: 15px;\r\n\t\t\t}\r\n\t\t\t.goBye{\r\n\t\t\t\twidth: 130rpx;\r\n\t\t\t\theight: 58rpx;\r\n\t\t\t\tz-index: 11;\r\n\t\t\t\tbackground: url(../static/shandian.png) no-repeat;\r\n\t\t\t\tbackground-size: 100% 100%;\r\n\t\t\t\tline-height: 58rpx;\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tpadding-left: 36rpx;\r\n\t\t\t\tpadding-right: 10rpx;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tmargin-left: -28rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t    .nothing{\r\n\t\t\twidth: 148rpx;\r\n\t\t\theight: 58rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tline-height: 58rpx;\r\n\t\t\tbackground: #CCCCCC;\r\n\t\t\topacity: 1;\r\n\t\t\tcolor: #FFFFFF;\r\n\t\t\tborder-radius: 30rpx;\r\n\t\t\tfont-size: 24rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.group-list .list .item .text .team {\r\n\t\theight: 38rpx;\r\n\t\tborder-radius: 4rpx;\r\n\t\tfont-size: 22rpx;\r\n\t\tmargin-top: 20rpx;\r\n\t}\r\n\r\n\t.group-list .list .item .text .team .iconfont {\r\n\t\twidth: 54rpx;\r\n\t\tbackground-color: #ffdcd9;\r\n\t\ttext-align: center;\r\n\t\tcolor: #dd3823;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.group-list .list .item .text .team .num {\r\n\t\ttext-align: center;\r\n\t\tpadding: 0 6rpx;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.group-list .list .item .text .bottom .money {\r\n\t\tfont-size: 24rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: $theme-color;\r\n\t}\r\n\r\n\t.group-list .list .item .text .bottom .money .num {\r\n\t\tfont-size: 32rpx;\r\n\t}\r\n\r\n\t.group-list .list .item .text .y-money {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999;\r\n\t\tfont-weight: normal;\r\n\t\ttext-decoration: line-through;\r\n\t}\r\n\r\n\t.group-list .list .item .text .bottom .groupBnt {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #fff;\r\n\t\twidth: 146rpx;\r\n\t\theight: 54rpx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 54rpx;\r\n\t\tborder-radius: 4rpx;\r\n\t}\r\n\r\n\t.group-list .list .item .text .bottom .groupBnt .iconfont {\r\n\t\tfont-size: 25rpx;\r\n\t\tvertical-align: 2rpx;\r\n\t\tmargin-left: 10rpx;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363903084\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=1&id=e6e4503c&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=1&id=e6e4503c&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363903386\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}