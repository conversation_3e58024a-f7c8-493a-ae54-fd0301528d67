(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/users/wechat_login/index"],{"22f1":function(t,e,n){"use strict";n.r(e);var o=n("3df1"),i=n("30ed");for(var a in i)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(a);n("62e9");var c=n("828b"),u=Object(c["a"])(i["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);e["default"]=u.exports},"30ed":function(t,e,n){"use strict";n.r(e);var o=n("8dd8"),i=n.n(o);for(var a in o)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(a);e["default"]=i.a},"3df1":function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return i})),n.d(e,"a",(function(){}));var o=function(){var t=this.$createElement;this._self._c},i=[]},"62e9":function(t,e,n){"use strict";var o=n("a70b"),i=n.n(o);i.a},"8dd8":function(t,e,n){"use strict";(function(t){var o=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;n("8f59");var i=n("292f"),a=(n("4bbc"),n("5904")),c=o(n("5902")),u=(o(n("9852")),getApp()),s=t.getSystemInfoSync().statusBarHeight+"px",r={data:function(){return{isUp:!1,phone:"",statusBarHeight:s,isHome:!1,isPhoneBox:!1,logoUrl:"",code:"",authKey:"",options:"",userInfo:{},codeNum:0}},components:{mobileLogin:function(){Promise.all([n.e("common/vendor"),n.e("components/login_mobile/index")]).then(function(){return resolve(n("8819"))}.bind(null,n)).catch(n.oe)},routinePhone:function(){n.e("components/login_mobile/routine_phone").then(function(){return resolve(n("8b66"))}.bind(null,n)).catch(n.oe)}},onLoad:function(t){var e=this;(0,i.getLogo)().then((function(t){e.logoUrl=t.data.logoUrl}));getCurrentPages()},methods:{back:function(){t.navigateBack()},home:function(){t.switchTab({url:"/pages/index/index"})},maskClose:function(){this.isUp=!1},bindPhoneClose:function(t){t.isStatus?(this.isPhoneBox=!1,this.$util.Tips({title:"登录成功",icon:"success"},{tab:3})):this.isPhoneBox=!1},getphonenumber:function(e){var n=this;t.showLoading({title:"正在登录中"}),c.default.getCode().then((function(t){n.getUserPhoneNumber(e.detail.encryptedData,e.detail.iv,t)})).catch((function(e){t.$emit("closePage",!1),t.hideLoading()}))},getUserPhoneNumber:function(e,n,o){var a=this;(0,i.getUserPhone)({encryptedData:e,iv:n,code:o,type:"routine",key:this.authKey}).then((function(t){a.$store.commit("LOGIN",{token:t.data.token}),a.$store.commit("SETUID",t.data.uid),a.getUserInfo(),a.$util.Tips({title:"登录成功",icon:"success"},{tab:3})})).catch((function(e){t.hideLoading(),that.$util.Tips({title:e})}))},getUserInfo:function(){var e=this;(0,a.getUserInfo)().then((function(n){t.hideLoading(),e.userInfo=n.data,e.$store.commit("UPDATE_USERINFO",n.data),e.$util.Tips({title:"登录成功",icon:"success"},{tab:3})}))},getUserProfile:function(){var e=this;t.showLoading({title:"正在登录中"}),c.default.getUserProfile().then((function(n){c.default.getCode().then((function(t){e.getWxUser(t,n)})).catch((function(e){t.hideLoading()}))})).catch((function(e){t.hideLoading()}))},getWxUser:function(e,n){var o=this,i=n.userInfo;i.code=e,i.spread_spid=u.globalData.spid,i.spread_code=u.globalData.code,i.avatar=i.userInfo.avatarUrl,i.city=i.userInfo.city,i.country=i.userInfo.country,i.nickName=i.userInfo.nickName,i.province=i.userInfo.province,i.sex=i.userInfo.gender,i.type="routine",c.default.authUserInfo(i.code,i).then((function(e){o.authKey=e.data.key,"register"===e.data.type&&(t.hideLoading(),o.isPhoneBox=!0),"login"===e.data.type&&(t.hideLoading(),o.$store.commit("LOGIN",{token:e.data.token}),o.$store.commit("SETUID",e.data.uid),o.getUserInfo(),o.$util.Tips({title:e,icon:"success"},{tab:3}))})).catch((function(e){t.hideLoading(),t.showToast({title:e,icon:"none",duration:2e3})}))}}};e.default=r}).call(this,n("df3c")["default"])},9816:function(t,e,n){"use strict";(function(t,e){var o=n("47a9");n("5c2d");o(n("3240"));var i=o(n("22f1"));t.__webpack_require_UNI_MP_PLUGIN__=n,e(i.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},a70b:function(t,e,n){}},[["9816","common/runtime","common/vendor"]]]);