{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\userprocess-add-and-update.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\userprocess-add-and-update.vue", "mtime": 1753666157945}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\"use strict\";\n\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _userprocess = require(\"@/api/userprocess\");\nvar systemConfigApi = _interopRequireWildcard(require(\"@/api/systemConfig.js\"));\nvar _index = _interopRequireDefault(require(\"@/components/Tinymce/index\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != _typeof(e) && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  data: function data() {\n    return {\n      visible: false,\n      userStatus: [],\n      dataForm: {\n        id: 0,\n        userId: '',\n        doTime: '',\n        name: '',\n        userStatus: '',\n        remarks: ''\n      },\n      dataRule: {\n        doTime: [{\n          required: true,\n          message: '记录时间不能为空',\n          trigger: 'blur'\n        }],\n        userStatus: [{\n          required: true,\n          message: '记录状态不能为空',\n          trigger: 'blur'\n        }]\n      }\n    };\n  },\n  components: {\n    Tinymce: _index.default\n  },\n  methods: {\n    init: function init(id, userId) {\n      var _this = this;\n      this.dataForm.id = id || 0;\n      this.visible = true;\n      this.$nextTick(function () {\n        _this.$refs['dataForm'].resetFields();\n        _this.dataForm.remarks = '';\n        if (_this.dataForm.id) {\n          (0, _userprocess.userprocessDetailApi)(_this.dataForm.id).then(function (data) {\n            _this.dataForm.name = data.name;\n            _this.dataForm.type = data.type;\n            _this.dataForm.userStatus = data.userStatus;\n            _this.dataForm.userId = data.userId;\n            _this.dataForm.remarks = data.remarks;\n          }).catch(function (res) {\n            _this.$message.error(res.message);\n          });\n        } else {\n          _this.dataForm.userId = userId;\n        }\n        _this.getUserStatus();\n      });\n    },\n    getUserStatus: function getUserStatus() {\n      var _this2 = this;\n      systemConfigApi.configGetUniq({\n        key: \"userstatus\"\n      }).then(function (data) {\n        _this2.userStatus = data ? data.split(',') : [];\n      });\n    },\n    // 表单提交\n    dataFormSubmit: function dataFormSubmit() {\n      var _this3 = this;\n      this.$refs['dataForm'].validate(function (valid) {\n        if (valid) {\n          if (!_this3.dataForm.id) {\n            (0, _userprocess.userprocessCreateApi)(_this3.dataForm).then(function () {\n              _this3.$message({\n                message: '操作成功',\n                type: 'success',\n                duration: 1500,\n                onClose: function onClose() {\n                  _this3.visible = false;\n                  _this3.$emit('refreshDataList');\n                }\n              });\n            }).catch(function (res) {\n              _this3.$message.error(res.message);\n            });\n          } else {\n            (0, _userprocess.userprocessUpdateApi)(_this3.dataForm).then(function () {\n              _this3.$message({\n                message: '操作成功',\n                type: 'success',\n                duration: 1500,\n                onClose: function onClose() {\n                  _this3.visible = false;\n                  _this3.$emit('refreshDataList');\n                }\n              });\n            }).catch(function (res) {\n              _this3.$message.error(res.message);\n            });\n          }\n        }\n      });\n    }\n  }\n};", null]}