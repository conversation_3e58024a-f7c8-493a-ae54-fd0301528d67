{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\creatStore\\index.vue?vue&type=style&index=0&id=4ffe5f63&scoped=true&lang=scss", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\creatStore\\index.vue", "mtime": 1753666157922}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\css-loader\\index.js", "mtime": 1753666298053}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753666301105}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753666299466}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1753666297707}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n  .disLabel{\n    ::v-deep.el-form-item__label{\n      margin-left: 36px !important;\n    }\n  }\n  .disLabelmoren{\n    ::v-deep.el-form-item__label{\n      margin-left: 120px !important;\n    }\n  }\n  .priamry_border{\n    border: 1px solid #1890FF;\n    color: #1890FF;\n  }\n  .color-item{\n    height: 30px;\n    line-height: 30px;\n    padding: 0 10px;\n    color:#fff;\n    margin-right :10px;\n  }\n  .color-list .color-item.blue{\n    background-color: #1E9FFF;\n  }\n  .color-list .color-item.yellow{\n    background-color: rgb(254, 185, 0);\n  }\n  .color-list .color-item.green{\n    background-color: #009688;\n  }\n  .color-list .color-item.red{\n    background-color: #ed4014;\n  }\n  .proCoupon{\n    ::v-deep.el-form-item__content{\n      margin-top: 5px;\n    }\n  }\n  .tabPic{\n    width: 40px !important;\n    height: 40px !important;\n    img{\n      width: 100%;\n      height: 100%;\n    }\n  }\n  .noLeft{\n    ::v-deep.el-form-item__content{\n      margin-left: 0 !important;\n    }\n  }\n  .tabNumWidth{\n    ::v-deep.el-input-number--medium{\n      width: 121px !important;\n    }\n    ::v-deep.el-input-number__increase{\n      width: 20px !important;\n      font-size: 12px !important;\n    }\n    ::v-deep.el-input-number__decrease{\n      width: 20px !important;\n      font-size: 12px !important;\n    }\n    ::v-deep.el-input-number--medium .el-input__inner {\n      padding-left: 25px !important;\n      padding-right: 25px !important;\n    }\n    ::v-deep thead{\n      line-height: normal !important;\n    }\n    ::v-deep .el-table .cell{\n      line-height: normal !important;\n    }\n  }\n  .selWidth{\n    width: 100%;\n  }\n  .selWidthd{\n    width: 300px;\n  }\n  .button-new-tag {\n    height: 28px;\n    line-height: 26px;\n    padding-top: 0;\n    padding-bottom: 0;\n  }\n  .input-new-tag {\n    width: 90px;\n    margin-left: 10px;\n    vertical-align: bottom;\n  }\n  .pictrue{\n    width: 60px;\n    height: 60px;\n    border: 1px dotted rgba(0,0,0,0.1);\n    margin-right: 10px;\n    position: relative;\n    cursor: pointer;\n    img{\n      width: 100%;\n      height: 100%;\n    }\n    video{\n      width: 100%;\n      height: 100%;\n    }\n  }\n  .btndel{\n    position: absolute;\n    z-index: 1;\n    width :20px !important;\n    height: 20px !important;\n    left: 46px;\n    top: -4px;\n  }\n  .labeltop{\n    ::v-deep.el-form-item__label{\n      float: none !important;\n      display: inline-block !important;\n      width: auto !important;\n    }\n  }\n  .iview-video-style {\n  width: 300px;\n  height: 180px;\n  border-radius: 10px;\n  background-color: #707070;\n  margin: 0 120px 20px;\n  position: relative;\n  overflow: hidden;\n}\n\n.iview-video-style .iconv {\n  color: #fff;\n  line-height: 180px;\n  width: 50px;\n  height: 50px;\n  display: inherit;\n  font-size: 26px;\n  position: absolute;\n  top: -74px;\n  left: 50%;\n  margin-left: -25px;\n}\n\n.iview-video-style .mark {\n  position: absolute;\n  width: 100%;\n  height: 30px;\n  top: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  text-align: center;\n}\n", null]}