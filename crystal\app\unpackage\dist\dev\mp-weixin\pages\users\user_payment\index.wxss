@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  width: 100%;
  height: 100%;
  background-color: #fff;
}
.payment {
  position: relative;
  top: -60rpx;
  width: 100%;
  background-color: #fff;
  border-radius: 10rpx;
  padding-top: 25rpx;
  border-top-right-radius: 14rpx;
  border-top-left-radius: 14rpx;
}
.payment .nav {
  height: 75rpx;
  line-height: 75rpx;
  padding: 0 100rpx;
}
.payment .nav .item {
  font-size: 30rpx;
  color: #333;
}
.payment .nav .item.on {
  font-weight: bold;
  border-bottom: 4rpx solid #e83323;
}
.payment .input {
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px dashed #dddddd;
  margin: 60rpx auto 0 auto;
  padding-bottom: 20rpx;
  font-size: 56rpx;
  color: #333333;
  flex-wrap: nowrap;
}
.payment .input text {
  padding-left: 106rpx;
}
.payment .input input {
  padding-right: 106rpx;
  width: 300rpx;
  height: 94rpx;
  text-align: center;
  font-size: 70rpx;
}
.payment .placeholder {
  color: #d0d0d0;
  height: 100%;
  line-height: 94rpx;
}
.payment .tip {
  font-size: 26rpx;
  color: #888888;
  padding: 0 30rpx;
  margin-top: 25rpx;
}
.payment .but {
  color: #fff;
  font-size: 30rpx;
  width: 700rpx;
  height: 86rpx;
  border-radius: 43rpx;
  margin: 50rpx auto 0 auto;
  background: linear-gradient(90deg, #FF7931 0%, #F11B09 100%);
  line-height: 86rpx;
}
.payment-top {
  width: 100%;
  height: 350rpx;
  background-color: #e83323;
}
.payment-top .name {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-top: -38rpx;
  margin-bottom: 30rpx;
}
.payment-top .pic {
  font-size: 32rpx;
  color: #fff;
}
.payment-top .pic-font {
  font-size: 78rpx;
  color: #fff;
}
.picList {
  display: flex;
  flex-wrap: wrap;
  margin: 30rpx 0;
}
.picList .pic-box {
  width: 32%;
  height: auto;
  border-radius: 20rpx;
  margin-top: 21rpx;
  padding: 20rpx 0;
  margin-right: 12rpx;
}
.picList .pic-box:nth-child(3n) {
  margin-right: 0;
}
.picList .pic-box-color {
  background-color: #f4f4f4;
  color: #656565;
}
.picList .pic-number {
  font-size: 22rpx;
}
.picList .pic-number-pic {
  font-size: 38rpx;
  margin-right: 10rpx;
  text-align: center;
}
.pic-box-color-active {
  background-color: #ec3323 !important;
  color: #fff !important;
}
.tips-box .tips {
  font-size: 28rpx;
  color: #333333;
  font-weight: 800;
  margin-bottom: 14rpx;
  margin-top: 20rpx;
}
.tips-box .tips-samll {
  font-size: 24rpx;
  color: #333333;
  margin-bottom: 14rpx;
}
.tips-box .tip-box {
  margin-top: 30rpx;
}
.tips-title {
  margin-top: 20rpx;
  font-size: 24rpx;
  color: #333;
}

