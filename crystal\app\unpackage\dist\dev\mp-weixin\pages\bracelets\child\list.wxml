<view class="productSort data-v-0b75b564"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="top data-v-0b75b564"><view style="height:100rpx;display:flex;align-items:center;justify-content:space-between;padding:0 20rpx;" class="_div data-v-0b75b564"><view class="data-v-0b75b564"><text class="data-v-0b75b564">{{"名称："+item.$orig.name}}</text></view><view style="display:flex;gap:10rpx;" class="data-v-0b75b564"><view data-event-opts="{{[['tap',[['turnDetail',['$0'],[[['productList','',index,'id']]]]]]]}}" class="botton_1 data-v-0b75b564" bindtap="__e">编辑</view><button class="botton_2 data-v-0b75b564" open-type="share" hover-class="none" data-id="{{item.$orig.id}}"><view class="data-v-0b75b564">分享手串</view></button></view></view><view style="{{('display: flex;justify-content: center;align-items: center;height: '+item.$orig.radius*3+'rpx;border-top: #c9ab79 1px dashed ;position: relative;')}}" class="_div data-v-0b75b564"><view class="circle-container _div data-v-0b75b564" style="{{'width:'+(item.$orig.radius*2+'rpx')+';'+('height:'+(item.$orig.radius*2+'rpx')+';')}}"><block wx:for="{{item.l0}}" wx:for-item="item1" wx:for-index="index1" wx:key="index1"><view class="marble _div data-v-0b75b564" style="{{item1.s0}}"></view></block></view></view></view></block></view>