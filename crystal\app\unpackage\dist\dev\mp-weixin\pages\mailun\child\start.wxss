@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.mailun-test-container.data-v-1b07f0e9 {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 40rpx;
}
.radio-select.data-v-1b07f0e9 {
  display: flex;
  font-weight: 500;
  font-size: 26rpx;
  align-items: center;
  justify-content: space-between;
  height: 100rpx;
  padding: 0 30rpx;
  margin-bottom: 10rpx;
}
.radio-group.data-v-1b07f0e9 {
  display: flex;
  justify-content: space-between;
  flex: 1;
  padding: 0 20rpx;
}
.radio-label.data-v-1b07f0e9 {
  margin: 0 8rpx;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.radio-item.data-v-1b07f0e9 {
  opacity: 0;
  position: absolute;
  z-index: 1;
  width: 100%;
  height: 100%;
}
.radio-indicator.data-v-1b07f0e9 {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  border: 2rpx solid #c9ab79;
  background-color: white;
  position: relative;
  transition: all 0.2s;
}
.radio-selected.data-v-1b07f0e9 {
  background-color: white;
  border-color: #c9ab79;
  box-shadow: 0 0 6rpx rgba(201, 171, 121, 0.5);
}
.radio-selected.data-v-1b07f0e9:after {
  content: '';
  position: absolute;
  width: 20rpx;
  height: 10rpx;
  border-left: 4rpx solid #c9ab79;
  border-bottom: 4rpx solid #c9ab79;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -70%) rotate(-45deg);
          transform: translate(-50%, -70%) rotate(-45deg);
}
.option-label.data-v-1b07f0e9 {
  min-width: 100rpx;
  text-align: center;
  font-size: 28rpx;
  padding: 0 10rpx;
}
.left-option.data-v-1b07f0e9 {
  text-align: left;
}
.right-option.data-v-1b07f0e9 {
  text-align: right;
}
.title.data-v-1b07f0e9 {
  padding: 30rpx 30rpx;
  font-weight: 600;
  font-size: 32rpx;
  letter-spacing: 1rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}
.nav-title.data-v-1b07f0e9 {
  padding: 20rpx 40rpx;
  display: flex;
  align-items: center;
  height: 80rpx;
  line-height: 80rpx;
  background: #f6f6f6;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.nav-title .color.data-v-1b07f0e9 {
  width: 10rpx;
  height: 40rpx;
  background: #c9ab79;
  border-radius: 6rpx;
  margin-right: 15rpx;
}
.nav-title .text.data-v-1b07f0e9 {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}
.botton_1.data-v-1b07f0e9 {
  width: 45%;
  background-color: #c9ab79;
  color: #fff;
  font-size: 32rpx;
  font-weight: 600;
  height: 90rpx;
  border-radius: 50rpx;
  text-align: center;
  line-height: 90rpx;
  box-shadow: 0 4rpx 12rpx rgba(201, 171, 121, 0.3);
  transition: all 0.2s;
}
.botton_2.data-v-1b07f0e9 {
  width: 45%;
  background-color: #DD5C5F;
  color: #fff;
  font-size: 32rpx;
  font-weight: 600;
  height: 90rpx;
  border-radius: 50rpx;
  text-align: center;
  line-height: 90rpx;
  box-shadow: 0 4rpx 12rpx rgba(221, 92, 95, 0.3);
  transition: all 0.2s;
}
.button-hover.data-v-1b07f0e9 {
  -webkit-transform: translateY(3rpx);
          transform: translateY(3rpx);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.2);
}
.box.data-v-1b07f0e9 {
  width: 92%;
  margin-left: 4%;
  background: white;
  margin-bottom: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}
.bottom.data-v-1b07f0e9 {
  display: flex;
  padding: 40rpx 20rpx;
  justify-content: space-around;
}
.box-error.data-v-1b07f0e9 {
  border: 2rpx solid #ff6b6b;
  -webkit-animation: shake-data-v-1b07f0e9 0.5s ease-in-out;
          animation: shake-data-v-1b07f0e9 0.5s ease-in-out;
}
@-webkit-keyframes shake-data-v-1b07f0e9 {
0%, 100% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
}
20%, 60% {
    -webkit-transform: translateX(-5rpx);
            transform: translateX(-5rpx);
}
40%, 80% {
    -webkit-transform: translateX(5rpx);
            transform: translateX(5rpx);
}
}
@keyframes shake-data-v-1b07f0e9 {
0%, 100% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
}
20%, 60% {
    -webkit-transform: translateX(-5rpx);
            transform: translateX(-5rpx);
}
40%, 80% {
    -webkit-transform: translateX(5rpx);
            transform: translateX(5rpx);
}
}
.error-tip.data-v-1b07f0e9 {
  color: #ff6b6b;
  font-size: 24rpx;
  padding: 0 30rpx 20rpx;
  text-align: center;
}

