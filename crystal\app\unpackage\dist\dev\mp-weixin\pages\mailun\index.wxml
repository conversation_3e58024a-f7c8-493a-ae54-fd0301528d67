<view style="background-color:#fff;" class="data-v-aa744fe8"><view style="text-align:center;" class="data-v-aa744fe8"><image style="width:710rpx;margin:30rpx 0;" src="{{mail<PERSON><PERSON><PERSON>}}" mode="widthFix" lazy-load="false" class="data-v-aa744fe8"></image><view class="maitext data-v-aa744fe8">做脉轮测试之前先找个不受外界干扰的地方</view><view class="maitext data-v-aa744fe8" style="margin-bottom:30rpx;">让自己放松，放下刚才任何情绪</view><view data-event-opts="{{[['tap',[['start',['$event']]]]]}}" class="botton_1 data-v-aa744fe8" bindtap="__e">开始我的脉轮测试</view></view><liu-rotating-menu vue-id="a414ab0c-1" btnObj="{{btnObj}}" data-event-opts="{{[['^click',[['click']]]]}}" bind:click="__e" class="data-v-aa744fe8" bind:__l="__l"></liu-rotating-menu></view>