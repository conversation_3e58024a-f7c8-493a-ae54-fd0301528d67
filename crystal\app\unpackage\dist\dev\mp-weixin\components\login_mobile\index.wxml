<block wx:if="{{isUp}}"><view class="data-v-********"><block wx:if="{{isShow}}"><view data-event-opts="{{[['tap',[['close',['$event']]]]]}}" class="mobile-bg data-v-********" bindtap="__e"></view></block><view class="{{['mobile-mask','animated','data-v-********',(isUp)?'slideInUp':'']}}" style="{{'position:'+(isPos?'fixed':'static')+';'}}"><view class="input-item data-v-********"><input type="text" placeholder="输入手机号" data-event-opts="{{[['input',[['__set_model',['','account','$event',[]]]]]]}}" value="{{account}}" bindinput="__e" class="data-v-********"/></view><view class="input-item data-v-********"><input type="text" placeholder="输入验证码" data-event-opts="{{[['input',[['__set_model',['','codeNum','$event',[]]]]]]}}" value="{{codeNum}}" bindinput="__e" class="data-v-********"/><button class="code data-v-********" disabled="{{disabled}}" data-event-opts="{{[['tap',[['code',['$event']]]]]}}" bindtap="__e">{{text}}</button></view><view data-event-opts="{{[['tap',[['loginBtn',['$event']]]]]}}" class="sub_btn data-v-********" bindtap="__e">{{!userInfo.phone&&isLogin||userInfo.phone&&isLogin?'立即绑定':'立即登录'}}</view></view></view></block>