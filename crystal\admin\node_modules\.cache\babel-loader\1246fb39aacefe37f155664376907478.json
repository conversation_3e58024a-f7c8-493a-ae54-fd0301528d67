{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\group\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\group\\index.vue", "mtime": 1753666157940}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _user = require(\"@/api/user\");\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  name: 'UserGroup',\n  data: function data() {\n    return {\n      tableFrom: {\n        page: 1,\n        limit: 20\n      },\n      tableData: {\n        data: [],\n        total: 0\n      },\n      listLoading: true\n    };\n  },\n  mounted: function mounted() {\n    this.getList();\n  },\n  methods: {\n    info: function info() {},\n    onAdd: function onAdd(row) {\n      var _this = this;\n      this.$prompt(this.$route.path.indexOf('group') !== -1 ? '分组名称' : '标签名称', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        inputErrorMessage: this.$route.path.indexOf('group') !== -1 ? '请输入分组名称' : '请输入标签名称',\n        inputType: 'text',\n        closeOnClickModal: false,\n        inputValue: row ? this.$route.path.indexOf('group') !== -1 ? row.groupName : row.name : '',\n        inputPlaceholder: this.$route.path.indexOf('group') !== -1 ? '请输入分组名称' : '请输入标签名称',\n        inputValidator: function inputValidator(value) {\n          if (!value) return '输入不能为空';\n        }\n      }).then(function (_ref) {\n        var value = _ref.value;\n        if (_this.$route.path.indexOf('group') !== -1) {\n          row ? (0, _user.groupUpdateApi)({\n            id: row.id\n          }, {\n            groupName: value\n          }).then(function () {\n            _this.$message.success('编辑成功');\n            _this.getList();\n          }) : (0, _user.groupSaveApi)({\n            groupName: value\n          }).then(function () {\n            _this.$message.success('新增成功');\n            _this.getList();\n          });\n        } else {\n          row ? (0, _user.tagUpdateApi)({\n            id: row.id\n          }, {\n            name: value\n          }).then(function () {\n            _this.$message.success('编辑成功');\n            _this.getList();\n          }) : (0, _user.tagSaveApi)({\n            name: value\n          }).then(function () {\n            _this.$message.success('新增成功');\n            _this.getList();\n          });\n        }\n      }).catch(function () {\n        _this.$message.info('取消输入');\n      });\n    },\n    // 列表\n    getList: function getList() {\n      var _this2 = this;\n      this.listLoading = true;\n      this.$route.path.indexOf('group') !== -1 ? (0, _user.groupListApi)(this.tableFrom).then(function (res) {\n        _this2.tableData.data = res.list;\n        _this2.tableData.total = res.total;\n        _this2.listLoading = false;\n      }).catch(function (res) {\n        _this2.listLoading = false;\n      }) : (0, _user.tagListApi)(this.tableFrom).then(function (res) {\n        _this2.tableData.data = res.list;\n        _this2.tableData.total = res.total;\n        _this2.listLoading = false;\n      }).catch(function (res) {\n        _this2.listLoading = false;\n      });\n    },\n    pageChange: function pageChange(page) {\n      this.tableFrom.page = page;\n      this.getList();\n    },\n    handleSizeChange: function handleSizeChange(val) {\n      this.tableFrom.limit = val;\n      this.getList();\n    },\n    // 删除\n    handleDelete: function handleDelete(id, idx) {\n      var _this3 = this;\n      this.$modalSure('删除吗？所有用户已经关联的数据都会清除').then(function () {\n        _this3.$route.path.indexOf('group') !== -1 ? (0, _user.groupDeleteApi)({\n          id: id\n        }).then(function () {\n          _this3.$message.success('删除成功');\n          _this3.tableData.data.splice(idx, 1);\n        }) : (0, _user.tagDeleteApi)({\n          id: id\n        }).then(function () {\n          _this3.$message.success('删除成功');\n          _this3.tableData.data.splice(idx, 1);\n        });\n      });\n    }\n  }\n};", null]}