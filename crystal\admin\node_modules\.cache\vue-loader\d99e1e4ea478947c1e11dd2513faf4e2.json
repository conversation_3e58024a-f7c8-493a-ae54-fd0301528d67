{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\FormGenerator\\index\\RightPanel.vue?vue&type=template&id=17795e15&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\FormGenerator\\index\\RightPanel.vue", "mtime": 1753666157771}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"right-board\" },\n    [\n      _c(\n        \"el-tabs\",\n        {\n          staticClass: \"center-tabs\",\n          model: {\n            value: _vm.currentTab,\n            callback: function ($$v) {\n              _vm.currentTab = $$v\n            },\n            expression: \"currentTab\",\n          },\n        },\n        [\n          _c(\"el-tab-pane\", { attrs: { label: \"组件属性\", name: \"field\" } }),\n          _vm._v(\" \"),\n          _c(\"el-tab-pane\", { attrs: { label: \"表单属性\", name: \"form\" } }),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"div\",\n        { staticClass: \"field-box\" },\n        [\n          _c(\n            \"el-scrollbar\",\n            { staticClass: \"right-scrollbar\" },\n            [\n              _c(\n                \"el-form\",\n                {\n                  directives: [\n                    {\n                      name: \"show\",\n                      rawName: \"v-show\",\n                      value: _vm.currentTab === \"field\" && _vm.showField,\n                      expression: \"currentTab==='field' && showField\",\n                    },\n                  ],\n                  attrs: { size: \"small\", \"label-width\": \"90px\" },\n                },\n                [\n                  _vm.activeData.__config__.changeTag\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"组件类型\" } },\n                        [\n                          _c(\n                            \"el-select\",\n                            {\n                              style: { width: \"100%\" },\n                              attrs: { placeholder: \"请选择组件类型\" },\n                              on: { change: _vm.tagChange },\n                              model: {\n                                value: _vm.activeData.__config__.tagIcon,\n                                callback: function ($$v) {\n                                  _vm.$set(\n                                    _vm.activeData.__config__,\n                                    \"tagIcon\",\n                                    $$v\n                                  )\n                                },\n                                expression: \"activeData.__config__.tagIcon\",\n                              },\n                            },\n                            _vm._l(_vm.tagList, function (group) {\n                              return _c(\n                                \"el-option-group\",\n                                {\n                                  key: group.label,\n                                  attrs: { label: group.label },\n                                },\n                                _vm._l(group.options, function (item) {\n                                  return _c(\n                                    \"el-option\",\n                                    {\n                                      key: item.__config__.label,\n                                      attrs: {\n                                        label: item.__config__.label,\n                                        value: item.__config__.tagIcon,\n                                      },\n                                    },\n                                    [\n                                      _c(\"svg-icon\", {\n                                        staticClass: \"node-icon\",\n                                        attrs: {\n                                          \"icon-class\": item.__config__.tagIcon,\n                                        },\n                                      }),\n                                      _vm._v(\" \"),\n                                      _c(\"span\", [\n                                        _vm._v(\n                                          \" \" + _vm._s(item.__config__.label)\n                                        ),\n                                      ]),\n                                    ],\n                                    1\n                                  )\n                                }),\n                                1\n                              )\n                            }),\n                            1\n                          ),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.__vModel__ !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"字段名\" } },\n                        [\n                          _c(\"el-input\", {\n                            attrs: { placeholder: \"请输入字段名（v-model）\" },\n                            model: {\n                              value: _vm.activeData.__vModel__,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.activeData, \"__vModel__\", $$v)\n                              },\n                              expression: \"activeData.__vModel__\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.__config__.componentName !== undefined\n                    ? _c(\"el-form-item\", { attrs: { label: \"组件名\" } }, [\n                        _vm._v(\n                          \"\\n          \" +\n                            _vm._s(_vm.activeData.__config__.componentName) +\n                            \"\\n        \"\n                        ),\n                      ])\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.__config__.label !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"标题\" } },\n                        [\n                          _c(\"el-input\", {\n                            attrs: { placeholder: \"请输入标题\" },\n                            model: {\n                              value: _vm.activeData.__config__.label,\n                              callback: function ($$v) {\n                                _vm.$set(\n                                  _vm.activeData.__config__,\n                                  \"label\",\n                                  $$v\n                                )\n                              },\n                              expression: \"activeData.__config__.label\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.placeholder !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"占位提示\" } },\n                        [\n                          _c(\"el-input\", {\n                            attrs: { placeholder: \"请输入占位提示\" },\n                            model: {\n                              value: _vm.activeData.placeholder,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.activeData, \"placeholder\", $$v)\n                              },\n                              expression: \"activeData.placeholder\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData[\"start-placeholder\"] !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"开始占位\" } },\n                        [\n                          _c(\"el-input\", {\n                            attrs: { placeholder: \"请输入占位提示\" },\n                            model: {\n                              value: _vm.activeData[\"start-placeholder\"],\n                              callback: function ($$v) {\n                                _vm.$set(\n                                  _vm.activeData,\n                                  \"start-placeholder\",\n                                  $$v\n                                )\n                              },\n                              expression: \"activeData['start-placeholder']\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData[\"end-placeholder\"] !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"结束占位\" } },\n                        [\n                          _c(\"el-input\", {\n                            attrs: { placeholder: \"请输入占位提示\" },\n                            model: {\n                              value: _vm.activeData[\"end-placeholder\"],\n                              callback: function ($$v) {\n                                _vm.$set(_vm.activeData, \"end-placeholder\", $$v)\n                              },\n                              expression: \"activeData['end-placeholder']\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.__config__.span !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"表单栅格\" } },\n                        [\n                          _c(\"el-slider\", {\n                            attrs: { max: 24, min: 1, marks: { 12: \"\" } },\n                            on: { change: _vm.spanChange },\n                            model: {\n                              value: _vm.activeData.__config__.span,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.activeData.__config__, \"span\", $$v)\n                              },\n                              expression: \"activeData.__config__.span\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.__config__.layout === \"rowFormItem\"\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"栅格间隔\" } },\n                        [\n                          _c(\"el-input-number\", {\n                            attrs: { min: 0, placeholder: \"栅格间隔\" },\n                            model: {\n                              value: _vm.activeData.gutter,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.activeData, \"gutter\", $$v)\n                              },\n                              expression: \"activeData.gutter\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.__config__.layout === \"rowFormItem\"\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"布局模式\" } },\n                        [\n                          _c(\n                            \"el-radio-group\",\n                            {\n                              model: {\n                                value: _vm.activeData.type,\n                                callback: function ($$v) {\n                                  _vm.$set(_vm.activeData, \"type\", $$v)\n                                },\n                                expression: \"activeData.type\",\n                              },\n                            },\n                            [\n                              _c(\"el-radio-button\", {\n                                attrs: { label: \"default\" },\n                              }),\n                              _vm._v(\" \"),\n                              _c(\"el-radio-button\", {\n                                attrs: { label: \"flex\" },\n                              }),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.justify !== undefined &&\n                  _vm.activeData.type === \"flex\"\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"水平排列\" } },\n                        [\n                          _c(\n                            \"el-select\",\n                            {\n                              style: { width: \"100%\" },\n                              attrs: { placeholder: \"请选择水平排列\" },\n                              model: {\n                                value: _vm.activeData.justify,\n                                callback: function ($$v) {\n                                  _vm.$set(_vm.activeData, \"justify\", $$v)\n                                },\n                                expression: \"activeData.justify\",\n                              },\n                            },\n                            _vm._l(_vm.justifyOptions, function (item, index) {\n                              return _c(\"el-option\", {\n                                key: index,\n                                attrs: { label: item.label, value: item.value },\n                              })\n                            }),\n                            1\n                          ),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.align !== undefined &&\n                  _vm.activeData.type === \"flex\"\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"垂直排列\" } },\n                        [\n                          _c(\n                            \"el-radio-group\",\n                            {\n                              model: {\n                                value: _vm.activeData.align,\n                                callback: function ($$v) {\n                                  _vm.$set(_vm.activeData, \"align\", $$v)\n                                },\n                                expression: \"activeData.align\",\n                              },\n                            },\n                            [\n                              _c(\"el-radio-button\", {\n                                attrs: { label: \"top\" },\n                              }),\n                              _vm._v(\" \"),\n                              _c(\"el-radio-button\", {\n                                attrs: { label: \"middle\" },\n                              }),\n                              _vm._v(\" \"),\n                              _c(\"el-radio-button\", {\n                                attrs: { label: \"bottom\" },\n                              }),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.__config__.labelWidth !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"标签宽度\" } },\n                        [\n                          _c(\"el-input\", {\n                            attrs: {\n                              type: \"number\",\n                              placeholder: \"请输入标签宽度\",\n                            },\n                            model: {\n                              value: _vm.activeData.__config__.labelWidth,\n                              callback: function ($$v) {\n                                _vm.$set(\n                                  _vm.activeData.__config__,\n                                  \"labelWidth\",\n                                  _vm._n($$v)\n                                )\n                              },\n                              expression: \"activeData.__config__.labelWidth\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.style &&\n                  _vm.activeData.style.width !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"组件宽度\" } },\n                        [\n                          _c(\"el-input\", {\n                            attrs: {\n                              placeholder: \"请输入组件宽度\",\n                              clearable: \"\",\n                            },\n                            model: {\n                              value: _vm.activeData.style.width,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.activeData.style, \"width\", $$v)\n                              },\n                              expression: \"activeData.style.width\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.__vModel__ !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"默认值\" } },\n                        [\n                          _c(\"el-input\", {\n                            attrs: {\n                              value: _vm.setDefaultValue(\n                                _vm.activeData.__config__.defaultValue\n                              ),\n                              placeholder: \"请输入默认值\",\n                            },\n                            on: { input: _vm.onDefaultValueInput },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.__config__.tag === \"el-checkbox-group\"\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"至少应选\" } },\n                        [\n                          _c(\"el-input-number\", {\n                            attrs: {\n                              value: _vm.activeData.min,\n                              min: 0,\n                              placeholder: \"至少应选\",\n                            },\n                            on: {\n                              input: function ($event) {\n                                return _vm.$set(\n                                  _vm.activeData,\n                                  \"min\",\n                                  $event ? $event : undefined\n                                )\n                              },\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.__config__.tag === \"el-checkbox-group\"\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"最多可选\" } },\n                        [\n                          _c(\"el-input-number\", {\n                            attrs: {\n                              value: _vm.activeData.max,\n                              min: 0,\n                              placeholder: \"最多可选\",\n                            },\n                            on: {\n                              input: function ($event) {\n                                return _vm.$set(\n                                  _vm.activeData,\n                                  \"max\",\n                                  $event ? $event : undefined\n                                )\n                              },\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.__slot__ &&\n                  _vm.activeData.__slot__.prepend !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"前缀\" } },\n                        [\n                          _c(\"el-input\", {\n                            attrs: { placeholder: \"请输入前缀\" },\n                            model: {\n                              value: _vm.activeData.__slot__.prepend,\n                              callback: function ($$v) {\n                                _vm.$set(\n                                  _vm.activeData.__slot__,\n                                  \"prepend\",\n                                  $$v\n                                )\n                              },\n                              expression: \"activeData.__slot__.prepend\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.__slot__ &&\n                  _vm.activeData.__slot__.append !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"后缀\" } },\n                        [\n                          _c(\"el-input\", {\n                            attrs: { placeholder: \"请输入后缀\" },\n                            model: {\n                              value: _vm.activeData.__slot__.append,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.activeData.__slot__, \"append\", $$v)\n                              },\n                              expression: \"activeData.__slot__.append\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData[\"prefix-icon\"] !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"前图标\" } },\n                        [\n                          _c(\n                            \"el-input\",\n                            {\n                              attrs: { placeholder: \"请输入前图标名称\" },\n                              model: {\n                                value: _vm.activeData[\"prefix-icon\"],\n                                callback: function ($$v) {\n                                  _vm.$set(_vm.activeData, \"prefix-icon\", $$v)\n                                },\n                                expression: \"activeData['prefix-icon']\",\n                              },\n                            },\n                            [\n                              _c(\n                                \"el-button\",\n                                {\n                                  attrs: {\n                                    slot: \"append\",\n                                    icon: \"el-icon-thumb\",\n                                  },\n                                  on: {\n                                    click: function ($event) {\n                                      return _vm.openIconsDialog(\"prefix-icon\")\n                                    },\n                                  },\n                                  slot: \"append\",\n                                },\n                                [_vm._v(\"\\n              选择\\n            \")]\n                              ),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData[\"suffix-icon\"] !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"后图标\" } },\n                        [\n                          _c(\n                            \"el-input\",\n                            {\n                              attrs: { placeholder: \"请输入后图标名称\" },\n                              model: {\n                                value: _vm.activeData[\"suffix-icon\"],\n                                callback: function ($$v) {\n                                  _vm.$set(_vm.activeData, \"suffix-icon\", $$v)\n                                },\n                                expression: \"activeData['suffix-icon']\",\n                              },\n                            },\n                            [\n                              _c(\n                                \"el-button\",\n                                {\n                                  attrs: {\n                                    slot: \"append\",\n                                    icon: \"el-icon-thumb\",\n                                  },\n                                  on: {\n                                    click: function ($event) {\n                                      return _vm.openIconsDialog(\"suffix-icon\")\n                                    },\n                                  },\n                                  slot: \"append\",\n                                },\n                                [_vm._v(\"\\n              选择\\n            \")]\n                              ),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData[\"icon\"] !== undefined &&\n                  _vm.activeData.__config__.tag === \"el-button\"\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"按钮图标\" } },\n                        [\n                          _c(\n                            \"el-input\",\n                            {\n                              attrs: { placeholder: \"请输入按钮图标名称\" },\n                              model: {\n                                value: _vm.activeData[\"icon\"],\n                                callback: function ($$v) {\n                                  _vm.$set(_vm.activeData, \"icon\", $$v)\n                                },\n                                expression: \"activeData['icon']\",\n                              },\n                            },\n                            [\n                              _c(\n                                \"el-button\",\n                                {\n                                  attrs: {\n                                    slot: \"append\",\n                                    icon: \"el-icon-thumb\",\n                                  },\n                                  on: {\n                                    click: function ($event) {\n                                      return _vm.openIconsDialog(\"icon\")\n                                    },\n                                  },\n                                  slot: \"append\",\n                                },\n                                [_vm._v(\"\\n              选择\\n            \")]\n                              ),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.__config__.tag === \"el-cascader\"\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"选项分隔符\" } },\n                        [\n                          _c(\"el-input\", {\n                            attrs: { placeholder: \"请输入选项分隔符\" },\n                            model: {\n                              value: _vm.activeData.separator,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.activeData, \"separator\", $$v)\n                              },\n                              expression: \"activeData.separator\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.autosize !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"最小行数\" } },\n                        [\n                          _c(\"el-input-number\", {\n                            attrs: { min: 1, placeholder: \"最小行数\" },\n                            model: {\n                              value: _vm.activeData.autosize.minRows,\n                              callback: function ($$v) {\n                                _vm.$set(\n                                  _vm.activeData.autosize,\n                                  \"minRows\",\n                                  $$v\n                                )\n                              },\n                              expression: \"activeData.autosize.minRows\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.autosize !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"最大行数\" } },\n                        [\n                          _c(\"el-input-number\", {\n                            attrs: { min: 1, placeholder: \"最大行数\" },\n                            model: {\n                              value: _vm.activeData.autosize.maxRows,\n                              callback: function ($$v) {\n                                _vm.$set(\n                                  _vm.activeData.autosize,\n                                  \"maxRows\",\n                                  $$v\n                                )\n                              },\n                              expression: \"activeData.autosize.maxRows\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.isShowMin\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"最小值\" } },\n                        [\n                          _c(\"el-input-number\", {\n                            attrs: { placeholder: \"最小值\" },\n                            model: {\n                              value: _vm.activeData.min,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.activeData, \"min\", $$v)\n                              },\n                              expression: \"activeData.min\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.isShowMax\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"最大值\" } },\n                        [\n                          _c(\"el-input-number\", {\n                            attrs: { placeholder: \"最大值\" },\n                            model: {\n                              value: _vm.activeData.max,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.activeData, \"max\", $$v)\n                              },\n                              expression: \"activeData.max\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.height !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"组件高度\" } },\n                        [\n                          _c(\"el-input-number\", {\n                            attrs: { placeholder: \"高度\" },\n                            on: { input: _vm.changeRenderKey },\n                            model: {\n                              value: _vm.activeData.height,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.activeData, \"height\", $$v)\n                              },\n                              expression: \"activeData.height\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.isShowStep\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"步长\" } },\n                        [\n                          _c(\"el-input-number\", {\n                            attrs: { placeholder: \"步数\" },\n                            model: {\n                              value: _vm.activeData.step,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.activeData, \"step\", $$v)\n                              },\n                              expression: \"activeData.step\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.__config__.tag === \"el-input-number\"\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"精度\" } },\n                        [\n                          _c(\"el-input-number\", {\n                            attrs: { min: 0, placeholder: \"精度\" },\n                            model: {\n                              value: _vm.activeData.precision,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.activeData, \"precision\", $$v)\n                              },\n                              expression: \"activeData.precision\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.__config__.tag === \"el-input-number\"\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"按钮位置\" } },\n                        [\n                          _c(\n                            \"el-radio-group\",\n                            {\n                              model: {\n                                value: _vm.activeData[\"controls-position\"],\n                                callback: function ($$v) {\n                                  _vm.$set(\n                                    _vm.activeData,\n                                    \"controls-position\",\n                                    $$v\n                                  )\n                                },\n                                expression: \"activeData['controls-position']\",\n                              },\n                            },\n                            [\n                              _c(\"el-radio-button\", { attrs: { label: \"\" } }, [\n                                _vm._v(\"\\n              默认\\n            \"),\n                              ]),\n                              _vm._v(\" \"),\n                              _c(\n                                \"el-radio-button\",\n                                { attrs: { label: \"right\" } },\n                                [_vm._v(\"\\n              右侧\\n            \")]\n                              ),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.maxlength !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"最多输入\" } },\n                        [\n                          _c(\n                            \"el-input\",\n                            {\n                              attrs: { placeholder: \"请输入字符长度\" },\n                              model: {\n                                value: _vm.activeData.maxlength,\n                                callback: function ($$v) {\n                                  _vm.$set(_vm.activeData, \"maxlength\", $$v)\n                                },\n                                expression: \"activeData.maxlength\",\n                              },\n                            },\n                            [\n                              _c(\"template\", { slot: \"append\" }, [\n                                _vm._v(\"\\n              个字符\\n            \"),\n                              ]),\n                            ],\n                            2\n                          ),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData[\"active-text\"] !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"开启提示\" } },\n                        [\n                          _c(\"el-input\", {\n                            attrs: { placeholder: \"请输入开启提示\" },\n                            model: {\n                              value: _vm.activeData[\"active-text\"],\n                              callback: function ($$v) {\n                                _vm.$set(_vm.activeData, \"active-text\", $$v)\n                              },\n                              expression: \"activeData['active-text']\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData[\"inactive-text\"] !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"关闭提示\" } },\n                        [\n                          _c(\"el-input\", {\n                            attrs: { placeholder: \"请输入关闭提示\" },\n                            model: {\n                              value: _vm.activeData[\"inactive-text\"],\n                              callback: function ($$v) {\n                                _vm.$set(_vm.activeData, \"inactive-text\", $$v)\n                              },\n                              expression: \"activeData['inactive-text']\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData[\"active-value\"] !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"开启值\" } },\n                        [\n                          _c(\"el-input\", {\n                            attrs: {\n                              value: _vm.setDefaultValue(\n                                _vm.activeData[\"active-value\"]\n                              ),\n                              placeholder: \"请输入开启值\",\n                            },\n                            on: {\n                              input: function ($event) {\n                                return _vm.onSwitchValueInput(\n                                  $event,\n                                  \"active-value\"\n                                )\n                              },\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData[\"inactive-value\"] !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"关闭值\" } },\n                        [\n                          _c(\"el-input\", {\n                            attrs: {\n                              value: _vm.setDefaultValue(\n                                _vm.activeData[\"inactive-value\"]\n                              ),\n                              placeholder: \"请输入关闭值\",\n                            },\n                            on: {\n                              input: function ($event) {\n                                return _vm.onSwitchValueInput(\n                                  $event,\n                                  \"inactive-value\"\n                                )\n                              },\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.type !== undefined &&\n                  \"el-date-picker\" === _vm.activeData.__config__.tag\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"时间类型\" } },\n                        [\n                          _c(\n                            \"el-select\",\n                            {\n                              style: { width: \"100%\" },\n                              attrs: { placeholder: \"请选择时间类型\" },\n                              on: { change: _vm.dateTypeChange },\n                              model: {\n                                value: _vm.activeData.type,\n                                callback: function ($$v) {\n                                  _vm.$set(_vm.activeData, \"type\", $$v)\n                                },\n                                expression: \"activeData.type\",\n                              },\n                            },\n                            _vm._l(_vm.dateOptions, function (item, index) {\n                              return _c(\"el-option\", {\n                                key: index,\n                                attrs: { label: item.label, value: item.value },\n                              })\n                            }),\n                            1\n                          ),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.name !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"文件字段名\" } },\n                        [\n                          _c(\"el-input\", {\n                            attrs: { placeholder: \"请输入上传文件字段名\" },\n                            model: {\n                              value: _vm.activeData.name,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.activeData, \"name\", $$v)\n                              },\n                              expression: \"activeData.name\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.accept === \"image\"\n                    ? _c(\"el-form-item\", { attrs: { label: \"文件类型\" } }, [\n                        _c(\"span\", [_vm._v(\"图片\")]),\n                      ])\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.accept !== undefined &&\n                  _vm.activeData.accept !== \"image\"\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"文件类型\" } },\n                        [\n                          _c(\n                            \"el-select\",\n                            {\n                              style: { width: \"100%\" },\n                              attrs: {\n                                placeholder: \"请选择文件类型\",\n                                clearable: \"\",\n                              },\n                              model: {\n                                value: _vm.activeData.accept,\n                                callback: function ($$v) {\n                                  _vm.$set(_vm.activeData, \"accept\", $$v)\n                                },\n                                expression: \"activeData.accept\",\n                              },\n                            },\n                            [\n                              _c(\"el-option\", {\n                                attrs: { label: \"视频\", value: \"video/*\" },\n                              }),\n                              _vm._v(\" \"),\n                              _c(\"el-option\", {\n                                attrs: { label: \"音频\", value: \"audio/*\" },\n                              }),\n                              _vm._v(\" \"),\n                              _c(\"el-option\", {\n                                attrs: { label: \"excel\", value: \".xls,.xlsx\" },\n                              }),\n                              _vm._v(\" \"),\n                              _c(\"el-option\", {\n                                attrs: { label: \"word\", value: \".doc,.docx\" },\n                              }),\n                              _vm._v(\" \"),\n                              _c(\"el-option\", {\n                                attrs: { label: \"pdf\", value: \".pdf\" },\n                              }),\n                              _vm._v(\" \"),\n                              _c(\"el-option\", {\n                                attrs: { label: \"txt\", value: \".txt\" },\n                              }),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.__config__.fileSize !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"文件大小\" } },\n                        [\n                          _c(\n                            \"el-input\",\n                            {\n                              attrs: { placeholder: \"请输入文件大小\" },\n                              model: {\n                                value: _vm.activeData.__config__.fileSize,\n                                callback: function ($$v) {\n                                  _vm.$set(\n                                    _vm.activeData.__config__,\n                                    \"fileSize\",\n                                    _vm._n($$v)\n                                  )\n                                },\n                                expression: \"activeData.__config__.fileSize\",\n                              },\n                            },\n                            [\n                              _c(\n                                \"el-select\",\n                                {\n                                  style: { width: \"66px\" },\n                                  attrs: { slot: \"append\" },\n                                  slot: \"append\",\n                                  model: {\n                                    value: _vm.activeData.__config__.sizeUnit,\n                                    callback: function ($$v) {\n                                      _vm.$set(\n                                        _vm.activeData.__config__,\n                                        \"sizeUnit\",\n                                        $$v\n                                      )\n                                    },\n                                    expression:\n                                      \"activeData.__config__.sizeUnit\",\n                                  },\n                                },\n                                [\n                                  _c(\"el-option\", {\n                                    attrs: { label: \"KB\", value: \"KB\" },\n                                  }),\n                                  _vm._v(\" \"),\n                                  _c(\"el-option\", {\n                                    attrs: { label: \"MB\", value: \"MB\" },\n                                  }),\n                                  _vm._v(\" \"),\n                                  _c(\"el-option\", {\n                                    attrs: { label: \"GB\", value: \"GB\" },\n                                  }),\n                                ],\n                                1\n                              ),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.action !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"上传地址\" } },\n                        [\n                          _c(\"el-input\", {\n                            attrs: {\n                              placeholder: \"请输入上传地址\",\n                              clearable: \"\",\n                            },\n                            model: {\n                              value: _vm.activeData.action,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.activeData, \"action\", $$v)\n                              },\n                              expression: \"activeData.action\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData[\"list-type\"] !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"列表类型\" } },\n                        [\n                          _c(\n                            \"el-radio-group\",\n                            {\n                              attrs: { size: \"small\" },\n                              model: {\n                                value: _vm.activeData[\"list-type\"],\n                                callback: function ($$v) {\n                                  _vm.$set(_vm.activeData, \"list-type\", $$v)\n                                },\n                                expression: \"activeData['list-type']\",\n                              },\n                            },\n                            [\n                              _c(\n                                \"el-radio-button\",\n                                { attrs: { label: \"text\" } },\n                                [_vm._v(\"\\n              text\\n            \")]\n                              ),\n                              _vm._v(\" \"),\n                              _c(\n                                \"el-radio-button\",\n                                { attrs: { label: \"picture\" } },\n                                [\n                                  _vm._v(\n                                    \"\\n              picture\\n            \"\n                                  ),\n                                ]\n                              ),\n                              _vm._v(\" \"),\n                              _c(\n                                \"el-radio-button\",\n                                { attrs: { label: \"picture-card\" } },\n                                [\n                                  _vm._v(\n                                    \"\\n              picture-card\\n            \"\n                                  ),\n                                ]\n                              ),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.type !== undefined &&\n                  _vm.activeData.__config__.tag === \"el-button\"\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"按钮类型\" } },\n                        [\n                          _c(\n                            \"el-select\",\n                            {\n                              style: { width: \"100%\" },\n                              model: {\n                                value: _vm.activeData.type,\n                                callback: function ($$v) {\n                                  _vm.$set(_vm.activeData, \"type\", $$v)\n                                },\n                                expression: \"activeData.type\",\n                              },\n                            },\n                            [\n                              _c(\"el-option\", {\n                                attrs: { label: \"primary\", value: \"primary\" },\n                              }),\n                              _vm._v(\" \"),\n                              _c(\"el-option\", {\n                                attrs: { label: \"success\", value: \"success\" },\n                              }),\n                              _vm._v(\" \"),\n                              _c(\"el-option\", {\n                                attrs: { label: \"warning\", value: \"warning\" },\n                              }),\n                              _vm._v(\" \"),\n                              _c(\"el-option\", {\n                                attrs: { label: \"danger\", value: \"danger\" },\n                              }),\n                              _vm._v(\" \"),\n                              _c(\"el-option\", {\n                                attrs: { label: \"info\", value: \"info\" },\n                              }),\n                              _vm._v(\" \"),\n                              _c(\"el-option\", {\n                                attrs: { label: \"text\", value: \"text\" },\n                              }),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.__config__.buttonText !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        {\n                          directives: [\n                            {\n                              name: \"show\",\n                              rawName: \"v-show\",\n                              value:\n                                \"picture-card\" !== _vm.activeData[\"list-type\"],\n                              expression:\n                                \"'picture-card' !== activeData['list-type']\",\n                            },\n                          ],\n                          attrs: { label: \"按钮文字\" },\n                        },\n                        [\n                          _c(\"el-input\", {\n                            attrs: { placeholder: \"请输入按钮文字\" },\n                            model: {\n                              value: _vm.activeData.__config__.buttonText,\n                              callback: function ($$v) {\n                                _vm.$set(\n                                  _vm.activeData.__config__,\n                                  \"buttonText\",\n                                  $$v\n                                )\n                              },\n                              expression: \"activeData.__config__.buttonText\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData[\"range-separator\"] !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"分隔符\" } },\n                        [\n                          _c(\"el-input\", {\n                            attrs: { placeholder: \"请输入分隔符\" },\n                            model: {\n                              value: _vm.activeData[\"range-separator\"],\n                              callback: function ($$v) {\n                                _vm.$set(_vm.activeData, \"range-separator\", $$v)\n                              },\n                              expression: \"activeData['range-separator']\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData[\"picker-options\"] !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"时间段\" } },\n                        [\n                          _c(\"el-input\", {\n                            attrs: { placeholder: \"请输入时间段\" },\n                            model: {\n                              value:\n                                _vm.activeData[\"picker-options\"]\n                                  .selectableRange,\n                              callback: function ($$v) {\n                                _vm.$set(\n                                  _vm.activeData[\"picker-options\"],\n                                  \"selectableRange\",\n                                  $$v\n                                )\n                              },\n                              expression:\n                                \"activeData['picker-options'].selectableRange\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.format !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"时间格式\" } },\n                        [\n                          _c(\"el-input\", {\n                            attrs: {\n                              value: _vm.activeData.format,\n                              placeholder: \"请输入时间格式\",\n                            },\n                            on: {\n                              input: function ($event) {\n                                return _vm.setTimeValue($event)\n                              },\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  [\"el-checkbox-group\", \"el-radio-group\", \"el-select\"].indexOf(\n                    _vm.activeData.__config__.tag\n                  ) > -1\n                    ? [\n                        _c(\"el-divider\", [_vm._v(\"选项\")]),\n                        _vm._v(\" \"),\n                        _c(\n                          \"draggable\",\n                          {\n                            attrs: {\n                              list: _vm.activeData.__slot__.options,\n                              animation: 340,\n                              group: \"selectItem\",\n                              handle: \".option-drag\",\n                            },\n                          },\n                          _vm._l(\n                            _vm.activeData.__slot__.options,\n                            function (item, index) {\n                              return _c(\n                                \"div\",\n                                { key: index, staticClass: \"select-item\" },\n                                [\n                                  _c(\n                                    \"div\",\n                                    {\n                                      staticClass:\n                                        \"select-line-icon option-drag\",\n                                    },\n                                    [\n                                      _c(\"i\", {\n                                        staticClass: \"el-icon-s-operation\",\n                                      }),\n                                    ]\n                                  ),\n                                  _vm._v(\" \"),\n                                  _c(\"el-input\", {\n                                    attrs: {\n                                      placeholder: \"选项名\",\n                                      size: \"small\",\n                                    },\n                                    model: {\n                                      value: item.label,\n                                      callback: function ($$v) {\n                                        _vm.$set(item, \"label\", $$v)\n                                      },\n                                      expression: \"item.label\",\n                                    },\n                                  }),\n                                  _vm._v(\" \"),\n                                  _c(\"el-input\", {\n                                    attrs: {\n                                      placeholder: \"选项值\",\n                                      size: \"small\",\n                                      value: item.value,\n                                    },\n                                    on: {\n                                      input: function ($event) {\n                                        return _vm.setOptionValue(item, $event)\n                                      },\n                                    },\n                                  }),\n                                  _vm._v(\" \"),\n                                  _c(\n                                    \"div\",\n                                    {\n                                      staticClass: \"close-btn select-line-icon\",\n                                      on: {\n                                        click: function ($event) {\n                                          return _vm.activeData.__slot__.options.splice(\n                                            index,\n                                            1\n                                          )\n                                        },\n                                      },\n                                    },\n                                    [\n                                      _c(\"i\", {\n                                        staticClass: \"el-icon-remove-outline\",\n                                      }),\n                                    ]\n                                  ),\n                                ],\n                                1\n                              )\n                            }\n                          ),\n                          0\n                        ),\n                        _vm._v(\" \"),\n                        _c(\n                          \"div\",\n                          { staticStyle: { \"margin-left\": \"20px\" } },\n                          [\n                            _c(\n                              \"el-button\",\n                              {\n                                staticStyle: { \"padding-bottom\": \"0\" },\n                                attrs: {\n                                  icon: \"el-icon-circle-plus-outline\",\n                                  type: \"text\",\n                                },\n                                on: { click: _vm.addSelectItem },\n                              },\n                              [_vm._v(\"\\n              添加选项\\n            \")]\n                            ),\n                          ],\n                          1\n                        ),\n                        _vm._v(\" \"),\n                        _c(\"el-divider\"),\n                      ]\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  [\"el-cascader\"].indexOf(_vm.activeData.__config__.tag) > -1\n                    ? [\n                        _c(\"el-divider\", [_vm._v(\"选项\")]),\n                        _vm._v(\" \"),\n                        _c(\n                          \"el-form-item\",\n                          { attrs: { label: \"数据类型\" } },\n                          [\n                            _c(\n                              \"el-radio-group\",\n                              {\n                                attrs: { size: \"small\" },\n                                model: {\n                                  value: _vm.activeData.__config__.dataType,\n                                  callback: function ($$v) {\n                                    _vm.$set(\n                                      _vm.activeData.__config__,\n                                      \"dataType\",\n                                      $$v\n                                    )\n                                  },\n                                  expression: \"activeData.__config__.dataType\",\n                                },\n                              },\n                              [\n                                _c(\n                                  \"el-radio-button\",\n                                  { attrs: { label: \"dynamic\" } },\n                                  [\n                                    _vm._v(\n                                      \"\\n                动态数据\\n              \"\n                                    ),\n                                  ]\n                                ),\n                                _vm._v(\" \"),\n                                _c(\n                                  \"el-radio-button\",\n                                  { attrs: { label: \"static\" } },\n                                  [\n                                    _vm._v(\n                                      \"\\n                静态数据\\n              \"\n                                    ),\n                                  ]\n                                ),\n                              ],\n                              1\n                            ),\n                          ],\n                          1\n                        ),\n                        _vm._v(\" \"),\n                        _vm.activeData.__config__.dataType === \"dynamic\"\n                          ? [\n                              _c(\n                                \"el-form-item\",\n                                { attrs: { label: \"标签键名\" } },\n                                [\n                                  _c(\"el-input\", {\n                                    attrs: { placeholder: \"请输入标签键名\" },\n                                    model: {\n                                      value: _vm.activeData.props.props.label,\n                                      callback: function ($$v) {\n                                        _vm.$set(\n                                          _vm.activeData.props.props,\n                                          \"label\",\n                                          $$v\n                                        )\n                                      },\n                                      expression:\n                                        \"activeData.props.props.label\",\n                                    },\n                                  }),\n                                ],\n                                1\n                              ),\n                              _vm._v(\" \"),\n                              _c(\n                                \"el-form-item\",\n                                { attrs: { label: \"值键名\" } },\n                                [\n                                  _c(\"el-input\", {\n                                    attrs: { placeholder: \"请输入值键名\" },\n                                    model: {\n                                      value: _vm.activeData.props.props.value,\n                                      callback: function ($$v) {\n                                        _vm.$set(\n                                          _vm.activeData.props.props,\n                                          \"value\",\n                                          $$v\n                                        )\n                                      },\n                                      expression:\n                                        \"activeData.props.props.value\",\n                                    },\n                                  }),\n                                ],\n                                1\n                              ),\n                              _vm._v(\" \"),\n                              _c(\n                                \"el-form-item\",\n                                { attrs: { label: \"子级键名\" } },\n                                [\n                                  _c(\"el-input\", {\n                                    attrs: { placeholder: \"请输入子级键名\" },\n                                    model: {\n                                      value:\n                                        _vm.activeData.props.props.children,\n                                      callback: function ($$v) {\n                                        _vm.$set(\n                                          _vm.activeData.props.props,\n                                          \"children\",\n                                          $$v\n                                        )\n                                      },\n                                      expression:\n                                        \"activeData.props.props.children\",\n                                    },\n                                  }),\n                                ],\n                                1\n                              ),\n                            ]\n                          : _vm._e(),\n                        _vm._v(\" \"),\n                        _vm.activeData.__config__.dataType === \"static\"\n                          ? _c(\"el-tree\", {\n                              attrs: {\n                                draggable: \"\",\n                                data: _vm.activeData.options,\n                                \"node-key\": \"id\",\n                                \"expand-on-click-node\": false,\n                                \"render-content\": _vm.renderContent,\n                              },\n                            })\n                          : _vm._e(),\n                        _vm._v(\" \"),\n                        _vm.activeData.__config__.dataType === \"static\"\n                          ? _c(\n                              \"div\",\n                              { staticStyle: { \"margin-left\": \"20px\" } },\n                              [\n                                _c(\n                                  \"el-button\",\n                                  {\n                                    staticStyle: { \"padding-bottom\": \"0\" },\n                                    attrs: {\n                                      icon: \"el-icon-circle-plus-outline\",\n                                      type: \"text\",\n                                    },\n                                    on: { click: _vm.addTreeItem },\n                                  },\n                                  [\n                                    _vm._v(\n                                      \"\\n              添加父级\\n            \"\n                                    ),\n                                  ]\n                                ),\n                              ],\n                              1\n                            )\n                          : _vm._e(),\n                        _vm._v(\" \"),\n                        _c(\"el-divider\"),\n                      ]\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.__config__.optionType !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"选项样式\" } },\n                        [\n                          _c(\n                            \"el-radio-group\",\n                            {\n                              model: {\n                                value: _vm.activeData.__config__.optionType,\n                                callback: function ($$v) {\n                                  _vm.$set(\n                                    _vm.activeData.__config__,\n                                    \"optionType\",\n                                    $$v\n                                  )\n                                },\n                                expression: \"activeData.__config__.optionType\",\n                              },\n                            },\n                            [\n                              _c(\n                                \"el-radio-button\",\n                                { attrs: { label: \"default\" } },\n                                [_vm._v(\"\\n              默认\\n            \")]\n                              ),\n                              _vm._v(\" \"),\n                              _c(\n                                \"el-radio-button\",\n                                { attrs: { label: \"button\" } },\n                                [_vm._v(\"\\n              按钮\\n            \")]\n                              ),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData[\"active-color\"] !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"开启颜色\" } },\n                        [\n                          _c(\"el-color-picker\", {\n                            model: {\n                              value: _vm.activeData[\"active-color\"],\n                              callback: function ($$v) {\n                                _vm.$set(_vm.activeData, \"active-color\", $$v)\n                              },\n                              expression: \"activeData['active-color']\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData[\"inactive-color\"] !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"关闭颜色\" } },\n                        [\n                          _c(\"el-color-picker\", {\n                            model: {\n                              value: _vm.activeData[\"inactive-color\"],\n                              callback: function ($$v) {\n                                _vm.$set(_vm.activeData, \"inactive-color\", $$v)\n                              },\n                              expression: \"activeData['inactive-color']\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.__config__.showLabel !== undefined &&\n                  _vm.activeData.__config__.labelWidth !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"显示标签\" } },\n                        [\n                          _c(\"el-switch\", {\n                            model: {\n                              value: _vm.activeData.__config__.showLabel,\n                              callback: function ($$v) {\n                                _vm.$set(\n                                  _vm.activeData.__config__,\n                                  \"showLabel\",\n                                  $$v\n                                )\n                              },\n                              expression: \"activeData.__config__.showLabel\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.branding !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"品牌烙印\" } },\n                        [\n                          _c(\"el-switch\", {\n                            on: { input: _vm.changeRenderKey },\n                            model: {\n                              value: _vm.activeData.branding,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.activeData, \"branding\", $$v)\n                              },\n                              expression: \"activeData.branding\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData[\"allow-half\"] !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"允许半选\" } },\n                        [\n                          _c(\"el-switch\", {\n                            model: {\n                              value: _vm.activeData[\"allow-half\"],\n                              callback: function ($$v) {\n                                _vm.$set(_vm.activeData, \"allow-half\", $$v)\n                              },\n                              expression: \"activeData['allow-half']\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData[\"show-text\"] !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"辅助文字\" } },\n                        [\n                          _c(\"el-switch\", {\n                            on: { change: _vm.rateTextChange },\n                            model: {\n                              value: _vm.activeData[\"show-text\"],\n                              callback: function ($$v) {\n                                _vm.$set(_vm.activeData, \"show-text\", $$v)\n                              },\n                              expression: \"activeData['show-text']\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData[\"show-score\"] !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"显示分数\" } },\n                        [\n                          _c(\"el-switch\", {\n                            on: { change: _vm.rateScoreChange },\n                            model: {\n                              value: _vm.activeData[\"show-score\"],\n                              callback: function ($$v) {\n                                _vm.$set(_vm.activeData, \"show-score\", $$v)\n                              },\n                              expression: \"activeData['show-score']\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData[\"show-stops\"] !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"显示间断点\" } },\n                        [\n                          _c(\"el-switch\", {\n                            model: {\n                              value: _vm.activeData[\"show-stops\"],\n                              callback: function ($$v) {\n                                _vm.$set(_vm.activeData, \"show-stops\", $$v)\n                              },\n                              expression: \"activeData['show-stops']\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.range !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"范围选择\" } },\n                        [\n                          _c(\"el-switch\", {\n                            on: { change: _vm.rangeChange },\n                            model: {\n                              value: _vm.activeData.range,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.activeData, \"range\", $$v)\n                              },\n                              expression: \"activeData.range\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.__config__.border !== undefined &&\n                  _vm.activeData.__config__.optionType === \"default\"\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"是否带边框\" } },\n                        [\n                          _c(\"el-switch\", {\n                            model: {\n                              value: _vm.activeData.__config__.border,\n                              callback: function ($$v) {\n                                _vm.$set(\n                                  _vm.activeData.__config__,\n                                  \"border\",\n                                  $$v\n                                )\n                              },\n                              expression: \"activeData.__config__.border\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.__config__.tag === \"el-color-picker\"\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"颜色格式\" } },\n                        [\n                          _c(\n                            \"el-select\",\n                            {\n                              style: { width: \"100%\" },\n                              attrs: {\n                                placeholder: \"请选择颜色格式\",\n                                clearable: \"\",\n                              },\n                              on: { change: _vm.colorFormatChange },\n                              model: {\n                                value: _vm.activeData[\"color-format\"],\n                                callback: function ($$v) {\n                                  _vm.$set(_vm.activeData, \"color-format\", $$v)\n                                },\n                                expression: \"activeData['color-format']\",\n                              },\n                            },\n                            _vm._l(\n                              _vm.colorFormatOptions,\n                              function (item, index) {\n                                return _c(\"el-option\", {\n                                  key: index,\n                                  attrs: {\n                                    label: item.label,\n                                    value: item.value,\n                                  },\n                                })\n                              }\n                            ),\n                            1\n                          ),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.size !== undefined &&\n                  (_vm.activeData.__config__.optionType === \"button\" ||\n                    _vm.activeData.__config__.border ||\n                    _vm.activeData.__config__.tag === \"el-color-picker\" ||\n                    _vm.activeData.__config__.tag === \"el-button\")\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"组件尺寸\" } },\n                        [\n                          _c(\n                            \"el-radio-group\",\n                            {\n                              model: {\n                                value: _vm.activeData.size,\n                                callback: function ($$v) {\n                                  _vm.$set(_vm.activeData, \"size\", $$v)\n                                },\n                                expression: \"activeData.size\",\n                              },\n                            },\n                            [\n                              _c(\n                                \"el-radio-button\",\n                                { attrs: { label: \"medium\" } },\n                                [_vm._v(\"\\n              中等\\n            \")]\n                              ),\n                              _vm._v(\" \"),\n                              _c(\n                                \"el-radio-button\",\n                                { attrs: { label: \"small\" } },\n                                [_vm._v(\"\\n              较小\\n            \")]\n                              ),\n                              _vm._v(\" \"),\n                              _c(\n                                \"el-radio-button\",\n                                { attrs: { label: \"mini\" } },\n                                [_vm._v(\"\\n              迷你\\n            \")]\n                              ),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData[\"show-word-limit\"] !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"输入统计\" } },\n                        [\n                          _c(\"el-switch\", {\n                            model: {\n                              value: _vm.activeData[\"show-word-limit\"],\n                              callback: function ($$v) {\n                                _vm.$set(_vm.activeData, \"show-word-limit\", $$v)\n                              },\n                              expression: \"activeData['show-word-limit']\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.__config__.tag === \"el-input-number\"\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"严格步数\" } },\n                        [\n                          _c(\"el-switch\", {\n                            model: {\n                              value: _vm.activeData[\"step-strictly\"],\n                              callback: function ($$v) {\n                                _vm.$set(_vm.activeData, \"step-strictly\", $$v)\n                              },\n                              expression: \"activeData['step-strictly']\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.__config__.tag === \"el-cascader\"\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"是否多选\" } },\n                        [\n                          _c(\"el-switch\", {\n                            model: {\n                              value: _vm.activeData.props.props.multiple,\n                              callback: function ($$v) {\n                                _vm.$set(\n                                  _vm.activeData.props.props,\n                                  \"multiple\",\n                                  $$v\n                                )\n                              },\n                              expression: \"activeData.props.props.multiple\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.__config__.tag === \"el-cascader\"\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"展示全路径\" } },\n                        [\n                          _c(\"el-switch\", {\n                            model: {\n                              value: _vm.activeData[\"show-all-levels\"],\n                              callback: function ($$v) {\n                                _vm.$set(_vm.activeData, \"show-all-levels\", $$v)\n                              },\n                              expression: \"activeData['show-all-levels']\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.__config__.tag === \"el-cascader\"\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"可否筛选\" } },\n                        [\n                          _c(\"el-switch\", {\n                            model: {\n                              value: _vm.activeData.filterable,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.activeData, \"filterable\", $$v)\n                              },\n                              expression: \"activeData.filterable\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.clearable !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"能否清空\" } },\n                        [\n                          _c(\"el-switch\", {\n                            model: {\n                              value: _vm.activeData.clearable,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.activeData, \"clearable\", $$v)\n                              },\n                              expression: \"activeData.clearable\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.__config__.showTip !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"显示提示\" } },\n                        [\n                          _c(\"el-switch\", {\n                            model: {\n                              value: _vm.activeData.__config__.showTip,\n                              callback: function ($$v) {\n                                _vm.$set(\n                                  _vm.activeData.__config__,\n                                  \"showTip\",\n                                  $$v\n                                )\n                              },\n                              expression: \"activeData.__config__.showTip\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.__config__.tag === \"el-upload\" ||\n                  _vm.activeData.__config__.tag === \"self-upload\"\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"多选文件\" } },\n                        [\n                          _c(\"el-switch\", {\n                            model: {\n                              value: _vm.activeData.multiple,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.activeData, \"multiple\", $$v)\n                              },\n                              expression: \"activeData.multiple\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData[\"auto-upload\"] !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"自动上传\" } },\n                        [\n                          _c(\"el-switch\", {\n                            model: {\n                              value: _vm.activeData[\"auto-upload\"],\n                              callback: function ($$v) {\n                                _vm.$set(_vm.activeData, \"auto-upload\", $$v)\n                              },\n                              expression: \"activeData['auto-upload']\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.readonly !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"是否只读\" } },\n                        [\n                          _c(\"el-switch\", {\n                            model: {\n                              value: _vm.activeData.readonly,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.activeData, \"readonly\", $$v)\n                              },\n                              expression: \"activeData.readonly\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.disabled !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"是否禁用\" } },\n                        [\n                          _c(\"el-switch\", {\n                            model: {\n                              value: _vm.activeData.disabled,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.activeData, \"disabled\", $$v)\n                              },\n                              expression: \"activeData.disabled\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.__config__.tag === \"el-select\"\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"能否搜索\" } },\n                        [\n                          _c(\"el-switch\", {\n                            model: {\n                              value: _vm.activeData.filterable,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.activeData, \"filterable\", $$v)\n                              },\n                              expression: \"activeData.filterable\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.__config__.tag === \"el-select\"\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"是否多选\" } },\n                        [\n                          _c(\"el-switch\", {\n                            on: { change: _vm.multipleChange },\n                            model: {\n                              value: _vm.activeData.multiple,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.activeData, \"multiple\", $$v)\n                              },\n                              expression: \"activeData.multiple\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.__config__.required !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"是否必填\" } },\n                        [\n                          _c(\"el-switch\", {\n                            model: {\n                              value: _vm.activeData.__config__.required,\n                              callback: function ($$v) {\n                                _vm.$set(\n                                  _vm.activeData.__config__,\n                                  \"required\",\n                                  $$v\n                                )\n                              },\n                              expression: \"activeData.__config__.required\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.__config__.tips !== undefined\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"开启描述\" } },\n                        [\n                          _c(\"el-switch\", {\n                            model: {\n                              value: _vm.activeData.__config__.tips,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.activeData.__config__, \"tips\", $$v)\n                              },\n                              expression: \"activeData.__config__.tips\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.__config__.tips\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"描述内容\" } },\n                        [\n                          _c(\"el-input\", {\n                            attrs: { placeholder: \"请输入描述\" },\n                            model: {\n                              value: _vm.activeData.__config__.tipsDesc,\n                              callback: function ($$v) {\n                                _vm.$set(\n                                  _vm.activeData.__config__,\n                                  \"tipsDesc\",\n                                  $$v\n                                )\n                              },\n                              expression: \"activeData.__config__.tipsDesc\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.__config__.tips\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"描述链接\" } },\n                        [\n                          _c(\"el-switch\", {\n                            model: {\n                              value: _vm.activeData.__config__.tipsIsLink,\n                              callback: function ($$v) {\n                                _vm.$set(\n                                  _vm.activeData.__config__,\n                                  \"tipsIsLink\",\n                                  $$v\n                                )\n                              },\n                              expression: \"activeData.__config__.tipsIsLink\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.__config__.tipsIsLink\n                    ? _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"链接地址\" } },\n                        [\n                          _c(\"el-input\", {\n                            attrs: { placeholder: \"请输入链接地址\" },\n                            model: {\n                              value: _vm.activeData.__config__.tipsLink,\n                              callback: function ($$v) {\n                                _vm.$set(\n                                  _vm.activeData.__config__,\n                                  \"tipsLink\",\n                                  $$v\n                                )\n                              },\n                              expression: \"activeData.__config__.tipsLink\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.__config__.layoutTree\n                    ? [\n                        _c(\"el-divider\", [_vm._v(\"布局结构树\")]),\n                        _vm._v(\" \"),\n                        _c(\"el-tree\", {\n                          attrs: {\n                            data: [_vm.activeData.__config__],\n                            props: _vm.layoutTreeProps,\n                            \"node-key\": \"renderKey\",\n                            \"default-expand-all\": \"\",\n                            draggable: \"\",\n                          },\n                          scopedSlots: _vm._u(\n                            [\n                              {\n                                key: \"default\",\n                                fn: function (ref) {\n                                  var node = ref.node\n                                  var data = ref.data\n                                  return _c(\"span\", {}, [\n                                    _c(\n                                      \"span\",\n                                      { staticClass: \"node-label\" },\n                                      [\n                                        _c(\"svg-icon\", {\n                                          staticClass: \"node-icon\",\n                                          attrs: {\n                                            \"icon-class\": data.__config__\n                                              ? data.__config__.tagIcon\n                                              : data.tagIcon,\n                                          },\n                                        }),\n                                        _vm._v(\n                                          \"\\n                \" +\n                                            _vm._s(node.label) +\n                                            \"\\n              \"\n                                        ),\n                                      ],\n                                      1\n                                    ),\n                                  ])\n                                },\n                              },\n                            ],\n                            null,\n                            false,\n                            3398330875\n                          ),\n                        }),\n                      ]\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.activeData.__config__.layout === \"colFormItem\"\n                    ? [\n                        _c(\"el-divider\", [_vm._v(\"正则校验\")]),\n                        _vm._v(\" \"),\n                        _vm._l(\n                          _vm.activeData.__config__.regList,\n                          function (item, index) {\n                            return _c(\n                              \"div\",\n                              { key: index, staticClass: \"reg-item\" },\n                              [\n                                _c(\n                                  \"span\",\n                                  {\n                                    staticClass: \"close-btn\",\n                                    on: {\n                                      click: function ($event) {\n                                        return _vm.activeData.__config__.regList.splice(\n                                          index,\n                                          1\n                                        )\n                                      },\n                                    },\n                                  },\n                                  [_c(\"i\", { staticClass: \"el-icon-close\" })]\n                                ),\n                                _vm._v(\" \"),\n                                _c(\n                                  \"el-form-item\",\n                                  { attrs: { label: \"表达式\" } },\n                                  [\n                                    _c(\"el-input\", {\n                                      attrs: { placeholder: \"请输入正则\" },\n                                      model: {\n                                        value: item.pattern,\n                                        callback: function ($$v) {\n                                          _vm.$set(item, \"pattern\", $$v)\n                                        },\n                                        expression: \"item.pattern\",\n                                      },\n                                    }),\n                                  ],\n                                  1\n                                ),\n                                _vm._v(\" \"),\n                                _c(\n                                  \"el-form-item\",\n                                  {\n                                    staticStyle: { \"margin-bottom\": \"0\" },\n                                    attrs: { label: \"错误提示\" },\n                                  },\n                                  [\n                                    _c(\"el-input\", {\n                                      attrs: { placeholder: \"请输入错误提示\" },\n                                      model: {\n                                        value: item.message,\n                                        callback: function ($$v) {\n                                          _vm.$set(item, \"message\", $$v)\n                                        },\n                                        expression: \"item.message\",\n                                      },\n                                    }),\n                                  ],\n                                  1\n                                ),\n                              ],\n                              1\n                            )\n                          }\n                        ),\n                        _vm._v(\" \"),\n                        _c(\n                          \"div\",\n                          { staticStyle: { \"margin-left\": \"20px\" } },\n                          [\n                            _c(\n                              \"el-button\",\n                              {\n                                attrs: {\n                                  icon: \"el-icon-circle-plus-outline\",\n                                  type: \"text\",\n                                },\n                                on: { click: _vm.addReg },\n                              },\n                              [_vm._v(\"\\n              添加规则\\n            \")]\n                            ),\n                          ],\n                          1\n                        ),\n                      ]\n                    : _vm._e(),\n                ],\n                2\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-form\",\n                {\n                  directives: [\n                    {\n                      name: \"show\",\n                      rawName: \"v-show\",\n                      value: _vm.currentTab === \"form\",\n                      expression: \"currentTab === 'form'\",\n                    },\n                  ],\n                  attrs: { size: \"small\", \"label-width\": \"90px\" },\n                },\n                [\n                  _c(\n                    \"el-form-item\",\n                    { attrs: { label: \"表单名\" } },\n                    [\n                      _c(\"el-input\", {\n                        attrs: { placeholder: \"请输入表单名（ref）\" },\n                        model: {\n                          value: _vm.formConf.formRef,\n                          callback: function ($$v) {\n                            _vm.$set(_vm.formConf, \"formRef\", $$v)\n                          },\n                          expression: \"formConf.formRef\",\n                        },\n                      }),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-form-item\",\n                    { attrs: { label: \"表单模型\" } },\n                    [\n                      _c(\"el-input\", {\n                        attrs: { placeholder: \"请输入数据模型\" },\n                        model: {\n                          value: _vm.formConf.formModel,\n                          callback: function ($$v) {\n                            _vm.$set(_vm.formConf, \"formModel\", $$v)\n                          },\n                          expression: \"formConf.formModel\",\n                        },\n                      }),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-form-item\",\n                    { attrs: { label: \"校验模型\" } },\n                    [\n                      _c(\"el-input\", {\n                        attrs: { placeholder: \"请输入校验模型\" },\n                        model: {\n                          value: _vm.formConf.formRules,\n                          callback: function ($$v) {\n                            _vm.$set(_vm.formConf, \"formRules\", $$v)\n                          },\n                          expression: \"formConf.formRules\",\n                        },\n                      }),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-form-item\",\n                    { attrs: { label: \"表单尺寸\" } },\n                    [\n                      _c(\n                        \"el-radio-group\",\n                        {\n                          model: {\n                            value: _vm.formConf.size,\n                            callback: function ($$v) {\n                              _vm.$set(_vm.formConf, \"size\", $$v)\n                            },\n                            expression: \"formConf.size\",\n                          },\n                        },\n                        [\n                          _c(\n                            \"el-radio-button\",\n                            { attrs: { label: \"medium\" } },\n                            [_vm._v(\"\\n              中等\\n            \")]\n                          ),\n                          _vm._v(\" \"),\n                          _c(\"el-radio-button\", { attrs: { label: \"small\" } }, [\n                            _vm._v(\"\\n              较小\\n            \"),\n                          ]),\n                          _vm._v(\" \"),\n                          _c(\"el-radio-button\", { attrs: { label: \"mini\" } }, [\n                            _vm._v(\"\\n              迷你\\n            \"),\n                          ]),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-form-item\",\n                    { attrs: { label: \"标签对齐\" } },\n                    [\n                      _c(\n                        \"el-radio-group\",\n                        {\n                          model: {\n                            value: _vm.formConf.labelPosition,\n                            callback: function ($$v) {\n                              _vm.$set(_vm.formConf, \"labelPosition\", $$v)\n                            },\n                            expression: \"formConf.labelPosition\",\n                          },\n                        },\n                        [\n                          _c(\"el-radio-button\", { attrs: { label: \"left\" } }, [\n                            _vm._v(\"\\n              左对齐\\n            \"),\n                          ]),\n                          _vm._v(\" \"),\n                          _c(\"el-radio-button\", { attrs: { label: \"right\" } }, [\n                            _vm._v(\"\\n              右对齐\\n            \"),\n                          ]),\n                          _vm._v(\" \"),\n                          _c(\"el-radio-button\", { attrs: { label: \"top\" } }, [\n                            _vm._v(\"\\n              顶部对齐\\n            \"),\n                          ]),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-form-item\",\n                    { attrs: { label: \"标签宽度\" } },\n                    [\n                      _c(\"el-input\", {\n                        attrs: {\n                          type: \"number\",\n                          placeholder: \"请输入标签宽度\",\n                        },\n                        model: {\n                          value: _vm.formConf.labelWidth,\n                          callback: function ($$v) {\n                            _vm.$set(_vm.formConf, \"labelWidth\", _vm._n($$v))\n                          },\n                          expression: \"formConf.labelWidth\",\n                        },\n                      }),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-form-item\",\n                    { attrs: { label: \"栅格间隔\" } },\n                    [\n                      _c(\"el-input-number\", {\n                        attrs: { min: 0, placeholder: \"栅格间隔\" },\n                        model: {\n                          value: _vm.formConf.gutter,\n                          callback: function ($$v) {\n                            _vm.$set(_vm.formConf, \"gutter\", $$v)\n                          },\n                          expression: \"formConf.gutter\",\n                        },\n                      }),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-form-item\",\n                    { attrs: { label: \"禁用表单\" } },\n                    [\n                      _c(\"el-switch\", {\n                        model: {\n                          value: _vm.formConf.disabled,\n                          callback: function ($$v) {\n                            _vm.$set(_vm.formConf, \"disabled\", $$v)\n                          },\n                          expression: \"formConf.disabled\",\n                        },\n                      }),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-form-item\",\n                    { attrs: { label: \"表单按钮\" } },\n                    [\n                      _c(\"el-switch\", {\n                        model: {\n                          value: _vm.formConf.formBtns,\n                          callback: function ($$v) {\n                            _vm.$set(_vm.formConf, \"formBtns\", $$v)\n                          },\n                          expression: \"formConf.formBtns\",\n                        },\n                      }),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-form-item\",\n                    { attrs: { label: \"显示未选中组件边框\" } },\n                    [\n                      _c(\"el-switch\", {\n                        model: {\n                          value: _vm.formConf.unFocusedComponentBorder,\n                          callback: function ($$v) {\n                            _vm.$set(\n                              _vm.formConf,\n                              \"unFocusedComponentBorder\",\n                              $$v\n                            )\n                          },\n                          expression: \"formConf.unFocusedComponentBorder\",\n                        },\n                      }),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\"treeNode-dialog\", {\n        attrs: { visible: _vm.dialogVisible, title: \"添加选项\" },\n        on: {\n          \"update:visible\": function ($event) {\n            _vm.dialogVisible = $event\n          },\n          commit: _vm.addNode,\n        },\n      }),\n      _vm._v(\" \"),\n      _c(\"icons-dialog\", {\n        attrs: {\n          visible: _vm.iconsVisible,\n          current: _vm.activeData[_vm.currentIconModel],\n        },\n        on: {\n          \"update:visible\": function ($event) {\n            _vm.iconsVisible = $event\n          },\n          select: _vm.setIcon,\n        },\n      }),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}