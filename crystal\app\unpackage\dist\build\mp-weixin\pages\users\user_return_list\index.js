(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/users/user_return_list/index"],{1237:function(t,e,n){"use strict";n.r(e);var i=n("db8a"),o=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);e["default"]=o.a},"266f":function(t,e,n){"use strict";n.r(e);var i=n("d3de"),o=n("1237");for(var r in o)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(r);n("6256");var a=n("828b"),u=Object(a["a"])(o["default"],i["b"],i["c"],!1,null,"353f5b7a",null,!1,i["a"],void 0);e["default"]=u.exports},"3f65":function(t,e,n){},"44e9":function(t,e,n){"use strict";(function(t,e){var i=n("47a9");n("5c2d");i(n("3240"));var o=i(n("266f"));t.__webpack_require_UNI_MP_PLUGIN__=n,e(o.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},6256:function(t,e,n){"use strict";var i=n("3f65"),o=n.n(i);o.a},d3de:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){}));var i=function(){var t=this.$createElement,e=(this._self._c,this.orderList.length),n=this.orderList.length,i=this.orderList.length;this.$mp.data=Object.assign({},{$root:{g0:e,g1:n,g2:i}})},o=[]},db8a:function(t,e,n){"use strict";(function(t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=n("3988"),o=n("cda4"),r=n("8f59"),a={components:{emptyPage:function(){n.e("components/emptyPage").then(function(){return resolve(n("bf60"))}.bind(null,n)).catch(n.oe)},home:function(){n.e("components/home/<USER>").then(function(){return resolve(n("bc9e"))}.bind(null,n)).catch(n.oe)},authorize:function(){Promise.all([n.e("common/vendor"),n.e("components/Authorize")]).then(function(){return resolve(n("cf49"))}.bind(null,n)).catch(n.oe)}},data:function(){return{loading:!1,loadend:!1,loadTitle:"加载更多",orderList:[],orderStatus:-3,page:1,limit:20,isAuto:!1,isShowAuth:!1}},computed:(0,r.mapGetters)(["isLogin"]),watch:{isLogin:{handler:function(t,e){t&&this.getOrderList()},deep:!0}},onLoad:function(){this.isLogin?this.getOrderList():(0,o.toLogin)()},onReachBottom:function(){this.getOrderList()},methods:{onLoadFun:function(){this.getOrderList()},authColse:function(t){this.isShowAuth=t},goOrderDetails:function(e){if(!e)return that.$util.Tips({title:"缺少订单号无法查看订单详情"});t.navigateTo({url:"/pages/order_details/index?order_id="+e+"&isReturen=1"})},getOrderList:function(){var t=this;t.loadend||t.loading||(t.loading=!0,t.loadTitle="",(0,i.getOrderList)({type:t.orderStatus,page:t.page,limit:t.limit}).then((function(e){var n=e.data.list||[],i=n.length<t.limit;t.orderList=t.$util.SplitArray(n,t.orderList),t.$set(t,"orderList",t.orderList),t.loadend=i,t.loading=!1,t.loadTitle=i?"我也是有底线的":"加载更多",t.page=t.page+1})).catch((function(e){t.loading=!1,t.loadTitle="加载更多"})))}}};e.default=a}).call(this,n("df3c")["default"])}},[["44e9","common/runtime","common/vendor"]]]);