{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\marketing\\bargain\\bargainGoods\\index.vue?vue&type=template&id=139608a7&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\marketing\\bargain\\bargainGoods\\index.vue", "mtime": 1753666157888}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\n<div class=\"divBox\">\n  <el-card class=\"box-card\">\n    <div slot=\"header\" class=\"clearfix\">\n      <div class=\"container\">\n        <el-form inline>\n          <el-form-item label=\"砍价状态：\">\n            <el-select v-model=\"tableFrom.status\" placeholder=\"请选择\" class=\"filter-item selWidth mr20\" @change=\"getList(1)\" clearable>\n              <el-option label=\"关闭\" :value=\"0\" />\n              <el-option label=\"开启\" :value=\"1\" />\n            </el-select>\n          </el-form-item>\n          <el-form-item label=\"商品搜索：\">\n            <el-input v-model=\"tableFrom.keywords\" placeholder=\"请输入商品名称、ID\" class=\"selWidth\" clearable>\n              <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"getList(1)\"/>\n            </el-input>\n          </el-form-item>\n        </el-form>\n      </div>\n      <router-link :to=\" { path:'/marketing/bargain/creatBargain' }\">\n        <el-button size=\"mini\" type=\"primary\" class=\"mr10\" v-hasPermi=\"['admin:bargain:save']\">添加砍价商品</el-button>\n      </router-link>\n      <el-button size=\"mini\" class=\"mr10\" @click=\"exportList\" v-hasPermi=\"['admin:export:excel:bargain']\">导出</el-button>\n    </div>\n    <el-table\n      v-loading=\"listLoading\"\n      :data=\"tableData.data\"\n      style=\"width: 100%\"\n      size=\"mini\"\n      ref=\"multipleTable\"\n      :header-cell-style=\" {fontWeight:'bold'}\"\n    >\n      <el-table-column\n        prop=\"id\"\n        label=\"ID\"\n        min-width=\"50\"\n      />\n      <el-table-column label=\"砍价图片\" min-width=\"80\">\n        <template slot-scope=\"scope\">\n          <div class=\"demo-image__preview\">\n            <el-image\n              style=\"width: 36px; height: 36px\"\n              :src=\"scope.row.image\"\n              :preview-src-list=\"[scope.row.image]\"\n            />\n          </div>\n        </template>\n      </el-table-column>\n      <el-table-column label=\"砍价名称\" prop=\"title\" min-width=\"300\">\n        <template slot-scope=\"scope\">\n            <el-popover trigger=\"hover\" placement=\"right\" :open-delay=\"800\">\n              <div class=\"text_overflow\" slot=\"reference\">{{scope.row.title}}</div>\n              <div class=\"pup_card\">{{scope.row.title}}</div>\n            </el-popover>\n          </template>\n      </el-table-column>\n      <el-table-column\n        label=\"砍价价格\"\n        prop=\"price\"\n        min-width=\"100\"\n        align=\"center\"\n      />\n      <el-table-column\n        label=\"最低价\"\n        prop=\"minPrice\"\n        min-width=\"100\"\n        align=\"center\"\n      />\n      <el-table-column\n        label=\"参与人数\"\n        prop=\"countPeopleAll\"\n        min-width=\"100\"\n        align=\"center\"\n      />\n      <el-table-column\n        label=\"帮忙砍价人数\"\n        prop=\"countPeopleHelp\"\n        min-width=\"100\"\n        align=\"center\"\n      />\n      <el-table-column\n        label=\"砍价成功人数\"\n        prop=\"countPeopleSuccess\"\n        min-width=\"100\"\n        align=\"center\"\n      />\n      <el-table-column\n        label=\"限量\"\n        min-width=\"100\"\n        prop=\"quotaShow\"\n        align=\"center\"\n      />\n      <el-table-column\n        label=\"限量剩余\"\n        prop=\"surplusQuota\"\n        min-width=\"100\"\n        align=\"center\"\n      />\n      <el-table-column\n        prop=\"stopTime\"\n        label=\"活动时间\"\n        min-width=\"160\"\n      >\n        <template slot-scope=\"scope\">\n          {{scope.row.startTime + ' ~ ' + scope.row.stopTime}}\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"砍价状态\"\n        min-width=\"150\"\n      >\n        <template slot-scope=\"scope\">\n          <el-switch\n            v-model=\"scope.row.status\"\n            :active-value=\"true\"\n            :inactive-value=\"false\"\n            active-text=\"开启\"\n            inactive-text=\"关闭\"\n            @change=\"onchangeIsShow(scope.row)\"\n            v-if=\"checkPermi(['admin:bargain:update:status'])\"\n          />\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" min-width=\"130\" fixed=\"right\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <router-link :to=\"{ path:'/marketing/bargain/creatBargain/' + scope.row.id}\">\n            <el-button type=\"text\" size=\"small\" v-hasPermi=\"['admin:bargain:info']\">编辑</el-button>\n          </router-link>\n          <el-button type=\"text\" size=\"small\" @click=\"handleDelete(scope.row.id, scope.$index)\" class=\"mr10\" v-hasPermi=\"['admin:bargain:delete']\">删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <div class=\"block mb20\">\n      <el-pagination\n        :page-sizes=\"[10, 20, 30, 40]\"\n        :page-size=\"tableFrom.limit\"\n        :current-page=\"tableFrom.page\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        :total=\"tableData.total\"\n        @size-change=\"handleSizeChange\"\n        @current-change=\"pageChange\"\n        v-if=\"checkPermi(['admin:bargain:list'])\"\n      />\n    </div>\n  </el-card>\n</div>\n", null]}