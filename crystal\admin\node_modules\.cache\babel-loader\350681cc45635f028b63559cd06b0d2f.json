{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\braceletsIndex.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\braceletsIndex.vue", "mtime": 1753666157921}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _store = require(\"@/api/store\");\nvar _auth = require(\"@/utils/auth\");\nvar _taoBao = _interopRequireDefault(require(\"./taoBao\"));\nvar _permission = require(\"@/utils/permission\");\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n// 权限判断函数\nvar _default = exports.default = {\n  name: \"ProductList\",\n  components: {\n    taoBao: _taoBao.default\n  },\n  data: function data() {\n    return {\n      props: {\n        children: \"child\",\n        label: \"name\",\n        value: \"id\",\n        emitPath: false\n      },\n      // roterPre: roterPre,\n      headeNum: [],\n      listLoading: true,\n      tableData: {\n        data: [],\n        total: 0\n      },\n      tableFrom: {\n        page: 1,\n        limit: 20,\n        cateId: \"\",\n        keywords: \"\",\n        type: \"1\",\n        productType: \"1\"\n      },\n      categoryList: [],\n      merCateList: [],\n      objectUrl: process.env.VUE_APP_BASE_API,\n      dialogVisible: false\n    };\n  },\n  mounted: function mounted() {\n    this.goodHeade();\n    this.getList();\n    this.getCategorySelect();\n    this.checkedCities = this.$cache.local.has(\"goods_stroge\") ? this.$cache.local.getJSON(\"goods_stroge\") : this.checkedCities;\n  },\n  methods: {\n    checkPermi: _permission.checkPermi,\n    handleRestore: function handleRestore(id) {\n      var _this = this;\n      this.$modalSure(\"恢复商品\").then(function () {\n        (0, _store.restoreApi)(id).then(function (res) {\n          _this.$message.success(\"操作成功\");\n          _this.goodHeade();\n          _this.getList();\n        });\n      });\n    },\n    seachList: function seachList() {\n      this.tableFrom.page = 1;\n      this.getList();\n    },\n    handleClose: function handleClose() {\n      this.dialogVisible = false;\n    },\n    handleCloseMod: function handleCloseMod(item) {\n      this.dialogVisible = item;\n      this.goodHeade();\n      this.getList();\n    },\n    // 复制\n    onCopy: function onCopy() {\n      this.dialogVisible = true;\n    },\n    // 导出\n    exports: function exports() {\n      (0, _store.productExcelApi)({\n        cateId: this.tableFrom.cateId,\n        keywords: this.tableFrom.keywords,\n        type: this.tableFrom.type,\n        productType: this.tableFrom.productType\n      }).then(function (res) {\n        window.location.href = res.fileName;\n      });\n    },\n    // 获取商品表单头数量\n    goodHeade: function goodHeade() {\n      var _this2 = this;\n      (0, _store.productHeadersApi)({\n        type: 1\n      }).then(function (res) {\n        _this2.headeNum = res;\n      }).catch(function (res) {\n        _this2.$message.error(res.message);\n      });\n    },\n    // 商户分类；\n    getCategorySelect: function getCategorySelect() {\n      var _this3 = this;\n      (0, _store.categoryApi)({\n        status: -1,\n        type: 8\n      }).then(function (res) {\n        _this3.merCateList = res;\n      }).catch(function (res) {\n        _this3.$message.error(res.message);\n      });\n    },\n    // 列表\n    getList: function getList() {\n      var _this4 = this;\n      this.listLoading = true;\n      (0, _store.productLstApi)(this.tableFrom).then(function (res) {\n        _this4.tableData.data = res.list;\n        _this4.tableData.total = res.total;\n        _this4.listLoading = false;\n      }).catch(function (res) {\n        _this4.listLoading = false;\n        _this4.$message.error(res.message);\n      });\n    },\n    pageChange: function pageChange(page) {\n      this.tableFrom.page = page;\n      this.getList();\n    },\n    handleSizeChange: function handleSizeChange(val) {\n      this.tableFrom.limit = val;\n      this.getList();\n    },\n    // 删除\n    handleDelete: function handleDelete(id, type) {\n      var _this5 = this;\n      this.$modalSure(\"\\u5220\\u9664 id \\u4E3A \".concat(id, \" \\u7684\\u5546\\u54C1\")).then(function () {\n        var deleteFlag = type == 5 ? \"delete\" : \"recycle\";\n        (0, _store.productDeleteApi)(id, deleteFlag).then(function () {\n          _this5.$message.success(\"删除成功\");\n          _this5.getList();\n          _this5.goodHeade();\n        });\n      });\n    },\n    onchangeIsShow: function onchangeIsShow(row) {\n      var _this6 = this;\n      row.isShow ? (0, _store.putOnShellApi)(row.id).then(function () {\n        _this6.$message.success(\"上架成功\");\n        _this6.getList();\n        _this6.goodHeade();\n      }).catch(function () {\n        row.isShow = !row.isShow;\n      }) : (0, _store.offShellApi)(row.id).then(function () {\n        _this6.$message.success(\"下架成功\");\n        _this6.getList();\n        _this6.goodHeade();\n      }).catch(function () {\n        row.isShow = !row.isShow;\n      });\n    }\n  }\n};", null]}