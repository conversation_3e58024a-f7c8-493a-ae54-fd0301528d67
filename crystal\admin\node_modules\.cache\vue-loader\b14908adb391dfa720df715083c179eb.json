{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\FormGenerator\\index\\TreeNodeDialog.vue?vue&type=template&id=01b3f275&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\FormGenerator\\index\\TreeNodeDialog.vue", "mtime": 1753666157771}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\n<div>\n  <el-dialog\n    v-bind=\"$attrs\"\n    :close-on-click-modal=\"false\"\n    :modal-append-to-body=\"false\"\n    v-on=\"$listeners\"\n    @open=\"onOpen\"\n    @close=\"onClose\"\n  >\n    <el-row :gutter=\"0\">\n      <el-form\n        ref=\"elForm\"\n        :model=\"formData\"\n        :rules=\"rules\"\n        size=\"small\"\n        label-width=\"100px\"\n      >\n        <el-col :span=\"24\">\n          <el-form-item\n            label=\"选项名\"\n            prop=\"label\"\n          >\n            <el-input\n              v-model=\"formData.label\"\n              placeholder=\"请输入选项名\"\n              clearable\n            />\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"24\">\n          <el-form-item\n            label=\"选项值\"\n            prop=\"value\"\n          >\n            <el-input\n              v-model=\"formData.value\"\n              placeholder=\"请输入选项值\"\n              clearable\n            >\n              <el-select\n                slot=\"append\"\n                v-model=\"dataType\"\n                :style=\"{width: '100px'}\"\n              >\n                <el-option\n                  v-for=\"(item, index) in dataTypeOptions\"\n                  :key=\"index\"\n                  :label=\"item.label\"\n                  :value=\"item.value\"\n                  :disabled=\"item.disabled\"\n                />\n              </el-select>\n            </el-input>\n          </el-form-item>\n        </el-col>\n      </el-form>\n    </el-row>\n    <div slot=\"footer\">\n      <el-button\n        type=\"primary\"\n        @click=\"handelConfirm\"\n      >\n        确定\n      </el-button>\n      <el-button @click=\"close\">\n        取消\n      </el-button>\n    </div>\n  </el-dialog>\n</div>\n", null]}