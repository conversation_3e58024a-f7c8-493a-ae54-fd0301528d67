<view><view class="newsList"><block wx:if="{{$root.g0>0}}"><view class="swiper"><swiper indicator-dots="true" autoplay="{{autoplay}}" circular="{{circular}}" interval="{{interval}}" duration="{{duration}}" indicator-color="rgba(102,102,102,0.3)" indicator-active-color="#666"><block wx:for="{{imgUrls}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><swiper-item><navigator url="{{'/pages/news_details/index?id='+item.id}}"><image class="slide-image" src="{{item.imageInput}}"></image></navigator></swiper-item></block></block></swiper></view></block><block wx:if="{{$root.g1>0}}"><view class="nav"><scroll-view class="scroll-view_x" style="width:auto;overflow:hidden;" scroll-x="{{true}}" scroll-with-animation="{{true}}" scroll-left="{{scrollLeft}}"><block wx:for="{{navList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view data-event-opts="{{[['tap',[['tabSelect',['$0',index],[[['navList','',index,'id']]]]]]]}}" class="{{['item','borRadius14',active==item.id?'on':'']}}" bindtap="__e"><view>{{item.name}}</view><block wx:if="{{active==item.id}}"><view class="line bg-color"></view></block></view></block></block></scroll-view></view></block><view class="list"><block wx:for="{{articleList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><navigator class="item acea-row row-between-wrapper" url="{{'/pages/news_details/index?id='+item.id}}" hover-class="none"><view class="text acea-row row-column-between"><view class="name line2">{{item.title}}</view><view>{{item.createTime}}</view></view><view class="pictrue"><image src="{{item.imageInput}}"></image></view></navigator></block></block></view></view><block wx:if="{{$root.g2}}"><view class="noCommodity"><view class="pictrue"><image src="../../static/images/noNews.png"></image></view></view></block><home vue-id="2ec9fb3c-1" bind:__l="__l"></home></view>