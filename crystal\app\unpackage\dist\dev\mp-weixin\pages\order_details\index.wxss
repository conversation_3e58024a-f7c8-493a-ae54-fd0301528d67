@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.shuoming.data-v-251d72aa {
  width: 32rpx;
  height: 32rpx;
}
.goodCall.data-v-251d72aa {
  color: #c9ab79;
  text-align: center;
  width: 100%;
  height: 86rpx;
  padding: 0 30rpx;
  border-bottom: 1rpx solid #eee;
  font-size: 30rpx;
  line-height: 86rpx;
  background: #fff;
}
.goodCall .icon-kefu.data-v-251d72aa {
  font-size: 36rpx;
  margin-right: 15rpx;
}
.goodCall button.data-v-251d72aa {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 86rpx;
  font-size: 30rpx;
  color: #c9ab79;
}
.order-details .header.data-v-251d72aa {
  height: 250rpx;
  padding: 0 30rpx;
}
.order-details .header.on.data-v-251d72aa {
  background-color: #666 !important;
}
.order-details .header .pictrue.data-v-251d72aa {
  width: 110rpx;
  height: 110rpx;
}
.order-details .header .pictrue image.data-v-251d72aa {
  width: 100%;
  height: 100%;
}
.order-details .header .data.data-v-251d72aa {
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
  margin-left: 27rpx;
}
.order-details .header .data.on.data-v-251d72aa {
  margin-left: 0;
}
.order-details .header .data .state.data-v-251d72aa {
  font-size: 30rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 7rpx;
}
.order-details .header .data .time.data-v-251d72aa {
  margin-left: 20rpx;
}
.picTxt.data-v-251d72aa {
  height: 150rpx;
}
.order-details .nav.data-v-251d72aa {
  background-color: #fff;
  font-size: 26rpx;
  color: #282828;
  padding: 27rpx 0;
  width: 100%;
  border-radius: 14rpx;
  margin: -100rpx auto 0 auto;
}
.order-details .nav .navCon.data-v-251d72aa {
  padding: 0 40rpx;
}
.order-details .nav .on.data-v-251d72aa {
  color: #c9ab79;
}
.order-details .nav .progress.data-v-251d72aa {
  padding: 0 65rpx;
  margin-top: 10rpx;
}
.order-details .nav .progress .line.data-v-251d72aa {
  width: 100rpx;
  height: 2rpx;
  background-color: #939390;
}
.order-details .nav .progress .iconfont.data-v-251d72aa {
  font-size: 25rpx;
  color: #939390;
  margin-top: -2rpx;
}
.order-details .address.data-v-251d72aa {
  font-size: 26rpx;
  color: #868686;
  background-color: #fff;
  margin-top: 15rpx;
  padding: 30rpx 24rpx;
}
.order-details .address .name.data-v-251d72aa {
  font-size: 30rpx;
  color: #282828;
  margin-bottom: 15rpx;
}
.order-details .address .name .phone.data-v-251d72aa {
  margin-left: 40rpx;
}
.order-details .line.data-v-251d72aa {
  width: 100%;
  height: 3rpx;
}
.order-details .line image.data-v-251d72aa {
  width: 100%;
  height: 100%;
  display: block;
}
.order-details .wrapper.data-v-251d72aa {
  background-color: #fff;
  margin-top: 12rpx;
  padding: 30rpx 24rpx;
}
.order-details .wrapper .item.data-v-251d72aa {
  font-size: 28rpx;
  color: #282828;
}
.order-details .wrapper .item ~ .item.data-v-251d72aa {
  margin-top: 20rpx;
}
.order-details .wrapper .item .conter.data-v-251d72aa {
  color: #868686;
  text-align: right;
}
.order-details .wrapper .item .conter .copy.data-v-251d72aa {
  font-size: 20rpx;
  color: #333;
  border-radius: 20rpx;
  border: 1rpx solid #666;
  padding: 3rpx 15rpx;
  margin-left: 24rpx;
}
.order-details .wrapper .actualPay.data-v-251d72aa {
  border-top: 1rpx solid #eee;
  margin-top: 30rpx;
  padding-top: 30rpx;
}
.order-details .wrapper .actualPay .money.data-v-251d72aa {
  font-weight: bold;
  font-size: 30rpx;
}
.order-details .footer.data-v-251d72aa {
  width: 100%;
  height: 100rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  background-color: #fff;
  padding: 0 30rpx;
  box-sizing: border-box;
}
.order-details .footer .bnt.data-v-251d72aa {
  width: 158rpx;
  height: 54rpx;
  text-align: center;
  line-height: 54rpx;
  border-radius: 50rpx;
  color: #fff;
  font-size: 27rpx;
}
.order-details .footer .bnt.cancel.data-v-251d72aa {
  color: #aaa;
  border: 1rpx solid #ddd;
}
.order-details .footer .bnt ~ .bnt.data-v-251d72aa {
  margin-left: 18rpx;
}
.order-details .writeOff.data-v-251d72aa {
  background-color: #fff;
  margin-top: 15rpx;
  padding-bottom: 50rpx;
}
.order-details .writeOff .title.data-v-251d72aa {
  font-size: 30rpx;
  color: #282828;
  height: 87rpx;
  border-bottom: 1px solid #f0f0f0;
  padding: 0 24rpx;
  line-height: 87rpx;
}
.order-details .writeOff .grayBg.data-v-251d72aa {
  background-color: #f2f5f7;
  width: 590rpx;
  height: 384rpx;
  border-radius: 20rpx 20rpx 0 0;
  margin: 50rpx auto 0 auto;
  padding-top: 55rpx;
}
.order-details .writeOff .grayBg .pictrue.data-v-251d72aa {
  width: 290rpx;
  height: 290rpx;
  margin: 0 auto;
}
.order-details .writeOff .grayBg .pictrue image.data-v-251d72aa {
  width: 100%;
  height: 100%;
  display: block;
}
.order-details .writeOff .gear.data-v-251d72aa {
  width: 590rpx;
  height: 30rpx;
  margin: 0 auto;
}
.order-details .writeOff .gear image.data-v-251d72aa {
  width: 100%;
  height: 100%;
  display: block;
}
.order-details .writeOff .num.data-v-251d72aa {
  background-color: #f0c34c;
  width: 590rpx;
  height: 84rpx;
  color: #282828;
  font-size: 48rpx;
  margin: 0 auto;
  border-radius: 0 0 20rpx 20rpx;
  text-align: center;
  padding-top: 4rpx;
}
.order-details .writeOff .rules.data-v-251d72aa {
  margin: 46rpx 30rpx 0 30rpx;
  border-top: 1px solid #f0f0f0;
  padding-top: 10rpx;
}
.order-details .writeOff .rules .item.data-v-251d72aa {
  margin-top: 20rpx;
}
.order-details .writeOff .rules .item .rulesTitle.data-v-251d72aa {
  font-size: 28rpx;
  color: #282828;
}
.order-details .writeOff .rules .item .rulesTitle .iconfont.data-v-251d72aa {
  font-size: 30rpx;
  color: #333;
  margin-right: 8rpx;
  margin-top: 5rpx;
}
.order-details .writeOff .rules .item .info.data-v-251d72aa {
  font-size: 28rpx;
  color: #999;
  margin-top: 7rpx;
}
.order-details .writeOff .rules .item .info .time.data-v-251d72aa {
  margin-left: 20rpx;
}
.order-details .map.data-v-251d72aa {
  height: 86rpx;
  font-size: 30rpx;
  color: #282828;
  line-height: 86rpx;
  border-bottom: 1px solid #f0f0f0;
  margin-top: 15rpx;
  background-color: #fff;
  padding: 0 24rpx;
}
.order-details .map .place.data-v-251d72aa {
  font-size: 26rpx;
  width: 176rpx;
  height: 50rpx;
  border-radius: 25rpx;
  line-height: 50rpx;
  text-align: center;
}
.order-details .map .place .iconfont.data-v-251d72aa {
  font-size: 27rpx;
  height: 27rpx;
  line-height: 27rpx;
  margin: 2rpx 3rpx 0 0;
}
.order-details .address .name .iconfont.data-v-251d72aa {
  font-size: 34rpx;
  margin-left: 10rpx;
}
.refund.data-v-251d72aa {
  padding: 0 !important;
  margin-top: 15rpx;
  background-color: #fff;
}
.refund .title.data-v-251d72aa {
  display: flex;
  align-items: center;
  font-size: 30rpx;
  color: #333;
  height: 86rpx;
  border-bottom: 1px solid #f5f5f5;
  font-weight: 400;
  padding: 0 24rpx;
}
.refund .title image.data-v-251d72aa {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}
.refund .con.data-v-251d72aa {
  font-size: 25rpx;
  color: #666666;
  padding: 30rpx 24rpx;
}


.qs-btn {
	width: auto;
	height: 60rpx;
	text-align: center;
	line-height: 60rpx;
	border-radius: 50rpx;
	color: #fff;
	font-size: 27rpx;
	padding: 0 3%;
	color: #aaa;
	border: 1px solid #ddd;
	margin-right: 20rpx;
}

