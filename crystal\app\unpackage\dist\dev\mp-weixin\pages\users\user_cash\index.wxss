@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page {
  background-color: #fff !important;
}
.cash-withdrawal .nav {
  height: 130rpx;
  box-shadow: 0 10rpx 10rpx #f8f8f8;
}
.cash-withdrawal .nav .item {
  font-size: 26rpx;
  flex: 1;
  text-align: center;
}
.cash-withdrawal .nav .item ~ .item {
  border-left: 1px solid #f0f0f0;
}
.cash-withdrawal .nav .item .iconfont {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx solid #c9ab79;
  text-align: center;
  line-height: 37rpx;
  margin: 0 auto 6rpx auto;
  font-size: 22rpx;
  box-sizing: border-box;
}
.cash-withdrawal .nav .item .iconfont.on {
  background-color: #c9ab79;
  color: #fff;
  border-color: #c9ab79;
}
.cash-withdrawal .nav .item .line {
  width: 2rpx;
  height: 20rpx;
  margin: 0 auto;
  transition: height 0.3s;
}
.cash-withdrawal .nav .item .line.on {
  height: 39rpx;
}
.cash-withdrawal .wrapper .list {
  padding: 0 30rpx;
}
.cash-withdrawal .wrapper .list .item {
  border-bottom: 1rpx solid #eee;
  min-height: 28rpx;
  font-size: 30rpx;
  color: #333;
  padding: 39rpx 0;
}
.cash-withdrawal .wrapper .list .item .name {
  width: 130rpx;
}
.cash-withdrawal .wrapper .list .item .input {
  width: 505rpx;
}
.cash-withdrawal .wrapper .list .item .input .placeholder {
  color: #bbb;
}
.cash-withdrawal .wrapper .list .item .picEwm, .cash-withdrawal .wrapper .list .item .pictrue {
  width: 140rpx;
  height: 140rpx;
  border-radius: 3rpx;
  position: relative;
  margin-right: 23rpx;
}
.cash-withdrawal .wrapper .list .item .picEwm image {
  width: 100%;
  height: 100%;
  border-radius: 3rpx;
}
.cash-withdrawal .wrapper .list .item .picEwm .icon-guanbi1 {
  position: absolute;
  right: -14rpx;
  top: -16rpx;
  font-size: 40rpx;
}
.cash-withdrawal .wrapper .list .item .pictrue {
  border: 1px solid #dddddd;
  font-size: 22rpx;
  color: #BBBBBB;
}
.cash-withdrawal .wrapper .list .item .pictrue .icon-icon25201 {
  font-size: 47rpx;
  color: #DDDDDD;
  margin-bottom: 3px;
}
.cash-withdrawal .wrapper .list .tip {
  font-size: 26rpx;
  color: #999;
  margin-top: 25rpx;
}
.cash-withdrawal .wrapper .list .bnt {
  font-size: 32rpx;
  color: #fff;
  width: 690rpx;
  height: 90rpx;
  text-align: center;
  border-radius: 50rpx;
  line-height: 90rpx;
  margin: 64rpx auto;
}
.cash-withdrawal .wrapper .list .tip2 {
  font-size: 26rpx;
  color: #999;
  text-align: center;
  margin: 44rpx 0 20rpx 0;
}
.cash-withdrawal .wrapper .list .value {
  height: 135rpx;
  line-height: 135rpx;
  border-bottom: 1rpx solid #eee;
  width: 690rpx;
  margin: 0 auto;
}
.cash-withdrawal .wrapper .list .value input {
  font-size: 80rpx;
  color: #282828;
  height: 135rpx;
  text-align: center;
}
.cash-withdrawal .wrapper .list .value .placeholder2 {
  color: #bbb;
}
.price {
  color: #c9ab79;
}

