{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\storeAttr\\index.vue?vue&type=template&id=aaf27ec6&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\storeAttr\\index.vue", "mtime": 1753666157924}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\n<div class=\"divBox\">\n  <el-card class=\"box-card\">\n    <div slot=\"header\" class=\"clearfix\">\n      <div class=\"container\">\n        <el-form inline size=\"small\">\n          <el-form-item label=\"规格搜索：\">\n            <el-input v-model=\"tableFrom.keywords\" placeholder=\"请输入商品规格\" class=\"selWidth\" clearable>\n              <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"seachList\" />\n            </el-input>\n          </el-form-item>\n        </el-form>\n      </div>\n      <div class=\"acea-row\">\n        <el-button size=\"small\" type=\"primary\" @click=\"add\" v-hasPermi=\"['admin:product:rule:save']\">添加商品规格</el-button>\n        <el-button size=\"small\" @click=\"handleDeleteAll\" v-hasPermi=\"['admin:product:rule:delete']\">批量删除</el-button>\n      </div>\n    </div>\n    <el-table\n      ref=\"table\"\n      v-loading=\"listLoading\"\n      :data=\"tableData.data\"\n      style=\"width: 100%\"\n      size=\"mini\"\n      highlight-current-row\n      @selection-change=\"handleSelectionChange\"\n    >\n      <el-table-column\n        type=\"selection\"\n        width=\"55\"\n      />\n      <el-table-column\n        prop=\"id\"\n        label=\"ID\"\n        min-width=\"60\"\n      />\n      <el-table-column\n        prop=\"ruleName\"\n        label=\"规格名称\"\n        min-width=\"150\"\n      />\n      <el-table-column\n        label=\"商品规格\"\n        min-width=\"150\"\n      >\n        <template slot-scope=\"scope\">\n          <span v-for=\"(item, index) in scope.row.ruleValue\" :key=\"index\" class=\"mr10\" v-text=\"item.value\" />\n        </template>\n      </el-table-column>\n      <el-table-column\n        label=\"商品属性\"\n        min-width=\"300\"\n      >\n        <template slot-scope=\"scope\">\n          <div v-for=\"(item, index) in scope.row.ruleValue\" :key=\"index\" v-text=\"item.detail.join(',')\" />\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" min-width=\"120\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <el-button type=\"text\" size=\"small\" @click=\"onEdit(scope.row)\" class=\"mr10\" v-hasPermi=\"['admin:product:rule:update','admin:product:rule:info']\">编辑</el-button>\n          <el-button type=\"text\" size=\"small\" @click=\"handleDelete(scope.row.id, scope.$index)\" v-hasPermi=\"['admin:product:rule:delete']\">删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n    <div class=\"block\">\n      <el-pagination\n        :page-sizes=\"[20, 40, 60, 80]\"\n        :page-size=\"tableFrom.limit\"\n        :current-page=\"tableFrom.page\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        :total=\"tableData.total\"\n        @size-change=\"handleSizeChange\"\n        @current-change=\"pageChange\"\n      />\n    </div>\n  </el-card>\n</div>\n", null]}