(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/users/user_address/index"],{"1a49":function(t,i,e){},"294a":function(t,i,e){"use strict";(function(t){Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var n=e("5904"),s=e("fdf2"),r=e("cda4"),a=e("8f59"),c=(getApp(),{components:{authorize:function(){Promise.all([e.e("common/vendor"),e.e("components/Authorize")]).then(function(){return resolve(e("cf49"))}.bind(null,e)).catch(e.oe)},home:function(){e.e("components/home/<USER>").then(function(){return resolve(e("bc9e"))}.bind(null,e)).catch(e.oe)}},data:function(){return{regionDval:["浙江省","杭州市","滨江区"],cartId:"",pinkId:0,couponId:0,id:0,userAddress:{isDefault:!1},region:["省","市","区"],valueRegion:[0,0,0],isAuto:!1,isShowAuth:!1,district:[],multiArray:[],multiIndex:[0,0,0],cityId:0,defaultRegion:["广东省","广州市","番禺区"],defaultRegionCode:"440113",bargain:!1,combination:!1,secKill:!1}},computed:(0,a.mapGetters)(["isLogin"]),watch:{isLogin:{handler:function(t,i){t&&(this.getUserAddress(),this.getCityList())},deep:!0}},onLoad:function(i){this.isLogin?(this.preOrderNo=i.preOrderNo||0,this.id=i.id||0,t.setNavigationBarTitle({title:i.id?"修改地址":"添加地址"}),this.getUserAddress(),this.$Cache.has("cityList")?(this.district=this.$Cache.getItem("cityList"),this.initialize()):this.getCityList()):(0,r.toLogin)()},methods:{getCityList:function(){var t=this,i=this;(0,s.getCity)().then((function(e){t.district=e.data;t.$Cache.setItem({name:"cityList",value:e.data,expires:6048e5}),i.initialize()}))},initialize:function(){var t=[],i=[],e=[];if(this.district.length){var n=this.district[0].child||[],s=n.length&&n[0].child||[];this.district.forEach((function(i){t.push(i.name)})),n.forEach((function(t){i.push(t.name)})),s.forEach((function(t){e.push(t.name)})),this.multiArray=[t,i,e]}},bindRegionChange:function(t){var i=this.multiIndex,e=this.district[i[0]]||{child:[]},n=e.child[i[1]]||{cityId:0},s=this.multiArray,r=t.detail.value;this.region=[s[0][r[0]],s[1][r[1]],s[2][r[2]]],this.cityId=n.cityId,this.valueRegion=[0,0,0],this.initialize()},bindMultiPickerColumnChange:function(t){var i=t.detail.column,e=t.detail.value,n=this.district[e]||{child:[]},s=this.multiArray,r=this.multiIndex;switch(r[i]=e,i){case 0:var a=n.child[0]||{child:[]};s[1]=n.child.map((function(t){return t.name})),s[2]=a.child.map((function(t){return t.name}));break;case 1:var c=this.district[r[0]].child[r[1]].child||[];s[2]=c.map((function(t){return t.name}));break;case 2:break}this.$set(this.multiArray,0,s[0]),this.$set(this.multiArray,1,s[1]),this.$set(this.multiArray,2,s[2]),this.multiIndex=r},onLoadFun:function(){this.getUserAddress()},authColse:function(t){this.isShowAuth=t},toggleTab:function(t){this.$refs[t].show()},onConfirm:function(t){this.region=t.checkArr[0]+"-"+t.checkArr[1]+"-"+t.checkArr[2]},getUserAddress:function(){if(!this.id)return!1;var t=this;(0,n.getAddressDetail)(this.id).then((function(i){var e=[i.data.province,i.data.city,i.data.district];t.$set(t,"userAddress",i.data),t.$set(t,"region",e),t.city_id=i.data.cityId}))},chooseLocation:function(){var i=this;t.chooseLocation({success:function(t){i.$set(i.userAddress,"detail",t.address.replace(/.+?(省|市|自治区|自治州|县|区)/g,""))}})},getWxAddress:function(){var i=this;t.authorize({scope:"scope.address",success:function(e){t.chooseAddress({success:function(e){var s={};s.province=e.provinceName,s.city=e.cityName,s.district=e.countyName,s.cityId=0,(0,n.editAddress)({address:s,isDefault:1,realName:e.userName,postCode:e.postalCode,phone:e.telNumber,detail:e.detailInfo,id:0}).then((function(e){return setTimeout((function(){if(i.cartId){var n=i.cartId,s=i.pinkId,r=i.couponId;i.cartId="",i.pinkId="",i.couponId="",t.navigateTo({url:"/pages/users/order_confirm/index?cartId="+n+"&addressId="+(i.id?i.id:e.data.id)+"&pinkId="+s+"&couponId="+r+"&secKill="+i.secKill+"&combination="+i.combination+"&bargain="+i.bargain})}else t.navigateBack({delta:1})}),1e3),i.$util.Tips({title:"添加成功",icon:"success"})})).catch((function(t){return i.$util.Tips({title:t})}))},fail:function(t){if("chooseAddress:cancel"==t.errMsg)return i.$util.Tips({title:"取消选择"})}})},fail:function(e){t.showModal({title:"您已拒绝导入微信地址权限",content:"是否进入权限管理，调整授权？",success:function(e){if(e.confirm)t.openSetting({success:function(t){}});else if(e.cancel)return i.$util.Tips({title:"已取消！"})}})}})},getAddress:function(){var i=this,e=this;e.$wechat.openAddress().then((function(s){(0,n.editAddress)({id:i.id,realName:s.userName,phone:s.telNumber,address:{province:s.provinceName,city:s.cityName,district:s.countryName,cityId:0},detail:s.detailInfo,isDefault:1,postCode:s.postalCode}).then((function(){setTimeout((function(){if(e.cartId){var i=e.cartId,n=e.pinkId,s=e.couponId;e.cartId="",e.pinkId="",e.couponId="",t.navigateTo({url:"/pages/users/order_confirm/index?cartId="+i+"&addressId="+(e.id?e.id:res.data.id)+"&pinkId="+n+"&couponId="+s+"&secKill="+e.secKill+"&combination="+e.combination+"&bargain="+e.bargain})}else t.navigateTo({url:"/pages/users/user_address_list/index"})}),1e3),e.$util.Tips({title:"添加成功",icon:"success"})})).catch((function(t){return e.$util.Tips({title:t||"添加失败"})}))})).catch((function(t){console.log(t)}))},formSubmit:function(i){var e=this,s=i.detail.value;if(!s.realName)return e.$util.Tips({title:"请填写收货人姓名"});if(!s.phone)return e.$util.Tips({title:"请填写联系电话"});if(!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(s.phone))return e.$util.Tips({title:"请输入正确的手机号码"});if("省-市-区"==e.region)return e.$util.Tips({title:"请选择所在地区"});if(!s.detail)return e.$util.Tips({title:"请填写详细地址"});s.id=e.id;var r=e.region;s.address={province:r[0],city:r[1],district:r[2],cityId:e.cityId},s.isDefault=e.userAddress.isDefault,t.showLoading({title:"保存中",mask:!0}),(0,n.editAddress)(s).then((function(i){e.id?e.$util.Tips({title:"修改成功",icon:"success"}):e.$util.Tips({title:"添加成功",icon:"success"}),setTimeout((function(){if(!(e.preOrderNo>0))return t.navigateBack({delta:1});t.redirectTo({url:"/pages/users/order_confirm/index?preOrderNo="+e.preOrderNo+"&addressId="+(e.id?e.id:i.data.id)})}),1e3)})).catch((function(t){return e.$util.Tips({title:t})}))},ChangeIsDefault:function(t){this.$set(this.userAddress,"isDefault",!this.userAddress.isDefault)}}});i.default=c}).call(this,e("df3c")["default"])},"2deb":function(t,i,e){"use strict";e.d(i,"b",(function(){return n})),e.d(i,"c",(function(){return s})),e.d(i,"a",(function(){}));var n=function(){var t=this.$createElement;this._self._c},s=[]},a9ba:function(t,i,e){"use strict";e.r(i);var n=e("2deb"),s=e("e97a");for(var r in s)["default"].indexOf(r)<0&&function(t){e.d(i,t,(function(){return s[t]}))}(r);e("d998");var a=e("828b"),c=Object(a["a"])(s["default"],n["b"],n["c"],!1,null,"50b24c1a",null,!1,n["a"],void 0);i["default"]=c.exports},d998:function(t,i,e){"use strict";var n=e("1a49"),s=e.n(n);s.a},e97a:function(t,i,e){"use strict";e.r(i);var n=e("294a"),s=e.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){e.d(i,t,(function(){return n[t]}))}(r);i["default"]=s.a},fada:function(t,i,e){"use strict";(function(t,i){var n=e("47a9");e("5c2d");n(e("3240"));var s=n(e("a9ba"));t.__webpack_require_UNI_MP_PLUGIN__=e,i(s.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])}},[["fada","common/runtime","common/vendor"]]]);