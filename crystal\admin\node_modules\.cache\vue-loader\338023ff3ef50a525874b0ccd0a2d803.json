{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\FormGenerator\\index\\RightPanel.vue?vue&type=template&id=17795e15&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\FormGenerator\\index\\RightPanel.vue", "mtime": 1753666157771}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\n<div class=\"right-board\">\n  <el-tabs v-model=\"currentTab\" class=\"center-tabs\">\n    <el-tab-pane label=\"组件属性\" name=\"field\" />\n    <el-tab-pane label=\"表单属性\" name=\"form\" />\n  </el-tabs>\n  <div class=\"field-box\">\n    <!-- <a class=\"document-link\" target=\"_blank\" :href=\"documentLink\" title=\"查看组件文档\">\n      <i class=\"el-icon-link\" />\n    </a> -->\n    <el-scrollbar class=\"right-scrollbar\">\n      <!-- 组件属性 -->\n      <el-form v-show=\"currentTab==='field' && showField\" size=\"small\" label-width=\"90px\">\n        <el-form-item v-if=\"activeData.__config__.changeTag\" label=\"组件类型\">\n          <el-select\n            v-model=\"activeData.__config__.tagIcon\"\n            placeholder=\"请选择组件类型\"\n            :style=\"{width: '100%'}\"\n            @change=\"tagChange\"\n          >\n            <el-option-group v-for=\"group in tagList\" :key=\"group.label\" :label=\"group.label\">\n              <el-option\n                v-for=\"item in group.options\"\n                :key=\"item.__config__.label\"\n                :label=\"item.__config__.label\"\n                :value=\"item.__config__.tagIcon\"\n              >\n                <svg-icon class=\"node-icon\" :icon-class=\"item.__config__.tagIcon\" />\n                <span> {{ item.__config__.label }}</span>\n              </el-option>\n            </el-option-group>\n          </el-select>\n        </el-form-item>\n        <el-form-item v-if=\"activeData.__vModel__!==undefined\" label=\"字段名\">\n          <el-input v-model=\"activeData.__vModel__\" placeholder=\"请输入字段名（v-model）\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData.__config__.componentName!==undefined\" label=\"组件名\">\n          {{ activeData.__config__.componentName }}\n        </el-form-item>\n        <el-form-item v-if=\"activeData.__config__.label!==undefined\" label=\"标题\">\n          <el-input v-model=\"activeData.__config__.label\" placeholder=\"请输入标题\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData.placeholder!==undefined\" label=\"占位提示\">\n          <el-input v-model=\"activeData.placeholder\" placeholder=\"请输入占位提示\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData['start-placeholder']!==undefined\" label=\"开始占位\">\n          <el-input v-model=\"activeData['start-placeholder']\" placeholder=\"请输入占位提示\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData['end-placeholder']!==undefined\" label=\"结束占位\">\n          <el-input v-model=\"activeData['end-placeholder']\" placeholder=\"请输入占位提示\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData.__config__.span!==undefined\" label=\"表单栅格\">\n          <el-slider v-model=\"activeData.__config__.span\" :max=\"24\" :min=\"1\" :marks=\"{12:''}\" @change=\"spanChange\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData.__config__.layout==='rowFormItem'\" label=\"栅格间隔\">\n          <el-input-number v-model=\"activeData.gutter\" :min=\"0\" placeholder=\"栅格间隔\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData.__config__.layout==='rowFormItem'\" label=\"布局模式\">\n          <el-radio-group v-model=\"activeData.type\">\n            <el-radio-button label=\"default\" />\n            <el-radio-button label=\"flex\" />\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item v-if=\"activeData.justify!==undefined&&activeData.type==='flex'\" label=\"水平排列\">\n          <el-select v-model=\"activeData.justify\" placeholder=\"请选择水平排列\" :style=\"{width: '100%'}\">\n            <el-option\n              v-for=\"(item, index) in justifyOptions\"\n              :key=\"index\"\n              :label=\"item.label\"\n              :value=\"item.value\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item v-if=\"activeData.align!==undefined&&activeData.type==='flex'\" label=\"垂直排列\">\n          <el-radio-group v-model=\"activeData.align\">\n            <el-radio-button label=\"top\" />\n            <el-radio-button label=\"middle\" />\n            <el-radio-button label=\"bottom\" />\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item v-if=\"activeData.__config__.labelWidth!==undefined\" label=\"标签宽度\">\n          <el-input v-model.number=\"activeData.__config__.labelWidth\" type=\"number\" placeholder=\"请输入标签宽度\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData.style&&activeData.style.width!==undefined\" label=\"组件宽度\">\n          <el-input v-model=\"activeData.style.width\" placeholder=\"请输入组件宽度\" clearable />\n        </el-form-item>\n        <el-form-item v-if=\"activeData.__vModel__!==undefined\" label=\"默认值\">\n          <el-input\n            :value=\"setDefaultValue(activeData.__config__.defaultValue)\"\n            placeholder=\"请输入默认值\"\n            @input=\"onDefaultValueInput\"\n          />\n        </el-form-item>\n        <el-form-item v-if=\"activeData.__config__.tag==='el-checkbox-group'\" label=\"至少应选\">\n          <el-input-number\n            :value=\"activeData.min\"\n            :min=\"0\"\n            placeholder=\"至少应选\"\n            @input=\"$set(activeData, 'min', $event?$event:undefined)\"\n          />\n        </el-form-item>\n        <el-form-item v-if=\"activeData.__config__.tag==='el-checkbox-group'\" label=\"最多可选\">\n          <el-input-number\n            :value=\"activeData.max\"\n            :min=\"0\"\n            placeholder=\"最多可选\"\n            @input=\"$set(activeData, 'max', $event?$event:undefined)\"\n          />\n        </el-form-item>\n        <el-form-item v-if=\"activeData.__slot__&&activeData.__slot__.prepend!==undefined\" label=\"前缀\">\n          <el-input v-model=\"activeData.__slot__.prepend\" placeholder=\"请输入前缀\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData.__slot__&&activeData.__slot__.append!==undefined\" label=\"后缀\">\n          <el-input v-model=\"activeData.__slot__.append\" placeholder=\"请输入后缀\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData['prefix-icon']!==undefined\" label=\"前图标\">\n          <el-input v-model=\"activeData['prefix-icon']\" placeholder=\"请输入前图标名称\">\n            <el-button slot=\"append\" icon=\"el-icon-thumb\" @click=\"openIconsDialog('prefix-icon')\">\n              选择\n            </el-button>\n          </el-input>\n        </el-form-item>\n        <el-form-item v-if=\"activeData['suffix-icon'] !== undefined\" label=\"后图标\">\n          <el-input v-model=\"activeData['suffix-icon']\" placeholder=\"请输入后图标名称\">\n            <el-button slot=\"append\" icon=\"el-icon-thumb\" @click=\"openIconsDialog('suffix-icon')\">\n              选择\n            </el-button>\n          </el-input>\n        </el-form-item>\n        <el-form-item\n          v-if=\"activeData['icon']!==undefined && activeData.__config__.tag === 'el-button'\"\n          label=\"按钮图标\"\n        >\n          <el-input v-model=\"activeData['icon']\" placeholder=\"请输入按钮图标名称\">\n            <el-button slot=\"append\" icon=\"el-icon-thumb\" @click=\"openIconsDialog('icon')\">\n              选择\n            </el-button>\n          </el-input>\n        </el-form-item>\n        <el-form-item v-if=\"activeData.__config__.tag === 'el-cascader'\" label=\"选项分隔符\">\n          <el-input v-model=\"activeData.separator\" placeholder=\"请输入选项分隔符\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData.autosize !== undefined\" label=\"最小行数\">\n          <el-input-number v-model=\"activeData.autosize.minRows\" :min=\"1\" placeholder=\"最小行数\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData.autosize !== undefined\" label=\"最大行数\">\n          <el-input-number v-model=\"activeData.autosize.maxRows\" :min=\"1\" placeholder=\"最大行数\" />\n        </el-form-item>\n        <el-form-item v-if=\"isShowMin\" label=\"最小值\">\n          <el-input-number v-model=\"activeData.min\" placeholder=\"最小值\" />\n        </el-form-item>\n        <el-form-item v-if=\"isShowMax\" label=\"最大值\">\n          <el-input-number v-model=\"activeData.max\" placeholder=\"最大值\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData.height!==undefined\" label=\"组件高度\">\n          <el-input-number v-model=\"activeData.height\" placeholder=\"高度\" @input=\"changeRenderKey\" />\n        </el-form-item>\n        <el-form-item v-if=\"isShowStep\" label=\"步长\">\n          <el-input-number v-model=\"activeData.step\" placeholder=\"步数\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData.__config__.tag === 'el-input-number'\" label=\"精度\">\n          <el-input-number v-model=\"activeData.precision\" :min=\"0\" placeholder=\"精度\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData.__config__.tag === 'el-input-number'\" label=\"按钮位置\">\n          <el-radio-group v-model=\"activeData['controls-position']\">\n            <el-radio-button label=\"\">\n              默认\n            </el-radio-button>\n            <el-radio-button label=\"right\">\n              右侧\n            </el-radio-button>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item v-if=\"activeData.maxlength !== undefined\" label=\"最多输入\">\n          <el-input v-model=\"activeData.maxlength\" placeholder=\"请输入字符长度\">\n            <template slot=\"append\">\n              个字符\n            </template>\n          </el-input>\n        </el-form-item>\n        <el-form-item v-if=\"activeData['active-text'] !== undefined\" label=\"开启提示\">\n          <el-input v-model=\"activeData['active-text']\" placeholder=\"请输入开启提示\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData['inactive-text'] !== undefined\" label=\"关闭提示\">\n          <el-input v-model=\"activeData['inactive-text']\" placeholder=\"请输入关闭提示\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData['active-value'] !== undefined\" label=\"开启值\">\n          <el-input\n            :value=\"setDefaultValue(activeData['active-value'])\"\n            placeholder=\"请输入开启值\"\n            @input=\"onSwitchValueInput($event, 'active-value')\"\n          />\n        </el-form-item>\n        <el-form-item v-if=\"activeData['inactive-value'] !== undefined\" label=\"关闭值\">\n          <el-input\n            :value=\"setDefaultValue(activeData['inactive-value'])\"\n            placeholder=\"请输入关闭值\"\n            @input=\"onSwitchValueInput($event, 'inactive-value')\"\n          />\n        </el-form-item>\n        <el-form-item\n          v-if=\"activeData.type !== undefined && 'el-date-picker' === activeData.__config__.tag\"\n          label=\"时间类型\"\n        >\n          <el-select\n            v-model=\"activeData.type\"\n            placeholder=\"请选择时间类型\"\n            :style=\"{ width: '100%' }\"\n            @change=\"dateTypeChange\"\n          >\n            <el-option\n              v-for=\"(item, index) in dateOptions\"\n              :key=\"index\"\n              :label=\"item.label\"\n              :value=\"item.value\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item v-if=\"activeData.name !== undefined\" label=\"文件字段名\">\n          <el-input v-model=\"activeData.name\" placeholder=\"请输入上传文件字段名\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData.accept === 'image'\" label=\"文件类型\">\n          <span>图片</span>\n        </el-form-item>\n        <el-form-item v-if=\"activeData.accept !== undefined && activeData.accept !== 'image'\" label=\"文件类型\">\n          <el-select\n            v-model=\"activeData.accept\"\n            placeholder=\"请选择文件类型\"\n            :style=\"{ width: '100%' }\"\n            clearable\n          >\n            <el-option label=\"视频\" value=\"video/*\" />\n            <el-option label=\"音频\" value=\"audio/*\" />\n            <el-option label=\"excel\" value=\".xls,.xlsx\" />\n            <el-option label=\"word\" value=\".doc,.docx\" />\n            <el-option label=\"pdf\" value=\".pdf\" />\n            <el-option label=\"txt\" value=\".txt\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item v-if=\"activeData.__config__.fileSize !== undefined\" label=\"文件大小\">\n          <el-input v-model.number=\"activeData.__config__.fileSize\" placeholder=\"请输入文件大小\">\n            <el-select slot=\"append\" v-model=\"activeData.__config__.sizeUnit\" :style=\"{ width: '66px' }\">\n              <el-option label=\"KB\" value=\"KB\" />\n              <el-option label=\"MB\" value=\"MB\" />\n              <el-option label=\"GB\" value=\"GB\" />\n            </el-select>\n          </el-input>\n        </el-form-item>\n        <el-form-item v-if=\"activeData.action !== undefined\" label=\"上传地址\">\n          <el-input v-model=\"activeData.action\" placeholder=\"请输入上传地址\" clearable />\n        </el-form-item>\n        <el-form-item v-if=\"activeData['list-type'] !== undefined\" label=\"列表类型\">\n          <el-radio-group v-model=\"activeData['list-type']\" size=\"small\">\n            <el-radio-button label=\"text\">\n              text\n            </el-radio-button>\n            <el-radio-button label=\"picture\">\n              picture\n            </el-radio-button>\n            <el-radio-button label=\"picture-card\">\n              picture-card\n            </el-radio-button>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item\n          v-if=\"activeData.type !== undefined && activeData.__config__.tag === 'el-button'\"\n          label=\"按钮类型\"\n        >\n          <el-select v-model=\"activeData.type\" :style=\"{ width: '100%' }\">\n            <el-option label=\"primary\" value=\"primary\" />\n            <el-option label=\"success\" value=\"success\" />\n            <el-option label=\"warning\" value=\"warning\" />\n            <el-option label=\"danger\" value=\"danger\" />\n            <el-option label=\"info\" value=\"info\" />\n            <el-option label=\"text\" value=\"text\" />\n          </el-select>\n        </el-form-item>\n        <el-form-item\n          v-if=\"activeData.__config__.buttonText !== undefined\"\n          v-show=\"'picture-card' !== activeData['list-type']\"\n          label=\"按钮文字\"\n        >\n          <el-input v-model=\"activeData.__config__.buttonText\" placeholder=\"请输入按钮文字\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData['range-separator'] !== undefined\" label=\"分隔符\">\n          <el-input v-model=\"activeData['range-separator']\" placeholder=\"请输入分隔符\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData['picker-options'] !== undefined\" label=\"时间段\">\n          <el-input\n            v-model=\"activeData['picker-options'].selectableRange\"\n            placeholder=\"请输入时间段\"\n          />\n        </el-form-item>\n        <el-form-item v-if=\"activeData.format !== undefined\" label=\"时间格式\">\n          <el-input\n            :value=\"activeData.format\"\n            placeholder=\"请输入时间格式\"\n            @input=\"setTimeValue($event)\"\n          />\n        </el-form-item>\n        <template v-if=\"['el-checkbox-group', 'el-radio-group', 'el-select'].indexOf(activeData.__config__.tag) > -1\">\n          <el-divider>选项</el-divider>\n          <draggable\n            :list=\"activeData.__slot__.options\"\n            :animation=\"340\"\n            group=\"selectItem\"\n            handle=\".option-drag\"\n          >\n            <div v-for=\"(item, index) in activeData.__slot__.options\" :key=\"index\" class=\"select-item\">\n              <div class=\"select-line-icon option-drag\">\n                <i class=\"el-icon-s-operation\" />\n              </div>\n              <el-input v-model=\"item.label\" placeholder=\"选项名\" size=\"small\" />\n              <el-input\n                placeholder=\"选项值\"\n                size=\"small\"\n                :value=\"item.value\"\n                @input=\"setOptionValue(item, $event)\"\n              />\n              <div class=\"close-btn select-line-icon\" @click=\"activeData.__slot__.options.splice(index, 1)\">\n                <i class=\"el-icon-remove-outline\" />\n              </div>\n            </div>\n          </draggable>\n          <div style=\"margin-left: 20px;\">\n            <el-button\n              style=\"padding-bottom: 0\"\n              icon=\"el-icon-circle-plus-outline\"\n              type=\"text\"\n              @click=\"addSelectItem\"\n            >\n              添加选项\n            </el-button>\n          </div>\n          <el-divider />\n        </template>\n\n        <template v-if=\"['el-cascader'].indexOf(activeData.__config__.tag) > -1\">\n          <el-divider>选项</el-divider>\n          <el-form-item label=\"数据类型\">\n            <el-radio-group v-model=\"activeData.__config__.dataType\" size=\"small\">\n              <el-radio-button label=\"dynamic\">\n                动态数据\n              </el-radio-button>\n              <el-radio-button label=\"static\">\n                静态数据\n              </el-radio-button>\n            </el-radio-group>\n          </el-form-item>\n\n          <template v-if=\"activeData.__config__.dataType === 'dynamic'\">\n            <el-form-item label=\"标签键名\">\n              <el-input v-model=\"activeData.props.props.label\" placeholder=\"请输入标签键名\" />\n            </el-form-item>\n            <el-form-item label=\"值键名\">\n              <el-input v-model=\"activeData.props.props.value\" placeholder=\"请输入值键名\" />\n            </el-form-item>\n            <el-form-item label=\"子级键名\">\n              <el-input v-model=\"activeData.props.props.children\" placeholder=\"请输入子级键名\" />\n            </el-form-item>\n          </template>\n\n          <!-- 级联选择静态树 -->\n          <el-tree\n            v-if=\"activeData.__config__.dataType === 'static'\"\n            draggable\n            :data=\"activeData.options\"\n            node-key=\"id\"\n            :expand-on-click-node=\"false\"\n            :render-content=\"renderContent\"\n          />\n          <div v-if=\"activeData.__config__.dataType === 'static'\" style=\"margin-left: 20px\">\n            <el-button\n              style=\"padding-bottom: 0\"\n              icon=\"el-icon-circle-plus-outline\"\n              type=\"text\"\n              @click=\"addTreeItem\"\n            >\n              添加父级\n            </el-button>\n          </div>\n          <el-divider />\n        </template>\n\n        <el-form-item v-if=\"activeData.__config__.optionType !== undefined\" label=\"选项样式\">\n          <el-radio-group v-model=\"activeData.__config__.optionType\">\n            <el-radio-button label=\"default\">\n              默认\n            </el-radio-button>\n            <el-radio-button label=\"button\">\n              按钮\n            </el-radio-button>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item v-if=\"activeData['active-color'] !== undefined\" label=\"开启颜色\">\n          <el-color-picker v-model=\"activeData['active-color']\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData['inactive-color'] !== undefined\" label=\"关闭颜色\">\n          <el-color-picker v-model=\"activeData['inactive-color']\" />\n        </el-form-item>\n\n        <el-form-item\n          v-if=\"activeData.__config__.showLabel !== undefined\n            && activeData.__config__.labelWidth !== undefined\"\n          label=\"显示标签\"\n        >\n          <el-switch v-model=\"activeData.__config__.showLabel\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData.branding !== undefined\" label=\"品牌烙印\">\n          <el-switch v-model=\"activeData.branding\" @input=\"changeRenderKey\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData['allow-half'] !== undefined\" label=\"允许半选\">\n          <el-switch v-model=\"activeData['allow-half']\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData['show-text'] !== undefined\" label=\"辅助文字\">\n          <el-switch v-model=\"activeData['show-text']\" @change=\"rateTextChange\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData['show-score'] !== undefined\" label=\"显示分数\">\n          <el-switch v-model=\"activeData['show-score']\" @change=\"rateScoreChange\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData['show-stops'] !== undefined\" label=\"显示间断点\">\n          <el-switch v-model=\"activeData['show-stops']\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData.range !== undefined\" label=\"范围选择\">\n          <el-switch v-model=\"activeData.range\" @change=\"rangeChange\" />\n        </el-form-item>\n        <el-form-item\n          v-if=\"activeData.__config__.border !== undefined && activeData.__config__.optionType === 'default'\"\n          label=\"是否带边框\"\n        >\n          <el-switch v-model=\"activeData.__config__.border\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData.__config__.tag === 'el-color-picker'\" label=\"颜色格式\">\n          <el-select\n            v-model=\"activeData['color-format']\"\n            placeholder=\"请选择颜色格式\"\n            :style=\"{ width: '100%' }\"\n            clearable\n            @change=\"colorFormatChange\"\n          >\n            <el-option\n              v-for=\"(item, index) in colorFormatOptions\"\n              :key=\"index\"\n              :label=\"item.label\"\n              :value=\"item.value\"\n            />\n          </el-select>\n        </el-form-item>\n        <el-form-item\n          v-if=\"activeData.size !== undefined &&\n            (activeData.__config__.optionType === 'button' ||\n            activeData.__config__.border ||\n            activeData.__config__.tag === 'el-color-picker' ||\n            activeData.__config__.tag === 'el-button')\"\n          label=\"组件尺寸\"\n        >\n          <el-radio-group v-model=\"activeData.size\">\n            <el-radio-button label=\"medium\">\n              中等\n            </el-radio-button>\n            <el-radio-button label=\"small\">\n              较小\n            </el-radio-button>\n            <el-radio-button label=\"mini\">\n              迷你\n            </el-radio-button>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item v-if=\"activeData['show-word-limit'] !== undefined\" label=\"输入统计\">\n          <el-switch v-model=\"activeData['show-word-limit']\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData.__config__.tag === 'el-input-number'\" label=\"严格步数\">\n          <el-switch v-model=\"activeData['step-strictly']\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData.__config__.tag === 'el-cascader'\" label=\"是否多选\">\n          <el-switch v-model=\"activeData.props.props.multiple\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData.__config__.tag === 'el-cascader'\" label=\"展示全路径\">\n          <el-switch v-model=\"activeData['show-all-levels']\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData.__config__.tag === 'el-cascader'\" label=\"可否筛选\">\n          <el-switch v-model=\"activeData.filterable\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData.clearable !== undefined\" label=\"能否清空\">\n          <el-switch v-model=\"activeData.clearable\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData.__config__.showTip !== undefined\" label=\"显示提示\">\n          <el-switch v-model=\"activeData.__config__.showTip\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData.__config__.tag === 'el-upload' || activeData.__config__.tag === 'self-upload'\" label=\"多选文件\">\n          <el-switch v-model=\"activeData.multiple\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData['auto-upload'] !== undefined\" label=\"自动上传\">\n          <el-switch v-model=\"activeData['auto-upload']\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData.readonly !== undefined\" label=\"是否只读\">\n          <el-switch v-model=\"activeData.readonly\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData.disabled !== undefined\" label=\"是否禁用\">\n          <el-switch v-model=\"activeData.disabled\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData.__config__.tag === 'el-select'\" label=\"能否搜索\">\n          <el-switch v-model=\"activeData.filterable\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData.__config__.tag === 'el-select'\" label=\"是否多选\">\n          <el-switch v-model=\"activeData.multiple\" @change=\"multipleChange\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData.__config__.required !== undefined\" label=\"是否必填\">\n          <el-switch v-model=\"activeData.__config__.required\" />\n        </el-form-item>\n        <el-form-item v-if=\"activeData.__config__.tips !== undefined\" label=\"开启描述\">\n          <el-switch v-model=\"activeData.__config__.tips\" /> \n        </el-form-item>\n        <el-form-item v-if=\"activeData.__config__.tips\" label=\"描述内容\">\n          <el-input v-model=\"activeData.__config__.tipsDesc\" placeholder=\"请输入描述\" /> \n        </el-form-item>\n        <el-form-item v-if=\"activeData.__config__.tips\" label=\"描述链接\">\n          <el-switch v-model=\"activeData.__config__.tipsIsLink\" /> \n        </el-form-item>\n        <el-form-item v-if=\"activeData.__config__.tipsIsLink\" label=\"链接地址\">\n          <el-input v-model=\"activeData.__config__.tipsLink\" placeholder=\"请输入链接地址\" /> \n        </el-form-item>\n        <!-- <el-form-item v-if=\"activeData.__config__.bindInput !== undefined\" label=\"绑定输入\">\n          <el-switch v-model=\"activeData.__config__.bindInput\" />\n        </el-form-item>\n         <el-form-item v-if=\"activeData.__config__.bindInput\" label=\"绑定内容\">\n          <el-input v-model=\"activeData.__config__.bindValve\" placeholder=\"请输入内容\" />\n        </el-form-item> -->\n        <template v-if=\"activeData.__config__.layoutTree\"> \n          <el-divider>布局结构树</el-divider>\n          <el-tree\n            :data=\"[activeData.__config__]\"\n            :props=\"layoutTreeProps\"\n            node-key=\"renderKey\"\n            default-expand-all\n            draggable\n          >\n            <span slot-scope=\"{ node, data }\">\n              <span class=\"node-label\">\n                <svg-icon class=\"node-icon\" :icon-class=\"data.__config__?data.__config__.tagIcon:data.tagIcon\" />\n                {{ node.label }}\n              </span>\n            </span>\n          </el-tree>\n        </template>\n\n        <template v-if=\"activeData.__config__.layout === 'colFormItem'\">\n          <el-divider>正则校验</el-divider>\n          <div\n            v-for=\"(item, index) in activeData.__config__.regList\"\n            :key=\"index\"\n            class=\"reg-item\"\n          >\n            <span class=\"close-btn\" @click=\"activeData.__config__.regList.splice(index, 1)\">\n              <i class=\"el-icon-close\" />\n            </span>\n            <el-form-item label=\"表达式\">\n              <el-input v-model=\"item.pattern\" placeholder=\"请输入正则\" />\n            </el-form-item>\n            <el-form-item label=\"错误提示\" style=\"margin-bottom:0\">\n              <el-input v-model=\"item.message\" placeholder=\"请输入错误提示\" />\n            </el-form-item>\n          </div>\n          <div style=\"margin-left: 20px\">\n            <el-button icon=\"el-icon-circle-plus-outline\" type=\"text\" @click=\"addReg\">\n              添加规则\n            </el-button>\n          </div>\n        </template>\n      </el-form>\n      <!-- 表单属性 -->\n      <el-form v-show=\"currentTab === 'form'\" size=\"small\" label-width=\"90px\">\n        <el-form-item label=\"表单名\">\n          <el-input v-model=\"formConf.formRef\" placeholder=\"请输入表单名（ref）\" />\n        </el-form-item>\n        <el-form-item label=\"表单模型\">\n          <el-input v-model=\"formConf.formModel\" placeholder=\"请输入数据模型\" />\n        </el-form-item>\n        <el-form-item label=\"校验模型\">\n          <el-input v-model=\"formConf.formRules\" placeholder=\"请输入校验模型\" />\n        </el-form-item>\n        <el-form-item label=\"表单尺寸\">\n          <el-radio-group v-model=\"formConf.size\">\n            <el-radio-button label=\"medium\">\n              中等\n            </el-radio-button>\n            <el-radio-button label=\"small\">\n              较小\n            </el-radio-button>\n            <el-radio-button label=\"mini\">\n              迷你\n            </el-radio-button>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item label=\"标签对齐\">\n          <el-radio-group v-model=\"formConf.labelPosition\">\n            <el-radio-button label=\"left\">\n              左对齐\n            </el-radio-button>\n            <el-radio-button label=\"right\">\n              右对齐\n            </el-radio-button>\n            <el-radio-button label=\"top\">\n              顶部对齐\n            </el-radio-button>\n          </el-radio-group>\n        </el-form-item>\n        <el-form-item label=\"标签宽度\">\n          <el-input v-model.number=\"formConf.labelWidth\" type=\"number\" placeholder=\"请输入标签宽度\" />\n        </el-form-item>\n        <el-form-item label=\"栅格间隔\">\n          <el-input-number v-model=\"formConf.gutter\" :min=\"0\" placeholder=\"栅格间隔\" />\n        </el-form-item>\n        <el-form-item label=\"禁用表单\">\n          <el-switch v-model=\"formConf.disabled\" />\n        </el-form-item>\n        <el-form-item label=\"表单按钮\">\n          <el-switch v-model=\"formConf.formBtns\" />\n        </el-form-item>\n        <el-form-item label=\"显示未选中组件边框\">\n          <el-switch v-model=\"formConf.unFocusedComponentBorder\" />\n        </el-form-item>\n      </el-form>\n    </el-scrollbar>\n  </div>\n\n  <treeNode-dialog :visible.sync=\"dialogVisible\" title=\"添加选项\" @commit=\"addNode\" />\n  <icons-dialog :visible.sync=\"iconsVisible\" :current=\"activeData[currentIconModel]\" @select=\"setIcon\" />\n</div>\n", null]}