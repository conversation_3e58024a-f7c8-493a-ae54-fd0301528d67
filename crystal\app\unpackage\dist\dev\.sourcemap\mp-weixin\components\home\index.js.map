{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/home/<USER>", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/home/<USER>", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/home/<USER>", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/home/<USER>", "uni-app:///components/home/<USER>", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/home/<USER>", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/home/<USER>"], "names": ["name", "props", "data", "top", "computed", "methods", "setTouchMove", "that", "open", "created"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACqC;;;AAGzF;AACgM;AAChM,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAmvB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;AC2BvwB;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACAA;EACAC;EACAC;IACA;MACAC;IACA;EACA;EACAC;EACAC;IACAC;MACA;MACA;QACAC;MACA;IACA;IACAC;MACA,kBACA,mCACA;IACA;EACA;EACAC,6BACA;AACA;AAAA,2B;;;;;;;;;;;;ACtDA;AAAA;AAAA;AAAA;AAAwlC,CAAgB,89BAAG,EAAC,C;;;;;;;;;;;ACA5mC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/home/<USER>", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=08ae13c6&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=08ae13c6&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"08ae13c6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/home/<USER>\"\nexport default component.exports", "export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=08ae13c6&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view style=\"touch-action: none;\">\r\n\t\t<view class=\"home\" style=\"position:fixed;\" :style=\"{ top: top + 'px'}\" id=\"right-nav\" @touchmove.stop.prevent=\"setTouchMove\">\r\n\t\t\t<view class=\"homeCon\" :class=\"homeActive === true ? 'on' : ''\" v-if=\"homeActive\">\r\n\t\t\t\t<navigator hover-class='none' url='/pages/index/index' open-type='switchTab' class='nav-item'>\r\n\t\t\t\t\t<text class='iconfont icon-shouye-xianxing'></text>\r\n\t\t\t\t\t<text class=\"nav-text\">首页</text>\r\n\t\t\t\t</navigator>\r\n\t\t\t\t<navigator hover-class='none' url='/pages/order_addcart/order_addcart' open-type='switchTab' class='nav-item'>\r\n\t\t\t\t\t<text class='iconfont icon-caigou-xianxing'></text>\r\n\t\t\t\t\t<text class=\"nav-text\">购物车</text>\r\n\t\t\t\t</navigator>\r\n\t\t\t\t<navigator hover-class='none' url='/pages/user/index' open-type='switchTab' class='nav-item'>\r\n\t\t\t\t\t<text class='iconfont icon-yonghu1'></text>\r\n\t\t\t\t\t<text class=\"nav-text\">我的</text>\r\n\t\t\t\t</navigator>\r\n\t\t\t</view>\r\n\t\t\t<view @click=\"open\" class=\"pictrueBox\">\r\n\t\t\t\t<view class=\"pictrue\" :class=\"homeActive ? 'active' : ''\">\r\n\t\t\t\t\t<text class=\"iconfont\" :class=\"homeActive ? 'icon-guanbi' : 'icon-caidan'\"></text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tmapGetters\r\n\t} from \"vuex\";\r\n\texport default {\r\n\t\tname: \"Home\",\r\n\t\tprops: {},\r\n\t\tdata: function() {\r\n\t\t\treturn {\r\n\t\t\t\ttop: \"500\"\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: mapGetters([\"homeActive\"]),\r\n\t\tmethods: {\r\n\t\t\tsetTouchMove(e) {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tif (e.touches[0].clientY < 545 && e.touches[0].clientY > 66) {\r\n\t\t\t\t\tthat.top = e.touches[0].clientY\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\topen: function() {\r\n\t\t\t\tthis.homeActive ?\r\n\t\t\t\t\tthis.$store.commit(\"CLOSE_HOME\") :\r\n\t\t\t\t\tthis.$store.commit(\"OPEN_HOME\");\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style scoped>\r\n\t.pictrueBox {\r\n\t\twidth: 110rpx;\r\n\t\theight: 110rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t/*返回主页按钮*/\r\n\t.home {\r\n\t\tposition: fixed;\r\n\t\tcolor: white;\r\n\t\ttext-align: center;\r\n\t\tz-index: 9999;\r\n\t\tright: 20rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.home .homeCon {\r\n\t\tborder-radius: 50rpx;\r\n\t\topacity: 0;\r\n\t\theight: 0;\r\n\t\twidth: 0;\r\n\t\toverflow: hidden;\r\n\t\ttransition: all 0.3s ease-in-out;\r\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);\r\n\t}\r\n\r\n\t.home .homeCon.on {\r\n\t\topacity: 1;\r\n\t\twidth: 320rpx;\r\n\t\theight: 100rpx;\r\n\t\tmargin-right: 20rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-around;\r\n\t\talign-items: center;\r\n\t\tbackground: linear-gradient(135deg, #c9ab79, #e5c68f);\r\n\t\tborder-radius: 50rpx;\r\n\t}\r\n\r\n\t.home .homeCon .nav-item {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tpadding: 0 15rpx;\r\n\t}\r\n\r\n\t.home .homeCon .iconfont {\r\n\t\tfont-size: 40rpx;\r\n\t\tcolor: #fff;\r\n\t\tmargin-bottom: 6rpx;\r\n\t}\r\n\t\r\n\t.home .homeCon .nav-text {\r\n\t\tfont-size: 20rpx;\r\n\t\tcolor: #fff;\r\n\t}\r\n\r\n\t.home .pictrue {\r\n\t\twidth: 90rpx;\r\n\t\theight: 90rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tbackground: linear-gradient(135deg, #c9ab79, #e5c68f);\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.2);\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\t\r\n\t.home .pictrue.active {\r\n\t\ttransform: rotate(180deg);\r\n\t\tbackground: linear-gradient(135deg, #e5c68f, #c9ab79);\r\n\t}\r\n\t\r\n\t.home .pictrue .iconfont {\r\n\t\tfont-size: 46rpx;\r\n\t\tcolor: #fff;\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=08ae13c6&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=08ae13c6&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363899874\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}