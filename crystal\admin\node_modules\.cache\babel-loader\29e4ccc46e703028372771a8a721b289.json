{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\userList\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\userList\\index.vue", "mtime": 1753666157797}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _user = require(\"@/api/user\");\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  name: 'UserList',\n  filters: {\n    saxFilter: function saxFilter(status) {\n      var statusMap = {\n        0: '未知',\n        1: '男',\n        2: '女'\n      };\n      return statusMap[status];\n    },\n    statusFilter: function statusFilter(status) {\n      var statusMap = {\n        'wechat': '微信用户',\n        'routine': '小程序用户'\n      };\n      return statusMap[status];\n    }\n  },\n  data: function data() {\n    return {\n      templateRadio: 0,\n      loading: false,\n      tableData: {\n        data: [],\n        total: 0\n      },\n      tableFrom: {\n        page: 1,\n        limit: 10,\n        keywords: ''\n      }\n    };\n  },\n  mounted: function mounted() {\n    this.getList();\n  },\n  methods: {\n    getTemplateRow: function getTemplateRow(idx, row) {\n      this.$emit('getTemplateRow', row);\n    },\n    // 列表\n    getList: function getList() {\n      var _this = this;\n      this.loading = true;\n      (0, _user.userListApi)(this.tableFrom).then(function (res) {\n        _this.tableData.data = res.list;\n        _this.tableData.total = res.total;\n        _this.loading = false;\n      }).catch(function (res) {\n        _this.$message.error(res.message);\n        _this.loading = false;\n      });\n    },\n    search: function search() {\n      var _this2 = this;\n      this.loading = true;\n      (0, _user.userListApi)({\n        keywords: this.tableFrom.keywords\n      }).then(function (res) {\n        _this2.tableData.data = res.list;\n        _this2.tableData.total = res.total;\n        _this2.loading = false;\n      }).catch(function (res) {\n        _this2.$message.error(res.message);\n        _this2.loading = false;\n      });\n    },\n    pageChange: function pageChange(page) {\n      this.tableFrom.page = page;\n      this.getList();\n    },\n    handleSizeChange: function handleSizeChange(val) {\n      this.tableFrom.limit = val;\n      this.getList();\n    }\n  }\n};", null]}