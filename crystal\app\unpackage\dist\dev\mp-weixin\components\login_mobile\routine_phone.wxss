@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.mobile-bg {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}
.mobile-mask {
  z-index: 20;
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  padding: 67rpx 30rpx;
  background: #fff;
}
.mobile-mask .info-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.mobile-mask .info-box image {
  width: 150rpx;
  height: 150rpx;
  border-radius: 10rpx;
}
.mobile-mask .info-box .title {
  margin-top: 30rpx;
  margin-bottom: 20rpx;
  font-size: 36rpx;
}
.mobile-mask .info-box .txt {
  font-size: 30rpx;
  color: #868686;
}
.mobile-mask .sub_btn {
  width: 690rpx;
  height: 86rpx;
  line-height: 86rpx;
  margin-top: 60rpx;
  background: #c9ab79;
  border-radius: 43rpx;
  color: #fff;
  font-size: 28rpx;
  text-align: center;
}
.animated {
  -webkit-animation-duration: .4s;
          animation-duration: .4s;
}

