@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.my-order .header.data-v-09744212 {
  height: 250rpx;
  padding: 0 30rpx;
}
.my-order .header .picTxt.data-v-09744212 {
  height: 190rpx;
}
.my-order .header .picTxt .text.data-v-09744212 {
  color: rgba(255, 255, 255, 0.8);
  font-size: 26rpx;
  font-family: 'Guildford Pro';
}
.my-order .header .picTxt .text .name.data-v-09744212 {
  font-size: 34rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 20rpx;
}
.my-order .header .picTxt .pictrue.data-v-09744212 {
  width: 122rpx;
  height: 109rpx;
}
.my-order .header .picTxt .pictrue image.data-v-09744212 {
  width: 100%;
  height: 100%;
}
.my-order .nav.data-v-09744212 {
  background-color: #fff;
  width: 690rpx;
  height: 140rpx;
  border-radius: 14rpx;
  margin: -60rpx auto 0 auto;
}
.my-order .nav .item.data-v-09744212 {
  text-align: center;
  font-size: 26rpx;
  color: #282828;
  padding: 26rpx 0;
}
.my-order .nav .item.on.data-v-09744212 {
  font-weight: bold;
  border-bottom: 5rpx solid #c9ab79;
}
.my-order .nav .item .num.data-v-09744212 {
  margin-top: 18rpx;
}
.my-order .list.data-v-09744212 {
  width: 690rpx;
  margin: 14rpx auto 0 auto;
}
.my-order .list .item.data-v-09744212 {
  background-color: #fff;
  border-radius: 14rpx;
  margin-bottom: 14rpx;
}
.my-order .list .item .title.data-v-09744212 {
  height: 84rpx;
  padding: 0 24rpx;
  border-bottom: 1rpx solid #eee;
  font-size: 28rpx;
  color: #282828;
}
.my-order .list .item .title .sign.data-v-09744212 {
  font-size: 24rpx;
  padding: 0 13rpx;
  height: 36rpx;
  margin-right: 15rpx;
  border-radius: 18rpx;
}
.my-order .list .item .item-info.data-v-09744212 {
  padding: 0 24rpx;
  margin-top: 22rpx;
}
.my-order .list .item .item-info .pictrue.data-v-09744212 {
  width: 120rpx;
  height: 120rpx;
}
.my-order .list .item .item-info .pictrue image.data-v-09744212 {
  width: 100%;
  height: 100%;
  border-radius: 14rpx;
}
.my-order .list .item .item-info .text.data-v-09744212 {
  width: 500rpx;
  font-size: 28rpx;
  color: #999;
}
.my-order .list .item .item-info .text .name.data-v-09744212 {
  width: 350rpx;
  color: #282828;
}
.my-order .list .item .item-info .text .money.data-v-09744212 {
  text-align: right;
}
.my-order .list .item .totalPrice.data-v-09744212 {
  font-size: 26rpx;
  color: #282828;
  text-align: right;
  margin: 27rpx 0 0 30rpx;
  padding: 0 30rpx 30rpx 0;
  border-bottom: 1rpx solid #eee;
}
.my-order .list .item .totalPrice .money.data-v-09744212 {
  font-size: 28rpx;
  font-weight: bold;
}
.my-order .list .item .bottom.data-v-09744212 {
  height: 107rpx;
  padding: 0 30rpx;
}
.my-order .list .item .bottom .bnt.data-v-09744212 {
  width: 176rpx;
  height: 60rpx;
  text-align: center;
  line-height: 60rpx;
  color: #fff;
  border-radius: 50rpx;
  font-size: 27rpx;
}
.my-order .list .item .bottom .bnt.cancelBnt.data-v-09744212 {
  border: 1rpx solid #ddd;
  color: #aaa;
}
.my-order .list .item .bottom .bnt ~ .bnt.data-v-09744212 {
  margin-left: 17rpx;
}
.noCart.data-v-09744212 {
  margin-top: 171rpx;
  padding-top: 0.1rpx;
}
.noCart .pictrue.data-v-09744212 {
  width: 414rpx;
  height: 336rpx;
  margin: 78rpx auto 56rpx auto;
}
.noCart .pictrue image.data-v-09744212 {
  width: 100%;
  height: 100%;
}
.circle-container.data-v-09744212 {
  position: relative;
  border-radius: 50%;
  border: gray 1px solid;
  transition: all 1s;
}
.marble.data-v-09744212 {
  position: absolute;
  transition: all 1s;
}

