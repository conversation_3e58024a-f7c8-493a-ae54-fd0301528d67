<view class="data-v-56ab2edc"><form report-submit="true" data-event-opts="{{[['submit',[['subRefund',['$event']]]]]}}" bindsubmit="__e" class="data-v-56ab2edc"><view class="apply-return data-v-56ab2edc"><block wx:if="{{orderInfo.type==2}}"><view class="data-v-56ab2edc"><view style="display:flex;justify-content:center;align-items:center;height:500rpx;position:relative;background-color:white;border-radius:14rpx;" class="_div data-v-56ab2edc"><view class="circle-container _div data-v-56ab2edc" style="{{'width:'+(radius*2+'rpx')+';'+('height:'+(radius*2+'rpx')+';')}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="marble _div data-v-56ab2edc" style="{{item.s0}}"></view></block></view></view></view></block><block wx:else><view class="data-v-56ab2edc"><block wx:for="{{orderInfo.orderInfoList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="goodsStyle acea-row row-between borRadius14 data-v-56ab2edc"><view class="pictrue data-v-56ab2edc"><image src="{{item.image}}" class="data-v-56ab2edc"></image></view><view class="text acea-row row-between data-v-56ab2edc"><view class="name line2 data-v-56ab2edc">{{item.storeName}}</view><view class="money data-v-56ab2edc"><view class="data-v-56ab2edc">{{"￥"+item.price}}</view><view class="num data-v-56ab2edc">{{"x"+item.cartNum}}</view></view></view></view></block></view></block><view class="list borRadius14 data-v-56ab2edc"><block wx:if="{{orderInfo.type!=2}}"><view class="item acea-row row-between-wrapper data-v-56ab2edc"><view class="data-v-56ab2edc">退货件数</view><view class="num data-v-56ab2edc">{{orderInfo.totalNum}}</view></view></block><view class="item acea-row row-between-wrapper data-v-56ab2edc"><view class="data-v-56ab2edc">退款金额</view><view class="num data-v-56ab2edc">{{"￥"+orderInfo.payPrice}}</view></view><view data-event-opts="{{[['tap',[['toggleTab',['region']]]]]}}" class="item acea-row row-between-wrapper data-v-56ab2edc" bindtap="__e"><view class="data-v-56ab2edc">退款原因</view><picker class="num data-v-56ab2edc" value="{{index}}" range="{{RefundArray}}" data-event-opts="{{[['change',[['bindPickerChange',['$event']]]]]}}" bindchange="__e"><view class="picker acea-row row-between-wrapper data-v-56ab2edc"><view class="reason data-v-56ab2edc">{{RefundArray[index]}}</view><text class="iconfont icon-jiantou data-v-56ab2edc"></text></view></picker></view><view class="item textarea acea-row row-between data-v-56ab2edc"><view class="data-v-56ab2edc">备注说明</view><textarea class="num data-v-56ab2edc" placeholder="填写备注信息，100字以内" name="refund_reason_wap_explain" placeholder-class="填写备注信息，100字以内"></textarea></view><view class="item acea-row row-between data-v-56ab2edc" style="border:none;"><view class="title acea-row row-between-wrapper data-v-56ab2edc"><view class="data-v-56ab2edc">上传凭证</view><view class="tip data-v-56ab2edc">( 最多可上传3张 )</view></view><view class="upload acea-row row-middle data-v-56ab2edc"><block wx:for="{{refund_reason_wap_imgPath}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="pictrue data-v-56ab2edc"><image src="{{item}}" class="data-v-56ab2edc"></image><view data-event-opts="{{[['tap',[['DelPic',[index]]]]]}}" class="iconfont icon-guanbi1 font-color data-v-56ab2edc" bindtap="__e"></view></view></block><block wx:if="{{$root.g0<3}}"><view data-event-opts="{{[['tap',[['uploadpic',['$event']]]]]}}" class="pictrue acea-row row-center-wrapper row-column data-v-56ab2edc" bindtap="__e"><text class="iconfont icon-icon25201 data-v-56ab2edc"></text><view class="data-v-56ab2edc">上传凭证</view></view></block></view></view><button class="returnBnt bg-color data-v-56ab2edc" form-type="submit">申请退款</button></view></view></form></view>