<view class="ai-recommend _div"><view class="steps-container _div"><view class="steps _div"><block wx:for="{{steps}}" wx:for-item="step" wx:for-index="index" wx:key="index"><view class="{{['step','_div',(currentStep>=index)?'active':'']}}"><view class="step-number _div">{{index+1}}</view><view class="step-text _div">{{step}}</view><block wx:if="{{index<$root.g0-1}}"><view class="step-line _div"></view></block></view></block></view></view><block wx:if="{{currentStep===0}}"><view class="form-container _div"><form data-event-opts="{{[['submit',[['submitForm',['$event']]]]]}}" bindsubmit="__e"><view class="form-section _div"><view class="section-title _div">基础信息</view><view class="form-item _div"><text class="form-label">年龄</text><input class="form-input" type="number" placeholder="请输入您的年龄" data-event-opts="{{[['input',[['__set_model',['$0','age','$event',[]],['formData']]]]]}}" value="{{formData.age}}" bindinput="__e"/></view><view class="form-item _div"><text class="form-label">手围尺寸（cm）</text><input class="form-input" type="number" placeholder="请输入您的手腕围度" step="0.1" data-event-opts="{{[['input',[['__set_model',['$0','hand','$event',[]],['formData']]]]]}}" value="{{formData.hand}}" bindinput="__e"/></view><view class="form-item _div"><text class="form-label">性别</text><view class="radio-group _div"><view data-event-opts="{{[['tap',[['setGenderMale',['$event']]]]]}}" class="{{['radio-item','_div',(formData.gender==='male')?'active':'']}}" bindtap="__e"><text>男</text></view><view data-event-opts="{{[['tap',[['setGenderFemale',['$event']]]]]}}" class="{{['radio-item','_div',(formData.gender==='female')?'active':'']}}" bindtap="__e"><text>女</text></view></view></view><view class="form-item _div"><text class="form-label">职业</text><picker class="form-picker" range="{{occupationOptions}}" data-event-opts="{{[['change',[['onOccupationChange',['$event']]]]]}}" bindchange="__e"><view class="picker-value _div">{{formData.occupation||'请选择您的职业'}}</view></picker></view></view><view class="form-section _div"><view class="section-title _div">需求信息</view><view class="form-item _div"><text class="form-label">目的</text><picker class="form-picker" range="{{purposeOptions}}" data-event-opts="{{[['change',[['onPurposeChange',['$event']]]]]}}" bindchange="__e"><view class="picker-value _div">{{formData.purpose||'请选择您的目的'}}</view></picker></view><view class="form-item _div"><text class="form-label">预算范围</text><view class="budget-slider _div"><slider min="{{100}}" max="{{10000}}" step="{{100}}" value="{{formData.budget}}" show-value="{{true}}" data-event-opts="{{[['change',[['onBudgetChange',['$event']]]]]}}" bindchange="__e"></slider><view class="budget-value _div">{{"¥"+formData.budget}}</view></view></view><view class="form-item _div"><text class="form-label">颜色偏好</text><view class="color-grid _div"><block wx:for="{{$root.l0}}" wx:for-item="color" wx:for-index="index" wx:key="index"><view class="{{['color-item','_div',(color.g1)?'active':'']}}" style="{{'background-color:'+(color.$orig.value)+';'}}" data-index="{{index}}" data-event-opts="{{[['tap',[['toggleColorByIndex',['$event']]]]]}}" bindtap="__e"></view></block></view></view><view class="form-item _div"><text class="form-label">材质偏好</text><view class="checkbox-group _div"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['checkbox-item','_div',(item.g2)?'active':'']}}" data-index="{{index}}" data-event-opts="{{[['tap',[['toggleMaterialByIndex',['$event']]]]]}}" bindtap="__e"><text>{{item.$orig.label}}</text></view></block></view></view></view><view class="form-section _div"><view class="section-title _div">特殊需求</view><view class="form-item _div"><text class="form-label">特殊需求描述</text><textarea class="form-textarea" placeholder="请描述您的特殊需求..." data-event-opts="{{[['input',[['__set_model',['$0','specialNeeds','$event',[]],['formData']]]]]}}" value="{{formData.specialNeeds}}" bindinput="__e"></textarea></view><view class="form-item _div"><text class="form-label">脉轮需求</text><view class="chakra-selector _div"><block wx:for="{{$root.l2}}" wx:for-item="chakra" wx:for-index="index" wx:key="index"><view class="{{['chakra-item','_div',(chakra.g3)?'active':'']}}" data-index="{{index}}" data-event-opts="{{[['tap',[['toggleChakraByIndex',['$event']]]]]}}" bindtap="__e"><view class="chakra-color _div" style="{{'background-color:'+(chakra.$orig.color)+';'}}"></view><text class="chakra-name">{{chakra.$orig.label}}</text></view></block></view></view></view><button data-event-opts="{{[['tap',[['submitForm',['$event']]]]]}}" class="submit-button" bindtap="__e">开始生成</button></form></view></block><block wx:if="{{currentStep===1}}"><view class="generating _div"><block wx:if="{{aiTaskStatus==='PROCESSING'}}"><view class="loading-animation _div"><block wx:for="{{3}}" wx:for-item="i" wx:for-index="__i0__" wx:key="*this"><view class="loading-circle _div"></view></block></view></block><view class="generating-text _div">AI正在为您精心设计手串...</view><view class="generating-subtext _div">{{generatingStatusText}}</view><block wx:if="{{aiProgress>0}}"><view class="progress-container _div"><view class="progress-bar _div"><view class="progress-fill _div" style="{{'width:'+(aiProgress+'%')+';'}}"></view></view><view class="progress-text _div">{{aiProgress+"%"}}</view></view></block><block wx:if="{{aiDescription}}"><scroll-view class="ai-output vue-ref" scroll-y="true" scroll-into-view="{{scrollIntoViewId}}" data-ref="aiOutput" data-event-opts="{{[['scrolltolower',[['onScrollToBottom',['$event']]]]]}}" bindscrolltolower="__e"><view class="output-title _div">AI分析结果预览：</view><mp-html vue-id="86e568b0-1" content="{{formattedAiDescription}}" tag-style="{{htmlTagStyle}}" lazy-load="{{true}}" selectable="{{true}}" bind:__l="__l"></mp-html><view id="scroll-bottom-anchor" class="_div"></view></scroll-view></block></view></block><block wx:if="{{currentStep===2}}"><view class="result-display _div"><view class="bracelet-prediv _div"><view style="display:flex;justify-content:center;align-items:center;height:500rpx;position:relative;" class="_div"><view class="circle-container _div" style="{{'width:'+(radius*2+'rpx')+';'+('height:'+(radius*2+'rpx')+';')+('border:'+('2px dashed #c9ab79')+';')}}"><block wx:for="{{$root.l3}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="marble _div" style="{{item.s0}}"></view></block></view></view></view><block wx:if="{{showRecommendationDetails}}"><view class="recommendation-details"><view class="recommendation-header"><text class="recommendation-title">推荐结果</text></view><scroll-view class="recommendation-body" scroll-y="true" scroll-into-view="{{scrollIntoViewId}}" id="recommendationScrollView" data-event-opts="{{[['scrolltolower',[['onScrollToBottom',['$event']]]]]}}" bindscrolltolower="__e"><block wx:if="{{aiTaskStatus==='PROCESSING'}}"><view class="ai-generation-status"><view class="loading-animation"><view class="dot"></view><view class="dot"></view><view class="dot"></view></view><text class="generation-text">AI正在为您设计手串方案，请稍候...</text><view class="progress-bar"><view class="progress-filled" style="{{'width:'+(aiProgress+'%')+';'}}"></view></view><text class="progress-text">{{aiProgressText}}</text></view></block><view class="ai-output-container"><mp-html vue-id="86e568b0-2" content="{{formattedAiDescription}}" tag-style="{{htmlTagStyle}}" lazy-load="{{true}}" selectable="{{true}}" bind:__l="__l"></mp-html></view><view style="height:2px;" id="scroll-bottom-anchor"></view><block wx:if="{{aiTaskStatus==='FAILED'}}"><view class="error-message"><text>{{errorMessage||'生成失败，请重试'}}</text></view></block></scroll-view><view class="action-buttons action-buttons-row"><button data-event-opts="{{[['tap',[['generateNewRecommendation',['$event']]]]]}}" class="action-button outline" bindtap="__e">{{''+(aiTaskStatus==='PROCESSING'?'取消生成':'重新生成')+''}}</button></view></view></block></view></block><block wx:if="{{currentStep===3}}"><view class="confirm-order _div"><view class="order-summary _div"><view class="price-info _div"><text>总价：</text><text class="price">{{"¥"+totalPrice}}</text></view><view class="beads-count _div">{{"共"+$root.g4+"颗水晶"}}</view></view><view class="bracelet-prediv _div"><view style="display:flex;justify-content:center;align-items:center;height:500rpx;position:relative;" class="_div"><view class="circle-container _div" style="{{'width:'+(radius*2+'rpx')+';'+('height:'+(radius*2+'rpx')+';')+('border:'+('2px dashed #c9ab79')+';')}}"><block wx:for="{{$root.l4}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="marble _div" style="{{item.s1}}"></view></block></view></view></view><view class="order-buttons _div"><button class="order-button" type="primary" data-event-opts="{{[['tap',[['placeOrder',['$event']]]]]}}" bindtap="__e">立即下单</button><button data-event-opts="{{[['tap',[['saveToMyBracelets',['$event']]]]]}}" class="save-button" bindtap="__e">保存到我的手串</button></view></view></block></view>