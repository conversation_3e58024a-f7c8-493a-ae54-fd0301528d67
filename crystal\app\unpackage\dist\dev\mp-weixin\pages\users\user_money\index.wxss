@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.my-account .wrapper.data-v-19311a2f {
  background-color: #fff;
  padding: 32rpx 0 15rpx 0;
  margin-bottom: 14rpx;
}
.my-account .wrapper .header.data-v-19311a2f {
  width: 690rpx;
  height: 330rpx;
  background-image: linear-gradient(to right, #f33b2b 0%, #f36053 100%);
  border-radius: 16rpx;
  margin: 0 auto;
  box-sizing: border-box;
  color: rgba(255, 255, 255, 0.6);
  font-size: 24rpx;
}
.my-account .wrapper .header .headerCon.data-v-19311a2f {
  background-image: url("data:image/png;base64,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");
  background-repeat: no-repeat;
  background-size: 100%;
  height: 100%;
  width: 100%;
  padding: 36rpx 0 29rpx 0;
  box-sizing: border-box;
}
.my-account .wrapper .header .headerCon .account.data-v-19311a2f {
  padding: 0 35rpx;
}
.my-account .wrapper .header .headerCon .account .assets .money.data-v-19311a2f {
  font-size: 72rpx;
  color: #fff;
  font-family: 'Guildford Pro';
}
.my-account .wrapper .header .headerCon .account .recharge.data-v-19311a2f {
  font-size: 28rpx;
  width: 150rpx;
  height: 54rpx;
  border-radius: 27rpx;
  background-color: #fff9f8;
  text-align: center;
  line-height: 54rpx;
}
.my-account .wrapper .header .headerCon .cumulative.data-v-19311a2f {
  margin-top: 46rpx;
}
.my-account .wrapper .header .headerCon .cumulative .item.data-v-19311a2f {
  flex: 1;
  padding-left: 35rpx;
}
.my-account .wrapper .header .headerCon .cumulative .item .money.data-v-19311a2f {
  font-size: 48rpx;
  font-family: 'Guildford Pro';
  color: #fff;
  margin-top: 6rpx;
}
.my-account .wrapper .nav.data-v-19311a2f {
  height: 155rpx;
  border-bottom: 1rpx solid #f5f5f5;
}
.my-account .wrapper .nav .item.data-v-19311a2f {
  flex: 1;
  text-align: center;
  font-size: 26rpx;
  color: #999;
}
.my-account .wrapper .nav .item .pictrue.data-v-19311a2f {
  width: 44rpx;
  height: 44rpx;
  margin: 0 auto;
  margin-bottom: 20rpx;
}
.my-account .wrapper .nav .item .pictrue image.data-v-19311a2f {
  width: 100%;
  height: 100%;
}
.my-account .wrapper .advert.data-v-19311a2f {
  padding: 0 30rpx;
  margin-top: 30rpx;
}
.my-account .wrapper .advert .item.data-v-19311a2f {
  background-color: #fff6d1;
  width: 332rpx;
  height: 118rpx;
  border-radius: 10rpx;
  padding: 0 27rpx 0 25rpx;
  box-sizing: border-box;
  font-size: 24rpx;
  color: #e44609;
}
.my-account .wrapper .advert .item.on.data-v-19311a2f {
  background-color: #fff3f3;
  color: #e96868;
}
.my-account .wrapper .advert .item .pictrue.data-v-19311a2f {
  width: 78rpx;
  height: 78rpx;
}
.my-account .wrapper .advert .item .pictrue image.data-v-19311a2f {
  width: 100%;
  height: 100%;
}
.my-account .wrapper .advert .item .text .name.data-v-19311a2f {
  font-size: 30rpx;
  font-weight: bold;
  color: #f33c2b;
  margin-bottom: 7rpx;
}
.my-account .wrapper .advert .item.on .text .name.data-v-19311a2f {
  color: #f64051;
}
.my-account .wrapper .list.data-v-19311a2f {
  padding: 0 30rpx;
}
.my-account .wrapper .list .item.data-v-19311a2f {
  margin-top: 44rpx;
}
.my-account .wrapper .list .item .picTxt .iconfont.data-v-19311a2f {
  width: 82rpx;
  height: 82rpx;
  border-radius: 50%;
  background-image: linear-gradient(to right, #ff9389 0%, #f9776b 100%);
  text-align: center;
  line-height: 82rpx;
  color: #fff;
  font-size: 40rpx;
}
.my-account .wrapper .list .item .picTxt .iconfont.yellow.data-v-19311a2f {
  background-image: linear-gradient(to right, #ffccaa 0%, #fea060 100%);
}
.my-account .wrapper .list .item .picTxt .iconfont.green.data-v-19311a2f {
  background-image: linear-gradient(to right, #a1d67c 0%, #9dd074 100%);
}
.my-account .wrapper .list .item .picTxt.data-v-19311a2f {
  width: 428rpx;
  font-size: 30rpx;
  color: #282828;
}
.my-account .wrapper .list .item .picTxt .text.data-v-19311a2f {
  width: 317rpx;
}
.my-account .wrapper .list .item .picTxt .text .infor.data-v-19311a2f {
  font-size: 24rpx;
  color: #999;
  margin-top: 5rpx;
}
.my-account .wrapper .list .item .bnt.data-v-19311a2f {
  font-size: 26rpx;
  color: #282828;
  width: 156rpx;
  height: 52rpx;
  border: 1rpx solid #ddd;
  border-radius: 26rpx;
  text-align: center;
  line-height: 52rpx;
}
.my-account .wrapper .list .item .bnt.end.data-v-19311a2f {
  font-size: 26rpx;
  color: #aaa;
  background-color: #f2f2f2;
  border-color: #f2f2f2;
}

