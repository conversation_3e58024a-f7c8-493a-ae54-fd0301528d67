(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/activity/goods_combination_details/index"],{

/***/ 533:
/*!*****************************************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/main.js?{"page":"pages%2Factivity%2Fgoods_combination_details%2Findex"} ***!
  \*****************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _index = _interopRequireDefault(__webpack_require__(/*! ./pages/activity/goods_combination_details/index.vue */ 534));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_index.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 534:
/*!********************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/goods_combination_details/index.vue ***!
  \********************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _index_vue_vue_type_template_id_26bb9225_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.vue?vue&type=template&id=26bb9225&scoped=true& */ 535);
/* harmony import */ var _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.vue?vue&type=script&lang=js& */ 537);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _index_vue_vue_type_style_index_0_id_26bb9225_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.vue?vue&type=style&index=0&id=26bb9225&scoped=true&lang=scss& */ 539);
/* harmony import */ var _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 51);

var renderjs





/* normalize component */

var component = Object(_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _index_vue_vue_type_template_id_26bb9225_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _index_vue_vue_type_template_id_26bb9225_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "26bb9225",
  null,
  false,
  _index_vue_vue_type_template_id_26bb9225_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/activity/goods_combination_details/index.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 535:
/*!***************************************************************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/goods_combination_details/index.vue?vue&type=template&id=26bb9225&scoped=true& ***!
  \***************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_26bb9225_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=26bb9225&scoped=true& */ 536);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_26bb9225_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_26bb9225_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_26bb9225_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_26bb9225_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 536:
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/goods_combination_details/index.vue?vue&type=template&id=26bb9225&scoped=true& ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    jyfParser: function () {
      return Promise.all(/*! import() | components/jyf-parser/jyf-parser */[__webpack_require__.e("common/vendor"), __webpack_require__.e("components/jyf-parser/jyf-parser")]).then(__webpack_require__.bind(null, /*! @/components/jyf-parser/jyf-parser.vue */ 717))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var m0 = parseFloat(_vm.storeInfo.sales)
  var m1 = parseFloat(_vm.storeInfo.ficti)
  var g0 = _vm.attribute.productAttr.length
  var m2 = parseFloat(_vm.pinkOkSum)
  var g1 = _vm.attribute.productSelect.quota > 0 ? _vm.pink.length : null
  var g2 = _vm.attribute.productSelect.quota > 0 && g1 ? _vm.pink.length : null
  var g3 =
    _vm.attribute.productSelect.quota > 0 && g1 && !(g2 > _vm.AllIndex)
      ? _vm.pink.length === _vm.AllIndex &&
        _vm.pink.length !== _vm.AllIndexDefault
      : null
  var g4 = _vm.reply.length
  if (!_vm._isMounted) {
    _vm.e0 = function ($event) {
      _vm.H5ShareBox = false
    }
  }
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        m0: m0,
        m1: m1,
        g0: g0,
        m2: m2,
        g1: g1,
        g2: g2,
        g3: g3,
        g4: g4,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 537:
/*!*********************************************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/goods_combination_details/index.vue?vue&type=script&lang=js& ***!
  \*********************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js& */ 538);
/* harmony import */ var _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 538:
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/goods_combination_details/index.vue?vue&type=script&lang=js& ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _uqrcode = _interopRequireDefault(__webpack_require__(/*! @/js_sdk/Sansnn-uQRCode/uqrcode.js */ 108));
var _vuex = __webpack_require__(/*! vuex */ 35);
var _utils = __webpack_require__(/*! @/utils */ 81);
var _base64src = __webpack_require__(/*! @/utils/base64src.js */ 109);
var _api = __webpack_require__(/*! @/api/api.js */ 62);
var _login = __webpack_require__(/*! @/libs/login.js */ 33);
var _activity = __webpack_require__(/*! @/api/activity.js */ 68);
var _store = __webpack_require__(/*! @/api/store.js */ 80);
var _public = __webpack_require__(/*! @/api/public */ 47);
var _user = __webpack_require__(/*! @/api/user */ 38);
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//

var app = getApp();
var authorize = function authorize() {
  Promise.all(/*! require.ensure | components/Authorize */[__webpack_require__.e("common/vendor"), __webpack_require__.e("components/Authorize")]).then((function () {
    return resolve(__webpack_require__(/*! @/components/Authorize */ 696));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var productConSwiper = function productConSwiper() {
  __webpack_require__.e(/*! require.ensure | components/productConSwiper/index */ "components/productConSwiper/index").then((function () {
    return resolve(__webpack_require__(/*! @/components/productConSwiper */ 727));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var parser = function parser() {
  Promise.all(/*! require.ensure | components/jyf-parser/jyf-parser */[__webpack_require__.e("common/vendor"), __webpack_require__.e("components/jyf-parser/jyf-parser")]).then((function () {
    return resolve(__webpack_require__(/*! @/components/jyf-parser/jyf-parser */ 717));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var home = function home() {
  __webpack_require__.e(/*! require.ensure | components/home/<USER>/ "components/home/<USER>").then((function () {
    return resolve(__webpack_require__(/*! @/components/home/<USER>/ 755));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var productWindow = function productWindow() {
  __webpack_require__.e(/*! require.ensure | components/productWindow/index */ "components/productWindow/index").then((function () {
    return resolve(__webpack_require__(/*! @/components/productWindow/index.vue */ 710));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var userEvaluation = function userEvaluation() {
  __webpack_require__.e(/*! require.ensure | components/userEvaluation/index */ "components/userEvaluation/index").then((function () {
    return resolve(__webpack_require__(/*! @/components/userEvaluation/index.vue */ 741));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var countDown = function countDown() {
  __webpack_require__.e(/*! require.ensure | components/countDown/index */ "components/countDown/index").then((function () {
    return resolve(__webpack_require__(/*! @/components/countDown/index.vue */ 682));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var shareRedPackets = function shareRedPackets() {
  __webpack_require__.e(/*! require.ensure | components/shareRedPackets/index */ "components/shareRedPackets/index").then((function () {
    return resolve(__webpack_require__(/*! @/components/shareRedPackets */ 748));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var _default = {
  components: {
    shareRedPackets: shareRedPackets,
    productConSwiper: productConSwiper,
    authorize: authorize,
    "jyf-parser": parser,
    home: home,
    "product-window": productWindow,
    userEvaluation: userEvaluation,
    countDown: countDown
  },
  computed: (0, _vuex.mapGetters)({
    'isLogin': 'isLogin',
    'userData': 'userInfo',
    'uid': 'uid',
    'chatUrl': 'chatUrl'
  }),
  data: function data() {
    var _ref;
    return _ref = {
      bgColor: {
        'bgColor': '',
        'Color': '#999999',
        'isDay': true
      },
      userCollect: false,
      dataShow: 0,
      navH: '',
      id: 0,
      userInfo: {},
      itemNew: [],
      indicatorDots: false,
      circular: true,
      autoplay: true,
      interval: 3000,
      duration: 500,
      attribute: {
        cartAttr: false,
        productAttr: [],
        productSelect: {}
      },
      productValue: [],
      isOpen: false,
      attr: '请选择',
      attrValue: '',
      AllIndex: 2,
      maxAllIndex: 0,
      replyChance: '',
      limitNum: 1,
      timeer: null,
      iSplus: false
    }, (0, _defineProperty2.default)(_ref, "navH", ""), (0, _defineProperty2.default)(_ref, "navList", ['商品', '评价', '详情']), (0, _defineProperty2.default)(_ref, "opacity", 0), (0, _defineProperty2.default)(_ref, "scrollY", 0), (0, _defineProperty2.default)(_ref, "topArr", []), (0, _defineProperty2.default)(_ref, "toView", ''), (0, _defineProperty2.default)(_ref, "height", 0), (0, _defineProperty2.default)(_ref, "heightArr", []), (0, _defineProperty2.default)(_ref, "lock", false), (0, _defineProperty2.default)(_ref, "scrollTop", 0), (0, _defineProperty2.default)(_ref, "storeInfo", {}), (0, _defineProperty2.default)(_ref, "pinkOkSum", 0), (0, _defineProperty2.default)(_ref, "pink", []), (0, _defineProperty2.default)(_ref, "replyCount", 0), (0, _defineProperty2.default)(_ref, "reply", []), (0, _defineProperty2.default)(_ref, "imgUrls", []), (0, _defineProperty2.default)(_ref, "sharePacket", {
      isState: true //默认不显示
    }), (0, _defineProperty2.default)(_ref, "tagStyle", {
      img: 'width:100%;display:block;',
      table: 'width:100%',
      video: 'width:100%'
    }), (0, _defineProperty2.default)(_ref, "posters", false), (0, _defineProperty2.default)(_ref, "weixinStatus", false), (0, _defineProperty2.default)(_ref, "posterImageStatus", false), (0, _defineProperty2.default)(_ref, "canvasStatus", false), (0, _defineProperty2.default)(_ref, "storeImage", ''), (0, _defineProperty2.default)(_ref, "PromotionCode", ''), (0, _defineProperty2.default)(_ref, "posterImage", ''), (0, _defineProperty2.default)(_ref, "posterbackgd", '/static/images/posterbackgd.png'), (0, _defineProperty2.default)(_ref, "navActive", 0), (0, _defineProperty2.default)(_ref, "actionSheetHidden", false), (0, _defineProperty2.default)(_ref, "attrTxt", ''), (0, _defineProperty2.default)(_ref, "cart_num", ''), (0, _defineProperty2.default)(_ref, "isAuto", false), (0, _defineProperty2.default)(_ref, "isShowAuth", false), (0, _defineProperty2.default)(_ref, "AllIndexDefault", 0), (0, _defineProperty2.default)(_ref, "imgTop", ''), (0, _defineProperty2.default)(_ref, "qrcodeSize", 600), (0, _defineProperty2.default)(_ref, "H5ShareBox", false), (0, _defineProperty2.default)(_ref, "onceNum", 0), (0, _defineProperty2.default)(_ref, "errT", ''), (0, _defineProperty2.default)(_ref, "returnShow", true), (0, _defineProperty2.default)(_ref, "homeTop", 20), _ref;
  },
  watch: {
    isLogin: {
      handler: function handler(newV, oldV) {
        if (newV) {
          this.combinationDetail();
        }
      },
      deep: true
    }
  },
  onLoad: function onLoad(options) {
    var _this = this;
    var that = this;
    this.$store.commit("PRODUCT_TYPE", 'normal');
    var pages = getCurrentPages();
    //	that.returnShow = pages.length === 1 ? false : true;
    this.$nextTick(function () {
      var menuButton = uni.getMenuButtonBoundingClientRect();
      var query = uni.createSelectorQuery().in(_this);
      query.select('#home').boundingClientRect(function (data) {
        _this.homeTop = menuButton.top * 2 + menuButton.height - data.height;
      }).exec();
    });
    this.navH = app.globalData.navHeight;

    //设置商品列表高度
    uni.getSystemInfo({
      success: function success(res) {
        that.height = res.windowHeight;
        //res.windowHeight:获取整个窗口高度为px，*2为rpx；98为头部占据的高度；
      }
    });

    if (options.hasOwnProperty('id') || options.scene) {
      if (options.scene) {
        // 仅仅小程序扫码进入
        var qrCodeValue = this.$util.getUrlParams(decodeURIComponent(options.scene));
        var mapeMpQrCodeValue = this.$util.formatMpQrCodeData(qrCodeValue);
        app.globalData.spread = mapeMpQrCodeValue.spread;
        this.id = mapeMpQrCodeValue.id;
        setTimeout(function () {
          (0, _user.spread)(mapeMpQrCodeValue.spread).then(function (res) {}).catch(function (res) {});
        }, 2000);
      } else {
        this.id = options.id;
      }
      if (this.isLogin) {
        this.combinationDetail();
      } else {
        this.$Cache.set('login_back_url', "/pages/activity/goods_combination_details/index?id=".concat(options.id, "&spread=").concat(options.pid ? options.pid : 0));
        (0, _login.toLogin)();
      }
    } else {
      try {
        var val = uni.getStorageSync('comGoodsId');
        if (val != '') {
          this.id = val;
          this.combinationDetail();
        }
      } catch (e) {
        uni.showToast({
          title: '参数错误',
          icon: 'none',
          duration: 1000,
          mask: true
        });
      }
    }
    ;
  },
  methods: {
    getProductReplyCount: function getProductReplyCount() {
      var that = this;
      (0, _store.getReplyConfig)(that.storeInfo.productId).then(function (res) {
        that.$set(that, 'replyChance', res.data.replyChance * 100);
        that.$set(that, 'replyCount', res.data.sumCount);
      });
    },
    getProductReplyList: function getProductReplyList() {
      var _this2 = this;
      (0, _store.getReplyProduct)(this.storeInfo.productId).then(function (res) {
        _this2.reply = res.data.productReply ? [res.data.productReply] : [];
      });
    },
    kefuClick: function kefuClick() {
      location.href = this.chatUrl;
    },
    closePosters: function closePosters() {
      this.posters = false;
    },
    closeChange: function closeChange() {
      this.$set(this.sharePacket, 'isState', true);
    },
    showAll: function showAll() {
      this.AllIndexDefault = this.AllIndex;
      this.AllIndex = this.pink.length;
    },
    hideAll: function hideAll() {
      this.AllIndex = this.AllIndexDefault;
    },
    // 授权关闭
    authColse: function authColse(e) {
      this.isShowAuth = e;
    },
    /**
     * 购物车手动填写
     * 
     */
    iptCartNum: function iptCartNum(e) {
      if (e > this.onceNum) {
        this.$util.Tips({
          title: "\u8BE5\u5546\u54C1\u6BCF\u6B21\u9650\u8D2D".concat(this.onceNum).concat(this.storeInfo.unitName)
        });
        this.$set(this.attribute.productSelect, 'cart_num', this.onceNum);
        this.$set(this, "cart_num", this.onceNum);
      } else {
        this.$set(this.attribute.productSelect, 'cart_num', e);
        this.$set(this, "cart_num", e);
      }
    },
    // 返回
    returns: function returns() {
      uni.navigateBack();
    },
    // 获取详情
    combinationDetail: function combinationDetail() {
      var that = this;
      var data = that.id;
      (0, _activity.getCombinationDetail)(data).then(function (res) {
        that.dataShow = 1;
        uni.setNavigationBarTitle({
          title: res.data.storeCombination.storeName.substring(0, 16)
        });
        that.storeInfo = res.data.storeCombination;
        that.getProductReplyList();
        that.getProductReplyCount();
        that.imgUrls = JSON.parse(res.data.storeCombination.sliderImage);
        that.attribute.productSelect.num = res.data.storeCombination.onceNum;
        that.userCollect = res.data.userCollect;
        that.pink = res.data.pinkList || [];
        // that.pindAll = res.data.pindAll || [];
        that.itemNew = res.data.pinkOkList || [];
        that.pinkOkSum = res.data.pinkOkSum;
        that.attribute.productAttr = res.data.productAttr || [];
        that.productValue = res.data.productValue;
        that.onceNum = res.data.storeCombination.onceNum;

        //	that.PromotionCode = res.data.storeInfo.code_base

        that.getQrcode();
        that.imgTop = res.data.storeCombination.image;
        that.downloadFilestoreImage();
        var productAttr = res.data.productAttr.map(function (item) {
          return {
            attrName: item.attrName,
            attrValues: item.attrValues.split(','),
            id: item.id,
            isDel: item.isDel,
            productId: item.productId,
            type: item.type
          };
        });
        that.$set(that.attribute, 'productAttr', productAttr);
        // that.setProductSelect();
        that.DefaultSelect();
        setTimeout(function () {
          that.infoScroll();
        }, 500);
      }).catch(function (err) {
        that.$util.Tips({
          title: err
        }, {
          tab: 3
        });
      });
    },
    /**
     * 默认选中属性
     * 
     */
    DefaultSelect: function DefaultSelect() {
      var self = this;
      var productAttr = self.attribute.productAttr;
      var value = [];
      for (var key in self.productValue) {
        if (self.productValue[key].quota > 0) {
          value = self.attribute.productAttr.length ? key.split(",") : [];
          break;
        }
      }
      for (var i = 0; i < productAttr.length; i++) {
        self.$set(productAttr[i], "index", value[i]);
      }
      //sort();排序函数:数字-英文-汉字；
      var productSelect = self.productValue[value.join(",")];
      if (productSelect && productAttr.length) {
        self.$set(self.attribute.productSelect, "storeName", self.storeInfo.storeName);
        self.$set(self.attribute.productSelect, "image", productSelect.image);
        self.$set(self.attribute.productSelect, "price", productSelect.price);
        self.$set(self.attribute.productSelect, "unique", productSelect.id);
        self.$set(self.attribute.productSelect, "quota", productSelect.quota);
        self.$set(self.attribute.productSelect, "quotaShow", productSelect.quotaShow);
        self.$set(self.attribute.productSelect, "cart_num", 1);
        this.$set(this, "attrValue", value.join(","));
        this.$set(this, "attrTxt", "已选择");
      } else if (!productSelect && productAttr.length) {
        self.$set(self.attribute.productSelect, "storeName", self.storeInfo.storeName);
        self.$set(self.attribute.productSelect, "image", self.storeInfo.image);
        self.$set(self.attribute.productSelect, "price", self.storeInfo.price);
        self.$set(self.attribute.productSelect, "quota", 0);
        self.$set(self.attribute.productSelect, "quotaShow", 0);
        self.$set(self.attribute.productSelect, "unique", "");
        self.$set(self.attribute.productSelect, "cart_num", 0);
        self.$set(self, "attrValue", "");
        self.$set(self, "attrTxt", "请选择");
      } else if (!productSelect && !productAttr.length) {
        self.$set(self.attribute.productSelect, "storeName", self.storeInfo.storeName);
        self.$set(self.attribute.productSelect, "image", self.storeInfo.image);
        self.$set(self.attribute.productSelect, "price", self.storeInfo.price);
        self.$set(self.attribute.productSelect, "quota", 0);
        self.$set(self.attribute.productSelect, "unique", "");
        self.$set(self.attribute.productSelect, "cart_num", 1);
        self.$set(self, "attrValue", "");
        self.$set(self, "attrTxt", "请选择");
      }
    },
    infoScroll: function infoScroll() {
      var that = this,
        topArr = [],
        heightArr = [];
      for (var i = 0; i < that.navList.length; i++) {
        //productList
        //获取元素所在位置
        var query = uni.createSelectorQuery().in(this);
        var idView = "#past" + i;
        // if (!that.data.good_list.length && i == 2) {
        //   var idView = "#past" + 3;
        // }
        query.select(idView).boundingClientRect();
        query.exec(function (res) {
          var top = res[0].top;
          var height = res[0].height;
          topArr.push(top);
          heightArr.push(height);
          that.topArr = topArr;
          that.heightArr = heightArr;
        });
      }
      ;
    },
    // 授权后回调
    onLoadFun: function onLoadFun(e) {
      this.userInfo = e;
      app.globalData.openPages = '/pages/activity/goods_combination_details/index?id=' + this.id + '&spid=' + e.uid;
      this.combinationDetail();
      //this.downloadFilePromotionCode();
    },

    selecAttr: function selecAttr() {
      this.attribute.cartAttr = true;
    },
    onMyEvent: function onMyEvent() {
      this.$set(this.attribute, 'cartAttr', false);
      this.$set(this, 'isOpen', false);
    },
    /**
     * 购物车数量加和数量减
     * 
     */
    ChangeCartNum: function ChangeCartNum(changeValue) {
      //changeValue:是否 加|减
      //获取当前变动属性
      var productSelect = this.productValue[this.attrValue];
      if (this.buyNum === productSelect.quota) {
        return this.$util.Tips({
          title: '您已超出当前商品每人限购数量，请浏览其他商品'
        });
      }
      if (this.cart_num) {
        productSelect.cart_num = this.cart_num;
        this.attribute.productSelect.cart_num = this.cart_num;
      }
      //如果没有属性,赋值给商品默认库存
      if (productSelect === undefined && !this.attribute.productAttr.length) productSelect = this.attribute.productSelect;
      //无属性值即库存为0；不存在加减；
      if (productSelect === undefined) return;
      var quotaShow = productSelect.quota_show || 0;
      var quota = productSelect.quota || 0;
      var num = this.attribute.productSelect;
      var nums = this.storeInfo.onceNum || 0;
      //设置默认数据
      if (productSelect.cart_num == undefined) productSelect.cart_num = 1;
      if (changeValue) {
        if (num.cart_num === this.onceNum) {
          return this.$util.Tips({
            title: "\u8BE5\u5546\u54C1\u6BCF\u6B21\u9650\u8D2D".concat(this.onceNum).concat(this.storeInfo.unitName)
          });
        }
        num.cart_num++;
        var arrMin = [];
        arrMin.push(nums);
        arrMin.push(quota);
        // arrMin.push(stock);
        var minN = Math.min.apply(null, arrMin);
        if (num.cart_num >= minN) {
          this.$set(this.attribute.productSelect, "cart_num", minN ? minN : 1);
          this.$set(this, "cart_num", minN ? minN : 1);
        }
        this.$set(this, "cart_num", num.cart_num);
        this.$set(this.attribute.productSelect, "cart_num", num.cart_num);
      } else {
        num.cart_num--;
        if (num.cart_num < 1) {
          this.$set(this.attribute.productSelect, "cart_num", 1);
          this.$set(this, "cart_num", 1);
        }
        this.$set(this, "cart_num", num.cart_num);
        this.$set(this.attribute.productSelect, "cart_num", num.cart_num);
      }
    },
    attrVal: function attrVal(val) {
      this.attribute.productAttr[val.indexw].index = this.attribute.productAttr[val.indexw].attrValues[val.indexn];
    },
    /**
     * 属性变动赋值
     * 
     */
    ChangeAttr: function ChangeAttr(res) {
      this.$set(this, 'cart_num', 1);
      var productSelect = this.productValue[res];
      if (productSelect) {
        this.$set(this.attribute.productSelect, "image", productSelect.image);
        this.$set(this.attribute.productSelect, "price", productSelect.price);
        this.$set(this.attribute.productSelect, "unique", productSelect.id);
        this.$set(this.attribute.productSelect, "cart_num", 1);
        this.$set(this.attribute.productSelect, "quota", productSelect.quota);
        this.$set(this.attribute.productSelect, "quotaShow", productSelect.quotaShow);
        this.$set(this, "attrValue", res);
        this.attrTxt = "已选择";
      } else {
        this.$set(this.attribute.productSelect, "image", this.storeInfo.image);
        this.$set(this.attribute.productSelect, "price", this.storeInfo.price);
        this.$set(this.attribute.productSelect, "unique", "");
        this.$set(this.attribute.productSelect, "cart_num", 0);
        this.$set(this.attribute.productSelect, "quota", 0);
        this.$set(this.attribute.productSelect, "quotaShow", 0);
        this.$set(this, "attrValue", "");
        this.attrTxt = "已选择";
      }
    },
    // 单独购买
    goProduct: function goProduct() {
      uni.navigateTo({
        url: '/pages/goods_details/index?id=' + this.storeInfo.productId
      });
    },
    // 立即购买
    goCat: function goCat() {
      var that = this;
      var productSelect = this.productValue[this.attrValue];
      //打开属性
      if (this.isOpen) this.attribute.cartAttr = true;else this.attribute.cartAttr = !this.attribute.cartAttr;
      //只有关闭属性弹窗时进行加入购物车
      if (this.attribute.cartAttr === true && this.isOpen == false) return this.isOpen = true;
      //如果有属性,没有选择,提示用户选择
      if (this.attribute.productAttr.length && productSelect === undefined && this.isOpen == true) return that.$util.Tips({
        title: '请选择属性'
      });
      var data = {
        productId: that.storeInfo.productId,
        combinationId: parseFloat(that.id),
        cartNum: that.cart_num ? this.cart_num : this.attribute.productSelect.cart_num,
        productAttrUnique: productSelect !== undefined ? productSelect.id : '',
        isNew: true
      };
      this.$Order.getPreOrder("buyNow", [{
        "attrValueId": parseFloat(this.attribute.productSelect.unique),
        "combinationId": parseFloat(this.id),
        "productNum": parseFloat(this.cart_num ? this.cart_num : this.attribute.productSelect.cart_num),
        "productId": parseFloat(this.storeInfo.productId)
      }]);
    },
    /**
     * 收藏商品
     */
    setCollect: function setCollect() {
      var that = this;
      if (this.userCollect) {
        (0, _store.collectDel)(this.storeInfo.productId).then(function (res) {
          that.userCollect = !that.userCollect;
        });
      } else {
        (0, _store.collectAdd)(this.storeInfo.productId).then(function (res) {
          that.userCollect = !that.userCollect;
        });
      }
    },
    /**
     * 分享打开
     * 
     */
    listenerActionSheet: function listenerActionSheet() {
      if (this.isLogin == false) {
        (0, _login.toLogin)();
      } else {
        this.posters = true;
      }
    },
    // 分享关闭
    listenerActionClose: function listenerActionClose() {
      this.canvasStatus = false;
    },
    //隐藏海报
    posterImageClose: function posterImageClose() {
      this.canvasStatus = false;
    },
    //替换安全域名
    setDomain: function setDomain(url) {
      url = url ? url.toString() : '';
      //本地调试打开,生产请注销
      if (url.indexOf("https://") > -1) return url;else return url.replace('http://', 'https://');
    },
    //获取海报产品图
    downloadFilestoreImage: function downloadFilestoreImage() {
      var that = this;
      uni.downloadFile({
        url: that.setDomain(that.storeInfo.image),
        success: function success(res) {
          that.storeImage = res.tempFilePath;
        },
        fail: function fail() {
          return that.$util.Tips({
            title: ''
          });
          that.storeImage = '';
        }
      });
    },
    // app获取二维码
    downloadFileAppCode: function downloadFileAppCode() {
      var that = this;
      uni.downloadFile({
        url: that.setDomain(that.storeInfo.code_base),
        success: function success(res) {
          that.PromotionCode = res.tempFilePath;
        },
        fail: function fail() {
          return that.$util.Tips({
            title: ''
          });
          that.PromotionCode = '';
        }
      });
    },
    /**
     * 获取产品分销二维码
     * @param function successFn 下载完成回调
     * 
     */
    downloadFilePromotionCode: function downloadFilePromotionCode(successFn) {
      var that = this;
      scombinationCode(that.id).then(function (res) {
        uni.downloadFile({
          url: that.setDomain(res.data.code),
          success: function success(res) {
            that.$set(that, 'isDown', false);
            if (typeof successFn == 'function') successFn && successFn(res.tempFilePath);else that.$set(that, 'PromotionCode', res.tempFilePath);
          },
          fail: function fail() {
            that.$set(that, 'isDown', false);
            that.$set(that, 'PromotionCode', '');
          }
        });
      }).catch(function (err) {
        that.$set(that, 'isDown', false);
        that.$set(that, 'PromotionCode', '');
      });
    },
    getImageBase64: function getImageBase64(images) {
      var that = this;
      (0, _public.imageBase64)({
        url: images
      }).then(function (res) {
        that.imgTop = res.data.code;
      });
    },
    // 小程序关闭分享弹窗；
    goFriend: function goFriend() {
      this.posters = false;
    },
    /**
     * 生成海报
     */
    goPoster: function goPoster() {
      var that = this;
      uni.showLoading({
        title: '海报生成中',
        mask: true
      });
      that.posters = false;
      var arrImagesUrl = '';
      var arrImagesUrlTop = '';
      if (!that.PromotionCode) {
        uni.hideLoading();
        that.$util.Tips({
          title: that.errT
        });
        return;
      }
      uni.downloadFile({
        url: that.imgTop,
        success: function success(res) {
          arrImagesUrlTop = res.tempFilePath;
          var arrImages = [that.posterbackgd, arrImagesUrlTop, that.PromotionCode];
          var storeName = that.storeInfo.storeName;
          var price = that.storeInfo.price;
          setTimeout(function () {
            that.$util.PosterCanvas(arrImages, storeName, price, that.storeInfo.otPrice, function (tempFilePath) {
              that.posterImage = tempFilePath;
              that.canvasStatus = true;
              uni.hideLoading();
            });
          }, 500);
        }
      });
    },
    // 小程序二维码
    getQrcode: function getQrcode() {
      var that = this;
      var data = {
        pid: that.uid,
        id: that.id,
        path: 'pages/activity/goods_combination_details/index'
      };
      (0, _api.getQrcode)(data).then(function (res) {
        (0, _base64src.base64src)(res.data.code, function (res) {
          that.PromotionCode = res;
        });
      }).catch(function (err) {
        that.errT = err;
      });
    },
    // 生成二维码；
    make: function make() {
      var _this3 = this;
      var href = location.href.split('?')[0] + "?id=" + this.id + "&spread=" + this.uid;
      _uqrcode.default.make({
        canvasId: 'qrcode',
        text: href,
        size: this.qrcodeSize,
        margin: 10,
        success: function success(res) {
          _this3.PromotionCode = res;
        },
        complete: function complete(res) {},
        fail: function fail(res) {
          _this3.$util.Tips({
            title: '海报二维码生成失败！'
          });
        }
      });
    },
    /*
     * 保存到手机相册
     */

    savePosterPath: function savePosterPath() {
      var that = this;
      uni.getSetting({
        success: function success(res) {
          if (!res.authSetting['scope.writePhotosAlbum']) {
            uni.authorize({
              scope: 'scope.writePhotosAlbum',
              success: function success() {
                uni.saveImageToPhotosAlbum({
                  filePath: that.posterImage,
                  success: function success(res) {
                    that.posterImageClose();
                    that.$util.Tips({
                      title: '保存成功',
                      icon: 'success'
                    });
                  },
                  fail: function fail(res) {
                    that.$util.Tips({
                      title: '保存失败'
                    });
                  }
                });
              }
            });
          } else {
            uni.saveImageToPhotosAlbum({
              filePath: that.posterImage,
              success: function success(res) {
                that.posterImageClose();
                that.$util.Tips({
                  title: '保存成功',
                  icon: 'success'
                });
              },
              fail: function fail(res) {
                that.$util.Tips({
                  title: '保存失败'
                });
              }
            });
          }
        }
      });
    },
    setShareInfoStatus: function setShareInfoStatus() {
      var data = this.storeInfo;
      var href = location.href;
      if (this.$wechat.isWeixin()) {
        href = href.indexOf("?") === -1 ? href + "?spread=" + this.uid : href + "&spread=" + this.uid;
        var configAppMessage = {
          desc: data.storeInfo,
          title: data.storeName,
          link: href,
          imgUrl: data.image
        };
        this.$wechat.wechatEvevt(["updateAppMessageShareData", "updateTimelineShareData"], configAppMessage);
      }
    },
    scroll: function scroll(e) {
      var that = this,
        scrollY = e.detail.scrollTop;
      var opacity = scrollY / 200;
      opacity = opacity > 1 ? 1 : opacity;
      that.opacity = opacity;
      that.scrollY = scrollY;
      if (that.lock) {
        that.lock = false;
        return;
      }
      for (var i = 0; i < that.topArr.length; i++) {
        if (scrollY < that.topArr[i] - app.globalData.navHeight / 2 + that.heightArr[i]) {
          that.navActive = i;
          break;
        }
      }
    },
    tap: function tap(item, index) {
      var id = item.id;
      var index = index;
      var that = this;
      // if (!this.data.good_list.length && id == "past2") {
      //   id = "past3"
      // }
      this.toView = id;
      this.navActive = index;
      this.lock = true;
      this.scrollTop = index > 0 ? that.topArr[index] - app.globalData.navHeight / 2 : that.topArr[index];
    }
  },
  onShareAppMessage: function onShareAppMessage() {
    var that = this;
    return {
      title: that.storeInfo.storeName,
      path: app.globalData.openPages,
      imageUrl: that.storeInfo.image
    };
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 539:
/*!******************************************************************************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/goods_combination_details/index.vue?vue&type=style&index=0&id=26bb9225&scoped=true&lang=scss& ***!
  \******************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_26bb9225_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=26bb9225&scoped=true&lang=scss& */ 540);
/* harmony import */ var _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_26bb9225_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_26bb9225_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_26bb9225_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_26bb9225_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_26bb9225_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 540:
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/goods_combination_details/index.vue?vue&type=style&index=0&id=26bb9225&scoped=true&lang=scss& ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[533,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/activity/goods_combination_details/index.js.map