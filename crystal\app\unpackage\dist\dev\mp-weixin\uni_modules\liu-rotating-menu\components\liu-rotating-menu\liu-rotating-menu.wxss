
.movable-view.data-v-cc32306a {
	width: 120rpx;
	height: 120rpx;
	background: linear-gradient(120deg, #c9ab79, #cfb78d, #c9ab79);
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
	border-radius: 50%;
	color: #FFFFFF;
	font-size: 26rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
}
.item-main.data-v-cc32306a {
	width: 100rpx;
	height: 100rpx;
	background: linear-gradient(120deg, #ad4448, #b3595c, #ad4448);
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
	border-radius: 50%;
	color: #FFFFFF;
	font-size: 22rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	position: absolute;
}
.item-main1.data-v-cc32306a {
	left: -20rpx;
	top: -105rpx;
}
.item-main2.data-v-cc32306a {
	left: -105rpx;
	top: -40rpx;
}
.item-main3.data-v-cc32306a {
	left: -90rpx;
	top: 65rpx;
}
.toOut1.data-v-cc32306a {
	-webkit-animation: toOut1-data-v-cc32306a 1s;
	        animation: toOut1-data-v-cc32306a 1s;
}
.toOut2.data-v-cc32306a {
	-webkit-animation: toOut2-data-v-cc32306a 1s;
	        animation: toOut2-data-v-cc32306a 1s;
}
.toOut3.data-v-cc32306a {
	-webkit-animation: toOut3-data-v-cc32306a 1s;
	        animation: toOut3-data-v-cc32306a 1s;
}
.item-main11.data-v-cc32306a {
	right: -20rpx;
	top: -105rpx;
}
.item-main12.data-v-cc32306a {
	right: -105rpx;
	top: -40rpx;
}
.item-main13.data-v-cc32306a {
	right: -90rpx;
	top: 65rpx;
}
.toOut11.data-v-cc32306a {
	-webkit-animation: toOut11-data-v-cc32306a 1s;
	        animation: toOut11-data-v-cc32306a 1s;
}
.toOut12.data-v-cc32306a {
	-webkit-animation: toOut12-data-v-cc32306a 1s;
	        animation: toOut12-data-v-cc32306a 1s;
}
.toOut13.data-v-cc32306a {
	-webkit-animation: toOut13-data-v-cc32306a 1s;
	        animation: toOut13-data-v-cc32306a 1s;
}
@-webkit-keyframes toOut1-data-v-cc32306a {
0% {
		-webkit-transform: scale(0);
		        transform: scale(0);
		-webkit-transform-origin: bottom right;
		        transform-origin: bottom right;
}
100% {
		-webkit-transform: scale(1);
		        transform: scale(1);
		-webkit-transform-origin: top left;
		        transform-origin: top left;
}
}
@keyframes toOut1-data-v-cc32306a {
0% {
		-webkit-transform: scale(0);
		        transform: scale(0);
		-webkit-transform-origin: bottom right;
		        transform-origin: bottom right;
}
100% {
		-webkit-transform: scale(1);
		        transform: scale(1);
		-webkit-transform-origin: top left;
		        transform-origin: top left;
}
}
@-webkit-keyframes toOut2-data-v-cc32306a {
0% {
		-webkit-transform: scale(0);
		        transform: scale(0);
		-webkit-transform-origin: center right;
		        transform-origin: center right;
}
100% {
		-webkit-transform: scale(1);
		        transform: scale(1);
		-webkit-transform-origin: center left;
		        transform-origin: center left;
}
}
@keyframes toOut2-data-v-cc32306a {
0% {
		-webkit-transform: scale(0);
		        transform: scale(0);
		-webkit-transform-origin: center right;
		        transform-origin: center right;
}
100% {
		-webkit-transform: scale(1);
		        transform: scale(1);
		-webkit-transform-origin: center left;
		        transform-origin: center left;
}
}
@-webkit-keyframes toOut3-data-v-cc32306a {
0% {
		-webkit-transform: scale(0);
		        transform: scale(0);
		-webkit-transform-origin: top right;
		        transform-origin: top right;
}
100% {
		-webkit-transform: scale(1);
		        transform: scale(1);
		-webkit-transform-origin: bottom left;
		        transform-origin: bottom left;
}
}
@keyframes toOut3-data-v-cc32306a {
0% {
		-webkit-transform: scale(0);
		        transform: scale(0);
		-webkit-transform-origin: top right;
		        transform-origin: top right;
}
100% {
		-webkit-transform: scale(1);
		        transform: scale(1);
		-webkit-transform-origin: bottom left;
		        transform-origin: bottom left;
}
}
@-webkit-keyframes toOut11-data-v-cc32306a {
0% {
		-webkit-transform: scale(0);
		        transform: scale(0);
		-webkit-transform-origin: bottom left;
		        transform-origin: bottom left;
}
100% {
		-webkit-transform: scale(1);
		        transform: scale(1);
		-webkit-transform-origin: top right;
		        transform-origin: top right;
}
}
@keyframes toOut11-data-v-cc32306a {
0% {
		-webkit-transform: scale(0);
		        transform: scale(0);
		-webkit-transform-origin: bottom left;
		        transform-origin: bottom left;
}
100% {
		-webkit-transform: scale(1);
		        transform: scale(1);
		-webkit-transform-origin: top right;
		        transform-origin: top right;
}
}
@-webkit-keyframes toOut12-data-v-cc32306a {
0% {
		-webkit-transform: scale(0);
		        transform: scale(0);
		-webkit-transform-origin: center left;
		        transform-origin: center left;
}
100% {
		-webkit-transform: scale(1);
		        transform: scale(1);
		-webkit-transform-origin: center right;
		        transform-origin: center right;
}
}
@keyframes toOut12-data-v-cc32306a {
0% {
		-webkit-transform: scale(0);
		        transform: scale(0);
		-webkit-transform-origin: center left;
		        transform-origin: center left;
}
100% {
		-webkit-transform: scale(1);
		        transform: scale(1);
		-webkit-transform-origin: center right;
		        transform-origin: center right;
}
}
@-webkit-keyframes toOut13-data-v-cc32306a {
0% {
		-webkit-transform: scale(0);
		        transform: scale(0);
		-webkit-transform-origin: top left;
		        transform-origin: top left;
}
100% {
		-webkit-transform: scale(1);
		        transform: scale(1);
		-webkit-transform-origin: bottom right;
		        transform-origin: bottom right;
}
}
@keyframes toOut13-data-v-cc32306a {
0% {
		-webkit-transform: scale(0);
		        transform: scale(0);
		-webkit-transform-origin: top left;
		        transform-origin: top left;
}
100% {
		-webkit-transform: scale(1);
		        transform: scale(1);
		-webkit-transform-origin: bottom right;
		        transform-origin: bottom right;
}
}
.animation-info.data-v-cc32306a {
	transition: left .25s ease;
}
.movable-area.data-v-cc32306a {
	width: 100%;
	height: 100%;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 999999 !important;
	pointer-events: none;
}

