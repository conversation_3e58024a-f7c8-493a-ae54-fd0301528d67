{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/jyf-parser/jyf-parser.vue?26b8", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/jyf-parser/jyf-parser.vue?9fa7", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/jyf-parser/jyf-parser.vue?1df8", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/jyf-parser/jyf-parser.vue?f534", "uni-app:///components/jyf-parser/jyf-parser.vue", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/jyf-parser/jyf-parser.vue?afa7", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/components/jyf-parser/jyf-parser.vue?9724"], "names": ["fs", "<PERSON><PERSON><PERSON>", "val", "name", "data", "scaleAm", "showAm", "imgs", "nodes", "components", "trees", "props", "type", "default", "watch", "html", "mounted", "newSrc", "info", "filePath", "encoding", "success", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "methods", "<PERSON><PERSON><PERSON><PERSON>", "cache", "n", "parser", "f", "console", "uni", "title", "i", "c", "top", "bottom", "ctx", "j", "select", "height", "getText", "replace", "navigateTo", "errMsg", "scrollOffset", "exec", "obj", "<PERSON><PERSON>", "getVideoContext", "preLoad", "wait", "_load", "_tap", "scrollTop", "duration", "transform<PERSON><PERSON>in", "timingFunction", "_touchstart", "_touchmove"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACa;;;AAGtE;AACgM;AAChM,gBAAgB,2LAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAwvB,CAAgB,0rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;ACgC5wB;EAEAA;EAEAC;AACA;AACA;AACA;EACA;IACAC;EAAA;EACA;AACA;AAAA,eASA;EACAC;EACAC;IACA;MAYAC;MACAC;MACAC;MAEAC;IACA;EACA;EAEAC;IACAC;EACA;EAEAC;IACA;IAEA;MACAC;MACAC;IACA;IAEA;MACAD;MACAC;IACA;IAEA;IACA;IACA;IAEA;IAEA;IAGA;IAEA;IACA;IACA;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;MACA;QACA;MAAA;IACA;IACA;MAAA;MACA;;MAEA;MACA;QACA;QACA;UACA;UACAC;QACA;QACAA;QACA;MACA;MAEA;MACA;MACA;QACA;UAAAC;QACA;QAEAC;QACAnB;UACAmB;UACAf;UACAgB;UACAC;YAAA;UAAA;QACA;MAYA;IACA;IACA;EACA;EACAC;IAIA;MASA,iDACAtB;QACAmB;MACA;IAEA;IACAI;EACA;EACAC;IAmCAC;MAAA;MAuLA;MACA,WACA,4BACA;QACA;QACA;QACA;UACA;UACA,oBACAjB,4BACA;YACAA;YACAkB;UACA;QACA;QACA;MACA;QACA;QACA;UACA;UACA;YACA;cACA;cACAC;cACA;gBACA;cAAA;cACAC;cACA;gBACAA;gBACAC;gBACAD;cACA;YACA;UACA;QACA;QACApB;MACA;QACAA;QACAsB;MACA,OACA;MAIA;MACA,uDACA;MACA,yDACAC;QACAC;MACA;MACA;QACA;QACA;QAIA;UAAA,2BACAC;YACA;cACA;gBACA;gBACA;kBACA;kBAEA;oBACA;sBACAC;sBACAA;wBACAC;wBACAC;sBACA;wBACAF;wBACAA;sBACA;oBACA,OACAA;kBACA;gBAEA,OAEA;kBACAG;kBACAA;kBACA;gBACA;cAWA;YACA;YACA,wBACAR;UAAA;UAzCA;YAAA,IAEAS;YAAA,IAsBAD;YAAA,MAxBAJ;UA0CA;QACA;QACAJ;MAUA;MAGA;MACAN;MACA;QASA,6BAEAgB;UAEA;UACA;YACA;YACAhB;UACA;UACAiB;QAEA;MAEA;MACA;IAEA;IACAC;MAAA;MAQA;MACA;QACA,2GACAC,4BACA,qCACA;UACA;UACA,oHACA;UACA;UACA;UACA,2DACA;QACA;MACA;MACA;IAEA;IACAC;MAAA;MACA,qBACA;QACAC;MACA;MA0BA;QACAb,oHACAc,eACAC;UACA,qBACA;YACAF;UACA;UACAG;UACAhB;QACA;MACA;MACA,iCACA;QAEAiB;MAOA;IAEA;IACAC;MAEA,wCAEA;QACA;MAAA;IAEA;IACA;IACAC;MAeA;QACA;QACAnC;QACAW;MACA;MACA;MACA;QACA;UACA,mEACAyB;UACAtB;QACA;MACA;MACA;MACA;MACA,0DACA,2BACA;IAEA;IAgEAuB;MACA,uBACA;IACA;IAEAC;MAEA;QACA;QACA;UACA;UACAtB;YACAuB;YACAC;UACA;QACA;UACA;UACA;UACA;YACAC;YACAC;UACA;UAIA;UACA;UACA;UACA;QACA;QACA;QACA;MACA;MACA;IAEA;IACAC;MAEA,2BACA;IAEA;IACAC;MAEA;MACA;QACA;QACA,8EACA;QACA;QACA;QACA;QACA;QACA;MACA;IAEA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;ACvxBA;AAAA;AAAA;AAAA;AAAqkC,CAAgB,28BAAG,EAAC,C;;;;;;;;;;;ACAzlC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/jyf-parser/jyf-parser.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./jyf-parser.vue?vue&type=template&id=eab15eb8&\"\nvar renderjs\nimport script from \"./jyf-parser.vue?vue&type=script&lang=js&\"\nexport * from \"./jyf-parser.vue?vue&type=script&lang=js&\"\nimport style0 from \"./jyf-parser.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/jyf-parser/jyf-parser.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./jyf-parser.vue?vue&type=template&id=eab15eb8&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.nodes.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./jyf-parser.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./jyf-parser.vue?vue&type=script&lang=js&\"", "<!--\r\n  parser 主模块组件\r\n  github：https://github.com/jin-yufeng/Parser \r\n  docs：https://jin-yufeng.github.io/Parser\r\n  插件市场：https://ext.dcloud.net.cn/plugin?id=805\r\n  author：JinYufeng\r\n  update：2020/04/14\r\n-->\r\n<template>\r\n\t<view>\r\n\t\t<slot v-if=\"!nodes.length\" />\r\n\t\t<!--#ifdef APP-PLUS-NVUE-->\r\n\t\t<web-view id=\"top\" ref=\"web\" :src=\"src\" :style=\"'margin-top:-2px;height:'+height+'px'\" @onPostMessage=\"_message\" />\r\n\t\t<!--#endif-->\r\n\t\t<!--#ifndef APP-PLUS-NVUE-->\r\n\t\t<view id=\"top\" :style=\"showAm+(selectable?';user-select:text;-webkit-user-select:text':'')\" :animation=\"scaleAm\" @tap=\"_tap\"\r\n\t\t @touchstart=\"_touchstart\" @touchmove=\"_touchmove\">\r\n\t\t\t<!--#ifdef H5-->\r\n\t\t\t<div :id=\"'rtf'+uid\"></div>\r\n\t\t\t<!--#endif-->\r\n\t\t\t<!--#ifndef H5-->\r\n\t\t\t<trees :nodes=\"nodes\" :lazy-load=\"lazyLoad\" :loadVideo=\"loadVideo\" />\r\n\t\t\t<image v-for=\"(item, index) in imgs\" v-bind:key=\"index\" :id=\"index\" :src=\"item\" hidden @load=\"_load\" />\r\n\t\t\t<!--#endif-->\r\n\t\t</view>\r\n\t\t<!--#endif-->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t// #ifndef H5 || APP-PLUS-NVUE\r\n\timport trees from './libs/trees';\r\n\tvar cache = {},\r\n\t\t// #ifdef MP-WEIXIN || MP-TOUTIAO\r\n\t\tfs = uni.getFileSystemManager ? uni.getFileSystemManager() : null,\r\n\t\t// #endif\r\n\t\tParser = require('./libs/MpHtmlParser.js');\r\n\tvar document; // document 补丁包 https://jin-yufeng.github.io/Parser/#/instructions?id=document\r\n\t// 计算 cache 的 key\r\n\tfunction hash(str) {\r\n\t\tfor (var i = str.length, val = 5381; i--;)\r\n\t\t\tval += (val << 5) + str.charCodeAt(i);\r\n\t\treturn val;\r\n\t}\r\n\t// #endif\r\n\t// #ifdef H5 || APP-PLUS-NVUE\r\n\tvar rpx = uni.getSystemInfoSync().screenWidth / 750,\r\n\t\tcfg = require('./libs/config.js');\r\n\t// #endif\r\n\t// #ifdef APP-PLUS-NVUE\r\n\tvar dom = weex.requireModule('dom');\r\n\t// #endif\r\n\texport default {\r\n\t\tname: 'parser',\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\tloadVideo: false,\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tuid: this._uid,\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef APP-PLUS-NVUE\r\n\t\t\t\tsrc: '',\r\n\t\t\t\theight: 1,\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef APP-PLUS-NVUE\r\n\t\t\t\tscaleAm: '',\r\n\t\t\t\tshowAm: '',\r\n\t\t\t\timgs: [],\r\n\t\t\t\t// #endif\r\n\t\t\t\tnodes: []\r\n\t\t\t}\r\n\t\t},\r\n\t\t// #ifndef H5 || APP-PLUS-NVUE\r\n\t\tcomponents: {\r\n\t\t\ttrees\r\n\t\t},\r\n\t\t// #endif\r\n\t\tprops: {\r\n\t\t\t'html': null,\r\n\t\t\t// #ifndef MP-ALIPAY\r\n\t\t\t'autopause': {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// #endif\r\n\t\t\t'autosetTitle': {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// #ifndef H5 || APP-PLUS-NVUE\r\n\t\t\t'compress': Number,\r\n\t\t\t'useCache': Boolean,\r\n\t\t\t'xml': Boolean,\r\n\t\t\t// #endif\r\n\t\t\t'domain': String,\r\n\t\t\t// #ifndef MP-BAIDU || MP-ALIPAY || APP-PLUS\r\n\t\t\t'gestureZoom': Boolean,\r\n\t\t\t// #endif\r\n\t\t\t// #ifdef MP-WEIXIN || MP-QQ || H5 || APP-PLUS\r\n\t\t\t'lazyLoad': Boolean,\r\n\t\t\t// #endif\r\n\t\t\t'selectable': Boolean,\r\n\t\t\t'tagStyle': Object,\r\n\t\t\t'showWithAnimation': Boolean,\r\n\t\t\t'useAnchor': Boolean\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\thtml(html) {\r\n\t\t\t\tthis.setContent(html);\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\t// 图片数组\r\n\t\t\tthis.imgList = [];\r\n\t\t\tthis.imgList.each = function(f) {\r\n\t\t\t\tfor (var i = 0, len = this.length; i < len; i++)\r\n\t\t\t\t\tthis.setItem(i, f(this[i], i, this));\r\n\t\t\t}\r\n\t\t\tthis.imgList.setItem = function(i, src) {\r\n\t\t\t\tif (i == void 0 || !src) return;\r\n\t\t\t\t// #ifndef MP-ALIPAY || APP-PLUS\r\n\t\t\t\t// 去重\r\n\t\t\t\tif (src.indexOf('http') == 0 && this.includes(src)) {\r\n\t\t\t\t\tvar newSrc = '';\r\n\t\t\t\t\tfor (var j = 0, c; c = src[j]; j++) {\r\n\t\t\t\t\t\tif (c == '/' && src[j - 1] != '/' && src[j + 1] != '/') break;\r\n\t\t\t\t\t\tnewSrc += Math.random() > 0.5 ? c.toUpperCase() : c;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tnewSrc += src.substr(j);\r\n\t\t\t\t\treturn this[i] = newSrc;\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\tthis[i] = src;\r\n\t\t\t\t// 暂存 data src\r\n\t\t\t\tif (src.includes('data:image')) {\r\n\t\t\t\t\tvar filePath, info = src.match(/data:image\\/(\\S+?);(\\S+?),(.+)/);\r\n\t\t\t\t\tif (!info) return;\r\n\t\t\t\t\t// #ifdef MP-WEIXIN || MP-TOUTIAO\r\n\t\t\t\t\tfilePath = `${wx.env.USER_DATA_PATH}/${Date.now()}.${info[1]}`;\r\n\t\t\t\t\tfs && fs.writeFile({\r\n\t\t\t\t\t\tfilePath,\r\n\t\t\t\t\t\tdata: info[3],\r\n\t\t\t\t\t\tencoding: info[2],\r\n\t\t\t\t\t\tsuccess: () => this[i] = filePath\r\n\t\t\t\t\t})\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\t\tfilePath = `_doc/parser_tmp/${Date.now()}.${info[1]}`;\r\n\t\t\t\t\tvar bitmap = new plus.nativeObj.Bitmap();\r\n\t\t\t\t\tbitmap.loadBase64Data(src, () => {\r\n\t\t\t\t\t\tbitmap.save(filePath, {}, () => {\r\n\t\t\t\t\t\t\tbitmap.clear()\r\n\t\t\t\t\t\t\tthis[i] = filePath;\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t})\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (this.html) this.setContent(this.html);\r\n\t\t},\r\n\t\tbeforeDestroy() {\r\n\t\t\t// #ifdef H5\r\n\t\t\tif (this._observer) this._observer.disconnect();\r\n\t\t\t// #endif\r\n\t\t\tthis.imgList.each(src => {\r\n\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\tif (src && src.includes('_doc')) {\r\n\t\t\t\t\tplus.io.resolveLocalFileSystemURL(src, entry => {\r\n\t\t\t\t\t\tentry.remove();\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef MP-WEIXIN || MP-TOUTIAO\r\n\t\t\t\tif (src && src.includes(uni.env.USER_DATA_PATH))\r\n\t\t\t\t\tfs && fs.unlink({\r\n\t\t\t\t\t\tfilePath: src\r\n\t\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t})\r\n\t\t\tclearInterval(this._timer);\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// #ifdef H5 || APP-PLUS-NVUE\r\n\t\t\t_Dom2Str(nodes) {\r\n\t\t\t\tvar str = '';\r\n\t\t\t\tfor (var node of nodes) {\r\n\t\t\t\t\tif (node.type == 'text')\r\n\t\t\t\t\t\tstr += node.text;\r\n\t\t\t\t\telse {\r\n\t\t\t\t\t\tstr += ('<' + node.name);\r\n\t\t\t\t\t\tfor (var attr in node.attrs || {})\r\n\t\t\t\t\t\t\tstr += (' ' + attr + '=\"' + node.attrs[attr] + '\"');\r\n\t\t\t\t\t\tif (!node.children || !node.children.length) str += '>';\r\n\t\t\t\t\t\telse str += ('>' + this._Dom2Str(node.children) + '</' + node.name + '>');\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\treturn str;\r\n\t\t\t},\r\n\t\t\t_handleHtml(html, append) {\r\n\t\t\t\tif (typeof html != 'string') html = this._Dom2Str(html.nodes || html);\r\n\t\t\t\t// 处理 rpx\r\n\t\t\t\tif (html.includes('rpx'))\r\n\t\t\t\t\thtml = html.replace(/[0-9.]+\\s*rpx/g, $ => parseFloat($) * rpx + 'px');\r\n\t\t\t\tif (!append) {\r\n\t\t\t\t\t// 处理 tag-style 和 userAgentStyles\r\n\t\t\t\t\tvar style = '<style>@keyframes show{0%{opacity:0}100%{opacity:1}}';\r\n\t\t\t\t\tfor (var item in cfg.userAgentStyles)\r\n\t\t\t\t\t\tstyle += `${item}{${cfg.userAgentStyles[item]}}`;\r\n\t\t\t\t\tfor (item in this.tagStyle)\r\n\t\t\t\t\t\tstyle += `${item}{${this.tagStyle[item]}}`;\r\n\t\t\t\t\tstyle += '</style>';\r\n\t\t\t\t\thtml = style + html;\r\n\t\t\t\t}\r\n\t\t\t\treturn html;\r\n\t\t\t},\r\n\t\t\t// #endif\r\n\t\t\tsetContent(html, append) {\r\n\t\t\t\t// #ifdef APP-PLUS-NVUE\r\n\t\t\t\tif (!html) {\r\n\t\t\t\t\tthis.src = '';\r\n\t\t\t\t\tthis.height = 1;\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (append) return;\r\n\t\t\t\tplus.io.resolveLocalFileSystemURL('_doc', entry => {\r\n\t\t\t\t\tentry.getDirectory('parser_tmp', {\r\n\t\t\t\t\t\tcreate: true\r\n\t\t\t\t\t}, entry => {\r\n\t\t\t\t\t\tvar fileName = Date.now() + '.html';\r\n\t\t\t\t\t\tentry.getFile(fileName, {\r\n\t\t\t\t\t\t\tcreate: true\r\n\t\t\t\t\t\t}, entry => {\r\n\t\t\t\t\t\t\tentry.createWriter(writer => {\r\n\t\t\t\t\t\t\t\twriter.onwriteend = () => {\r\n\t\t\t\t\t\t\t\t\tthis.nodes = [1];\r\n\t\t\t\t\t\t\t\t\tthis.src = '_doc/parser_tmp/' + fileName;\r\n\t\t\t\t\t\t\t\t\tthis.$nextTick(function() {\r\n\t\t\t\t\t\t\t\t\t\tentry.remove();\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\thtml =\r\n\t\t\t\t\t\t\t\t\t'<meta name=\"viewport\" content=\"width=device-width,initial-scale=1,minimum-scale=1' +\r\n\t\t\t\t\t\t\t\t\t(this.selectable ? '' : ',user-scalable=no') +\r\n\t\t\t\t\t\t\t\t\t'\"><script type=\"text/javascript\" src=\"https://js.cdn.aliyun.dcloud.net.cn/dev/uni-app/uni.webview.1.5.2.js\"></' +\r\n\t\t\t\t\t\t\t\t\t'script><base href=\"' + this.domain + '\">' + this._handleHtml(html) +\r\n\t\t\t\t\t\t\t\t\t'<script>\"use strict\";function post(t){uni.postMessage({data:t})}' +\r\n\t\t\t\t\t\t\t\t\t(this.showWithAnimation ? 'document.body.style.animation=\"show .5s\",' : '') +\r\n\t\t\t\t\t\t\t\t\t'document.addEventListener(\"UniAppJSBridgeReady\",function(){post({action:\"load\",text:document.body.innerText});var t=document.getElementsByTagName(\"title\");t.length&&post({action:\"getTitle\",title:t[0].innerText});for(var e,o=document.getElementsByTagName(\"img\"),n=[],i=0,r=0;e=o[i];i++)e.onerror=function(){post({action:\"error\",source:\"img\",target:this})},e.hasAttribute(\"ignore\")||\"A\"==e.parentElement.nodeName||(e.i=r++,n.push(e.src),e.onclick=function(){post({action:\"preview\",img:{i:this.i,src:this.src}})});post({action:\"getImgList\",imgList:n});for(var a,s=document.getElementsByTagName(\"a\"),c=0;a=s[c];c++)a.onclick=function(){var t,e=this.getAttribute(\"href\");if(\"#\"==e[0]){var r=document.getElementById(e.substr(1));r&&(t=r.offsetTop)}return post({action:\"linkpress\",href:e,offset:t}),!1};;for(var u,m=document.getElementsByTagName(\"video\"),d=0;u=m[d];d++)u.style.maxWidth=\"100%\",u.onerror=function(){post({action:\"error\",source:\"video\",target:this})}' +\r\n\t\t\t\t\t\t\t\t\t(this.autopause ? ',u.onplay=function(){for(var t,e=0;t=m[e];e++)t!=this&&t.pause()}' : '') +\r\n\t\t\t\t\t\t\t\t\t';for(var g,l=document.getElementsByTagName(\"audio\"),p=0;g=l[p];p++)g.onerror=function(){post({action:\"error\",source:\"audio\",target:this})};window.onload=function(){post({action:\"ready\",height:document.body.scrollHeight})}});</' +\r\n\t\t\t\t\t\t\t\t\t'script>';\r\n\t\t\t\t\t\t\t\twriter.write(html);\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tif (!html) {\r\n\t\t\t\t\tif (this.rtf && !append) this.rtf.parentNode.removeChild(this.rtf);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tvar div = document.createElement('div');\r\n\t\t\t\tif (!append) {\r\n\t\t\t\t\tif (this.rtf) this.rtf.parentNode.removeChild(this.rtf);\r\n\t\t\t\t\tthis.rtf = div;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tif (!this.rtf) this.rtf = div;\r\n\t\t\t\t\telse this.rtf.appendChild(div);\r\n\t\t\t\t}\r\n\t\t\t\tdiv.innerHTML = this._handleHtml(html, append);\r\n\t\t\t\tfor (var styles = this.rtf.getElementsByTagName('style'), i = 0, style; style = styles[i++];) {\r\n\t\t\t\t\tstyle.innerHTML = style.innerHTML.replace(/body/g, '#rtf' + this._uid);\r\n\t\t\t\t\tstyle.setAttribute('scoped', 'true');\r\n\t\t\t\t}\r\n\t\t\t\t// 懒加载\r\n\t\t\t\tif (!this._observer && this.lazyLoad && IntersectionObserver) {\r\n\t\t\t\t\tthis._observer = new IntersectionObserver(changes => {\r\n\t\t\t\t\t\tfor (let item, i = 0; item = changes[i++];) {\r\n\t\t\t\t\t\t\tif (item.isIntersecting) {\r\n\t\t\t\t\t\t\t\titem.target.src = item.target.getAttribute('data-src');\r\n\t\t\t\t\t\t\t\titem.target.removeAttribute('data-src');\r\n\t\t\t\t\t\t\t\tthis._observer.unobserve(item.target);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\trootMargin: '900px 0px 900px 0px'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tvar _ts = this;\r\n\t\t\t\t// 获取标题\r\n\t\t\t\tvar title = this.rtf.getElementsByTagName('title');\r\n\t\t\t\tif (title.length && this.autosetTitle)\r\n\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\ttitle: title[0].innerText\r\n\t\t\t\t\t})\r\n\t\t\t\t// 图片处理\r\n\t\t\t\tthis.imgList.length = 0;\r\n\t\t\t\tvar imgs = this.rtf.getElementsByTagName('img');\r\n\t\t\t\tfor (let i = 0, j = 0, img; img = imgs[i]; i++) {\r\n\t\t\t\t\timg.style.maxWidth = '100%';\r\n\t\t\t\t\tvar src = img.getAttribute('src');\r\n\t\t\t\t\tif (this.domain && src) {\r\n\t\t\t\t\t\tif (src[0] == '/') {\r\n\t\t\t\t\t\t\tif (src[1] == '/')\r\n\t\t\t\t\t\t\t\timg.src = (this.domain.includes('://') ? this.domain.split('://')[0] : '') + ':' + src;\r\n\t\t\t\t\t\t\telse img.src = this.domain + src;\r\n\t\t\t\t\t\t} else if (!src.includes('://')) img.src = this.domain + '/' + src;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (!img.hasAttribute('ignore') && img.parentElement.nodeName != 'A') {\r\n\t\t\t\t\t\timg.i = j++;\r\n\t\t\t\t\t\t_ts.imgList.push(img.src || img.getAttribute('data-src'));\r\n\t\t\t\t\t\timg.onclick = function() {\r\n\t\t\t\t\t\t\tvar preview = true;\r\n\t\t\t\t\t\t\tthis.ignore = () => preview = false;\r\n\t\t\t\t\t\t\t_ts.$emit('imgtap', this);\r\n\t\t\t\t\t\t\tif (preview) {\r\n\t\t\t\t\t\t\t\tuni.previewImage({\r\n\t\t\t\t\t\t\t\t\tcurrent: this.i,\r\n\t\t\t\t\t\t\t\t\turls: _ts.imgList\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\timg.onerror = function() {\r\n\t\t\t\t\t\t_ts.$emit('error', {\r\n\t\t\t\t\t\t\tsource: 'img',\r\n\t\t\t\t\t\t\ttarget: this\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (_ts.lazyLoad && this._observer && img.src && img.i != 0) {\r\n\t\t\t\t\t\timg.setAttribute('data-src', img.src);\r\n\t\t\t\t\t\timg.removeAttribute('src');\r\n\t\t\t\t\t\tthis._observer.observe(img);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// 链接处理\r\n\t\t\t\tvar links = this.rtf.getElementsByTagName('a');\r\n\t\t\t\tfor (var link of links) {\r\n\t\t\t\t\tlink.onclick = function() {\r\n\t\t\t\t\t\tvar jump = true,\r\n\t\t\t\t\t\t\thref = this.getAttribute('href');\r\n\t\t\t\t\t\t_ts.$emit('linkpress', {\r\n\t\t\t\t\t\t\thref,\r\n\t\t\t\t\t\t\tignore: () => jump = false\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tif (jump && href) {\r\n\t\t\t\t\t\t\tif (href[0] == '#') {\r\n\t\t\t\t\t\t\t\tif (_ts.useAnchor) {\r\n\t\t\t\t\t\t\t\t\t_ts.navigateTo({\r\n\t\t\t\t\t\t\t\t\t\tid: href.substr(1)\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t} else if (href.indexOf('http') == 0 || href.indexOf('//') == 0)\r\n\t\t\t\t\t\t\t\treturn true;\r\n\t\t\t\t\t\t\telse {\r\n\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\turl: href\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// 视频处理\r\n\t\t\t\tvar videos = this.rtf.getElementsByTagName('video');\r\n\t\t\t\t_ts.videoContexts = videos;\r\n\t\t\t\tfor (let video, i = 0; video = videos[i++];) {\r\n\t\t\t\t\tvideo.style.maxWidth = '100%';\r\n\t\t\t\t\tvideo.onerror = function() {\r\n\t\t\t\t\t\t_ts.$emit('error', {\r\n\t\t\t\t\t\t\tsource: 'video',\r\n\t\t\t\t\t\t\ttarget: this\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t\tvideo.onplay = function() {\r\n\t\t\t\t\t\tif (_ts.autopause)\r\n\t\t\t\t\t\t\tfor (let item, i = 0; item = _ts.videoContexts[i++];)\r\n\t\t\t\t\t\t\t\tif (item != this) item.pause();\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// 音频处理\r\n\t\t\t\tvar audios = this.rtf.getElementsByTagName('audios');\r\n\t\t\t\tfor (var audio of audios)\r\n\t\t\t\t\taudio.onerror = function() {\r\n\t\t\t\t\t\t_ts.$emit('error', {\r\n\t\t\t\t\t\t\tsource: 'audio',\r\n\t\t\t\t\t\t\ttarget: this\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\tthis.document = this.rtf;\r\n\t\t\t\tif (!append) document.getElementById('rtf' + this._uid).appendChild(this.rtf);\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tthis.nodes = [1];\r\n\t\t\t\t\tthis.$emit('load');\r\n\t\t\t\t})\r\n\t\t\t\tsetTimeout(() => this.showAm = '', 500);\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef H5 || APP-PLUS-NVUE\r\n\t\t\t\tvar nodes;\r\n\t\t\t\tif (!html)\r\n\t\t\t\t\treturn this.nodes = [];\r\n\t\t\t\telse if (typeof html == 'string') {\r\n\t\t\t\t\tlet parser = new Parser(html, this);\r\n\t\t\t\t\t// 缓存读取\r\n\t\t\t\t\tif (this.useCache) {\r\n\t\t\t\t\t\tvar hashVal = hash(html);\r\n\t\t\t\t\t\tif (cache[hashVal])\r\n\t\t\t\t\t\t\tnodes = cache[hashVal];\r\n\t\t\t\t\t\telse {\r\n\t\t\t\t\t\t\tnodes = parser.parse();\r\n\t\t\t\t\t\t\tcache[hashVal] = nodes;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else nodes = parser.parse();\r\n\t\t\t\t\tthis.$emit('parse', nodes);\r\n\t\t\t\t} else if (Object.prototype.toString.call(html) == '[object Array]') {\r\n\t\t\t\t\t// 非本插件产生的 array 需要进行一些转换\r\n\t\t\t\t\tif (html.length && html[0].PoweredBy != 'Parser') {\r\n\t\t\t\t\t\tlet parser = new Parser(html, this);\r\n\t\t\t\t\t\t(function f(ns) {\r\n\t\t\t\t\t\t\tfor (var i = 0, n; n = ns[i]; i++) {\r\n\t\t\t\t\t\t\t\tif (n.type == 'text') continue;\r\n\t\t\t\t\t\t\t\tn.attrs = n.attrs || {};\r\n\t\t\t\t\t\t\t\tfor (var item in n.attrs)\r\n\t\t\t\t\t\t\t\t\tif (typeof n.attrs[item] != 'string') n.attrs[item] = n.attrs[item].toString();\r\n\t\t\t\t\t\t\t\tparser.matchAttr(n, parser);\r\n\t\t\t\t\t\t\t\tif (n.children && n.children.length) {\r\n\t\t\t\t\t\t\t\t\tparser.STACK.push(n);\r\n\t\t\t\t\t\t\t\t\tf(n.children);\r\n\t\t\t\t\t\t\t\t\tparser.popNode(parser.STACK.pop());\r\n\t\t\t\t\t\t\t\t} else n.children = void 0;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})(html);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tnodes = html;\r\n\t\t\t\t} else if (typeof html == 'object' && html.nodes) {\r\n\t\t\t\t\tnodes = html.nodes;\r\n\t\t\t\t\tconsole.warn('错误的 html 类型：object 类型已废弃');\r\n\t\t\t\t} else\r\n\t\t\t\t\treturn console.warn('错误的 html 类型：' + typeof html);\r\n\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\tthis.loadVideo = false;\r\n\t\t\t\t// #endif\r\n\t\t\t\tif (document) this.document = new document(this.nodes, 'nodes', this);\r\n\t\t\t\tif (append) this.nodes = this.nodes.concat(nodes);\r\n\t\t\t\telse this.nodes = nodes;\r\n\t\t\t\tif (nodes.length && nodes[0].title && this.autosetTitle)\r\n\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\ttitle: nodes[0].title\r\n\t\t\t\t\t})\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tthis.imgList.length = 0;\r\n\t\t\t\t\tthis.videoContexts = [];\r\n\t\t\t\t\t// #ifdef MP-TOUTIAO\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\tvar f = (cs) => {\r\n\t\t\t\t\t\t\tfor (let i = 0, c; c = cs[i++];) {\r\n\t\t\t\t\t\t\t\tif (c.$options.name == 'trees') {\r\n\t\t\t\t\t\t\t\t\tfor (var j = c.nodes.length, item; item = c.nodes[--j];) {\r\n\t\t\t\t\t\t\t\t\t\tif (item.c) continue;\r\n\t\t\t\t\t\t\t\t\t\tif (item.name == 'img') {\r\n\t\t\t\t\t\t\t\t\t\t\tthis.imgList.setItem(item.attrs.i, item.attrs.src);\r\n\t\t\t\t\t\t\t\t\t\t\t// #ifndef MP-ALIPAY\r\n\t\t\t\t\t\t\t\t\t\t\tif (!c.observer && !c.imgLoad && item.attrs.i != '0') {\r\n\t\t\t\t\t\t\t\t\t\t\t\tif (this.lazyLoad && uni.createIntersectionObserver) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tc.observer = uni.createIntersectionObserver(c);\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tc.observer.relativeToViewport({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttop: 900,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tbottom: 900\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}).observe('._img', () => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tc.imgLoad = true;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tc.observer.disconnect();\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t\t\t} else\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tc.imgLoad = true;\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t// #ifndef MP-ALIPAY\r\n\t\t\t\t\t\t\t\t\t\telse if (item.name == 'video') {\r\n\t\t\t\t\t\t\t\t\t\t\tvar ctx = uni.createVideoContext(item.attrs.id, c);\r\n\t\t\t\t\t\t\t\t\t\t\tctx.id = item.attrs.id;\r\n\t\t\t\t\t\t\t\t\t\t\tthis.videoContexts.push(ctx);\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t\t\t\t// #ifdef MP-BAIDU || MP-ALIPAY || APP-PLUS\r\n\t\t\t\t\t\t\t\t\t\tif (item.attrs && item.attrs.id) {\r\n\t\t\t\t\t\t\t\t\t\t\tthis.anchors = this.anchors || [];\r\n\t\t\t\t\t\t\t\t\t\t\tthis.anchors.push({\r\n\t\t\t\t\t\t\t\t\t\t\t\tid: item.attrs.id,\r\n\t\t\t\t\t\t\t\t\t\t\t\tnode: c\r\n\t\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tif (c.$children.length)\r\n\t\t\t\t\t\t\t\t\tf(c.$children)\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tf(this.$children);\r\n\t\t\t\t\t\t// #ifdef MP-TOUTIAO\r\n\t\t\t\t\t}, 200)\r\n\t\t\t\t\tthis.$emit('load');\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.loadVideo = true;\r\n\t\t\t\t\t}, 3000);\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef APP-PLUS-NVUE\r\n\t\t\t\tvar height;\r\n\t\t\t\tclearInterval(this._timer);\r\n\t\t\t\tthis._timer = setInterval(() => {\r\n\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\tvar res = [this.rtf.getBoundingClientRect()];\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifndef H5\r\n\t\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\t\tuni.createSelectorQuery().in(this)\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifndef APP-PLUS\r\n\t\t\t\t\tthis.createSelectorQuery()\r\n\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t.select('#top').boundingClientRect().exec(res => {\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t\tthis.width = res[0].width;\r\n\t\t\t\t\t\t\tif (res[0].height == height) {\r\n\t\t\t\t\t\t\t\tthis.$emit('ready', res[0])\r\n\t\t\t\t\t\t\t\tclearInterval(this._timer);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\theight = res[0].height;\r\n\t\t\t\t\t\t\t// #ifndef H5\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t}, 350)\r\n\t\t\t\tif (this.showWithAnimation && !append) this.showAm = 'animation:show .5s';\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tgetText(ns = this.nodes) {\r\n\t\t\t\t// #ifdef APP-PLUS-NVUE\r\n\t\t\t\treturn this._text;\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\treturn this.rtf.innerText;\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef H5 || APP-PLUS-NVUE\r\n\t\t\t\tvar txt = '';\r\n\t\t\t\tfor (var i = 0, n; n = ns[i++];) {\r\n\t\t\t\t\tif (n.type == 'text') txt += n.text.replace(/&nbsp;/g, '\\u00A0').replace(/&lt;/g, '<').replace(/&gt;/g, '>')\r\n\t\t\t\t\t\t.replace(/&amp;/g, '&');\r\n\t\t\t\t\telse if (n.type == 'br') txt += '\\n';\r\n\t\t\t\t\telse {\r\n\t\t\t\t\t\t// 块级标签前后加换行\r\n\t\t\t\t\t\tvar block = n.name == 'p' || n.name == 'div' || n.name == 'tr' || n.name == 'li' || (n.name[0] == 'h' && n.name[1] >\r\n\t\t\t\t\t\t\t'0' && n.name[1] < '7');\r\n\t\t\t\t\t\tif (block && txt && txt[txt.length - 1] != '\\n') txt += '\\n';\r\n\t\t\t\t\t\tif (n.children) txt += this.getText(n.children);\r\n\t\t\t\t\t\tif (block && txt[txt.length - 1] != '\\n') txt += '\\n';\r\n\t\t\t\t\t\telse if (n.name == 'td' || n.name == 'th') txt += '\\t';\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\treturn txt;\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tnavigateTo(obj) {\r\n\t\t\t\tif (!this.useAnchor)\r\n\t\t\t\t\treturn obj.fail && obj.fail({\r\n\t\t\t\t\t\terrMsg: 'Anchor is disabled'\r\n\t\t\t\t\t})\r\n\t\t\t\t// #ifdef APP-PLUS-NVUE\r\n\t\t\t\tif (!obj.id)\r\n\t\t\t\t\tdom.scrollToElement(this.$refs.web);\r\n\t\t\t\telse\r\n\t\t\t\t\tthis.$refs.web.evalJs('var pos=document.getElementById(\"' + obj.id +\r\n\t\t\t\t\t\t'\");if(pos)post({action:\"linkpress\",href:\"#\",offset:pos.offsetTop})');\r\n\t\t\t\treturn obj.success && obj.success({\r\n\t\t\t\t\terrMsg: 'pageScrollTo:ok'\r\n\t\t\t\t});\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tif (!obj.id) {\r\n\t\t\t\t\twindow.scrollTo(0, this.rtf.offsetTop);\r\n\t\t\t\t\treturn obj.success && obj.success({\r\n\t\t\t\t\t\terrMsg: 'pageScrollTo:ok'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\tvar target = document.getElementById(obj.id);\r\n\t\t\t\tif (!target) return obj.fail && obj.fail({\r\n\t\t\t\t\terrMsg: 'Label not found'\r\n\t\t\t\t});\r\n\t\t\t\tobj.scrollTop = this.rtf.offsetTop + target.offsetTop;\r\n\t\t\t\tuni.pageScrollTo(obj);\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef H5\r\n\t\t\t\tvar Scroll = (selector, component) => {\r\n\t\t\t\t\tuni.createSelectorQuery().in(component ? component : this).select(selector).boundingClientRect().selectViewport()\r\n\t\t\t\t\t\t.scrollOffset()\r\n\t\t\t\t\t\t.exec(res => {\r\n\t\t\t\t\t\t\tif (!res || !res[0])\r\n\t\t\t\t\t\t\t\treturn obj.fail && obj.fail({\r\n\t\t\t\t\t\t\t\t\terrMsg: 'Label not found'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\tobj.scrollTop = res[1].scrollTop + res[0].top;\r\n\t\t\t\t\t\t\tuni.pageScrollTo(obj);\r\n\t\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tif (!obj.id) Scroll('#top');\r\n\t\t\t\telse {\r\n\t\t\t\t\t// #ifndef MP-BAIDU || MP-ALIPAY || APP-PLUS\r\n\t\t\t\t\tScroll('#top >>> #' + obj.id + ', #top >>> .' + obj.id);\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifdef MP-BAIDU || MP-ALIPAY || APP-PLUS\r\n\t\t\t\t\tfor (var anchor of this.anchors)\r\n\t\t\t\t\t\tif (anchor.id == obj.id)\r\n\t\t\t\t\t\t\tScroll('#' + obj.id + ', .' + obj.id, anchor.node);\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tgetVideoContext(id) {\r\n\t\t\t\t// #ifndef APP-PLUS-NVUE\r\n\t\t\t\tif (!id) return this.videoContexts;\r\n\t\t\t\telse\r\n\t\t\t\t\tfor (var i = this.videoContexts.length; i--;)\r\n\t\t\t\t\t\tif (this.videoContexts[i].id == id) return this.videoContexts[i];\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\t// 预加载\r\n\t\t\tpreLoad(html, num) {\r\n\t\t\t\t// #ifdef H5 || APP-PLUS-NVUE\r\n\t\t\t\tif (html.constructor == Array)\r\n\t\t\t\t\thtml = this._Dom2Str(html);\r\n\t\t\t\tvar script = \"var contain=document.createElement('div');contain.innerHTML='\" + html.replace(/'/g, \"\\\\'\") +\r\n\t\t\t\t\t\"';for(var imgs=contain.querySelectorAll('img'),i=imgs.length-1;i>=\" + num +\r\n\t\t\t\t\t\";i--)imgs[i].removeAttribute('src');\";\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef APP-PLUS-NVUE\r\n\t\t\t\tthis.$refs.web.evalJs(script);\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\teval(script);\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef H5 || APP-PLUS-NVUE\r\n\t\t\t\tif (typeof html == 'string') {\r\n\t\t\t\t\tvar id = hash(html);\r\n\t\t\t\t\thtml = new Parser(html, this).parse();\r\n\t\t\t\t\tcache[id] = html;\r\n\t\t\t\t}\r\n\t\t\t\tvar wait = [];\r\n\t\t\t\t(function f(ns) {\r\n\t\t\t\t\tfor (var i = 0, n; n = ns[i++];) {\r\n\t\t\t\t\t\tif (n.name == 'img' && n.attrs.src && !wait.includes(n.attrs.src))\r\n\t\t\t\t\t\t\twait.push(n.attrs.src);\r\n\t\t\t\t\t\tf(n.children || []);\r\n\t\t\t\t\t}\r\n\t\t\t\t})(html);\r\n\t\t\t\tif (num) wait = wait.slice(0, num);\r\n\t\t\t\tthis._wait = (this._wait || []).concat(wait);\r\n\t\t\t\tif (!this.imgs) this.imgs = this._wait.splice(0, 15);\r\n\t\t\t\telse if (this.imgs.length < 15)\r\n\t\t\t\t\tthis.imgs = this.imgs.concat(this._wait.splice(0, 15 - this.imgs.length));\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\t// #ifdef APP-PLUS-NVUE\r\n\t\t\t_message(e) {\r\n\t\t\t\t// 接收 web-view 消息\r\n\t\t\t\tvar data = e.detail.data[0];\r\n\t\t\t\tif (data.action == 'load') {\r\n\t\t\t\t\tthis.$emit('load');\r\n\t\t\t\t\tthis._text = data.text;\r\n\t\t\t\t} else if (data.action == 'getTitle') {\r\n\t\t\t\t\tif (this.autosetTitle)\r\n\t\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\t\ttitle: data.title\r\n\t\t\t\t\t\t})\r\n\t\t\t\t} else if (data.action == 'getImgList') {\r\n\t\t\t\t\tthis.imgList.length = 0;\r\n\t\t\t\t\tfor (var i = data.imgList.length; i--;)\r\n\t\t\t\t\t\tthis.imgList.setItem(i, data.imgList[i]);\r\n\t\t\t\t} else if (data.action == 'preview') {\r\n\t\t\t\t\tvar preview = true;\r\n\t\t\t\t\tdata.img.ignore = () => preview = false;\r\n\t\t\t\t\tthis.$emit('imgtap', data.img);\r\n\t\t\t\t\tif (preview)\r\n\t\t\t\t\t\tuni.previewImage({\r\n\t\t\t\t\t\t\tcurrent: data.img.i,\r\n\t\t\t\t\t\t\turls: this.imgList\r\n\t\t\t\t\t\t})\r\n\t\t\t\t} else if (data.action == 'linkpress') {\r\n\t\t\t\t\tvar jump = true,\r\n\t\t\t\t\t\thref = data.href;\r\n\t\t\t\t\tthis.$emit('linkpress', {\r\n\t\t\t\t\t\thref,\r\n\t\t\t\t\t\tignore: () => jump = false\r\n\t\t\t\t\t})\r\n\t\t\t\t\tif (jump && href) {\r\n\t\t\t\t\t\tif (href[0] == '#') {\r\n\t\t\t\t\t\t\tif (this.useAnchor)\r\n\t\t\t\t\t\t\t\tdom.scrollToElement(this.$refs.web, {\r\n\t\t\t\t\t\t\t\t\toffset: data.offset\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t} else if (href.includes('://'))\r\n\t\t\t\t\t\t\tplus.runtime.openWeb(href);\r\n\t\t\t\t\t\telse\r\n\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\turl: href\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t} else if (data.action == 'error')\r\n\t\t\t\t\tthis.$emit('error', {\r\n\t\t\t\t\t\tsource: data.source,\r\n\t\t\t\t\t\ttarget: data.target\r\n\t\t\t\t\t})\r\n\t\t\t\telse if (data.action == 'ready') {\r\n\t\t\t\t\tthis.height = data.height;\r\n\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\tuni.createSelectorQuery().in(this).select('#top').boundingClientRect().exec(res => {\r\n\t\t\t\t\t\t\tthis.rect = res[0];\r\n\t\t\t\t\t\t\tthis.$emit('ready', res[0]);\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// #endif\r\n\t\t\t// #ifndef APP-PLUS-NVUE\r\n\t\t\t// #ifndef H5\r\n\t\t\t_load(e) {\r\n\t\t\t\tif (this._wait.length)\r\n\t\t\t\t\tthis.$set(this.imgs, e.target.id, this._wait.shift());\r\n\t\t\t},\r\n\t\t\t// #endif\r\n\t\t\t_tap(e) {\r\n\t\t\t\t// #ifndef MP-BAIDU || MP-ALIPAY || APP-PLUS\r\n\t\t\t\tif (this.gestureZoom && e.timeStamp - this._lastT < 300) {\r\n\t\t\t\t\tvar initY = e.touches[0].pageY - e.currentTarget.offsetTop;\r\n\t\t\t\t\tif (this._zoom) {\r\n\t\t\t\t\t\tthis._scaleAm.translateX(0).scale(1).step();\r\n\t\t\t\t\t\tuni.pageScrollTo({\r\n\t\t\t\t\t\t\tscrollTop: (initY + this._initY) / 2 - e.touches[0].clientY,\r\n\t\t\t\t\t\t\tduration: 400\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tvar initX = e.touches[0].pageX - e.currentTarget.offsetLeft;\r\n\t\t\t\t\t\tthis._initY = initY;\r\n\t\t\t\t\t\tthis._scaleAm = uni.createAnimation({\r\n\t\t\t\t\t\t\ttransformOrigin: `${initX}px ${this._initY}px 0`,\r\n\t\t\t\t\t\t\ttimingFunction: 'ease-in-out'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\t// #ifdef MP-TOUTIAO\r\n\t\t\t\t\t\tthis._scaleAm.opacity(1);\r\n\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\tthis._scaleAm.scale(2).step();\r\n\t\t\t\t\t\tthis._tMax = initX / 2;\r\n\t\t\t\t\t\tthis._tMin = (initX - this.width) / 2;\r\n\t\t\t\t\t\tthis._tX = 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis._zoom = !this._zoom;\r\n\t\t\t\t\tthis.scaleAm = this._scaleAm.export();\r\n\t\t\t\t}\r\n\t\t\t\tthis._lastT = e.timeStamp;\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\t_touchstart(e) {\r\n\t\t\t\t// #ifndef MP-BAIDU || MP-ALIPAY || APP-PLUS\r\n\t\t\t\tif (e.touches.length == 1)\r\n\t\t\t\t\tthis._initX = this._lastX = e.touches[0].pageX;\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\t_touchmove(e) {\r\n\t\t\t\t// #ifndef MP-BAIDU || MP-ALIPAY || APP-PLUS\r\n\t\t\t\tvar diff = e.touches[0].pageX - this._lastX;\r\n\t\t\t\tif (this._zoom && e.touches.length == 1 && Math.abs(diff) > 20) {\r\n\t\t\t\t\tthis._lastX = e.touches[0].pageX;\r\n\t\t\t\t\tif ((this._tX <= this._tMin && diff < 0) || (this._tX >= this._tMax && diff > 0))\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\tthis._tX += (diff * Math.abs(this._lastX - this._initX) * 0.05);\r\n\t\t\t\t\tif (this._tX < this._tMin) this._tX = this._tMin;\r\n\t\t\t\t\tif (this._tX > this._tMax) this._tX = this._tMax;\r\n\t\t\t\t\tthis._scaleAm.translateX(this._tX).step();\r\n\t\t\t\t\tthis.scaleAm = this._scaleAm.export();\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t@keyframes show {\r\n\t\t0% {\r\n\t\t\topacity: 0\r\n\t\t}\r\n\r\n\t\t100% {\r\n\t\t\topacity: 1;\r\n\t\t}\r\n\t}\r\n\r\n\t/* #ifdef MP-WEIXIN */\r\n\t:host {\r\n\t\tdisplay: block;\r\n\t\toverflow: scroll;\r\n\t\t-webkit-overflow-scrolling: touch;\r\n\t}\r\n\r\n\t/* #endif */\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./jyf-parser.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./jyf-parser.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363900334\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}