{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\taoBao.vue?vue&type=template&id=74cf9dd4&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\taoBao.vue", "mtime": 1753666157925}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\n<div class=\"Box\">\n  <el-card>\n    <div class=\"line-ht\">生成的商品默认是没有上架的，请手动上架商品！\n      <span v-if=\"copyConfig.copyType && copyConfig.copyType==1\">您当前剩余{{copyConfig.copyNum}}条采集次数，\n        <router-link :to=\"{path:'/operation/systemSms/pay?type=copy'}\">\n          <span style=\"color:#1890ff;\">增加采集次数</span>\n        </router-link>\n      </span>\n      <el-link v-if=\"copyConfig.copyType && copyConfig.copyType!=1\" type=\"primary\" :underline=\"false\"\n               href=\"https://help.crmeb.net/crmeb_java/2103903\" target=\"_blank\">如何配置密钥\n      </el-link>\n      <br>\n      商品采集设置：设置 > 系统设置 > 第三方接口设置 > 采集商品配置（如配置一号通采集，请先登录一号通账号，无一号通，请选择99Api设置）\n    </div>\n  </el-card>\n  <el-form class=\"formValidate mt20\" ref=\"formValidate\" :model=\"formValidate\" :rules=\"ruleInline\" label-width=\"120px\"\n           @submit.native.prevent v-loading=\"loading\">\n    <el-form-item v-if=\"copyConfig.copyType && copyConfig.copyType!=1\">\n      <el-radio-group v-model=\"form\">\n        <el-radio :label=\"1\">淘宝</el-radio>\n        <el-radio :label=\"2\">京东</el-radio>\n        <!--<el-radio :label=\"3\">苏宁</el-radio>-->\n        <!--<el-radio :label=\"4\">拼多多</el-radio>-->\n        <el-radio :label=\"5\">天猫</el-radio>\n      </el-radio-group>\n    </el-form-item>\n    <el-row :gutter=\"24\">\n      <el-col :span=\"24\" v-if=\"copyConfig.copyType\">\n        <el-form-item label=\"链接地址：\">\n          <el-input v-model=\"url\" placeholder=\"请输入链接地址\" class=\"selWidth\" size=\"small\">\n            <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"add\" size=\"small\" v-hasPermi=\"['admin:product:copy:product','admin:product:import:product']\" />\n          </el-input>\n        </el-form-item>\n      </el-col>\n      <el-col v-if=\"formValidate\">\n        <el-col :span=\"24\">\n          <el-form-item label=\"商品名称：\" prop=\"storeName\">\n            <el-input v-model=\"formValidate.storeName\" maxlength=\"249\" placeholder=\"请输入商品名称\"></el-input>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"24\">\n          <el-form-item label=\"商品简介：\">\n            <el-input v-model=\"formValidate.storeInfo\" maxlength=\"250\" type=\"textarea\" :rows=\"3\"\n                      placeholder=\"请输入商品简介\"></el-input>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"24\">\n          <el-form-item label=\"商品分类：\" prop=\"cateIds\">\n            <el-cascader v-model=\"formValidate.cateIds\" :options=\"merCateList\" :props=\"props2\" clearable\n                         class=\"selWidth\" :show-all-levels=\"false\"/>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"24\">\n          <el-form-item label=\"商品关键字：\" prop=\"keyword\">\n            <el-input v-model=\"formValidate.keyword\" placeholder=\"请输入商品关键字\"></el-input>\n          </el-form-item>\n        </el-col>\n        <el-col v-bind=\"grid\">\n          <el-form-item label=\"单位：\" prop=\"unitName\">\n            <el-input v-model=\"formValidate.unitName\" placeholder=\"请输入单位\" class=\"selWidth\"></el-input>\n          </el-form-item>\n        </el-col>\n        <el-col v-bind=\"grid\">\n          <el-form-item label=\"运费模板：\" prop=\"tempId\">\n            <el-select v-model=\"formValidate.tempId\" placeholder=\"请选择\" class=\"selWidth\">\n              <el-option\n                v-for=\"item in shippingList\"\n                :key=\"item.id\"\n                :label=\"item.name\"\n                :value=\"item.id\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"24\">\n          <el-form-item label=\"商品封面图：\" prop=\"image\">\n            <div class=\"upLoadPicBox\" @click=\"modalPicTap('1')\">\n              <div v-if=\"formValidate.image\" class=\"pictrue\"><img :src=\"formValidate.image\"></div>\n              <div v-else class=\"upLoad\">\n                <i class=\"el-icon-camera cameraIconfont\"/>\n              </div>\n            </div>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"24\">\n          <el-form-item label=\"商品轮播图：\">\n            <div class=\"acea-row\">\n              <div\n                v-for=\"(item,index) in formValidate.sliderImages\"\n                :key=\"index\"\n                class=\"lunBox mr5\"\n                draggable=\"false\"\n                @dragstart=\"handleDragStart($event, item)\"\n                @dragover.prevent=\"handleDragOver($event, item)\"\n                @dragenter=\"handleDragEnter($event, item)\"\n                @dragend=\"handleDragEnd($event, item)\"\n              >\n                <div class=\"pictrue\"><img :src=\"item\"></div>\n                <el-button-group>\n                  <el-button size=\"mini\" @click.native=\"checked(item,index)\">主图</el-button>\n                  <el-button size=\"mini\" @click.native=\"handleRemove(index)\">移除</el-button>\n                </el-button-group>\n              </div>\n            </div>\n          </el-form-item>\n        </el-col>\n        <el-col v-if=\"formValidate.specType || formValidate.attr.length\" :span=\"24\" class=\"noForm\">\n          <el-form-item label=\"批量设置：\" class=\"labeltop\">\n            <el-table :data=\"oneFormBatch\" border class=\"tabNumWidth\" size=\"mini\">\n              <el-table-column align=\"center\" label=\"图片\" min-width=\"80\">\n                <template slot-scope=\"scope\">\n                  <div class=\"upLoadPicBox\" @click=\"modalPicTap('1','pi')\">\n                    <div v-if=\"scope.row.image\" class=\"pictrue pictrueTab\"><img :src=\"scope.row.image\"></div>\n                    <div v-else class=\"upLoad pictrueTab\">\n                      <i class=\"el-icon-camera cameraIconfont\"/>\n                    </div>\n                  </div>\n                </template>\n              </el-table-column>\n              <el-table-column v-for=\"(item,iii) in attrValue\" :key=\"iii\" :label=\"formThead[iii].title\"\n                               align=\"center\" min-width=\"120\">\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row[iii]\" :type=\"formThead[iii].title==='商品编号'?'text':'number'\" :min=\"0\"\n                            class=\"priceBox\"/>\n                </template>\n              </el-table-column>\n              <el-table-column align=\"center\" label=\"操作\" min-width=\"80\">\n                <template>\n                  <el-button type=\"text\" class=\"submission\" @click=\"batchAdd\">批量添加</el-button>\n                </template>\n              </el-table-column>\n            </el-table>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"24\">\n          <el-form-item label=\"商品规格：\" props=\"spec_type\" label-for=\"spec_type\">\n            <el-table :data=\"formValidate.attrValue\" border class=\"tabNumWidth\" size=\"mini\">\n              <template v-if=\"manyTabDate\">\n                <el-table-column v-for=\"(item,iii) in manyTabDate\" :key=\"iii\" align=\"center\"\n                                 :label=\"manyTabTit[iii].title\" min-width=\"80\">\n                  <template slot-scope=\"scope\">\n                    <span class=\"priceBox\" v-text=\"scope.row[iii]\"/>\n                  </template>\n                </el-table-column>\n              </template>\n              <el-table-column align=\"center\" label=\"图片\" min-width=\"80\">\n                <template slot-scope=\"scope\">\n                  <el-form-item :rules=\"[{required: true, message: '请上传图片', trigger: 'change'}]\" :prop=\"'attrValue.'+scope.$index+'.image'\">\n                    <div class=\"upLoadPicBox\" @click=\"modalPicTap('1','duo',scope.$index)\">\n                      <div v-if=\"scope.row.image\" class=\"pictrue pictrueTab\"><img :src=\"scope.row.image\"></div>\n                      <div v-else class=\"upLoad pictrueTab\">\n                        <i class=\"el-icon-camera cameraIconfont\"/>\n                      </div>\n                    </div>\n                  </el-form-item>\n                </template>\n              </el-table-column>\n              <el-table-column v-for=\"(item,iii) in attrValue\" :key=\"iii\" :label=\"formThead[iii].title\"\n                               align=\"center\" min-width=\"120\">\n                <template slot-scope=\"scope\">\n                  <el-form-item :rules=\"[{required: true, message: '请输入'+formThead[iii].title, trigger: 'blur'}]\" :prop=\"formThead[iii].title!=='商品编号'?'attrValue.'+scope.$index+'.'+iii:''\">\n                    <el-input v-model=\"scope.row[iii]\" :type=\"formThead[iii].title==='商品编号'?'text':'number'\" class=\"priceBox\" />\n                  </el-form-item>\n                  <!--<el-input v-model=\"scope.row[iii]\" :type=\"formThead[iii].title==='商品编号'?'text':'number'\"-->\n                            <!--class=\"priceBox\"/>-->\n                </template>\n              </el-table-column>\n              <el-table-column align=\"center\" label=\"操作\" min-width=\"80\">\n                <template slot-scope=\"scope\">\n                  <el-button type=\"text\" class=\"submission\" @click=\"delAttrTable(scope.$index)\">删除</el-button>\n                </template>\n              </el-table-column>\n            </el-table>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"24\">\n          <el-form-item label=\"商品详情：\">\n            <Tinymce v-model=\"formValidate.content\"></Tinymce>\n          </el-form-item>\n        </el-col>\n        <el-col :span=\"24\">\n          <template>\n            <el-row :gutter=\"24\">\n              <el-col :span=\"24\">\n                <template>\n                  <el-row :gutter=\"24\">\n                    <el-col :span=\"8\">\n                    <el-form-item label=\"排序：\">\n                      <el-input-number v-model=\"formValidate.sort\" :max=\"9999\" placeholder=\"请输入排序\" :disabled=\"isDisabled\" />\n                    </el-form-item>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <el-form-item label=\"积分：\">\n                      <el-input-number v-model=\"formValidate.giveIntegral\" placeholder=\"请输入排序\" :disabled=\"isDisabled\" />\n                    </el-form-item>\n                  </el-col>\n                  <el-col :span=\"8\">\n                    <el-form-item label=\"虚拟销量：\">\n                      <el-input-number v-model=\"formValidate.ficti\" placeholder=\"请输入排序\" :disabled=\"isDisabled\" />\n                    </el-form-item>\n                  </el-col>\n                  </el-row>\n                </template>\n              </el-col>\n              <el-col :span=\"24\">\n                <el-form-item label=\"商品推荐：\">\n                  <el-checkbox-group v-model=\"checkboxGroup\" size=\"small\" @change=\"onChangeGroup\" :disabled=\"isDisabled\">\n                    <el-checkbox v-for=\"(item, index) in recommend\" :key=\"index\" :label=\"item.value\">{{ item.name }}</el-checkbox>\n                  </el-checkbox-group>\n                </el-form-item>\n              </el-col>\n            </el-row>\n          </template>\n        </el-col>\n\n        <el-col :span=\"24\">\n          <el-form-item>\n            <el-button type=\"primary\" :loading=\"modal_loading\" class=\"submission\" @click=\"handleSubmit('formValidate')\">提交\n            </el-button>\n          </el-form-item>\n        </el-col>\n      </el-col>\n    </el-row>\n  </el-form>\n</div>\n", null]}