<view class="productSort data-v-098d4cba"><movable-area style="width:100vw;height:100vh;position:fixed;top:0;left:0;z-index:999;pointer-events:none;" class="data-v-098d4cba"><block wx:if="{{isDragging}}"><movable-view style="pointer-events:auto;" x="{{dragPosition.x}}" y="{{dragPosition.y}}" direction="all" animation="{{false}}" out-of-bounds="{{true}}" data-event-opts="{{[['change',[['onChange',['$event']]]],['touchend',[['onTouchEnd',['$event']]]]]}}" bindchange="__e" bindtouchend="__e" class="data-v-098d4cba"><image style="{{'width:'+(dragProduct.width*3+'rpx')+';'+('height:'+(dragProduct.height*3+'rpx')+';')}}" src="{{dragProduct.image}}" mode="aspectFit" class="data-v-098d4cba"></image></movable-view></block></movable-area><movable-area style="width:100vw;height:100vh;position:fixed;top:0;left:0;z-index:999;pointer-events:none;" class="data-v-098d4cba"><block wx:if="{{isDraggingMarble}}"><movable-view style="pointer-events:auto;" x="{{dragPosition.x}}" y="{{dragPosition.y}}" direction="all" animation="{{false}}" out-of-bounds="{{true}}" data-event-opts="{{[['change',[['onChange2',['$event']]]],['touchend',[['onMarbleTouchEnd',['$event']]]]]}}" bindchange="__e" bindtouchend="__e" class="data-v-098d4cba"><image style="{{'width:'+(dragProduct.width*3+'rpx')+';'+('height:'+(dragProduct.height*3+'rpx')+';')}}" src="{{dragProduct.image}}" mode="aspectFit" class="data-v-098d4cba"></image></movable-view></block></movable-area><view class="top data-v-098d4cba"><view class="left-bbb data-v-098d4cba"><block wx:if="{{showDropdown}}"><view class="specifications-dropdown data-v-098d4cba" style="{{'left:'+(dropdownLeft+'rpx')+';'}}"><view class="data-v-098d4cba"><view class="specs-header data-v-098d4cba" style="justify-content:center;"><text class="specs-title data-v-098d4cba">{{productList[scrollIndex].name}}</text></view><view class="specs-list data-v-098d4cba"><block wx:for="{{productList[scrollIndex].child}}" wx:for-item="spec" wx:for-index="specIndex" wx:key="specIndex"><view data-event-opts="{{[['tap',[['scrollToProduct',['$0',specIndex],['scrollIndex']]]]]}}" class="specs-item data-v-098d4cba" bindtap="__e"><view class="specs-info data-v-098d4cba"><text class="specs-size data-v-098d4cba">{{spec.name}}</text></view></view></block></view></view></view></block><view class="left-bottom data-v-098d4cba">点击宝石或长按拖动宝石，进行手串DIY定制</view><block wx:if="{{$root.g0}}"><scroll-view style="white-space:nowrap;border-top-left-radius:16rpx;border-top-right-radius:16rpx;" scroll-x="true" scroll-into-view="{{scrollIntoViewId}}" class="data-v-098d4cba"><view class="left-child data-v-098d4cba"><view style="text-align:center;display:flex;align-items:center;" class="data-v-098d4cba"><view class="bigtitle data-v-098d4cba" style="border-top-left-radius:16rpx;" id="{{'producttitle-'+index}}">{{''+productList[0].name}}</view><block wx:for="{{productList[0].child}}" wx:for-item="child" wx:for-index="index2" wx:key="index2"><view style="text-align:center;display:flex;align-items:center;flex:1;" class="data-v-098d4cba"><view class="{{['smalltitle','data-v-098d4cba',(indexOneProductIndex===index2)?'active':'']}}" id="{{'producttitle-'+index+index2}}" data-event-opts="{{[['tap',[['handleOneTabClick',[index2]]]]]}}" bindtap="__e">{{child.name+''}}</view></view></block></view></view></scroll-view></block><scroll-view style="white-space:nowrap;width:710rpx;" scroll-x="true" class="data-v-098d4cba"><view class="left data-v-098d4cba" style="display:flex;min-width:max-content;"><block wx:for="{{indexOneProduct}}" wx:for-item="product" wx:for-index="index1" wx:key="index1"><view class="product-item data-v-098d4cba" style="margin:10rpx;display:flex;padding:10rpx;position:relative;align-items:center;flex-shrink:0;" id="{{'product-'+index}}" data-event-opts="{{[['tap',[['addProduct',['$0'],[[['indexOneProduct','',index1]]]]]]]}}" bindtap="__e"><image style="{{('height: 50rpx;')}}" mode="heightFix" src="{{product.image}}" data-event-opts="{{[['longpress',[['onLongPress',['$event','$0'],[[['indexOneProduct','',index1]]]]]],['touchmove',[['onTouchMove',['$event']]]],['touchend',[['onTouchEnd',['$event']]]]]}}" bindlongpress="__e" catchtouchmove="__e" catchtouchend="__e" class="data-v-098d4cba"></image><view style="padding:0 10rpx;display:flex;flex-direction:column;align-items:center;justify-content:space-around;" class="_div data-v-098d4cba"><view style="width:100%;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;" class="data-v-098d4cba">{{''+product.storeName+''}}</view><view style="color:#c9ab79;font-weight:600;font-size:20rpx;" class="data-v-098d4cba">{{''+product.width+"x"+product.height+'mm'}}</view></view><block wx:if="{{product.showSpecs}}"><view class="specifications-dropdown data-v-098d4cba" style="{{'left:'+(specLeft+'rpx')+';'}}"><view class="specs-header data-v-098d4cba"><text class="specs-title data-v-098d4cba">规格选择</text><text data-event-opts="{{[['tap',[['toggleSpecifications',['$0','$event'],[[['indexOneProduct','',index1]]]]]]]}}" class="close-btn data-v-098d4cba" catchtap="__e">×</text></view><view class="specs-list data-v-098d4cba"><block wx:for="{{product.productValue}}" wx:for-item="spec" wx:for-index="specIndex" wx:key="specIndex"><view data-event-opts="{{[['tap',[['selectSpecification',['$0','$1'],[[['indexOneProduct','',index1]],[['indexOneProduct','',index1],['productValue','',specIndex]]]]]]]}}" class="specs-item data-v-098d4cba" catchtap="__e"><view class="specs-info data-v-098d4cba"><text class="specs-size data-v-098d4cba">{{spec.width+"×"+spec.height+"mm"}}</text></view></view></block></view></view></block></view></block></view></scroll-view><block wx:if="{{$root.g1}}"><scroll-view style="white-space:nowrap;" scroll-x="true" scroll-into-view="{{scrollIntoViewId}}" class="data-v-098d4cba"><view class="left-child data-v-098d4cba"><view style="text-align:center;display:flex;align-items:center;width:710rpx;" class="data-v-098d4cba"><view class="bigtitle data-v-098d4cba" id="{{'producttitle-'+index}}">{{''+productList[1].name}}</view><block wx:for="{{productList[1].child}}" wx:for-item="child" wx:for-index="index2" wx:key="index2"><view style="text-align:center;display:flex;align-items:center;flex:1;" class="data-v-098d4cba"><view class="{{['smalltitle','data-v-098d4cba',(indexTwoProductIndex===index2)?'active':'']}}" id="{{'producttitle-'+index+index2}}" data-event-opts="{{[['tap',[['handleTwoTabClick',[index2]]]]]}}" bindtap="__e">{{child.name+''}}</view></view></block></view></view></scroll-view></block><scroll-view style="white-space:nowrap;width:710rpx;" scroll-x="true" class="data-v-098d4cba"><view class="left data-v-098d4cba" style="display:flex;min-width:max-content;"><block wx:for="{{indexTwoProduct}}" wx:for-item="product" wx:for-index="index1" wx:key="index1"><view class="product-item data-v-098d4cba" style="margin:10rpx;display:flex;padding:10rpx;position:relative;align-items:center;flex-shrink:0;" id="{{'product-'+index}}" data-event-opts="{{[['tap',[['addProduct',['$0'],[[['indexTwoProduct','',index1]]]]]]]}}" bindtap="__e"><image style="{{('height: 50rpx;')}}" mode="heightFix" src="{{product.image}}" data-event-opts="{{[['longpress',[['onLongPress',['$event','$0'],[[['indexTwoProduct','',index1]]]]]],['touchmove',[['onTouchMove',['$event']]]],['touchend',[['onTouchEnd',['$event']]]]]}}" bindlongpress="__e" catchtouchmove="__e" catchtouchend="__e" class="data-v-098d4cba"></image><view style="padding:0 10rpx;display:flex;flex-direction:column;align-items:center;justify-content:space-around;" class="_div data-v-098d4cba"><view style="width:100%;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;" class="data-v-098d4cba">{{''+product.storeName+''}}</view><view style="color:#c9ab79;font-weight:600;font-size:20rpx;" class="data-v-098d4cba">{{''+product.width+"x"+product.height+'mm'}}</view></view><block wx:if="{{product.showSpecs}}"><view class="specifications-dropdown data-v-098d4cba" style="{{'left:'+(specLeft+'rpx')+';'}}"><view class="specs-header data-v-098d4cba"><text class="specs-title data-v-098d4cba">规格选择</text><text data-event-opts="{{[['tap',[['toggleSpecifications',['$0','$event'],[[['indexTwoProduct','',index1]]]]]]]}}" class="close-btn data-v-098d4cba" catchtap="__e">×</text></view><view class="specs-list data-v-098d4cba"><block wx:for="{{product.productValue}}" wx:for-item="spec" wx:for-index="specIndex" wx:key="specIndex"><view data-event-opts="{{[['tap',[['selectSpecification',['$0','$1'],[[['indexTwoProduct','',index1]],[['indexTwoProduct','',index1],['productValue','',specIndex]]]]]]]}}" class="specs-item data-v-098d4cba" catchtap="__e"><view class="specs-info data-v-098d4cba"><text class="specs-size data-v-098d4cba">{{spec.width+"×"+spec.height+"mm"}}</text></view></view></block></view></view></block></view></block></view></scroll-view></view><view class="right data-v-098d4cba"><view style="height:100rpx;display:flex;align-items:center;padding:0 20rpx;" class="_div data-v-098d4cba"><block wx:if="{{!isLogin}}"><view style="display:flex;justify-content:flex-end;" class="_div data-v-098d4cba"><view data-event-opts="{{[['tap',[['toLogin',['$event']]]]]}}" class="botton_1 _div data-v-098d4cba" bindtap="__e">快速登录</view></view></block><block wx:else><view style="display:flex;justify-content:flex-end;gap:10rpx;" class="_div data-v-098d4cba"><block wx:if="{{userInfo.hand<=0}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="botton_3 _div data-v-098d4cba" bindtap="__e">设置手围</view></block><block wx:if="{{$root.g2>5}}"><button class="botton_2 data-v-098d4cba" open-type="share" hover-class="none"><view class="data-v-098d4cba">分享手串</view></button></block><block wx:if="{{$root.g3}}"><view data-event-opts="{{[['tap',[['userBraceletsSubmit',['$event']]]]]}}" class="botton_3 _div data-v-098d4cba" bindtap="__e">保存手串</view></block><block wx:if="{{$root.g4}}"><view data-event-opts="{{[['tap',[['saveSelfBraceletsSubmit',['$event']]]]]}}" class="botton_3 _div data-v-098d4cba" bindtap="__e">保存为自己的手串</view></block></view></block></view><view style="display:flex;justify-content:center;align-items:center;height:700rpx;border-bottom:#c9ab79 1px dashed;border-top:#c9ab79 1px dashed;position:relative;" class="_div data-v-098d4cba"><view class="circle-container _div data-v-098d4cba" style="{{'width:'+(radius*2+'rpx')+';'+('height:'+(radius*2+'rpx')+';')}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['longpress',[['onMarbleLongPress',[index,'$event']]]],['touchmove',[['onMarbleTouchMove',[index,'$event']]]],['touchend',[['onMarbleTouchEnd',[index,'$event']]]]]}}" class="marble _div data-v-098d4cba" style="{{item.s0}}" bindlongpress="__e" catchtouchmove="__e" catchtouchend="__e"></view></block></view><view style="position:absolute;top:10rpx;left:10rpx;text-align:left;" class="_div data-v-098d4cba"><block wx:if="{{userInfo.hand>0}}"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="hand-text-1 _div data-v-098d4cba" bindtap="__e">您的手围<label style="color:#DD5C5F;font-size:26rpx;" class="_span data-v-098d4cba">{{userInfo.hand+"mm"}}</label>。当前手串预估手围<label style="color:#DD5C5F;font-size:26rpx;" class="_span data-v-098d4cba">{{realRadius+"mm"}}</label></view></block><block wx:else><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" class="hand-text-1 _div data-v-098d4cba" bindtap="__e">当前手串预估手围<label style="color:#DD5C5F;font-size:26rpx;" class="_span data-v-098d4cba">{{realRadius+"mm"}}</label></view></block></view><view style="position:absolute;top:10rpx;right:20rpx;display:flex;gap:10rpx;flex-direction:column;" class="_div data-v-098d4cba"><image style="width:60rpx;" src="https://mpjoy.oss-cn-beijing.aliyuncs.com/crmebimage/public/maintain/2024/12/20/865abacc296140f79dcaa89b3d047e77r1bpnw5pwp.png" mode="widthFix" data-event-opts="{{[['tap',[['reset',['$event']]]]]}}" bindtap="__e" class="data-v-098d4cba"></image><view class="hand-text-text data-v-098d4cba">重置</view><image style="width:60rpx;" src="https://mpjoy.oss-cn-beijing.aliyuncs.com/crmebimage/public/maintain/2024/12/20/680c1002ea76449190e861e02b25d4bffvx635j582.png" mode="widthFix" data-event-opts="{{[['tap',[['backOne',['$event']]]]]}}" bindtap="__e" class="data-v-098d4cba"></image><view class="hand-text-text data-v-098d4cba">撤回</view></view><view style="position:absolute;bottom:10rpx;left:10rpx;text-align:left;" class="_div data-v-098d4cba"><block wx:if="{{$root.g5>0}}"><view data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" class="hand-text-1 _div data-v-098d4cba" bindtap="__e">长按手串上面的珠子，可以灵活调整位置</view></block></view></view><view style="height:100rpx;display:flex;align-items:center;padding:0 10rpx;" class="_div data-v-098d4cba"><block wx:if="{{!changeFlag}}"><view style="width:60%;overflow-x:scroll;display:flex;gap:10rpx;align-items:center;" class="_div data-v-098d4cba"><block wx:for="{{selectList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="product-spec-item _div data-v-098d4cba" style="position:relative;"><view class="image-wrapper _div data-v-098d4cba"><image class="product-image data-v-098d4cba" style="{{('height: 50rpx;')}}" mode="heightFix" src="{{item.image}}" data-event-opts="{{[['tap',[['_previewImage',['$0'],[[['selectList','',index,'image']]]]]]]}}" catchtap="__e"></image><image class="delete-icon data-v-098d4cba" mode="heightFix" src="https://mpjoy.oss-cn-beijing.aliyuncs.com/crmebimage/public/maintain/2024/11/22/264c59ab0f89409d91641278e6e07ec2be9a9yqbwu.png" data-event-opts="{{[['tap',[['delProduct',[index]]]]]}}" bindtap="__e"></image></view><view class="price-tag _div data-v-098d4cba"><text class="price-symbol data-v-098d4cba">¥</text><text class="price-value data-v-098d4cba">{{item.price}}</text></view></view></block></view></block><block wx:else><view style="width:60%;" class="_div data-v-098d4cba"><shmily-drag-image vue-id="281ccb8c-1" keyName="image" value="{{selectList}}" data-event-opts="{{[['^changeprice',[['dualPrice']]],['^input',[['__set_model',['','selectList','$event',[]]]]]]}}" bind:changeprice="__e" bind:input="__e" class="data-v-098d4cba" bind:__l="__l"></shmily-drag-image></view></block><view style="width:20%;font-size:22rpx;" class="_div data-v-098d4cba"><view class="_div data-v-098d4cba">总价</view><view class="_div data-v-098d4cba">￥<label style="color:#c9ab79;font-weight:600;" class="_span data-v-098d4cba">{{price}}</label>元</view></view><view style="width:20%;" class="_div data-v-098d4cba"><view data-event-opts="{{[['tap',[['toBuy',['$event']]]]]}}" class="botton_2 _div data-v-098d4cba" bindtap="__e">去结算</view></view></view></view></view><view class="content data-v-098d4cba"><view style="padding:10rpx;" class="_div data-v-098d4cba"><image style="width:100%;" src="https://mpjoy.oss-cn-beijing.aliyuncs.com/crmebimage/public/maintain/2024/11/22/dde1678a2a4f48f4ad694a26563b7eb2or11mp8qem.jpg" mode="widthFix" class="data-v-098d4cba"></image></view></view><ljs-dialog bind:input="__e" class="comTc data-v-098d4cba" vue-id="281ccb8c-2" title="编辑我的手围" value="{{handVisible}}" data-event-opts="{{[['^input',[['__set_model',['','handVisible','$event',[]]]]]]}}" bind:__l="__l" vue-slots="{{['default']}}"><view class="comForm data-v-098d4cba"><view class="form-group _div data-v-098d4cba"><input type="text" placeholder="请输入手围" data-event-opts="{{[['input',[['__set_model',['','hand','$event',[]]]]]]}}" value="{{hand}}" bindinput="__e" class="data-v-098d4cba"/><label class="unit _span data-v-098d4cba">mm</label></view></view><view class="operate data-v-098d4cba" style="margin-top:0;"><view data-event-opts="{{[['tap',[['fromSubmit',['$event']]]]]}}" class="but data-v-098d4cba" bindtap="__e">确定</view><view data-event-opts="{{[['tap',[['e4',['$event']]]]]}}" class="but grey data-v-098d4cba" bindtap="__e">取消</view></view></ljs-dialog></view>