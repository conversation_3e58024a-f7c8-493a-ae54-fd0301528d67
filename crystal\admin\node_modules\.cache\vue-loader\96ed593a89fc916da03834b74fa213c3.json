{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\Category\\list.vue?vue&type=template&id=8f82d0e2&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\Category\\list.vue", "mtime": 1753666157756}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\n<div>\n  <template v-if=\"selectModel\">\n    <el-tree ref=\"tree\" :data=\"treeList\" show-checkbox node-key=\"id\" @check=\"getCurrentNode\"\n      :default-checked-keys=\"selectModelKeysNew\" :props=\"treeProps\">\n    </el-tree>\n  </template>\n  <template v-else>\n    <div class=\"divBox\">\n      <el-card class=\"box-card\">\n        <div slot=\"header\" class=\"clearfix\">\n          <div class=\"container\">\n            <el-form inline size=\"small\">\n              <el-form-item label=\"状态：\">\n                <el-select v-model=\"listPram.status\" placeholder=\"状态\" class=\"selWidth\" @change=\"handlerGetList\">\n                  <el-option label=\"全部\" :value=\"-1\"></el-option>\n                  <el-option label=\"显示\" :value=\"1\"></el-option>\n                  <el-option label=\"不显示\" :value=\"0\"></el-option>\n                </el-select>\n              </el-form-item>\n              <el-form-item label=\"名称：\">\n                <el-input v-model=\"listPram.name\" placeholder=\"请输入名称\" class=\"selWidth\" size=\"small\" clearable>\n                  <el-button slot=\"append\" icon=\"el-icon-search\" @click=\"handlerGetList\" size=\"small\" />\n                </el-input>\n              </el-form-item>\n            </el-form>\n          </div>\n          <el-button size=\"mini\" type=\"primary\" @click=\"handleAddMenu({ id: 0, name: '顶层目录' })\"\n            v-hasPermi=\"['admin:category:save']\">新增{{ biztype.name }}</el-button>\n        </div>\n        <el-table ref=\"treeList\" :data=\"treeList\" size=\"mini\" class=\"table\" highlight-current-row row-key=\"id\"\n          :tree-props=\"{ children: 'child', hasChildren: 'hasChildren' }\">\n          <el-table-column prop=\"name\" label=\"名称\" min-width=\"240\">\n            <template slot-scope=\"scope\">\n              {{ scope.row.name }} | {{ scope.row.id }}\n            </template>\n          </el-table-column>\n          <template v-if=\"!selectModel\">\n            <el-table-column label=\"类型\" min-width=\"150\">\n              <template slot-scope=\"scope\">\n                <span>{{ scope.row.type | filterCategroyType | filterEmpty }}</span>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"分类图标\" min-width=\"80\">\n              <template slot-scope=\"scope\">\n                <div class=\"listPic\" v-if=\"biztype.value === 5\">\n                  <i :class=\"'el-icon-' + scope.row.extra\" style=\"font-size: 20px;\" />\n                </div>\n                <div class=\"demo-image__preview\" v-else>\n                  <el-image style=\"width: 36px; height: 36px\" :src=\"scope.row.extra\"\n                    :preview-src-list=\"[scope.row.extra]\" v-if=\"scope.row.extra\" />\n                  <img style=\"width: 36px; height: 36px\" v-else :src=\"defaultImg\" alt=\"\">\n                </div>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"Url\" min-width=\"250\" v-if=\"biztype.value === 5\" key=\"2\">\n              <template slot-scope=\"scope\">\n                <span>{{ scope.row.url }}</span>\n              </template>\n            </el-table-column>\n            <el-table-column label=\"排序\" prop=\"sort\" min-width=\"150\" />\n            <el-table-column label=\"状态\" min-width=\"150\">\n              <!--  -->\n              <template slot-scope=\"scope\" v-if=\"checkPermi(['admin:category:update:status'])\">\n                <el-switch v-model=\"scope.row.status\" :active-value=\"true\" :inactive-value=\"false\" active-text=\"显示\"\n                  inactive-text=\"隐藏\" @change=\"onchangeIsShow(scope.row)\" />\n              </template>\n            </el-table-column>\n\n            <el-table-column label=\"操作\" min-width=\"200\" fixed=\"right\">\n              <template slot-scope=\"scope\">\n                <el-button v-if=\"(biztype.value === 1 && scope.row.pid === 0) || biztype.value === 5 || (biztype.value === 8 && scope.row.pid === 0)\" type=\"text\"\n                  size=\"small\" @click=\"handleAddMenu(scope.row)\">添加子目录</el-button>\n                <el-button type=\"text\" size=\"small\" @click=\"handleEditMenu(scope.row)\"\n                  v-hasPermi=\"['admin:category:info']\">编辑</el-button>\n                <el-button type=\"text\" size=\"small\" @click=\"handleDelMenu(scope.row)\"\n                  v-hasPermi=\"['admin:category:delete']\">删除</el-button>\n              </template>\n            </el-table-column>\n          </template>\n        </el-table>\n      </el-card>\n    </div>\n  </template>\n  <el-dialog :title=\"(editDialogConfig.isCreate === 0 ? `创建${biztype.name}` : `编辑${biztype.name}`)\"\n    :visible.sync=\"editDialogConfig.visible\" destroy-on-close :close-on-click-modal=\"false\">\n    <edit v-if=\"editDialogConfig.visible\" :prent=\"editDialogConfig.prent\" :is-create=\"editDialogConfig.isCreate\"\n      :edit-data=\"editDialogConfig.data\" :biztype=\"editDialogConfig.biztype\" :all-tree-list=\"treeList\"\n      @hideEditDialog=\"hideEditDialog\" />\n  </el-dialog>\n</div>\n", null]}