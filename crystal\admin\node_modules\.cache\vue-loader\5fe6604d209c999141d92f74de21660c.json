{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\marketing\\bargain\\bargainGoods\\creatBargain.vue?vue&type=template&id=3b5da284&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\marketing\\bargain\\bargainGoods\\creatBargain.vue", "mtime": 1753666157888}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"divBox\" },\n    [\n      _c(\n        \"el-card\",\n        { staticClass: \"box-card\" },\n        [\n          _c(\n            \"div\",\n            {\n              staticClass: \"clearfix\",\n              attrs: { slot: \"header\" },\n              slot: \"header\",\n            },\n            [\n              _c(\n                \"el-steps\",\n                {\n                  attrs: {\n                    active: _vm.currentTab,\n                    \"align-center\": \"\",\n                    \"finish-status\": \"success\",\n                  },\n                },\n                [\n                  _c(\"el-step\", { attrs: { title: \"选择砍价商品\" } }),\n                  _vm._v(\" \"),\n                  _c(\"el-step\", { attrs: { title: \"填写基础信息\" } }),\n                  _vm._v(\" \"),\n                  _c(\"el-step\", { attrs: { title: \"修改商品详情\" } }),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-form\",\n            {\n              directives: [\n                {\n                  name: \"loading\",\n                  rawName: \"v-loading\",\n                  value: _vm.fullscreenLoading,\n                  expression: \"fullscreenLoading\",\n                },\n              ],\n              ref: \"formValidate\",\n              staticClass: \"formValidate mt20\",\n              attrs: {\n                rules: _vm.ruleValidate,\n                model: _vm.formValidate,\n                \"label-width\": \"150px\",\n              },\n              nativeOn: {\n                submit: function ($event) {\n                  $event.preventDefault()\n                },\n              },\n            },\n            [\n              _c(\n                \"div\",\n                {\n                  directives: [\n                    {\n                      name: \"show\",\n                      rawName: \"v-show\",\n                      value: _vm.currentTab === 0,\n                      expression: \"currentTab === 0\",\n                    },\n                  ],\n                },\n                [\n                  _c(\n                    \"el-form-item\",\n                    { attrs: { label: \"选择商品：\", prop: \"image\" } },\n                    [\n                      _c(\n                        \"div\",\n                        {\n                          staticClass: \"upLoadPicBox\",\n                          on: { click: _vm.changeGood },\n                        },\n                        [\n                          _vm.formValidate.image\n                            ? _c(\"div\", { staticClass: \"pictrue\" }, [\n                                _c(\"img\", {\n                                  attrs: { src: _vm.formValidate.image },\n                                }),\n                              ])\n                            : _c(\"div\", { staticClass: \"upLoad\" }, [\n                                _c(\"i\", {\n                                  staticClass: \"el-icon-camera cameraIconfont\",\n                                }),\n                              ]),\n                        ]\n                      ),\n                    ]\n                  ),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"div\",\n                {\n                  directives: [\n                    {\n                      name: \"show\",\n                      rawName: \"v-show\",\n                      value: _vm.currentTab === 1,\n                      expression: \"currentTab === 1\",\n                    },\n                  ],\n                },\n                [\n                  _c(\n                    \"el-row\",\n                    { attrs: { gutter: 24 } },\n                    [\n                      _c(\n                        \"el-col\",\n                        { attrs: { span: 24 } },\n                        [\n                          _c(\n                            \"el-form-item\",\n                            { attrs: { label: \"商品主图：\", prop: \"image\" } },\n                            [\n                              _c(\n                                \"div\",\n                                {\n                                  staticClass: \"upLoadPicBox\",\n                                  on: {\n                                    click: function ($event) {\n                                      return _vm.modalPicTap(\"1\")\n                                    },\n                                  },\n                                },\n                                [\n                                  _vm.formValidate.image\n                                    ? _c(\"div\", { staticClass: \"pictrue\" }, [\n                                        _c(\"img\", {\n                                          attrs: {\n                                            src: _vm.formValidate.image,\n                                          },\n                                        }),\n                                      ])\n                                    : _c(\"div\", { staticClass: \"upLoad\" }, [\n                                        _c(\"i\", {\n                                          staticClass:\n                                            \"el-icon-camera cameraIconfont\",\n                                        }),\n                                      ]),\n                                ]\n                              ),\n                            ]\n                          ),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-col\",\n                        { attrs: { span: 24 } },\n                        [\n                          _c(\n                            \"el-form-item\",\n                            {\n                              attrs: { label: \"商品轮播图：\", prop: \"images\" },\n                            },\n                            [\n                              _c(\n                                \"div\",\n                                { staticClass: \"acea-row\" },\n                                [\n                                  _vm._l(\n                                    _vm.formValidate.imagess,\n                                    function (item, index) {\n                                      return _c(\n                                        \"div\",\n                                        {\n                                          key: index,\n                                          staticClass: \"pictrue\",\n                                          attrs: { draggable: \"true\" },\n                                          on: {\n                                            dragstart: function ($event) {\n                                              return _vm.handleDragStart(\n                                                $event,\n                                                item\n                                              )\n                                            },\n                                            dragover: function ($event) {\n                                              $event.preventDefault()\n                                              return _vm.handleDragOver(\n                                                $event,\n                                                item\n                                              )\n                                            },\n                                            dragenter: function ($event) {\n                                              return _vm.handleDragEnter(\n                                                $event,\n                                                item\n                                              )\n                                            },\n                                            dragend: function ($event) {\n                                              return _vm.handleDragEnd(\n                                                $event,\n                                                item\n                                              )\n                                            },\n                                          },\n                                        },\n                                        [\n                                          _c(\"img\", { attrs: { src: item } }),\n                                          _vm._v(\" \"),\n                                          _c(\"i\", {\n                                            staticClass: \"el-icon-error btndel\",\n                                            on: {\n                                              click: function ($event) {\n                                                return _vm.handleRemove(index)\n                                              },\n                                            },\n                                          }),\n                                        ]\n                                      )\n                                    }\n                                  ),\n                                  _vm._v(\" \"),\n                                  _vm.formValidate.imagess.length < 10\n                                    ? _c(\n                                        \"div\",\n                                        {\n                                          staticClass: \"upLoadPicBox\",\n                                          on: {\n                                            click: function ($event) {\n                                              return _vm.modalPicTap(\"2\")\n                                            },\n                                          },\n                                        },\n                                        [\n                                          _c(\"div\", { staticClass: \"upLoad\" }, [\n                                            _c(\"i\", {\n                                              staticClass:\n                                                \"el-icon-camera cameraIconfont\",\n                                            }),\n                                          ]),\n                                        ]\n                                      )\n                                    : _vm._e(),\n                                ],\n                                2\n                              ),\n                            ]\n                          ),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-col\",\n                        { attrs: { span: 24 } },\n                        [\n                          _c(\n                            \"el-form-item\",\n                            {\n                              attrs: { label: \"砍价活动名称：\", prop: \"title\" },\n                            },\n                            [\n                              _c(\"el-input\", {\n                                attrs: {\n                                  maxlength: \"249\",\n                                  placeholder: \"请输入砍价活动名称\",\n                                },\n                                model: {\n                                  value: _vm.formValidate.title,\n                                  callback: function ($$v) {\n                                    _vm.$set(_vm.formValidate, \"title\", $$v)\n                                  },\n                                  expression: \"formValidate.title\",\n                                },\n                              }),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-col\",\n                        _vm._b({}, \"el-col\", _vm.grid2, false),\n                        [\n                          _c(\n                            \"el-form-item\",\n                            { attrs: { label: \"单位：\", prop: \"unitName\" } },\n                            [\n                              _c(\"el-input\", {\n                                staticClass: \"selWidthd\",\n                                attrs: { placeholder: \"请输入单位\" },\n                                model: {\n                                  value: _vm.formValidate.unitName,\n                                  callback: function ($$v) {\n                                    _vm.$set(_vm.formValidate, \"unitName\", $$v)\n                                  },\n                                  expression: \"formValidate.unitName\",\n                                },\n                              }),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-col\",\n                        _vm._b({}, \"el-col\", _vm.grid2, false),\n                        [\n                          _c(\n                            \"el-form-item\",\n                            { attrs: { label: \"排序：\", prop: \"sort\" } },\n                            [\n                              _c(\"el-input-number\", {\n                                staticClass: \"selWidthd\",\n                                attrs: { max: 9999, placeholder: \"请输入排序\" },\n                                model: {\n                                  value: _vm.formValidate.sort,\n                                  callback: function ($$v) {\n                                    _vm.$set(_vm.formValidate, \"sort\", $$v)\n                                  },\n                                  expression: \"formValidate.sort\",\n                                },\n                              }),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-col\",\n                        { attrs: { span: 24 } },\n                        [\n                          _c(\n                            \"el-form-item\",\n                            { attrs: { label: \"活动日期：\", prop: \"timeVal\" } },\n                            [\n                              _c(\"el-date-picker\", {\n                                staticClass: \"mr20\",\n                                attrs: {\n                                  type: \"daterange\",\n                                  \"value-format\": \"yyyy-MM-dd\",\n                                  format: \"yyyy-MM-dd\",\n                                  \"range-separator\": \"至\",\n                                  \"start-placeholder\": \"开始日期\",\n                                  \"end-placeholder\": \"结束日期\",\n                                  \"picker-options\": _vm.pickerOptions,\n                                },\n                                on: { change: _vm.onchangeTime },\n                                model: {\n                                  value: _vm.formValidate.timeVal,\n                                  callback: function ($$v) {\n                                    _vm.$set(_vm.formValidate, \"timeVal\", $$v)\n                                  },\n                                  expression: \"formValidate.timeVal\",\n                                },\n                              }),\n                              _vm._v(\" \"),\n                              _c(\"span\", [\n                                _vm._v(\n                                  \"设置活动开启结束时间，用户可以在设置时间内发起参与砍价\"\n                                ),\n                              ]),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-col\",\n                        { attrs: { span: 24 } },\n                        [\n                          _c(\n                            \"el-form-item\",\n                            {\n                              attrs: { label: \"砍价人数：\", prop: \"peopleNum\" },\n                            },\n                            [\n                              _c(\"el-input-number\", {\n                                staticClass: \"selWidthd mr20\",\n                                attrs: {\n                                  min: 2,\n                                  step: 1,\n                                  \"step-strictly\": \"\",\n                                  placeholder: \"请输入砍价人数\",\n                                },\n                                model: {\n                                  value: _vm.formValidate.peopleNum,\n                                  callback: function ($$v) {\n                                    _vm.$set(_vm.formValidate, \"peopleNum\", $$v)\n                                  },\n                                  expression: \"formValidate.peopleNum\",\n                                },\n                              }),\n                              _vm._v(\" \"),\n                              _c(\"span\", [_vm._v(\"需邀请多少人砍价成功\")]),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-col\",\n                        { attrs: { span: 24 } },\n                        [\n                          _c(\n                            \"el-form-item\",\n                            {\n                              attrs: {\n                                label: \"帮砍次数：\",\n                                prop: \"bargainNum\",\n                              },\n                            },\n                            [\n                              _c(\"el-input-number\", {\n                                staticClass: \"selWidthd mr20\",\n                                attrs: {\n                                  min: 1,\n                                  step: 1,\n                                  \"step-strictly\": \"\",\n                                  placeholder: \"请输入帮砍次数\",\n                                },\n                                model: {\n                                  value: _vm.formValidate.bargainNum,\n                                  callback: function ($$v) {\n                                    _vm.$set(\n                                      _vm.formValidate,\n                                      \"bargainNum\",\n                                      $$v\n                                    )\n                                  },\n                                  expression: \"formValidate.bargainNum\",\n                                },\n                              }),\n                              _vm._v(\" \"),\n                              _c(\"span\", [\n                                _vm._v(\n                                  \"单个商品用户可以帮砍的次数，例：次数设置为1，甲和乙同时将商品A的砍价链接发给丙，丙只能帮甲或乙其中一个人砍价\"\n                                ),\n                              ]),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-col\",\n                        { attrs: { span: 24 } },\n                        [\n                          _c(\n                            \"el-form-item\",\n                            { attrs: { label: \"购买数量限制：\", prop: \"num\" } },\n                            [\n                              _c(\"el-input-number\", {\n                                staticClass: \"selWidthd mr20\",\n                                attrs: {\n                                  min: 1,\n                                  step: 1,\n                                  \"step-strictly\": \"\",\n                                  placeholder: \"请输入购买数量限制\",\n                                },\n                                model: {\n                                  value: _vm.formValidate.num,\n                                  callback: function ($$v) {\n                                    _vm.$set(_vm.formValidate, \"num\", $$v)\n                                  },\n                                  expression: \"formValidate.num\",\n                                },\n                              }),\n                              _vm._v(\" \"),\n                              _c(\"span\", [\n                                _vm._v(\"单个活动每个用户发起砍价次数限制\"),\n                              ]),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-col\",\n                        _vm._b({}, \"el-col\", _vm.grid2, false),\n                        [\n                          _c(\n                            \"el-form-item\",\n                            { attrs: { label: \"运费模板：\", prop: \"tempId\" } },\n                            [\n                              _c(\n                                \"div\",\n                                { staticClass: \"acea-row\" },\n                                [\n                                  _c(\n                                    \"el-select\",\n                                    {\n                                      staticClass: \"selWidthd\",\n                                      attrs: { placeholder: \"请选择\" },\n                                      model: {\n                                        value: _vm.formValidate.tempId,\n                                        callback: function ($$v) {\n                                          _vm.$set(\n                                            _vm.formValidate,\n                                            \"tempId\",\n                                            $$v\n                                          )\n                                        },\n                                        expression: \"formValidate.tempId\",\n                                      },\n                                    },\n                                    _vm._l(_vm.shippingList, function (item) {\n                                      return _c(\"el-option\", {\n                                        key: item.id,\n                                        attrs: {\n                                          label: item.name,\n                                          value: item.id,\n                                        },\n                                      })\n                                    }),\n                                    1\n                                  ),\n                                ],\n                                1\n                              ),\n                            ]\n                          ),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-col\",\n                        { attrs: { span: 24 } },\n                        [\n                          _c(\n                            \"el-form-item\",\n                            { attrs: { label: \"活动状态：\", required: \"\" } },\n                            [\n                              _c(\n                                \"el-radio-group\",\n                                {\n                                  model: {\n                                    value: _vm.formValidate.status,\n                                    callback: function ($$v) {\n                                      _vm.$set(_vm.formValidate, \"status\", $$v)\n                                    },\n                                    expression: \"formValidate.status\",\n                                  },\n                                },\n                                [\n                                  _c(\n                                    \"el-radio\",\n                                    {\n                                      staticClass: \"radio\",\n                                      attrs: { label: 0 },\n                                    },\n                                    [_vm._v(\"关闭\")]\n                                  ),\n                                  _vm._v(\" \"),\n                                  _c(\"el-radio\", { attrs: { label: 1 } }, [\n                                    _vm._v(\"开启\"),\n                                  ]),\n                                ],\n                                1\n                              ),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-col\",\n                        { attrs: { span: 24 } },\n                        [\n                          _c(\n                            \"el-form-item\",\n                            {\n                              staticClass: \"labeltop\",\n                              attrs: { label: \"商品属性：\", required: \"\" },\n                            },\n                            [\n                              _c(\n                                \"el-table\",\n                                {\n                                  ref: \"multipleTable\",\n                                  staticStyle: { width: \"100%\" },\n                                  attrs: {\n                                    data: _vm.ManyAttrValue,\n                                    \"tooltip-effect\": \"dark\",\n                                  },\n                                },\n                                [\n                                  _c(\"el-table-column\", {\n                                    attrs: { label: \"选择\", width: \"50\" },\n                                    scopedSlots: _vm._u([\n                                      {\n                                        key: \"default\",\n                                        fn: function (scope) {\n                                          return [\n                                            _c(\n                                              \"el-radio\",\n                                              {\n                                                attrs: { label: scope.row },\n                                                nativeOn: {\n                                                  change: function ($event) {\n                                                    return _vm.changeType(\n                                                      scope.row\n                                                    )\n                                                  },\n                                                },\n                                                model: {\n                                                  value: _vm.radio,\n                                                  callback: function ($$v) {\n                                                    _vm.radio = $$v\n                                                  },\n                                                  expression: \"radio\",\n                                                },\n                                              },\n                                              [\n                                                _vm._v(\n                                                  \" \\n                      \"\n                                                ),\n                                              ]\n                                            ),\n                                          ]\n                                        },\n                                      },\n                                    ]),\n                                  }),\n                                  _vm._v(\" \"),\n                                  _vm.manyTabDate && _vm.formValidate.specType\n                                    ? _vm._l(\n                                        _vm.manyTabDate,\n                                        function (item, iii) {\n                                          return _c(\"el-table-column\", {\n                                            key: iii,\n                                            attrs: {\n                                              align: \"center\",\n                                              label: _vm.manyTabTit[iii].title,\n                                              \"min-width\": \"80\",\n                                            },\n                                            scopedSlots: _vm._u(\n                                              [\n                                                {\n                                                  key: \"default\",\n                                                  fn: function (scope) {\n                                                    return [\n                                                      _c(\"span\", {\n                                                        staticClass: \"priceBox\",\n                                                        domProps: {\n                                                          textContent: _vm._s(\n                                                            scope.row[iii]\n                                                          ),\n                                                        },\n                                                      }),\n                                                    ]\n                                                  },\n                                                },\n                                              ],\n                                              null,\n                                              true\n                                            ),\n                                          })\n                                        }\n                                      )\n                                    : _vm._e(),\n                                  _vm._v(\" \"),\n                                  _c(\"el-table-column\", {\n                                    attrs: {\n                                      align: \"center\",\n                                      label: \"图片\",\n                                      \"min-width\": \"80\",\n                                    },\n                                    scopedSlots: _vm._u([\n                                      {\n                                        key: \"default\",\n                                        fn: function (scope) {\n                                          return [\n                                            _c(\n                                              \"div\",\n                                              {\n                                                staticClass: \"upLoadPicBox\",\n                                                on: {\n                                                  click: function ($event) {\n                                                    return _vm.modalPicTap(\n                                                      \"1\",\n                                                      \"duo\",\n                                                      scope.$index\n                                                    )\n                                                  },\n                                                },\n                                              },\n                                              [\n                                                scope.row.image\n                                                  ? _c(\n                                                      \"div\",\n                                                      {\n                                                        staticClass:\n                                                          \"pictrue tabPic\",\n                                                      },\n                                                      [\n                                                        _c(\"img\", {\n                                                          attrs: {\n                                                            src: scope.row\n                                                              .image,\n                                                          },\n                                                        }),\n                                                      ]\n                                                    )\n                                                  : _c(\n                                                      \"div\",\n                                                      {\n                                                        staticClass:\n                                                          \"upLoad tabPic\",\n                                                      },\n                                                      [\n                                                        _c(\"i\", {\n                                                          staticClass:\n                                                            \"el-icon-camera cameraIconfont\",\n                                                        }),\n                                                      ]\n                                                    ),\n                                              ]\n                                            ),\n                                          ]\n                                        },\n                                      },\n                                    ]),\n                                  }),\n                                  _vm._v(\" \"),\n                                  _vm._l(_vm.attrValue, function (item, iii) {\n                                    return _c(\"el-table-column\", {\n                                      key: iii,\n                                      attrs: {\n                                        label: _vm.formThead[iii].title,\n                                        align: \"center\",\n                                        \"min-width\": \"140\",\n                                      },\n                                      scopedSlots: _vm._u(\n                                        [\n                                          {\n                                            key: \"default\",\n                                            fn: function (ref) {\n                                              var row = ref.row\n                                              var $index = ref.$index\n                                              return [\n                                                _vm.formThead[iii].title ===\n                                                \"砍价起始金额\"\n                                                  ? _c(\"el-input-number\", {\n                                                      staticClass: \"priceBox\",\n                                                      attrs: {\n                                                        size: \"small\",\n                                                        min: 0,\n                                                        precision: 2,\n                                                        step: 0.1,\n                                                      },\n                                                      model: {\n                                                        value: row[iii],\n                                                        callback: function (\n                                                          $$v\n                                                        ) {\n                                                          _vm.$set(\n                                                            row,\n                                                            iii,\n                                                            $$v\n                                                          )\n                                                        },\n                                                        expression: \"row[iii]\",\n                                                      },\n                                                    })\n                                                  : _vm.formThead[iii].title ===\n                                                    \"砍价最低价\"\n                                                  ? _c(\"el-input-number\", {\n                                                      staticClass: \"priceBox\",\n                                                      attrs: {\n                                                        size: \"small\",\n                                                        min: 0,\n                                                        max: parseFloat(\n                                                          row.price\n                                                        ),\n                                                        precision: 2,\n                                                        step: 0.1,\n                                                      },\n                                                      model: {\n                                                        value: row[iii],\n                                                        callback: function (\n                                                          $$v\n                                                        ) {\n                                                          _vm.$set(\n                                                            row,\n                                                            iii,\n                                                            $$v\n                                                          )\n                                                        },\n                                                        expression: \"row[iii]\",\n                                                      },\n                                                    })\n                                                  : _vm.formThead[iii].title ===\n                                                    \"限量\"\n                                                  ? _c(\"el-input-number\", {\n                                                      staticClass: \"priceBox\",\n                                                      attrs: {\n                                                        size: \"small\",\n                                                        type: \"number\",\n                                                        min: 1,\n                                                        max: row.stock,\n                                                        step: 1,\n                                                        \"step-strictly\": \"\",\n                                                      },\n                                                      model: {\n                                                        value: row[iii],\n                                                        callback: function (\n                                                          $$v\n                                                        ) {\n                                                          _vm.$set(\n                                                            row,\n                                                            iii,\n                                                            $$v\n                                                          )\n                                                        },\n                                                        expression: \"row[iii]\",\n                                                      },\n                                                    })\n                                                  : _c(\"span\", {\n                                                      staticClass: \"priceBox\",\n                                                      domProps: {\n                                                        textContent: _vm._s(\n                                                          row[iii]\n                                                        ),\n                                                      },\n                                                    }),\n                                              ]\n                                            },\n                                          },\n                                        ],\n                                        null,\n                                        true\n                                      ),\n                                    })\n                                  }),\n                                ],\n                                2\n                              ),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"div\",\n                {\n                  directives: [\n                    {\n                      name: \"show\",\n                      rawName: \"v-show\",\n                      value: _vm.currentTab === 2,\n                      expression: \"currentTab === 2\",\n                    },\n                  ],\n                },\n                [\n                  _c(\n                    \"el-form-item\",\n                    { attrs: { label: \"商品详情：\" } },\n                    [\n                      _c(\"Tinymce\", {\n                        model: {\n                          value: _vm.formValidate.content,\n                          callback: function ($$v) {\n                            _vm.$set(_vm.formValidate, \"content\", $$v)\n                          },\n                          expression: \"formValidate.content\",\n                        },\n                      }),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                { staticStyle: { \"margin-top\": \"30px\" } },\n                [\n                  _c(\n                    \"el-button\",\n                    {\n                      directives: [\n                        {\n                          name: \"show\",\n                          rawName: \"v-show\",\n                          value:\n                            (!_vm.$route.params.id && _vm.currentTab > 0) ||\n                            (_vm.$route.params.id && _vm.currentTab === 2),\n                          expression:\n                            \"(!$route.params.id && currentTab > 0) || ($route.params.id && currentTab===2)\",\n                        },\n                      ],\n                      staticClass: \"submission\",\n                      attrs: { type: \"primary\", size: \"small\" },\n                      on: { click: _vm.handleSubmitUp },\n                    },\n                    [_vm._v(\"上一步\")]\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-button\",\n                    {\n                      directives: [\n                        {\n                          name: \"show\",\n                          rawName: \"v-show\",\n                          value: _vm.currentTab == 0,\n                          expression: \"currentTab == 0\",\n                        },\n                      ],\n                      staticClass: \"submission\",\n                      attrs: { type: \"primary\", size: \"small\" },\n                      on: {\n                        click: function ($event) {\n                          return _vm.handleSubmitNest1(\"formValidate\")\n                        },\n                      },\n                    },\n                    [_vm._v(\"下一步\")]\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-button\",\n                    {\n                      directives: [\n                        {\n                          name: \"show\",\n                          rawName: \"v-show\",\n                          value: _vm.currentTab == 1,\n                          expression: \"currentTab == 1\",\n                        },\n                      ],\n                      staticClass: \"submission\",\n                      attrs: { type: \"primary\", size: \"small\" },\n                      on: {\n                        click: function ($event) {\n                          return _vm.handleSubmitNest2(\"formValidate\")\n                        },\n                      },\n                    },\n                    [_vm._v(\"下一步\")]\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-button\",\n                    {\n                      directives: [\n                        {\n                          name: \"show\",\n                          rawName: \"v-show\",\n                          value: _vm.currentTab === 2,\n                          expression: \"currentTab===2\",\n                        },\n                        {\n                          name: \"hasPermi\",\n                          rawName: \"v-hasPermi\",\n                          value: [\"admin:bargain:update\"],\n                          expression: \"['admin:bargain:update']\",\n                        },\n                      ],\n                      staticClass: \"submission\",\n                      attrs: {\n                        loading: _vm.loading,\n                        type: \"primary\",\n                        size: \"small\",\n                      },\n                      on: {\n                        click: function ($event) {\n                          return _vm.handleSubmit(\"formValidate\")\n                        },\n                      },\n                    },\n                    [_vm._v(\"提交\")]\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\"CreatTemplates\", {\n        ref: \"addTemplates\",\n        on: { getList: _vm.getShippingList },\n      }),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}