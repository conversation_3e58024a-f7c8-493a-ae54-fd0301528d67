{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\marketing\\coupon\\list\\creatCoupon.vue?vue&type=template&id=945b1358&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\marketing\\coupon\\list\\creatCoupon.vue", "mtime": 1753666157891}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"divBox\" },\n    [\n      _c(\n        \"el-card\",\n        { staticClass: \"box-card\" },\n        [\n          _c(\n            \"el-form\",\n            {\n              ref: \"ruleForm\",\n              staticClass: \"demo-ruleForm\",\n              attrs: {\n                model: _vm.ruleForm,\n                rules: _vm.rules,\n                \"label-width\": \"150px\",\n              },\n            },\n            [\n              _c(\n                \"el-form-item\",\n                { attrs: { label: \"优惠劵名称\", prop: \"name\" } },\n                [\n                  _c(\"el-input\", {\n                    staticStyle: { width: \"350px\" },\n                    attrs: { placeholder: \"请输入优惠券名称\" },\n                    model: {\n                      value: _vm.ruleForm.name,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.ruleForm, \"name\", $$v)\n                      },\n                      expression: \"ruleForm.name\",\n                    },\n                  }),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                { attrs: { label: \"优惠劵类型\" } },\n                [\n                  _c(\n                    \"el-radio-group\",\n                    {\n                      model: {\n                        value: _vm.ruleForm.useType,\n                        callback: function ($$v) {\n                          _vm.$set(_vm.ruleForm, \"useType\", $$v)\n                        },\n                        expression: \"ruleForm.useType\",\n                      },\n                    },\n                    [\n                      _c(\"el-radio\", { attrs: { label: 1 } }, [\n                        _vm._v(\"通用券\"),\n                      ]),\n                      _vm._v(\" \"),\n                      _c(\"el-radio\", { attrs: { label: 2 } }, [\n                        _vm._v(\"商品券\"),\n                      ]),\n                      _vm._v(\" \"),\n                      _c(\"el-radio\", { attrs: { label: 3 } }, [\n                        _vm._v(\"品类券\"),\n                      ]),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _vm.ruleForm.useType === 3\n                ? _c(\n                    \"el-form-item\",\n                    { attrs: { label: \"选择品类：\", prop: \"primaryKey\" } },\n                    [\n                      _c(\"el-cascader\", {\n                        staticClass: \"selWidth\",\n                        attrs: {\n                          options: _vm.merCateList,\n                          props: _vm.props2,\n                          clearable: \"\",\n                          \"show-all-levels\": false,\n                        },\n                        model: {\n                          value: _vm.ruleForm.primaryKey,\n                          callback: function ($$v) {\n                            _vm.$set(_vm.ruleForm, \"primaryKey\", $$v)\n                          },\n                          expression: \"ruleForm.primaryKey\",\n                        },\n                      }),\n                    ],\n                    1\n                  )\n                : _vm._e(),\n              _vm._v(\" \"),\n              _vm.ruleForm.useType === 2\n                ? _c(\n                    \"el-form-item\",\n                    { attrs: { label: \"商品：\", prop: \"checked\" } },\n                    [\n                      _c(\n                        \"div\",\n                        { staticClass: \"acea-row\" },\n                        [\n                          _vm.ruleForm.checked.length\n                            ? _vm._l(\n                                _vm.ruleForm.checked,\n                                function (item, index) {\n                                  return _c(\n                                    \"div\",\n                                    { key: index, staticClass: \"pictrue\" },\n                                    [\n                                      _c(\"img\", { attrs: { src: item.image } }),\n                                      _vm._v(\" \"),\n                                      _c(\"i\", {\n                                        staticClass: \"el-icon-error btndel\",\n                                        on: {\n                                          click: function ($event) {\n                                            return _vm.handleRemove(index)\n                                          },\n                                        },\n                                      }),\n                                    ]\n                                  )\n                                }\n                              )\n                            : _vm._e(),\n                          _vm._v(\" \"),\n                          _c(\n                            \"div\",\n                            {\n                              staticClass: \"upLoadPicBox\",\n                              on: { click: _vm.changeGood },\n                            },\n                            [\n                              _c(\"div\", { staticClass: \"upLoad\" }, [\n                                _c(\"i\", {\n                                  staticClass: \"el-icon-camera cameraIconfont\",\n                                }),\n                              ]),\n                            ]\n                          ),\n                        ],\n                        2\n                      ),\n                    ]\n                  )\n                : _vm._e(),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                { attrs: { label: \"优惠券面值\", prop: \"money\" } },\n                [\n                  _c(\"el-input-number\", {\n                    attrs: { min: 1, label: \"描述文字\" },\n                    model: {\n                      value: _vm.ruleForm.money,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.ruleForm, \"money\", $$v)\n                      },\n                      expression: \"ruleForm.money\",\n                    },\n                  }),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                { attrs: { label: \"使用门槛\" } },\n                [\n                  _c(\n                    \"el-radio-group\",\n                    {\n                      model: {\n                        value: _vm.threshold,\n                        callback: function ($$v) {\n                          _vm.threshold = $$v\n                        },\n                        expression: \"threshold\",\n                      },\n                    },\n                    [\n                      _c(\"el-radio\", { attrs: { label: false } }, [\n                        _vm._v(\"无门槛\"),\n                      ]),\n                      _vm._v(\" \"),\n                      _c(\"el-radio\", { attrs: { label: true } }, [\n                        _vm._v(\"有门槛\"),\n                      ]),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _vm.threshold\n                ? _c(\n                    \"el-form-item\",\n                    { attrs: { label: \"优惠券最低消费\", prop: \"minPrice\" } },\n                    [\n                      _c(\"el-input-number\", {\n                        attrs: { min: 1, label: \"描述文字\" },\n                        model: {\n                          value: _vm.ruleForm.minPrice,\n                          callback: function ($$v) {\n                            _vm.$set(_vm.ruleForm, \"minPrice\", $$v)\n                          },\n                          expression: \"ruleForm.minPrice\",\n                        },\n                      }),\n                    ],\n                    1\n                  )\n                : _vm._e(),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                { attrs: { label: \"使用有效期\" } },\n                [\n                  _c(\n                    \"el-radio-group\",\n                    {\n                      model: {\n                        value: _vm.ruleForm.isFixedTime,\n                        callback: function ($$v) {\n                          _vm.$set(_vm.ruleForm, \"isFixedTime\", $$v)\n                        },\n                        expression: \"ruleForm.isFixedTime\",\n                      },\n                    },\n                    [\n                      _c(\"el-radio\", { attrs: { label: false } }, [\n                        _vm._v(\"天数\"),\n                      ]),\n                      _vm._v(\" \"),\n                      _c(\"el-radio\", { attrs: { label: true } }, [\n                        _vm._v(\"时间段\"),\n                      ]),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              !_vm.ruleForm.isFixedTime\n                ? _c(\n                    \"el-form-item\",\n                    { attrs: { label: \"使用有效期限（天）\", prop: \"day\" } },\n                    [\n                      _c(\"el-input-number\", {\n                        attrs: { min: 0, max: 999, label: \"描述文字\" },\n                        model: {\n                          value: _vm.ruleForm.day,\n                          callback: function ($$v) {\n                            _vm.$set(_vm.ruleForm, \"day\", $$v)\n                          },\n                          expression: \"ruleForm.day\",\n                        },\n                      }),\n                    ],\n                    1\n                  )\n                : _vm._e(),\n              _vm._v(\" \"),\n              _vm.ruleForm.isFixedTime\n                ? _c(\n                    \"el-form-item\",\n                    { attrs: { label: \"使用有效期限\", prop: \"resource\" } },\n                    [\n                      _c(\"el-date-picker\", {\n                        staticStyle: { width: \"550px\" },\n                        attrs: {\n                          type: \"datetimerange\",\n                          \"range-separator\": \"至\",\n                          \"value-format\": \"yyyy-MM-dd HH:mm:ss\",\n                          \"start-placeholder\": \"开始日期\",\n                          \"picker-options\": _vm.pickerOptions,\n                          \"end-placeholder\": \"结束日期\",\n                        },\n                        model: {\n                          value: _vm.termTime,\n                          callback: function ($$v) {\n                            _vm.termTime = $$v\n                          },\n                          expression: \"termTime\",\n                        },\n                      }),\n                    ],\n                    1\n                  )\n                : _vm._e(),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                { attrs: { label: \"领取是否限时\", prop: \"isForever\" } },\n                [\n                  _c(\n                    \"el-radio-group\",\n                    {\n                      model: {\n                        value: _vm.ruleForm.isForever,\n                        callback: function ($$v) {\n                          _vm.$set(_vm.ruleForm, \"isForever\", $$v)\n                        },\n                        expression: \"ruleForm.isForever\",\n                      },\n                    },\n                    [\n                      _c(\"el-radio\", { attrs: { label: true } }, [\n                        _vm._v(\"限时\"),\n                      ]),\n                      _vm._v(\" \"),\n                      _c(\"el-radio\", { attrs: { label: false } }, [\n                        _vm._v(\"不限时\"),\n                      ]),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _vm.ruleForm.isForever\n                ? _c(\n                    \"el-form-item\",\n                    { attrs: { label: \"领取时间\" } },\n                    [\n                      _c(\"el-date-picker\", {\n                        staticStyle: { width: \"550px\" },\n                        attrs: {\n                          type: \"datetimerange\",\n                          \"range-separator\": \"至\",\n                          \"value-format\": \"yyyy-MM-dd HH:mm:ss\",\n                          \"picker-options\": _vm.pickerOptions,\n                          \"start-placeholder\": \"开始日期\",\n                          \"end-placeholder\": \"结束日期\",\n                        },\n                        on: { blur: _vm.handleTimestamp },\n                        model: {\n                          value: _vm.isForeverTime,\n                          callback: function ($$v) {\n                            _vm.isForeverTime = $$v\n                          },\n                          expression: \"isForeverTime\",\n                        },\n                      }),\n                    ],\n                    1\n                  )\n                : _vm._e(),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                { attrs: { label: \"领取方式\", prop: \"resource\" } },\n                [\n                  _c(\n                    \"el-radio-group\",\n                    {\n                      model: {\n                        value: _vm.ruleForm.type,\n                        callback: function ($$v) {\n                          _vm.$set(_vm.ruleForm, \"type\", $$v)\n                        },\n                        expression: \"ruleForm.type\",\n                      },\n                    },\n                    [\n                      _c(\"el-radio\", { attrs: { label: 1 } }, [\n                        _vm._v(\"手动领取\"),\n                      ]),\n                      _vm._v(\" \"),\n                      _c(\"el-radio\", { attrs: { label: 2 } }, [\n                        _vm._v(\"新人券\"),\n                      ]),\n                      _vm._v(\" \"),\n                      _c(\"el-radio\", { attrs: { label: 3 } }, [\n                        _vm._v(\"赠送券\"),\n                      ]),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                { attrs: { label: \"是否限量\", prop: \"isLimited\" } },\n                [\n                  _c(\n                    \"el-radio-group\",\n                    {\n                      model: {\n                        value: _vm.ruleForm.isLimited,\n                        callback: function ($$v) {\n                          _vm.$set(_vm.ruleForm, \"isLimited\", $$v)\n                        },\n                        expression: \"ruleForm.isLimited\",\n                      },\n                    },\n                    [\n                      _c(\"el-radio\", { attrs: { label: true } }, [\n                        _vm._v(\"限量\"),\n                      ]),\n                      _vm._v(\" \"),\n                      _c(\"el-radio\", { attrs: { label: false } }, [\n                        _vm._v(\"不限量\"),\n                      ]),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _vm.ruleForm.isLimited\n                ? _c(\n                    \"el-form-item\",\n                    { attrs: { label: \"发布数量\", prop: \"total\" } },\n                    [\n                      _c(\"el-input-number\", {\n                        attrs: { min: 1, label: \"排序\" },\n                        model: {\n                          value: _vm.ruleForm.total,\n                          callback: function ($$v) {\n                            _vm.$set(_vm.ruleForm, \"total\", $$v)\n                          },\n                          expression: \"ruleForm.total\",\n                        },\n                      }),\n                    ],\n                    1\n                  )\n                : _vm._e(),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                { attrs: { label: \"排序\", prop: \"sort\" } },\n                [\n                  _c(\"el-input-number\", {\n                    attrs: { min: 0, label: \"排序\" },\n                    model: {\n                      value: _vm.ruleForm.sort,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.ruleForm, \"sort\", $$v)\n                      },\n                      expression: \"ruleForm.sort\",\n                    },\n                  }),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                { attrs: { label: \"状态\", prop: \"status\" } },\n                [\n                  _c(\n                    \"el-radio-group\",\n                    {\n                      model: {\n                        value: _vm.ruleForm.status,\n                        callback: function ($$v) {\n                          _vm.$set(_vm.ruleForm, \"status\", $$v)\n                        },\n                        expression: \"ruleForm.status\",\n                      },\n                    },\n                    [\n                      _c(\"el-radio\", { attrs: { label: true } }, [\n                        _vm._v(\"开启\"),\n                      ]),\n                      _vm._v(\" \"),\n                      _c(\"el-radio\", { attrs: { label: false } }, [\n                        _vm._v(\"关闭\"),\n                      ]),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                [\n                  _c(\n                    \"el-button\",\n                    {\n                      directives: [\n                        {\n                          name: \"hasPermi\",\n                          rawName: \"v-hasPermi\",\n                          value: [\"admin:coupon:save\"],\n                          expression: \"['admin:coupon:save']\",\n                        },\n                      ],\n                      attrs: {\n                        size: \"mini\",\n                        type: \"primary\",\n                        loading: _vm.loading,\n                      },\n                      on: {\n                        click: function ($event) {\n                          return _vm.submitForm(\"ruleForm\")\n                        },\n                      },\n                    },\n                    [_vm._v(\"立即创建\")]\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}