@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.Popup.data-v-4147c09e {
  width: 500rpx;
  background-color: #fff;
  position: fixed;
  top: 50%;
  left: 50%;
  margin-left: -250rpx;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  z-index: 320;
}
.Popup image.data-v-4147c09e {
  width: 150rpx;
  height: 150rpx;
  margin: -67rpx auto 0 auto;
  display: block;
  border: 8rpx solid #fff;
  border-radius: 50%;
}
.Popup .title.data-v-4147c09e {
  font-size: 28rpx;
  color: #000;
  text-align: center;
  margin-top: 30rpx;
}
.Popup .tip.data-v-4147c09e {
  font-size: 22rpx;
  color: #555;
  padding: 0 24rpx;
  margin-top: 25rpx;
}
.Popup .bottom .item.data-v-4147c09e {
  width: 50%;
  height: 80rpx;
  background-color: #eeeeee;
  text-align: center;
  line-height: 80rpx;
  font-size: 24rpx;
  color: #666;
  margin-top: 54rpx;
}
.Popup .bottom .item.on.data-v-4147c09e {
  width: 100%;
}
.flex.data-v-4147c09e {
  display: flex;
}
.Popup .bottom .item.grant.data-v-4147c09e {
  font-size: 28rpx;
  color: #fff;
  font-weight: bold;
  background-color: #c9ab79;
  border-radius: 0;
  padding: 0;
}
.mask.data-v-4147c09e {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.65);
  z-index: 310;
}

