{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/goods_combination_status/index.vue?3f7b", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/goods_combination_status/index.vue?e5ee", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/goods_combination_status/index.vue?8c48", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/goods_combination_status/index.vue?e477", "uni-app:///pages/activity/goods_combination_status/index.vue", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/goods_combination_status/index.vue?7cb4", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/goods_combination_status/index.vue?13e0"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "components", "CountDown", "ProductWindow", "home", "authorize", "props", "data", "bgColor", "currentPinkOrder", "isOk", "pinkBool", "userBool", "pinkAll", "pinkT", "storeCombination", "storeCombinationHost", "pinkId", "count", "iShidden", "isOpen", "attr", "cartAttr", "productSelect", "image", "storeName", "price", "quota", "unique", "cart_num", "quotaShow", "stock", "num", "attrValue", "productAttr", "limit", "page", "loading", "loadend", "userInfo", "posters", "H5ShareBox", "isAuto", "isShowAuth", "onceNum", "timestamp", "qrcodeSize", "posterbackgd", "PromotionCode", "canvasStatus", "imgTop", "imagePath", "watch", "userData", "handler", "app", "deep", "computed", "onLoad", "that", "onShow", "mounted", "onShareAppMessage", "title", "path", "imageUrl", "methods", "listenerActionClose", "combinationMore", "comId", "then", "catch", "auth<PERSON><PERSON><PERSON>", "onLoadFun", "iptCartNum", "attrVal", "onMyEvent", "ChangeAttr", "ChangeCartNum", "arrMin", "DefaultSelect", "value", "setProductSelect", "pay", "goPay", "go<PERSON><PERSON><PERSON>", "goOrder", "uni", "url", "goList", "goDetail", "getImageBase64", "make", "uQRCode", "canvasId", "text", "size", "margin", "success", "complete", "fail", "getCombinationPink", "res", "getCombinationRemove", "id", "cid", "tab", "lookAll"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACmM;AACnM,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtBA;AAAA;AAAA;AAAA;AAAkwB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC2HtxB;AACA;AAGA;AAGA;AAGA;AAKA;AAEA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAKA;AACA;AAAA,eACA;EACAC;EACAC;IACAC;IACAC;IACAC;IAEAC;EAEA;EACAC;EACAC;IACA;MACAC;QACA;QACA;QACA;QACA;QACA;MACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;QACAC;QACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;QACAC;QACAC;MACA;MACAL;MACAM;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACAC;MACAC;QACA;UACA;UACAC,gGACA;QACA;MACA;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;IACAC;IACA;MACA;IACA;MACA;MACA;IACA;EACA;EACAC;EACAC;IACA;EACA;EACA;EACA;;EAEA;AACA;AACA;EACAC;IACA;IACA;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACAhC;QACAD;QACAkC;MACA;MACA;MACA,wCACAC;QACA;QACA;QACAX;QACAA;QACAA;QACAA;QACAA;MACA,GACAY;QACAZ;QACAA;UACAI;QACA;MACA;IACA;IACA;IACAS;MACA;IACA;IACA;IACAC;MACA;MACAlB;MACA;IACA;IACA;AACA;AACA;AACA;IACAmB;MACA;QACA;UACAX;QACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;IACAY;MACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACAvD;QACA;MACA;MACA;MACA,4FACAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAS;QACA;QACA+C;QACAA;QACAA;QACA;QACA;UACA;UACA;QACA;QACA;QACA;MACA;QACA/C;QACA;UACA;UACA;QACA;QACA;QACA;MACA;IACA;IACA;IACAgD;MACA;QACAC;MACA;QACA;UACAA;UACA;QACA;MACA;MACA;QACA;MACA;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;;IACAC;MACA;MACA;MACA7D;MACAA;MACAA;MACAA;MACAA;MACAA;MACAA;MACAsC;IACA;IACAwB;MACA;MACAxB;MACAA;IACA;IACAyB;MACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACAC,+BA+CA;IACAC;MACA;MACAC;QACAC;MACA;IACA;IACA;IACAC;MACAF;QACAC;MACA;IACA;IACA;IACAE;MACA;MACAH;QACAC;MACA;IACA;IACA;IACAG;MACA;MACA;QACAH;MACA;QACA7B;MACA;IACA;IACA;IACAiC;MAAA;MACA,sDACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;UACA;QACA;QACAC;QACAC;UACA;YACArC;UACA;QACA;MACA;IACA;IACA;IACAsC;MACA;MACA,+CACA/B;QACA;QACAgC;QACA3C;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QAOAA;QACA;MAEA,GACAY;QACA;UACAZ;YACAI;UACA;YACAyB;UACA;QACA;MACA;IACA;IA8BA;IACAe;MACA;MACA;QACAC;QACAC;MACA,GACAnC;QACAX;UACAI;QACA;UACA2C;QACA;MACA,GACAnC;QACAZ;UACAI;QACA;MACA;IACA;IACA4C;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtqBA;AAAA;AAAA;AAAA;AAAq8C,CAAgB,ovCAAG,EAAC,C;;;;;;;;;;;ACAz9C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/activity/goods_combination_status/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/activity/goods_combination_status/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=35f14445&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=35f14445&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"35f14445\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/activity/goods_combination_status/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=35f14445&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.pinkAll.length\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.H5ShareBox = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<div class=\"group-con\">\r\n\t\t<div class=\"header acea-row row-between-wrapper\">\r\n\t\t\t<div class=\"pictrue\"><img :src=\"storeCombination.image\" /></div>\r\n\t\t\t<div class=\"text\">\r\n\t\t\t\t<div class=\"line1\" v-text=\"storeCombination.title\"></div>\r\n\t\t\t\t<div class=\"money\">\r\n\t\t\t\t\t￥\r\n\t\t\t\t\t<span class=\"num\" v-text=\"storeCombination.price || 0\"></span>\r\n\t\t\t\t\t<span class=\"team cart-color\">{{storeCombination.people +'人拼'}}</span>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\t<div v-if=\"pinkBool === -1\" class=\"iconfont icon-pintuanshibai\"></div>\r\n\t\t\t<div v-else-if=\"pinkBool === 1\" class=\"iconfont icon-pintuanchenggong font-color-red\"></div>\r\n\t\t</div>\r\n\t\t<div class=\"wrapper\">\r\n\t\t\t<div class=\"title acea-row row-center-wrapper\" v-if=\"pinkBool === 0\">\r\n\t\t\t\t<div class=\"line\"></div>\r\n\t\t\t\t<div class=\"name acea-row row-center-wrapper\">\r\n\t\t\t\t\t剩余\r\n\t\t\t\t\t<CountDown :bgColor=\"bgColor\" :is-day=\"false\" :tip-text=\"' '\" :day-text=\"' '\" :hour-text=\"' : '\"\r\n\t\t\t\t\t\t:minute-text=\"' : '\" :second-text=\"' '\" :datatime=\"pinkT.stopTime/1000\"></CountDown>\r\n\t\t\t\t\t<span class=\"end\">结束</span>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div class=\"line\"></div>\r\n\t\t\t</div>\r\n\t\t\t<div class=\"tips font-color-red\" v-if=\"pinkBool === 1\">恭喜您拼团成功</div>\r\n\t\t\t<div class=\"tips\" v-else-if=\"pinkBool === -1\">还差{{ count }}人，拼团失败</div>\r\n\t\t\t<div class=\"tips font-color-red\" v-else-if=\"pinkBool === 0\">拼团中，还差{{ count }}人拼团成功</div>\r\n\t\t\t<div class=\"list acea-row row-middle\"\r\n\t\t\t\t:class=\"[pinkBool === 1 || pinkBool === -1 ? 'result' : '', iShidden ? 'on' : '']\">\r\n\t\t\t\t<div class=\"pinkT\">\r\n\t\t\t\t\t<div class=\"pictrue\"><img :src=\"pinkT.avatar\" /></div>\r\n\t\t\t\t\t<div class=\"chief\">团长</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<block v-if=\"pinkAll.length > 0\">\r\n\t\t\t\t\t<div class=\"pictrue\" v-for=\"(item, index) in pinkAll\" :key=\"index\"><img :src=\"item.avatar\" /></div>\r\n\t\t\t\t</block>\r\n\t\t\t\t<div class=\"pictrue\" v-for=\"index in count\" :key=\"index\"><img class=\"img-none\"\r\n\t\t\t\t\t\tsrc=\"/static/images/vacancy.png\" /></div>\r\n\t\t\t</div>\r\n\t\t\t<div v-if=\"(pinkBool === 1 || pinkBool === -1) && count > 9\" class=\"lookAll acea-row row-center-wrapper\"\r\n\t\t\t\t@click=\"lookAll\">\r\n\t\t\t\t{{ iShidden ? '收起' : '查看全部' }}\r\n\t\t\t\t<span class=\"iconfont\" :class=\"iShidden ? 'icon-xiangshang' : 'icon-xiangxia'\"></span>\r\n\t\t\t</div>\r\n\t\t\t<!-- #ifndef MP -->\r\n\t\t\t<div v-if=\"userBool === 1 && isOk == 0 && pinkBool === 0\">\r\n\t\t\t\t<div class=\"teamBnt bg-color-red\" v-if='pinkT.stopTime>timestamp' @click=\"goPoster\">邀请好友参团</div>\r\n\t\t\t</div>\r\n\t\t\t<!-- #endif -->\r\n\t\t\t<!-- #ifdef MP -->\r\n\t\t\t<button open-type=\"share\" class=\"teamBnt bg-color-red\"\r\n\t\t\t\tv-if=\"userBool === 1 && isOk == 0 && pinkBool === 0 && pinkT.stopTime>timestamp\">邀请好友参团</button>\r\n\t\t\t<!-- #endif -->\r\n\t\t\t<div class=\"teamBnt bg-color-hui\" v-if=\"pinkT.stopTime<timestamp && isOk == 0 && pinkBool === 0\">拼团已过期</div>\r\n\t\t\t<div class=\"teamBnt bg-color-red\"\r\n\t\t\t\tv-else-if=\"userBool === 0 && pinkBool === 0 && count > 0 && pinkT.stopTime>timestamp\" @click=\"pay\">我要参团\r\n\t\t\t</div>\r\n\t\t\t<div class=\"teamBnt bg-color-red\" v-if=\"pinkBool === 1 || pinkBool === -1\"\r\n\t\t\t\t@click=\"goDetail(storeCombination.id)\">再次开团</div>\r\n\t\t\t<div class=\"cancel\" @click=\"getCombinationRemove\" v-if=\"pinkBool === 0 && userBool === 1\">\r\n\t\t\t\t<span class=\"iconfont icon-guanbi3\"></span>\r\n\t\t\t\t取消开团\r\n\t\t\t</div>\r\n\r\n\t\t\t<div class=\"lookOrder\" v-if=\"pinkBool === 1\" @click=\"goOrder\">\r\n\t\t\t\t查看订单信息\r\n\t\t\t\t<span class=\"iconfont icon-xiangyou\"></span>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t<div class=\"group-recommend\">\r\n\t\t\t<div class=\"title acea-row row-between-wrapper\">\r\n\t\t\t\t<div>大家都在拼</div>\r\n\t\t\t\t<div class=\"more\" @click=\"goList\">\r\n\t\t\t\t\t更多拼团\r\n\t\t\t\t\t<span class=\"iconfont icon-jiantou\"></span>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t\t<div class=\"list acea-row row-middle\">\r\n\t\t\t\t<div class=\"item\" v-for=\"(item, index) in storeCombinationHost\" :key=\"index\" @click=\"goDetail(item.id)\">\r\n\t\t\t\t\t<div class=\"pictrue\">\r\n\t\t\t\t\t\t<img :src=\"item.image\" />\r\n\t\t\t\t\t\t<div class=\"team\" v-text=\"item.people + '人团'\"></div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div class=\"name line1\" v-text=\"item.title\"></div>\r\n\t\t\t\t\t<div class=\"money font-color-red\" v-text=\"'￥' + item.price\"></div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t<product-window :attr=\"attr\" :limitNum=\"1\" :iSbnt=\"1\" @myevent=\"onMyEvent\" @ChangeAttr=\"ChangeAttr\"\r\n\t\t\t@ChangeCartNum=\"ChangeCartNum\" @iptCartNum=\"iptCartNum\" @attrVal=\"attrVal\" @goCat=\"goPay\"></product-window>\r\n\t\t<view class=\"mask\" v-if=\"posters || canvasStatus\" @click=\"listenerActionClose\"></view>\r\n\t\t<!-- 发送给朋友图片 -->\r\n\t\t<view class=\"share-box\" v-if=\"H5ShareBox\">\r\n\t\t\t<image src=\"/static/images/share-info.png\" @click=\"H5ShareBox = false\"></image>\r\n\t\t</view>\r\n\t\t<!-- 海报展示 -->\r\n\t\t<view class='poster-pop' v-if=\"canvasStatus\">\r\n\t\t\t<image :src='imagePath'></image>\r\n\t\t\t<!-- #ifndef H5  -->\r\n\t\t\t<view class='save-poster' @click=\"savePosterPath\">保存到手机</view>\r\n\t\t\t<!-- #endif -->\r\n\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t<view class=\"keep\">长按图片保存至相册</view>\r\n\t\t\t<view class='iconfont icon-cha2 close' @tap='listenerActionClose'></view>\r\n\t\t\t<!-- #endif -->\r\n\t\t</view>\r\n\t\t<view class=\"canvas\">\r\n\t\t\t<canvas style=\"width:597px;height:850px;\" canvas-id=\"activityCanvas\"></canvas>\r\n\t\t\t<canvas canvas-id=\"qrcode\" :style=\"{width: `${qrcodeSize}px`, height: `${qrcodeSize}px`}\"\r\n\t\t\t\tstyle=\"opacity: 0;\" />\r\n\t\t</view>\r\n\r\n\t\t<!-- #ifdef MP -->\r\n\t\t<!-- <authorize @onLoadFun=\"onLoadFun\" :isAuto=\"isAuto\" :isShowAuth=\"isShowAuth\" @authColse=\"authColse\"></authorize> -->\r\n\t\t<!-- #endif -->\r\n\t\t<home></home>\r\n\t</div>\r\n</template>\r\n<script>\r\n\timport CountDown from '@/components/countDown';\r\n\timport ProductWindow from '@/components/productWindow';\r\n\timport uQRCode from '@/js_sdk/Sansnn-uQRCode/uqrcode.js';\r\n\timport {\r\n\t\timageBase64\r\n\t} from \"@/api/public\";\r\n\timport {\r\n\t\ttoLogin\r\n\t} from '@/libs/login.js';\r\n\timport {\r\n\t\tmapGetters\r\n\t} from 'vuex';\r\n\timport {\r\n\t\tgetCombinationPink,\r\n\t\tpostCombinationRemove,\r\n\t\tgetCombinationMore\r\n\t} from '@/api/activity';\r\n\timport {\r\n\t\tpostCartAdd\r\n\t} from '@/api/store';\r\n\t// #ifdef MP\r\n\timport authorize from '@/components/Authorize';\r\n\t// #endif\r\n\timport home from '@/components/home';\r\n\tconst NAME = 'GroupRule';\r\n\tconst app = getApp();\r\n\texport default {\r\n\t\tname: NAME,\r\n\t\tcomponents: {\r\n\t\t\tCountDown,\r\n\t\t\tProductWindow,\r\n\t\t\thome,\r\n\t\t\t// #ifdef MP\r\n\t\t\tauthorize\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tprops: {},\r\n\t\tdata: function() {\r\n\t\t\treturn {\r\n\t\t\t\tbgColor: {\r\n\t\t\t\t\t'bgColor': '#333333',\r\n\t\t\t\t\t'Color': '#fff',\r\n\t\t\t\t\t'width': '44rpx',\r\n\t\t\t\t\t'timeTxtwidth': '16rpx',\r\n\t\t\t\t\t'isDay': true\r\n\t\t\t\t},\r\n\t\t\t\tcurrentPinkOrder: '', //当前拼团订单\r\n\t\t\t\tisOk: 0, //判断拼团是否完成\r\n\t\t\t\tpinkBool: 0, //判断拼团是否成功|0=失败,1=成功\r\n\t\t\t\tuserBool: 0, //判断当前用户是否在团内|0=未在,1=在\r\n\t\t\t\tpinkAll: [], //团员\r\n\t\t\t\tpinkT: [], //团长信息\r\n\t\t\t\tstoreCombination: {}, //拼团产品\r\n\t\t\t\tstoreCombinationHost: [], //拼团推荐\r\n\t\t\t\tpinkId: 0,\r\n\t\t\t\tcount: 0, //拼团剩余人数\r\n\t\t\t\tiShidden: false,\r\n\t\t\t\tisOpen: false, //是否打开属性组件\r\n\t\t\t\tattr: {\r\n\t\t\t\t\tcartAttr: false,\r\n\t\t\t\t\tproductSelect: {\r\n\t\t\t\t\t\timage: '',\r\n\t\t\t\t\t\tstoreName: '',\r\n\t\t\t\t\t\tprice: '',\r\n\t\t\t\t\t\tquota: 0,\r\n\t\t\t\t\t\tunique: '',\r\n\t\t\t\t\t\tcart_num: 1,\r\n\t\t\t\t\t\tquotaShow: 0,\r\n\t\t\t\t\t\tstock: 0,\r\n\t\t\t\t\t\tnum: 0\r\n\t\t\t\t\t},\r\n\t\t\t\t\tattrValue: '',\r\n\t\t\t\t\tproductAttr: []\r\n\t\t\t\t},\r\n\t\t\t\tcart_num: '',\r\n\t\t\t\tlimit: 10,\r\n\t\t\t\tpage: 1,\r\n\t\t\t\tloading: false,\r\n\t\t\t\tloadend: false,\r\n\t\t\t\tuserInfo: {},\r\n\t\t\t\tposters: false, // app分享\r\n\t\t\t\tH5ShareBox: false, //公众号分享图片\r\n\t\t\t\tisAuto: false, //没有授权的不会自动授权\r\n\t\t\t\tisShowAuth: false, //是否隐藏授权\r\n\t\t\t\tonceNum: 0, //一次可以购买几个,\r\n\t\t\t\ttimestamp: 0, // 当前时间戳\r\n\t\t\t\tqrcodeSize: 600,\r\n\t\t\t\tposterbackgd: '/static/images/canbj.png',\r\n\t\t\t\tPromotionCode: '', //二维码\r\n\t\t\t\tcanvasStatus: false,\r\n\t\t\t\timgTop: '', //商品图base64位\r\n\t\t\t\timagePath: '' // 海报图片\r\n\t\t\t};\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tuserData: {\r\n\t\t\t\thandler: function(newV, oldV) {\r\n\t\t\t\t\tif (newV) {\r\n\t\t\t\t\t\tthis.userInfo = newV;\r\n\t\t\t\t\t\tapp.globalData.openPages = '/pages/activity/goods_combination_status/index?id=' + this.pinkId +\r\n\t\t\t\t\t\t\t\"&spread=\" + this.uid;\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tdeep: true\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: mapGetters({\r\n\t\t\t'isLogin': 'isLogin',\r\n\t\t\t'userData': 'userInfo',\r\n\t\t\t'uid': 'uid'\r\n\t\t}),\r\n\t\tonLoad(options) {\r\n\t\t\tvar that = this;\r\n\t\t\tthat.pinkId = options.id;\r\n\t\t\tif (that.isLogin == false) {\r\n\t\t\t\ttoLogin();\r\n\t\t\t} else {\r\n\t\t\t\tthis.timestamp = (new Date()).getTime();\r\n\t\t\t\tthis.getCombinationPink();\r\n\t\t\t}\r\n\t\t},\r\n\t\tonShow() {},\r\n\t\tmounted: function() {\r\n\t\t\tthis.combinationMore();\r\n\t\t},\r\n\t\t// link: window.location.protocol + '//' + window.location.host +\r\n\t\t// \t'/pages/activity/goods_combination_status/index?id=' + that.pinkId + \"&spread=\" + this.uid,\r\n\t\t//#ifdef MP\r\n\t\t/**\r\n\t\t * 用户点击右上角分享\r\n\t\t */\r\n\t\tonShareAppMessage: function() {\r\n\t\t\tlet that = this;\r\n\t\t\treturn {\r\n\t\t\t\ttitle: '您的好友' + that.userInfo.nickname + '邀请您参团' + that.storeCombination.title,\r\n\t\t\t\tpath: app.globalData.openPages,\r\n\t\t\t\timageUrl: that.storeCombination.image\r\n\t\t\t};\r\n\t\t},\r\n\t\t//#endif\r\n\t\tmethods: {\r\n\t\t\t// 分享关闭\r\n\t\t\tlistenerActionClose: function() {\r\n\t\t\t\tthis.posters = false;\r\n\t\t\t\tthis.canvasStatus = false;\r\n\t\t\t},\r\n\t\t\t// 更多拼团\r\n\t\t\tcombinationMore: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tif (that.loadend) return;\r\n\t\t\t\tif (that.loading) return;\r\n\t\t\t\tvar data = {\r\n\t\t\t\t\tpage: that.page,\r\n\t\t\t\t\tlimit: that.limit,\r\n\t\t\t\t\tcomId: that.pinkId\r\n\t\t\t\t};\r\n\t\t\t\tthis.loading = true\r\n\t\t\t\tgetCombinationMore(data)\r\n\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\tvar storeCombinationHost = that.storeCombinationHost;\r\n\t\t\t\t\t\tvar limit = that.limit;\r\n\t\t\t\t\t\tthat.page++;\r\n\t\t\t\t\t\tthat.loadend = limit > res.data.length;\r\n\t\t\t\t\t\tthat.storeCombinationHost = storeCombinationHost.concat(res.data.list);\r\n\t\t\t\t\t\tthat.page = that.data.page;\r\n\t\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(res => {\r\n\t\t\t\t\t\tthat.loading = false\r\n\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: res\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 授权关闭\r\n\t\t\tauthColse: function(e) {\r\n\t\t\t\tthis.isShowAuth = e;\r\n\t\t\t},\r\n\t\t\t// 授权后回调\r\n\t\t\tonLoadFun: function(e) {\r\n\t\t\t\tthis.userInfo = e;\r\n\t\t\t\tapp.globalData.openPages = '/pages/activity/goods_combination_status/index?id=' + this.pinkId;\r\n\t\t\t\tthis.getCombinationPink();\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 购物车手动填写\r\n\t\t\t *\r\n\t\t\t */\r\n\t\t\tiptCartNum: function(e) {\r\n\t\t\t\tif (e > this.onceNum) {\r\n\t\t\t\t\tthis.$util.Tips({\r\n\t\t\t\t\t\ttitle: `该商品每次限购${this.onceNum}${this.storeCombination.unitName}`\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'cart_num', this.onceNum);\r\n\t\t\t\t\tthis.$set(this, \"cart_num\", this.onceNum);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'cart_num', e);\r\n\t\t\t\t\tthis.$set(this, \"cart_num\", e);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tattrVal(val) {\r\n\t\t\t\tthis.attr.productAttr[val.indexw].index = this.attr.productAttr[val.indexw].attrValues[val.indexn];\r\n\t\t\t},\r\n\t\t\tonMyEvent: function() {\r\n\t\t\t\tthis.$set(this.attr, 'cartAttr', false);\r\n\t\t\t\tthis.$set(this, 'isOpen', false);\r\n\t\t\t},\r\n\t\t\t//将父级向子集多次传送的函数合二为一；\r\n\t\t\t// changeFun: function(opt) {\r\n\t\t\t// \tif (typeof opt !== \"object\") opt = {};\r\n\t\t\t// \tlet action = opt.action || \"\";\r\n\t\t\t// \tlet value = opt.value === undefined ? \"\" : opt.value;\r\n\t\t\t// \tthis[action] && this[action](value);\r\n\t\t\t// },\r\n\t\t\t// changeattr: function(res) {\r\n\t\t\t// \tvar that = this;\r\n\t\t\t// \tthat.attr.cartAttr = res;\r\n\t\t\t// },\r\n\t\t\t//选择属性；\r\n\t\t\tChangeAttr: function(res) {\r\n\t\t\t\tthis.$set(this, 'cart_num', 1);\r\n\t\t\t\tlet productSelect = this.productValue[res];\r\n\t\t\t\tif (productSelect) {\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'image', productSelect.image);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'price', productSelect.price);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'quota', productSelect.quota);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'unique', productSelect.id);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'cart_num', 1);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'stock', productSelect.stock);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'quotaShow', productSelect.quotaShow);\r\n\t\t\t\t\tthis.attrValue = res;\r\n\t\t\t\t\tthis.attrTxt = '已选择';\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'image', this.storeCombination.image);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'price', this.storeCombination.price);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'quota', 0);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'unique', '');\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'cart_num', 0);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'quotaShow', 0);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'stock', 0);\r\n\t\t\t\t\tthis.attrValue = '';\r\n\t\t\t\t\tthis.attrTxt = '请选择';\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tChangeCartNum: function(res) {\r\n\t\t\t\t//changeValue:是否 加|减\r\n\t\t\t\t//获取当前变动属性\r\n\t\t\t\tlet productSelect = this.productValue[this.attrValue];\r\n\t\t\t\tif (this.cart_num) {\r\n\t\t\t\t\tproductSelect.cart_num = this.cart_num;\r\n\t\t\t\t\tthis.attr.productSelect.cart_num = this.cart_num;\r\n\t\t\t\t}\r\n\t\t\t\t//如果没有属性,赋值给商品默认库存\r\n\t\t\t\tif (productSelect === undefined && !this.attr.productAttr.length) productSelect = this.attr\r\n\t\t\t\t\t.productSelect;\r\n\t\t\t\tif (productSelect === undefined) return;\r\n\t\t\t\tlet stock = productSelect.stock || 0;\r\n\t\t\t\tlet quotaShow = productSelect.quotaShow || 0;\r\n\t\t\t\tlet quota = productSelect.quota || 0;\r\n\t\t\t\tlet num = this.attr.productSelect;\r\n\t\t\t\tlet nums = this.storeCombination.num || 0;\r\n\t\t\t\t//设置默认数据\r\n\t\t\t\tif (productSelect.cart_num == undefined) productSelect.cart_num = 1;\r\n\t\t\t\tif (res) {\r\n\t\t\t\t\tnum.cart_num++;\r\n\t\t\t\t\tlet arrMin = [];\r\n\t\t\t\t\tarrMin.push(nums);\r\n\t\t\t\t\tarrMin.push(quota);\r\n\t\t\t\t\tarrMin.push(stock);\r\n\t\t\t\t\tlet minN = Math.min.apply(null, arrMin);\r\n\t\t\t\t\tif (num.cart_num >= minN) {\r\n\t\t\t\t\t\tthis.$set(this.attr.productSelect, 'cart_num', minN ? minN : 1);\r\n\t\t\t\t\t\tthis.$set(this, 'cart_num', minN ? minN : 1);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.$set(this, 'cart_num', num.cart_num);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'cart_num', num.cart_num);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tnum.cart_num--;\r\n\t\t\t\t\tif (num.cart_num < 1) {\r\n\t\t\t\t\t\tthis.$set(this.attr.productSelect, 'cart_num', 1);\r\n\t\t\t\t\t\tthis.$set(this, 'cart_num', 1);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.$set(this, 'cart_num', num.cart_num);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'cart_num', num.cart_num);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t//默认选中属性；\r\n\t\t\tDefaultSelect() {\r\n\t\t\t\tlet productAttr = this.attr.productAttr,\r\n\t\t\t\t\tvalue = [];\r\n\t\t\t\tfor (var key in this.productValue) {\r\n\t\t\t\t\tif (this.productValue[key].quota > 0) {\r\n\t\t\t\t\t\tvalue = this.attr.productAttr.length ? key.split(',') : [];\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tfor (let i = 0; i < productAttr.length; i++) {\r\n\t\t\t\t\tthis.$set(productAttr[i], 'index', value[i]);\r\n\t\t\t\t}\r\n\t\t\t\t//sort();排序函数:数字-英文-汉字；\r\n\t\t\t\tlet productSelect = this.productValue[value.join(',')];\r\n\t\t\t\tif (productSelect && productAttr.length) {\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'storeName', this.storeCombination.title);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'image', productSelect.image);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'price', productSelect.price);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'quota', productSelect.quota);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'unique', productSelect.id);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'cart_num', 1);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'stock', productSelect.stock);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'quotaShow', productSelect.quotaShow);\r\n\t\t\t\t\t//this.$set(this, 'attrValue', value.join(','));\r\n\t\t\t\t\tthis.attrValue = value.join(',');\r\n\t\t\t\t\tthis.attrTxt = '已选择';\r\n\t\t\t\t\t//this.$set(this, 'attrTxt', '已选择');\r\n\t\t\t\t} else if (!productSelect && productAttr.length) {\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'storeName', this.storeCombination.title);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'image', this.storeCombination.image);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'price', this.storeCombination.price);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'quota', 0);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'unique', '');\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'cart_num', 0);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'stock', 0);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'quotaShow', 0);\r\n\t\t\t\t\t//this.$set(this, 'attrValue', '');\r\n\t\t\t\t\tthis.attrValue = '';\r\n\t\t\t\t\tthis.attrTxt = '请选择';\r\n\t\t\t\t\t//\tthis.$set(this, 'attrTxt', '请选择');\r\n\t\t\t\t} else if (!productSelect && !productAttr.length) {\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'storeName', this.storeCombination.title);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'image', this.storeCombination.image);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'price', this.storeCombination.price);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'quota', 0);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'unique', this.storeCombination.id || '');\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'cart_num', 1);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'quotaShow', 0);\r\n\t\t\t\t\tthis.$set(this.attr.productSelect, 'stock', 0);\r\n\t\t\t\t\t//this.$set(this, 'attrValue', '');\r\n\t\t\t\t\tthis.attrValue = '';\r\n\t\t\t\t\tthis.attrTxt = '请选择';\r\n\t\t\t\t\t//this.$set(this, 'attrTxt', '请选择');\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tsetProductSelect: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar attr = that.attr;\r\n\t\t\t\tattr.productSelect.image = that.storeCombination.image;\r\n\t\t\t\tattr.productSelect.storeName = that.storeCombination.title;\r\n\t\t\t\tattr.productSelect.price = that.storeCombination.price;\r\n\t\t\t\tattr.productSelect.quota = 0;\r\n\t\t\t\tattr.productSelect.quotaShow = 0;\r\n\t\t\t\tattr.productSelect.stock = 0;\r\n\t\t\t\tattr.cartAttr = false;\r\n\t\t\t\tthat.$set(that, 'attr', attr);\r\n\t\t\t},\r\n\t\t\tpay: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tthat.attr.cartAttr = true;\r\n\t\t\t\tthat.isOpen = true;\r\n\t\t\t},\r\n\t\t\tgoPay() {\r\n\t\t\t\tthis.$Order.getPreOrder(\"buyNow\", [{\r\n\t\t\t\t\t\"attrValueId\": parseFloat(this.attr.productSelect.unique),\r\n\t\t\t\t\t\"combinationId\": parseFloat(this.storeCombination.id),\r\n\t\t\t\t\t\"productNum\": parseFloat(this.attr.productSelect.cart_num),\r\n\t\t\t\t\t\"productId\": parseFloat(this.storeCombination.productId),\r\n\t\t\t\t\t\"pinkId\": parseFloat(this.pinkId)\r\n\t\t\t\t}]);\r\n\t\t\t},\r\n\t\t\tgoPoster: function() {\r\n\t\t\t\t//#ifdef H5\r\n\t\t\t\tif (this.$wechat.isWeixin()) {\r\n\t\t\t\t\tthis.H5ShareBox = true;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\ttitle: '海报生成中',\r\n\t\t\t\t\t\tmask: true\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthis.posters = false;\r\n\t\t\t\t\tlet arrImagesUrl = '';\r\n\t\t\t\t\tlet arrImagesUrlTop = '';\r\n\t\t\t\t\tif (!this.PromotionCode) {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tthis.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: this.errT\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\treturn\r\n\t\t\t\t\t}\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tif (!this.imgTop) {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tthis.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: '无法生成商品海报！'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\treturn\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, 1000);\r\n\t\t\t\t\tuni.downloadFile({\r\n\t\t\t\t\t\turl: this.imgTop,\r\n\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\tarrImagesUrlTop = res.tempFilePath;\r\n\t\t\t\t\t\t\tlet arrImages = [this.posterbackgd, arrImagesUrlTop, this.PromotionCode];\r\n\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\tthis.$util.activityCanvas(arrImages, this.storeCombination.title,\r\n\t\t\t\t\t\t\t\t\tthis.storeCombination.price, this.storeCombination.people +\r\n\t\t\t\t\t\t\t\t\t'人团', '还差' + this.count + '人拼团成功', 9,\r\n\t\t\t\t\t\t\t\t\t(tempFilePath) => {\r\n\t\t\t\t\t\t\t\t\t\tthis.imagePath = tempFilePath;\r\n\t\t\t\t\t\t\t\t\t\tthis.canvasStatus = true;\r\n\t\t\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}, 500);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\t//#endif\r\n\t\t\t},\r\n\t\t\tgoOrder: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/order_details/index?order_id=' + that.currentPinkOrder\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t//拼团列表\r\n\t\t\tgoList: function() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/activity/goods_combination/index'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t//拼团详情\r\n\t\t\tgoDetail: function(id) {\r\n\t\t\t\tthis.pinkId = id;\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/activity/goods_combination_details/index?id=' + id\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 商品图片转base64\r\n\t\t\tgetImageBase64: function(images) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\timageBase64({\r\n\t\t\t\t\turl: images\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tthat.imgTop = res.data.code;\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 生成二维码；\r\n\t\t\tmake() {\r\n\t\t\t\tlet href = location.protocol + '//' + location.host +\r\n\t\t\t\t\t'/pages/activity/goods_combination_status/index?id=' + this.pinkId + \"&spread=\" + this.uid;\r\n\t\t\t\tuQRCode.make({\r\n\t\t\t\t\tcanvasId: 'qrcode',\r\n\t\t\t\t\ttext: href,\r\n\t\t\t\t\tsize: this.qrcodeSize,\r\n\t\t\t\t\tmargin: 10,\r\n\t\t\t\t\tsuccess: res => {\r\n\t\t\t\t\t\tthis.PromotionCode = res;\r\n\t\t\t\t\t},\r\n\t\t\t\t\tcomplete: () => {},\r\n\t\t\t\t\tfail: res => {\r\n\t\t\t\t\t\tthis.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: '海报二维码生成失败！'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//拼团信息\r\n\t\t\tgetCombinationPink: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tgetCombinationPink(that.pinkId)\r\n\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\tlet storeCombination = res.data.storeCombination;\r\n\t\t\t\t\t\tres.data.pinkT.stop_time = parseInt(res.data.pinkT.stopTime);\r\n\t\t\t\t\t\tthat.$set(that, 'storeCombination', storeCombination);\r\n\t\t\t\t\t\tthat.$set(that.attr.productSelect, 'num', storeCombination.totalNum);\r\n\t\t\t\t\t\tthat.$set(that, 'pinkT', res.data.pinkT);\r\n\t\t\t\t\t\tthat.$set(that, 'pinkAll', res.data.pinkAll);\r\n\t\t\t\t\t\tthat.$set(that, 'count', res.data.count);\r\n\t\t\t\t\t\tthat.$set(that, 'userBool', res.data.userBool);\r\n\t\t\t\t\t\tthat.$set(that, 'pinkBool', res.data.pinkBool);\r\n\t\t\t\t\t\tthat.$set(that, 'isOk', res.data.isOk);\r\n\t\t\t\t\t\tthat.$set(that, 'currentPinkOrder', res.data.currentPinkOrder);\r\n\t\t\t\t\t\tthat.$set(that, 'userInfo', res.data.userInfo);\r\n\t\t\t\t\t\tthat.onceNum = storeCombination.onceNum;\r\n\t\t\t\t\t\tthat.attr.productAttr = storeCombination.productAttr;\r\n\t\t\t\t\t\tthat.productValue = storeCombination.productValue;\r\n\t\t\t\t\t\t//#ifdef H5\r\n\t\t\t\t\t\tthis.getImageBase64(storeCombination.image);\r\n\t\t\t\t\t\tthat.make();\r\n\r\n\t\t\t\t\t\tthat.setOpenShare();\r\n\t\t\t\t\t\t//#endif\r\n\t\t\t\t\t\tthat.setProductSelect();\r\n\t\t\t\t\t\tif (that.attr.productAttr != 0) that.DefaultSelect();\r\n\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\tif (that.isLogin) {\r\n\t\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\turl: '/pages/index/index'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t//#ifdef H5\r\n\t\t\tsetOpenShare() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet configTimeline = {\r\n\t\t\t\t\ttitle: '您的好友' + that.userInfo.nickname + '邀请您参团' + that.storeCombination.title,\r\n\t\t\t\t\tdesc: that.storeCombination.title,\r\n\t\t\t\t\tlink: window.location.protocol + '//' + window.location.host +\r\n\t\t\t\t\t\t'/pages/activity/goods_combination_status/index?id=' + that.pinkId + \"&spread=\" + this.uid,\r\n\t\t\t\t\timgUrl: that.storeCombination.image\r\n\t\t\t\t};\r\n\t\t\t\tif (this.$wechat.isWeixin()) {\r\n\t\t\t\t\tthis.$wechat\r\n\t\t\t\t\t\t.wechatEvevt(['updateAppMessageShareData', 'updateTimelineShareData', 'onMenuShareAppMessage',\r\n\t\t\t\t\t\t\t'onMenuShareTimeline'\r\n\t\t\t\t\t\t], configTimeline)\r\n\t\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.catch(res => {\r\n\t\t\t\t\t\t\tif (res.is_ready) {\r\n\t\t\t\t\t\t\t\tres.wx.updateAppMessageShareData(configTimeline);\r\n\t\t\t\t\t\t\t\tres.wx.updateTimelineShareData(configTimeline);\r\n\t\t\t\t\t\t\t\tres.wx.onMenuShareAppMessage(configTimeline);\r\n\t\t\t\t\t\t\t\tres.wx.onMenuShareTimeline(configTimeline);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t//#endif\r\n\t\t\t//拼团取消\r\n\t\t\tgetCombinationRemove: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tpostCombinationRemove({\r\n\t\t\t\t\t\tid: that.pinkId,\r\n\t\t\t\t\t\tcid: that.storeCombination.id\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: res.msg\r\n\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\ttab: 3\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(res => {\r\n\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: res\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tlookAll: function() {\r\n\t\t\t\tthis.iShidden = !this.iShidden;\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n\t.pinkT {\r\n\t\tposition: relative;\r\n\r\n\t\t.chief {\r\n\t\t\tposition: absolute;\r\n\t\t\twidth: 72rpx;\r\n\t\t\theight: 30rpx;\r\n\t\t\tbackground-color: #c9ab79;\r\n\t\t\tborder-radius: 15rpx;\r\n\t\t\tfont-size: 20rpx;\r\n\t\t\tline-height: 30rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tright: -24rpx;\r\n\t\t\ttop: -16rpx;\r\n\t\t\tcolor: #fff;\r\n\t\t}\r\n\t}\r\n\r\n\t.canvas {\r\n\t\tposition: fixed;\r\n\t\topacity: 0;\r\n\t}\r\n\t.poster-pop {\r\n\t\twidth: 594rpx;\r\n\t\theight: 850rpx;\r\n\t\tposition: fixed;\r\n\t\tleft: 50%;\r\n\t\ttransform: translateX(-50%);\r\n\t\tz-index: 999;\r\n\t\ttop: 50%;\r\n\t\tmargin-top: -466rpx;\r\n\t\r\n\t\timage {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t\tdisplay: block;\r\n\t\t\tborder-radius: 10rpx;\r\n\t\t}\r\n\t\r\n\t\t.close {\r\n\t\t\ttext-align: center;\r\n\t\t\tmargin-top: 55rpx;\r\n\t\t\tcolor: #fff;\r\n\t\t\tfont-size: 52rpx;\r\n\t\t}\r\n\t\r\n\t\t.save-poster {\r\n\t\t\tbackground-color: #df2d0a;\r\n\t\t\tfont-size: ：22rpx;\r\n\t\t\tcolor: #fff;\r\n\t\t\ttext-align: center;\r\n\t\t\theight: 76rpx;\r\n\t\t\tline-height: 76rpx;\r\n\t\t\twidth: 100%;\r\n\t\t}\r\n\t\r\n\t\t.keep {\r\n\t\t\tcolor: #fff;\r\n\t\t\ttext-align: center;\r\n\t\t\tfont-size: 25rpx;\r\n\t\t\tmargin-top: 25rpx;\r\n\t\t}\r\n\t}\r\n\t\r\n\t/*开团*/\r\n\t.group-con .header {\r\n\t\twidth: 100%;\r\n\t\theight: 186rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-top: 1px solid #f5f5f5;\r\n\t\tpadding: 0 30rpx;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.group-con .header .iconfont {\r\n\t\tfont-size: 100rpx;\r\n\t\tposition: absolute;\r\n\t\tcolor: #ccc;\r\n\t\tright: 33rpx;\r\n\t\tbottom: 20rpx;\r\n\t}\r\n\r\n\t.group-con .header .pictrue {\r\n\t\twidth: 140rpx;\r\n\t\theight: 140rpx;\r\n\t}\r\n\r\n\t.group-con .header .pictrue img {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 6rpx;\r\n\t}\r\n\r\n\t.group-con .header .text {\r\n\t\twidth: 540rpx;\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #222;\r\n\t}\r\n\r\n\t.group-con .header .text .money {\r\n\t\tfont-size: 24rpx;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-top: 15rpx;\r\n\t}\r\n\r\n\t.group-con .header .text .money .num {\r\n\t\tfont-size: 32rpx;\r\n\t}\r\n\r\n\t.group-con .header .text .money .team {\r\n\t\tpadding: 1rpx 10rpx;\r\n\t\tfont-weight: normal;\r\n\t\tborder-radius: 50rpx;\r\n\t\tfont-size: 20rpx;\r\n\t\tvertical-align: 4rpx;\r\n\t\tmargin-left: 15rpx;\r\n\t}\r\n\r\n\t.group-con .wrapper {\r\n\t\tbackground-color: #fff;\r\n\t\tmargin-top: 20rpx;\r\n\t\tpadding: 2rpx 0 35rpx 0;\r\n\t}\r\n\r\n\t.group-con .wrapper .title {\r\n\t\tmargin-top: 30rpx;\r\n\t}\r\n\r\n\t.group-con .wrapper .title .line {\r\n\t\twidth: 136rpx;\r\n\t\theight: 1px;\r\n\t\tbackground-color: #ddd;\r\n\t}\r\n\r\n\t.group-con .wrapper .title .name {\r\n\t\tmargin: 0 45rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #282828;\r\n\t}\r\n\r\n\t.group-con .wrapper .title .name .time {\r\n\t\tmargin: 0 14rpx;\r\n\t}\r\n\r\n\t.group-con .wrapper .title .name .timeTxt {\r\n\t\tcolor: #fc4141;\r\n\t}\r\n\r\n\t.group-con .wrapper .title .name .time .styleAll {\r\n\t\tbackground-color: #ffcfcb;\r\n\t\ttext-align: center;\r\n\t\tborder-radius: 3rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t\tdisplay: inline-block;\r\n\t\tvertical-align: middle;\r\n\t\tcolor: #fc4141;\r\n\t\tpadding: 2rpx 5rpx;\r\n\t}\r\n\r\n\t.group-con .wrapper .tips {\r\n\t\tfont-size: 30rpx;\r\n\t\tfont-weight: bold;\r\n\t\ttext-align: center;\r\n\t\tmargin-top: 30rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.group-con .wrapper .list {\r\n\t\tpadding: 0 30rpx;\r\n\t\tmargin-top: 45rpx;\r\n\t}\r\n\r\n\t.group-con .wrapper .list.result {\r\n\t\tmax-height: 240rpx;\r\n\t}\r\n\r\n\t.group-con .wrapper .list.result.on {\r\n\t\tmax-height: 2000rpx;\r\n\t}\r\n\r\n\t.group-con .wrapper .list .pictrue {\r\n\t\twidth: 94rpx;\r\n\t\theight: 94rpx;\r\n\t\tmargin: 0 0 29rpx 35rpx;\r\n\t}\r\n\r\n\t.group-con .wrapper .list .pictrue img {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 50%;\r\n\t\tborder: 2rpx solid #c9ab79;\r\n\t}\r\n\r\n\t.group-con .wrapper .list .pictrue img.img-none {\r\n\t\tborder: none;\r\n\t}\r\n\r\n\t.group-con .wrapper .lookAll {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #282828;\r\n\t\tpadding-top: 10rpx;\r\n\t}\r\n\r\n\t.group-con .wrapper .lookAll .iconfont {\r\n\t\tfont-size: 25rpx;\r\n\t\tmargin: 2rpx 0 0 10rpx;\r\n\t}\r\n\r\n\t.group-con .wrapper .teamBnt {\r\n\t\tfont-size: 30rpx;\r\n\t\twidth: 620rpx;\r\n\t\theight: 86rpx;\r\n\t\tborder-radius: 50rpx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 86rpx;\r\n\t\tcolor: #fff;\r\n\t\tmargin: 21rpx auto 0 auto;\r\n\t}\r\n\r\n\t.group-con .wrapper .cancel,\r\n\t.group-con .wrapper .lookOrder {\r\n\t\ttext-align: center;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #282828;\r\n\t\tpadding-top: 30rpx;\r\n\t}\r\n\r\n\t.group-con .wrapper .cancel .iconfont {\r\n\t\tfont-size: 35rpx;\r\n\t\tcolor: #2c2c2c;\r\n\t\tvertical-align: -4rpx;\r\n\t\tmargin-right: 9rpx;\r\n\t}\r\n\r\n\t.group-con .wrapper .lookOrder .iconfont {\r\n\t\tfont-size: 25rpx;\r\n\t\tcolor: #2c2c2c;\r\n\t\tmargin-left: 10rpx;\r\n\t}\r\n\r\n\t.group-con .group-recommend {\r\n\t\tbackground-color: #fff;\r\n\t\tmargin-top: 25rpx;\r\n\t}\r\n\r\n\t.group-con .group-recommend .title {\r\n\t\tpadding-right: 30rpx;\r\n\t\tmargin-left: 30rpx;\r\n\t\theight: 85rpx;\r\n\t\tborder-bottom: 1px solid #eee;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #282828;\r\n\t}\r\n\r\n\t.group-con .group-recommend .title .more {\r\n\t\tcolor: #808080;\r\n\t}\r\n\r\n\t.group-con .group-recommend .title .more .iconfont {\r\n\t\tmargin-left: 13rpx;\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n\r\n\t.group-con .group-recommend .list {\r\n\t\tmargin-top: 30rpx;\r\n\t}\r\n\r\n\t.group-con .group-recommend .list .item {\r\n\t\twidth: 210rpx;\r\n\t\tmargin: 0 0 25rpx 30rpx;\r\n\t}\r\n\r\n\t.group-con .group-recommend .list .item .pictrue {\r\n\t\twidth: 100%;\r\n\t\theight: 210rpx;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.group-con .group-recommend .list .item .pictrue img {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 10rpx;\r\n\t}\r\n\r\n\t.group-con .group-recommend .list .item .pictrue .team {\r\n\t\tposition: absolute;\r\n\t\ttop: 28rpx;\r\n\t\tleft: -5rpx;\r\n\t\tmin-width: 100rpx;\r\n\t\theight: 36rpx;\r\n\t\tline-height: 36rpx;\r\n\t\ttext-align: center;\r\n\t\tborder-radius: 0 18rpx 18rpx 0;\r\n\t\tfont-size: 20rpx;\r\n\t\tcolor: #fff;\r\n\t\tbackground-image: linear-gradient(to right, #fb5445 0%, #c9ab79 100%);\r\n\t\tbackground-image: -webkit-linear-gradient(to right, #fb5445 0%, #c9ab79 100%);\r\n\t\tbackground-image: -moz-linear-gradient(to right, #fb5445 0%, #c9ab79 100%);\r\n\t}\r\n\r\n\t.group-con .group-recommend .list .item .name {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #333;\r\n\t\tmargin-top: 0.18rem;\r\n\t}\r\n\r\n\t.group-con .group-recommend .list .item .money {\r\n\t\tfont-weight: bold;\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n\r\n\t.share-box {\r\n\t\tz-index: 1000;\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\ttop: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\r\n\t\timage {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=35f14445&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=35f14445&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363903349\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}