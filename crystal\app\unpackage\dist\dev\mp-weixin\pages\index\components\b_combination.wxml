<view class="{{['data-v-a1cf24e2',(isBorader)?'borderShow':'']}}"><block wx:if="{{$root.g0}}"><view class="combination data-v-a1cf24e2"><view class="title acea-row row-between data-v-a1cf24e2"><view class="spike-bd data-v-a1cf24e2"><block wx:if="{{$root.g1>0}}"><view class="activity_pic data-v-a1cf24e2"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="picture data-v-a1cf24e2" style="{{(index===2?'position: relative':'position: static')}}"><label class="avatar _span data-v-a1cf24e2" style="{{('background-image: url('+item.$orig+')')}}"></label><block wx:if="{{item.m0}}"><label class="mengceng _span data-v-a1cf24e2"><view class="_i data-v-a1cf24e2">···</view></label></block></view></block><text class="pic_count data-v-a1cf24e2">{{assistUserCount+"人参与"}}</text></view></block></view><navigator class="more acea-row row-center-wrapper data-v-a1cf24e2" url="/pages/activity/goods_combination/index" hover-class="none">GO<text class="iconfont icon-xiangyou data-v-a1cf24e2"></text></navigator></view><view class="conter acea-row data-v-a1cf24e2"><scroll-view style="white-space:nowrap;vertical-align:middle;" scroll-x="true" show-scrollbar="false" class="data-v-a1cf24e2"><block wx:for="{{combinationList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['goDetail',['$0'],[[['combinationList','',index]]]]]]]}}" class="itemCon data-v-a1cf24e2" bindtap="__e"><view class="item data-v-a1cf24e2"><view class="pictrue data-v-a1cf24e2"><image src="{{item.image}}" class="data-v-a1cf24e2"></image></view><view class="text lines1 data-v-a1cf24e2"><view class="name line1 data-v-a1cf24e2">{{item.title}}</view><view class="money data-v-a1cf24e2">¥<text class="num data-v-a1cf24e2">{{item.price}}</text></view><view class="y_money data-v-a1cf24e2">{{"¥"+item.otPrice}}</view></view></view></view></block></scroll-view></view></view></block></view>