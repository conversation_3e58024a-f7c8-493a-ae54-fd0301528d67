(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/login_mobile/index"],{"46b1":function(t,e,n){},8819:function(t,e,n){"use strict";n.r(e);var i=n("eda8"),o=n("c385");for(var c in o)["default"].indexOf(c)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(c);n("993f");var u=n("828b"),a=Object(u["a"])(o["default"],i["b"],i["c"],!1,null,"fd9fc3a4",null,!1,i["a"],void 0);e["default"]=a.exports},"993f":function(t,e,n){"use strict";var i=n("46b1"),o=n.n(i);o.a},c385:function(t,e,n){"use strict";n.r(e);var i=n("e13d"),o=n.n(i);for(var c in i)["default"].indexOf(c)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(c);e["default"]=o.a},e13d:function(t,e,n){"use strict";(function(t){var i=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=i(n("7eb4")),c=i(n("ee10")),u=i(n("74cc")),a=(i(n("5902")),n("8f59")),s=n("5904"),r=(n("fdf2"),n("292f")),f=getApp(),d={name:"login_mobile",computed:(0,a.mapGetters)(["userInfo","isLogin"]),props:{isUp:{type:Boolean,default:!1},authKey:{type:String,default:""},isShow:{type:Boolean,default:!0},isPos:{type:Boolean,default:!0},appleShow:{type:String,default:""},platform:{type:String,default:""}},data:function(){return{keyCode:"",account:"",codeNum:"",isApp:0}},mixins:[u.default],mounted:function(){},onLoad:function(){},methods:{code:function(){var t=this;return(0,c.default)(o.default.mark((function e(){var n;return o.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=t,n.account){e.next=3;break}return e.abrupt("return",n.$util.Tips({title:"请填写手机号码"}));case 3:if(/^1(3|4|5|7|8|9|6)\d{9}$/i.test(n.account)){e.next=5;break}return e.abrupt("return",n.$util.Tips({title:"请输入正确的手机号码"}));case 5:return e.next=7,(0,s.registerVerify)(n.account).then((function(t){n.$util.Tips({title:t.msg}),n.sendCode()})).catch((function(t){return n.$util.Tips({title:t})}));case 7:case"end":return e.stop()}}),e)})))()},getCode:function(){var t=this;(0,s.getCodeApi)().then((function(e){t.keyCode=e.data.key})).catch((function(e){t.$util.Tips({title:e})}))},close:function(){this.$emit("close",!1)},loginBtn:function(){var e=this;return e.account?/^1(3|4|5|7|8|9|6)\d{9}$/i.test(e.account)?e.codeNum?/^[\w\d]+$/i.test(e.codeNum)?(t.showLoading({title:!this.userInfo.phone&&this.isLogin?"正在绑定中":"正在登录中"}),void(!this.userInfo.phone&&this.isLogin?(0,r.iosBinding)({captcha:e.codeNum,phone:e.account}).then((function(t){e.$util.Tips({title:"绑定手机号成功",icon:"success"},{tab:3}),e.isApp=1,e.getUserInfo()})).catch((function(n){t.hideLoading(),e.$util.Tips({title:n})})):(0,r.getUserPhone)({captcha:e.codeNum,phone:e.account,key:e.authKey}).then((function(t){e.$store.commit("LOGIN",{token:t.data.token}),e.$store.commit("SETUID",t.data.uid),e.getUserInfo()})).catch((function(n){t.hideLoading(),e.$util.Tips({title:n})})))):e.$util.Tips({title:"请输入正确的验证码"}):e.$util.Tips({title:"请填写验证码"}):e.$util.Tips({title:"请输入正确的手机号码"}):e.$util.Tips({title:"请填写手机号码"})},phoneSilenceAuth:function(t){var e=this,n=this;(0,s.phoneSilenceAuth)({code:t,spid:f.globalData.spid,spread:f.globalData.code,phone:this.account,captcha:this.codeNum}).then((function(t){e.$store.commit("LOGIN",t.data.token),e.$store.commit("SETUID",t.data.uid),e.getUserInfo()})).catch((function(t){n.$util.Tips({title:t})}))},getUserInfo:function(){var e=this;(0,s.getUserInfo)().then((function(n){t.hideLoading(),e.$store.commit("UPDATE_USERINFO",n.data),e.$util.Tips({title:"登录成功",icon:"success"},{tab:3}),e.close()}))}}};e.default=d}).call(this,n("df3c")["default"])},eda8:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){}));var i=function(){var t=this.$createElement;this._self._c},o=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/login_mobile/index-create-component',
    {
        'components/login_mobile/index-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("8819"))
        })
    },
    [['components/login_mobile/index-create-component']]
]);
