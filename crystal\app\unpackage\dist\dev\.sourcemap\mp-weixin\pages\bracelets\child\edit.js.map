{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/bracelets/child/edit.vue?c877", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/bracelets/child/edit.vue?088a", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/bracelets/child/edit.vue?c22d", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/bracelets/child/edit.vue?eb19", "uni-app:///pages/bracelets/child/edit.vue", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/bracelets/child/edit.vue?1860", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/bracelets/child/edit.vue?3f4f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "indexOneProduct", "indexOneProductIndex", "indexTwoProduct", "indexTwoProductIndex", "scrollIndex", "dropdownLeft", "specLeft", "scrollIntoViewId", "showDropdown", "isDraggingMarble", "draggingMarbleIndex", "isDragging", "dragProduct", "dragPosition", "x", "y", "circleRect", "isInCircleArea", "id", "createUserId", "hand", "changeFlag", "handVisible", "radius", "realRadius", "productList", "selectList", "selectListChange", "price", "computed", "onLoad", "setTimeout", "app", "onShow", "onShareAppMessage", "title", "path", "methods", "handleOneTabClick", "handleTwoTabClick", "backOne", "reset", "<PERSON><PERSON><PERSON>", "productAttrValueIds", "fromSubmit", "userEditHand", "that", "icon", "tab", "url", "_previewImage", "imgArr", "uni", "urls", "current", "dualPrice", "addProduct", "e", "list", "delProduct", "getMarbleStyleSmall", "position", "left", "top", "width", "height", "transform", "background", "backgroundSize", "getMarbleStyle", "sum", "angleOffset", "transition", "generateMarbles", "getAllCategory", "getInfo", "userBraceletsSubmit", "content", "success", "userId", "productIds", "showCancel", "console", "<PERSON><PERSON><PERSON><PERSON>", "onLongPress", "query", "onTouchMove", "onChange", "centerX", "centerY", "Math", "onChange2", "onTouchEnd", "touch", "insertIndex", "calculateAngle", "calculateInsertIndex", "onMarbleLongPress", "onMarbleTouchMove", "onMarbleTouchEnd", "targetIndex", "calculateTotalArc", "calculateInsertIndexFromAngle", "deepCopy", "toggleDropdown", "category", "scrollToProduct", "toggleSpecifications", "selectSpecification", "result"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACwD;AACL;AACsC;;;AAGzF;AACmM;AACnM,gBAAgB,2LAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wUAEN;AACP,KAAK;AACL;AACA,aAAa,+TAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3FA;AAAA;AAAA;AAAA;AAAiwB,CAAgB,orBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACkTrxB;AACA;AAGA;AAGA;AACA;AAAA;AAAA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;EACAC;IACA;;IAEA;IACAC;MACA;QACAC;QACA;MACA;IACA;IAEA;IACA;IACA;EACA;EACAC;IACA;EACA;EACA;AACA;AACA;;EAEAC;IACA;IACA;MACAC;MACA;MACAC;IACA;EACA;EAEAC;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;QAAA;MAAA;MACA;QAAA;MAAA;MACA,wCACA;QACA;MACA,gBACAC,qBACA;IACA;IACAC;MAAA;MACA;MACA;QACA;UACAT;QACA;MACA;MACAU;QAAAzB;MAAA;QAEA0B;UACAX;UACAY;QACA;UACAC;UACAC;QACA;QACAH;QACA;MACA;QACA;UACAX;QACA;UACAa;UACAC;QACA;MACA;IACA;IACAC;MACA;MACAC;MACA;MACAC;QACAC;QACAC;MACA;IAEA;IACAC;MACA;QAAA;MAAA;IACA;IACAC;MAEA;MACA;QACA;UAAA;QAAA;UAAA;QAAA;UACA;YACArB;UACA;QACA;MACA;QACA;UAAA;QAAA;UAAA;QAAA;UACA;YACAA;UACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAsB;MACA;MACA;QAAAC;MAAA;MACA;MACA;IACA;IACAC;MACA;MACA;QAAAD;MAAA;MACA;MACA;IACA;IACAE;MACA;MACA;MACA;MACA;;MAEA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;IACAC;MACA;MACA;QAAA,OACAC;MAAA;;MAEA;MACA;MACA;;MAEA;MACA;;MAEA;MACA;MACA;QACA;QACAC;MACA;MAEA;MACA;;MAEA;MACA;MACA;MAEA;QACAV;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAI;MACA;IACA;IAEAC;MACA;QAAA;MAAA;QAAA;MAAA;MACA;MACA;QACA;QACA;MACA;MAEA;QAAA,OACAH;MAAA;MAEA;MACA;;MAEA;MACA;MACA;IACA;IACAI;MAAA;MACA;QACA;QACA;UACA;QACA;QACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MACA;QAAAzD;MAAA;QACA;QACA;QACA;QACA;MACA;IACA;IACA0D;MACA;QAAA;MAAA;MACA;QAAA;MAAA;MACA;MACA;MACA;MACAxB;QACAjB;QACA0C;QACAC;UACA;YACA1B;cACAjB;YACA;YACA;cACAjB;cACA6D;cACA5D;cACAwB;cACAqC;YACA;cAEA5B;cACAA;gBACAjB;gBACA0C;gBACAI;gBACAH;kBACA;oBACAI;kBACA;oBACAA;kBACA;gBACA;cACA;YACA;cACA9B;cACAA;gBACAjB;gBACA0C;gBACAI;gBACAH;kBACA;oBACAI;kBACA;oBACAA;kBACA;gBACA;cACA;YACA;UACA;YACAA;UACA;QACA;MACA;IACA;IAEAC;MAEA;IACA;IAGAC;MAAA;MACA;;MAEA;MACA;MACA;MACA;QACAtE;QACAC;MACA;;MAEA;MACA;MACAsE;QACA;MACA;IACA;IAEAC;MACA;MAEA;MACA;QACAxE;QACAC;MACA;IACA;IAEAwE;MACA;MAEA;QAAAzE;QAAAC;MACA;QACAyE;QACAC;QACAlE;MACA;MAEA,yBACAmE,mEACAA,kEACA;;MAEA;MACA;IACA;IACAC;MACA;MAEA;QACA;UAAA7E;UAAAC;QACA;QACA;QACA,yBACA2E,4DACAA,2DACA;QACA;MACA;IACA;IAEAE;MACA;MAEA;QACA;UAAA;QAAA;UAAA;QAAA;UAEA;UACA;UACA;UACA;YACAzD;UACA;QACA;MACA;QACA;UAAA;QAAA;UAAA;QAAA;UAEA;UACA;UACA;UACA;YACAA;UACA;QACA;MACA;MACA;QACA;QACA;QACA;QACA;UACA;UACA;UACA,yBACAuD,+BACAA,6BACA;;UAEA;UACA;YACA;YACA;YACA;YACA;YACA;cAAAhC;YAAA;YACA;YACA;UACA;QACA;;QAEA;QACA;QACA;MACA;QAGA;QACA;UACA5C;UACAC;QACA;;QAEA;QACA,2BACA8E,2BACAA,0BACA;;QAEA;QACA;QACA;QACA;;QAEA;QACA,8BACA,uDACA;QACAC;;QAEA;QACA;QACA;UAAApC;QAAA;QACA;QACA;QAGA;QACA;MACA;IAEA;IACAqC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;IACA;IAEAC;MAAA;MACA;MACA;MACA;;MAEA;MACA;MACAZ;QACA;MACA;IACA;IAEAa;MACA;MAEA;MACA;QACApF;QACAC;MACA;IACA;IAEA;IACAoF;MACA;MACA;QAEA;QACA;QACA;;QAEA;QACA;UACA;UACA;UACA,yBACAT,+BACAA,6BACA;UAEA;YACA;YACA;YACA;;YAEA;YACA;cACA;cACA;cACA;cACA;gBAAAhC;cAAA;YACA;UACA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;MAEA;QAEA;QACA;UACA5C;UACAC;QACA;;QAEA;QACA,2BACA8E,4BACAA,2BACA;;QAEA;QACA;QACA;QACA;;QAEA;QACA,6BACA,uDACA;QACAO;QAEA;UACA;UACA;UACA;YAAA1C;UAAA;QACA;QAEA;QACA;QACA;QACA;MACA;IACA;IAEA2C;MACA;QAAA,OACA/B;MAAA;MACA;MACA;IACA;IAEAgC;MACA;MACA;MACA;MAEA;IACA;IACAC;MACA;QAAA;MAAA;IACA;IACAC;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;UACAC;YAEA;cACAhD;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IACAiD;MAAA;MACA;MACA;MACA;QACA;UACAD;YAEA;cACAhD;gBACA;cACA;YACA;UACA;QACA;MACA;MACA;IACA;IAGAkD;MAAA;MACAzB;MACA;MACA;QACA;UACAuB;YAEA;cACAhD;gBACA;kBACA;gBACA;cACA;YACA;UACA;QACA;MACA;MAEA;MACA;MACA;MACA;MACA;MACA;IACA;IAEAmD;MACA1B;MACA;MACA;MACA2B;MACAA;MACAA;MACAA;;MAEA;MACA;;MAEA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1+BA;AAAA;AAAA;AAAA;AAAo8C,CAAgB,mvCAAG,EAAC,C;;;;;;;;;;;ACAx9C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/bracelets/child/edit.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/bracelets/child/edit.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./edit.vue?vue&type=template&id=098d4cba&scoped=true&\"\nvar renderjs\nimport script from \"./edit.vue?vue&type=script&lang=js&\"\nexport * from \"./edit.vue?vue&type=script&lang=js&\"\nimport style0 from \"./edit.vue?vue&type=style&index=0&id=098d4cba&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"098d4cba\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/bracelets/child/edit.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=template&id=098d4cba&scoped=true&\"", "var components\ntry {\n  components = {\n    shmilyDragImage: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/shmily-drag-image/components/shmily-drag-image/shmily-drag-image\" */ \"@/uni_modules/shmily-drag-image/components/shmily-drag-image/shmily-drag-image.vue\"\n      )\n    },\n    ljsDialog: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/ljs-dialog/components/ljs-dialog/ljs-dialog\" */ \"@/uni_modules/ljs-dialog/components/ljs-dialog/ljs-dialog.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.productList && _vm.productList.length > 0\n  var g1 = _vm.productList && _vm.productList.length > 1\n  var g2 = !!_vm.isLogin ? _vm.selectList.length : null\n  var g3 = !!_vm.isLogin\n    ? _vm.selectList.length > 5 && _vm.uid == _vm.createUserId\n    : null\n  var g4 = !!_vm.isLogin\n    ? _vm.selectList.length > 5 && _vm.uid != _vm.createUserId\n    : null\n  var l0 = _vm.__map(_vm.selectList, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var s0 = _vm.__get_style([\n      _vm.selectList.length < 6\n        ? _vm.getMarbleStyleSmall(index)\n        : _vm.getMarbleStyle(index),\n    ])\n    return {\n      $orig: $orig,\n      s0: s0,\n    }\n  })\n  var g5 = _vm.selectList.length\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.handVisible = true\n    }\n    _vm.e1 = function ($event) {\n      _vm.handVisible = true\n    }\n    _vm.e2 = function ($event) {\n      _vm.handVisible = true\n    }\n    _vm.e3 = function ($event) {\n      _vm.handVisible = true\n    }\n    _vm.e4 = function ($event) {\n      _vm.handVisible = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n        l0: l0,\n        g5: g5,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class='productSort'>\r\n\t\t<!-- 拖拽区域 -->\r\n\t\t<movable-area\r\n\t\t\tstyle=\"width: 100vw; height: 100vh; position: fixed; top: 0; left: 0; z-index: 999; pointer-events: none;\">\r\n\t\t\t<movable-view v-if=\"isDragging\" :x=\"dragPosition.x\" :y=\"dragPosition.y\" direction=\"all\" :animation=\"false\"\r\n\t\t\t\t:out-of-bounds=\"true\" @change=\"onChange\" @touchend=\"onTouchEnd\" style=\"pointer-events: auto;\">\r\n\t\t\t\t<image :src=\"dragProduct.image\"\r\n\t\t\t\t\t:style=\"{ width: dragProduct.width * 3 + 'rpx', height: dragProduct.height * 3 + 'rpx' }\"\r\n\t\t\t\t\tmode=\"aspectFit\" />\r\n\t\t\t</movable-view>\r\n\t\t</movable-area>\r\n\t\t<!-- 环形珠子拖拽区域 -->\r\n\t\t<movable-area\r\n\t\t\tstyle=\"width: 100vw; height: 100vh; position: fixed; top: 0; left: 0; z-index: 999; pointer-events: none;\">\r\n\t\t\t<movable-view v-if=\"isDraggingMarble\" :x=\"dragPosition.x\" :y=\"dragPosition.y\" direction=\"all\"\r\n\t\t\t\t:animation=\"false\" :out-of-bounds=\"true\" @change=\"onChange2\" @touchend=\"onMarbleTouchEnd\"\r\n\t\t\t\tstyle=\"pointer-events: auto;\">\r\n\t\t\t\t<image :src=\"dragProduct.image\"\r\n\t\t\t\t\t:style=\"{ width: dragProduct.width * 3 + 'rpx', height: dragProduct.height * 3 + 'rpx' }\"\r\n\t\t\t\t\tmode=\"aspectFit\" />\r\n\t\t\t</movable-view>\r\n\t\t</movable-area>\r\n\t\t<view class=\"top\">\r\n\t\t\t<view class=\"left-bbb\">\r\n\t\t\t\t<!-- 下拉菜单 -->\r\n\t\t\t\t<!-- <view class=\"dropdown-menu\" v-if=\"showDropdown\" :style=\"{ left: (dropdownLeft + 'rpx') }\">\r\n\t\t\t\t\t<view class=\"dropdown-item\" v-for=\"(category, idx) in productList\" :key=\"idx\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t{{ category.name }}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view> -->\r\n\t\t\t\t<view class=\"specifications-dropdown \" :style=\"{ left: (dropdownLeft + 'rpx') }\" v-if=\"showDropdown\">\r\n\t\t\t\t\t<!-- <view v-for=\"(category, idx) in productList\" :key=\"idx\"> -->\r\n\t\t\t\t\t<view>\r\n\t\t\t\t\t\t<view class=\"specs-header\" style=\"justify-content: center;\">\r\n\t\t\t\t\t\t\t<text class=\"specs-title\">{{ productList[scrollIndex].name }}</text>\r\n\t\t\t\t\t\t\t<!-- <text class=\"close-btn\" @click.stop=\"toggleSpecifications(product, $event)\">×</text> -->\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"specs-list\">\r\n\t\t\t\t\t\t\t<view class=\"specs-item\" v-for=\"(spec, specIndex) in productList[scrollIndex].child\"\r\n\t\t\t\t\t\t\t\t:key=\"specIndex\" @click=\"scrollToProduct(scrollIndex, specIndex)\">\r\n\t\t\t\t\t\t\t\t<view class=\"specs-info\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"specs-size\">{{ spec.name }}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"left-bottom\">\r\n\t\t\t\t\t点击宝石或长按拖动宝石，进行手串DIY定制\r\n\t\t\t\t</view>\r\n\t\t\t\t<scroll-view scroll-x=\"true\" v-if=\"productList && productList.length > 0\" :key=\"index\"\r\n\t\t\t\t\tstyle=\"white-space: nowrap;border-top-left-radius: 16rpx;border-top-right-radius: 16rpx;\"\r\n\t\t\t\t\t:scroll-into-view=\"scrollIntoViewId\">\r\n\t\t\t\t\t<view class=\"left-child\">\r\n\t\t\t\t\t\t<!-- 下拉列表 -->\r\n\t\t\t\t\t\t<view style=\"text-align: center;display: flex;align-items: center;\">\r\n\t\t\t\t\t\t\t<view class=\"bigtitle\" style=\"border-top-left-radius: 16rpx;\" :id=\"'producttitle-' + index\">\r\n\t\t\t\t\t\t\t\t{{\r\n\t\t\t\t\t\t\t\t\tproductList[0].name\r\n\t\t\t\t\t\t\t\t}}</view>\r\n\t\t\t\t\t\t\t<view style=\"text-align: center;display: flex;align-items: center;flex: 1;\"\r\n\t\t\t\t\t\t\t\tv-for=\"(child, index2) in productList[0].child\" :key=\"index2\">\r\n\t\t\t\t\t\t\t\t<view class=\"smalltitle\" :class=\"{ 'active': indexOneProductIndex === index2 }\"\r\n\t\t\t\t\t\t\t\t\t:id=\"'producttitle-' + index + index2\" @click=\"handleOneTabClick(index2)\">{{\r\n\t\t\t\t\t\t\t\t\t\tchild.name\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<!-- <view class=\"product-item\" @click=\"toggleSpecifications(product, $event)\" -->\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</scroll-view>\r\n\t\t\t\t<scroll-view scroll-x=\"true\" style=\"white-space: nowrap; width: 710rpx;\">\r\n\t\t\t\t\t<view class=\"left\" style=\"display: flex; min-width: max-content;\">\r\n\t\t\t\t\t\t<!-- 下拉列表 -->\r\n\t\t\t\t\t\t<view class=\"product-item\" v-for=\"(product, index1) in indexOneProduct\" :key=\"index1\"\r\n\t\t\t\t\t\t\t:id=\"'product-' + index\" @click=\"addProduct(product)\"\r\n\t\t\t\t\t\t\tstyle=\"margin: 10rpx;display: flex;padding: 10rpx;position: relative;align-items: center;flex-shrink: 0;\">\r\n\r\n\t\t\t\t\t\t\t<image @longpress=\"onLongPress($event, product)\"\r\n\t\t\t\t\t\t\t\************************=\"onTouchMove($event)\"\r\n\t\t\t\t\t\t\t\***********************=\"onTouchEnd($event)\" :style=\"'height: 50rpx;'\" mode=\"heightFix\"\r\n\t\t\t\t\t\t\t\t:src=\"product.image\" />\r\n\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\tstyle=\"padding: 0 10rpx;display: flex;flex-direction: column;align-items: center;justify-content:space-around;\">\r\n\t\t\t\t\t\t\t\t<view style=\"width: 100%;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;\">\r\n\t\t\t\t\t\t\t\t\t{{ product.storeName }}\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view style=\"color: #c9ab79;font-weight: 600;font-size: 20rpx;\">\r\n\t\t\t\t\t\t\t\t\t{{ product.width }}x{{ product.height }}mm\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t<!-- 规格选择下拉框 -->\r\n\t\t\t\t\t\t\t<view class=\"specifications-dropdown\" :style=\"{ left: (specLeft + 'rpx') }\"\r\n\t\t\t\t\t\t\t\tv-if=\"product.showSpecs\">\r\n\t\t\t\t\t\t\t\t<view class=\"specs-header\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"specs-title\">规格选择</text>\r\n\t\t\t\t\t\t\t\t\t<text class=\"close-btn\" @click.stop=\"toggleSpecifications(product, $event)\">×</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"specs-list\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"specs-item\" v-for=\"(spec, specIndex) in product.productValue\"\r\n\t\t\t\t\t\t\t\t\t\t:key=\"specIndex\" @click.stop=\"selectSpecification(product, spec)\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"specs-info\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"specs-size\">{{ spec.width }}×{{ spec.height }}mm</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</scroll-view>\r\n\t\t\t\t<scroll-view scroll-x=\"true\" v-if=\"productList && productList.length > 1\" :key=\"index\"\r\n\t\t\t\t\tstyle=\"white-space: nowrap;\" :scroll-into-view=\"scrollIntoViewId\">\r\n\t\t\t\t\t<view class=\"left-child\">\r\n\t\t\t\t\t\t<!-- 下拉列表 -->\r\n\t\t\t\t\t\t<view style=\"text-align: center;display: flex;align-items: center;width: 710rpx\t;\">\r\n\t\t\t\t\t\t\t<view class=\"bigtitle\" :id=\"'producttitle-' + index\">\r\n\t\t\t\t\t\t\t\t{{\r\n\t\t\t\t\t\t\t\t\tproductList[1].name\r\n\t\t\t\t\t\t\t\t}}</view>\r\n\t\t\t\t\t\t\t<view style=\"text-align: center;display: flex;align-items: center;flex: 1;\"\r\n\t\t\t\t\t\t\t\tv-for=\"(child, index2) in productList[1].child\" :key=\"index2\">\r\n\t\t\t\t\t\t\t\t<view class=\"smalltitle\" :class=\"{ 'active': indexTwoProductIndex === index2 }\"\r\n\t\t\t\t\t\t\t\t\t:id=\"'producttitle-' + index + index2\" @click=\"handleTwoTabClick(index2)\">{{\r\n\t\t\t\t\t\t\t\t\t\tchild.name\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<!-- <view class=\"product-item\" @click=\"toggleSpecifications(product, $event)\" -->\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</scroll-view>\r\n\r\n\r\n\t\t\t\t<scroll-view scroll-x=\"true\" style=\"white-space: nowrap; width: 710rpx;\">\r\n\t\t\t\t\t<view class=\"left\" style=\"display: flex; min-width: max-content;\">\r\n\t\t\t\t\t\t<!-- 下拉列表 -->\r\n\t\t\t\t\t\t<view class=\"product-item\" v-for=\"(product, index1) in indexTwoProduct\" :key=\"index1\"\r\n\t\t\t\t\t\t\t:id=\"'product-' + index\" @click=\"addProduct(product)\"\r\n\t\t\t\t\t\t\tstyle=\"margin: 10rpx;display: flex;padding: 10rpx;position: relative;align-items: center;flex-shrink: 0;\">\r\n\r\n\t\t\t\t\t\t\t<image @longpress=\"onLongPress($event, product)\"\r\n\t\t\t\t\t\t\t\************************=\"onTouchMove($event)\"\r\n\t\t\t\t\t\t\t\***********************=\"onTouchEnd($event)\" :style=\"'height: 50rpx;'\" mode=\"heightFix\"\r\n\t\t\t\t\t\t\t\t:src=\"product.image\" />\r\n\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\tstyle=\"padding: 0 10rpx;display: flex;flex-direction: column;align-items: center;justify-content:space-around;\">\r\n\t\t\t\t\t\t\t\t<view style=\"width: 100%;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;\">\r\n\t\t\t\t\t\t\t\t\t{{ product.storeName }}\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view style=\"color: #c9ab79;font-weight: 600;font-size: 20rpx;\">\r\n\t\t\t\t\t\t\t\t\t{{ product.width }}x{{ product.height }}mm\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t<!-- 规格选择下拉框 -->\r\n\t\t\t\t\t\t\t<view class=\"specifications-dropdown\" :style=\"{ left: (specLeft + 'rpx') }\"\r\n\t\t\t\t\t\t\t\tv-if=\"product.showSpecs\">\r\n\t\t\t\t\t\t\t\t<view class=\"specs-header\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"specs-title\">规格选择</text>\r\n\t\t\t\t\t\t\t\t\t<text class=\"close-btn\" @click.stop=\"toggleSpecifications(product, $event)\">×</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"specs-list\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"specs-item\" v-for=\"(spec, specIndex) in product.productValue\"\r\n\t\t\t\t\t\t\t\t\t\t:key=\"specIndex\" @click.stop=\"selectSpecification(product, spec)\">\r\n\t\t\t\t\t\t\t\t\t\t<view class=\"specs-info\">\r\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"specs-size\">{{ spec.width }}×{{ spec.height }}mm</text>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</scroll-view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"right\">\r\n\t\t\t\t<div style=\"height: 100rpx;display: flex;align-items: center;padding: 0 20rpx;\">\r\n\r\n\t\t\t\t\t<!-- 一些操作按钮 -->\r\n\t\t\t\t\t<div v-if=\"!isLogin\" style=\"display: flex;justify-content: flex-end;\">\r\n\t\t\t\t\t\t<div class=\"botton_1\" @click=\"toLogin\">快速登录</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div v-else style=\"display: flex;justify-content: flex-end;gap: 10rpx;\">\r\n\t\t\t\t\t\t<div v-if=\"userInfo.hand <= 0\" class=\"botton_3\" @click=\"handVisible = true\">{{ '设置手围' }}</div>\r\n\t\t\t\t\t\t<button v-if=\"selectList.length > 5\" class=\"botton_2\" open-type=\"share\" hover-class='none'>\r\n\t\t\t\t\t\t\t<view class=\"\">分享手串</view>\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t<div v-if=\"selectList.length > 5 && uid == createUserId\" @click=\"userBraceletsSubmit\"\r\n\t\t\t\t\t\t\tclass=\"botton_3\">保存手串</div>\r\n\t\t\t\t\t\t<div v-if=\"selectList.length > 5 && uid != createUserId\" @click=\"saveSelfBraceletsSubmit\"\r\n\t\t\t\t\t\t\tclass=\"botton_3\">保存为自己的手串</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div\r\n\t\t\t\t\tstyle=\"display: flex;justify-content: center;align-items: center;height: 700rpx;border-bottom: #c9ab79 1px dashed ;border-top: #c9ab79 1px dashed ;position: relative;\">\r\n\r\n\t\t\t\t\t<!-- <movable-area :style=\"{ width: 600 + 'rpx', height: 600 + 'rpx' }\" :scale-area=\"true\">\r\n\t\t\t\t\t\t<movable-view direction=\"false\" :out-of-bounds=\"true\" scale scale-min=\"0.5\" x=\"100\" y=\"100\" scale-max=\"4\"> -->\r\n\t\t\t\t\t<div class=\"circle-container\" :style=\"{ width: radius * 2 + 'rpx', height: radius * 2 + 'rpx' }\">\r\n\t\t\t\t\t\t<div v-for=\"(item, index) in selectList\" :key=\"index\" class=\"marble\"\r\n\t\t\t\t\t\t\t:style=\"[selectList.length < 6 ? getMarbleStyleSmall(index) : getMarbleStyle(index)]\"\r\n\t\t\t\t\t\t\t@longpress=\"onMarbleLongPress(index, $event)\"\r\n\t\t\t\t\t\t\************************=\"onMarbleTouchMove(index, $event)\"\r\n\t\t\t\t\t\t\***********************=\"onMarbleTouchEnd(index, $event)\">\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<!-- \t</movable-view>\r\n\t\t\t\t\t </movable-area> -->\r\n\t\t\t\t\t<div style=\"position: absolute;top: 10rpx;left: 10rpx;text-align: left;\">\r\n\t\t\t\t\t\t<div @click=\"handVisible = true\" class=\"hand-text-1\" v-if=\"userInfo.hand > 0\">\r\n\t\t\t\t\t\t\t您的手围\r\n\t\t\t\t\t\t\t<span style=\"color: #DD5C5F;font-size: 26rpx;\">{{ userInfo.hand }}mm</span>。当前手串预估手围<span\r\n\t\t\t\t\t\t\t\tstyle=\"color: #DD5C5F;font-size: 26rpx;\">{{ realRadius }}mm</span>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t<div @click=\"handVisible = true\" class=\"hand-text-1\" v-else>\r\n\t\t\t\t\t\t\t当前手串预估手围<span style=\"color: #DD5C5F;font-size: 26rpx;\">{{ realRadius }}mm</span>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div\r\n\t\t\t\t\t\tstyle=\"position: absolute;top: 10rpx;right: 20rpx;display: flex;gap: 10rpx;flex-direction: column;\">\r\n\t\t\t\t\t\t<image @click=\"reset\"\r\n\t\t\t\t\t\t\tsrc=\"https://mpjoy.oss-cn-beijing.aliyuncs.com/crmebimage/public/maintain/2024/12/20/865abacc296140f79dcaa89b3d047e77r1bpnw5pwp.png\"\r\n\t\t\t\t\t\t\tmode=\"widthFix\" style=\"width: 60rpx;\" />\r\n\t\t\t\t\t\t<view class=\"hand-text-text\">重置</view>\r\n\t\t\t\t\t\t<image @click=\"backOne\"\r\n\t\t\t\t\t\t\tsrc=\"https://mpjoy.oss-cn-beijing.aliyuncs.com/crmebimage/public/maintain/2024/12/20/680c1002ea76449190e861e02b25d4bffvx635j582.png\"\r\n\t\t\t\t\t\t\tmode=\"widthFix\" style=\"width: 60rpx;\" />\r\n\t\t\t\t\t\t<view class=\"hand-text-text\">撤回</view>\r\n\t\t\t\t\t\t<!-- <image v-if=\"selectList.length > 5\" @click=\"userBraceletsSubmit\"\r\n\t\t\t\t\t\t\tsrc=\"https://mpjoy.oss-cn-beijing.aliyuncs.com/crmebimage/public/maintain/2024/12/20/d93a44342db34f0ca5e3aa008079d160cpo5l4eqxt.png\"\r\n\t\t\t\t\t\t\tmode=\"widthFix\" style=\"width: 80rpx;\" />\r\n\t\t\t\t\t\t<view class=\"hand-text-text\" v-if=\"selectList.length > 5\">保存</view> -->\r\n\t\t\t\t\t\t<!-- <image v-if=\"userInfo.hand <= 0\" @click=\"handVisible = true\"\"\r\n\t\t\t\t\t\t\tsrc=\"https://mpjoy.oss-cn-beijing.aliyuncs.com/crmebimage/public/maintain/2024/12/21/b06ed367abb24cc89f04c44718deac50t7lyqkalkg.png\"\r\n\t\t\t\t\t\t\tmode=\"widthFix\" style=\"width: 60rpx;\" />\r\n\t\t\t\t\t\t<view class=\"hand-text-text\" v-if=\"userInfo.hand <= 0\">手围</view> -->\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div style=\"position: absolute;bottom: 10rpx;left: 10rpx;text-align: left;\">\r\n\t\t\t\t\t\t<div @click=\"handVisible = true\" class=\"hand-text-1\" v-if=\"selectList.length > 0\">\r\n\t\t\t\t\t\t\t长按手串上面的珠子，可以灵活调整位置</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<!-- <div style=\"display: flex;justify-content: flex-end;position: absolute;bottom: 10rpx;right: 10rpx;\">\r\n\t\t\t\t\t\t<div v-if=\"selectList.length > 5 && changeFlag == false\" @click=\"changeFlag = true\"\r\n\t\t\t\t\t\t\tclass=\"botton_1\">调整顺序</div>\r\n\t\t\t\t\t\t<div v-if=\"selectList.length > 5 && changeFlag == true\" @click=\"changeFlag = false\"\r\n\t\t\t\t\t\t\tclass=\"botton_3\">调整完成</div>\r\n\t\t\t\t\t</div> -->\r\n\t\t\t\t</div>\r\n\t\t\t\t<div style=\"height: 100rpx;display: flex;align-items: center;padding: 0 10rpx;\">\r\n\t\t\t\t\t<div v-if=\"!changeFlag\"\r\n\t\t\t\t\t\tstyle=\"width: 60%;overflow-x: scroll;display: flex;gap: 10rpx;align-items: center;\">\r\n\t\t\t\t\t\t<div v-for=\"(item, index) in selectList\" :key=\"index\" class=\"product-spec-item\"\r\n\t\t\t\t\t\t\tstyle=\"position: relative;\">\r\n\t\t\t\t\t\t\t<div class=\"image-wrapper\">\r\n\t\t\t\t\t\t\t\t<image class=\"product-image\" :style=\"'height: 50rpx;'\" mode=\"heightFix\"\r\n\t\t\t\t\t\t\t\t\************=\"_previewImage(item.image)\" :src=\"item.image\" />\r\n\t\t\t\t\t\t\t\t<image class=\"delete-icon\" @click=\"delProduct(index)\" mode=\"heightFix\"\r\n\t\t\t\t\t\t\t\t\tsrc=\"https://mpjoy.oss-cn-beijing.aliyuncs.com/crmebimage/public/maintain/2024/11/22/264c59ab0f89409d91641278e6e07ec2be9a9yqbwu.png\" />\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div class=\"price-tag\">\r\n\t\t\t\t\t\t\t\t<text class=\"price-symbol\">¥</text>\r\n\t\t\t\t\t\t\t\t<text class=\"price-value\">{{ item.price }}</text>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div v-else style=\"width: 60%;\">\r\n\t\t\t\t\t\t<shmily-drag-image v-model=\"selectList\" keyName=\"image\"\r\n\t\t\t\t\t\t\t@changeprice=\"dualPrice\"></shmily-drag-image>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div style=\"width: 20%;font-size: 22rpx;\">\r\n\t\t\t\t\t\t<div>总价</div>\r\n\t\t\t\t\t\t<div>￥<span style=\"color: #c9ab79;font-weight: 600;\">{{ price }}</span>元</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div style=\"width: 20%;\">\r\n\t\t\t\t\t\t<div class=\"botton_2\" @click=\"toBuy\">去结算</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"content\">\r\n\t\t\t<div style=\"padding: 10rpx;\">\r\n\t\t\t\t<image style=\"width: 100%;\"\r\n\t\t\t\t\tsrc=\"https://mpjoy.oss-cn-beijing.aliyuncs.com/crmebimage/public/maintain/2024/11/22/dde1678a2a4f48f4ad694a26563b7eb2or11mp8qem.jpg\"\r\n\t\t\t\t\tmode=\"widthFix\" />\r\n\t\t\t</div>\r\n\t\t</view>\r\n\t\t<ljs-dialog title=\"编辑我的手围\" v-model=\"handVisible\" class=\"comTc\">\r\n\t\t\t<view class=\"comForm\">\r\n\t\t\t\t<div class=\"form-group\">\r\n\t\t\t\t\t<input type=\"text\" v-model=\"hand\" placeholder=\"请输入手围\">\r\n\t\t\t\t\t<span class=\"unit\">mm</span>\r\n\t\t\t\t</div>\r\n\r\n\t\t\t</view>\r\n\t\t\t<view class=\"operate\" style=\"margin-top: 0;\">\r\n\t\t\t\t<view class=\"but\" @click=\"fromSubmit\">确定</view>\r\n\t\t\t\t<view class=\"but grey\" @click=\"handVisible = false\">取消</view>\r\n\t\t\t</view>\r\n\t\t</ljs-dialog>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport { spread } from \"@/api/user\";\r\nimport {\r\n\tuserBraceletsInfo, userBraceletsUpdate\r\n} from '@/api/user.js';\r\nimport {\r\n\tcategoryZhuzi\r\n} from '@/api/store.js';\r\nimport { mapGetters } from \"vuex\";\r\nimport { toLogin } from '@/libs/login.js';\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tindexOneProduct: [],\r\n\t\t\tindexOneProductIndex: 0,\r\n\t\t\tindexTwoProduct: [],\r\n\t\t\tindexTwoProductIndex: 0,\r\n\t\t\tscrollIndex: 0,\r\n\t\t\tdropdownLeft: 0,\r\n\t\t\tspecLeft: 0,\r\n\t\t\tscrollIntoViewId: '', // 用于 scroll-into-view 的 id\r\n\t\t\tshowDropdown: false, // 控制下拉列表显示状态\r\n\t\t\tisDraggingMarble: false, // 新增：用于标识是否正在拖动珠子\r\n\t\t\tdraggingMarbleIndex: null, // 新增：记录正在拖动的珠子索引\r\n\t\t\tisDragging: false,\r\n\t\t\tdragProduct: null,\r\n\t\t\tdragPosition: {\r\n\t\t\t\tx: 0,\r\n\t\t\t\ty: 0\r\n\t\t\t},\r\n\t\t\tcircleRect: null,\r\n\t\t\tisInCircleArea: false,\r\n\t\t\tid: '',\r\n\t\t\tcreateUserId: '',\r\n\t\t\thand: 0,\r\n\t\t\tchangeFlag: false,\r\n\t\t\thandVisible: false,\r\n\t\t\tradius: 100, // 圆弧的半径\r\n\t\t\trealRadius: 0, // 圆弧的半径\r\n\t\t\tproductList: [],\r\n\t\t\tselectList: [],\r\n\t\t\tselectListChange: [],\r\n\t\t\tprice: 0\r\n\t\t}\r\n\t},\r\n\tcomputed: mapGetters(['isLogin', 'chatUrl', 'userInfo', 'uid']),\r\n\tonLoad(options) {\r\n\t\tthis.id = options.id;\r\n\t\t// #ifdef MP || APP-PLUS\r\n\t\t// 小程序链接进入获取绑定关系id\r\n\t\tsetTimeout(() => {\r\n\t\t\tif (options.spread) {\r\n\t\t\t\tapp.globalData.spread = options.spread;\r\n\t\t\t\tspread(options.spread).then(res => { })\r\n\t\t\t}\r\n\t\t}, 2000)\r\n\t\t// #endif\r\n\t\tthis.getAllCategory();\r\n\t\tthis.getInfo();\r\n\t\t// this.generateMarbles();\r\n\t},\r\n\tonShow() {\r\n\t\tthis.hand = this.userInfo.hand;\r\n\t},\r\n\t/**\r\n\t * 用户点击右上角分享\r\n\t */\r\n\t// #ifdef MP\r\n\tonShareAppMessage: function () {\r\n\t\tlet that = this;\r\n\t\treturn {\r\n\t\t\ttitle: '快来看看我自定义的手串吧~',\r\n\t\t\t// imageUrl:  '',\r\n\t\t\tpath: '/pages/bracelets/child/edit?id=' + that.id + '&spread=' + that.uid,\r\n\t\t}\r\n\t},\r\n\t// #endif\r\n\tmethods: {\r\n\t\thandleOneTabClick(index2) {\r\n\t\t\tthis.indexOneProductIndex = index2;\r\n\t\t\tthis.indexOneProduct = this.productList[0].child[index2].productVos;\r\n\t\t},\r\n\t\thandleTwoTabClick(index2) {\r\n\t\t\tthis.indexTwoProductIndex = index2;\r\n\t\t\tthis.indexTwoProduct = this.productList[1].child[index2].productVos;\r\n\t\t},\r\n\t\tbackOne() {\r\n\t\t\tthis.selectList = this.selectListChange.length > 1 ? this.selectListChange[this.selectListChange.length - 2].list : [];\r\n\t\t\tthis.selectListChange.pop();\r\n\t\t\tthis.generateMarbles();\r\n\t\t\tthis.dualPrice();\r\n\t\t},\r\n\t\treset() {\r\n\t\t\tthis.selectList = [];\r\n\t\t},\r\n\t\ttoBuy() {\r\n\t\t\tlet productIds = this.selectList.map(e => e.productId)\r\n\t\t\tlet productAttrValueIds = this.selectList.map(e => e.productAttrValueId)\r\n\t\t\tthis.$Order.getPreOrder(\"braceletsBuy\",\r\n\t\t\t\t[{\r\n\t\t\t\t\t\"productNum\": 1,\r\n\t\t\t\t}], productIds,\r\n\t\t\t\tproductAttrValueIds,\r\n\t\t\t\tnull);\r\n\t\t},\r\n\t\tfromSubmit: function () {\r\n\t\t\tlet that = this;\r\n\t\t\tif (!that.hand) {\r\n\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\ttitle: '手围不能为空'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t\tuserEditHand({ hand: that.hand }).then(res => {\r\n\r\n\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\ttitle: '设置成功',\r\n\t\t\t\t\ticon: 'success'\r\n\t\t\t\t}, {\r\n\t\t\t\t\ttab: 3,\r\n\t\t\t\t\turl: 1\r\n\t\t\t\t});\r\n\t\t\t\tthat.handVisible = false;\r\n\t\t\t\tthis.$store.dispatch('USERINFO');\r\n\t\t\t}).catch(msg => {\r\n\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\ttitle: msg || '修改失败'\r\n\t\t\t\t}, {\r\n\t\t\t\t\ttab: 3,\r\n\t\t\t\t\turl: 1\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t},\r\n\t\t_previewImage(image) {\r\n\t\t\tvar imgArr = [];\r\n\t\t\timgArr.push(image);\r\n\t\t\t//预览图片\r\n\t\t\tuni.previewImage({\r\n\t\t\t\turls: imgArr,\r\n\t\t\t\tcurrent: imgArr[0]\r\n\t\t\t});\r\n\r\n\t\t},\r\n\t\tdualPrice() {\r\n\t\t\tthis.price = this.selectList.reduce((accumulator, currentValue) => accumulator + parseFloat(currentValue.price), 0).toFixed(2);\r\n\t\t},\r\n\t\taddProduct(e) {\r\n\t\t\t\r\n\t\t\t// 手围限制：当userInfo.hand <= 0时，珠子的宽度之和不能超过350，否则提示“请先设置手围”，并且不能再添加珠子，当userInfo.hand > 0时，珠子的宽度之和不能超过userInfo.hand的130%，否则提示“手围不够”，并且不能再添加珠子\r\n\t\t\tif (this.userInfo.hand <= 0) {\r\n\t\t\t\tif ((this.selectList.map(gg => { return parseFloat(gg.width) }).reduce((accumulator, currentValue) => accumulator + currentValue, 0) + parseFloat(e.width)) > 350) {\r\n\t\t\t\t\treturn this.$util.Tips({\r\n\t\t\t\t\t\ttitle: '珠子手串太大啦，请先设置手围'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tif ((this.selectList.map(gg => { return parseFloat(gg.width) }).reduce((accumulator, currentValue) => accumulator + currentValue, 0) + parseFloat(e.width)) > this.userInfo.hand * 1.5) {\r\n\t\t\t\t\treturn this.$util.Tips({\r\n\t\t\t\t\t\ttitle: '珠子的手围不能超过您手围的50%'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// if (this.selectList.length >= 20) {\r\n\t\t\t// \treturn this.$util.Tips({\r\n\t\t\t// \t\ttitle: '最多只能添加20个珠子'\r\n\t\t\t// \t});\r\n\t\t\t// }\r\n\t\t\te.productId = e.id;\r\n\t\t\tthis.selectList.push(e);\r\n\t\t\tthis.selectListChange.push({ list: this.deepCopy(this.selectList) });\r\n\t\t\tthis.generateMarbles();\r\n\t\t\tthis.dualPrice();\r\n\t\t},\r\n\t\tdelProduct(index) {\r\n\t\t\tthis.selectList.splice(index, 1);\r\n\t\t\tthis.selectListChange.push({ list: this.deepCopy(this.selectList) });\r\n\t\t\tthis.generateMarbles();\r\n\t\t\tthis.dualPrice();\r\n\t\t},\r\n\t\tgetMarbleStyleSmall(index) {\r\n\t\t\tconst angle = (index * 2 * Math.PI) / this.selectList.length;\r\n\t\t\tconst x = this.radius + (this.radius * Math.cos(angle));\r\n\t\t\tconst y = this.radius + (this.radius * Math.sin(angle));\r\n\t\t\tconst rotation = angle + (Math.PI / 2); // 旋转角度，使珠子垂直于原点\r\n\r\n\t\t\treturn {\r\n\t\t\t\tposition: 'absolute',\r\n\t\t\t\tleft: x - (this.selectList[index].width * 3 / 2) + 'rpx',\r\n\t\t\t\ttop: y - (this.selectList[index].height * 3 / 2) + 'rpx',\r\n\t\t\t\twidth: this.selectList[index].width * 3 + 'rpx',\r\n\t\t\t\theight: this.selectList[index].height * 3 + 'rpx',\r\n\t\t\t\ttransform: `rotate(${rotation}rad)`,\r\n\t\t\t\tbackground: `url('${this.selectList[index].image}') no-repeat center`,\r\n\t\t\t\tbackgroundSize: `cover`\r\n\t\t\t};\r\n\t\t},\r\n\t\tgetMarbleStyle(index) {\r\n\t\t\t// Calculate total circumference based on bead sizes\r\n\t\t\tconst totalBeadsWidth = this.selectList.reduce((sum, bead) =>\r\n\t\t\t\tsum + (bead.width * 3), 0);\r\n\r\n\t\t\t// Add small gaps between beads (5% of average bead size)\r\n\t\t\tconst spacing = (totalBeadsWidth / this.selectList.length) * 0.05;\r\n\t\t\tconst circumference = totalBeadsWidth + (spacing * this.selectList.length);\r\n\r\n\t\t\t// Calculate radius to match circle border\r\n\t\t\tconst dynamicRadius = circumference / (2 * Math.PI);\r\n\r\n\t\t\t// Calculate position on circle for current bead\r\n\t\t\tlet angleOffset = 0;\r\n\t\t\tfor (let i = 0; i < index; i++) {\r\n\t\t\t\tconst prevBeadWidth = this.selectList[i].width * 3;\r\n\t\t\t\tangleOffset += (prevBeadWidth + spacing) / dynamicRadius;\r\n\t\t\t}\r\n\r\n\t\t\tconst currentBeadWidth = this.selectList[index].width * 3;\r\n\t\t\tconst angle = angleOffset + (currentBeadWidth / 2) / dynamicRadius;\r\n\r\n\t\t\t// Position bead exactly on circle circumference\r\n\t\t\tconst x = dynamicRadius * (1 + Math.cos(angle));\r\n\t\t\tconst y = dynamicRadius * (1 + Math.sin(angle));\r\n\r\n\t\t\treturn {\r\n\t\t\t\tposition: 'absolute',\r\n\t\t\t\tleft: (x - currentBeadWidth / 2) + 'rpx',\r\n\t\t\t\ttop: (y - (this.selectList[index].height * 3 / 2)) + 'rpx',\r\n\t\t\t\twidth: currentBeadWidth + 'rpx',\r\n\t\t\t\theight: (this.selectList[index].height * 3) + 'rpx',\r\n\t\t\t\ttransform: `rotate(${angle + Math.PI / 2}rad)`,\r\n\t\t\t\tbackground: `url('${this.selectList[index].image}') no-repeat center`,\r\n\t\t\t\tbackgroundSize: 'cover',\r\n\t\t\t\ttransition: 'all 0.3s ease'\r\n\t\t\t};\r\n\t\t},\r\n\r\n\t\tgenerateMarbles() {\r\n\t\t\tlet realRoundWidth = this.selectList.map(e => { return parseFloat(e.width) }).reduce((accumulator, currentValue) => accumulator + currentValue, 0);\r\n\t\t\tthis.realRadius = (realRoundWidth).toFixed(2);\r\n\t\t\tif (this.selectList.length < 6) {\r\n\t\t\t\tthis.radius = 100;\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tconst totalBeadsWidth = this.selectList.reduce((sum, bead) =>\r\n\t\t\t\tsum + (parseFloat(bead.width) * 3), 0);\r\n\r\n\t\t\tconst spacing = (totalBeadsWidth / this.selectList.length) * 0.05;\r\n\t\t\tconst totalCircumference = totalBeadsWidth + (spacing * this.selectList.length);\r\n\r\n\t\t\t// Set radius to match circle border\r\n\t\t\tthis.radius = totalCircumference / (2 * Math.PI);\r\n\t\t\t//   this.realRadius = (totalCircumference / Math.PI).toFixed(2);\r\n\t\t},\r\n\t\tgetAllCategory() {\r\n\t\t\tcategoryZhuzi().then(res => {\r\n\t\t\t\tthis.productList = res.data;\r\n\t\t\t\tif (this.productList && this.productList.length > 0 && this.productList[0].child) {\r\n\t\t\t\t\tthis.indexOneProduct = this.productList[0].child[0].productVos;\r\n\t\t\t\t}\r\n\t\t\t\tif (this.productList && this.productList.length > 1 && this.productList[1].child) {\r\n\t\t\t\t\tthis.indexTwoProduct = this.productList[1].child[0].productVos;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tgetInfo() {\r\n\t\t\tuserBraceletsInfo({ id: this.id }).then(res => {\r\n\t\t\t\tthis.createUserId = res.data.userId;\r\n\t\t\t\tthis.selectList = res.data.userBraceletsItemEntities;\r\n\t\t\t\tthis.generateMarbles();\r\n\t\t\t\tthis.dualPrice();\r\n\t\t\t})\r\n\t\t},\r\n\t\tuserBraceletsSubmit() {\r\n\t\t\tlet productIds = this.selectList.map(e => e.productId)\r\n\t\t\tlet productAttrValueIds = this.selectList.map(e => e.productAttrValueId)\r\n\t\t\tlet userId = this.uid\r\n\t\t\tlet createUserId = this.createUserId\r\n\t\t\tlet id = this.id\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle: '提示',\r\n\t\t\t\tcontent: '确认保存手串',\r\n\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\t\ttitle: '保存中'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tuserBraceletsUpdate({\r\n\t\t\t\t\t\t\tid: id,\r\n\t\t\t\t\t\t\tuserId: userId,\r\n\t\t\t\t\t\t\tcreateUserId: createUserId,\r\n\t\t\t\t\t\t\tproductAttrValueIds: productAttrValueIds,\r\n\t\t\t\t\t\t\tproductIds: productIds\r\n\t\t\t\t\t\t}).then(res => {\r\n\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\t\t\tcontent: '保存成功',\r\n\t\t\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\t\t\tconsole.log('用户点击确定');\r\n\t\t\t\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\t\t\t\tconsole.log('用户点击取消');\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}).catch(res => {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\ttitle: '错误信息',\r\n\t\t\t\t\t\t\t\tcontent: res,\r\n\t\t\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\t\t\tconsole.log('用户点击确定');\r\n\t\t\t\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\t\t\t\tconsole.log('用户点击取消');\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\tconsole.log('用户点击取消');\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\ttoLogin() {\r\n\r\n\t\t\ttoLogin()\r\n\t\t},\r\n\r\n\r\n\t\tonLongPress(event, product) {\r\n\t\t\tconst touch = event.touches[0];\r\n\r\n\t\t\t// 开始拖拽\r\n\t\t\tthis.isDragging = true;\r\n\t\t\tthis.dragProduct = product;\r\n\t\t\tthis.dragPosition = {\r\n\t\t\t\tx: touch.clientX - (product.width * 3 / 2),\r\n\t\t\t\ty: touch.clientY - (product.height * 3 / 2)\r\n\t\t\t};\r\n\r\n\t\t\t// 获取圆形容器位置\r\n\t\t\tconst query = uni.createSelectorQuery();\r\n\t\t\tquery.select('.circle-container').boundingClientRect(data => {\r\n\t\t\t\tthis.circleRect = data;\r\n\t\t\t}).exec();\r\n\t\t},\r\n\r\n\t\tonTouchMove(event) {\r\n\t\t\tif (!this.isDragging) return;\r\n\r\n\t\t\tconst touch = event.touches[0];\r\n\t\t\tthis.dragPosition = {\r\n\t\t\t\tx: touch.clientX - (this.dragProduct.width * 3 / 2),\r\n\t\t\t\ty: touch.clientY - (this.dragProduct.height * 3 / 2)\r\n\t\t\t};\r\n\t\t},\r\n\r\n\t\tonChange(event) {\r\n\t\t\tif (!this.isDragging || !this.circleRect) return;\r\n\r\n\t\t\tconst { x, y } = event.detail;\r\n\t\t\tconst circle = {\r\n\t\t\t\tcenterX: this.circleRect.left + this.circleRect.width / 2,\r\n\t\t\t\tcenterY: this.circleRect.top + this.circleRect.height / 2,\r\n\t\t\t\tradius: this.radius\r\n\t\t\t};\r\n\r\n\t\t\tconst distance = Math.sqrt(\r\n\t\t\t\tMath.pow(x + (this.dragProduct.width * 3 / 2) - circle.centerX, 2) +\r\n\t\t\t\tMath.pow(y + (this.dragProduct.height * 3 / 2) - circle.centerY, 2)\r\n\t\t\t);\r\n\r\n\t\t\t// Update hit detection state\r\n\t\t\tthis.isInCircleArea = Math.abs(distance - circle.radius) <= 50;\r\n\t\t},\r\n\t\tonChange2(event) {\r\n\t\t\tif (!this.isDragging) return;\r\n\r\n\t\t\tif (this.circleRect) {\r\n\t\t\t\tconst { x, y } = event.detail;\r\n\t\t\t\tconst centerX = this.circleRect.left + this.circleRect.width / 2;\r\n\t\t\t\tconst centerY = this.circleRect.top + this.circleRect.height / 2;\r\n\t\t\t\tconst distance = Math.sqrt(\r\n\t\t\t\t\tMath.pow(x + (this.dragProduct.width * 3 / 2) - centerX, 2) +\r\n\t\t\t\t\tMath.pow(y + (this.dragProduct.height * 3 / 2) - centerY, 2)\r\n\t\t\t\t);\r\n\t\t\t\tthis.isInCircleArea = distance <= this.radius; // 增加缓冲区\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tonTouchEnd(event) {\r\n\t\t\tif (!this.isDragging) return;\r\n\r\n\t\t\tif (this.userInfo.hand <= 0) {\r\n\t\t\t\tif ((this.selectList.map(gg => { return parseFloat(gg.width) }).reduce((accumulator, currentValue) => accumulator + currentValue, 0) + parseFloat(this.dragProduct.width)) > 350) {\r\n\r\n\t\t\t\t\t// 重置状态\r\n\t\t\t\t\tthis.isDragging = false;\r\n\t\t\t\t\tthis.dragProduct = null;\r\n\t\t\t\t\treturn this.$util.Tips({\r\n\t\t\t\t\t\ttitle: '珠子手串太大啦，请先设置手围'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tif ((this.selectList.map(gg => { return parseFloat(gg.width) }).reduce((accumulator, currentValue) => accumulator + currentValue, 0) + parseFloat(this.dragProduct.width)) > this.userInfo.hand * 1.5) {\r\n\r\n\t\t\t\t\t// 重置状态\r\n\t\t\t\t\tthis.isDragging = false;\r\n\t\t\t\t\tthis.dragProduct = null;\r\n\t\t\t\t\treturn this.$util.Tips({\r\n\t\t\t\t\t\ttitle: '珠子的手围不能超过您手围的50%'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (this.selectList.length < 6) {\r\n\t\t\t\tconst touch = event.changedTouches[0];\r\n\t\t\t\tconst dropX = touch.clientX - (this.dragProduct.width * 3 / 2);\r\n\t\t\t\tconst dropY = touch.clientY - (this.dragProduct.height * 3 / 2);\r\n\t\t\t\tif (this.circleRect) {\r\n\t\t\t\t\tconst centerX = this.circleRect.left + (this.circleRect.width / 2);\r\n\t\t\t\t\tconst centerY = this.circleRect.top + (this.circleRect.height / 2);\r\n\t\t\t\t\tconst distance = Math.sqrt(\r\n\t\t\t\t\t\tMath.pow(dropX - centerX, 2) +\r\n\t\t\t\t\t\tMath.pow(dropY - centerY, 2)\r\n\t\t\t\t\t);\r\n\r\n\t\t\t\t\t// 检查是否在圆形区域内\r\n\t\t\t\t\tif (distance <= this.radius) {\r\n\t\t\t\t\t\t// 在圆形区域内的处理\r\n\t\t\t\t\t\tconst angle = this.calculateAngle(dropX, dropY);\r\n\t\t\t\t\t\tconst insertIndex = this.calculateInsertIndex(angle);\r\n\t\t\t\t\t\tthis.selectList.splice(insertIndex, 0, this.dragProduct);\r\n\t\t\t\t\t\tthis.selectListChange.push({ list: this.deepCopy(this.selectList) });\r\n\t\t\t\t\t\tthis.generateMarbles();\r\n\t\t\t\t\t\tthis.dualPrice();\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 重置状态\r\n\t\t\t\tthis.isDragging = false;\r\n\t\t\t\tthis.dragProduct = null;\r\n\t\t\t} else {\r\n\r\n\r\n\t\t\t\tconst touch = event.changedTouches[0];\r\n\t\t\t\tconst circle = {\r\n\t\t\t\t\tx: this.circleRect.left + this.circleRect.width / 2,\r\n\t\t\t\t\ty: this.circleRect.top + this.circleRect.height / 2\r\n\t\t\t\t};\r\n\r\n\t\t\t\t// Calculate drop angle relative to circle center\r\n\t\t\t\tconst dropAngle = Math.atan2(\r\n\t\t\t\t\ttouch.clientY - circle.y,\r\n\t\t\t\t\ttouch.clientX - circle.x\r\n\t\t\t\t);\r\n\r\n\t\t\t\t// Calculate optimal position based on bead sizes\r\n\t\t\t\tconst beadCount = this.selectList.length;\r\n\t\t\t\tconst totalArc = this.calculateTotalArc();\r\n\t\t\t\tconst arcPerBead = totalArc / beadCount;\r\n\r\n\t\t\t\t// Find nearest valid position\r\n\t\t\t\tlet insertIndex = Math.round(\r\n\t\t\t\t\t((dropAngle + 2 * Math.PI) % (2 * Math.PI)) / arcPerBead\r\n\t\t\t\t);\r\n\t\t\t\tinsertIndex = (insertIndex + beadCount) % beadCount;\r\n\r\n\t\t\t\t// Insert bead\r\n\t\t\t\tthis.selectList.splice(insertIndex, 0, this.dragProduct);\r\n\t\t\t\tthis.selectListChange.push({ list: this.deepCopy(this.selectList) });\r\n\t\t\t\tthis.generateMarbles();\r\n\t\t\t\tthis.dualPrice();\r\n\r\n\r\n\t\t\t\tthis.isDragging = false;\r\n\t\t\t\tthis.dragProduct = null;\r\n\t\t\t}\r\n\r\n\t\t},\r\n\t\tcalculateAngle(x, y) {\r\n\t\t\tconst centerX = this.circleRect.left + this.circleRect.width / 2;\r\n\t\t\tconst centerY = this.circleRect.top + this.circleRect.height / 2;\r\n\t\t\tconst deltaX = x + (this.dragProduct.width * 3 / 2) - centerX;\r\n\t\t\tconst deltaY = y + (this.dragProduct.height * 3 / 2) - centerY;\r\n\t\t\tlet angle = Math.atan2(deltaY, deltaX);\r\n\t\t\tif (angle < 0) angle += 2 * Math.PI;\r\n\t\t\treturn angle;\r\n\t\t},\r\n\r\n\t\tcalculateInsertIndex(angle) {\r\n\t\t\tif (this.selectList.length === 0) return 0;\r\n\t\t\tconst angleStep = (2 * Math.PI) / this.selectList.length;\r\n\t\t\treturn Math.floor(angle / angleStep);\r\n\t\t},\r\n\r\n\t\tonMarbleLongPress(index, event) {\r\n\t\t\tthis.isDraggingMarble = true; // 设置拖动状态\r\n\t\t\tthis.draggingMarbleIndex = index; // 记录正在拖动的珠子索引\r\n\t\t\tthis.dragProduct = this.selectList[index]; // 记录当前拖动的珠子\r\n\r\n\t\t\t// 获取圆形容器位置\r\n\t\t\tconst query = uni.createSelectorQuery();\r\n\t\t\tquery.select('.circle-container').boundingClientRect(data => {\r\n\t\t\t\tthis.circleRect = data;\r\n\t\t\t}).exec();\r\n\t\t},\r\n\r\n\t\tonMarbleTouchMove(index, event) {\r\n\t\t\tif (!this.isDraggingMarble) return;\r\n\r\n\t\t\tconst touch = event.touches[0];\r\n\t\t\tthis.dragPosition = {\r\n\t\t\t\tx: touch.clientX - (this.dragProduct.width * 3 / 2),\r\n\t\t\t\ty: touch.clientY - (this.dragProduct.height * 3 / 2),\r\n\t\t\t};\r\n\t\t},\r\n\r\n\t\t// Circle position adjustment handlers  \r\n\t\tonMarbleTouchEnd(index, event) {\r\n\t\t\tif (!this.isDraggingMarble) return;\r\n\t\t\tif (this.selectList.length < 6) {\r\n\r\n\t\t\t\tconst touch = event.changedTouches[0];\r\n\t\t\t\tconst dropX = touch.clientX;\r\n\t\t\t\tconst dropY = touch.clientY;\r\n\r\n\t\t\t\t// 确保珠子在圆环内\r\n\t\t\t\tif (this.circleRect) {\r\n\t\t\t\t\tconst centerX = this.circleRect.left + this.circleRect.width / 2;\r\n\t\t\t\t\tconst centerY = this.circleRect.top + this.circleRect.height / 2;\r\n\t\t\t\t\tconst distance = Math.sqrt(\r\n\t\t\t\t\t\tMath.pow(dropX - centerX, 2) +\r\n\t\t\t\t\t\tMath.pow(dropY - centerY, 2)\r\n\t\t\t\t\t);\r\n\r\n\t\t\t\t\tif (distance <= this.radius) {\r\n\t\t\t\t\t\t// 计算新的插入位置\r\n\t\t\t\t\t\tconst angle = this.calculateAngle(dropX, dropY);\r\n\t\t\t\t\t\tconst newIndex = this.calculateInsertIndex(angle);\r\n\r\n\t\t\t\t\t\t// 确保新索引不与当前索引相同\r\n\t\t\t\t\t\tif (newIndex !== this.draggingMarbleIndex) {\r\n\t\t\t\t\t\t\t// 移动珠子到新的位置\r\n\t\t\t\t\t\t\tconst movingMarble = this.selectList.splice(this.draggingMarbleIndex, 1)[0];\r\n\t\t\t\t\t\t\tthis.selectList.splice(newIndex, 0, movingMarble);\r\n\t\t\t\t\t\t\tthis.selectListChange.push({ list: this.deepCopy(this.selectList) });\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 重新计算珠子的排列和总金额\r\n\t\t\t\tthis.generateMarbles(); // 重新生成珠子的排列\r\n\t\t\t\t// 重置状态\r\n\t\t\t\tthis.isDraggingMarble = false;\r\n\t\t\t\tthis.draggingMarbleIndex = null;\r\n\t\t\t\tthis.dragProduct = null; // 清空拖动的珠子\r\n\r\n\t\t\t} else {\r\n\r\n\t\t\t\tconst touch = event.changedTouches[0];\r\n\t\t\t\tconst circle = {\r\n\t\t\t\t\tx: this.circleRect.left + this.circleRect.width / 2,\r\n\t\t\t\t\ty: this.circleRect.top + this.circleRect.height / 2\r\n\t\t\t\t};\r\n\r\n\t\t\t\t// Calculate drop angle relative to circle center\r\n\t\t\t\tconst dropAngle = Math.atan2(\r\n\t\t\t\t\ttouch.clientY - circle.y,\r\n\t\t\t\t\ttouch.clientX - circle.x\r\n\t\t\t\t);\r\n\r\n\t\t\t\t// Calculate optimal position based on bead sizes\r\n\t\t\t\tconst beadCount = this.selectList.length;\r\n\t\t\t\tconst totalArc = this.calculateTotalArc();\r\n\t\t\t\tconst arcPerBead = totalArc / beadCount;\r\n\r\n\t\t\t\t// Find nearest valid position\r\n\t\t\t\tlet targetIndex = Math.round(\r\n\t\t\t\t\t((dropAngle + 2 * Math.PI) % (2 * Math.PI)) / arcPerBead\r\n\t\t\t\t);\r\n\t\t\t\ttargetIndex = (targetIndex + beadCount) % beadCount;\r\n\r\n\t\t\t\tif (targetIndex !== this.draggingMarbleIndex) {\r\n\t\t\t\t\tconst bead = this.selectList.splice(this.draggingMarbleIndex, 1)[0];\r\n\t\t\t\t\tthis.selectList.splice(targetIndex, 0, bead);\r\n\t\t\t\t\tthis.selectListChange.push({ list: this.deepCopy(this.selectList) });\r\n\t\t\t\t}\r\n\r\n\t\t\t\tthis.isDraggingMarble = false;\r\n\t\t\t\tthis.draggingMarbleIndex = null;\r\n\t\t\t\tthis.dragProduct = null;\r\n\t\t\t\tthis.generateMarbles();\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tcalculateTotalArc() {\r\n\t\t\tconst totalWidth = this.selectList.reduce((sum, bead) =>\r\n\t\t\t\tsum + (bead.width * 3), 0);\r\n\t\t\tconst spacing = (totalWidth / this.selectList.length) * 0.05;\r\n\t\t\treturn 2 * Math.PI * (1 + spacing / totalWidth);\r\n\t\t},\r\n\r\n\t\tcalculateInsertIndexFromAngle(angle) {\r\n\t\t\tconst normalizedAngle = (angle + 2 * Math.PI) % (2 * Math.PI);\r\n\t\t\tconst beadCount = this.selectList.length;\r\n\t\t\tif (beadCount === 0) return 0;\r\n\r\n\t\t\treturn Math.floor((normalizedAngle * beadCount) / (2 * Math.PI));\r\n\t\t},\r\n\t\tdeepCopy(array) {\r\n\t\t\treturn array.map(item => ({ ...item })); // 创建对象的深拷贝\r\n\t\t},\r\n\t\ttoggleDropdown(event, index) {\r\n\t\t\t// 从事件对象中获取点击位置的left坐标\r\n\t\t\tconst clickLeft = event.touches[0].clientX;\r\n\t\t\tthis.dropdownLeft = clickLeft; // 存储点击位置的left值\r\n\t\t\tthis.scrollIndex = index;\r\n\t\t\tthis.showDropdown = !this.showDropdown; // 切换下拉列表显示状态\r\n\t\t\tthis.productList.forEach(category => {\r\n\t\t\t\tif (category.child) {\r\n\t\t\t\t\tcategory.child.forEach(e => {\r\n\r\n\t\t\t\t\t\tif (e.productVos) {\r\n\t\t\t\t\t\t\te.productVos.forEach(p => {\r\n\t\t\t\t\t\t\t\tthis.$set(p, 'showSpecs', false);\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tscrollToProduct(index, index1) {\r\n\t\t\t// 设置 scrollIntoView 属性为目标 id\r\n\t\t\tthis.scrollIntoViewId = `producttitle-${index}${index1}`;\r\n\t\t\tthis.productList.forEach(category => {\r\n\t\t\t\tif (category.child) {\r\n\t\t\t\t\tcategory.child.forEach(e => {\r\n\r\n\t\t\t\t\t\tif (e.productVos) {\r\n\t\t\t\t\t\t\te.productVos.forEach(p => {\r\n\t\t\t\t\t\t\t\tthis.$set(p, 'showSpecs', false);\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\tthis.showDropdown = false;\r\n\t\t},\r\n\r\n\r\n\t\ttoggleSpecifications(product, event) {\r\n\t\t\tconsole.log(product)\r\n\t\t\t// 关闭其他产品的规格选择\r\n\t\t\tthis.productList.forEach(category => {\r\n\t\t\t\tif (category.child) {\r\n\t\t\t\t\tcategory.child.forEach(e => {\r\n\r\n\t\t\t\t\t\tif (e.productVos) {\r\n\t\t\t\t\t\t\te.productVos.forEach(p => {\r\n\t\t\t\t\t\t\t\tif (p.id !== e.id) {\r\n\t\t\t\t\t\t\t\t\tthis.$set(p, 'showSpecs', false);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t});\r\n\r\n\t\t\tthis.showDropdown = false;\r\n\t\t\t// 从事件对象中获取点击位置的left坐标\r\n\t\t\tconst clickLeft = event.touches[0].clientX;\r\n\t\t\tthis.specLeft = clickLeft; // 存储点击位置的left值\r\n\t\t\t// 切换当前产品的规格选择显示状态\r\n\t\t\tthis.$set(product, 'showSpecs', !product.showSpecs);\r\n\t\t},\r\n\r\n\t\tselectSpecification(product, spec) {\r\n\t\t\tconsole.log(product)\r\n\t\t\tlet result = JSON.parse(JSON.stringify(product))\r\n\t\t\t// 更新产品的规格信息\r\n\t\t\tresult.width = spec.width;\r\n\t\t\tresult.height = spec.height;\r\n\t\t\tresult.price = spec.price;\r\n\t\t\tresult.productAttrValueId = spec.id;\r\n\r\n\t\t\t// 关闭规格选择框\r\n\t\t\tthis.$set(product, 'showSpecs', false);\r\n\r\n\t\t\t// 添加到选中列表\r\n\t\t\tthis.addProduct(result);\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.bigtitle {\r\n\t// width: 100rpx;\r\n\theight: 80rpx;\r\n\tline-height: 80rpx;\r\n\tmin-width: 120rpx;\r\n\t// background-color: #ffd89b;\r\n\tfont-size: 36rpx;\r\n\tfont-weight: 600;\r\n\ttext-align: center;\r\n\tcolor: #c9ab79;\r\n\tborder-right: #c9ab79 1rpx dashed;\r\n\tposition: sticky;\r\n\t/* 吸顶效果 */\r\n\tleft: 0;\r\n\t/* 距离顶部的距离 */\r\n\tz-index: 99;\r\n\t/* 确保在其他元素之上 */\r\n\tbackground-color: white;\r\n}\r\n\r\n.smalltitle {\r\n\t// width: 100rpx;\r\n\tmin-width: 110rpx;\r\n\theight: 80rpx;\r\n\tline-height: 80rpx;\r\n\t// background-color: #ffe4b7;\r\n\tfont-size: 32rpx;\r\n\tfont-weight: 500;\r\n\tposition: sticky;\r\n\t/* 吸顶效果 */\r\n\tleft: 0;\r\n\t/* 距离顶部的距离 */\r\n\tz-index: 10;\r\n\t/* 确保在其他元素之上 */\r\n\ttransition: all 0.3s ease; // 添加过渡效果\r\n\tcursor: pointer;\r\n\t// flex: 1;\r\n\r\n\t// 添加hover效果\r\n\t&:hover {\r\n\t\t// background-color: #c9ab79;\r\n\t}\r\n\r\n\t// 选中状态样式\r\n\t&.active {\r\n\t\t// background-color: #DD5C5F;\r\n\t\tcolor: #c9ab79;\r\n\t\tfont-weight: 600;\r\n\t\tposition: relative;\r\n\r\n\t\t// 可选：添加底部指示器\r\n\t\t&::after {\r\n\t\t\tcontent: '';\r\n\t\t\tposition: absolute;\r\n\t\t\tbottom: 0;\r\n\t\t\tleft: 50%;\r\n\t\t\ttransform: translateX(-50%);\r\n\t\t\twidth: 40rpx;\r\n\t\t\theight: 5rpx;\r\n\t\t\tbackground-color: #c9ab79;\r\n\t\t\tborder-radius: 4rpx;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.circle-container {\r\n\tposition: relative;\r\n\tborder-radius: 50%;\r\n\t// border: gray 1px dashed;\r\n\tborder: gray 1px solid;\r\n\t// transition: all 1s;\r\n}\r\n\r\n.marble {\r\n\tposition: absolute;\r\n\t// transition: all 1s;\r\n}\r\n\r\n.content {\r\n\twidth: 710rpx;\r\n\tmargin-left: 20rpx;\r\n\tmargin-top: 20rpx;\r\n\tmargin-bottom: 20rpx;\r\n\tbackground-color: white;\r\n\tborder-radius: 16rpx;\r\n\tbox-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.top {\r\n\twidth: 710rpx;\r\n\tmargin-left: 20rpx;\r\n\tmargin-top: 20rpx;\r\n\r\n\t.left-bbb {\r\n\t\t// width: 30%;\r\n\t\tbackground-color: white;\r\n\t\tborder-radius: 16rpx;\r\n\t\tbox-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);\r\n\r\n\t\t.left {\r\n\t\t\t// width: 30%;\r\n\t\t\t// background-color: #ffe4b7;\r\n\t\t\t// border-radius: 16rpx;\r\n\t\t\theight: 80rpx;\r\n\t\t\t// overflow-x: scroll;\r\n\t\t\twidth: 710rpx;\r\n\t\t\tdisplay: flex;\r\n\r\n\t\t}\r\n\r\n\t\t.left-child {\r\n\t\t\t// width: 30%;\r\n\t\t\t// background-color: white;\r\n\t\t\t// border-radius: 16rpx;\r\n\t\t\theight: 80rpx;\r\n\t\t\t// overflow-x: scroll;\r\n\t\t\t// width: 710rpx;\r\n\t\t\tdisplay: flex;\r\n\r\n\t\t}\r\n\r\n\t\t.left-bottom {\r\n\t\t\ttext-align: center;\r\n\t\t\tcolor: #DD5C5F;\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tpadding: 20rpx 0;\r\n\t\t\tbackground: #fff5f5;\r\n\t\t\tborder-radius: 8rpx;\r\n\t\t\tmargin-top: 20rpx;\r\n\t\t\tborder-top-left-radius: 16rpx;\r\n\t\t\tborder-top-right-radius: 16rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.right {\r\n\t\tmargin-top: 20rpx;\r\n\t\t// width: 70%;\r\n\t\tbackground-color: white;\r\n\t\tborder-radius: 16rpx;\r\n\t\ttext-align: center;\r\n\t\theight: 900rpx;\r\n\t\tposition: relative;\r\n\t\tbox-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);\r\n\t}\r\n}\r\n\r\n.botton {\r\n\tbackground-color: #c9ab79;\r\n\r\n\tcolor: #fff;\r\n\tfont-size: 22rpx;\r\n\theight: 65rpx;\r\n\tborder-radius: 50rpx;\r\n\ttext-align: center;\r\n\tline-height: 65rpx;\r\n}\r\n\r\n.botton_1 {\r\n\tbackground-color: #c9ab79;\r\n\tpadding: 0 20rpx;\r\n\tcolor: #fff;\r\n\tfont-size: 22rpx;\r\n\theight: 65rpx;\r\n\tborder-radius: 50rpx;\r\n\ttext-align: center;\r\n\tline-height: 65rpx;\r\n}\r\n\r\n.botton_2 {\r\n\tbackground-color: #DD5C5F;\r\n\tpadding: 0 20rpx;\r\n\tcolor: #fff;\r\n\tfont-size: 22rpx;\r\n\theight: 65rpx;\r\n\tborder-radius: 50rpx;\r\n\ttext-align: center;\r\n\tline-height: 65rpx;\r\n}\r\n\r\n.botton_3 {\r\n\tbackground-color: #398ade;\r\n\tpadding: 0 20rpx;\r\n\tcolor: #fff;\r\n\tfont-size: 22rpx;\r\n\theight: 65rpx;\r\n\tborder-radius: 50rpx;\r\n\ttext-align: center;\r\n\tline-height: 65rpx;\r\n}\r\n\r\n.comTc {\r\n\t.comForm {\r\n\t\twidth: 100%;\r\n\t\theight: calc(100% - 100upx);\r\n\t\toverflow-y: auto;\r\n\t}\r\n\r\n\t.operate {\r\n\t\theight: 100upx;\r\n\t\tpadding: 0 30upx;\r\n\t\tmargin-top: 20upx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\r\n\t\t.but {\r\n\t\t\tflex: 1;\r\n\t\t\theight: 56upx;\r\n\t\t\tline-height: 56upx;\r\n\t\t\ttext-align: center;\r\n\t\t\tborder-radius: 10upx;\r\n\t\t\tborder: 2upx solid #084AA1;\r\n\t\t\tbackground-color: #084AA1;\r\n\t\t\tcolor: #FFF;\r\n\t\t\tmargin-right: 20upx;\r\n\r\n\t\t\t&:last-child {\r\n\t\t\t\tmargin-right: 0;\r\n\t\t\t}\r\n\r\n\t\t\t&.grey {\r\n\t\t\t\tborder: 2upx solid #eee;\r\n\t\t\t\tbackground-color: #FFF;\r\n\t\t\t\tcolor: #666;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.form-group {\r\n\tposition: relative;\r\n\tmargin-bottom: 20px;\r\n}\r\n\r\n.form-group input {\r\n\tpadding: 10px;\r\n\tmargin-bottom: 10px;\r\n\tborder: 1px solid #ddd;\r\n\tborder-radius: 4px;\r\n}\r\n\r\n.unit {\r\n\tposition: absolute;\r\n\tright: 10px;\r\n\ttop: 50%;\r\n\ttransform: translateY(-50%);\r\n\tcolor: red;\r\n\tfont-weight: 600;\r\n}\r\n.hand-text-1 {\r\n\tfont-size: 22rpx;\r\n\tfont-weight: 600;\r\n\tcolor: #c9ab79;\r\n\tmargin: 10rpx 0;\r\n}\r\n\r\n.hand-text-2 {\r\n\tfont-size: 24rpx;\r\n\tfont-weight: 600;\r\n\tcolor: #888888;\r\n\tmargin: 10rpx 0;\r\n}\r\n\r\n.lianying {\r\n\tposition: fixed;\r\n\tbottom: 100rpx;\r\n\tright: 0;\r\n\twidth: 100rpx;\r\n\theight: 100rpx;\r\n\tbackground-color: #c9ab79;\r\n\tborder-radius: 50%;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tcolor: white;\r\n\tflex-direction: column;\r\n\tfont-size: 28rpx;\r\n\tfont-weight: 600;\r\n\tline-height: 36rpx;\r\n}\r\n\r\n.movable-area {\r\n\tpointer-events: none;\r\n}\r\n\r\n.movable-view {\r\n\tpointer-events: auto;\r\n\tposition: fixed;\r\n}\r\n\r\n.product-item {\r\n\t// touch-action: none;\r\n\t// user-select: none;\r\n\t// -webkit-user-drag: none;\r\n\tposition: relative;\r\n\tbackground: white;\r\n\tcolor: black;\r\n\tfont-weight: 400;\r\n\tfont-size: 28rpx;\r\n}\r\n\r\n.dropdown-menu {\r\n\tposition: fixed;\r\n\ttop: 120rpx;\r\n\tleft: 0;\r\n\twidth: 100rpx;\r\n\tbackground-color: #fff;\r\n\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\tz-index: 11;\r\n\tborder-radius: 0 0 8rpx 8rpx;\r\n}\r\n\r\n.dropdown-item {\r\n\tpadding: 20rpx 0;\r\n\ttext-align: center;\r\n\tfont-size: 28rpx;\r\n\tborder-bottom: 1px solid #eee;\r\n\r\n\t&:last-child {\r\n\t\tborder-bottom: none;\r\n\t}\r\n\r\n\t&:active {\r\n\t\tbackground-color: #f5f5f5;\r\n\t}\r\n}\r\n\r\n.specifications-dropdown {\r\n\tposition: fixed;\r\n\ttop: 150rpx;\r\n\tleft: 30rpx;\r\n\twidth: 200rpx; // 稍微减小宽度\r\n\tbackground: #fff;\r\n\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);\r\n\tz-index: 999;\r\n\tborder-radius: 12rpx;\r\n\toverflow: hidden; // 确保内容不会溢出圆角\r\n}\r\n\r\n.specs-header {\r\n\tpadding: 20rpx 24rpx;\r\n\tborder-bottom: 1rpx solid #f5f5f5;\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tbackground: #fafafa; // 添加浅灰色背景\r\n}\r\n\r\n.specs-title {\r\n\tfont-size: 26rpx;\r\n\tcolor: #333;\r\n\tfont-weight: 500;\r\n\theight: 40rpx;\r\n\tline-height: 40rpx;\r\n}\r\n\r\n.close-btn {\r\n\tfont-size: 32rpx;\r\n\tcolor: #999;\r\n\tpadding: 0 10rpx;\r\n\tline-height: 1;\r\n}\r\n\r\n.specs-list {\r\n\tmax-height: 400rpx;\r\n\toverflow-y: auto;\r\n}\r\n\r\n.specs-item {\r\n\tpadding: 20rpx 24rpx;\r\n\tborder-bottom: 1rpx solid #f8f8f8;\r\n\ttransition: background-color 0.2s;\r\n\r\n\t&:active {\r\n\t\tbackground-color: #f9f9f9; // 点击效果\r\n\t}\r\n\r\n\t&:last-child {\r\n\t\tborder-bottom: none; // 最后一项不需要边框\r\n\t}\r\n}\r\n\r\n.specs-info {\r\n\tdisplay: flex;\r\n\t//   justify-content: space-between;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n}\r\n\r\n.specs-size {\r\n\tfont-size: 24rpx;\r\n\tcolor: #c9ab79;\r\n\tfont-weight: 500;\r\n\theight: 40rpx;\r\n\tline-height: 40rpx;\r\n}\r\n\r\n.specs-price {\r\n\tfont-size: 22rpx; // 减小价格字体\r\n\tcolor: #c9ab79;\r\n\tfont-weight: 500;\r\n\r\n\t&::before {\r\n\t\tcontent: '￥';\r\n\t\tfont-size: 20rpx; // 人民币符号更小\r\n\t\tmargin-right: 2rpx;\r\n\t}\r\n}\r\n\r\n.product-spec-item {\r\n\tposition: relative;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tborder-radius: 8rpx;\r\n\tpadding: 4rpx;\r\n}\r\n\r\n.image-wrapper {\r\n\tposition: relative;\r\n}\r\n\r\n.product-image {\r\n\tborder-radius: 6rpx;\r\n\tobject-fit: cover;\r\n}\r\n\r\n.delete-icon {\r\n\theight: 30rpx;\r\n\tposition: absolute;\r\n\tright: -6rpx;\r\n\ttop: -6rpx;\r\n\tbackground: #fff;\r\n\tborder-radius: 50%;\r\n\tpadding: 2rpx;\r\n}\r\n\r\n.price-tag {\r\n\tmargin-top: 6rpx;\r\n\tfont-size: 24rpx;\r\n\tcolor: #333;\r\n\tdisplay: flex;\r\n\talign-items: baseline;\r\n}\r\n\r\n.price-symbol {\r\n\tfont-size: 20rpx;\r\n\tcolor: #ff4d4f;\r\n\tmargin-right: 2rpx;\r\n}\r\n\r\n.price-value {\r\n\tcolor: #ff4d4f;\r\n\tfont-weight: 500;\r\n}\r\n\r\n.hand-text-text {\r\n\tfont-weight: 500;\r\n\tfont-size: 26rpx;\r\n\tcolor: #c9ab79;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=style&index=0&id=098d4cba&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=style&index=0&id=098d4cba&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363903384\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}