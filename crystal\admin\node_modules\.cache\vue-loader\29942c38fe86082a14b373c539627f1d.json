{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\list\\userDetails.vue?vue&type=template&id=879441c0&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\list\\userDetails.vue", "mtime": 1753666157942}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    [\n      _vm.psInfo\n        ? _c(\n            \"div\",\n            { staticClass: \"acea-row row-middle border_bottom pb-24\" },\n            [\n              _c(\"div\", { staticClass: \"avatar mr20\" }, [\n                _c(\"img\", { attrs: { src: _vm.psInfo.user.avatar } }),\n              ]),\n              _vm._v(\" \"),\n              _c(\"div\", { staticClass: \"dashboard-workplace-header-tip\" }, [\n                _c(\"p\", {\n                  staticClass: \"dashboard-workplace-header-tip-title\",\n                  domProps: {\n                    textContent: _vm._s(_vm.psInfo.user.nickname || \"-\"),\n                  },\n                }),\n                _vm._v(\" \"),\n                _c(\n                  \"div\",\n                  { staticClass: \"dashboard-workplace-header-tip-desc\" },\n                  [\n                    _c(\n                      \"span\",\n                      {\n                        staticClass:\n                          \"dashboard-workplace-header-tip-desc-sp pb-1\",\n                      },\n                      [_vm._v(\"余额: \" + _vm._s(_vm.psInfo.balance))]\n                    ),\n                    _vm._v(\" \"),\n                    _c(\n                      \"span\",\n                      {\n                        staticClass:\n                          \"dashboard-workplace-header-tip-desc-sp pb-1\",\n                      },\n                      [_vm._v(\"总计订单: \" + _vm._s(_vm.psInfo.allOrderCount))]\n                    ),\n                    _vm._v(\" \"),\n                    _c(\n                      \"span\",\n                      {\n                        staticClass:\n                          \"dashboard-workplace-header-tip-desc-sp pb-1\",\n                      },\n                      [\n                        _vm._v(\n                          \"总消费金额: \" + _vm._s(_vm.psInfo.allConsumeCount)\n                        ),\n                      ]\n                    ),\n                    _vm._v(\" \"),\n                    _c(\n                      \"span\",\n                      { staticClass: \"dashboard-workplace-header-tip-desc-sp\" },\n                      [_vm._v(\"积分: \" + _vm._s(_vm.psInfo.integralCount))]\n                    ),\n                    _vm._v(\" \"),\n                    _c(\n                      \"span\",\n                      { staticClass: \"dashboard-workplace-header-tip-desc-sp\" },\n                      [_vm._v(\"本月订单: \" + _vm._s(_vm.psInfo.mothOrderCount))]\n                    ),\n                    _vm._v(\" \"),\n                    _c(\n                      \"span\",\n                      { staticClass: \"dashboard-workplace-header-tip-desc-sp\" },\n                      [\n                        _vm._v(\n                          \"本月消费金额: \" + _vm._s(_vm.psInfo.mothConsumeCount)\n                        ),\n                      ]\n                    ),\n                  ]\n                ),\n              ]),\n            ]\n          )\n        : _vm._e(),\n      _vm._v(\" \"),\n      _c(\n        \"el-row\",\n        { staticClass: \"ivu-mt mt20\", attrs: { align: \"middle\", gutter: 10 } },\n        [\n          _c(\n            \"el-col\",\n            { attrs: { span: 4 } },\n            [\n              _c(\n                \"el-menu\",\n                {\n                  staticClass: \"el-menu-vertical-demo\",\n                  attrs: { \"default-active\": \"0\" },\n                  on: { select: _vm.changeType },\n                },\n                _vm._l(_vm.list, function (item, index) {\n                  return _c(\n                    \"el-menu-item\",\n                    { key: index, attrs: { name: item.val, index: item.val } },\n                    [\n                      _c(\"span\", { attrs: { slot: \"title\" }, slot: \"title\" }, [\n                        _vm._v(_vm._s(item.label)),\n                      ]),\n                    ]\n                  )\n                }),\n                1\n              ),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-col\",\n            { attrs: { span: 20 } },\n            [\n              _c(\n                \"el-table\",\n                {\n                  directives: [\n                    {\n                      name: \"loading\",\n                      rawName: \"v-loading\",\n                      value: _vm.loading,\n                      expression: \"loading\",\n                    },\n                  ],\n                  staticClass: \"tabNumWidth\",\n                  attrs: { data: _vm.tableData.data, \"max-height\": \"400\" },\n                },\n                _vm._l(_vm.columns, function (item, index) {\n                  return _c(\"el-table-column\", {\n                    key: index,\n                    attrs: {\n                      prop: item.key,\n                      label: item.title,\n                      width: \"item.minWidth\",\n                      \"show-overflow-tooltip\": true,\n                    },\n                  })\n                }),\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"div\",\n                { staticClass: \"block\" },\n                [\n                  _c(\"el-pagination\", {\n                    attrs: {\n                      \"page-sizes\": [6, 12, 18, 24],\n                      \"page-size\": _vm.tableFrom.limit,\n                      \"current-page\": _vm.tableFrom.page,\n                      layout: \"total, sizes, prev, pager, next, jumper\",\n                      total: _vm.tableData.total,\n                    },\n                    on: {\n                      \"size-change\": _vm.handleSizeChange,\n                      \"current-change\": _vm.pageChange,\n                    },\n                  }),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}