{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\list\\level.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\list\\level.vue", "mtime": 1753666157941}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _user = require(\"@/api/user\");\nvar _validate = require(\"@/utils/validate\");\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  props: {\n    levelInfo: {\n      type: Object,\n      default: {}\n    },\n    levelList: {\n      type: Array,\n      default: []\n    }\n  },\n  data: function data() {\n    return {\n      grade: '',\n      levelStatus: false,\n      ruleForm: {\n        isSub: false,\n        levelId: \"\",\n        uid: this.levelInfo.uid\n      }\n    };\n  },\n  created: function created() {\n    this.ruleForm.levelId = this.levelInfo.level ? Number(this.levelInfo.level) : '';\n  },\n  watch: {\n    levelInfo: function levelInfo(val) {\n      this.ruleForm.uid = val.uid || 0;\n      this.ruleForm.levelId = this.levelInfo.level ? Number(this.levelInfo.level) : val.levelId;\n    }\n  },\n  methods: {\n    submitForm: (0, _validate.Debounce)(function (formName) {\n      var _this = this;\n      this.$refs[formName].validate(function (valid) {\n        if (valid) {\n          (0, _user.userLevelUpdateApi)(_this.ruleForm).then(function (res) {\n            _this.$message.success('编辑成功');\n            _this.$parent.$parent.getList();\n            _this.$parent.$parent.levelVisible = false;\n            _this.$refs[formName].resetFields();\n            _this.grade = '';\n          });\n        } else {\n          return false;\n        }\n      });\n    }),\n    currentSel: function currentSel() {\n      var _this2 = this;\n      this.levelList.forEach(function (item) {\n        if (item.id == _this2.ruleForm.levelId) {\n          _this2.grade = item.grade;\n        }\n      });\n    },\n    resetForm: function resetForm(formName) {\n      var _this3 = this;\n      this.$nextTick(function () {\n        _this3.$refs[formName].resetFields();\n        _this3.grade = '';\n      });\n      this.$parent.$parent.levelVisible = false;\n    }\n  }\n};", null]}