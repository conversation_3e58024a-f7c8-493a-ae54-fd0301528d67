(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/users/login/index"],{"22dd":function(t,e,n){"use strict";n.r(e);var i=n("4649"),a=n("b75f");for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);n("f79c");var o=n("828b"),c=Object(o["a"])(a["default"],i["b"],i["c"],!1,null,"e2df1c88",null,!1,i["a"],void 0);e["default"]=c.exports},"2d1d":function(t,e,n){"use strict";(function(t,e){var i=n("47a9");n("5c2d");i(n("3240"));var a=i(n("22dd"));t.__webpack_require_UNI_MP_PLUGIN__=n,e(a.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},4649:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement;t._self._c;t._isMounted||(t.e0=function(e){t.current=1},t.e1=function(e){t.current=0})},a=[]},b75f:function(t,e,n){"use strict";n.r(e);var i=n("c1de"),a=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);e["default"]=a.a},c1de:function(t,e,n){"use strict";(function(t){var i=n("47a9"),a=n("3b2d");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=i(n("7eb4")),o=i(n("ee10")),c=(i(n("c9b6")),i(n("74cc"))),u=n("5904"),s=(function(t,e){if(!e&&t&&t.__esModule)return t;if(null===t||"object"!==a(t)&&"function"!==typeof t)return{default:t};var n=l(e);if(n&&n.has(t))return n.get(t);var i={},r=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in t)if("default"!==o&&Object.prototype.hasOwnProperty.call(t,o)){var c=r?Object.getOwnPropertyDescriptor(t,o):null;c&&(c.get||c.set)?Object.defineProperty(i,o,c):i[o]=t[o]}i.default=t,n&&n.set(t,i)}(n("5354")),n("9245"),n("292f")),f=n("03f5");function l(t){if("function"!==typeof WeakMap)return null;var e=new WeakMap,n=new WeakMap;return(l=function(t){return t?n:e})(t)}var p={name:"Login",mixins:[c.default],data:function(){return{navList:["快速登录","账号登录"],current:1,account:"",password:"",captcha:"",formItem:1,type:"login",logoUrl:"",keyCode:"",codeUrl:"",codeVal:"",isShowCode:!1,platform:"",appLoginStatus:!1,appUserInfo:null,appleLoginStatus:!1,appleUserInfo:null,appleShow:!1}},watch:{formItem:function(t,e){this.type=1==t?"login":"register"}},mounted:function(){this.getCode(),this.getLogoImage()},onLoad:function(){var e=this;t.getSystemInfo({success:function(t){"ios"==t.platform.toLowerCase()&&t.system.split(" ")[1]>=13&&(e.appleShow=!0)}})},methods:{appleLogin:function(){var e=this;this.account="",this.captcha="",t.showLoading({title:"登录中"}),t.login({provider:"apple",timeout:1e4,success:function(n){t.getUserInfo({provider:"apple",success:function(t){e.appleUserInfo=t.userInfo,e.appleLoginApi()},fail:function(){t.hideLoading(),t.showToast({title:"获取用户信息失败",icon:"none",duration:2e3})},complete:function(){t.hideLoading()}})},fail:function(e){t.hideLoading(),console.log(e)}})},appleLoginApi:function(){var e=this;(0,s.appleLogin)({openId:this.appleUserInfo.openId,email:void 0==this.appleUserInfo.email?"":this.appleUserInfo.email,identityToken:this.appleUserInfo.identityToken||""}).then((function(t){e.$store.commit("LOGIN",{token:t.data.token}),e.getUserInfo(t.data)})).catch((function(e){t.hideLoading(),t.showModal({title:"提示",content:"错误信息".concat(e),success:function(t){t.confirm?console.log("用户点击确定"):t.cancel&&console.log("用户点击取消")}})}))},wxLogin:function(){var e=this;this.account="",this.captcha="",t.showLoading({title:"登录中"}),t.login({provider:"weixin",success:function(n){t.getUserInfo({provider:"weixin",success:function(n){t.hideLoading(),e.appUserInfo=n.userInfo,e.appUserInfo.type="ios"===e.platform?"iosWx":"androidWx",e.wxLoginGo(e.appUserInfo)},fail:function(){t.hideLoading(),t.showToast({title:"获取用户信息失败",icon:"none",duration:2e3})},complete:function(){t.hideLoading()}})},fail:function(){t.hideLoading(),t.showToast({title:"登录失败",icon:"none",duration:2e3})}})},wxLoginGo:function(e){var n=this;(0,s.appAuth)(e).then((function(e){"register"===e.data.type&&t.navigateTo({url:"/pages/users/app_login/index?authKey="+e.data.key}),"login"===e.data.type&&(n.$store.commit("LOGIN",{token:e.data.token}),n.getUserInfo(e.data))})).catch((function(t){that.$util.Tips({title:t})}))},again:function(){this.codeUrl=f.VUE_APP_API_URL+"/sms_captcha?key="+this.keyCode+Date.parse(new Date)},getCode:function(){},getLogoImage:function(){var t=this;return(0,o.default)(r.default.mark((function e(){var n;return r.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:n=t,(0,s.getLogo)().then((function(t){n.logoUrl=t.data.logoUrl?t.data.logoUrl:"/static/images/logo2.png"}));case 2:case"end":return e.stop()}}),e)})))()},loginMobile:function(){var t=this;return(0,o.default)(r.default.mark((function e(){var n;return r.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=t,n.account){e.next=3;break}return e.abrupt("return",n.$util.Tips({title:"请填写手机号码"}));case 3:if(/^1(3|4|5|7|8|9|6)\d{9}$/i.test(n.account)){e.next=5;break}return e.abrupt("return",n.$util.Tips({title:"请输入正确的手机号码"}));case 5:if(n.captcha){e.next=7;break}return e.abrupt("return",n.$util.Tips({title:"请填写验证码"}));case 7:if(/^[\w\d]+$/i.test(n.captcha)){e.next=9;break}return e.abrupt("return",n.$util.Tips({title:"请输入正确的验证码"}));case 9:(0,u.loginMobile)({phone:n.account,captcha:n.captcha,spread_spid:n.$Cache.get("spread")}).then((function(e){var i=e.data;Math.round(new Date/1e3);t.$store.commit("LOGIN",{token:e.data.token}),n.getUserInfo(i)})).catch((function(t){n.$util.Tips({title:t})}));case 10:case"end":return e.stop()}}),e)})))()},register:function(){var t=this;return(0,o.default)(r.default.mark((function e(){var n;return r.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=t,n.account){e.next=3;break}return e.abrupt("return",n.$util.Tips({title:"请填写手机号码"}));case 3:if(/^1(3|4|5|7|8|9|6)\d{9}$/i.test(n.account)){e.next=5;break}return e.abrupt("return",n.$util.Tips({title:"请输入正确的手机号码"}));case 5:if(n.captcha){e.next=7;break}return e.abrupt("return",n.$util.Tips({title:"请填写验证码"}));case 7:if(/^[\w\d]+$/i.test(n.captcha)){e.next=9;break}return e.abrupt("return",n.$util.Tips({title:"请输入正确的验证码"}));case 9:if(n.password){e.next=11;break}return e.abrupt("return",n.$util.Tips({title:"请填写密码"}));case 11:if(/^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,16}$/i.test(n.password)){e.next=13;break}return e.abrupt("return",n.$util.Tips({title:"您输入的密码过于简单"}));case 13:(0,u.register)({account:n.account,captcha:n.captcha,password:n.password,spread:n.$Cache.get("spread")}).then((function(t){n.$util.Tips({title:t}),n.formItem=1})).catch((function(t){n.$util.Tips({title:t})}));case 14:case"end":return e.stop()}}),e)})))()},code:function(){var t=this;return(0,o.default)(r.default.mark((function e(){var n;return r.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=t,n.account){e.next=3;break}return e.abrupt("return",n.$util.Tips({title:"请填写手机号码"}));case 3:if(/^1(3|4|5|7|8|9|6)\d{9}$/i.test(n.account)){e.next=5;break}return e.abrupt("return",n.$util.Tips({title:"请输入正确的手机号码"}));case 5:return 2==n.formItem&&(n.type="register"),e.next=8,(0,u.registerVerify)(n.account).then((function(t){n.$util.Tips({title:t.message}),n.sendCode()})).catch((function(t){return n.$util.Tips({title:t})}));case 8:case"end":return e.stop()}}),e)})))()},navTap:function(t){this.current=t},submit:function(){var t=this;return(0,o.default)(r.default.mark((function e(){var n;return r.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=t,n.account){e.next=3;break}return e.abrupt("return",n.$util.Tips({title:"请填写账号"}));case 3:if(/^[\w\d]{5,16}$/i.test(n.account)){e.next=5;break}return e.abrupt("return",n.$util.Tips({title:"请输入正确的账号"}));case 5:if(n.password){e.next=7;break}return e.abrupt("return",n.$util.Tips({title:"请填写密码"}));case 7:(0,u.loginH5)({account:n.account,password:n.password,spread:n.$Cache.get("spread")}).then((function(e){var i=e.data;t.$store.commit("LOGIN",{token:i.token}),n.getUserInfo(i)})).catch((function(t){n.$util.Tips({title:t})}));case 8:case"end":return e.stop()}}),e)})))()},getUserInfo:function(e){var n=this;this.$store.commit("SETUID",e.uid),(0,u.getUserInfo)().then((function(e){n.$store.commit("UPDATE_USERINFO",e.data);var i=n.$Cache.get("login_back_url")||"/pages/index/index";-1!==i.indexOf("/pages/users/login/index")&&(i="/pages/index/index"),console.log(69999),console.log(i),t.reLaunch({url:i})}))}}};e.default=p}).call(this,n("df3c")["default"])},f79c:function(t,e,n){"use strict";var i=n("fed8"),a=n.n(i);a.a},fed8:function(t,e,n){}},[["2d1d","common/runtime","common/vendor"]]]);