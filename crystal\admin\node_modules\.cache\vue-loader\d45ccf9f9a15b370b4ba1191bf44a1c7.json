{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\userprocess-add-and-update.vue?vue&type=template&id=4d28cc6e", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\userprocess-add-and-update.vue", "mtime": 1753666157945}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"el-dialog\",\n    {\n      attrs: {\n        title: !_vm.dataForm.id ? \"新增\" : \"修改\",\n        \"close-on-click-modal\": false,\n        visible: _vm.visible,\n      },\n      on: {\n        \"update:visible\": function ($event) {\n          _vm.visible = $event\n        },\n      },\n    },\n    [\n      _c(\n        \"el-form\",\n        {\n          ref: \"dataForm\",\n          attrs: {\n            model: _vm.dataForm,\n            rules: _vm.dataRule,\n            \"label-width\": \"120px\",\n          },\n        },\n        [\n          _c(\n            \"el-form-item\",\n            { attrs: { label: \"记录名称\", prop: \"name\" } },\n            [\n              _c(\"el-input\", {\n                attrs: { placeholder: \"记录名称\" },\n                model: {\n                  value: _vm.dataForm.name,\n                  callback: function ($$v) {\n                    _vm.$set(_vm.dataForm, \"name\", $$v)\n                  },\n                  expression: \"dataForm.name\",\n                },\n              }),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-form-item\",\n            { attrs: { label: \"记录状态\", prop: \"userStatus\" } },\n            [\n              _c(\n                \"el-select\",\n                {\n                  attrs: { placeholder: \"记录状态\", filterable: \"\" },\n                  model: {\n                    value: _vm.dataForm.userStatus,\n                    callback: function ($$v) {\n                      _vm.$set(_vm.dataForm, \"userStatus\", $$v)\n                    },\n                    expression: \"dataForm.userStatus\",\n                  },\n                },\n                _vm._l(_vm.userStatus, function (item) {\n                  return _c(\"el-option\", {\n                    key: item,\n                    attrs: { label: item, value: item },\n                  })\n                }),\n                1\n              ),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-form-item\",\n            { attrs: { label: \"记录时间\", prop: \"doTime\" } },\n            [\n              _c(\"el-date-picker\", {\n                staticStyle: { width: \"100%\" },\n                attrs: {\n                  type: \"datetime\",\n                  placeholder: \"记录时间\",\n                  \"value-format\": \"yyyy/MM/dd HH:mm:ss\",\n                },\n                model: {\n                  value: _vm.dataForm.doTime,\n                  callback: function ($$v) {\n                    _vm.$set(_vm.dataForm, \"doTime\", $$v)\n                  },\n                  expression: \"dataForm.doTime\",\n                },\n              }),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-form-item\",\n            { attrs: { label: \"备注\", prop: \"remarks\" } },\n            [\n              _c(\"Tinymce\", {\n                model: {\n                  value: _vm.dataForm.remarks,\n                  callback: function ($$v) {\n                    _vm.$set(_vm.dataForm, \"remarks\", $$v)\n                  },\n                  expression: \"dataForm.remarks\",\n                },\n              }),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"span\",\n        {\n          staticClass: \"dialog-footer\",\n          attrs: { slot: \"footer\" },\n          slot: \"footer\",\n        },\n        [\n          _c(\n            \"el-button\",\n            {\n              on: {\n                click: function ($event) {\n                  _vm.visible = false\n                },\n              },\n            },\n            [_vm._v(\"取消\")]\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-button\",\n            {\n              attrs: { type: \"primary\" },\n              on: {\n                click: function ($event) {\n                  return _vm.dataFormSubmit()\n                },\n              },\n            },\n            [_vm._v(\"确定\")]\n          ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}