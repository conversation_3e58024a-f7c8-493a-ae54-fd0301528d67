{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\list\\edit.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\list\\edit.vue", "mtime": 1753666157940}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { groupListApi, levelListApi, tagList<PERSON>pi, userInfoApi, userUpdate<PERSON>pi } from '@/api/user'\nimport {Debounce} from '@/utils/validate'\nconst defaultObj = {\n // birthday: '',\n // cardId: '',\n  id : null,\n  mark: '',\n//  phone: '',\n // realName: '',\n  addres:'',\n  groupId: '',\n  level: '',\n  isPromoter: false,\n  status: false\n}\nexport default {\n  name: \"UserEdit\",\n  props:{\n    uid: {\n      type: Number,\n      default: null\n    }\n  },\n  data() {\n    return {\n      ruleForm: Object.assign({}, defaultObj),\n      groupData: [],\n      labelData: [],\n      labelLists: [],\n      levelList: [],\n      groupList: [],\n      rules: {}\n    }\n  },\n  mounted() {\n    if(this.uid) this.userInfo()\n    this.groupLists()\n    this.levelLists()\n    this.getTagList()\n  },\n  methods: {\n    // 详情\n    userInfo () {\n      userInfoApi({ id: this.uid}).then(async res => {\n        this.ruleForm = {\n         // birthday: res.birthday,\n         // cardId: res.cardId,\n          id : res.uid,\n          mark: res.mark,\n         // phone: res.phone,\n         // realName: res.realName,\n          status: res.status,\n          addres: res.addres,\n          groupId: Number(res.groupId) || '',\n          level: res.level || '',\n          isPromoter: res.isPromoter,\n          tagId: res.tagId || ''\n        }\n        this.labelData = res.tagId ? res.tagId.split(',').map(Number): []\n      })\n    },\n    // 分组列表\n    groupLists () {\n      groupListApi({ page: 1, limit: 9999}).then(async res => {\n        this.groupList = res.list\n      })\n    },\n    //标签列表\n    getTagList () {\n      tagListApi({ page: 1, limit: 9999}).then(res => {\n        this.labelLists = res.list\n      })\n    },\n    // 等级列表\n    levelLists () {\n      levelListApi().then(async res => {\n        this.levelList = res.list\n      })\n    },\n    submitForm:Debounce(function(formName) {\n      this.$refs[formName].validate((valid) => {\n        if (valid) {\n          this.ruleForm.tagId=this.labelData.join(',')\n          userUpdateApi({id: this.ruleForm.id},this.ruleForm).then(async res => {\n            this.$message.success('编辑成功')\n            this.$parent.$parent.visible = false\n            this.$parent.$parent.getList()\n          })\n        } else {\n          return false;\n        }\n      });\n    }),\n    resetForm(formName) {\n      this.$refs[formName].resetFields();\n      this.$emit('resetForm');\n    }\n  }\n}\n", null]}