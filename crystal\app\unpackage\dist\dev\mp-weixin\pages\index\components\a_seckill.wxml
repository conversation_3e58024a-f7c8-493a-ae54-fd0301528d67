<block wx:if="{{$root.g0}}"><view class="seckill data-v-7d45776e"><view class="title acea-row row-between-wrapper data-v-7d45776e"><view class="acea-row row-middle data-v-7d45776e"><view class="pictrue data-v-7d45776e"><image src="/static/images/seckillTitle.png" class="data-v-7d45776e"></image></view><view class="lines data-v-7d45776e"></view><view class="point data-v-7d45776e">{{point+" 场"}}</view><count-down vue-id="2b69f758-1" is-day="{{false}}" tip-text=" " day-text=" " hour-text=" : " minute-text=" : " second-text=" " datatime="{{datatime}}" is-col="{{true}}" bgColor="{{bgColor}}" class="data-v-7d45776e" bind:__l="__l"></count-down></view><navigator class="more acea-row row-center-wrapper data-v-7d45776e" url="/pages/activity/goods_seckill/index" hover-class="none">GO<text class="iconfont icon-xiangyou data-v-7d45776e"></text></navigator></view><view class="conter data-v-7d45776e"><scroll-view style="white-space:nowrap;vertical-align:middle;" scroll-x="true" show-scrollbar="false" class="data-v-7d45776e"><block wx:for="{{spikeList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['goDetail',['$0'],[[['spikeList','',index]]]]]]]}}" class="itemCon data-v-7d45776e" bindtap="__e"><view class="item data-v-7d45776e"><view class="pictrue data-v-7d45776e"><image src="{{item.image}}" class="data-v-7d45776e"></image></view><view class="name line1 data-v-7d45776e">{{item.title}}</view><view class="x_money line1 data-v-7d45776e">¥<text class="num data-v-7d45776e">{{item.price}}</text></view><view class="y_money line1 data-v-7d45776e">{{"¥"+item.otPrice}}</view></view></view></block></scroll-view></view></view></block>