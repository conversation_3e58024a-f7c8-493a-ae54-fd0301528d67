{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\marketing\\bargain\\bargainGoods\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\marketing\\bargain\\bargainGoods\\index.vue", "mtime": 1753666157888}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { bargainListApi,bargainDeleteApi,bargainStatusApi,exportBargainApi } from '@/api/marketing'\nimport { checkPermi, checkRole } from \"@/utils/permission\";\nexport default {\n  name: \"index\",\n  data() {\n    return {\n      tableFrom: {\n        page: 1,\n        limit: 20,\n        keywords: '',\n        status: null\n      },\n      listLoading: true,\n      tableData: {\n        data: [],\n        total: 0\n      },\n      afterData: []\n    }\n  },\n  mounted() {\n    // 源数据\n    var oldData = [\n      {\n        city_id: 1,\n        city_name: '北京',\n        city_img: \"http://dfknbdjknvkjsfnvlkjdn.png\",\n        city_country: \"中国\"\n      },\n      {\n        city_id: 2,\n        city_name: '上海',\n        city_img: \"http://wergerbe.png\",\n        city_country: \"中国\"\n      },\n      {\n        city_id: 3,\n        city_name: '广州',\n        city_img: \"http://hrthhr.png\",\n        city_country: \"中国\"\n      },\n      {\n        city_id: 4,\n        city_name: '西雅图',\n        city_img: \"http://frevfd.png\",\n        city_country: \"美国\"\n      },\n      {\n        city_id: 5,\n        city_name: '纽约',\n        city_img: \"http://反而个.png\",\n        city_country: \"美国\"\n      }\n    ]\n   // 把源数据先变成目标数据的规则\n    var oldDataRule = []\n    oldData.forEach(el => {\n      var oldObj = {\n        name: el.city_country,\n        citys:[]\n      }\n      var cityObj = {\n        city_name: el.city_name,\n        city_img: el.city_img,\n        city_id: el.city_id\n      }\n      oldObj.citys.push(cityObj)\n      oldDataRule.push(oldObj)\n    })\n    var newData = []\n    var newObj = {}\n    oldDataRule.forEach((el, i) => {\n      if (!newObj[el.name]) {\n        newData.push(el);\n        newObj[el.name] = true;\n      } else {\n        newData.forEach(el => {\n          if (el.name === oldDataRule[i].name) {\n            el.citys = el.citys.concat(oldDataRule[i].citys);\n            // el.citys = [...el.citys, ...oldDataRule[i].citys]; // es6语法\n          }\n        })\n      }\n    })\n    this.getList()\n  },\n  methods: {\n    checkPermi, //权限控制\n    //导出\n    exportList(){\n      exportBargainApi({keywords: this.tableFrom.keywords, status:this.tableFrom.status}).then((res) => {\n        window.open(res.fileName)\n      })\n    },\n    // 删除\n    handleDelete(id, idx) {\n      let that = this;\n      this.$modal.confirm('确认删除该商品吗').then(function() {\n        bargainDeleteApi({id: id}).then(() => {\n          that.$message.success('删除成功')\n          that.getList();\n        })\n      }).catch(() => {});\n    },\n    onchangeIsShow(row) {\n      bargainStatusApi({id:row.id, status: row.status})\n        .then(async () => {\n          this.$message.success('修改成功');\n          this.getList()\n        }).catch(()=>{\n        row.status = !row.status\n      })\n    },\n    // 列表\n    getList(num) {\n      this.listLoading = true\n      this.tableFrom.page = num ? num : this.tableFrom.page;\n      bargainListApi(this.tableFrom).then(res => {\n        this.tableData.data = res.list\n        this.tableData.total = res.total\n        this.listLoading = false\n      }).catch((res) => {\n        this.listLoading = false\n      })\n    },\n    pageChange(page) {\n      this.tableFrom.page = page\n      this.getList()\n    },\n    handleSizeChange(val) {\n      this.tableFrom.limit = val\n      this.getList()\n    },\n    add() {\n      this.isCreate = 0\n      this.dialogVisible = true\n    },\n    handleClose() {\n      this.dialogVisible = false\n      this.editData = {}\n    }\n  }\n}\n", null]}