{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\list\\level.vue?vue&type=template&id=06d28e26", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\list\\level.vue", "mtime": 1753666157941}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"el-form\",\n    {\n      ref: \"ruleForm\",\n      staticClass: \"demo-ruleForm\",\n      attrs: { model: _vm.ruleForm, \"label-width\": \"100px\" },\n    },\n    [\n      _c(\n        \"el-form-item\",\n        [\n          _c(\"el-alert\", {\n            attrs: {\n              title: \"请勿频繁更改，以免计算产生混乱！\",\n              type: \"warning\",\n            },\n          }),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"el-form-item\",\n        { attrs: { label: \"用户等级\", \"label-width\": \"100px\" } },\n        [\n          _c(\n            \"el-select\",\n            {\n              attrs: { clearable: \"\", placeholder: \"请选择\" },\n              on: { change: _vm.currentSel },\n              model: {\n                value: _vm.ruleForm.levelId,\n                callback: function ($$v) {\n                  _vm.$set(_vm.ruleForm, \"levelId\", $$v)\n                },\n                expression: \"ruleForm.levelId\",\n              },\n            },\n            _vm._l(_vm.levelList, function (item) {\n              return _c(\"el-option\", {\n                key: item.grade,\n                attrs: { label: item.name, value: item.id },\n              })\n            }),\n            1\n          ),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      (_vm.grade == \"\" ? false : _vm.grade < _vm.levelInfo.gradeLevel)\n        ? _c(\n            \"el-form-item\",\n            { attrs: { label: \"扣除经验\", \"label-width\": \"100px\" } },\n            [\n              _c(\"el-switch\", {\n                model: {\n                  value: _vm.ruleForm.isSub,\n                  callback: function ($$v) {\n                    _vm.$set(_vm.ruleForm, \"isSub\", $$v)\n                  },\n                  expression: \"ruleForm.isSub\",\n                },\n              }),\n            ],\n            1\n          )\n        : _vm._e(),\n      _vm._v(\" \"),\n      _c(\n        \"el-form-item\",\n        [\n          _c(\n            \"el-button\",\n            {\n              on: {\n                click: function ($event) {\n                  return _vm.resetForm(\"ruleForm\")\n                },\n              },\n            },\n            [_vm._v(\"取消\")]\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-button\",\n            {\n              attrs: { type: \"primary\" },\n              on: {\n                click: function ($event) {\n                  return _vm.submitForm(\"ruleForm\")\n                },\n              },\n            },\n            [_vm._v(\"确定\")]\n          ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}