<view class="_div data-v-4f2faf7c"><view class="flash-sale data-v-4f2faf7c"><view class="saleBox data-v-4f2faf7c"></view><block wx:if="{{$root.g0}}"><view class="header data-v-4f2faf7c"><swiper indicator-dots="true" autoplay="true" circular="{{circular}}" interval="3000" duration="1500" indicator-color="rgba(255,255,255,0.6)" indicator-active-color="#fff" class="data-v-4f2faf7c"><block wx:for="{{dataList[active].slide}}" wx:for-item="items" wx:for-index="index" wx:key="index"><block class="data-v-4f2faf7c"><swiper-item class="borRadius14 data-v-4f2faf7c"><image class="slide-image borRadius14 data-v-4f2faf7c" src="{{items.sattDir}}" lazy-load="{{true}}"></image></swiper-item></block></block></swiper></view></block><view class="seckillList acea-row row-between-wrapper data-v-4f2faf7c"><view class="priceTag data-v-4f2faf7c"><image src="/static/images/priceTag.png" class="data-v-4f2faf7c"></image></view><view class="timeLsit data-v-4f2faf7c"><scroll-view class="scroll-view_x data-v-4f2faf7c" style="width:auto;overflow:hidden;" scroll-x="{{true}}" scroll-with-animation="{{true}}" scroll-left="{{scrollLeft}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block class="data-v-4f2faf7c"><view data-event-opts="{{[['tap',[['settimeList',['$0',index],[[['dataList','',index]]]]]]]}}" class="{{['item','data-v-4f2faf7c',active==index?'on':'']}}" bindtap="__e"><view class="time data-v-4f2faf7c">{{item.g1[0]}}</view><view class="state data-v-4f2faf7c">{{item.$orig.statusName}}</view></view></block></block></scroll-view></view></view><block wx:if="{{$root.g2>0}}"><view class="list pad30 data-v-4f2faf7c"><block wx:for="{{seckillList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block class="data-v-4f2faf7c"><view data-event-opts="{{[['tap',[['goDetails',['$0'],[[['seckillList','',index]]]]]]]}}" class="item acea-row row-between-wrapper data-v-4f2faf7c" bindtap="__e"><view class="pictrue data-v-4f2faf7c"><image src="{{item.image}}" class="data-v-4f2faf7c"></image></view><view class="text acea-row row-column-around data-v-4f2faf7c"><view class="name line1 data-v-4f2faf7c">{{item.title}}</view><view class="money data-v-4f2faf7c"><text class="font-color data-v-4f2faf7c">￥</text><text class="num font-color data-v-4f2faf7c">{{item.price}}</text><text class="y_money data-v-4f2faf7c">{{"￥"+item.otPrice}}</text></view><view class="limit data-v-4f2faf7c">限量<text class="limitPrice data-v-4f2faf7c">{{item.quota+" "+item.unitName}}</text></view><view class="progress data-v-4f2faf7c"><view class="bg-reds data-v-4f2faf7c" style="{{('width:'+item.percent+'%;')}}"></view><view class="piece data-v-4f2faf7c">{{"已抢"+item.percent+"%"}}</view></view></view><block wx:if="{{status==2}}"><view class="grab bg-color data-v-4f2faf7c">马上抢</view></block><block wx:else><block wx:if="{{status==1}}"><view class="grab bg-color data-v-4f2faf7c">未开始</view></block><block wx:else><view class="grab bg-color-hui data-v-4f2faf7c">已结束</view></block></block></view></block></block></view></block></view><block wx:if="{{$root.g3}}"><view class="noCommodity data-v-4f2faf7c"><view class="pictrue data-v-4f2faf7c"><image src="../../../static/images/noShopper.png" class="data-v-4f2faf7c"></image></view></view></block><home vue-id="349d6104-1" class="data-v-4f2faf7c" bind:__l="__l"></home></view>