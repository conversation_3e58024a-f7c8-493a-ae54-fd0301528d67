{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/uni_modules/liu-rotating-menu/components/liu-rotating-menu/liu-rotating-menu.vue?0e15", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/uni_modules/liu-rotating-menu/components/liu-rotating-menu/liu-rotating-menu.vue?7d9b", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/uni_modules/liu-rotating-menu/components/liu-rotating-menu/liu-rotating-menu.vue?58f0", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/uni_modules/liu-rotating-menu/components/liu-rotating-menu/liu-rotating-menu.vue?0710", "uni-app:///uni_modules/liu-rotating-menu/components/liu-rotating-menu/liu-rotating-menu.vue", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/uni_modules/liu-rotating-menu/components/liu-rotating-menu/liu-rotating-menu.vue?fa89", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/uni_modules/liu-rotating-menu/components/liu-rotating-menu/liu-rotating-menu.vue?4716"], "names": ["props", "btnObj", "type", "default", "disabled", "bottomPx", "rightPx", "data", "left", "top", "isRemove", "windowWidth", "windowHeight", "btnWidth", "btnHeight", "x", "y", "old", "showBtn", "isLeft", "mounted", "methods", "getSysInfo", "onChange", "touchstart", "touchend", "clickBtn", "click"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0I;AAC1I;AACqE;AACL;AACqC;;;AAGrG;AACsM;AACtM,gBAAgB,2LAAU;AAC1B,EAAE,uFAAM;AACR,EAAE,wGAAM;AACR,EAAE,iHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA6xB,CAAgB,isBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCkBjzB;EACAA;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;EACA;EACAI;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAF;QACAC;MACA;MACAE;MACAC;IACA;EACA;EACAC;IAAA;IACA;MACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;UACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;QACA;QACA;QACA;UACA;YACA;YACA;UACA;QACA;UACA;YACA;YACA;UACA;QACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtHA;AAAA;AAAA;AAAA;AAA8oC,CAAgB,0+BAAG,EAAC,C;;;;;;;;;;;ACAlqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/liu-rotating-menu/components/liu-rotating-menu/liu-rotating-menu.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./liu-rotating-menu.vue?vue&type=template&id=cc32306a&scoped=true&\"\nvar renderjs\nimport script from \"./liu-rotating-menu.vue?vue&type=script&lang=js&\"\nexport * from \"./liu-rotating-menu.vue?vue&type=script&lang=js&\"\nimport style0 from \"./liu-rotating-menu.vue?vue&type=style&index=0&id=cc32306a&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"cc32306a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/liu-rotating-menu/components/liu-rotating-menu/liu-rotating-menu.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./liu-rotating-menu.vue?vue&type=template&id=cc32306a&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./liu-rotating-menu.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./liu-rotating-menu.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<movable-area class=\"movable-area\" :scale-area=\"false\">\r\n\t\t\t<movable-view class=\"movable-view\" :class=\"!isRemove?'animation-info':''\" style=\"pointer-events: auto;\"\r\n\t\t\t\t@touchstart=\"touchstart\" @touchend=\"touchend\" @change=\"onChange\" direction=\"all\" inertia=\"true\" :x=\"x\"\r\n\t\t\t\t:y=\"y\" :disabled=\"disabled\" :out-of-bounds=\"true\" :damping=\"200\" :friction=\"100\">\r\n\t\t\t\t<view @click=\"clickBtn\">{{btnObj.name}}</view>\r\n\t\t\t\t<view v-if=\"showBtn\" v-for=\"(item,index) in btnObj.childs\" :key=\"index\" @click=\"click(item)\"\r\n\t\t\t\t\tclass=\"item-main\"\r\n\t\t\t\t\t:class=\"!isLeft?'item-main'+(index+1)+' toOut'+(index+1):'item-main1'+(index+1)+' toOut1'+(index+1)\">\r\n\t\t\t\t\t{{item.name}}\r\n\t\t\t\t</view>\r\n\t\t\t</movable-view>\r\n\t\t</movable-area>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tprops: {\r\n\t\t\t//菜单数据源\r\n\t\t\tbtnObj: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault: {}\r\n\t\t\t},\r\n\t\t\t//是否禁用拖动\r\n\t\t\tdisabled: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t//按钮默认位置离底部距离（px）\r\n\t\t\tbottomPx: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 100\r\n\t\t\t},\r\n\t\t\t//按钮默认位置离右边距离（px）\r\n\t\t\trightPx: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tleft: 0,\r\n\t\t\t\ttop: 0,\r\n\t\t\t\tisRemove: true,\r\n\t\t\t\twindowWidth: 0,\r\n\t\t\t\twindowHeight: 0,\r\n\t\t\t\tbtnWidth: 0,\r\n\t\t\t\tbtnHeight: 0,\r\n\t\t\t\tx: 10000,\r\n\t\t\t\ty: 10000,\r\n\t\t\t\told: {\r\n\t\t\t\t\tx: 0,\r\n\t\t\t\t\ty: 0\r\n\t\t\t\t},\r\n\t\t\t\tshowBtn: false,\r\n\t\t\t\tisLeft: false,\r\n\t\t\t};\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tthis.$nextTick(res => {\r\n\t\t\t\tthis.getSysInfo()\r\n\t\t\t})\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetSysInfo() {\r\n\t\t\t\tlet sysInfo = uni.getSystemInfoSync()\r\n\t\t\t\tthis.windowWidth = sysInfo.windowWidth\r\n\t\t\t\tthis.windowHeight = sysInfo.windowHeight\r\n\t\t\t\tthis.createSelectorQuery().select('.movable-view').boundingClientRect(rect => {\r\n\t\t\t\t\tthis.btnWidth = rect.width\r\n\t\t\t\t\tthis.btnHeight = rect.height\r\n\t\t\t\t\tthis.x = this.old.x\r\n\t\t\t\t\tthis.y = this.old.y\r\n\t\t\t\t\tthis.$nextTick(res => {\r\n\t\t\t\t\t\tthis.x = this.windowWidth - this.btnWidth - this.rightPx\r\n\t\t\t\t\t\tthis.y = this.windowHeight - this.btnHeight - this.bottomPx\r\n\t\t\t\t\t})\r\n\t\t\t\t}).exec()\r\n\t\t\t},\r\n\t\t\t//移动按钮\r\n\t\t\tonChange(e) {\r\n\t\t\t\tthis.old.x = e.detail.x\r\n\t\t\t\tthis.old.y = e.detail.y\r\n\t\t\t},\r\n\t\t\t//开始移动\r\n\t\t\ttouchstart(e) {\r\n\t\t\t\tthis.isRemove = true\r\n\t\t\t},\r\n\t\t\t//结束移动\r\n\t\t\ttouchend(e) {\r\n\t\t\t\tif (!this.disabled && this.old.x) {\r\n\t\t\t\t\tthis.x = this.old.x\r\n\t\t\t\t\tthis.y = this.old.y\r\n\t\t\t\t\tlet bWidth = (this.windowWidth - this.btnWidth) / 2\r\n\t\t\t\t\tif (this.x < 0 || (this.x > 0 && this.x <= bWidth)) {\r\n\t\t\t\t\t\tthis.$nextTick(res => {\r\n\t\t\t\t\t\t\tthis.x = 0\r\n\t\t\t\t\t\t\tthis.isLeft = true\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.$nextTick(res => {\r\n\t\t\t\t\t\t\tthis.x = this.windowWidth - this.btnWidth\r\n\t\t\t\t\t\t\tthis.isLeft = false\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.isRemove = false\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tclickBtn() {\r\n\t\t\t\tthis.showBtn = !this.showBtn\r\n\t\t\t},\r\n\t\t\t//点击菜单\r\n\t\t\tclick(item) {\r\n\t\t\t\tthis.$emit('click', item)\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style scoped>\r\n\t.movable-view {\r\n\t\twidth: 120rpx;\r\n\t\theight: 120rpx;\r\n\t\tbackground: linear-gradient(120deg, #c9ab79, #cfb78d, #c9ab79);\r\n\t\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);\r\n\t\tborder-radius: 50%;\r\n\t\tcolor: #FFFFFF;\r\n\t\tfont-size: 26rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.item-main {\r\n\t\twidth: 100rpx;\r\n\t\theight: 100rpx;\r\n\t\tbackground: linear-gradient(120deg, #ad4448, #b3595c, #ad4448);\r\n\t\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);\r\n\t\tborder-radius: 50%;\r\n\t\tcolor: #FFFFFF;\r\n\t\tfont-size: 22rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tposition: absolute;\r\n\t}\r\n\r\n\t.item-main1 {\r\n\t\tleft: -20rpx;\r\n\t\ttop: -105rpx;\r\n\t}\r\n\r\n\t.item-main2 {\r\n\t\tleft: -105rpx;\r\n\t\ttop: -40rpx;\r\n\t}\r\n\r\n\t.item-main3 {\r\n\t\tleft: -90rpx;\r\n\t\ttop: 65rpx;\r\n\t}\r\n\r\n\t.toOut1 {\r\n\t\tanimation: toOut1 1s;\r\n\t}\r\n\r\n\t.toOut2 {\r\n\t\tanimation: toOut2 1s;\r\n\t}\r\n\r\n\t.toOut3 {\r\n\t\tanimation: toOut3 1s;\r\n\t}\r\n\r\n\t.item-main11 {\r\n\t\tright: -20rpx;\r\n\t\ttop: -105rpx;\r\n\t}\r\n\r\n\t.item-main12 {\r\n\t\tright: -105rpx;\r\n\t\ttop: -40rpx;\r\n\t}\r\n\r\n\t.item-main13 {\r\n\t\tright: -90rpx;\r\n\t\ttop: 65rpx;\r\n\t}\r\n\r\n\t.toOut11 {\r\n\t\tanimation: toOut11 1s;\r\n\t}\r\n\r\n\t.toOut12 {\r\n\t\tanimation: toOut12 1s;\r\n\t}\r\n\r\n\t.toOut13 {\r\n\t\tanimation: toOut13 1s;\r\n\t}\r\n\r\n\t@keyframes toOut1 {\r\n\t\t0% {\r\n\t\t\ttransform: scale(0);\r\n\t\t\ttransform-origin: bottom right;\r\n\t\t}\r\n\r\n\t\t100% {\r\n\t\t\ttransform: scale(1);\r\n\t\t\ttransform-origin: top left;\r\n\t\t}\r\n\t}\r\n\r\n\t@keyframes toOut2 {\r\n\t\t0% {\r\n\t\t\ttransform: scale(0);\r\n\t\t\ttransform-origin: center right;\r\n\t\t}\r\n\r\n\t\t100% {\r\n\t\t\ttransform: scale(1);\r\n\t\t\ttransform-origin: center left;\r\n\t\t}\r\n\t}\r\n\r\n\t@keyframes toOut3 {\r\n\t\t0% {\r\n\t\t\ttransform: scale(0);\r\n\t\t\ttransform-origin: top right;\r\n\t\t}\r\n\r\n\t\t100% {\r\n\t\t\ttransform: scale(1);\r\n\t\t\ttransform-origin: bottom left;\r\n\t\t}\r\n\t}\r\n\r\n\t@keyframes toOut11 {\r\n\t\t0% {\r\n\t\t\ttransform: scale(0);\r\n\t\t\ttransform-origin: bottom left;\r\n\t\t}\r\n\r\n\t\t100% {\r\n\t\t\ttransform: scale(1);\r\n\t\t\ttransform-origin: top right;\r\n\t\t}\r\n\t}\r\n\r\n\t@keyframes toOut12 {\r\n\t\t0% {\r\n\t\t\ttransform: scale(0);\r\n\t\t\ttransform-origin: center left;\r\n\t\t}\r\n\r\n\t\t100% {\r\n\t\t\ttransform: scale(1);\r\n\t\t\ttransform-origin: center right;\r\n\t\t}\r\n\t}\r\n\r\n\t@keyframes toOut13 {\r\n\t\t0% {\r\n\t\t\ttransform: scale(0);\r\n\t\t\ttransform-origin: top left;\r\n\t\t}\r\n\r\n\t\t100% {\r\n\t\t\ttransform: scale(1);\r\n\t\t\ttransform-origin: bottom right;\r\n\t\t}\r\n\t}\r\n\r\n\t.animation-info {\r\n\t\ttransition: left .25s ease;\r\n\t}\r\n\r\n\t.movable-area {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tz-index: 999999 !important;\r\n\t\tpointer-events: none;\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./liu-rotating-menu.vue?vue&type=style&index=0&id=cc32306a&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./liu-rotating-menu.vue?vue&type=style&index=0&id=cc32306a&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363903580\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}