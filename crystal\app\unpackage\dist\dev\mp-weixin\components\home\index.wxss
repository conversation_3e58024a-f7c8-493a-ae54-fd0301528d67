
.pictrueBox.data-v-08ae13c6 {
	width: 110rpx;
	height: 110rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}

/*返回主页按钮*/
.home.data-v-08ae13c6 {
	position: fixed;
	color: white;
	text-align: center;
	z-index: 9999;
	right: 20rpx;
	display: flex;
	align-items: center;
}
.home .homeCon.data-v-08ae13c6 {
	border-radius: 50rpx;
	opacity: 0;
	height: 0;
	width: 0;
	overflow: hidden;
	transition: all 0.3s ease-in-out;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
}
.home .homeCon.on.data-v-08ae13c6 {
	opacity: 1;
	width: 320rpx;
	height: 100rpx;
	margin-right: 20rpx;
	display: flex;
	justify-content: space-around;
	align-items: center;
	background: linear-gradient(135deg, #c9ab79, #e5c68f);
	border-radius: 50rpx;
}
.home .homeCon .nav-item.data-v-08ae13c6 {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 0 15rpx;
}
.home .homeCon .iconfont.data-v-08ae13c6 {
	font-size: 40rpx;
	color: #fff;
	margin-bottom: 6rpx;
}
.home .homeCon .nav-text.data-v-08ae13c6 {
	font-size: 20rpx;
	color: #fff;
}
.home .pictrue.data-v-08ae13c6 {
	width: 90rpx;
	height: 90rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #c9ab79, #e5c68f);
	display: flex;
	justify-content: center;
	align-items: center;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.2);
	transition: all 0.3s ease;
}
.home .pictrue.active.data-v-08ae13c6 {
	-webkit-transform: rotate(180deg);
	        transform: rotate(180deg);
	background: linear-gradient(135deg, #e5c68f, #c9ab79);
}
.home .pictrue .iconfont.data-v-08ae13c6 {
	font-size: 46rpx;
	color: #fff;
}

