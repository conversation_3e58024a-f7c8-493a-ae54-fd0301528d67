(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/recommend/index"],{"23b8":function(t,n,e){"use strict";e.r(n);var u=e("ac7e"),c=e.n(u);for(var a in u)["default"].indexOf(a)<0&&function(t){e.d(n,t,(function(){return u[t]}))}(a);n["default"]=c.a},"45a4":function(t,n,e){"use strict";var u=e("cf0e"),c=e.n(u);c.a},"9cde":function(t,n,e){"use strict";e.d(n,"b",(function(){return u})),e.d(n,"c",(function(){return c})),e.d(n,"a",(function(){}));var u=function(){var t=this.$createElement;this._self._c},c=[]},ac7e:function(t,n,e){"use strict";(function(t){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var u=e("8f59"),c=e("a014"),a={computed:(0,u.mapGetters)(["uid"]),props:{hostProduct:{type:Array,default:function(){return[]}}},data:function(){return{}},methods:{goDetail:function(n){(0,c.goShopDetail)(n,this.uid).then((function(e){t.navigateTo({url:"/pages/goods_details/index?id=".concat(n.id)})}))}}};n.default=a}).call(this,e("df3c")["default"])},cf0e:function(t,n,e){},e032:function(t,n,e){"use strict";e.r(n);var u=e("9cde"),c=e("23b8");for(var a in c)["default"].indexOf(a)<0&&function(t){e.d(n,t,(function(){return c[t]}))}(a);e("45a4");var i=e("828b"),o=Object(i["a"])(c["default"],u["b"],u["c"],!1,null,"df213c7c",null,!1,u["a"],void 0);n["default"]=o.exports}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/recommend/index-create-component',
    {
        'components/recommend/index-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("e032"))
        })
    },
    [['components/recommend/index-create-component']]
]);
