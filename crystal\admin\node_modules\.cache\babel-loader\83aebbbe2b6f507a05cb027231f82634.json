{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\taoBao.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\taoBao.vue", "mtime": 1753666157925}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\"use strict\";\n\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _store = require(\"@/api/store\");\nvar _systemGroup = require(\"@/api/systemGroup\");\nvar _index = _interopRequireDefault(require(\"@/components/Tinymce/index\"));\nvar _logistics = require(\"@/api/logistics\");\nvar _validate = require(\"@/utils/validate\");\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _regenerator() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */ var e, t, r = \"function\" == typeof Symbol ? Symbol : {}, n = r.iterator || \"@@iterator\", o = r.toStringTag || \"@@toStringTag\"; function i(r, n, o, i) { var c = n && n.prototype instanceof Generator ? n : Generator, u = Object.create(c.prototype); return _regeneratorDefine2(u, \"_invoke\", function (r, n, o) { var i, c, u, f = 0, p = o || [], y = !1, G = { p: 0, n: 0, v: e, a: d, f: d.bind(e, 4), d: function d(t, r) { return i = t, c = 0, u = e, G.n = r, a; } }; function d(r, n) { for (c = r, u = n, t = 0; !y && f && !o && t < p.length; t++) { var o, i = p[t], d = G.p, l = i[2]; r > 3 ? (o = l === n) && (u = i[(c = i[4]) ? 5 : (c = 3, 3)], i[4] = i[5] = e) : i[0] <= d && ((o = r < 2 && d < i[1]) ? (c = 0, G.v = n, G.n = i[1]) : d < l && (o = r < 3 || i[0] > n || n > l) && (i[4] = r, i[5] = n, G.n = l, c = 0)); } if (o || r > 1) return a; throw y = !0, n; } return function (o, p, l) { if (f > 1) throw TypeError(\"Generator is already running\"); for (y && 1 === p && d(p, l), c = p, u = l; (t = c < 2 ? e : u) || !y;) { i || (c ? c < 3 ? (c > 1 && (G.n = -1), d(c, u)) : G.n = u : G.v = u); try { if (f = 2, i) { if (c || (o = \"next\"), t = i[o]) { if (!(t = t.call(i, u))) throw TypeError(\"iterator result is not an object\"); if (!t.done) return t; u = t.value, c < 2 && (c = 0); } else 1 === c && (t = i.return) && t.call(i), c < 2 && (u = TypeError(\"The iterator does not provide a '\" + o + \"' method\"), c = 1); i = e; } else if ((t = (y = G.n < 0) ? u : r.call(n, G)) !== a) break; } catch (t) { i = e, c = 1, u = t; } finally { f = 1; } } return { value: t, done: y }; }; }(r, o, i), !0), u; } var a = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} t = Object.getPrototypeOf; var c = [][n] ? t(t([][n]())) : (_regeneratorDefine2(t = {}, n, function () { return this; }), t), u = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c); function f(e) { return Object.setPrototypeOf ? Object.setPrototypeOf(e, GeneratorFunctionPrototype) : (e.__proto__ = GeneratorFunctionPrototype, _regeneratorDefine2(e, o, \"GeneratorFunction\")), e.prototype = Object.create(u), e; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, _regeneratorDefine2(u, \"constructor\", GeneratorFunctionPrototype), _regeneratorDefine2(GeneratorFunctionPrototype, \"constructor\", GeneratorFunction), GeneratorFunction.displayName = \"GeneratorFunction\", _regeneratorDefine2(GeneratorFunctionPrototype, o, \"GeneratorFunction\"), _regeneratorDefine2(u), _regeneratorDefine2(u, o, \"Generator\"), _regeneratorDefine2(u, n, function () { return this; }), _regeneratorDefine2(u, \"toString\", function () { return \"[object Generator]\"; }), (_regenerator = function _regenerator() { return { w: i, m: f }; })(); }\nfunction _regeneratorDefine2(e, r, n, t) { var i = Object.defineProperty; try { i({}, \"\", {}); } catch (e) { i = 0; } _regeneratorDefine2 = function _regeneratorDefine(e, r, n, t) { function o(r, n) { _regeneratorDefine2(e, r, function (e) { return this._invoke(r, n, e); }); } r ? i ? i(e, r, { value: n, enumerable: !t, configurable: !t, writable: !t }) : e[r] = n : (o(\"next\", 0), o(\"throw\", 1), o(\"return\", 2)); }, _regeneratorDefine2(e, r, n, t); }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _createForOfIteratorHelper(r, e) { var t = \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; } //\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar defaultObj = [{\n  image: '',\n  price: null,\n  cost: null,\n  otPrice: null,\n  stock: null,\n  barCode: '',\n  weight: 0,\n  volume: 0\n}];\nvar objTitle = {\n  price: {\n    title: '售价'\n  },\n  cost: {\n    title: '成本价'\n  },\n  otPrice: {\n    title: '原价'\n  },\n  stock: {\n    title: '库存'\n  },\n  barCode: {\n    title: '商品编号'\n  },\n  weight: {\n    title: '重量（KG）'\n  },\n  volume: {\n    title: '体积(m³)'\n  }\n};\nvar _default = exports.default = {\n  name: 'taoBao',\n  components: {\n    Tinymce: _index.default\n  },\n  data: function data() {\n    return {\n      loading: false,\n      formThead: Object.assign({}, objTitle),\n      manyTabTit: {},\n      manyTabDate: {},\n      formValidate: null,\n      form: 1,\n      props2: {\n        children: 'child',\n        label: 'name',\n        value: 'id',\n        multiple: true,\n        emitPath: false\n      },\n      checkboxGroup: [],\n      //\n      recommend: [],\n      modal_loading: false,\n      ManyAttrValue: [Object.assign({}, defaultObj[0])],\n      // 多规格\n      imgList: [],\n      tempData: {\n        page: 1,\n        limit: 9999\n      },\n      shippingList: [],\n      merCateList: [],\n      images: '',\n      url: '',\n      modalPic: false,\n      isChoice: '',\n      isDisabled: false,\n      ruleInline: {\n        storeName: [{\n          required: true,\n          message: '请输入商品名称',\n          trigger: 'blur'\n        }],\n        cateIds: [{\n          required: true,\n          message: '请选择商品分类',\n          trigger: 'change',\n          type: 'array',\n          min: '1'\n        }],\n        unitName: [{\n          required: true,\n          message: '请输入单位',\n          trigger: 'blur'\n        }],\n        tempId: [{\n          required: true,\n          message: '请选择运费模板',\n          trigger: 'change',\n          type: 'number'\n        }],\n        keyword: [{\n          required: true,\n          message: '请输入商品关键字',\n          trigger: 'blur'\n        }],\n        attrValue: [{\n          required: true,\n          message: '请上传商品轮播图',\n          type: 'array',\n          trigger: 'change'\n        }]\n      },\n      grid: {\n        xl: 12,\n        lg: 12,\n        md: 12,\n        sm: 24,\n        xs: 24\n      },\n      copyConfig: {}\n    };\n  },\n  created: function created() {\n    this.goodsCategory();\n  },\n  computed: {\n    attrValue: function attrValue() {\n      var obj = Object.assign({}, defaultObj[0]);\n      delete obj.image;\n      return obj;\n    },\n    oneFormBatch: function oneFormBatch() {\n      var obj = [Object.assign({}, defaultObj[0])];\n      delete obj[0].barCode;\n      return obj;\n    }\n  },\n  watch: {\n    'formValidate.attr': {\n      handler: function handler(val) {\n        this.watCh(val);\n      },\n      immediate: false,\n      deep: true\n    }\n  },\n  mounted: function mounted() {\n    this.productGetTemplate();\n    this.getCopyConfig();\n    this.getGoodsType();\n  },\n  methods: {\n    // 删除表格中的属性\n    delAttrTable: function delAttrTable(index) {\n      this.formValidate.attrValue.splice(index, 1);\n    },\n    getCopyConfig: function getCopyConfig() {\n      var _this2 = this;\n      (0, _store.copyConfigApi)().then(function (res) {\n        _this2.copyConfig = res;\n      });\n    },\n    onChangeGroup: function onChangeGroup() {\n      this.checkboxGroup.includes('isGood') ? this.formValidate.isGood = true : this.formValidate.isGood = false;\n      this.checkboxGroup.includes('isBenefit') ? this.formValidate.isBenefit = true : this.formValidate.isBenefit = false;\n      this.checkboxGroup.includes('isBest') ? this.formValidate.isBest = true : this.formValidate.isBest = false;\n      this.checkboxGroup.includes('isNew') ? this.formValidate.isNew = true : this.formValidate.isNew = false;\n      this.checkboxGroup.includes('isHot') ? this.formValidate.isHot = true : this.formValidate.isHot = false;\n    },\n    // 批量添加\n    batchAdd: function batchAdd() {\n      // if (!this.oneFormBatch[0].pic || !this.oneFormBatch[0].price || !this.oneFormBatch[0].cost || !this.oneFormBatch[0].ot_price ||\n      //     !this.oneFormBatch[0].stock || !this.oneFormBatch[0].bar_code) return this.$Message.warning('请填写完整的批量设置内容！');\n      var _iterator = _createForOfIteratorHelper(this.formValidate.attrValue),\n        _step;\n      try {\n        for (_iterator.s(); !(_step = _iterator.n()).done;) {\n          var val = _step.value;\n          this.$set(val, 'image', this.oneFormBatch[0].image);\n          this.$set(val, 'price', this.oneFormBatch[0].price);\n          this.$set(val, 'cost', this.oneFormBatch[0].cost);\n          this.$set(val, 'otPrice', this.oneFormBatch[0].otPrice);\n          this.$set(val, 'stock', this.oneFormBatch[0].stock);\n          this.$set(val, 'barCode', this.oneFormBatch[0].barCode);\n          this.$set(val, 'weight', this.oneFormBatch[0].weight);\n          this.$set(val, 'volume', this.oneFormBatch[0].volume);\n        }\n      } catch (err) {\n        _iterator.e(err);\n      } finally {\n        _iterator.f();\n      }\n    },\n    watCh: function watCh(val) {\n      var tmp = {};\n      var tmpTab = {};\n      this.formValidate.attr.forEach(function (o, i) {\n        // tmp['value' + i] = {title: o.attrName}\n        // tmpTab['value' + i] = ''\n        tmp[o.attrName] = {\n          title: o.attrName\n        };\n        tmpTab[o.attrName] = '';\n      });\n      this.formValidate.attrValue = this.attrFormat(val);\n      this.manyTabTit = tmp;\n      this.manyTabDate = tmpTab;\n      this.formThead = Object.assign({}, this.formThead, tmp);\n    },\n    attrFormat: function attrFormat(arr) {\n      var data = [];\n      var res = [];\n      return format(arr);\n      function format(arr) {\n        if (arr.length > 1) {\n          arr.forEach(function (v, i) {\n            if (i === 0) data = arr[i]['attrValue'];\n            var tmp = [];\n            data.forEach(function (vv) {\n              arr[i + 1] && arr[i + 1]['attrValue'] && arr[i + 1]['attrValue'].forEach(function (g) {\n                var rep2 = (i !== 0 ? '' : arr[i]['attrName'] + '_') + vv + '$&' + arr[i + 1]['attrName'] + '_' + g;\n                tmp.push(rep2);\n                if (i === arr.length - 2) {\n                  var rep4 = {\n                    image: '',\n                    price: 0,\n                    cost: 0,\n                    otPrice: 0,\n                    stock: 0,\n                    barCode: '',\n                    weight: 0,\n                    volume: 0,\n                    brokerage: 0,\n                    brokerage_two: 0\n                  };\n                  rep2.split('$&').forEach(function (h, k) {\n                    var rep3 = h.split('_');\n                    if (!rep4['attrValue']) rep4['attrValue'] = {};\n                    rep4['attrValue'][rep3[0]] = rep3.length > 1 ? rep3[1] : '';\n                  });\n                  // Object.values(rep4.attrValue).forEach((v, i) => {\n                  //   rep4['value' + i] = v\n                  // })\n                  for (var attrValueKey in rep4.attrValue) {\n                    rep4[attrValueKey] = rep4.attrValue[attrValueKey];\n                  }\n                  res.push(rep4);\n                }\n              });\n            });\n            data = tmp.length ? tmp : [];\n          });\n        } else {\n          var dataArr = [];\n          arr.forEach(function (v, k) {\n            v['attrValue'].forEach(function (vv, kk) {\n              dataArr[kk] = v['attrName'] + '_' + vv;\n              res[kk] = {\n                image: '',\n                price: 0,\n                cost: 0,\n                otPrice: 0,\n                stock: 0,\n                barCode: '',\n                weight: 0,\n                volume: 0,\n                brokerage: 0,\n                brokerage_two: 0,\n                attrValue: _defineProperty({}, v['attrName'], vv)\n              };\n              for (var attrValueKey in res[kk].attrValue) {\n                res[kk][attrValueKey] = res[kk].attrValue[attrValueKey];\n              }\n            });\n          });\n          data.push(dataArr.join('$&'));\n        }\n        return res;\n      }\n    },\n    // 获取运费模板；\n    productGetTemplate: function productGetTemplate() {\n      var _this3 = this;\n      (0, _logistics.shippingTemplatesList)(this.tempData).then(function (res) {\n        _this3.shippingList = res.list;\n      });\n    },\n    // 删除图片\n    handleRemove: function handleRemove(i) {\n      this.formValidate.sliderImages.splice(i, 1);\n      this.$forceUpdate();\n    },\n    // 选择主图\n    checked: function checked(item, index) {\n      this.formValidate.image = item;\n    },\n    // 商品分类；\n    goodsCategory: function goodsCategory() {\n      var _this4 = this;\n      (0, _store.categoryApi)({\n        status: -1,\n        type: 1\n      }).then(function (res) {\n        _this4.merCateList = res;\n      });\n    },\n    // 生成表单\n    add: function add() {\n      var _this5 = this;\n      if (this.url) {\n        // var reg = /(http|ftp|https):\\/\\/[\\w\\-_]+(\\.[\\w\\-_]+)+([\\w\\-\\.,@?^=%&:/~\\+#]*[\\w\\-\\@?^=%&/~\\+#])?/;\n        // if (!reg.test(this.soure_link)) {\n        //   return this.$message.warning('请输入以http开头的地址！');\n        // }\n        this.loading = true;\n        this.copyConfig.copyType == 1 ? (0, _store.copyProductApi)({\n          url: this.url\n        }).then(function (res) {\n          var info = res.info;\n          _this5.formValidate = {\n            image: _this5.$selfUtil.setDomain(info.image),\n            sliderImage: info.sliderImage,\n            storeName: info.storeName,\n            storeInfo: info.storeInfo,\n            keyword: info.keyword,\n            cateIds: info.cateId ? info.cateId.split(',') : [],\n            // 商品分类id\n            cateId: info.cateId,\n            // 商品分类id传值\n            unitName: info.unitName,\n            sort: 0,\n            isShow: 0,\n            isBenefit: 0,\n            isNew: 0,\n            isGood: 0,\n            isHot: 0,\n            isBest: 0,\n            tempId: info.tempId,\n            attrValue: info.attrValue,\n            attr: info.attr || [],\n            selectRule: info.selectRule,\n            isSub: false,\n            content: _this5.$selfUtil.replaceImgSrcHttps(info.content),\n            specType: info.attr.length ? true : false,\n            id: info.id,\n            giveIntegral: info.giveIntegral,\n            ficti: info.ficti\n          };\n          if (info.isHot) _this5.checkboxGroup.push('isHot');\n          if (info.isGood) _this5.checkboxGroup.push('isGood');\n          if (info.isBenefit) _this5.checkboxGroup.push('isBenefit');\n          if (info.isBest) _this5.checkboxGroup.push('isBest');\n          if (info.isNew) _this5.checkboxGroup.push('isNew');\n          var imgs = JSON.parse(info.sliderImage);\n          var imgss = [];\n          Object.keys(imgs).map(function (i) {\n            imgss.push(_this5.$selfUtil.setDomain(imgs[i]));\n          });\n          _this5.formValidate.sliderImages = imgss;\n          if (_this5.formValidate.attr.length) {\n            _this5.oneFormBatch[0].image = _this5.$selfUtil.setDomain(info.image);\n            for (var i = 0; i < _this5.formValidate.attr.length; i++) {\n              _this5.formValidate.attr[i].attrValue = JSON.parse(_this5.formValidate.attr[i].attrValues);\n            }\n          }\n          _this5.loading = false;\n        }).catch(function () {\n          _this5.loading = false;\n        }) : (0, _store.importProductApi)({\n          url: this.url,\n          form: this.form\n        }).then(function (res) {\n          _this5.formValidate = {\n            image: _this5.$selfUtil.setDomain(res.image),\n            sliderImage: res.sliderImage,\n            storeName: res.storeName,\n            storeInfo: res.storeInfo,\n            keyword: res.keyword,\n            cateIds: res.cateId ? res.cateId.split(',') : [],\n            // 商品分类id\n            cateId: res.cateId,\n            // 商品分类id传值\n            unitName: res.unitName,\n            sort: 0,\n            isShow: 0,\n            isBenefit: 0,\n            isNew: 0,\n            isGood: 0,\n            isHot: 0,\n            isBest: 0,\n            tempId: res.tempId,\n            attrValue: res.attrValue,\n            attr: res.attr || [],\n            selectRule: res.selectRule,\n            isSub: false,\n            content: res.content,\n            specType: res.attr.length ? true : false,\n            id: res.id,\n            giveIntegral: res.giveIntegral,\n            ficti: res.ficti\n          };\n          var imgs = JSON.parse(res.sliderImage);\n          var imgss = [];\n          Object.keys(imgs).map(function (i) {\n            imgss.push(_this5.$selfUtil.setDomain(imgs[i]));\n          });\n          _this5.formValidate.sliderImages = imgss;\n          if (_this5.formValidate.attr.length) {\n            _this5.oneFormBatch[0].image = _this5.$selfUtil.setDomain(res.image);\n            for (var i = 0; i < _this5.formValidate.attr.length; i++) {\n              _this5.formValidate.attr[i].attrValue = JSON.parse(_this5.formValidate.attr[i].attrValues);\n            }\n          }\n          _this5.loading = false;\n        }).catch(function () {\n          _this5.loading = false;\n        });\n      } else {\n        this.$message.warning('请输入链接地址！');\n      }\n    },\n    // 提交\n    handleSubmit: (0, _validate.Debounce)(function (name) {\n      var _this6 = this;\n      var pram = JSON.parse(JSON.stringify(this.formValidate));\n      // this.formValidate.attr.length ? this.formValidate.attrValue = this.ManyAttrValue : this.formValidate.attrValue = []\n      pram.attr.forEach(function (item) {\n        item.attrValues = item.attrValue.join(\",\");\n      });\n      pram.cateId = pram.cateIds.join(',');\n      pram.sliderImage = JSON.stringify(pram.sliderImages);\n      pram.attrValue.forEach(function (itemData) {\n        itemData.attrValue = JSON.stringify(itemData.attrValue);\n      });\n      this.$refs[name].validate(function (valid) {\n        if (valid) {\n          _this6.modal_loading = true;\n          (0, _store.productCreateApi)(pram).then(/*#__PURE__*/function () {\n            var _ref = _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee(res) {\n              return _regenerator().w(function (_context) {\n                while (1) switch (_context.n) {\n                  case 0:\n                    _this6.$message.success('新增成功');\n                    _this6.$emit('handleCloseMod', false);\n                    _this6.modal_loading = false;\n                  case 1:\n                    return _context.a(2);\n                }\n              }, _callee);\n            }));\n            return function (_x) {\n              return _ref.apply(this, arguments);\n            };\n          }()).catch(function () {\n            _this6.modal_loading = false;\n          });\n        } else {\n          if (!pram.storeName || !pram.cateId || !pram.keyword || !pram.unitName || !pram.image) {\n            _this6.$message.warning(\"请填写完整商品信息！\");\n          }\n        }\n      });\n    }),\n    // 点击商品图\n    modalPicTap: function modalPicTap(tit, num, i) {\n      var _this = this;\n      this.$modalUpload(function (img) {\n        if (tit === '1' && !num) {\n          _this.formValidate.image = img[0].sattDir;\n          _this.OneattrValue[0].image = img[0].sattDir;\n        }\n        if (tit === '2' && !num) {\n          if (img.length > 10) return this.$message.warning(\"最多选择10张图片！\");\n          if (img.length + _this.formValidate.sliderImages.length > 10) return this.$message.warning(\"最多选择10张图片！\");\n          img.map(function (item) {\n            _this.formValidate.sliderImages.push(item.sattDir);\n          });\n        }\n        if (tit === '1' && num === 'dan') {\n          _this.OneattrValue[0].image = img[0].sattDir;\n        }\n        if (tit === '1' && num === 'duo') {\n          _this.formValidate.attrValue[i].image = img[0].sattDir;\n        }\n        if (tit === '1' && num === 'pi') {\n          _this.oneFormBatch[0].image = img[0].sattDir;\n        }\n      }, tit, 'store');\n    },\n    handleDragStart: function handleDragStart(e, item) {\n      this.dragging = item;\n    },\n    handleDragEnd: function handleDragEnd(e, item) {\n      this.dragging = null;\n    },\n    // 首先把div变成可以放置的元素，即重写dragenter/dragover\n    handleDragOver: function handleDragOver(e) {\n      // e.dataTransfer.dropEffect=\"move\";//在dragenter中针对放置目标来设置!\n      e.dataTransfer.dropEffect = 'move';\n    },\n    handleDragEnter: function handleDragEnter(e, item) {\n      // 为需要移动的元素设置dragstart事件\n      e.dataTransfer.effectAllowed = 'move';\n      if (item === this.dragging) {\n        return;\n      }\n      var newItems = _toConsumableArray(this.formValidate.slider_image);\n      var src = newItems.indexOf(this.dragging);\n      var dst = newItems.indexOf(item);\n      newItems.splice.apply(newItems, [dst, 0].concat(_toConsumableArray(newItems.splice(src, 1))));\n      this.formValidate.slider_image = newItems;\n    },\n    getGoodsType: function getGoodsType() {\n      var _this7 = this;\n      /** 让商品推荐列表的name属性与页面设置tab的name匹配**/\n      (0, _systemGroup.goodDesignList)({\n        gid: 70\n      }).then(function (response) {\n        var list = response.list;\n        var arr = [];\n        var arr1 = [];\n        var listArr = [{\n          name: '是否热卖',\n          value: 'isHot'\n        }];\n        var typeLists = [{\n          name: '',\n          value: 'isGood',\n          type: '2'\n        },\n        //精品推荐1  热门榜单2 首发新品3 促销单品4 \n        {\n          name: '',\n          value: 'isBenefit',\n          type: '4'\n        }, {\n          name: '',\n          value: 'isBest',\n          type: '1'\n        }, {\n          name: '',\n          value: 'isNew',\n          type: '3'\n        }];\n        list.forEach(function (item) {\n          var obj = {};\n          obj.value = JSON.parse(item.value);\n          obj.id = item.id;\n          obj.gid = item.gid;\n          obj.status = item.status;\n          arr.push(obj);\n        });\n        arr.forEach(function (item1) {\n          var obj1 = {};\n          obj1.name = item1.value.fields[1].value;\n          obj1.status = item1.status;\n          obj1.type = item1.value.fields[3].value;\n          arr1.push(obj1);\n        });\n        typeLists.forEach(function (item) {\n          arr1.forEach(function (item1) {\n            if (item.type == item1.type) {\n              listArr.push({\n                name: item1.name,\n                value: item.value,\n                type: item.type\n              });\n            }\n          });\n        });\n        _this7.recommend = listArr;\n      });\n    }\n  }\n};", null]}