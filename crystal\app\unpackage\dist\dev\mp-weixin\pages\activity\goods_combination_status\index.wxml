<view class="group-con _div data-v-35f14445"><view class="header acea-row row-between-wrapper _div data-v-35f14445"><view class="pictrue _div data-v-35f14445"><image src="{{storeCombination.image}}" class="_img data-v-35f14445"></image></view><view class="text _div data-v-35f14445"><view class="line1 _div data-v-35f14445">{{storeCombination.title}}</view><view class="money _div data-v-35f14445">￥<label class="num _span data-v-35f14445">{{storeCombination.price||0}}</label><label class="team cart-color _span data-v-35f14445">{{storeCombination.people+'人拼'}}</label></view></view><block wx:if="{{pinkBool===-1}}"><view class="iconfont icon-pintuanshibai _div data-v-35f14445"></view></block><block wx:else><block wx:if="{{pinkBool===1}}"><view class="iconfont icon-pintuanchenggong font-color-red _div data-v-35f14445"></view></block></block></view><view class="wrapper _div data-v-35f14445"><block wx:if="{{pinkBool===0}}"><view class="title acea-row row-center-wrapper _div data-v-35f14445"><view class="line _div data-v-35f14445"></view><view class="name acea-row row-center-wrapper _div data-v-35f14445">剩余<count-down vue-id="ef90753e-1" bgColor="{{bgColor}}" is-day="{{false}}" tip-text=" " day-text=" " hour-text=" : " minute-text=" : " second-text=" " datatime="{{pinkT.stopTime/1000}}" class="data-v-35f14445" bind:__l="__l"></count-down><label class="end _span data-v-35f14445">结束</label></view><view class="line _div data-v-35f14445"></view></view></block><block wx:if="{{pinkBool===1}}"><view class="tips font-color-red _div data-v-35f14445">恭喜您拼团成功</view></block><block wx:else><block wx:if="{{pinkBool===-1}}"><view class="tips _div data-v-35f14445">{{"还差"+count+"人，拼团失败"}}</view></block><block wx:else><block wx:if="{{pinkBool===0}}"><view class="tips font-color-red _div data-v-35f14445">{{"拼团中，还差"+count+"人拼团成功"}}</view></block></block></block><view class="{{['list','acea-row','row-middle','_div','data-v-35f14445',pinkBool===1||pinkBool===-1?'result':'',iShidden?'on':'']}}"><view class="pinkT _div data-v-35f14445"><view class="pictrue _div data-v-35f14445"><image src="{{pinkT.avatar}}" class="_img data-v-35f14445"></image></view><view class="chief _div data-v-35f14445">团长</view></view><block wx:if="{{$root.g0>0}}"><block class="data-v-35f14445"><block wx:for="{{pinkAll}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="pictrue _div data-v-35f14445"><image src="{{item.avatar}}" class="_img data-v-35f14445"></image></view></block></block></block><block wx:for="{{count}}" wx:for-item="index" wx:for-index="__i0__" wx:key="*this"><view class="pictrue _div data-v-35f14445"><image class="img-none _img data-v-35f14445" src="/static/images/vacancy.png"></image></view></block></view><block wx:if="{{(pinkBool===1||pinkBool===-1)&&count>9}}"><view data-event-opts="{{[['tap',[['lookAll',['$event']]]]]}}" class="lookAll acea-row row-center-wrapper _div data-v-35f14445" bindtap="__e">{{''+(iShidden?'收起':'查看全部')+''}}<label class="{{['iconfont','_span','data-v-35f14445',iShidden?'icon-xiangshang':'icon-xiangxia']}}"></label></view></block><block wx:if="{{userBool===1&&isOk==0&&pinkBool===0&&pinkT.stopTime>timestamp}}"><button class="teamBnt bg-color-red data-v-35f14445" open-type="share">邀请好友参团</button></block><block wx:if="{{pinkT.stopTime<timestamp&&isOk==0&&pinkBool===0}}"><view class="teamBnt bg-color-hui _div data-v-35f14445">拼团已过期</view></block><block wx:else><block wx:if="{{userBool===0&&pinkBool===0&&count>0&&pinkT.stopTime>timestamp}}"><view data-event-opts="{{[['tap',[['pay',['$event']]]]]}}" class="teamBnt bg-color-red _div data-v-35f14445" bindtap="__e">我要参团</view></block></block><block wx:if="{{pinkBool===1||pinkBool===-1}}"><view data-event-opts="{{[['tap',[['goDetail',['$0'],['storeCombination.id']]]]]}}" class="teamBnt bg-color-red _div data-v-35f14445" bindtap="__e">再次开团</view></block><block wx:if="{{pinkBool===0&&userBool===1}}"><view data-event-opts="{{[['tap',[['getCombinationRemove',['$event']]]]]}}" class="cancel _div data-v-35f14445" bindtap="__e"><label class="iconfont icon-guanbi3 _span data-v-35f14445"></label>取消开团</view></block><block wx:if="{{pinkBool===1}}"><view data-event-opts="{{[['tap',[['goOrder',['$event']]]]]}}" class="lookOrder _div data-v-35f14445" bindtap="__e">查看订单信息<label class="iconfont icon-xiangyou _span data-v-35f14445"></label></view></block></view><view class="group-recommend _div data-v-35f14445"><view class="title acea-row row-between-wrapper _div data-v-35f14445"><view class="_div data-v-35f14445">大家都在拼</view><view data-event-opts="{{[['tap',[['goList',['$event']]]]]}}" class="more _div data-v-35f14445" bindtap="__e">更多拼团<label class="iconfont icon-jiantou _span data-v-35f14445"></label></view></view><view class="list acea-row row-middle _div data-v-35f14445"><block wx:for="{{storeCombinationHost}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['goDetail',['$0'],[[['storeCombinationHost','',index,'id']]]]]]]}}" class="item _div data-v-35f14445" bindtap="__e"><view class="pictrue _div data-v-35f14445"><image src="{{item.image}}" class="_img data-v-35f14445"></image><view class="team _div data-v-35f14445">{{item.people+'人团'}}</view></view><view class="name line1 _div data-v-35f14445">{{item.title}}</view><view class="money font-color-red _div data-v-35f14445">{{'￥'+item.price}}</view></view></block></view></view><product-window vue-id="ef90753e-2" attr="{{attr}}" limitNum="{{1}}" iSbnt="{{1}}" data-event-opts="{{[['^myevent',[['onMyEvent']]],['^ChangeAttr',[['ChangeAttr']]],['^ChangeCartNum',[['ChangeCartNum']]],['^iptCartNum',[['iptCartNum']]],['^attrVal',[['attrVal']]],['^goCat',[['goPay']]]]}}" bind:myevent="__e" bind:ChangeAttr="__e" bind:ChangeCartNum="__e" bind:iptCartNum="__e" bind:attrVal="__e" bind:goCat="__e" class="data-v-35f14445" bind:__l="__l"></product-window><block wx:if="{{posters||canvasStatus}}"><view data-event-opts="{{[['tap',[['listenerActionClose',['$event']]]]]}}" class="mask data-v-35f14445" bindtap="__e"></view></block><block wx:if="{{H5ShareBox}}"><view class="share-box data-v-35f14445"><image src="/static/images/share-info.png" data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" bindtap="__e" class="data-v-35f14445"></image></view></block><block wx:if="{{canvasStatus}}"><view class="poster-pop data-v-35f14445"><image src="{{imagePath}}" class="data-v-35f14445"></image><view data-event-opts="{{[['tap',[['savePosterPath',['$event']]]]]}}" class="save-poster data-v-35f14445" bindtap="__e">保存到手机</view></view></block><view class="canvas data-v-35f14445"><canvas style="width:597px;height:850px;" canvas-id="activityCanvas" class="data-v-35f14445"></canvas><canvas style="{{'opacity:0;'+('width:'+(qrcodeSize+'px')+';')+('height:'+(qrcodeSize+'px')+';')}}" canvas-id="qrcode" class="data-v-35f14445"></canvas></view><home vue-id="ef90753e-3" class="data-v-35f14445" bind:__l="__l"></home></view>