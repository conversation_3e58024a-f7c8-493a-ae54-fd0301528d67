{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\mailun\\question-user-record-detail.vue?vue&type=template&id=377e772d&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\mailun\\question-user-record-detail.vue", "mtime": 1753666157878}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\n<el-dialog title=\"用户测试记录详情\" :visible.sync=\"visible\" width=\"80%\" :close-on-click-modal=\"false\">\n  <div v-loading=\"loading\">\n    <!-- 用户基本信息 -->\n    <el-card class=\"box-card\" style=\"margin-bottom: 20px;\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span>用户信息</span>\n      </div>\n      <el-row :gutter=\"20\">\n        <el-col :span=\"8\">\n          <div class=\"info-item\">\n            <span class=\"label\">用户名：</span>\n            <span class=\"value\">{{ userInfo.username || '-' }}</span>\n          </div>\n        </el-col>\n        <el-col :span=\"8\">\n          <div class=\"info-item\">\n            <span class=\"label\">手机号：</span>\n            <span class=\"value\">{{ userInfo.mobile || '-' }}</span>\n          </div>\n        </el-col>\n        <el-col :span=\"8\">\n          <div class=\"info-item\">\n            <span class=\"label\">测试状态：</span>\n            <el-tag :type=\"userInfo.status == 1 ? 'success' : 'warning'\">\n              {{ userInfo.status == 1 ? '已提交' : '未提交' }}\n            </el-tag>\n          </div>\n        </el-col>\n      </el-row>\n      <el-row :gutter=\"20\" style=\"margin-top: 15px;\">\n        <el-col :span=\"8\">\n          <div class=\"info-item\">\n            <span class=\"label\">总分：</span>\n            <span class=\"value\">{{ userInfo.points || 0 }}</span>\n          </div>\n        </el-col>\n        <el-col :span=\"8\">\n          <div class=\"info-item\">\n            <span class=\"label\">创建时间：</span>\n            <span class=\"value\">{{ userInfo.addTime || '-' }}</span>\n          </div>\n        </el-col>\n        <el-col :span=\"8\">\n          <div class=\"info-item\">\n            <span class=\"label\">更新时间：</span>\n            <span class=\"value\">{{ userInfo.updateTime || '-' }}</span>\n          </div>\n        </el-col>\n      </el-row>\n    </el-card>\n\n    <!-- 脉轮测试结果 -->\n    <el-card class=\"box-card\" style=\"margin-bottom: 20px;\" v-if=\"userInfo.status == 1\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span>脉轮测试结果</span>\n        <el-button style=\"float: right; padding: 3px 0\" type=\"text\" @click=\"copyChakraData()\">\n          <i class=\"el-icon-document-copy\"></i> 复制脉轮数据\n        </el-button>\n      </div>\n      <div class=\"chakra-results\">\n        <div class=\"chakra-grid\">\n          <div class=\"chakra-card\" v-for=\"(chakra, index) in chakraData\" :key=\"index\">\n            <div class=\"chakra-header\">\n              <div class=\"chakra-dot\" :style=\"{ backgroundColor: chakra.color }\"></div>\n              <div class=\"chakra-info\">\n                <div class=\"chakra-name\">{{ chakra.name }}</div>\n                <div class=\"chakra-en-name\">{{ chakra.enName }}</div>\n              </div>\n            </div>\n            <div class=\"chakra-value\">{{ chakra.value || 0 }}</div>\n            <div class=\"chakra-progress\">\n              <el-progress :percentage=\"getProgressPercentage(chakra.value)\" :color=\"chakra.color\" :show-text=\"false\"></el-progress>\n            </div>\n          </div>\n        </div>\n      </div>\n    </el-card>\n\n    <!-- 答题详情 -->\n    <el-card class=\"box-card\" v-if=\"questionOptions.length > 0\">\n      <div slot=\"header\" class=\"clearfix\">\n        <span>答题详情</span>\n      </div>\n      <div class=\"question-details\">\n        <div class=\"question-item\" v-for=\"(item, index) in questionOptions\" :key=\"index\">\n          <div class=\"question-title\">\n            <span class=\"question-number\">{{ index + 1 }}.</span>\n            <span>{{ item.questionName || '题目' + (index + 1) }}</span>\n          </div>\n          <div class=\"question-answer\">\n            <span class=\"answer-label\">选择答案：</span>\n            <span class=\"answer-value\">{{ item.optionName || '-' }}</span>\n          </div>\n        </div>\n      </div>\n    </el-card>\n  </div>\n\n  <div slot=\"footer\" class=\"dialog-footer\">\n    <el-button type=\"primary\" @click=\"exportDetail()\">导出详情</el-button>\n    <el-button @click=\"visible = false\">关闭</el-button>\n  </div>\n</el-dialog>\n", null]}