{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\Category\\info.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\Category\\info.vue", "mtime": 1753666157756}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\"use strict\";\n\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar categoryApi = _interopRequireWildcard(require(\"@/api/categoryApi.js\"));\nfunction _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != _typeof(e) && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  // name: \"info\"\n  props: {\n    id: {\n      type: Number,\n      required: true\n    }\n  },\n  data: function data() {\n    return {\n      defaultProps: {\n        children: 'children',\n        label: 'label'\n      },\n      ddd: [{\n        label: '一级 1',\n        children: [{\n          label: '二级 1-1',\n          children: [{\n            label: '三级 1-1-1'\n          }]\n        }]\n      }, {\n        label: '一级 2',\n        children: [{\n          label: '二级 2-1',\n          children: [{\n            label: '三级 2-1-1'\n          }]\n        }, {\n          label: '二级 2-2',\n          children: [{\n            label: '三级 2-2-1'\n          }]\n        }]\n      }, {\n        label: '一级 3',\n        children: [{\n          label: '二级 3-1',\n          children: [{\n            label: '三级 3-1-1'\n          }]\n        }, {\n          label: '二级 3-2',\n          children: [{\n            label: '三级 3-2-1'\n          }]\n        }]\n      }],\n      dataList: {\n        // 数据结果\n        page: 0,\n        limit: 0,\n        totalPage: 0,\n        total: 0,\n        list: []\n      }\n    };\n  },\n  mounted: function mounted() {\n    this.handlerGetTreeList(this.id);\n  },\n  methods: {\n    handlerGetTreeList: function handlerGetTreeList(id) {\n      var _this = this;\n      if (!id) {\n        this.$message.error('当前数据id不正确');\n        return;\n      }\n      categoryApi.treeCategroy({\n        pid: id\n      }).then(function (data) {\n        _this.dataList = data;\n      });\n    },\n    handleNodeClick: function handleNodeClick(data) {\n      console.log('data:', data);\n    }\n  }\n};", null]}