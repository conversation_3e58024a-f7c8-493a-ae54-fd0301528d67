{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/user_pwd_edit/index.vue?38fb", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/user_pwd_edit/index.vue?965b", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/user_pwd_edit/index.vue?7d15", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/user_pwd_edit/index.vue?6745", "uni-app:///pages/users/user_pwd_edit/index.vue", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/user_pwd_edit/index.vue?57d4", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/user_pwd_edit/index.vue?2b90"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "mixins", "components", "authorize", "data", "userInfo", "phone", "password", "<PERSON><PERSON>a", "qr_password", "isAuto", "isShowAuth", "computed", "watch", "is<PERSON>ogin", "handler", "deep", "onLoad", "methods", "onLoadFun", "auth<PERSON><PERSON><PERSON>", "getUserInfo", "that", "code", "title", "editPwd", "account", "tab", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACmM;AACnM,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAkwB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC6BtxB;AACA;AAIA;AAGA;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAMA;EACAC;EACAC;IAEAC;EAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;EACAC;IACAC;MACAC;QACA;UACA;QACA;MACA;MACAC;IACA;EACA;EACAC;IACA;MACA;IACA;MACA;IACA;EACA;EACAC;IACA;AACA;AACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA;QACA;QACA;QACAC;QACAA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAD;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBAAA;kBACAE;gBACA;cAAA;gBAAA;gBAAA,OACA;kBACAF;oBACAE;kBACA;kBACAF;gBACA;kBACA;oBACAE;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;AACA;AACA;AACA;IACAC;MACA;QACAlB;QACAE;QACAD;MACA;QACAgB;MACA;MACA;QACAA;MACA;MACA;QACAA;MACA;MACA;QACAA;MACA;MACA;QACAE;QACAlB;QACAD;MACA;QACA;UACAiB;QACA;UACAG;UACAC;QACA;MACA;QACA;UACAJ;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACrKA;AAAA;AAAA;AAAA;AAA66C,CAAgB,4tCAAG,EAAC,C;;;;;;;;;;;ACAj8C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/users/user_pwd_edit/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/users/user_pwd_edit/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=751ef713&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/users/user_pwd_edit/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=751ef713&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view class=\"ChangePassword\">\r\n\t\t\t<form @submit=\"editPwd\" report-submit='true'>\r\n\t\t\t\t<view class=\"phone\">当前手机号：{{phone}}</view>\r\n\t\t\t\t<view class=\"list\">\r\n\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t<input type='password' placeholder='以字母开头，长度在6~18之间，只能包含字符、数字和下划线' placeholder-class='placeholder' name=\"password\" :value=\"password\"></input>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t<input type='password' placeholder='确认新密码' placeholder-class='placeholder' name=\"qr_password\" :value=\"qr_password\"></input>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"item acea-row row-between-wrapper\">\r\n\t\t\t\t\t\t<input type='number' placeholder='填写验证码' placeholder-class='placeholder' class=\"codeIput\" name=\"captcha\" :value=\"captcha\"></input>\r\n\t\t\t\t\t\t<button class=\"code font-color\" :class=\"disabled === true ? 'on' : ''\" :disabled='disabled' @click=\"code\">\r\n\t\t\t\t\t\t\t{{ text }}\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<button form-type=\"submit\" class=\"confirmBnt bg-color\">确认修改</button>\r\n\t\t\t</form>\r\n\t\t</view>\r\n\t\t<!-- #ifdef MP -->\r\n\t\t<!-- <authorize @onLoadFun=\"onLoadFun\" :isAuto=\"isAuto\" :isShowAuth=\"isShowAuth\" @authColse=\"authColse\"></authorize> -->\r\n\t\t<!-- #endif -->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport sendVerifyCode from \"@/mixins/SendVerifyCode\";\r\n\timport {\r\n\t\tphoneRegisterReset,\r\n\t\tregisterVerify\r\n\t} from '@/api/api.js';\r\n\timport {\r\n\t\tgetUserInfo\r\n\t} from '@/api/user.js';\r\n\timport {\r\n\t\ttoLogin\r\n\t} from '@/libs/login.js';\r\n\timport {\r\n\t\tmapGetters\r\n\t} from \"vuex\";\r\n\t// #ifdef MP\r\n\timport authorize from '@/components/Authorize';\r\n\t// #endif\r\n\texport default {\r\n\t\tmixins: [sendVerifyCode],\r\n\t\tcomponents: {\r\n\t\t\t// #ifdef MP\r\n\t\t\tauthorize\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tuserInfo: {},\r\n\t\t\t\tphone: '',\r\n\t\t\t\tpassword: '',\r\n\t\t\t\tcaptcha: '',\r\n\t\t\t\tqr_password: '',\r\n\t\t\t\tisAuto: false, //没有授权的不会自动授权\r\n\t\t\t\tisShowAuth: false //是否隐藏授权\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: mapGetters(['isLogin']),\r\n\t\twatch:{\r\n\t\t\tisLogin:{\r\n\t\t\t\thandler:function(newV,oldV){\r\n\t\t\t\t\tif(newV){\r\n\t\t\t\t\t\tthis.getUserInfo();\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tdeep:true\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tif (this.isLogin) {\r\n\t\t\t\tthis.getUserInfo();\r\n\t\t\t} else {\r\n\t\t\t\ttoLogin();\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t/**\r\n\t\t\t * 授权回调\r\n\t\t\t */\r\n\t\t\tonLoadFun: function(e) {\r\n\t\t\t\tthis.getUserInfo();\r\n\t\t\t},\r\n\t\t\t// 授权关闭\r\n\t\t\tauthColse: function(e) {\r\n\t\t\t\tthis.isShowAuth = e\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 获取个人用户信息\r\n\t\t\t */\r\n\t\t\tgetUserInfo: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tgetUserInfo().then(res => {\r\n\t\t\t\t\tlet tel = res.data.phone;\r\n\t\t\t\t\tlet phone = tel.substr(0, 3) + \"****\" + tel.substr(7);\r\n\t\t\t\t\tthat.$set(that, 'userInfo', res.data);\r\n\t\t\t\t\tthat.phone = phone;\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 发送验证码\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tasync code() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif (!that.userInfo.phone) return that.$util.Tips({\r\n\t\t\t\t\ttitle: '手机号码不存在,无法发送验证码！'\r\n\t\t\t\t});\r\n\t\t\t\tawait registerVerify(that.userInfo.phone).then(res => {\r\n\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\ttitle: res.message\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthat.sendCode();\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * H5登录 修改密码\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\teditPwd: function(e) {\r\n\t\t\t\tlet that = this,\r\n\t\t\t\t\tpassword = e.detail.value.password,\r\n\t\t\t\t\tqr_password = e.detail.value.qr_password,\r\n\t\t\t\t\tcaptcha = e.detail.value.captcha;\r\n\t\t\t\tif (!password) return that.$util.Tips({\r\n\t\t\t\t\ttitle: '请输入新密码'\r\n\t\t\t\t});\r\n\t\t\t\tif (!/^[a-zA-Z]\\w{5,17}$/i.test(password)) return that.$util.Tips({\r\n\t\t\t\t\ttitle: '以字母开头，长度在6~18之间，只能包含字符、数字和下划线'\r\n\t\t\t\t});\r\n\t\t\t\tif (qr_password != password) return that.$util.Tips({\r\n\t\t\t\t\ttitle: '两次输入的密码不一致！'\r\n\t\t\t\t});\r\n\t\t\t\tif (!captcha) return that.$util.Tips({\r\n\t\t\t\t\ttitle: '请输入验证码'\r\n\t\t\t\t});\r\n\t\t\t\tphoneRegisterReset({\r\n\t\t\t\t\taccount: that.userInfo.phone,\r\n\t\t\t\t\tcaptcha: captcha,\r\n\t\t\t\t\tpassword: password\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\ttitle: res.message\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\ttab: 3,\r\n\t\t\t\t\t\turl: 1\r\n\t\t\t\t\t});\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\tpage {\r\n\t\tbackground-color: #fff !important;\r\n\t}\r\n\r\n\t.ChangePassword .phone {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\ttext-align: center;\r\n\t\tmargin-top: 55rpx;\r\n\t}\r\n\r\n\t.ChangePassword .list {\r\n\t\twidth: 580rpx;\r\n\t\tmargin: 53rpx auto 0 auto;\r\n\t}\r\n\r\n\t.ChangePassword .list .item {\r\n\t\twidth: 100%;\r\n\t\theight: 110rpx;\r\n\t\tborder-bottom: 2rpx solid #f0f0f0;\r\n\t}\r\n\r\n\t.ChangePassword .list .item input {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tfont-size: 32rpx;\r\n\t}\r\n\r\n\t.ChangePassword .list .item .placeholder {\r\n\t\tcolor: #b9b9bc;\r\n\t}\r\n\r\n\t.ChangePassword .list .item input.codeIput {\r\n\t\twidth: 340rpx;\r\n\t}\r\n\r\n\t.ChangePassword .list .item .code {\r\n\t\tfont-size: 32rpx;\r\n\t\tbackground-color: #fff;\r\n\t}\r\n\r\n\t.ChangePassword .list .item .code.on {\r\n\t\tcolor: #b9b9bc !important;\r\n\t}\r\n\r\n\t.ChangePassword .confirmBnt {\r\n\t\tfont-size: 32rpx;\r\n\t\twidth: 580rpx;\r\n\t\theight: 90rpx;\r\n\t\tborder-radius: 45rpx;\r\n\t\tcolor: #fff;\r\n\t\tmargin: 92rpx auto 0 auto;\r\n\t\ttext-align: center;\r\n\t\tline-height: 90rpx;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363903569\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}