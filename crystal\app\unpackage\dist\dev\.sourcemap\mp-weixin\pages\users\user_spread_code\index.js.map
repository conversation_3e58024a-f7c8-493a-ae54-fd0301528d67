{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/user_spread_code/index.vue?9e94", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/user_spread_code/index.vue?0b5c", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/user_spread_code/index.vue?502d", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/user_spread_code/index.vue?75bb", "uni-app:///pages/users/user_spread_code/index.vue", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/user_spread_code/index.vue?215d", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/user_spread_code/index.vue?c530"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "authorize", "home", "data", "imgUrls", "indicatorDots", "circular", "autoplay", "interval", "duration", "swiperIndex", "spreadList", "poster", "isAuto", "isShowAuth", "qrcodeSize", "PromotionCode", "base64List", "canvasStatus", "computed", "watch", "is<PERSON>ogin", "handler", "deep", "onLoad", "onShareAppMessage", "title", "imageUrl", "path", "onReady", "methods", "userSpreadBannerList", "uni", "mask", "page", "limit", "that", "getImageBase64", "getQrcode", "pid", "url", "success", "arrImagesUrl", "setTimeout", "make", "uQRCode", "canvasId", "text", "size", "margin", "nickname", "complete", "fail", "Poster<PERSON><PERSON><PERSON>", "context", "src", "destWidth", "destHeight", "fileType", "tempFile<PERSON>ath", "onLoadFun", "auth<PERSON><PERSON><PERSON>", "bindchange", "index", "savePosterPath", "scope", "filePath", "icon", "setShareInfoStatus", "desc", "link", "imgUrl", "configAppMessage"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACmM;AACnM,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAkwB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACqCtxB;AAIA;AAGA;AAIA;AAIA;AAKA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACAC;IAEAC;IAEAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;;EACAC;EACAC;IACAC;MACAC;QACA;UACA;QACA;MACA;MACAC;IACA;EACA;EACAC;IACA;MACA;IACA;MACA;IACA;EACA;EACA;AACA;AACA;;EAEAC;IACA;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;EACAC;IACAC;MACA;MACAC;QACAN;QACAO;MACA;MACA;QACAC;QACAC;MACA;QACAH;QACAI;QACAA;MACA;QACAJ;MACA;IACA;IACAK;MACAL;QACAN;QACAO;MACA;MACA;MAoBAG;QACA;MACA;MAIAA;IAEA;IACA;IACAE;MACA;MACA;QACAC;QACAX;MACA;MACA;MACAI;QACAQ;QACAC;UACAC;QACA;MACA;MACA;QACA;UACAN;QACA;QACAO;UACAP;QACA;MACA;QACAJ;QACAI;UACAV;QACA;QACAU;MACA;IACA;IACA;IACAQ;MAAA;MACA;MACA;MAIAC;QACAC;QACAC;QACAC;QACAC;QACAR;UACAL;UACAO;YACAP,yEACAc;UACA;QACA;QACAC;QACAC;UACApB;UACAI;YACAV;UACA;QACA;MACA;IACA;IACA2B;MACA;MACAC;MACA;MACAtB;QACAuB;QACAd;UACAa;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAX;YACAW;cACAtB;gBACAwB;gBACAC;gBACAX;gBACAY;gBACAjB;kBACA;kBACAT;kBACAI,iCACAuB;kBACAvB,8BACAuB;kBACAvB;gBACA;cACA;YACA;UACA;QACA;QACAgB;UACApB;UACAI;YACAV;UACA;QACA;MACA;IACA;IAGAkC;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;MACA;MACA;MACA;MACA9B;QACAQ;QACAC;UACAC;UACAC;YACA;YACA,kFACAoB;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACAhC;QACAS;UACA;YACAT;cACAiC;cACAxB;gBACAT;kBACAkC;kBACAzB;oBACAL;sBACAV;sBACAyC;oBACA;kBACA;kBACAf;oBACAhB;sBACAV;oBACA;kBACA;gBACA;cACA;YACA;UACA;YACAM;cACAkC;cACAzB;gBACAL;kBACAV;kBACAyC;gBACA;cACA;cACAf;gBACAhB;kBACAV;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IACA0C;MACA;QACA;UACAC;UACA3C;UACA4C;UACAC;QACA;QACA,mFACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChWA;AAAA;AAAA;AAAA;AAAq8C,CAAgB,ovCAAG,EAAC,C;;;;;;;;;;;ACAz9C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/users/user_spread_code/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/users/user_spread_code/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=5d8acc68&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=5d8acc68&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5d8acc68\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/users/user_spread_code/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=5d8acc68&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view style=\"height: 100%;\">\r\n\t\t<view class='distribution-posters'>\r\n\t\t\t<swiper :indicator-dots=\"indicatorDots\" :autoplay=\"autoplay\" :circular=\"circular\" :interval=\"interval\"\r\n\t\t\t\t:duration=\"duration\" @change=\"bindchange\" previous-margin=\"40px\" next-margin=\"40px\">\r\n\t\t\t\t<block v-for=\"(item,index) in spreadList\" :key=\"index\">\r\n\t\t\t\t\t<swiper-item>\r\n\t\t\t\t\t\t<image :src=\"item.pic\" class=\"slide-image\" :class=\"swiperIndex == index ? 'active' : 'quiet'\"\r\n\t\t\t\t\t\t\tmode='aspectFill' />\r\n\t\t\t\t\t</swiper-item>\r\n\t\t\t\t</block>\r\n\t\t\t</swiper>\r\n\t\t\t<!-- #ifdef MP -->\r\n\t\t\t<view class='keep bg-color' @click='savePosterPath'>保存海报</view>\r\n\t\t\t<!-- #endif -->\r\n\t\t\t<!-- #ifndef MP -->\r\n\t\t\t<div class=\"preserve acea-row row-center-wrapper\">\r\n\t\t\t\t<div class=\"line\"></div>\r\n\t\t\t\t<div class=\"tip\">长按保存图片</div>\r\n\t\t\t\t<div class=\"line\"></div>\r\n\t\t\t</div>\r\n\t\t\t<!-- #endif -->\r\n\t\t</view>\r\n\t\t<!-- #ifdef MP -->\r\n\t\t<!-- <authorize @onLoadFun=\"onLoadFun\" :isAuto=\"isAuto\" :isShowAuth=\"isShowAuth\" @authColse=\"authColse\"></authorize> -->\r\n\t\t<!-- #endif -->\r\n\t\t<view class=\"canvas\" v-if=\"canvasStatus\">\r\n\t\t\t<canvas style=\"width:750px;height:1190px;\" canvas-id=\"canvasOne\"></canvas>\r\n\t\t\t<canvas canvas-id=\"qrcode\" :style=\"{width: `${qrcodeSize}px`, height: `${qrcodeSize}px`}\" />\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t// #ifdef H5\r\n\timport uQRCode from '@/js_sdk/Sansnn-uQRCode/uqrcode.js'\r\n\t// #endif\r\n\timport {\r\n\t\tgetUserInfo,\r\n\t\tspreadBanner\r\n\t} from '@/api/user.js';\r\n\timport {\r\n\t\ttoLogin\r\n\t} from '@/libs/login.js';\r\n\timport {\r\n\t\tmapGetters\r\n\t} from \"vuex\";\r\n\t// #ifdef MP\r\n\timport {\r\n\t\tbase64src\r\n\t} from '@/utils/base64src.js'\r\n\timport authorize from '@/components/Authorize';\r\n\timport {\r\n\t\tgetQrcode\r\n\t} from '@/api/api.js';\r\n\t// #endif\r\n\timport home from '@/components/home';\r\n\timport {\r\n\t\timageBase64\r\n\t} from \"@/api/public\";\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\t// #ifdef MP\r\n\t\t\tauthorize,\r\n\t\t\t// #endif\r\n\t\t\thome\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\timgUrls: [],\r\n\t\t\t\tindicatorDots: false,\r\n\t\t\t\tcircular: false,\r\n\t\t\t\tautoplay: false,\r\n\t\t\t\tinterval: 3000,\r\n\t\t\t\tduration: 500,\r\n\t\t\t\tswiperIndex: 0,\r\n\t\t\t\tspreadList: [],\r\n\t\t\t\tposter: '',\r\n\t\t\t\tisAuto: false, //没有授权的不会自动授权\r\n\t\t\t\tisShowAuth: false, //是否隐藏授权\r\n\t\t\t\tqrcodeSize: 1000,\r\n\t\t\t\tPromotionCode: '',\r\n\t\t\t\tbase64List: [],\r\n\t\t\t\tcanvasStatus: true //海报绘图标签\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: mapGetters(['isLogin', 'uid', 'userInfo']),\r\n\t\twatch: {\r\n\t\t\tisLogin: {\r\n\t\t\t\thandler: function(newV, oldV) {\r\n\t\t\t\t\tif (newV) {\r\n\t\t\t\t\t\tthis.userSpreadBannerList();\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tdeep: true\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tif (this.isLogin) {\r\n\t\t\t\tthis.userSpreadBannerList();\r\n\t\t\t} else {\r\n\t\t\t\ttoLogin();\r\n\t\t\t}\r\n\t\t},\r\n\t\t/**\r\n\t\t * 用户点击右上角分享\r\n\t\t */\r\n\t\t// #ifdef MP\r\n\t\tonShareAppMessage: function() {\r\n\t\t\treturn {\r\n\t\t\t\ttitle: this.userInfo.nickname + '-分销海报',\r\n\t\t\t\timageUrl: this.spreadList[0].pic,\r\n\t\t\t\tpath: '/pages/index/index?spid=' + this.uid,\r\n\t\t\t};\r\n\t\t},\r\n\t\t// #endif\r\n\t\tonReady() {},\r\n\t\tmethods: {\r\n\t\t\tuserSpreadBannerList: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '获取中',\r\n\t\t\t\t\tmask: true,\r\n\t\t\t\t})\r\n\t\t\t\tspreadBanner({\r\n\t\t\t\t\tpage: 1,\r\n\t\t\t\t\tlimit: 5\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tthat.$set(that, 'spreadList', res.data);\r\n\t\t\t\t\tthat.getImageBase64(res.data);\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgetImageBase64: function(images) {\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '海报生成中',\r\n\t\t\t\t\tmask: true\r\n\t\t\t\t});\r\n\t\t\t\tlet that = this;\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tlet spreadList = []\r\n\t\t\t\t// 生成一个Promise对象的数组\r\n\t\t\t\timages.forEach(item => {\r\n\t\t\t\t\tconst oneApi = imageBase64({\r\n\t\t\t\t\t\turl: item.pic\r\n\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\treturn res.data.code;\r\n\t\t\t\t\t})\r\n\t\t\t\t\tspreadList.push(oneApi)\r\n\t\t\t\t})\r\n\t\t\t\tPromise.all(spreadList).then(result => {\r\n\t\t\t\t\tthat.$set(that, 'base64List', result);\r\n\t\t\t\t\tthat.make();\r\n\t\t\t\t\tthat.setShareInfoStatus();\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\r\n\t\t\t\t// #ifdef MP\r\n\t\t\t\tthat.base64List = images.map(item => {\r\n\t\t\t\t\treturn item.pic\r\n\t\t\t\t});\r\n\t\t\t\t// #endif\r\n\r\n\t\t\t\t// #ifdef MP\r\n\t\t\t\tthat.getQrcode();\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\t// 小程序二维码\r\n\t\t\tgetQrcode() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\tpid: that.uid,\r\n\t\t\t\t\tpath: 'pages/index/index'\r\n\t\t\t\t}\r\n\t\t\t\tlet arrImagesUrl = \"\";\r\n\t\t\t\tuni.downloadFile({\r\n\t\t\t\t\turl: this.base64List[0],\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tarrImagesUrl = res.tempFilePath;\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\tgetQrcode(data).then(res => {\r\n\t\t\t\t\tbase64src(res.data.code, res => {\r\n\t\t\t\t\t\tthat.PromotionCode = res;\r\n\t\t\t\t\t});\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthat.PosterCanvas(arrImagesUrl, that.PromotionCode, that.userInfo.nickname, 0);\r\n\t\t\t\t\t}, 300);\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthat.$set(that, 'canvasStatus', false);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 生成二维码；\r\n\t\t\tmake() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet href = '';\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\thref = window.location.href.split('/pages')[0];\r\n\t\t\t\t// #endif\r\n\t\t\t\tuQRCode.make({\r\n\t\t\t\t\tcanvasId: 'qrcode',\r\n\t\t\t\t\ttext: href + '/pages/index/index?spread=' + that.uid,\r\n\t\t\t\t\tsize: this.qrcodeSize,\r\n\t\t\t\t\tmargin: 10,\r\n\t\t\t\t\tsuccess: res => {\r\n\t\t\t\t\t\tthat.PromotionCode = res;\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tthat.PosterCanvas(this.base64List[0], that.PromotionCode, that.userInfo\r\n\t\t\t\t\t\t\t\t.nickname, 0);\r\n\t\t\t\t\t\t}, 300);\r\n\t\t\t\t\t},\r\n\t\t\t\t\tcomplete: (res) => {},\r\n\t\t\t\t\tfail: res => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: '海报二维码生成失败！'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tPosterCanvas: function(arrImages, code, nickname, index) {\r\n\t\t\t\tlet context = uni.createCanvasContext('canvasOne')\r\n\t\t\t\tcontext.clearRect(0, 0, 0, 0);\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tuni.getImageInfo({\r\n\t\t\t\t\tsrc: arrImages,\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tcontext.drawImage(arrImages, 0, 0, 750, 1190);\r\n\t\t\t\t\t\tcontext.save();\r\n\t\t\t\t\t\tcontext.drawImage(code, 110, 925, 140, 140);\r\n\t\t\t\t\t\tcontext.restore();\r\n\t\t\t\t\t\tcontext.setFontSize(28);\r\n\t\t\t\t\t\tcontext.fillText(nickname, 270, 980);\r\n\t\t\t\t\t\tcontext.fillText('邀请您加入', 270, 1020);\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tcontext.draw(true, function() {\r\n\t\t\t\t\t\t\t\tuni.canvasToTempFilePath({\r\n\t\t\t\t\t\t\t\t\tdestWidth: 750,\r\n\t\t\t\t\t\t\t\t\tdestHeight: 1190,\r\n\t\t\t\t\t\t\t\t\tcanvasId: 'canvasOne',\r\n\t\t\t\t\t\t\t\t\tfileType: 'jpg',\r\n\t\t\t\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\t\t\t\t// 在H5平台下，tempFilePath 为 base64\r\n\t\t\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t\t\tthat.spreadList[index].pic = res\r\n\t\t\t\t\t\t\t\t\t\t\t.tempFilePath;\r\n\t\t\t\t\t\t\t\t\t\tthat.$set(that, 'poster', res\r\n\t\t\t\t\t\t\t\t\t\t\t.tempFilePath);\r\n\t\t\t\t\t\t\t\t\t\tthat.$set(that, 'canvasStatus', false);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}, 100);\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: function(err) {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: '无法获取图片信息'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\r\n\t\t\tonLoadFun: function(e) {\r\n\t\t\t\tthis.$set(this, 'userInfo', e);\r\n\t\t\t\tthis.userSpreadBannerList();\r\n\t\t\t},\r\n\t\t\t// 授权关闭\r\n\t\t\tauthColse: function(e) {\r\n\t\t\t\tthis.isShowAuth = e\r\n\t\t\t},\r\n\t\t\tbindchange(e) {\r\n\t\t\t\tlet base64List = this.base64List;\r\n\t\t\t\tlet index = e.detail.current;\r\n\t\t\t\tthis.swiperIndex = index;\r\n\t\t\t\tlet arrImagesUrl = \"\";\r\n\t\t\t\tuni.downloadFile({\r\n\t\t\t\t\turl: base64List[index],\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tarrImagesUrl = res.tempFilePath;\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tthis.$set(this, 'canvasStatus', true);\r\n\t\t\t\t\t\t\tthis.PosterCanvas(arrImagesUrl, this.PromotionCode, this.userInfo.nickname,\r\n\t\t\t\t\t\t\t\tindex);\r\n\t\t\t\t\t\t}, 300);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 点击保存海报\r\n\t\t\tsavePosterPath: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tuni.getSetting({\r\n\t\t\t\t\tsuccess(res) {\r\n\t\t\t\t\t\tif (!res.authSetting['scope.writePhotosAlbum']) {\r\n\t\t\t\t\t\t\tuni.authorize({\r\n\t\t\t\t\t\t\t\tscope: 'scope.writePhotosAlbum',\r\n\t\t\t\t\t\t\t\tsuccess() {\r\n\t\t\t\t\t\t\t\t\tuni.saveImageToPhotosAlbum({\r\n\t\t\t\t\t\t\t\t\t\tfilePath: that.poster,\r\n\t\t\t\t\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: '保存成功',\r\n\t\t\t\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\tfail: function(res) {\r\n\t\t\t\t\t\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: '保存失败'\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tuni.saveImageToPhotosAlbum({\r\n\t\t\t\t\t\t\t\tfilePath: that.poster,\r\n\t\t\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '保存成功',\r\n\t\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tfail: function(res) {\r\n\t\t\t\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '保存失败'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tsetShareInfoStatus: function() {\r\n\t\t\t\tif (this.$wechat.isWeixin()) {\r\n\t\t\t\t\tlet configAppMessage = {\r\n\t\t\t\t\t\tdesc: '分销海报',\r\n\t\t\t\t\t\ttitle: this.userInfo.nickname + '-分销海报',\r\n\t\t\t\t\t\tlink: '/pages/index/index?spread=' + this.uid,\r\n\t\t\t\t\t\timgUrl: this.spreadList[0].pic\r\n\t\t\t\t\t};\r\n\t\t\t\t\tthis.$wechat.wechatEvevt([\"updateAppMessageShareData\", \"updateTimelineShareData\"],\r\n\t\t\t\t\t\tconfigAppMessage)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\tpage {\r\n\t\tbackground-color: #a3a3a3 !important;\r\n\t\theight: 100% !important;\r\n\t}\r\n\r\n\t.canvas {\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.distribution-posters {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.distribution-posters swiper {\r\n\t\twidth: 100%;\r\n\t\theight: 1000rpx;\r\n\t\tposition: relative;\r\n\t\tmargin-top: 40rpx;\r\n\t}\r\n\r\n\t.distribution-posters .slide-image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tmargin: 0 auto;\r\n\t\tborder-radius: 15rpx;\r\n\t}\r\n\r\n\t.distribution-posters .slide-image.active {\r\n\t\ttransform: none;\r\n\t\ttransition: all 0.2s ease-in 0s;\r\n\t}\r\n\r\n\t.distribution-posters .slide-image.quiet {\r\n\t\ttransform: scale(0.8333333);\r\n\t\ttransition: all 0.2s ease-in 0s;\r\n\t}\r\n\r\n\t.distribution-posters .keep {\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #fff;\r\n\t\twidth: 600rpx;\r\n\t\theight: 80rpx;\r\n\t\tborder-radius: 50rpx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 80rpx;\r\n\t\tmargin: 38rpx auto;\r\n\t}\r\n\r\n\t.distribution-posters .preserve {\r\n\t\tcolor: #fff;\r\n\t\ttext-align: center;\r\n\t\tmargin-top: 38rpx;\r\n\t}\r\n\r\n\t.distribution-posters .preserve .line {\r\n\t\twidth: 100rpx;\r\n\t\theight: 1px;\r\n\t\tbackground-color: #fff;\r\n\t}\r\n\r\n\t.distribution-posters .preserve .tip {\r\n\t\tmargin: 0 30rpx;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=5d8acc68&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=5d8acc68&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363903686\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}