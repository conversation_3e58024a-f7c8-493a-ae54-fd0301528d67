{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\userprocess.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\userprocess.vue", "mtime": 1753666157945}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport { userprocessFindByUserId } from '@/api/userprocess'\r\nimport AddOrUpdate from './userprocess-add-and-update'\r\nexport default {\r\n  data() {\r\n    return {\r\n      userId: '',\r\n      dataList: [],\r\n      dataListLoading: false,\r\n      dataListSelections: [],\r\n      addOrUpdateVisible: false\r\n    }\r\n  },\r\n  components: {\r\n    AddOrUpdate\r\n  },\r\n  mounted() {\r\n    this.userId = this.$route.query.id\r\n    this.getDataList()\r\n  },\r\n  methods: {\r\n    // 获取数据列表\r\n    getDataList() {\r\n      this.dataListLoading = true\r\n\r\n      userprocessFindByUserId({ userId: this.userId }).then(res => {\r\n        this.dataList = res || [];\r\n        this.dataListLoading = false\r\n      }).catch(() => {\r\n        this.dataList = []\r\n        this.dataListLoading = false\r\n      })\r\n    },\r\n    // 多选\r\n    selectionChangeHandle(val) {\r\n      this.dataListSelections = val\r\n    },\r\n    // 新增 / 修改\r\n    addOrUpdateHandle(id) {\r\n      this.addOrUpdateVisible = true\r\n      this.$nextTick(() => {\r\n        this.$refs.addOrUpdate.init(id, this.userId)\r\n      })\r\n    },\r\n    // 删除\r\n    deleteHandle(id) {\r\n      // var ids = id ? [id] : this.dataListSelections.map(item => {\r\n      //   return item.id\r\n      // })\r\n      this.$confirm(`确定删除操作?`, '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n\r\n        questionDeleteApi(id).then(() => {\r\n          this.$message.success(\"删除成功\");\r\n          this.getDataList();\r\n        }).catch((res) => {\r\n          this.$message.error(res.message)\r\n        });\r\n      })\r\n    }\r\n  }\r\n}\r\n", null]}