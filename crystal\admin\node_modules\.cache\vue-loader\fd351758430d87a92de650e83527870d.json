{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\list\\level.vue?vue&type=template&id=06d28e26", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\list\\level.vue", "mtime": 1753666157941}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\n<el-form :model=\"ruleForm\" ref=\"ruleForm\" label-width=\"100px\" class=\"demo-ruleForm\" >\n  <el-form-item>\n    <el-alert title=\"请勿频繁更改，以免计算产生混乱！\" type=\"warning\"></el-alert>\n  </el-form-item>\n  <el-form-item label=\"用户等级\" label-width=\"100px\">\n    <el-select v-model=\"ruleForm.levelId\" clearable placeholder=\"请选择\" @change=\"currentSel\">\n      <el-option\n        v-for=\"item in levelList\"\n        :key=\"item.grade\"\n        :label=\"item.name\"\n        :value=\"item.id\"\n      >\n      </el-option>\n    </el-select>\n  </el-form-item>\n  <el-form-item label=\"扣除经验\" label-width=\"100px\" v-if=\"grade =='' ? false : grade < levelInfo.gradeLevel\">\n    <el-switch v-model=\"ruleForm.isSub\"></el-switch>\n  </el-form-item>\n  <el-form-item>\n    <el-button @click=\"resetForm('ruleForm')\">取消</el-button>\n    <el-button type=\"primary\" @click=\"submitForm('ruleForm')\">确定</el-button>\n  </el-form-item>\n</el-form>\n", null]}