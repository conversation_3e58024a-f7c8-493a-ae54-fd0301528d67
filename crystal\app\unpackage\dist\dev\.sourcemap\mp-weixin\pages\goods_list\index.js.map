{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/goods_list/index.vue?3345", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/goods_list/index.vue?3415", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/goods_list/index.vue?53dc", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/goods_list/index.vue?776d", "uni-app:///pages/goods_list/index.vue", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/goods_list/index.vue?9d6b", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/goods_list/index.vue?f527"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "computed", "components", "recommend", "data", "productList", "is_switch", "where", "keyword", "priceOrder", "salesOrder", "news", "page", "limit", "cid", "price", "stock", "nows", "loadend", "loading", "loadTitle", "title", "hostProduct", "hotPage", "hotLimit", "hotScroll", "onLoad", "methods", "goback", "delta", "godDetail", "uni", "url", "<PERSON><PERSON><PERSON>", "that", "searchSubmit", "get_host_product", "set_where", "setWhere", "get_product_list", "onPullDownRefresh", "onReachBottom"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACgM;AAChM,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAmvB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;AC2EvwB;AAKA;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACAC;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;EACA;EACAC;IACAC;MAKA;QACAC;MACA;IAEA;IACA;IACAC;MACA;QACAC;UACAC;QACA;MACA;IACA;IACAC;MACA;MACAC;IACA;IACAC;MACA;MACAD;MACAA;MACAA;MACA;IACA;IACA;AACA;AACA;IACAE;MACA;MACA;MACA,0BACAF,cACAA,cACA;QACAA;QACAA;QACAA;QACA;MACA;IACA;;IACA;IACAG;MACA;QACA;UACA;UACA;QACA;UACA,yCACA,yCACA;UACA;UACA;QACA;UACA,yCACA,yCACA;UACA;UACA;QACA;UACA;UACA;MAAA;MAEA;MACA;MACA;IACA;IACA;IACAC;MACA,qDACA,wDACA;MACA,qDACA,wDACA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACAL;MACA;MACA;MACA;MACAA;MACAA;MACA;QACA;QACA;QACA;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;UACA;QACA;MACA;QACAA;QACAA;MACA;IACA;EACA;EACAM,iDAEA;EACAC;IACA;MACA;IACA;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;ACnPA;AAAA;AAAA;AAAA;AAA06C,CAAgB,ovCAAG,EAAC,C;;;;;;;;;;;ACA97C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/goods_list/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/goods_list/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=8b1a97ba&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=8b1a97ba&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"8b1a97ba\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/goods_list/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=8b1a97ba&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.productList.length\n  var l0 =\n    g0 > 0\n      ? _vm.__map(_vm.productList, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m0 = Number(item.sales) + Number(item.ficti) || 0\n          return {\n            $orig: $orig,\n            m0: m0,\n          }\n        })\n      : null\n  var g1 = g0 > 0 ? _vm.productList.length : null\n  var g2 = _vm.productList.length == 0 && _vm.where.page > 1\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        g1: g1,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view class='productList'>\r\n\t\t\t<view class='search bg-color acea-row row-between-wrapper'>\r\n\t\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t\t<view class=\"iconfont icon-xiangzuo\" @click=\"goback()\"></view>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t<view class='input acea-row row-between-wrapper'><text class='iconfont icon-sousuo'></text>\r\n\t\t\t\t\t<input placeholder='搜索商品名称' placeholder-class='placeholder' confirm-type='search' name=\"search\"\r\n\t\t\t\t\t\t:value='where.keyword' @confirm=\"searchSubmit\"></input>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='iconfont' :class='is_switch==true?\"icon-pailie\":\"icon-tupianpailie\"' @click='Changswitch'>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class='nav acea-row row-middle'>\r\n\t\t\t\t<view class='item' :class='title ? \"font-color\":\"\"' @click='set_where(1)'>{{title ? title:'默认'}}</view>\r\n\t\t\t\t<view class='item' @click='set_where(2)'>\r\n\t\t\t\t\t价格\r\n\t\t\t\t\t<image v-if=\"price==1\" src='../../static/images/up.png'></image>\r\n\t\t\t\t\t<image v-else-if=\"price==2\" src='../../static/images/down.png'></image>\r\n\t\t\t\t\t<image v-else src='../../static/images/horn.png'></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='item' @click='set_where(3)'>\r\n\t\t\t\t\t销量\r\n\t\t\t\t\t<image v-if=\"stock==1\" src='../../static/images/up.png'></image>\r\n\t\t\t\t\t<image v-else-if=\"stock==2\" src='../../static/images/down.png'></image>\r\n\t\t\t\t\t<image v-else src='../../static/images/horn.png'></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- down -->\r\n\t\t\t\t<view class='item' :class='nows ? \"font-color\":\"\"' @click='set_where(4)'>新品</view>\r\n\t\t\t</view>\r\n\t\t\t<view :class='is_switch==true?\"\":\"listBox\"' v-if=\"productList.length>0\">\r\n\t\t\t\t<view class='list acea-row row-between-wrapper' :class='is_switch==true?\"\":\"on\"'>\r\n\t\t\t\t\t<view class='item' :class='is_switch==true?\"\":\"on\"' hover-class='none'\r\n\t\t\t\t\t\tv-for=\"(item,index) in productList\" :key=\"index\" @click=\"godDetail(item)\">\r\n\t\t\t\t\t\t<view class='pictrue' :class='is_switch==true?\"\":\"on\"'>\r\n\t\t\t\t\t\t\t<image :src='item.image' :class='is_switch==true?\"\":\"on\"'></image>\r\n\t\t\t\t\t\t\t<span class=\"pictrue_log_class\"\r\n\t\t\t\t\t\t\t\t:class=\"is_switch === true ? 'pictrue_log_big' : 'pictrue_log'\"\r\n\t\t\t\t\t\t\t\tv-if=\"item.activityH5 && item.activityH5.type === '1'\">秒杀</span>\r\n\t\t\t\t\t\t\t<span class=\"pictrue_log_class\"\r\n\t\t\t\t\t\t\t\t:class=\"is_switch === true ? 'pictrue_log_big' : 'pictrue_log'\"\r\n\t\t\t\t\t\t\t\tv-if=\"item.activityH5 && item.activityH5.type === '2'\">砍价</span>\r\n\t\t\t\t\t\t\t<span class=\"pictrue_log_class\"\r\n\t\t\t\t\t\t\t\t:class=\"is_switch === true ? 'pictrue_log_big' : 'pictrue_log'\"\r\n\t\t\t\t\t\t\t\tv-if=\"item.activityH5 && item.activityH5.type === '3'\">拼团</span>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='text' :class='is_switch==true?\"\":\"on\"'>\r\n\t\t\t\t\t\t\t<view class='name line1'>{{item.storeName}}</view>\r\n\t\t\t\t\t\t\t<view class='money font-color' :class='is_switch==true?\"\":\"on\"'>￥<text\r\n\t\t\t\t\t\t\t\t\tclass='num'>{{item.price}}</text></view>\r\n\t\t\t\t\t\t\t<view class='vip acea-row row-between-wrapper' :class='is_switch==true?\"\":\"on\"'>\r\n\t\t\t\t\t\t\t\t<view class='vip-money' v-if=\"item.vip_price && item.vip_price > 0\">￥{{item.vip_price}}\r\n\t\t\t\t\t\t\t\t\t<image src='../../static/images/vip.png'></image>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view>已售{{Number(item.sales) + Number(item.ficti) || 0}}{{item.unitName}}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='loadingicon acea-row row-center-wrapper' v-if='productList.length > 0'>\r\n\t\t\t\t\t<text class='loading iconfont icon-jiazai' :hidden='loading==false'></text>{{loadTitle}}\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class='noCommodity' v-if=\"productList.length==0 && where.page > 1\">\r\n\t\t\t<view class='pictrue'>\r\n\t\t\t\t<image src='../../static/images/noShopper.png'></image>\r\n\t\t\t</view>\r\n\t\t\t<recommend :hostProduct=\"hostProduct\"></recommend>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tgetProductslist,\r\n\t\tgetProductHot\r\n\t} from '@/api/store.js';\r\n\timport recommend from '@/components/recommend';\r\n\timport {\r\n\t\tmapGetters\r\n\t} from \"vuex\";\r\n\timport {\r\n\t\tgoShopDetail\r\n\t} from '@/libs/order.js'\r\n\texport default {\r\n\t\tcomputed: mapGetters(['uid']),\r\n\t\tcomponents: {\r\n\t\t\trecommend\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tproductList: [],\r\n\t\t\t\tis_switch: true,\r\n\t\t\t\twhere: {\r\n\t\t\t\t\tkeyword: '',\r\n\t\t\t\t\tpriceOrder: '',\r\n\t\t\t\t\tsalesOrder: '',\r\n\t\t\t\t\tnews: 0,\r\n\t\t\t\t\tpage: 1,\r\n\t\t\t\t\tlimit: 20,\r\n\t\t\t\t\tcid: 0,\r\n\t\t\t\t},\r\n\t\t\t\tprice: 0,\r\n\t\t\t\tstock: 0,\r\n\t\t\t\tnows: false,\r\n\t\t\t\tloadend: false,\r\n\t\t\t\tloading: false,\r\n\t\t\t\tloadTitle: '加载更多',\r\n\t\t\t\ttitle: '',\r\n\t\t\t\thostProduct: [],\r\n\t\t\t\thotPage: 1,\r\n\t\t\t\thotLimit: 10,\r\n\t\t\t\thotScroll: false\r\n\t\t\t};\r\n\t\t},\r\n\t\tonLoad: function(options) {\r\n\t\t\tthis.$set(this.where, 'cid', options.cid || 0);\r\n\t\t\tthis.title = options.title || '';\r\n\t\t\tthis.$set(this.where, 'keyword', options.searchValue || '');\r\n\t\t\tthis.get_product_list();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgoback() {\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\treturn history.back();\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef H5\r\n\t\t\t\treturn uni.navigateBack({\r\n\t\t\t\t\tdelta: 1,\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\t// 去详情页\r\n\t\t\tgodDetail(item) {\r\n\t\t\t\tgoShopDetail(item, this.uid).then(res => {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: `/pages/goods_details/index?id=${item.id}`\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tChangswitch: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tthat.is_switch = !that.is_switch\r\n\t\t\t},\r\n\t\t\tsearchSubmit: function(e) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tthat.$set(that.where, 'keyword', e.detail.value);\r\n\t\t\t\tthat.loadend = false;\r\n\t\t\t\tthat.$set(that.where, 'page', 1)\r\n\t\t\t\tthis.get_product_list(true);\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 获取我的推荐\r\n\t\t\t */\r\n\t\t\tget_host_product: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tif (that.hotScroll) return\r\n\t\t\t\tgetProductHot(\r\n\t\t\t\t\tthat.hotPage,\r\n\t\t\t\t\tthat.hotLimit,\r\n\t\t\t\t).then(res => {\r\n\t\t\t\t\tthat.hotPage++\r\n\t\t\t\t\tthat.hotScroll = res.data.list.length < that.hotLimit\r\n\t\t\t\t\tthat.hostProduct = that.hostProduct.concat(res.data.list)\r\n\t\t\t\t\t// that.$set(that, 'hostProduct', res.data)\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t//点击事件处理\r\n\t\t\tset_where: function(e) {\r\n\t\t\t\tswitch (e) {\r\n\t\t\t\t\tcase 1:\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 2:\r\n\t\t\t\t\t\tif (this.price == 0) this.price = 1;\r\n\t\t\t\t\t\telse if (this.price == 1) this.price = 2;\r\n\t\t\t\t\t\telse if (this.price == 2) this.price = 0;\r\n\t\t\t\t\t\tthis.stock = 0;\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 3:\r\n\t\t\t\t\t\tif (this.stock == 0) this.stock = 1;\r\n\t\t\t\t\t\telse if (this.stock == 1) this.stock = 2;\r\n\t\t\t\t\t\telse if (this.stock == 2) this.stock = 0;\r\n\t\t\t\t\t\tthis.price = 0\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 4:\r\n\t\t\t\t\t\tthis.nows = !this.nows;\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t\tthis.loadend = false;\r\n\t\t\t\tthis.$set(this.where, 'page', 1);\r\n\t\t\t\tthis.get_product_list(true);\r\n\t\t\t},\r\n\t\t\t//设置where条件\r\n\t\t\tsetWhere: function() {\r\n\t\t\t\tif (this.price == 0) this.where.priceOrder = '';\r\n\t\t\t\telse if (this.price == 1) this.where.priceOrder = 'asc';\r\n\t\t\t\telse if (this.price == 2) this.where.priceOrder = 'desc';\r\n\t\t\t\tif (this.stock == 0) this.where.salesOrder = '';\r\n\t\t\t\telse if (this.stock == 1) this.where.salesOrder = 'asc';\r\n\t\t\t\telse if (this.stock == 2) this.where.salesOrder = 'desc';\r\n\t\t\t\tthis.where.news = this.nows ? 1 : 0;\r\n\t\t\t},\r\n\t\t\t//查找产品\r\n\t\t\tget_product_list: function(isPage) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tthat.setWhere();\r\n\t\t\t\tif (that.loadend) return;\r\n\t\t\t\tif (that.loading) return;\r\n\t\t\t\tif (isPage === true) that.$set(that, 'productList', []);\r\n\t\t\t\tthat.loading = true;\r\n\t\t\t\tthat.loadTitle = '';\r\n\t\t\t\tgetProductslist(that.where).then(res => {\r\n\t\t\t\t\tlet list = res.data.list;\r\n\t\t\t\t\tlet productList = that.$util.SplitArray(list, that.productList);\r\n\t\t\t\t\tlet loadend = list.length < that.where.limit;\r\n\t\t\t\t\tthat.loadend = loadend;\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tthat.loadTitle = loadend ? '已全部加载' : '加载更多';\r\n\t\t\t\t\tthat.$set(that, 'productList', productList);\r\n\t\t\t\t\tthat.$set(that.where, 'page', that.where.page + 1);\r\n\t\t\t\t\tif (that.productList.length === 0) {\r\n\t\t\t\t\t\tthis.get_host_product();\r\n\t\t\t\t\t} \r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tthat.loading = false;\r\n\t\t\t\t\tthat.loadTitle = '加载更多';\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t},\r\n\t\tonPullDownRefresh() {\r\n\r\n\t\t},\r\n\t\tonReachBottom() {\r\n\t\t\tif (this.productList.length > 0) {\r\n\t\t\t\tthis.get_product_list();\r\n\t\t\t} else {\r\n\t\t\t\tthis.get_host_product();\r\n\t\t\t}\r\n\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t.iconfont {\r\n\t\tcolor: #fff;\r\n\t}\r\n    .listBox{\r\n\t\tpadding: 20px 15px;\r\n\t\tmargin-top: 154rpx;\r\n\t}\r\n\t.productList .search {\r\n\t\twidth: 100%;\r\n\t\theight: 86rpx;\r\n\t\tpadding-left: 23rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\ttop: 0;\r\n\t\tz-index: 9;\r\n\t}\r\n\r\n\t.productList .search .input {\r\n\t\t// width: 640rpx;\r\n\t\theight: 60rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 50rpx;\r\n\t\tpadding: 0 20rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.productList .search .input input {\r\n\t\t/* #ifdef H5 */\r\n\t\twidth: 528rpx;\r\n\t\t/* #endif */\r\n\t\t/* #ifndef H5 */\r\n\t\twidth: 548rpx;\r\n\t\t/* #endif */\r\n\t\theight: 100%;\r\n\t\tfont-size: 26rpx;\r\n\t}\r\n\r\n\t.productList .search .input .placeholder {\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.productList .search .input .iconfont {\r\n\t\tfont-size: 35rpx;\r\n\t\tcolor: #555;\r\n\t}\r\n\r\n\t.productList .search .icon-pailie,\r\n\t.productList .search .icon-tupianpailie {\r\n\t\tcolor: #fff;\r\n\t\twidth: 62rpx;\r\n\t\tfont-size: 40rpx;\r\n\t\theight: 86rpx;\r\n\t\tline-height: 86rpx;\r\n\t}\r\n\r\n\t.productList .nav {\r\n\t\theight: 86rpx;\r\n\t\tcolor: #454545;\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\tfont-size: 28rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tmargin-top: 86rpx;\r\n\t\ttop: 0;\r\n\t\tz-index: 9;\r\n\t}\r\n\r\n\t.productList .nav .item {\r\n\t\twidth: 25%;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.productList .nav .item.font-color {\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.productList .nav .item image {\r\n\t\twidth: 15rpx;\r\n\t\theight: 19rpx;\r\n\t\tmargin-left: 10rpx;\r\n\t}\r\n\r\n\t.productList .list {\r\n\t\tpadding: 0 30rpx;\r\n\t\tmargin-top: 192rpx;\r\n\r\n\t}\r\n\r\n\t.productList .list.on {\r\n\t\tborder-radius: 14rpx;\r\n\t\tmargin-top: 0 !important;\r\n\t\tbackground-color: #fff;\r\n\t\tpadding: 40rpx 0 0 0;\r\n\t\t// margin: 20rpx 0;\r\n\t\t// background-color: #fff;\r\n\t}\r\n\r\n\t.productList .list .item {\r\n\t\twidth: 335rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 14rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.productList .list .item.on {\r\n\t\twidth: 100%;\r\n\t\tdisplay: flex;\r\n\t\tpadding: 0 24rpx 50rpx 24rpx;\r\n\t\tmargin: 0;\r\n\t\tborder-radius: 14rpx;\r\n\t}\r\n\r\n\t.productList .list .item .pictrue {\r\n\t\tposition: relative;\r\n\t\twidth: 100%;\r\n\t\theight: 335rpx;\r\n\t}\r\n\r\n\t.productList .list .item .pictrue.on {\r\n\t\twidth: 180rpx;\r\n\t\theight: 180rpx;\r\n\t}\r\n\r\n\t.productList .list .item .pictrue image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 20rpx 20rpx 0 0;\r\n\t}\r\n\r\n\t.productList .list .item .pictrue image.on {\r\n\t\tborder-radius: 6rpx;\r\n\t}\r\n\r\n\t.productList .list .item .text {\r\n\t\tpadding: 18rpx 20rpx;\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #222;\r\n\t}\r\n\r\n\t.productList .list .item .text.on {\r\n\t\twidth: 456rpx;\r\n\t\tpadding: 0 0 0 20rpx;\r\n\t}\r\n\r\n\t.productList .list .item .text .money {\r\n\t\tfont-size: 26rpx;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-top: 8rpx;\r\n\t}\r\n\r\n\t.productList .list .item .text .money.on {\r\n\t\tmargin-top: 50rpx;\r\n\t}\r\n\r\n\t.productList .list .item .text .money .num {\r\n\t\tfont-size: 34rpx;\r\n\t}\r\n\r\n\t.productList .list .item .text .vip {\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #aaa;\r\n\t\tmargin-top: 7rpx;\r\n\t}\r\n\r\n\t.productList .list .item .text .vip.on {\r\n\t\tmargin-top: 12rpx;\r\n\t}\r\n\r\n\t.productList .list .item .text .vip .vip-money {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #282828;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.productList .list .item .text .vip .vip-money image {\r\n\t\twidth: 46rpx;\r\n\t\theight: 21rpx;\r\n\t\tmargin-left: 4rpx;\r\n\t}\r\n\r\n\t.noCommodity {\r\n\t\tbackground-color: #fff;\r\n\t\tpadding-bottom: 30rpx;\r\n\t\tmargin-top: 172rpx;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=8b1a97ba&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=8b1a97ba&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363904131\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}