{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/goods_return/index.vue?8be0", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/goods_return/index.vue?102d", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/goods_return/index.vue?0590", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/goods_return/index.vue?9167", "uni-app:///pages/users/goods_return/index.vue", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/goods_return/index.vue?f709", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/goods_return/index.vue?a476"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "authorize", "data", "radius", "refund_reason_wap_img", "refund_reason_wap_imgPath", "orderInfo", "RefundArray", "index", "orderId", "isAuto", "isShowAuth", "computed", "watch", "is<PERSON>ogin", "handler", "deep", "onLoad", "title", "tab", "url", "methods", "getMarbleStyle", "sum", "angleOffset", "position", "left", "top", "width", "height", "transform", "background", "backgroundSize", "transition", "generateMarbles", "onLoadFun", "getOrderInfo", "that", "getRefundReason", "DelPic", "uploadpic", "name", "model", "pid", "subRefund", "value", "text", "refund_reason_wap_explain", "uni", "icon", "bindPickerChange"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACmM;AACnM,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7BA;AAAA;AAAA;AAAA;AAAkwB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACgFtxB;AACA;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAMA;EACAC;IAEAC;EAEA;EACAC;IACA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;EACAC;IACAC;MACAC;QACA;UACA;UACA;QACA;MACA;MACAC;IACA;EACA;EACAC;IACA;MAAAC;IAAA;MAAAC;MAAAC;IAAA;IACA;IACA;MACA;MACA;IACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;QAAA,OACAC;MAAA;;MAEA;MACA;MACA;;MAEA;MACA;;MAEA;MACA;MACA;QACA;QACAC;MACA;MAEA;MACA;;MAEA;MACA;MACA;MAEA;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;IACAC;MAEA;QAAA,OACAX;MAAA;MAEA;MACA;;MAEA;MACA;MACA;IACA;;IACAY;MACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;QACAC;QACAA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA;QACAD;MACA;IACA;IAEA;AACA;AACA;AACA;IACAE;MACA;QAAAF;MACAA;IACA;IACA;AACA;AACA;AACA;IACAG;MACA;MACAH;QAAAjB;QAAAqB;QAAAC;QAAAC;MAAA;QACAN;MACA;IACA;IAEA;AACA;AACA;IACAO;MAAA;MACA;QAAAC;MACA;MACA;MACA;QACAC;QACAC;QACA3C;QACA4C;MACA;QACA;UAAA9B;UAAA+B;QAAA;UAAA9B;UAAAC;QAAA;MACA;QACA;UAAAF;QAAA;MACA;IACA;IACAgC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACxPA;AAAA;AAAA;AAAA;AAAq8C,CAAgB,ovCAAG,EAAC,C;;;;;;;;;;;ACAz9C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/users/goods_return/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/users/goods_return/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=56ab2edc&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=56ab2edc&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"56ab2edc\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/users/goods_return/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=56ab2edc&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 =\n    _vm.orderInfo.type == 2\n      ? _vm.__map(_vm.orderInfo.orderInfoList, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var s0 = _vm.__get_style([_vm.getMarbleStyle(index)])\n          return {\n            $orig: $orig,\n            s0: s0,\n          }\n        })\n      : null\n  var g0 = _vm.refund_reason_wap_imgPath.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<form @submit=\"subRefund\" report-submit='true'>\r\n\t\t\t<view class='apply-return'>\r\n\t\t\t\t<view v-if=\"orderInfo.type == 2\">\r\n\t\t\t\t\t\r\n\t\t\t\t<div\r\n\t\t\t\t\tstyle=\"display: flex;justify-content: center;align-items: center;height: 500rpx;position: relative;background-color: white;border-radius: 14rpx;\">\r\n\t\t\t\t\t<div class=\"circle-container\" :style=\"{ width: radius * 2 + 'rpx', height: radius * 2 + 'rpx' }\">\r\n\t\t\t\t\t\t<div v-for=\"(item, index) in orderInfo.orderInfoList\" :key=\"index\" class=\"marble\"\r\n\t\t\t\t\t\t\t:style=\"[getMarbleStyle(index)]\">\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-else>\r\n\t\t\t\t\t<view class='goodsStyle acea-row row-between borRadius14'\r\n\t\t\t\t\t\tv-for=\"(item, index) in orderInfo.orderInfoList\" :key=\"index\">\r\n\t\t\t\t\t\t<view class='pictrue'>\r\n\t\t\t\t\t\t\t<image :src='item.image'></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='text acea-row row-between'>\r\n\t\t\t\t\t\t\t<view class='name line2'>{{ item.storeName }}</view>\r\n\t\t\t\t\t\t\t<view class='money'>\r\n\t\t\t\t\t\t\t\t<view>￥{{ item.price }}</view>\r\n\t\t\t\t\t\t\t\t<view class='num'>x{{ item.cartNum }}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='list borRadius14'>\r\n\t\t\t\t\t<view v-if=\"orderInfo.type != 2\" class='item acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t<view>退货件数</view>\r\n\t\t\t\t\t\t<view class='num'>{{ orderInfo.totalNum }}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t<view>退款金额</view>\r\n\t\t\t\t\t\t<view class='num'>￥{{ orderInfo.payPrice }}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item acea-row row-between-wrapper' @tap=\"toggleTab('region')\">\r\n\t\t\t\t\t\t<view>退款原因</view>\r\n\t\t\t\t\t\t<picker class='num' @change=\"bindPickerChange\" :value=\"index\" :range=\"RefundArray\">\r\n\t\t\t\t\t\t\t<view class=\"picker acea-row row-between-wrapper\">\r\n\t\t\t\t\t\t\t\t<view class='reason'>{{ RefundArray[index] }}</view>\r\n\t\t\t\t\t\t\t\t<text class='iconfont icon-jiantou'></text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</picker>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item textarea acea-row row-between'>\r\n\t\t\t\t\t\t<view>备注说明</view>\r\n\t\t\t\t\t\t<textarea placeholder='填写备注信息，100字以内' class='num' name=\"refund_reason_wap_explain\"\r\n\t\t\t\t\t\t\tplaceholder-class='填写备注信息，100字以内'></textarea>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='item acea-row row-between' style=\"border: none;\">\r\n\t\t\t\t\t\t<view class='title acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t\t<view>上传凭证</view>\r\n\t\t\t\t\t\t\t<view class='tip'>( 最多可上传3张 )</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='upload acea-row row-middle'>\r\n\t\t\t\t\t\t\t<view class='pictrue' v-for=\"(item, index) in refund_reason_wap_imgPath\" :key=\"index\">\r\n\t\t\t\t\t\t\t\t<image :src='item'></image>\r\n\t\t\t\t\t\t\t\t<view class='iconfont icon-guanbi1 font-color' @tap='DelPic(index)'></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class='pictrue acea-row row-center-wrapper row-column' @tap='uploadpic'\r\n\t\t\t\t\t\t\t\tv-if=\"refund_reason_wap_imgPath.length < 3\">\r\n\t\t\t\t\t\t\t\t<text class='iconfont icon-icon25201'></text>\r\n\t\t\t\t\t\t\t\t<view>上传凭证</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<button class='returnBnt bg-color' form-type=\"submit\">申请退款</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</form>\r\n\t\t<!-- #ifdef MP -->\r\n\t\t<!-- <authorize @onLoadFun=\"onLoadFun\" :isAuto=\"isAuto\" :isShowAuth=\"isShowAuth\" @authColse=\"authColse\"></authorize> -->\r\n\t\t<!-- #endif -->\r\n\t</view>\r\n</template>\r\n<script>\r\nimport { ordeRefundReason, orderRefundVerify, applyRefund } from '@/api/order.js';\r\nimport {\r\n\ttoLogin\r\n} from '@/libs/login.js';\r\nimport {\r\n\tmapGetters\r\n} from \"vuex\";\r\n// #ifdef MP\r\nimport authorize from '@/components/Authorize';\r\n// #endif\r\nexport default {\r\n\tcomponents: {\r\n\t\t// #ifdef MP\r\n\t\tauthorize\r\n\t\t// #endif\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tradius: 100, // 圆弧的半径\r\n\t\t\trefund_reason_wap_img: [],\r\n\t\t\trefund_reason_wap_imgPath: [],\r\n\t\t\torderInfo: {},\r\n\t\t\tRefundArray: [],\r\n\t\t\tindex: 0,\r\n\t\t\torderId: 0,\r\n\t\t\tisAuto: false, //没有授权的不会自动授权\r\n\t\t\tisShowAuth: false //是否隐藏授权\r\n\t\t};\r\n\t},\r\n\tcomputed: mapGetters(['isLogin']),\r\n\twatch: {\r\n\t\tisLogin: {\r\n\t\t\thandler: function (newV, oldV) {\r\n\t\t\t\tif (newV) {\r\n\t\t\t\t\tthis.getOrderInfo();\r\n\t\t\t\t\tthis.getRefundReason();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tdeep: true\r\n\t\t}\r\n\t},\r\n\tonLoad: function (options) {\r\n\t\tif (!options.orderId) return this.$util.Tips({ title: '缺少订单id,无法退款' }, { tab: 3, url: 1 });\r\n\t\tthis.orderId = options.orderId;\r\n\t\tif (this.isLogin) {\r\n\t\t\tthis.getOrderInfo();\r\n\t\t\tthis.getRefundReason();\r\n\t\t} else {\r\n\t\t\ttoLogin();\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\tgetMarbleStyle(index) {\r\n\t\t\t// Calculate total circumference based on bead sizes\r\n\t\t\tconst totalBeadsWidth = this.orderInfo.orderInfoList.reduce((sum, bead) =>\r\n\t\t\t\tsum + (parseFloat(bead.width) * 3), 0);\r\n\r\n\t\t\t// Add small gaps between beads (5% of average bead size)\r\n\t\t\tconst spacing = (totalBeadsWidth / this.orderInfo.orderInfoList.length) * 0.05;\r\n\t\t\tconst circumference = totalBeadsWidth + (spacing * this.orderInfo.orderInfoList.length);\r\n\r\n\t\t\t// Calculate radius to match circle border\r\n\t\t\tconst dynamicRadius = circumference / (2 * Math.PI);\r\n\r\n\t\t\t// Calculate position on circle for current bead\r\n\t\t\tlet angleOffset = 0;\r\n\t\t\tfor (let i = 0; i < index; i++) {\r\n\t\t\t\tconst prevBeadWidth = parseFloat(this.orderInfo.orderInfoList[i].width) * 3;\r\n\t\t\t\tangleOffset += (prevBeadWidth + spacing) / dynamicRadius;\r\n\t\t\t}\r\n\r\n\t\t\tconst currentBeadWidth = parseFloat(this.orderInfo.orderInfoList[index].width) * 3;\r\n\t\t\tconst angle = angleOffset + (currentBeadWidth / 2) / dynamicRadius;\r\n\r\n\t\t\t// Position bead exactly on circle circumference\r\n\t\t\tconst x = dynamicRadius * (1 + Math.cos(angle));\r\n\t\t\tconst y = dynamicRadius * (1 + Math.sin(angle));\r\n\r\n\t\t\treturn {\r\n\t\t\t\tposition: 'absolute',\r\n\t\t\t\tleft: (x - currentBeadWidth / 2) + 'rpx',\r\n\t\t\t\ttop: (y - (parseFloat(this.orderInfo.orderInfoList[index].height) * 3 / 2)) + 'rpx',\r\n\t\t\t\twidth: currentBeadWidth + 'rpx',\r\n\t\t\t\theight: (parseFloat(this.orderInfo.orderInfoList[index].height) * 3) + 'rpx',\r\n\t\t\t\ttransform: `rotate(${angle + Math.PI / 2}rad)`,\r\n\t\t\t\tbackground: `url('${this.orderInfo.orderInfoList[index].image}') no-repeat center`,\r\n\t\t\t\tbackgroundSize: 'cover',\r\n\t\t\t\ttransition: 'all 0.3s ease'\r\n\t\t\t};\r\n\t\t},\r\n\t\tgenerateMarbles() {\r\n\r\n\t\t\tconst totalBeadsWidth = this.orderInfo.orderInfoList.reduce((sum, bead) =>\r\n\t\t\t\tsum + (parseFloat(bead.width) * 3), 0);\r\n\r\n\t\t\tconst spacing = (totalBeadsWidth / this.orderInfo.orderInfoList.length) * 0.05;\r\n\t\t\tconst totalCircumference = totalBeadsWidth + (spacing * this.orderInfo.orderInfoList.length);\r\n\r\n\t\t\t// Set radius to match circle border\r\n\t\t\tthis.radius = totalCircumference / (2 * Math.PI);\r\n\t\t\t//   this.realRadius = (totalCircumference / Math.PI).toFixed(2);\r\n\t\t},\r\n\t\tonLoadFun: function () {\r\n\t\t\tthis.getOrderInfo();\r\n\t\t\tthis.getRefundReason();\r\n\t\t},\r\n\t\t/**\r\n\t\t   * 获取订单详情\r\n\t\t   * \r\n\t\t  */\r\n\t\tgetOrderInfo: function () {\r\n\t\t\tlet that = this;\r\n\t\t\tapplyRefund(that.orderId).then(res => {\r\n\t\t\t\tthat.$set(that, 'orderInfo', res.data);\r\n\t\t\t\tthat.generateMarbles();\r\n\t\t\t});\r\n\t\t},\r\n\t\t/**\r\n\t\t * 获取退款理由\r\n\t\t*/\r\n\t\tgetRefundReason: function () {\r\n\t\t\tlet that = this;\r\n\t\t\tordeRefundReason().then(res => {\r\n\t\t\t\tthat.$set(that, 'RefundArray', res.data);\r\n\t\t\t})\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 删除图片\r\n\t\t * \r\n\t\t*/\r\n\t\tDelPic: function (e) {\r\n\t\t\tlet index = e, that = this;\r\n\t\t\tthat.refund_reason_wap_imgPath.splice(index, 1);\r\n\t\t},\r\n\t\t/**\r\n\t\t * 上传文件\r\n\t\t * \r\n\t\t*/\r\n\t\tuploadpic: function () {\r\n\t\t\tlet that = this;\r\n\t\t\tthat.$util.uploadImageOne({ url: 'user/upload/image', name: 'multipart', model: \"product\", pid: 1 }, function (res) {\r\n\t\t\t\tthat.refund_reason_wap_imgPath.push(res.data.url);\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\t/**\r\n\t\t * 申请退货\r\n\t\t*/\r\n\t\tsubRefund: function (e) {\r\n\t\t\tlet that = this, value = e.detail.value;\r\n\t\t\t//收集form表单\r\n\t\t\t// if (!value.refund_reason_wap_explain) return this.$util.Tips({title:'请输入退款原因'});\r\n\t\t\torderRefundVerify({\r\n\t\t\t\ttext: that.RefundArray[that.index] || '',\r\n\t\t\t\trefund_reason_wap_explain: value.refund_reason_wap_explain,\r\n\t\t\t\trefund_reason_wap_img: that.refund_reason_wap_imgPath.join(','),\r\n\t\t\t\tuni: that.orderId\r\n\t\t\t}).then(res => {\r\n\t\t\t\treturn this.$util.Tips({ title: '申请成功', icon: 'success' }, { tab: 5, url: '/pages/users/user_return_list/index?isT=1' });\r\n\t\t\t}).catch(err => {\r\n\t\t\t\treturn this.$util.Tips({ title: err });\r\n\t\t\t})\r\n\t\t},\r\n\t\tbindPickerChange: function (e) {\r\n\t\t\tthis.$set(this, 'index', e.detail.value);\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.apply-return {\r\n\tpadding: 20rpx 30rpx 70rpx 30rpx;\r\n}\r\n\r\n.apply-return .list {\r\n\tbackground-color: #fff;\r\n\tmargin-top: 18rpx;\r\n\tpadding: 0 24rpx 70rpx 24rpx;\r\n}\r\n\r\n.apply-return .list .item {\r\n\tmin-height: 90rpx;\r\n\tborder-bottom: 1rpx solid #eee;\r\n\tfont-size: 30rpx;\r\n\tcolor: #333;\r\n}\r\n\r\n.apply-return .list .item .num {\r\n\tcolor: #282828;\r\n\twidth: 427rpx;\r\n\ttext-align: right;\r\n}\r\n\r\n.apply-return .list .item .num .picker .reason {\r\n\twidth: 385rpx;\r\n}\r\n\r\n.apply-return .list .item .num .picker .iconfont {\r\n\tcolor: #666;\r\n\tfont-size: 30rpx;\r\n\tmargin-top: 2rpx;\r\n}\r\n\r\n.apply-return .list .item.textarea {\r\n\tpadding: 24rpx 0;\r\n}\r\n\r\n.apply-return .list .item textarea {\r\n\theight: 100rpx;\r\n\tfont-size: 30rpx;\r\n}\r\n\r\n.apply-return .list .item .placeholder {\r\n\tcolor: #bbb;\r\n}\r\n\r\n.apply-return .list .item .title {\r\n\theight: 95rpx;\r\n\twidth: 100%;\r\n}\r\n\r\n.apply-return .list .item .title .tip {\r\n\tfont-size: 30rpx;\r\n\tcolor: #bbb;\r\n}\r\n\r\n.apply-return .list .item .upload {\r\n\tpadding-bottom: 36rpx;\r\n}\r\n\r\n.apply-return .list .item .upload .pictrue {\r\n\tborder-radius: 14rpx;\r\n\tmargin: 22rpx 23rpx 0 0;\r\n\twidth: 156rpx;\r\n\theight: 156rpx;\r\n\tposition: relative;\r\n\tfont-size: 24rpx;\r\n\tcolor: #bbb;\r\n}\r\n\r\n.apply-return .list .item .upload .pictrue:nth-of-type(4n) {\r\n\tmargin-right: 0;\r\n}\r\n\r\n.apply-return .list .item .upload .pictrue image {\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tborder-radius: 14rpx;\r\n}\r\n\r\n.apply-return .list .item .upload .pictrue .icon-guanbi1 {\r\n\tposition: absolute;\r\n\tfont-size: 45rpx;\r\n\ttop: -10rpx;\r\n\tright: -10rpx;\r\n}\r\n\r\n.apply-return .list .item .upload .pictrue .icon-icon25201 {\r\n\tcolor: #bfbfbf;\r\n\tfont-size: 50rpx;\r\n}\r\n\r\n.apply-return .list .item .upload .pictrue:nth-last-child(1) {\r\n\tborder: 1rpx solid #ddd;\r\n\tbox-sizing: border-box;\r\n}\r\n\r\n.apply-return .returnBnt {\r\n\tfont-size: 32rpx;\r\n\tcolor: #fff;\r\n\twidth: 100%;\r\n\theight: 86rpx;\r\n\tborder-radius: 50rpx;\r\n\ttext-align: center;\r\n\tline-height: 86rpx;\r\n\tmargin: 43rpx auto;\r\n}\r\n.circle-container {\r\n\tposition: relative;\r\n\tborder-radius: 50%;\r\n\tborder: gray 1px solid;\r\n\ttransition: all 1s;\r\n}\r\n\r\n.marble {\r\n\tposition: absolute;\r\n\ttransition: all 1s;\r\n}\r\n\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=56ab2edc&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=56ab2edc&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363903507\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}