{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\element-ui\\lib\\popover.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\element-ui\\lib\\popover.js", "mtime": 1753666303569}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}], "contextDependencies": [], "result": ["\"use strict\";\n\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nmodule.exports = /******/function (modules) {\n  // webpackBootstrap\n  /******/ // The module cache\n  /******/\n  var installedModules = {};\n  /******/\n  /******/ // The require function\n  /******/\n  function __webpack_require__(moduleId) {\n    /******/\n    /******/ // Check if module is in cache\n    /******/if (installedModules[moduleId]) {\n      /******/return installedModules[moduleId].exports;\n      /******/\n    }\n    /******/ // Create a new module (and put it into the cache)\n    /******/\n    var module = installedModules[moduleId] = {\n      /******/i: moduleId,\n      /******/l: false,\n      /******/exports: {}\n      /******/\n    };\n    /******/\n    /******/ // Execute the module function\n    /******/\n    modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n    /******/\n    /******/ // Flag the module as loaded\n    /******/\n    module.l = true;\n    /******/\n    /******/ // Return the exports of the module\n    /******/\n    return module.exports;\n    /******/\n  }\n  /******/\n  /******/\n  /******/ // expose the modules object (__webpack_modules__)\n  /******/\n  __webpack_require__.m = modules;\n  /******/\n  /******/ // expose the module cache\n  /******/\n  __webpack_require__.c = installedModules;\n  /******/\n  /******/ // define getter function for harmony exports\n  /******/\n  __webpack_require__.d = function (exports, name, getter) {\n    /******/if (!__webpack_require__.o(exports, name)) {\n      /******/Object.defineProperty(exports, name, {\n        enumerable: true,\n        get: getter\n      });\n      /******/\n    }\n    /******/\n  };\n  /******/\n  /******/ // define __esModule on exports\n  /******/\n  __webpack_require__.r = function (exports) {\n    /******/if (typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n      /******/Object.defineProperty(exports, Symbol.toStringTag, {\n        value: 'Module'\n      });\n      /******/\n    }\n    /******/\n    Object.defineProperty(exports, '__esModule', {\n      value: true\n    });\n    /******/\n  };\n  /******/\n  /******/ // create a fake namespace object\n  /******/ // mode & 1: value is a module id, require it\n  /******/ // mode & 2: merge all properties of value into the ns\n  /******/ // mode & 4: return value when already ns object\n  /******/ // mode & 8|1: behave like require\n  /******/\n  __webpack_require__.t = function (value, mode) {\n    /******/if (mode & 1) value = __webpack_require__(value);\n    /******/\n    if (mode & 8) return value;\n    /******/\n    if (mode & 4 && _typeof(value) === 'object' && value && value.__esModule) return value;\n    /******/\n    var ns = Object.create(null);\n    /******/\n    __webpack_require__.r(ns);\n    /******/\n    Object.defineProperty(ns, 'default', {\n      enumerable: true,\n      value: value\n    });\n    /******/\n    if (mode & 2 && typeof value != 'string') for (var key in value) __webpack_require__.d(ns, key, function (key) {\n      return value[key];\n    }.bind(null, key));\n    /******/\n    return ns;\n    /******/\n  };\n  /******/\n  /******/ // getDefaultExport function for compatibility with non-harmony modules\n  /******/\n  __webpack_require__.n = function (module) {\n    /******/var getter = module && module.__esModule ? /******/function getDefault() {\n      return module['default'];\n    } : /******/function getModuleExports() {\n      return module;\n    };\n    /******/\n    __webpack_require__.d(getter, 'a', getter);\n    /******/\n    return getter;\n    /******/\n  };\n  /******/\n  /******/ // Object.prototype.hasOwnProperty.call\n  /******/\n  __webpack_require__.o = function (object, property) {\n    return Object.prototype.hasOwnProperty.call(object, property);\n  };\n  /******/\n  /******/ // __webpack_public_path__\n  /******/\n  __webpack_require__.p = \"/dist/\";\n  /******/\n  /******/\n  /******/ // Load entry module and return exports\n  /******/\n  return __webpack_require__(__webpack_require__.s = 74);\n  /******/\n}\n/************************************************************************/\n/******/({\n  /***/0: (/***/function _(module, __webpack_exports__, __webpack_require__) {\n    \"use strict\";\n\n    /* harmony export (binding) */\n    __webpack_require__.d(__webpack_exports__, \"a\", function () {\n      return normalizeComponent;\n    });\n    /* globals __VUE_SSR_CONTEXT__ */\n\n    // IMPORTANT: Do NOT use ES2015 features in this file (except for modules).\n    // This module is a runtime utility for cleaner component module output and will\n    // be included in the final webpack user bundle.\n\n    function normalizeComponent(scriptExports, render, staticRenderFns, functionalTemplate, injectStyles, scopeId, moduleIdentifier, /* server only */\n    shadowMode /* vue-cli only */) {\n      // Vue.extend constructor export interop\n      var options = typeof scriptExports === 'function' ? scriptExports.options : scriptExports;\n\n      // render functions\n      if (render) {\n        options.render = render;\n        options.staticRenderFns = staticRenderFns;\n        options._compiled = true;\n      }\n\n      // functional template\n      if (functionalTemplate) {\n        options.functional = true;\n      }\n\n      // scopedId\n      if (scopeId) {\n        options._scopeId = 'data-v-' + scopeId;\n      }\n      var hook;\n      if (moduleIdentifier) {\n        // server build\n        hook = function hook(context) {\n          // 2.3 injection\n          context = context ||\n          // cached call\n          this.$vnode && this.$vnode.ssrContext ||\n          // stateful\n          this.parent && this.parent.$vnode && this.parent.$vnode.ssrContext; // functional\n          // 2.2 with runInNewContext: true\n          if (!context && typeof __VUE_SSR_CONTEXT__ !== 'undefined') {\n            context = __VUE_SSR_CONTEXT__;\n          }\n          // inject component styles\n          if (injectStyles) {\n            injectStyles.call(this, context);\n          }\n          // register component module identifier for async chunk inferrence\n          if (context && context._registeredComponents) {\n            context._registeredComponents.add(moduleIdentifier);\n          }\n        };\n        // used by ssr in case component is cached and beforeCreate\n        // never gets called\n        options._ssrRegister = hook;\n      } else if (injectStyles) {\n        hook = shadowMode ? function () {\n          injectStyles.call(this, this.$root.$options.shadowRoot);\n        } : injectStyles;\n      }\n      if (hook) {\n        if (options.functional) {\n          // for template-only hot-reload because in that case the render fn doesn't\n          // go through the normalizer\n          options._injectStyles = hook;\n          // register for functioal component in vue file\n          var originalRender = options.render;\n          options.render = function renderWithStyleInjection(h, context) {\n            hook.call(context);\n            return originalRender(h, context);\n          };\n        } else {\n          // inject component registration as beforeCreate hook\n          var existing = options.beforeCreate;\n          options.beforeCreate = existing ? [].concat(existing, hook) : [hook];\n        }\n      }\n      return {\n        exports: scriptExports,\n        options: options\n      };\n    }\n\n    /***/\n  }),\n  /***/2: (/***/function _(module, exports) {\n    module.exports = require(\"element-ui/lib/utils/dom\");\n\n    /***/\n  }),\n  /***/3: (/***/function _(module, exports) {\n    module.exports = require(\"element-ui/lib/utils/util\");\n\n    /***/\n  }),\n  /***/5: (/***/function _(module, exports) {\n    module.exports = require(\"element-ui/lib/utils/vue-popper\");\n\n    /***/\n  }),\n  /***/7: (/***/function _(module, exports) {\n    module.exports = require(\"vue\");\n\n    /***/\n  }),\n  /***/74: (/***/function _(module, __webpack_exports__, __webpack_require__) {\n    \"use strict\";\n\n    __webpack_require__.r(__webpack_exports__);\n\n    // CONCATENATED MODULE: ./node_modules/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/vue-loader/lib??vue-loader-options!./packages/popover/src/main.vue?vue&type=template&id=52060272&\n    var render = function render() {\n      var _vm = this;\n      var _h = _vm.$createElement;\n      var _c = _vm._self._c || _h;\n      return _c(\"span\", [_c(\"transition\", {\n        attrs: {\n          name: _vm.transition\n        },\n        on: {\n          \"after-enter\": _vm.handleAfterEnter,\n          \"after-leave\": _vm.handleAfterLeave\n        }\n      }, [_c(\"div\", {\n        directives: [{\n          name: \"show\",\n          rawName: \"v-show\",\n          value: !_vm.disabled && _vm.showPopper,\n          expression: \"!disabled && showPopper\"\n        }],\n        ref: \"popper\",\n        staticClass: \"el-popover el-popper\",\n        class: [_vm.popperClass, _vm.content && \"el-popover--plain\"],\n        style: {\n          width: _vm.width + \"px\"\n        },\n        attrs: {\n          role: \"tooltip\",\n          id: _vm.tooltipId,\n          \"aria-hidden\": _vm.disabled || !_vm.showPopper ? \"true\" : \"false\"\n        }\n      }, [_vm.title ? _c(\"div\", {\n        staticClass: \"el-popover__title\",\n        domProps: {\n          textContent: _vm._s(_vm.title)\n        }\n      }) : _vm._e(), _vm._t(\"default\", [_vm._v(_vm._s(_vm.content))])], 2)]), _vm._t(\"reference\")], 2);\n    };\n    var staticRenderFns = [];\n    render._withStripped = true;\n\n    // CONCATENATED MODULE: ./packages/popover/src/main.vue?vue&type=template&id=52060272&\n\n    // EXTERNAL MODULE: external \"element-ui/lib/utils/vue-popper\"\n    var vue_popper_ = __webpack_require__(5);\n    var vue_popper_default = /*#__PURE__*/__webpack_require__.n(vue_popper_);\n\n    // EXTERNAL MODULE: external \"element-ui/lib/utils/dom\"\n    var dom_ = __webpack_require__(2);\n\n    // EXTERNAL MODULE: external \"element-ui/lib/utils/util\"\n    var util_ = __webpack_require__(3);\n\n    // CONCATENATED MODULE: ./node_modules/babel-loader/lib!./node_modules/vue-loader/lib??vue-loader-options!./packages/popover/src/main.vue?vue&type=script&lang=js&\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n    //\n\n    /* harmony default export */\n    var mainvue_type_script_lang_js_ = {\n      name: 'ElPopover',\n      mixins: [vue_popper_default.a],\n      props: {\n        trigger: {\n          type: String,\n          default: 'click',\n          validator: function validator(value) {\n            return ['click', 'focus', 'hover', 'manual'].indexOf(value) > -1;\n          }\n        },\n        openDelay: {\n          type: Number,\n          default: 0\n        },\n        closeDelay: {\n          type: Number,\n          default: 200\n        },\n        title: String,\n        disabled: Boolean,\n        content: String,\n        reference: {},\n        popperClass: String,\n        width: {},\n        visibleArrow: {\n          default: true\n        },\n        arrowOffset: {\n          type: Number,\n          default: 0\n        },\n        transition: {\n          type: String,\n          default: 'fade-in-linear'\n        },\n        tabindex: {\n          type: Number,\n          default: 0\n        }\n      },\n      computed: {\n        tooltipId: function tooltipId() {\n          return 'el-popover-' + Object(util_[\"generateId\"])();\n        }\n      },\n      watch: {\n        showPopper: function showPopper(val) {\n          if (this.disabled) {\n            return;\n          }\n          val ? this.$emit('show') : this.$emit('hide');\n        }\n      },\n      mounted: function mounted() {\n        var _this = this;\n        var reference = this.referenceElm = this.reference || this.$refs.reference;\n        var popper = this.popper || this.$refs.popper;\n        if (!reference && this.$slots.reference && this.$slots.reference[0]) {\n          reference = this.referenceElm = this.$slots.reference[0].elm;\n        }\n        // 可访问性\n        if (reference) {\n          Object(dom_[\"addClass\"])(reference, 'el-popover__reference');\n          reference.setAttribute('aria-describedby', this.tooltipId);\n          reference.setAttribute('tabindex', this.tabindex); // tab序列\n          popper.setAttribute('tabindex', 0);\n          if (this.trigger !== 'click') {\n            Object(dom_[\"on\"])(reference, 'focusin', function () {\n              _this.handleFocus();\n              var instance = reference.__vue__;\n              if (instance && typeof instance.focus === 'function') {\n                instance.focus();\n              }\n            });\n            Object(dom_[\"on\"])(popper, 'focusin', this.handleFocus);\n            Object(dom_[\"on\"])(reference, 'focusout', this.handleBlur);\n            Object(dom_[\"on\"])(popper, 'focusout', this.handleBlur);\n          }\n          Object(dom_[\"on\"])(reference, 'keydown', this.handleKeydown);\n          Object(dom_[\"on\"])(reference, 'click', this.handleClick);\n        }\n        if (this.trigger === 'click') {\n          Object(dom_[\"on\"])(reference, 'click', this.doToggle);\n          Object(dom_[\"on\"])(document, 'click', this.handleDocumentClick);\n        } else if (this.trigger === 'hover') {\n          Object(dom_[\"on\"])(reference, 'mouseenter', this.handleMouseEnter);\n          Object(dom_[\"on\"])(popper, 'mouseenter', this.handleMouseEnter);\n          Object(dom_[\"on\"])(reference, 'mouseleave', this.handleMouseLeave);\n          Object(dom_[\"on\"])(popper, 'mouseleave', this.handleMouseLeave);\n        } else if (this.trigger === 'focus') {\n          if (this.tabindex < 0) {\n            console.warn('[Element Warn][Popover]a negative taindex means that the element cannot be focused by tab key');\n          }\n          if (reference.querySelector('input, textarea')) {\n            Object(dom_[\"on\"])(reference, 'focusin', this.doShow);\n            Object(dom_[\"on\"])(reference, 'focusout', this.doClose);\n          } else {\n            Object(dom_[\"on\"])(reference, 'mousedown', this.doShow);\n            Object(dom_[\"on\"])(reference, 'mouseup', this.doClose);\n          }\n        }\n      },\n      beforeDestroy: function beforeDestroy() {\n        this.cleanup();\n      },\n      deactivated: function deactivated() {\n        this.cleanup();\n      },\n      methods: {\n        doToggle: function doToggle() {\n          this.showPopper = !this.showPopper;\n        },\n        doShow: function doShow() {\n          this.showPopper = true;\n        },\n        doClose: function doClose() {\n          this.showPopper = false;\n        },\n        handleFocus: function handleFocus() {\n          Object(dom_[\"addClass\"])(this.referenceElm, 'focusing');\n          if (this.trigger === 'click' || this.trigger === 'focus') this.showPopper = true;\n        },\n        handleClick: function handleClick() {\n          Object(dom_[\"removeClass\"])(this.referenceElm, 'focusing');\n        },\n        handleBlur: function handleBlur() {\n          Object(dom_[\"removeClass\"])(this.referenceElm, 'focusing');\n          if (this.trigger === 'click' || this.trigger === 'focus') this.showPopper = false;\n        },\n        handleMouseEnter: function handleMouseEnter() {\n          var _this2 = this;\n          clearTimeout(this._timer);\n          if (this.openDelay) {\n            this._timer = setTimeout(function () {\n              _this2.showPopper = true;\n            }, this.openDelay);\n          } else {\n            this.showPopper = true;\n          }\n        },\n        handleKeydown: function handleKeydown(ev) {\n          if (ev.keyCode === 27 && this.trigger !== 'manual') {\n            // esc\n            this.doClose();\n          }\n        },\n        handleMouseLeave: function handleMouseLeave() {\n          var _this3 = this;\n          clearTimeout(this._timer);\n          if (this.closeDelay) {\n            this._timer = setTimeout(function () {\n              _this3.showPopper = false;\n            }, this.closeDelay);\n          } else {\n            this.showPopper = false;\n          }\n        },\n        handleDocumentClick: function handleDocumentClick(e) {\n          var reference = this.reference || this.$refs.reference;\n          var popper = this.popper || this.$refs.popper;\n          if (!reference && this.$slots.reference && this.$slots.reference[0]) {\n            reference = this.referenceElm = this.$slots.reference[0].elm;\n          }\n          if (!this.$el || !reference || this.$el.contains(e.target) || reference.contains(e.target) || !popper || popper.contains(e.target)) return;\n          this.showPopper = false;\n        },\n        handleAfterEnter: function handleAfterEnter() {\n          this.$emit('after-enter');\n        },\n        handleAfterLeave: function handleAfterLeave() {\n          this.$emit('after-leave');\n          this.doDestroy();\n        },\n        cleanup: function cleanup() {\n          if (this.openDelay || this.closeDelay) {\n            clearTimeout(this._timer);\n          }\n        }\n      },\n      destroyed: function destroyed() {\n        var reference = this.reference;\n        Object(dom_[\"off\"])(reference, 'click', this.doToggle);\n        Object(dom_[\"off\"])(reference, 'mouseup', this.doClose);\n        Object(dom_[\"off\"])(reference, 'mousedown', this.doShow);\n        Object(dom_[\"off\"])(reference, 'focusin', this.doShow);\n        Object(dom_[\"off\"])(reference, 'focusout', this.doClose);\n        Object(dom_[\"off\"])(reference, 'mousedown', this.doShow);\n        Object(dom_[\"off\"])(reference, 'mouseup', this.doClose);\n        Object(dom_[\"off\"])(reference, 'mouseleave', this.handleMouseLeave);\n        Object(dom_[\"off\"])(reference, 'mouseenter', this.handleMouseEnter);\n        Object(dom_[\"off\"])(document, 'click', this.handleDocumentClick);\n      }\n    };\n    // CONCATENATED MODULE: ./packages/popover/src/main.vue?vue&type=script&lang=js&\n    /* harmony default export */\n    var src_mainvue_type_script_lang_js_ = mainvue_type_script_lang_js_;\n    // EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js\n    var componentNormalizer = __webpack_require__(0);\n\n    // CONCATENATED MODULE: ./packages/popover/src/main.vue\n\n    /* normalize component */\n\n    var component = Object(componentNormalizer[\"a\" /* default */])(src_mainvue_type_script_lang_js_, render, staticRenderFns, false, null, null, null);\n\n    /* hot reload */\n    if (false) {\n      var api;\n    }\n    component.options.__file = \"packages/popover/src/main.vue\";\n    /* harmony default export */\n    var main = component.exports;\n    // CONCATENATED MODULE: ./packages/popover/src/directive.js\n    var getReference = function getReference(el, binding, vnode) {\n      var _ref = binding.expression ? binding.value : binding.arg;\n      var popper = vnode.context.$refs[_ref];\n      if (popper) {\n        if (Array.isArray(popper)) {\n          popper[0].$refs.reference = el;\n        } else {\n          popper.$refs.reference = el;\n        }\n      }\n    };\n\n    /* harmony default export */\n    var directive = {\n      bind: function bind(el, binding, vnode) {\n        getReference(el, binding, vnode);\n      },\n      inserted: function inserted(el, binding, vnode) {\n        getReference(el, binding, vnode);\n      }\n    };\n    // EXTERNAL MODULE: external \"vue\"\n    var external_vue_ = __webpack_require__(7);\n    var external_vue_default = /*#__PURE__*/__webpack_require__.n(external_vue_);\n\n    // CONCATENATED MODULE: ./packages/popover/index.js\n\n    external_vue_default.a.directive('popover', directive);\n\n    /* istanbul ignore next */\n    main.install = function (Vue) {\n      Vue.directive('popover', directive);\n      Vue.component(main.name, main);\n    };\n    main.directive = directive;\n\n    /* harmony default export */\n    var popover = __webpack_exports__[\"default\"] = main;\n\n    /***/\n  })\n\n  /******/\n});", null]}