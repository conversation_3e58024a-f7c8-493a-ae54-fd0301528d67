
page {
	height: 100%;
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.appBox.data-v-01db255e {
  background-color: #fff;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
}
.shading.data-v-01db255e {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}
.shading image.data-v-01db255e {
  width: 180rpx;
  height: 180rpx;
}
page.data-v-01db255e {
  background-color: #fff !important;
}
.ChangePassword .phone.data-v-01db255e {
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  margin-top: 55rpx;
}
.ChangePassword .list.data-v-01db255e {
  width: 580rpx;
  margin: 53rpx auto 0 auto;
}
.ChangePassword .list .item.data-v-01db255e {
  width: 100%;
  height: 110rpx;
  border-bottom: 2rpx solid #f0f0f0;
}
.ChangePassword .list .item input.data-v-01db255e {
  width: 100%;
  height: 100%;
  font-size: 32rpx;
}
.ChangePassword .list .item .placeholder.data-v-01db255e {
  color: #b9b9bc;
}
.ChangePassword .list .item input.codeIput.data-v-01db255e {
  width: 340rpx;
}
.ChangePassword .list .item .code.data-v-01db255e {
  font-size: 32rpx;
  background-color: #fff;
}
.ChangePassword .list .item .code.on.data-v-01db255e {
  color: #b9b9bc !important;
}
.ChangePassword .confirmBnt.data-v-01db255e {
  font-size: 32rpx;
  width: 580rpx;
  height: 90rpx;
  border-radius: 45rpx;
  color: #fff;
  margin: 92rpx auto 0 auto;
  text-align: center;
  line-height: 90rpx;
}

