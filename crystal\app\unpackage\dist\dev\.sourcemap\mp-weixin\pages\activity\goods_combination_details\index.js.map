{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/goods_combination_details/index.vue?d01b", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/goods_combination_details/index.vue?1ab3", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/goods_combination_details/index.vue?1994", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/goods_combination_details/index.vue?1664", "uni-app:///pages/activity/goods_combination_details/index.vue", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/goods_combination_details/index.vue?389e", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/goods_combination_details/index.vue?5293"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "shareRedPackets", "productConSwiper", "authorize", "home", "userEvaluation", "countDown", "computed", "data", "bgColor", "userCollect", "dataShow", "navH", "id", "userInfo", "itemNew", "indicatorDots", "circular", "autoplay", "interval", "duration", "attribute", "cartAttr", "productAttr", "productSelect", "productValue", "isOpen", "attr", "attrValue", "AllIndex", "maxAllIndex", "replyC<PERSON>ce", "limitNum", "timeer", "iSplus", "isState", "img", "table", "video", "watch", "is<PERSON>ogin", "handler", "deep", "onLoad", "query", "select", "boundingClientRect", "exec", "uni", "success", "that", "app", "setTimeout", "options", "title", "icon", "mask", "methods", "getProductReplyCount", "getProductReplyList", "kefuClick", "location", "closePosters", "closeChange", "showAll", "hide<PERSON>ll", "auth<PERSON><PERSON><PERSON>", "iptCartNum", "returns", "combinationDetail", "attrName", "attrV<PERSON>ues", "isDel", "productId", "type", "tab", "DefaultSelect", "value", "self", "infoScroll", "topArr", "heightArr", "onLoadFun", "e", "selecAttr", "onMyEvent", "ChangeCartNum", "num", "arrMin", "attrVal", "indexn", "ChangeAttr", "goProduct", "url", "goCat", "$util", "combinationId", "cartNum", "productAttrUnique", "isNew", "setCollect", "listenerActionSheet", "listenerActionClose", "posterImageClose", "setDomain", "downloadFilestoreImage", "fail", "downloadFileAppCode", "downloadFilePromotionCode", "scombinationCode", "successFn", "getImageBase64", "goFriend", "go<PERSON><PERSON><PERSON>", "arrImagesUrlTop", "otPrice", "getQrcode", "pid", "path", "make", "uQRCode", "canvasId", "text", "size", "margin", "complete", "savePosterPath", "scope", "filePath", "setShareInfoStatus", "href", "desc", "link", "imgUrl", "configAppMessage", "scroll", "scrollY", "opacity", "tap", "onShareAppMessage", "imageUrl"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACmM;AACnM,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,0PAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjEA;AAAA;AAAA;AAAA;AAAkwB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;AC6OtxB;AACA;AAGA;AAEA;AAIA;AAKA;AAGA;AAGA;AAQA;AAYA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA1CA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eA2CA;EACAC;IACAC;IACAC;IAEAC;IAEA;IACAC;IACA;IACAC;IACAC;EACA;EACAC;IACA;IACA;IACA;IACA;EACA;EACAC;IAAA;IACA;MACAC;QACA;QACA;QACA;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IAAA,+CACA,oDACA,oEACA,mDACA,kDACA,mDACA,mDACA,qDACA,iDACA,yDACA,qDACA,sDACA,gDACA,uDACA,iDACA,oDACA,wDACA;MACAC;IACA,oDACA;MACAC;MACAC;MACAC;IACA,mDACA,4DACA,iEACA,4DACA,0DACA,0DACA,wDACA,yDACA,qFACA,6DACA,uDACA,qDACA,mDACA,0DACA,+DACA,kDACA,uDACA,wDACA,uDACA,gDACA,uDACA,sDACA;EAEA;EACAC;IACAC;MACAC;QACA;UACA;QACA;MACA;MACAC;IACA;EACA;EACAC;IAAA;IACA;IACA;IACA;IACA;IACA;MAEA;MACA;MACAC,MACAC,gBACAC;QACA;MACA,GACAC;IAEA;IAEA;;IAKA;IACAC;MACAC;QACAC;QACA;MACA;IACA;;IACA;MACA;QAAA;QACA;QACA;QACAC;QACA;QACAC;UACA;QACA;MACA;QACA;MACA;MACA;QACA;MACA;QAMA,+FACAC,8DACA;QACA;MACA;IACA;MACA;QACA;QACA;UACA;UACA;QACA;MACA;QACAL;UACAM;UACAC;UACAnC;UACAoC;QACA;MACA;IACA;IAAA;EACA;EACAC;IACAC;MACA;MACA;QACAR;QACAA;MACA;IACA;IACAS;MAAA;MACA;QACA;MACA;IACA;IACAC;MACAC;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;QACA;UACAb;QACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;IACA;IACAc;MACApB;IACA;IACA;IACAqB;MACA;MACA;MACA;QACAnB;QACAF;UACAM;QACA;QACAJ;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;QACAA;QACAA;QACAA;QACAA;QACAA;;QAEA;;QAOAA;QACAA;QAGAA;QAEA;UACA;YACAoB;YACAC;YACA1D;YACA2D;YACAC;YACAC;UACA;QACA;QACAxB;QACA;QACAA;QAEAE;UACAF;QACA;MAEA;QACAA;UACAI;QACA;UACAqB;QACA;MACA;IACA;IAqBA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;MACA;QACA;UACAC;UACA;QACA;MACA;MACA;QACAC;MACA;MACA;MACA;MAEA;QACAA,UACAA,8BACA,aACAA,yBACA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACA;QACA;MACA;QACAA,UACAA,8BACA,aACAA,yBACA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;MACA;QACAA,UACAA,8BACA,aACAA,yBACA;QACAA;QACAA;QACAA;QACAA,UACAA,8BACA,aACA;QACAA;QACAA;QACAA;MACA;IACA;IAEAC;MACA;QACAC;QACAC;MACA;QAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACArC;QACAA;UACA;UACA;UACAoC;UACAC;UACA/B;UACAA;QACA;MACA;MAAA;IACA;IACA;IACAgC;MACA;MACA/B,wGACAgC;MACA;MACA;IACA;;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;MACA;QACA;UACAhC;QACA;MACA;MACA;QACA9B;QACA;MACA;MACA;MACA,sGACAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;UACA;YACA8B;UACA;QACA;QACAiC;QACA;QACAC;QACAA;QACA;QACA;QACA;UACA;UACA;QACA;QACA;QACA;MACA;QACAD;QACA;UACA;UACA;QACA;QACA;QACA;MACA;IACA;IACAE;MACA,qGACAC;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAEA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACA;IACAC;MACA5C;QACA6C;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA,iBACA,oCAEA;MACA;MACA;MACA;MACA,yGACAC;QACAzC;MACA;MACA;QACAmB;QACAuB;QACAC;QACAC;QACAC;MACA;MACA;QACA;QACA;QACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA;QACA;UACAlD;QACA;MACA;QACA;UACAA;QACA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAmD;MACA;QACA;MACA;QAOA;MAEA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACAX;MACA;MACA,kDACA;IACA;IACA;IACAY;MACA;MACAzD;QACA6C;QACA5C;UACAC;QACA;QACAwD;UACA;YACApD;UACA;UACAJ;QACA;MACA;IACA;IAEA;IACAyD;MACA;MACA3D;QACA6C;QACA5C;UACAC;QACA;QACAwD;UACA;YACApD;UACA;UACAJ;QACA;MACA;IACA;IAEA;AACA;AACA;AACA;AACA;IACA0D;MACA;MACAC;QACA7D;UACA6C;UACA5C;YACAC;YACA,oCACA4D,8CAEA5D;UACA;UACAwD;YACAxD;YACAA;UACA;QACA;MACA;QACAA;QACAA;MACA;IACA;IACA6D;MACA;MACA;QACAlB;MACA;QACA3C;MACA;IACA;IACA;IACA8D;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACAjE;QACAM;QACAE;MACA;MACAN;MACA;MACA;MACA;QACAF;QACAE;UACAI;QACA;QACA;MACA;MACAN;QACA6C;QACA5C;UACAiE;UACA;UACA;UACA;UACA9D;YACAF,oEACAiE,SACA;cACAjE;cACAA;cACAF;YACA;UACA;QACA;MACA;IACA;IACA;IACAoE;MACA;MACA;QACAC;QACAxG;QACAyG;MACA;MACA;QACA;UACApE;QACA;MACA;QACAA;MACA;IACA;IACA;IACAqE;MAAA;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACA3E;UACA;QAEA;QACA4E;QACAnB;UACA;YACApD;UACA;QACA;MACA;IACA;IACA;AACA;AACA;;IAEAwE;MACA;MACA9E;QACAC;UACA;YACAD;cACA+E;cACA9E;gBACAD;kBACAgF;kBACA/E;oBACAC;oBACAA;sBACAI;sBACAC;oBACA;kBACA;kBACAmD;oBACAxD;sBACAI;oBACA;kBACA;gBACA;cACA;YACA;UACA;YACAN;cACAgF;cACA/E;gBACAC;gBACAA;kBACAI;kBACAC;gBACA;cACA;cACAmD;gBACAxD;kBACAI;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IAEA2E;MACA;MACA;MACA;QACAC,OACAA,2BACAA,+BACAA;QAEA;UACAC;UACA7E;UACA8E;UACAC;QACA;QACA,mFACAC;MACA;IACA;IACAC;MACA;QACAC;MACA;MACAC;MACAvF;MACAA;MACA;QACAA;QACA;MACA;MACA;QACA;UACAA;UACA;QACA;MACA;IACA;IACAwF;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;EACA;EAEAC;IACA;IACA;MACArF;MACAgE;MACAsB;IACA;EACA;AAGA;AAAA,2B;;;;;;;;;;;;;AC3mCA;AAAA;AAAA;AAAA;AAAq8C,CAAgB,ovCAAG,EAAC,C;;;;;;;;;;;ACAz9C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/activity/goods_combination_details/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/activity/goods_combination_details/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=26bb9225&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=26bb9225&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"26bb9225\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/activity/goods_combination_details/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=26bb9225&scoped=true&\"", "var components\ntry {\n  components = {\n    jyfParser: function () {\n      return import(\n        /* webpackChunkName: \"components/jyf-parser/jyf-parser\" */ \"@/components/jyf-parser/jyf-parser.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = parseFloat(_vm.storeInfo.sales)\n  var m1 = parseFloat(_vm.storeInfo.ficti)\n  var g0 = _vm.attribute.productAttr.length\n  var m2 = parseFloat(_vm.pinkOkSum)\n  var g1 = _vm.attribute.productSelect.quota > 0 ? _vm.pink.length : null\n  var g2 = _vm.attribute.productSelect.quota > 0 && g1 ? _vm.pink.length : null\n  var g3 =\n    _vm.attribute.productSelect.quota > 0 && g1 && !(g2 > _vm.AllIndex)\n      ? _vm.pink.length === _vm.AllIndex &&\n        _vm.pink.length !== _vm.AllIndexDefault\n      : null\n  var g4 = _vm.reply.length\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.H5ShareBox = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        g0: g0,\n        m2: m2,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<!-- 头部 -->\r\n\t\t<view class='navbar' :style=\"{height:navH+'rpx',opacity:opacity}\">\r\n\t\t\t<view class='navbarH' :style='\"height:\"+navH+\"rpx;\"'>\r\n\t\t\t\t<view class='navbarCon acea-row row-center-wrapper'>\r\n\t\t\t\t\t<view class=\"header acea-row row-center-wrapper\">\r\n\t\t\t\t\t\t<view class=\"item\" :class=\"navActive === index ? 'on' : ''\" v-for=\"(item,index) in navList\"\r\n\t\t\t\t\t\t\t:key='index' @tap=\"tap(item,index)\">\r\n\t\t\t\t\t\t\t{{ item }}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- <view class='iconfont icon-xiangzuo' :style=\"'top:'+navH/2+'rpx'\" @tap='returns'></view> -->\r\n\t\t<!-- 详情 -->\r\n\t\t<view class='product-con'>\r\n\t\t\t<scroll-view :scroll-top=\"scrollTop\" scroll-y='true' scroll-with-animation=\"true\"\r\n\t\t\t\t:style=\"'height:'+height+'px;'\" @scroll=\"scroll\">\r\n\t\t\t\t<view id=\"past0\">\r\n\t\t\t\t\t<productConSwiper :imgUrls=\"imgUrls\" class=\"mb30\"></productConSwiper>\r\n\t\t\t\t\t<view class=\"pad30\">\r\n\t\t\t\t\t\t<view class='wrapper mb30'>\r\n\t\t\t\t\t\t\t<view class='share acea-row row-between row-bottom'>\r\n\t\t\t\t\t\t\t\t<view class='money font-color'>\r\n\t\t\t\t\t\t\t\t\t￥<text class='num'>{{storeInfo.price || 0}}</text><text\r\n\t\t\t\t\t\t\t\t\t\tclass='y-money'>￥{{storeInfo.otPrice || 0}}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class='iconfont icon-fenxiang' @click=\"listenerActionSheet\"></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class='introduce line2'>{{storeInfo.storeName}}</view>\r\n\t\t\t\t\t\t\t<view class='label acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t\t\t<view class='stock'>类型：{{storeInfo.people || 0}}人团</view>\r\n\t\t\t\t\t\t\t\t<view>累计销量：{{parseFloat(storeInfo.sales)  + parseFloat(storeInfo.ficti)}} {{storeInfo.unitName || ''}}</view>\r\n\t\t\t\t\t\t\t\t<view>限购: {{ storeInfo.quotaShow ? storeInfo.quotaShow : 0 }}\r\n\t\t\t\t\t\t\t\t\t{{storeInfo.unitName || ''}}\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='attribute acea-row row-between-wrapper mb30 borRadius14' @tap='selecAttr'\r\n\t\t\t\t\t\t\tv-if='attribute.productAttr.length'>\r\n\t\t\t\t\t\t\t<view class=\"line1\">{{attr}}：<text class='atterTxt'>{{attrValue}}</text></view>\r\n\t\t\t\t\t\t\t<view class='iconfont icon-jiantou'></view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='notice acea-row row-middle mb30 borRadius14' v-if=\"parseFloat(pinkOkSum) >0\">\r\n\t\t\t\t\t\t\t<view class='num font-color'>\r\n\t\t\t\t\t\t\t\t<text class='iconfont icon-laba'></text>\r\n\t\t\t\t\t\t\t\t已拼{{pinkOkSum}}件<text class='line'>|</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class='swiper'>\r\n\t\t\t\t\t\t\t\t<swiper :indicator-dots=\"indicatorDots\" :autoplay=\"autoplay\" interval=\"2500\"\r\n\t\t\t\t\t\t\t\t\tduration=\"500\" vertical=\"true\" circular=\"true\">\r\n\t\t\t\t\t\t\t\t\t<block v-for=\"(item,index) in itemNew\" :key='index'>\r\n\t\t\t\t\t\t\t\t\t\t<swiper-item>\r\n\t\t\t\t\t\t\t\t\t\t\t<view class='line1'>{{item.nickname}}拼团成功</view>\r\n\t\t\t\t\t\t\t\t\t\t</swiper-item>\r\n\t\t\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t\t\t</swiper>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view v-if='attribute.productSelect.quota > 0' class='assemble mb30 borRadius14'>\r\n\t\t\t\t\t\t\t<view class='item acea-row row-between-wrapper' v-for='(item,index) in pink' :key='index'\r\n\t\t\t\t\t\t\t\tv-if=\"index < AllIndex\">\r\n\t\t\t\t\t\t\t\t<view class='pictxt acea-row row-between-wrapper'>\r\n\t\t\t\t\t\t\t\t\t<view class='pictrue'>\r\n\t\t\t\t\t\t\t\t\t\t<image :src='item.avatar'></image>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class='text line1'>{{item.nickname}}</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class='right acea-row row-middle'>\r\n\t\t\t\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t\t\t\t<view class='lack'>还差<text class='font-color'>{{item.count}}</text>人成团</view>\r\n\t\t\t\t\t\t\t\t\t\t<view class='time acea-row'>\r\n\t\t\t\t\t\t\t\t\t\t\t<count-down :is-day=\"false\" :tip-text=\"'剩余 '\" :day-text=\"' '\" :hour-text=\"':'\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t:minute-text=\"':'\" :second-text=\"' '\" :datatime=\"item.stopTime/1000\" :bgColor=\"bgColor\">\r\n\t\t\t\t\t\t\t\t\t\t\t</count-down>\r\n\t\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<navigator hover-class='none'\r\n\t\t\t\t\t\t\t\t\t\t:url=\"'/pages/activity/goods_combination_status/index?id='+item.id\"\r\n\t\t\t\t\t\t\t\t\t\tclass='spellBnt'>\r\n\t\t\t\t\t\t\t\t\t\t去拼单\r\n\t\t\t\t\t\t\t\t\t\t<text class='iconfont icon-jiantou'></text>\r\n\t\t\t\t\t\t\t\t\t</navigator>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<template v-if=\"pink.length\">\r\n\t\t\t\t\t\t\t\t<view class='more' @tap='showAll' v-if=\"pink.length > AllIndex\">查看更多<text\r\n\t\t\t\t\t\t\t\t\t\tclass='iconfont icon-xiangxia'></text></view>\r\n\t\t\t\t\t\t\t\t<view class='more' @tap='hideAll'\r\n\t\t\t\t\t\t\t\t\tv-else-if=\"pink.length === AllIndex && pink.length !== AllIndexDefault\">收起<text\r\n\t\t\t\t\t\t\t\t\t\tclass='iconfont icon-xiangshang'></text></view>\r\n\t\t\t\t\t\t\t</template>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='playWay mb30 borRadius14'>\r\n\t\t\t\t\t\t\t<view class='title acea-row row-between row-middle'>\r\n\t\t\t\t\t\t\t\t<view>拼团玩法</view>\r\n\t\t\t\t\t\t\t\t<!-- <navigator hover-class='none' class='font-color' url='/pages/activity/goods_combination_rule/index'>查看规则<text class=\"iconfont icon-jiantou\"></text></navigator> -->\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class='way acea-row row-middle'>\r\n\t\t\t\t\t\t\t\t<view class='item acea-row row-middle'>\r\n\t\t\t\t\t\t\t\t\t<text class='num'>①</text>\r\n\t\t\t\t\t\t\t\t\t开团/参团\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class='iconfont icon-arrow'></view>\r\n\t\t\t\t\t\t\t\t<view class='item'>\r\n\t\t\t\t\t\t\t\t\t<text class='num'>②</text>\r\n\t\t\t\t\t\t\t\t\t邀请好友\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class='iconfont icon-arrow'></view>\r\n\t\t\t\t\t\t\t\t<view class='item'>\r\n\t\t\t\t\t\t\t\t\t<view>\r\n\t\t\t\t\t\t\t\t\t\t<text class='num'>③</text>\r\n\t\t\t\t\t\t\t\t\t\t满员发货\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<!-- <view class='tip'>不满自动退款</view> -->\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class='userEvaluation borRadius14' id=\"past1\">\r\n\t\t\t\t\t\t\t<view class='title acea-row row-between-wrapper' :style=\"replyCount==0?'border-bottom-left-radius:14rpx;border-bottom-right-radius:14rpx;':''\">\r\n\t\t\t\t\t\t\t\t<view>用户评价<i>({{replyCount}})</i></view>\r\n\t\t\t\t\t\t\t\t<navigator class='praise' hover-class='none'\r\n\t\t\t\t\t\t\t\t\t:url='\"/pages/users/goods_comment_list/index?productId=\"+storeInfo.productId'>\r\n\t\t\t\t\t\t\t\t\t<i>好评</i><text class='font-color'>{{replyChance || 0}}%</text>\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t<text class='iconfont icon-jiantou'></text>\r\n\t\t\t\t\t\t\t\t</navigator>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<userEvaluation :reply=\"reply\" v-if=\"reply.length>0\"></userEvaluation>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class='product-intro' id=\"past2\">\r\n\t\t\t\t\t<view class='title'>\r\n\t\t\t\t\t\t<image src=\"../../../static/images/xzuo.png\"></image>\r\n\t\t\t\t\t\t<span class=\"sp\">产品详情</span>\r\n\t\t\t\t\t\t<image src=\"../../../static/images/xyou.png\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class='conter'>\r\n\t\t\t\t\t\t<jyf-parser :html=\"storeInfo.content\" ref=\"article\" :tag-style=\"tagStyle\"></jyf-parser>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view style='height:120rpx;'></view>\r\n\t\t\t</scroll-view>\r\n\t\t\t<view class='footer acea-row row-between-wrapper'>\r\n\t\t\t\t<!-- #ifdef MP -->\r\n\t\t\t\t<button open-type=\"contact\" hover-class='none' class='item'>\r\n\t\t\t\t\t<view class='iconfont icon-kefu'></view>\r\n\t\t\t\t\t<view>客服</view>\r\n\t\t\t\t</button>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t<!-- #ifndef MP -->\r\n\t\t\t\t<navigator hover-class=\"none\" class=\"item\" @click=\"kefuClick\">\r\n\t\t\t\t\t<view class=\"iconfont icon-kefu\"></view>\r\n\t\t\t\t\t<view>客服</view>\r\n\t\t\t\t</navigator>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t<view @tap='setCollect' class='item'>\r\n\t\t\t\t\t<view class='iconfont icon-shoucang1' v-if=\"userCollect\"></view>\r\n\t\t\t\t\t<view class='iconfont icon-shoucang' v-else></view>\r\n\t\t\t\t\t<view>收藏</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"bnt acea-row\">\r\n\t\t\t\t\t<view class=\"joinCart bnts\" @tap=\"goProduct\">单独购买</view>\r\n\t\t\t\t\t<view class=\"buy bnts\" @tap=\"goCat\"\r\n\t\t\t\t\t\tv-if='attribute.productSelect.quota>0'>\r\n\t\t\t\t\t\t立即开团\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"buy bnts bg-color-hui\" v-if=\"!dataShow\">\r\n\t\t\t\t\t\t立即开团\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"buy bnts bg-color-hui\"\r\n\t\t\t\t\t\tv-if='attribute.productSelect.quota <= 0'>\r\n\t\t\t\t\t\t已售罄\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<shareRedPackets :sharePacket=\"sharePacket\" @listenerActionSheet=\"listenerActionSheet\"\r\n\t\t\t@closeChange=\"closeChange\"></shareRedPackets>\r\n\t\t<!-- 分享按钮 -->\r\n\t\t<view class=\"generate-posters acea-row row-middle\" :class=\"posters ? 'on' : ''\">\r\n\t\t\t<!-- #ifndef MP -->\r\n\t\t\t<button class=\"item\" hover-class='none' v-if=\"weixinStatus === true\" @click=\"H5ShareBox = true\">\r\n\t\t\t\t<view class=\"iconfont icon-weixin3\"></view>\r\n\t\t\t\t<view class=\"\">发送给朋友</view>\r\n\t\t\t</button>\r\n\t\t\t<!-- #endif -->\r\n\t\t\t<!-- #ifdef MP -->\r\n\t\t\t<button class=\"item\" open-type=\"share\" hover-class='none' @click=\"goFriend\">\r\n\t\t\t\t<view class=\"iconfont icon-weixin3\"></view>\r\n\t\t\t\t<view class=\"\">发送给朋友</view>\r\n\t\t\t</button>\r\n\t\t\t<!-- #endif -->\r\n\t\t\t<button class=\"item\" hover-class='none' @tap=\"goPoster\">\r\n\t\t\t\t<view class=\"iconfont icon-haibao\"></view>\r\n\t\t\t\t<view class=\"\">生成海报</view>\r\n\t\t\t</button>\r\n\t\t</view>\r\n\t\t<view class=\"mask\" v-if=\"posters\" @click=\"closePosters\"></view>\r\n\t\t<view class=\"mask\" v-if=\"canvasStatus\" @click=\"listenerActionClose\"></view>\r\n\t\t<!-- <view class=\"mask\" v-if=\"posters\" @click=\"listenerActionClose\"></view> -->\r\n\r\n\t\t<!-- 海报展示 -->\r\n\t\t<view class='poster-pop' v-if=\"canvasStatus\">\r\n\t\t\t<image src='/static/images/poster-close.png' class='close' @click=\"posterImageClose\"></image>\r\n\t\t\t<image :src='posterImage'></image>\r\n\t\t\t<!-- #ifndef H5  -->\r\n\t\t\t<view class='save-poster' @click=\"savePosterPath\">保存到手机</view>\r\n\t\t\t<!-- #endif -->\r\n\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t<view class=\"keep\">长按图片可以保存到手机</view>\r\n\t\t\t<!-- #endif -->\r\n\t\t</view>\r\n\t\t<view class=\"canvas\" v-else>\r\n\t\t\t<canvas style=\"width:750px;height:1190px;\" canvas-id=\"firstCanvas\"></canvas>\r\n\t\t\t<canvas canvas-id=\"qrcode\" :style=\"{width: `${qrcodeSize}px`, height: `${qrcodeSize}px`}\" />\r\n\t\t</view>\r\n\t\t<!-- 发送给朋友图片 -->\r\n\t\t<view class=\"share-box\" v-if=\"H5ShareBox\">\r\n\t\t\t<image src=\"/static/images/share-info.png\" @click=\"H5ShareBox = false\"></image>\r\n\t\t</view>\r\n\t\t<!-- #ifdef MP -->\r\n\t\t<!-- <authorize @onLoadFun=\"onLoadFun\" :isAuto=\"isAuto\" :isShowAuth=\"isShowAuth\" @authColse=\"authColse\"></authorize> -->\r\n\t\t<!-- #endif -->\r\n\t\t<home></home>\r\n\t\t<product-window :attr='attribute' :limitNum='1' @myevent=\"onMyEvent\" @ChangeAttr=\"ChangeAttr\"\r\n\t\t\t@ChangeCartNum=\"ChangeCartNum\" @iptCartNum=\"iptCartNum\" @attrVal=\"attrVal\"></product-window>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tconst app = getApp();\r\n\timport uQRCode from '@/js_sdk/Sansnn-uQRCode/uqrcode.js'\r\n\timport {\r\n\t\tmapGetters\r\n\t} from \"vuex\";\r\n\timport { silenceBindingSpread } from \"@/utils\";\r\n\t// #ifdef MP\r\n\timport {\r\n\t\tbase64src\r\n\t} from '@/utils/base64src.js'\r\n\timport authorize from '@/components/Authorize';\r\n\timport {\r\n\t\tgetQrcode\r\n\t} from '@/api/api.js';\r\n\t// #endif\r\n\timport productConSwiper from '@/components/productConSwiper'\r\n\timport {\r\n\t\ttoLogin\r\n\t} from '@/libs/login.js';\r\n\timport {\r\n\t\tgetCombinationDetail\r\n\t} from '@/api/activity.js';\r\n\timport {\r\n\t\tpostCartAdd,\r\n\t\tcollectAdd,\r\n\t\tcollectDel,\r\n\t\tgetReplyList,\r\n\t\tgetReplyConfig,\r\n\t\tgetReplyProduct\r\n\t} from '@/api/store.js';\r\n\timport {\r\n\t\timageBase64\r\n\t} from \"@/api/public\";\r\n\timport parser from \"@/components/jyf-parser/jyf-parser\";\r\n\timport home from '@/components/home/<USER>'\r\n\timport productWindow from '@/components/productWindow/index.vue'\r\n\timport userEvaluation from '@/components/userEvaluation/index.vue'\r\n\timport countDown from '@/components/countDown/index.vue'\r\n\timport shareRedPackets from '@/components/shareRedPackets';\r\n\timport {\r\n\t\tgetProductCode\r\n\t} from '@/api/store.js'\r\n\timport { spread } from \"@/api/user\";\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tshareRedPackets,\r\n\t\t\tproductConSwiper,\r\n\t\t\t// #ifdef MP\r\n\t\t\tauthorize,\r\n\t\t\t// #endif\r\n\t\t\t\"jyf-parser\": parser,\r\n\t\t\thome,\r\n\t\t\t\"product-window\": productWindow,\r\n\t\t\tuserEvaluation,\r\n\t\t\tcountDown\r\n\t\t},\r\n\t\tcomputed: mapGetters({\r\n\t\t\t'isLogin': 'isLogin',\r\n\t\t\t'userData': 'userInfo',\r\n\t\t\t'uid': 'uid',\r\n\t\t\t'chatUrl': 'chatUrl'\r\n\t\t}),\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tbgColor:{\r\n\t\t\t\t\t'bgColor': '',\r\n\t\t\t\t\t'Color': '#999999',\r\n\t\t\t\t\t'isDay': true\r\n\t\t\t\t},\r\n\t\t\t\tuserCollect:false,\r\n\t\t\t\tdataShow: 0,\r\n\t\t\t\tnavH: '',\r\n\t\t\t\tid: 0,\r\n\t\t\t\tuserInfo: {},\r\n\t\t\t\titemNew: [],\r\n\t\t\t\tindicatorDots: false,\r\n\t\t\t\tcircular: true,\r\n\t\t\t\tautoplay: true,\r\n\t\t\t\tinterval: 3000,\r\n\t\t\t\tduration: 500,\r\n\t\t\t\tattribute: {\r\n\t\t\t\t\tcartAttr: false,\r\n\t\t\t\t\tproductAttr: [],\r\n\t\t\t\t\tproductSelect: {}\r\n\t\t\t\t},\r\n\t\t\t\tproductValue: [],\r\n\t\t\t\tisOpen: false,\r\n\t\t\t\tattr: '请选择',\r\n\t\t\t\tattrValue: '',\r\n\t\t\t\tAllIndex: 2,\r\n\t\t\t\tmaxAllIndex: 0,\r\n\t\t\t\treplyChance: '',\r\n\t\t\t\tlimitNum: 1,\r\n\t\t\t\ttimeer: null,\r\n\t\t\t\tiSplus: false,\r\n\t\t\t\tnavH: \"\",\r\n\t\t\t\tnavList: ['商品', '评价', '详情'],\r\n\t\t\t\topacity: 0,\r\n\t\t\t\tscrollY: 0,\r\n\t\t\t\ttopArr: [],\r\n\t\t\t\ttoView: '',\r\n\t\t\t\theight: 0,\r\n\t\t\t\theightArr: [],\r\n\t\t\t\tlock: false,\r\n\t\t\t\tscrollTop: 0,\r\n\t\t\t\tstoreInfo: {},\r\n\t\t\t\tpinkOkSum: 0,\r\n\t\t\t\tpink: [],\r\n\t\t\t\treplyCount: 0,\r\n\t\t\t\treply: [],\r\n\t\t\t\timgUrls: [],\r\n\t\t\t\tsharePacket: {\r\n\t\t\t\t\tisState: true, //默认不显示\r\n\t\t\t\t},\r\n\t\t\t\ttagStyle: {\r\n\t\t\t\t\timg: 'width:100%;display:block;',\r\n\t\t\t\t\ttable: 'width:100%',\r\n\t\t\t\t\tvideo: 'width:100%'\r\n\t\t\t\t},\r\n\t\t\t\tposters: false,\r\n\t\t\t\tweixinStatus: false,\r\n\t\t\t\tposterImageStatus: false,\r\n\t\t\t\tcanvasStatus: false, //海报绘图标签\r\n\t\t\t\tstoreImage: '', //海报产品图\r\n\t\t\t\tPromotionCode: '', //二维码图片\r\n\t\t\t\tposterImage: '', //海报路径\r\n\t\t\t\tposterbackgd: '/static/images/posterbackgd.png',\r\n\t\t\t\tnavActive: 0,\r\n\t\t\t\tactionSheetHidden: false,\r\n\t\t\t\tattrTxt: '',\r\n\t\t\t\tcart_num: '',\r\n\t\t\t\tisAuto: false, //没有授权的不会自动授权\r\n\t\t\t\tisShowAuth: false, //是否隐藏授权\r\n\t\t\t\tAllIndexDefault: 0,\r\n\t\t\t\timgTop: '',\r\n\t\t\t\tqrcodeSize: 600,\r\n\t\t\t\tH5ShareBox: false, //公众号分享图片\r\n\t\t\t\tonceNum: 0, //一次可以购买几个\r\n\t\t\t\terrT: '',\r\n\t\t\t\treturnShow: true,\r\n\t\t\t\thomeTop: 20\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tisLogin: {\r\n\t\t\t\thandler: function(newV, oldV) {\r\n\t\t\t\t\tif (newV) {\r\n\t\t\t\t\t\tthis.combinationDetail();\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tdeep: true\r\n\t\t\t},\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\tlet that = this\r\n\t\t\tthis.$store.commit(\"PRODUCT_TYPE\", 'normal');\r\n\t\t\tvar pages = getCurrentPages();\r\n\t\t\t//\tthat.returnShow = pages.length === 1 ? false : true;\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t// #ifdef MP\r\n\t\t\t\tconst menuButton = uni.getMenuButtonBoundingClientRect();\r\n\t\t\t\tconst query = uni.createSelectorQuery().in(this);\r\n\t\t\t\tquery\r\n\t\t\t\t\t.select('#home')\r\n\t\t\t\t\t.boundingClientRect(data => {\r\n\t\t\t\t\t\tthis.homeTop = menuButton.top * 2 + menuButton.height - data.height;\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.exec();\r\n\t\t\t\t// #endif\r\n\t\t\t});\r\n\t\t\t// #ifdef MP\r\n\t\t\tthis.navH = app.globalData.navHeight;\r\n\t\t\t// #endif\r\n\t\t\t// #ifndef MP\r\n\t\t\tthis.navH = 96;\r\n\t\t\t// #endif\r\n\t\t\t//设置商品列表高度\r\n\t\t\tuni.getSystemInfo({\r\n\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\tthat.height = res.windowHeight\r\n\t\t\t\t\t//res.windowHeight:获取整个窗口高度为px，*2为rpx；98为头部占据的高度；\r\n\t\t\t\t},\r\n\t\t\t});\r\n\t\t\tif (options.hasOwnProperty('id') || options.scene) {\r\n\t\t\t\tif (options.scene) { // 仅仅小程序扫码进入\r\n\t\t\t\t\tlet qrCodeValue = this.$util.getUrlParams(decodeURIComponent(options.scene));\r\n\t\t\t\t\tlet mapeMpQrCodeValue = this.$util.formatMpQrCodeData(qrCodeValue);\r\n\t\t\t\t    app.globalData.spread = mapeMpQrCodeValue.spread;\r\n\t\t\t\t    this.id = mapeMpQrCodeValue.id;\r\n\t\t\t\t    setTimeout(()=>{\r\n\t\t\t\t    \tspread(mapeMpQrCodeValue.spread).then(res => {}).catch(res => {})\r\n\t\t\t\t    },2000)\r\n\t\t\t\t}else{\r\n\t\t\t\t\tthis.id = options.id;\r\n\t\t\t\t}\r\n\t\t\t\tif (this.isLogin) {\r\n\t\t\t\t\tthis.combinationDetail();\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// #ifdef H5 || APP-PLUS\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\tuni.setStorageSync('comGoodsId', options.id);\r\n\t\t\t\t\t} catch (e) {}\r\n\t\t\t\t\t// #endif \r\n\t\t\t\t\tthis.$Cache.set('login_back_url',\r\n\t\t\t\t\t\t`/pages/activity/goods_combination_details/index?id=${options.id}&spread=${options.pid?options.pid:0}`\r\n\t\t\t\t\t);\r\n\t\t\t\t\ttoLogin();\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tlet val = uni.getStorageSync('comGoodsId');\r\n\t\t\t\t\tif (val != '') {\r\n\t\t\t\t\t\tthis.id = val\r\n\t\t\t\t\t\tthis.combinationDetail();\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '参数错误',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 1000,\r\n\t\t\t\t\t\tmask: true,\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetProductReplyCount: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tgetReplyConfig(that.storeInfo.productId).then(res => {\r\n\t\t\t\t\tthat.$set(that, 'replyChance', res.data.replyChance * 100);\r\n\t\t\t\t\tthat.$set(that, 'replyCount', res.data.sumCount);\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgetProductReplyList: function() {\r\n\t\t\t\tgetReplyProduct(this.storeInfo.productId).then(res => {\r\n\t\t\t\t\tthis.reply = res.data.productReply ? [res.data.productReply] : [];\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tkefuClick() {\r\n\t\t\t\tlocation.href = this.chatUrl;\r\n\t\t\t},\r\n\t\t\tclosePosters: function() {\r\n\t\t\t\tthis.posters = false;\r\n\t\t\t},\r\n\t\t\tcloseChange: function() {\r\n\t\t\t\tthis.$set(this.sharePacket, 'isState', true);\r\n\t\t\t},\r\n\t\t\tshowAll: function() {\r\n\t\t\t\tthis.AllIndexDefault = this.AllIndex;\r\n\t\t\t\tthis.AllIndex = this.pink.length;\r\n\t\t\t},\r\n\t\t\thideAll: function() {\r\n\t\t\t\tthis.AllIndex = this.AllIndexDefault;\r\n\t\t\t},\r\n\t\t\t// 授权关闭\r\n\t\t\tauthColse: function(e) {\r\n\t\t\t\tthis.isShowAuth = e;\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 购物车手动填写\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tiptCartNum: function(e) {\r\n\t\t\t\tif (e > this.onceNum) {\r\n\t\t\t\t\tthis.$util.Tips({\r\n\t\t\t\t\t\ttitle: `该商品每次限购${this.onceNum}${this.storeInfo.unitName}`\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthis.$set(this.attribute.productSelect, 'cart_num', this.onceNum);\r\n\t\t\t\t\tthis.$set(this, \"cart_num\", this.onceNum);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$set(this.attribute.productSelect, 'cart_num', e);\r\n\t\t\t\t\tthis.$set(this, \"cart_num\", e);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 返回\r\n\t\t\treturns() {\r\n\t\t\t\tuni.navigateBack();\r\n\t\t\t},\r\n\t\t\t// 获取详情\r\n\t\t\tcombinationDetail() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar data = that.id;\r\n\t\t\t\tgetCombinationDetail(data).then(function(res) {\r\n\t\t\t\t\tthat.dataShow = 1;\r\n\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\ttitle: res.data.storeCombination.storeName.substring(0, 16)\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthat.storeInfo = res.data.storeCombination;\r\n\t\t\t\t\tthat.getProductReplyList();\r\n\t\t\t\t\tthat.getProductReplyCount();\r\n\t\t\t\t\tthat.imgUrls = JSON.parse(res.data.storeCombination.sliderImage);\r\n\t\t\t\t\tthat.attribute.productSelect.num = res.data.storeCombination.onceNum;\r\n\t\t\t\t\tthat.userCollect = res.data.userCollect;\r\n\t\t\t\t\tthat.pink = res.data.pinkList || [];\r\n\t\t\t\t\t// that.pindAll = res.data.pindAll || [];\r\n\t\t\t\t\tthat.itemNew = res.data.pinkOkList || [];\r\n\t\t\t\t\tthat.pinkOkSum = res.data.pinkOkSum;\r\n\t\t\t\t\tthat.attribute.productAttr = res.data.productAttr || [];\r\n\t\t\t\t\tthat.productValue = res.data.productValue;\r\n\t\t\t\t\tthat.onceNum = res.data.storeCombination.onceNum;\r\n\t\t\t\t\t\r\n\t\t\t\t\t//\tthat.PromotionCode = res.data.storeInfo.code_base\r\n\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\tthat.setShare();\r\n\t\t\t\t\tthat.storeImage = that.storeInfo.image\r\n\t\t\t\t\tthat.make();\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifdef MP\r\n\t\t\t\t\tthat.getQrcode();\r\n\t\t\t\t\tthat.imgTop = res.data.storeCombination.image;\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifndef H5\r\n\t\t\t\t\tthat.downloadFilestoreImage();\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\tlet productAttr = res.data.productAttr.map(item => {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tattrName : item.attrName,\r\n\t\t\t\t\t\tattrValues: item.attrValues.split(','),\r\n\t\t\t\t\t\tid:item.id,\r\n\t\t\t\t\t\tisDel:item.isDel,\r\n\t\t\t\t\t\tproductId:item.productId,\r\n\t\t\t\t\t\ttype:item.type\r\n\t\t\t\t\t }\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthat.$set(that.attribute,'productAttr',productAttr);\r\n\t\t\t\t\t// that.setProductSelect();\r\n\t\t\t\t\tthat.DefaultSelect();\r\n\t\t\t\t\t\r\n\t\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t\tthat.infoScroll();\r\n\t\t\t\t\t}, 500);\r\n\r\n\t\t\t\t}).catch(function(err) {\r\n\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\ttab: 3\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//#ifdef H5\r\n\t\t\tsetShare: function() {\r\n\t\t\t\tthis.$wechat.isWeixin() &&\r\n\t\t\t\t\tthis.$wechat.wechatEvevt([\r\n\t\t\t\t\t\t\"updateAppMessageShareData\",\r\n\t\t\t\t\t\t\"updateTimelineShareData\",\r\n\t\t\t\t\t\t\"onMenuShareAppMessage\",\r\n\t\t\t\t\t\t\"onMenuShareTimeline\"\r\n\t\t\t\t\t], {\r\n\t\t\t\t\t\tdesc: this.storeInfo.storeInfo,\r\n\t\t\t\t\t\ttitle: this.storeInfo.storeName,\r\n\t\t\t\t\t\tlink: location.href,\r\n\t\t\t\t\t\timgUrl: this.storeInfo.image\r\n\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t\t}).catch(err => {\r\n\t\t\t\t\t\tconsole.log(err);\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t//#endif\r\n\t\t\t/**\r\n\t\t\t * 默认选中属性\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tDefaultSelect: function() {\r\n\t\t\t\tlet self = this\r\n\t\t\t\tlet productAttr = self.attribute.productAttr;\r\n\t\t\t\tlet value = [];\r\n\t\t\t\tfor (var key in self.productValue) {\r\n\t\t\t\t\tif (self.productValue[key].quota > 0) {\r\n\t\t\t\t\t\tvalue = self.attribute.productAttr.length ? key.split(\",\") : [];\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tfor (let i = 0; i < productAttr.length; i++) {\r\n\t\t\t\t\tself.$set(productAttr[i], \"index\", value[i]);\r\n\t\t\t\t}\r\n\t\t\t\t//sort();排序函数:数字-英文-汉字；\r\n\t\t\t\tlet productSelect = self.productValue[value.join(\",\")];\r\n\r\n\t\t\t\tif (productSelect && productAttr.length) {\r\n\t\t\t\t\tself.$set(\r\n\t\t\t\t\t\tself.attribute.productSelect,\r\n\t\t\t\t\t\t\"storeName\",\r\n\t\t\t\t\t\tself.storeInfo.storeName\r\n\t\t\t\t\t);\r\n\t\t\t\t\tself.$set(self.attribute.productSelect, \"image\", productSelect.image);\r\n\t\t\t\t\tself.$set(self.attribute.productSelect, \"price\", productSelect.price);\r\n\t\t\t\t\tself.$set(self.attribute.productSelect, \"unique\", productSelect.id);\r\n\t\t\t\t\tself.$set(self.attribute.productSelect, \"quota\", productSelect.quota);\r\n\t\t\t\t\tself.$set(self.attribute.productSelect, \"quotaShow\", productSelect.quotaShow);\r\n\t\t\t\t\tself.$set(self.attribute.productSelect, \"cart_num\", 1);\r\n\t\t\t\t\tthis.$set(this, \"attrValue\", value.join(\",\"));\r\n\t\t\t\t\tthis.$set(this, \"attrTxt\", \"已选择\");\r\n\t\t\t\t} else if (!productSelect && productAttr.length) {\r\n\t\t\t\t\tself.$set(\r\n\t\t\t\t\t\tself.attribute.productSelect,\r\n\t\t\t\t\t\t\"storeName\",\r\n\t\t\t\t\t\tself.storeInfo.storeName\r\n\t\t\t\t\t);\r\n\t\t\t\t\tself.$set(self.attribute.productSelect, \"image\", self.storeInfo.image);\r\n\t\t\t\t\tself.$set(self.attribute.productSelect, \"price\", self.storeInfo.price);\r\n\t\t\t\t\tself.$set(self.attribute.productSelect, \"quota\", 0);\r\n\t\t\t\t\tself.$set(self.attribute.productSelect, \"quotaShow\", 0);\r\n\t\t\t\t\tself.$set(self.attribute.productSelect, \"unique\", \"\");\r\n\t\t\t\t\tself.$set(self.attribute.productSelect, \"cart_num\", 0);\r\n\t\t\t\t\tself.$set(self, \"attrValue\", \"\");\r\n\t\t\t\t\tself.$set(self, \"attrTxt\", \"请选择\");\r\n\t\t\t\t} else if (!productSelect && !productAttr.length) {\r\n\t\t\t\t\tself.$set(\r\n\t\t\t\t\t\tself.attribute.productSelect,\r\n\t\t\t\t\t\t\"storeName\",\r\n\t\t\t\t\t\tself.storeInfo.storeName\r\n\t\t\t\t\t);\r\n\t\t\t\t\tself.$set(self.attribute.productSelect, \"image\", self.storeInfo.image);\r\n\t\t\t\t\tself.$set(self.attribute.productSelect, \"price\", self.storeInfo.price);\r\n\t\t\t\t\tself.$set(self.attribute.productSelect, \"quota\", 0);\r\n\t\t\t\t\tself.$set(\r\n\t\t\t\t\t\tself.attribute.productSelect,\r\n\t\t\t\t\t\t\"unique\", \"\"\r\n\t\t\t\t\t);\r\n\t\t\t\t\tself.$set(self.attribute.productSelect, \"cart_num\", 1);\r\n\t\t\t\t\tself.$set(self, \"attrValue\", \"\");\r\n\t\t\t\t\tself.$set(self, \"attrTxt\", \"请选择\");\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\tinfoScroll: function() {\r\n\t\t\t\tvar that = this,\r\n\t\t\t\t\ttopArr = [],\r\n\t\t\t\t\theightArr = [];\r\n\t\t\t\tfor (var i = 0; i < that.navList.length; i++) { //productList\r\n\t\t\t\t\t//获取元素所在位置\r\n\t\t\t\t\tvar query = uni.createSelectorQuery().in(this);\r\n\t\t\t\t\tvar idView = \"#past\" + i;\r\n\t\t\t\t\t// if (!that.data.good_list.length && i == 2) {\r\n\t\t\t\t\t//   var idView = \"#past\" + 3;\r\n\t\t\t\t\t// }\r\n\t\t\t\t\tquery.select(idView).boundingClientRect();\r\n\t\t\t\t\tquery.exec(function(res) {\r\n\t\t\t\t\t\tvar top = res[0].top;\r\n\t\t\t\t\t\tvar height = res[0].height;\r\n\t\t\t\t\t\ttopArr.push(top);\r\n\t\t\t\t\t\theightArr.push(height);\r\n\t\t\t\t\t\tthat.topArr = topArr\r\n\t\t\t\t\t\tthat.heightArr = heightArr\r\n\t\t\t\t\t});\r\n\t\t\t\t};\r\n\t\t\t},\r\n\t\t\t// 授权后回调\r\n\t\t\tonLoadFun: function(e) {\r\n\t\t\t\tthis.userInfo = e\r\n\t\t\t\tapp.globalData.openPages = '/pages/activity/goods_combination_details/index?id=' + this.id + '&spid=' +\r\n\t\t\t\t\te.uid;\r\n\t\t\t\tthis.combinationDetail();\r\n\t\t\t\t//this.downloadFilePromotionCode();\r\n\t\t\t},\r\n\t\t\tselecAttr: function() {\r\n\t\t\t\tthis.attribute.cartAttr = true\r\n\t\t\t},\r\n\t\t\tonMyEvent: function() {\r\n\t\t\t\tthis.$set(this.attribute, 'cartAttr', false);\r\n\t\t\t\tthis.$set(this, 'isOpen', false);\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 购物车数量加和数量减\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tChangeCartNum: function(changeValue) {\r\n\t\t\t\t//changeValue:是否 加|减\r\n\t\t\t\t//获取当前变动属性\r\n\t\t\t\tlet productSelect = this.productValue[this.attrValue];\r\n\t\t\t\tif (this.buyNum === productSelect.quota) {\r\n\t\t\t\t\treturn this.$util.Tips({\r\n\t\t\t\t\t\ttitle: '您已超出当前商品每人限购数量，请浏览其他商品'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\tif (this.cart_num) {\r\n\t\t\t\t\tproductSelect.cart_num = this.cart_num;\r\n\t\t\t\t\tthis.attribute.productSelect.cart_num = this.cart_num;\r\n\t\t\t\t}\r\n\t\t\t\t//如果没有属性,赋值给商品默认库存\r\n\t\t\t\tif (productSelect === undefined && !this.attribute.productAttr.length) productSelect = this.attribute\r\n\t\t\t\t\t.productSelect;\r\n\t\t\t\t//无属性值即库存为0；不存在加减；\r\n\t\t\t\tif (productSelect === undefined) return;\r\n\t\t\t\tlet quotaShow = productSelect.quota_show || 0;\r\n\t\t\t\tlet quota = productSelect.quota || 0;\r\n\t\t\t\tlet num = this.attribute.productSelect;\r\n\t\t\t\tlet nums = this.storeInfo.onceNum || 0;\r\n\t\t\t\t//设置默认数据\r\n\t\t\t\tif (productSelect.cart_num == undefined) productSelect.cart_num = 1;\r\n\t\t\t\tif (changeValue) {\r\n\t\t\t\t\tif (num.cart_num === this.onceNum) {\r\n\t\t\t\t\t\treturn this.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: `该商品每次限购${this.onceNum}${this.storeInfo.unitName}`\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t\tnum.cart_num++;\r\n\t\t\t\t\tlet arrMin = [];\r\n\t\t\t\t\tarrMin.push(nums);\r\n\t\t\t\t\tarrMin.push(quota);\r\n\t\t\t\t\t// arrMin.push(stock);\r\n\t\t\t\t\tlet minN = Math.min.apply(null, arrMin);\r\n\t\t\t\t\tif (num.cart_num >= minN) {\r\n\t\t\t\t\t\tthis.$set(this.attribute.productSelect, \"cart_num\", minN ? minN : 1);\r\n\t\t\t\t\t\tthis.$set(this, \"cart_num\", minN ? minN : 1);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.$set(this, \"cart_num\", num.cart_num);\r\n\t\t\t\t\tthis.$set(this.attribute.productSelect, \"cart_num\", num.cart_num);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tnum.cart_num--;\r\n\t\t\t\t\tif (num.cart_num < 1) {\r\n\t\t\t\t\t\tthis.$set(this.attribute.productSelect, \"cart_num\", 1);\r\n\t\t\t\t\t\tthis.$set(this, \"cart_num\", 1);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.$set(this, \"cart_num\", num.cart_num);\r\n\t\t\t\t\tthis.$set(this.attribute.productSelect, \"cart_num\", num.cart_num);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tattrVal(val) {\r\n\t\t\t\tthis.attribute.productAttr[val.indexw].index = this.attribute.productAttr[val.indexw].attrValues[val\r\n\t\t\t\t\t.indexn];\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 属性变动赋值\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tChangeAttr: function(res) {\r\n\t\t\t\tthis.$set(this, 'cart_num', 1);\r\n\t\t\t\tlet productSelect = this.productValue[res];\r\n\t\t\t\tif (productSelect) {\r\n\t\t\t\t\tthis.$set(this.attribute.productSelect, \"image\", productSelect.image);\r\n\t\t\t\t\tthis.$set(this.attribute.productSelect, \"price\", productSelect.price);\r\n\t\t\t\t\tthis.$set(this.attribute.productSelect, \"unique\", productSelect.id);\r\n\t\t\t\t\tthis.$set(this.attribute.productSelect, \"cart_num\", 1);\r\n\t\t\t\t\tthis.$set(this.attribute.productSelect, \"quota\", productSelect.quota);\r\n\t\t\t\t\tthis.$set(this.attribute.productSelect, \"quotaShow\", productSelect.quotaShow);\r\n\t\t\t\t\tthis.$set(this, \"attrValue\", res);\r\n\r\n\t\t\t\t\tthis.attrTxt = \"已选择\"\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$set(this.attribute.productSelect, \"image\", this.storeInfo.image);\r\n\t\t\t\t\tthis.$set(this.attribute.productSelect, \"price\", this.storeInfo.price);\r\n\t\t\t\t\tthis.$set(this.attribute.productSelect, \"unique\", \"\");\r\n\t\t\t\t\tthis.$set(this.attribute.productSelect, \"cart_num\", 0);\r\n\t\t\t\t\tthis.$set(this.attribute.productSelect, \"quota\", 0);\r\n\t\t\t\t\tthis.$set(this.attribute.productSelect, \"quotaShow\", 0);\r\n\t\t\t\t\tthis.$set(this, \"attrValue\", \"\");\r\n\t\t\t\t\tthis.attrTxt = \"已选择\"\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 单独购买\r\n\t\t\tgoProduct() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/goods_details/index?id=' + this.storeInfo.productId\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 立即购买\r\n\t\t\tgoCat() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tvar productSelect = this.productValue[this.attrValue];\r\n\t\t\t\t//打开属性\r\n\t\t\t\tif (this.isOpen)\r\n\t\t\t\t\tthis.attribute.cartAttr = true\r\n\t\t\t\telse\r\n\t\t\t\t\tthis.attribute.cartAttr = !this.attribute.cartAttr\r\n\t\t\t\t//只有关闭属性弹窗时进行加入购物车\r\n\t\t\t\tif (this.attribute.cartAttr === true && this.isOpen == false) return this.isOpen = true\r\n\t\t\t\t//如果有属性,没有选择,提示用户选择\r\n\t\t\t\tif (this.attribute.productAttr.length && productSelect === undefined && this.isOpen == true) return that\r\n\t\t\t\t\t.$util.Tips({\r\n\t\t\t\t\t\ttitle: '请选择属性'\r\n\t\t\t\t\t});\r\n\t\t\t\tvar data = {\r\n\t\t\t\t\tproductId: that.storeInfo.productId,\r\n\t\t\t\t\tcombinationId: parseFloat(that.id),\r\n\t\t\t\t\tcartNum: that.cart_num ? this.cart_num : this.attribute.productSelect.cart_num,\r\n\t\t\t\t\tproductAttrUnique: productSelect !== undefined ? productSelect.id : '',\r\n\t\t\t\t\tisNew: true,\r\n\t\t\t\t};\r\n\t\t\t\tthis.$Order.getPreOrder(\"buyNow\",[{\r\n\t\t\t\t\t\t\"attrValueId\": parseFloat(this.attribute.productSelect.unique),\r\n\t\t\t\t\t\t\"combinationId\": parseFloat(this.id),\r\n\t\t\t\t\t\t\"productNum\": parseFloat(this.cart_num ? this.cart_num : this.attribute.productSelect.cart_num),\r\n\t\t\t\t\t\t\"productId\": parseFloat(this.storeInfo.productId)\r\n\t\t\t\t\t}]);\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 收藏商品\r\n\t\t\t */\r\n\t\t\tsetCollect: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tif (this.userCollect) {\r\n\t\t\t\t\tcollectDel(this.storeInfo.productId).then(res => {\r\n\t\t\t\t\t\tthat.userCollect = !that.userCollect\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tcollectAdd(this.storeInfo.productId).then(res => {\r\n\t\t\t\t\t\tthat.userCollect = !that.userCollect\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 分享打开\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tlistenerActionSheet: function() {\r\n\t\t\t\tif (this.isLogin == false) {\r\n\t\t\t\t\ttoLogin();\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\tif(!this.imgTop) this.getImageBase64(this.storeImage);\r\n\t\t\t\t\tif (this.$wechat.isWeixin() === true) {\r\n\t\t\t\t\t\tthis.weixinStatus = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\tthis.posters = true;\r\n\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 分享关闭\r\n\t\t\tlistenerActionClose: function() {\r\n\t\t\t\tthis.canvasStatus = false;\r\n\t\t\t},\r\n\t\t\t//隐藏海报\r\n\t\t\tposterImageClose: function() {\r\n\t\t\t\tthis.canvasStatus = false\r\n\t\t\t},\r\n\t\t\t//替换安全域名\r\n\t\t\tsetDomain: function(url) {\r\n\t\t\t\turl = url ? url.toString() : '';\r\n\t\t\t\t//本地调试打开,生产请注销\r\n\t\t\t\tif (url.indexOf(\"https://\") > -1) return url;\r\n\t\t\t\telse return url.replace('http://', 'https://');\r\n\t\t\t},\r\n\t\t\t//获取海报产品图\r\n\t\t\tdownloadFilestoreImage: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tuni.downloadFile({\r\n\t\t\t\t\turl: that.setDomain(that.storeInfo.image),\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tthat.storeImage = res.tempFilePath;\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: function() {\r\n\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: ''\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tthat.storeImage = '';\r\n\t\t\t\t\t},\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// app获取二维码\r\n\t\t\tdownloadFileAppCode() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tuni.downloadFile({\r\n\t\t\t\t\turl: that.setDomain(that.storeInfo.code_base),\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tthat.PromotionCode = res.tempFilePath;\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: function() {\r\n\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: ''\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tthat.PromotionCode = '';\r\n\t\t\t\t\t},\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * 获取产品分销二维码\r\n\t\t\t * @param function successFn 下载完成回调\r\n\t\t\t * \r\n\t\t\t */\r\n\t\t\tdownloadFilePromotionCode: function(successFn) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tscombinationCode(that.id).then(res => {\r\n\t\t\t\t\tuni.downloadFile({\r\n\t\t\t\t\t\turl: that.setDomain(res.data.code),\r\n\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\tthat.$set(that, 'isDown', false);\r\n\t\t\t\t\t\t\tif (typeof successFn == 'function')\r\n\t\t\t\t\t\t\t\tsuccessFn && successFn(res.tempFilePath);\r\n\t\t\t\t\t\t\telse\r\n\t\t\t\t\t\t\t\tthat.$set(that, 'PromotionCode', res.tempFilePath);\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail: function() {\r\n\t\t\t\t\t\t\tthat.$set(that, 'isDown', false);\r\n\t\t\t\t\t\t\tthat.$set(that, 'PromotionCode', '');\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t});\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tthat.$set(that, 'isDown', false);\r\n\t\t\t\t\tthat.$set(that, 'PromotionCode', '');\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgetImageBase64: function(images) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\timageBase64({\r\n\t\t\t\t\turl: images\r\n\t\t\t\t}).then(res => {\r\n\t\t\t\t\tthat.imgTop = res.data.code\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 小程序关闭分享弹窗；\r\n\t\t\tgoFriend: function() {\r\n\t\t\t\tthis.posters = false;\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 生成海报\r\n\t\t\t */\r\n\t\t\tgoPoster: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '海报生成中',\r\n\t\t\t\t\tmask: true\r\n\t\t\t\t});\r\n\t\t\t\tthat.posters = false;\r\n\t\t\t\tlet arrImagesUrl = '';\r\n\t\t\t\tlet arrImagesUrlTop = '';\r\n\t\t\t\tif (!that.PromotionCode) {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\ttitle: that.errT\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tuni.downloadFile({\r\n\t\t\t\t\turl: that.imgTop,\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tarrImagesUrlTop = res.tempFilePath;\r\n\t\t\t\t\t\tlet arrImages = [that.posterbackgd, arrImagesUrlTop, that.PromotionCode];\r\n\t\t\t\t\t\tlet storeName = that.storeInfo.storeName;\r\n\t\t\t\t\t\tlet price = that.storeInfo.price;\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tthat.$util.PosterCanvas(arrImages, storeName, price, that.storeInfo\r\n\t\t\t\t\t\t\t\t.otPrice,\r\n\t\t\t\t\t\t\t\tfunction(tempFilePath) {\r\n\t\t\t\t\t\t\t\t\tthat.posterImage = tempFilePath;\r\n\t\t\t\t\t\t\t\t\tthat.canvasStatus = true;\r\n\t\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}, 500);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 小程序二维码\r\n\t\t\tgetQrcode() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\tpid: that.uid,\r\n\t\t\t\t\tid: that.id,\r\n\t\t\t\t\tpath: 'pages/activity/goods_combination_details/index'\r\n\t\t\t\t}\r\n\t\t\t\tgetQrcode(data).then(res => {\r\n\t\t\t\t\tbase64src(res.data.code, res => {\r\n\t\t\t\t\t\tthat.PromotionCode = res;\r\n\t\t\t\t\t});\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tthat.errT = err;\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 生成二维码；\r\n\t\t\tmake() {\r\n\t\t\t\tlet href = location.href.split('?')[0] + \"?id=\"+ this.id + \"&spread=\"  + this.uid;\r\n\t\t\t\tuQRCode.make({\r\n\t\t\t\t\tcanvasId: 'qrcode',\r\n\t\t\t\t\ttext: href,\r\n\t\t\t\t\tsize: this.qrcodeSize,\r\n\t\t\t\t\tmargin: 10,\r\n\t\t\t\t\tsuccess: res => {\r\n\t\t\t\t\t\tthis.PromotionCode = res;\r\n\r\n\t\t\t\t\t},\r\n\t\t\t\t\tcomplete: (res) => {},\r\n\t\t\t\t\tfail: res => {\r\n\t\t\t\t\t\tthis.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: '海报二维码生成失败！'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t/*\r\n\t\t\t * 保存到手机相册\r\n\t\t\t */\r\n\t\t\t// #ifdef MP\r\n\t\t\tsavePosterPath: function() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tuni.getSetting({\r\n\t\t\t\t\tsuccess(res) {\r\n\t\t\t\t\t\tif (!res.authSetting['scope.writePhotosAlbum']) {\r\n\t\t\t\t\t\t\tuni.authorize({\r\n\t\t\t\t\t\t\t\tscope: 'scope.writePhotosAlbum',\r\n\t\t\t\t\t\t\t\tsuccess() {\r\n\t\t\t\t\t\t\t\t\tuni.saveImageToPhotosAlbum({\r\n\t\t\t\t\t\t\t\t\t\tfilePath: that.posterImage,\r\n\t\t\t\t\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\t\t\t\t\tthat.posterImageClose();\r\n\t\t\t\t\t\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: '保存成功',\r\n\t\t\t\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\tfail: function(res) {\r\n\t\t\t\t\t\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: '保存失败'\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tuni.saveImageToPhotosAlbum({\r\n\t\t\t\t\t\t\t\tfilePath: that.posterImage,\r\n\t\t\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\t\t\tthat.posterImageClose();\r\n\t\t\t\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '保存成功',\r\n\t\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tfail: function(res) {\r\n\t\t\t\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '保存失败'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// #endif\r\n\t\t\tsetShareInfoStatus: function() {\r\n\t\t\t\tlet data = this.storeInfo;\r\n\t\t\t\tlet href = location.href;\r\n\t\t\t\tif (this.$wechat.isWeixin()) {\r\n\t\t\t\t\thref =\r\n\t\t\t\t\t\thref.indexOf(\"?\") === -1 ?\r\n\t\t\t\t\t\thref + \"?spread=\" + this.uid :\r\n\t\t\t\t\t\thref + \"&spread=\" + this.uid;\r\n\r\n\t\t\t\t\tlet configAppMessage = {\r\n\t\t\t\t\t\tdesc: data.storeInfo,\r\n\t\t\t\t\t\ttitle: data.storeName,\r\n\t\t\t\t\t\tlink: href,\r\n\t\t\t\t\t\timgUrl: data.image\r\n\t\t\t\t\t};\r\n\t\t\t\t\tthis.$wechat.wechatEvevt([\"updateAppMessageShareData\", \"updateTimelineShareData\"],\r\n\t\t\t\t\t\tconfigAppMessage)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tscroll: function(e) {\r\n\t\t\t\tvar that = this,\r\n\t\t\t\t\tscrollY = e.detail.scrollTop;\r\n\t\t\t\tvar opacity = scrollY / 200;\r\n\t\t\t\topacity = opacity > 1 ? 1 : opacity;\r\n\t\t\t\tthat.opacity = opacity\r\n\t\t\t\tthat.scrollY = scrollY\r\n\t\t\t\tif (that.lock) {\r\n\t\t\t\t\tthat.lock = false\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tfor (var i = 0; i < that.topArr.length; i++) {\r\n\t\t\t\t\tif (scrollY < that.topArr[i] - (app.globalData.navHeight / 2) + that.heightArr[i]) {\r\n\t\t\t\t\t\tthat.navActive = i\r\n\t\t\t\t\t\tbreak\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\ttap: function(item, index) {\r\n\t\t\t\tvar id = item.id;\r\n\t\t\t\tvar index = index;\r\n\t\t\t\tvar that = this;\r\n\t\t\t\t// if (!this.data.good_list.length && id == \"past2\") {\r\n\t\t\t\t//   id = \"past3\"\r\n\t\t\t\t// }\r\n\t\t\t\tthis.toView = id;\r\n\t\t\t\tthis.navActive = index;\r\n\t\t\t\tthis.lock = true;\r\n\t\t\t\tthis.scrollTop = index > 0 ? that.topArr[index] - (app.globalData.navHeight / 2) : that.topArr[index]\r\n\t\t\t},\r\n\t\t},\r\n\t\t//#ifdef MP\r\n\t\tonShareAppMessage() {\r\n\t\t\tlet that = this;\r\n\t\t\treturn {\r\n\t\t\t\ttitle: that.storeInfo.storeName,\r\n\t\t\t\tpath: app.globalData.openPages,\r\n\t\t\t\timageUrl: that.storeInfo.image\r\n\t\t\t};\r\n\t\t}\r\n\t\t//#endif\r\n\r\n\t}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t.userEvaluation{\r\n\t\ti{\r\n\t\t\tdisplay: inline-block;\r\n\t\t}\r\n\t}\r\n\t.attribute{\r\n\t\t.line1{\r\n\t\t\twidth: 600rpx;\r\n\t\t}\r\n\t}\r\n\t.share-box {\r\n\t\tz-index: 1000;\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\ttop: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\r\n\t\timage {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t}\r\n\t}\r\n\r\n\t.generate-posters {\r\n\t\twidth: 100%;\r\n\t\theight: 170rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\tbottom: 0;\r\n\t\tz-index: 1000;\r\n\t\ttransform: translate3d(0, 100%, 0);\r\n\t\ttransition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);\r\n\t\tborder-top: 1rpx solid #eee;\r\n\t}\r\n\r\n\t.generate-posters.on {\r\n\t\ttransform: translate3d(0, 0, 0);\r\n\t}\r\n\r\n\t.generate-posters .item {\r\n\t\tflex: 50%;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 30rpx;\r\n\t}\r\n\r\n\t.generate-posters .item .iconfont {\r\n\t\tfont-size: 80rpx;\r\n\t\tcolor: #5eae72;\r\n\t}\r\n\r\n\t.generate-posters .item .iconfont.icon-haibao {\r\n\t\tcolor: #5391f1;\r\n\t}\r\n\t.navbar .header {\r\n\t\theight: 96rpx;\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #050505;\r\n\t\tbackground-color: #fff;\r\n\t\t/* #ifdef MP */\r\n\t\tpadding-right: 95rpx;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.icon-xiangzuo {\r\n\t\t/* #ifdef H5 */\r\n\t\ttop: 30rpx !important;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.navbar .header .item {\r\n\t\tposition: relative;\r\n\t\tmargin: 0 25rpx;\r\n\t}\r\n\r\n\t.navbar .header .item.on:before {\r\n\t\tposition: absolute;\r\n\t\twidth: 60rpx;\r\n\t\theight: 5rpx;\r\n\t\tbackground-repeat: no-repeat;\r\n\t\tcontent: \"\";\r\n\t\tbackground-image: linear-gradient(to right, #ff3366 0%, #ff6533 100%);\r\n\t\tbottom: -10rpx;\r\n\t\tleft: 50%;\r\n\t\tmargin-left: -28rpx;\r\n\t}\r\n\r\n\t.navbar {\r\n\t\tposition: fixed;\r\n\t\tbackground-color: #fff;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tz-index: 999;\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.navbar .navbarH {\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.navbar .navbarH .navbarCon {\r\n\t\tposition: absolute;\r\n\t\tbottom: 0;\r\n\t\theight: 100rpx;\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.icon-xiangzuo {\r\n\t\tcolor: #000;\r\n\t\tposition: fixed;\r\n\t\tfont-size: 40rpx;\r\n\t\twidth: 100rpx;\r\n\t\theight: 56rpx;\r\n\t\tline-height: 54rpx;\r\n\t\tz-index: 1000;\r\n\t\tleft: 33rpx;\r\n\t}\r\n\r\n\t.product-con .wrapper {\r\n        margin-top: 30rpx;\r\n\t\tborder-radius: 14rpx;\r\n\t}\r\n\r\n\t.product-con .wrapper .share .money .y-money {\r\n\t\tcolor: #82848f;\r\n\t\tmargin-left: 13rpx;\r\n\t\ttext-decoration: line-through;\r\n\t\tfont-weight: normal;\r\n\t}\r\n\r\n\t.product-con .notice {\r\n\t\twidth: 100%;\r\n\t\theight: 62rpx;\r\n\t\tbackground-color: #ffedeb;\r\n\t\tpadding: 0 24rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.product-con .notice .num {\r\n\t\tfont-size: 24rpx;\r\n\t}\r\n\r\n\t.product-con .notice .num .iconfont {\r\n\t\tfont-size: 30rpx;\r\n\t\tvertical-align: -3rpx;\r\n\t\tmargin-right: 20rpx;\r\n\t}\r\n\r\n\t.product-con .notice .num .line {\r\n\t\tcolor: #333333;\r\n\t\tmargin-left: 15rpx;\r\n\t}\r\n\r\n\t.product-con .notice .swiper {\r\n\t\theight: 100%;\r\n\t\twidth: 360rpx;\r\n\t\tline-height: 62rpx;\r\n\t\toverflow: hidden;\r\n\t\tmargin-left: 14rpx;\r\n\t}\r\n\r\n\t.product-con .notice .swiper swiper {\r\n\t\theight: 100%;\r\n\t\twidth: 100%;\r\n\t\toverflow: hidden;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\t.product-con .assemble {\r\n\t\tbackground-color: #fff;\r\n\t}\r\n\r\n\t.product-con .assemble .item {\r\n\t\tpadding-right: 24rpx;\r\n\t\tmargin-left: 24rpx;\r\n\t\tborder-bottom: 1rpx solid #f0f0f0;\r\n\t\theight: 130rpx;\r\n\t}\r\n\r\n\t.product-con .assemble .item .pictxt .text {\r\n\t\twidth: 172rpx;\r\n\t\tmargin-left: 16rpx;\r\n\t}\r\n\r\n\t.product-con .assemble .item .pictxt .pictrue {\r\n\t\twidth: 80rpx;\r\n\t\theight: 80rpx;\r\n\t\tbackground: #f0f0f0;\r\n\t\tborder-radius: 50%;\r\n\t}\r\n\r\n\t.product-con .assemble .item .pictxt .pictrue image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 50%;\r\n\t}\r\n\r\n\t.product-con .assemble .item .right .lack {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\t.product-con .assemble .item .right .time {\r\n\t\tposition: relative;\r\n\t\tleft: -10rpx;\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #82848f;\r\n\t\tmargin-top: 5rpx;\r\n\t}\r\n\r\n\t.product-con .assemble .item .right .spellBnt {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #fff;\r\n\t\twidth: 140rpx;\r\n\t\theight: 50rpx;\r\n\t\tborder-radius: 50rpx;\r\n\t\tbackground: linear-gradient(90deg, #FF5555 0%, #FF0000 100%);\r\n\t\ttext-align: center;\r\n\t\tline-height: 50rpx;\r\n\t\tmargin-left: 16rpx;\r\n\t}\r\n\r\n\t.product-con .assemble .item .right .spellBnt .iconfont {\r\n\t\tfont-size: 25rpx;\r\n\t\tmargin-left: 5rpx;\r\n\t}\r\n\r\n\t.product-con .assemble .more {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #333333;\r\n\t\ttext-align: center;\r\n\t\theight: 90rpx;\r\n\t\tline-height: 90rpx;\r\n\t}\r\n\r\n\t.product-con .assemble .more .iconfont {\r\n\t\tmargin-left: 13rpx;\r\n\t\tfont-size: 25rpx;\r\n\t}\r\n\r\n\t.product-con .playWay {\r\n\t\tbackground-color: #fff;\r\n\t\tpadding: 0 24rpx;\r\n\t\tmargin-top: 20rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\t.product-con .playWay .title {\r\n\t\theight: 86rpx;\r\n\t\tborder-bottom: 1rpx solid #eee;\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n\r\n\t.product-con .playWay .title .iconfont {\r\n\t\tmargin-left: 13rpx;\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #717171;\r\n\t}\r\n\r\n\t.product-con .playWay .way {\r\n\t\theight: 110rpx;\r\n\t\tline-height: 110rpx;\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\t.product-con .playWay .way .iconfont {\r\n\t\tcolor: #cdcdcd;\r\n\t\tfont-size: 26rpx;\r\n\t\tmargin: 0 35rpx;\r\n\t}\r\n\r\n\t.product-con .playWay .way .item .num {\r\n\t\tfont-size: 35rpx;\r\n\t\tvertical-align: 4rpx;\r\n\t\tmargin-right: 6rpx;\r\n\t\tdisplay: inline-block;\r\n\t}\r\n\r\n\t.product-con .playWay .way .item .tip {\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #a5a5a5;\r\n\t\tmargin-top: 7rpx;\r\n\t}\r\n\r\n\t.product-con .footer {\r\n\t\tpadding: 0 20rpx 0 30rpx;\r\n\t\tposition: fixed;\r\n\t\tbottom: 0;\r\n\t\twidth: 100%;\r\n\t\tbox-sizing: border-box;\r\n\t\theight: 100rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tz-index: 999;\r\n\t\tborder-top: 1rpx solid #f0f0f0;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.product-con .footer .item {\r\n\t\tfont-size: 18rpx;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.product-con .footer .item .iconfont {\r\n\t\ttext-align: center;\r\n\t\tfont-size: 40rpx;\r\n\t}\r\n\r\n\t.product-con .footer .item .iconfont.icon-shoucang1 {\r\n\t\tcolor: #f00;\r\n\t}\r\n\r\n\t.product-con .footer .item .iconfont.icon-gouwuche1 {\r\n\t\tfont-size: 40rpx;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.product-con .footer .item .iconfont.icon-gouwuche1 .num {\r\n\t\tcolor: #fff;\r\n\t\tposition: absolute;\r\n\t\tfont-size: 18rpx;\r\n\t\tpadding: 2rpx 8rpx 3rpx;\r\n\t\tborder-radius: 200rpx;\r\n\t\ttop: -10rpx;\r\n\t\tright: -10rpx;\r\n\t}\r\n\r\n\t.product-con .footer .bnt {\r\n\t\twidth: 540rpx;\r\n\t\theight: 76rpx;\r\n\t}\r\n\r\n\t.product-con .footer .bnt .bnts {\r\n\t\twidth: 270rpx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 76rpx;\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n\r\n\t.product-con .footer .bnt .joinCart {\r\n\t\tborder-radius: 50rpx 0 0 50rpx;\r\n\t\tbackground-image: linear-gradient(to right, #fea10f 0%, #fa8013 100%);\r\n\t}\r\n\r\n\t.product-con .footer .bnt .buy {\r\n\t\tborder-radius: 0 50rpx 50rpx 0;\r\n\t\tbackground-image: linear-gradient(to right, #fa6514 0%, #c9ab79 100%);\r\n\t}\r\n\r\n\t.setCollectBox {\r\n\t\tfont-size: 18rpx;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.canvas {\r\n\t\tposition: fixed;\r\n\t\tz-index: -5;\r\n\t\topacity: 0;\r\n\t}\r\n\r\n\t.poster-pop {\r\n\t\twidth: 450rpx;\r\n\t\theight: 714rpx;\r\n\t\tposition: fixed;\r\n\t\tleft: 50%;\r\n\t\ttransform: translateX(-50%);\r\n\t\tz-index: 99;\r\n\t\ttop: 50%;\r\n\t\tmargin-top: -357rpx;\r\n\t}\r\n\r\n\t.poster-pop image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t.poster-pop .close {\r\n\t\twidth: 46rpx;\r\n\t\theight: 75rpx;\r\n\t\tposition: fixed;\r\n\t\tright: 0;\r\n\t\ttop: -73rpx;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t.poster-pop .save-poster {\r\n\t\tbackground-color: #df2d0a;\r\n\t\tfont-size: ：22rpx;\r\n\t\tcolor: #fff;\r\n\t\ttext-align: center;\r\n\t\theight: 76rpx;\r\n\t\tline-height: 76rpx;\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.poster-pop .keep {\r\n\t\tcolor: #fff;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 25rpx;\r\n\t\tmargin-top: 10rpx;\r\n\t}\r\n\r\n\t.mask {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.6);\r\n\t\tz-index: 9;\r\n\t}\r\n\r\n\t.pro-wrapper .iconn {\r\n\t\tbackground-image: url('data:image/png;base64,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');\r\n\t\twidth: 100rpx;\r\n\t\theight: 100rpx;\r\n\t\tbackground-repeat: no-repeat;\r\n\t\tbackground-size: 100% 100%;\r\n\t\tmargin: 0 auto;\r\n\t}\r\n\r\n\t.pro-wrapper .iconn.iconn1 {\r\n\t\tbackground-image: url('data:image/png;base64,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');\r\n\t}\r\n\r\n\t.home-nav {\r\n\t\t/* #ifdef H5 */\r\n\t\ttop: 20rpx !important;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.home-nav {\r\n\t\tcolor: #fff;\r\n\t\tposition: fixed;\r\n\t\tfont-size: 33rpx;\r\n\t\twidth: 56rpx;\r\n\t\theight: 56rpx;\r\n\t\tz-index: 999;\r\n\t\tleft: 33rpx;\r\n\t\tbackground: rgba(190, 190, 190, 0.5);\r\n\t\tborder-radius: 50%;\r\n\r\n\t\t&.on {\r\n\t\t\tbackground: unset;\r\n\t\t\tcolor: #333;\r\n\t\t}\r\n\t}\r\n\r\n\t.home-nav .line {\r\n\t\twidth: 1rpx;\r\n\t\theight: 24rpx;\r\n\t\tbackground: rgba(255, 255, 255, 0.25);\r\n\t}\r\n\r\n\t.home-nav .icon-xiangzuo {\r\n\t\twidth: auto;\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n\r\n\t.share-box {\r\n\t\tz-index: 1000;\r\n\t\tposition: fixed;\r\n\t\tleft: 0;\r\n\t\ttop: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.share-box image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=26bb9225&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=26bb9225&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363903337\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}