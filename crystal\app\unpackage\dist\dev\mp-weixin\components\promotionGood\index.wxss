@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.promotionGood.data-v-7b4f8cd0 {
  padding: 0 30rpx;
}
.promotionGood .item.data-v-7b4f8cd0 {
  border-bottom: 1rpx solid #eee;
  height: 250rpx;
}
.promotionGood .item .pictrue.data-v-7b4f8cd0 {
  position: relative;
  width: 188rpx;
  height: 188rpx;
}
.promotionGood .item .pictrue image.data-v-7b4f8cd0 {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}
.promotionGood .item .text.data-v-7b4f8cd0 {
  font-size: 24rpx;
  color: #999;
  width: 472rpx;
}
.promotionGood .item .text .name.data-v-7b4f8cd0 {
  font-size: 30rpx;
  color: #333;
}
.promotionGood .item .text .sp-money.data-v-7b4f8cd0 {
  margin: 34rpx 0 20rpx 0;
}
.promotionGood .item .text .sp-money .moneyCon.data-v-7b4f8cd0 {
  padding: 0 18rpx;
  background-color: red;
  height: 46rpx;
  line-height: 46rpx;
  background-image: linear-gradient(to right, #ff6248 0%, #ff3e1e 100%);
  font-size: 20rpx;
  color: #fff;
  border-radius: 24rpx 3rpx 24rpx 3rpx;
}
.promotionGood .item .text .sp-money .moneyCon .num.data-v-7b4f8cd0 {
  font-size: 24rpx;
}
.promotionGood .item .text .money.data-v-7b4f8cd0 {
  text-decoration: line-through;
}

