{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\order\\orderVideoSend.vue?vue&type=template&id=4b3177f0&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\order\\orderVideoSend.vue", "mtime": 1753666157911}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\n<el-dialog :visible.sync=\"modals\" title=\"发送货\" class=\"order_box\" :before-close=\"handleClose\" width=\"600px\">\n  <el-form ref=\"formItem\" :model=\"formItem\" label-width=\"110px\" @submit.native.prevent :rules=\"rules\">\n    <el-form-item label=\"快递公司：\" prop=\"expressCode\">\n      <el-select v-model=\"formItem.deliveryId\" filterable style=\"width:80%;\">\n        <el-option v-for=\"(item,i) in express\" :value=\"item.deliveryId\" :key=\"i\" :label=\"item.deliveryName\"></el-option>\n      </el-select>\n    </el-form-item>\n    <el-form-item label=\"快递单号：\" prop=\"waybillId\">\n      <el-input v-model=\"formItem.waybillId\" placeholder=\"请输入快递单号\" style=\"width:80%;\"></el-input>\n    </el-form-item>\n  </el-form>\n  <div slot=\"footer\">\n    <el-button size=\"mini\" type=\"primary\" @click=\"putSend('formItem')\" v-hasPermi=\"['admin:order:video:send']\">提交</el-button>\n    <el-button size=\"mini\" @click=\"cancel('formItem')\">取消</el-button>\n  </div>\n</el-dialog>\n", null]}