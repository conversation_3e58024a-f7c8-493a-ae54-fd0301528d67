{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\creatStore\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\creatStore\\index.vue", "mtime": 1753666157922}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport Tinymce from '@/components/Tinymce/index'\nimport { templateListApi, productCreateApi, categoryApi, productDetailApi, productUpdateApi } from '@/api/store'\nimport { marketingSendApi} from '@/api/marketing';\nimport { shippingTemplatesList } from '@/api/logistics'\nimport { goodDesignList } from \"@/api/systemGroup\";\nimport { clearTreeData } from '@/utils/ZBKJIutil'\nimport CreatTemplates from '@/views/systemSetting/logistics/shippingTemplates/creatTemplates'\nimport Templates from \"../../appSetting/wxAccount/wxTemplate/index\";\nimport {Debounce} from '@/utils/validate'\nconst defaultObj = {\n  image: '',\n  sliderImages: [],\n  videoLink:'',\n  sliderImage: '',\n  storeName: '',\n  storeInfo: '',\n  keyword: '',\n  cateIds: [], // 商品分类id\n  cateId: null, // 商品分类id传值\n  unitName: '',\n  sort: 0,\n  giveIntegral: 0,\n  ficti: 0,\n  isShow: false,\n  isBenefit: false,\n  isNew: false,\n  isGood: false,\n  isHot: false,\n  isBest: false,\n  tempId: '',\n  attrValue: [{\n    image: '',\n    price: 0,\n    cost: 0,\n    otPrice: 0,\n    stock: 0,\n    barCode: '',\n    weight: 0,\n    volume: 0\n  }],\n  attr: [],\n  selectRule: '',\n  isSub: false,\n  content: '',\n  specType: false,\n  id: 0,\n  couponIds: [],\n  coupons: [],\n  activity: ['默认','秒杀','砍价','拼团']\n}\nconst objTitle = {\n  price: {\n    title: '售价'\n  },\n  cost: {\n    title: '成本价'\n  },\n  otPrice: {\n    title: '原价'\n  },\n  stock: {\n    title: '库存'\n  },\n  barCode: {\n    title: '商品编号'\n  },\n  weight: {\n    title: '重量（KG）'\n  },\n  volume: {\n    title: '体积(m³)'\n  }\n}\nexport default {\n  name: 'ProductProductAdd',\n  components: {Templates, CreatTemplates,Tinymce },\n  data() {\n    return {\n      isDisabled: this.$route.params.isDisabled==='1'?true:false,\n      activity: { '默认': 'red', '秒杀': 'blue', '砍价': 'green', '拼团': 'yellow' },\n      props2: {\n        children: 'child',\n        label: 'name',\n        value: 'id',\n        multiple: true,\n        emitPath: false\n      },\n      checkboxGroup: [],\n      recommend: [],\n      tabs: [],\n      fullscreenLoading: false,\n      props: { multiple: true },\n      active: 0,\n      OneattrValue: [Object.assign({}, defaultObj.attrValue[0])], // 单规格\n      ManyAttrValue: [Object.assign({}, defaultObj.attrValue[0])], // 多规格\n      ruleList: [],\n      merCateList: [], // 商户分类筛选\n      shippingList: [], // 运费模板\n      formThead: Object.assign({}, objTitle),\n      formValidate: Object.assign({}, defaultObj),\n      formDynamics: {\n        ruleName: '',\n        ruleValue: []\n      },\n      tempData: {\n        page: 1,\n        limit: 9999\n      },\n      manyTabTit: {},\n      manyTabDate: {},\n      grid2: {\n        xl: 12,\n        lg: 12,\n        md: 12,\n        sm: 24,\n        xs: 24\n      },\n      // 规格数据\n      formDynamic: {\n        attrsName: '',\n        attrsVal: ''\n      },\n      isBtn: false,\n      manyFormValidate: [],\n      currentTab: 0,\n      isChoice: '',\n      grid: {\n        xl: 8,\n        lg: 8,\n        md: 12,\n        sm: 24,\n        xs: 24\n      },\n      ruleValidate: {\n        storeName: [\n          { required: true, message: '请输入商品名称', trigger: 'blur' }\n        ],\n        cateIds: [\n          { required: true, message: '请选择商品分类', trigger: 'change', type: 'array', min: '1' }\n        ],\n        keyword: [\n          { required: true, message: '请输入商品关键字', trigger: 'blur' }\n        ],\n        unitName: [\n          { required: true, message: '请输入单位', trigger: 'blur' }\n        ],\n        storeInfo: [\n          { required: true, message: '请输入商品简介', trigger: 'blur' }\n        ],\n        tempId: [\n          { required: true, message: '请选择运费模板', trigger: 'change' }\n        ],\n        image: [\n          { required: true, message: '请上传商品图', trigger: 'change' }\n        ],\n        sliderImages: [\n          { required: true, message: '请上传商品轮播图', type: 'array', trigger: 'change' }\n        ],\n        specType: [\n          { required: true, message: '请选择商品规格', trigger: 'change' }\n        ]\n      },\n      attrInfo: {},\n      tableFrom: {\n        page: 1,\n        limit: 9999,\n        keywords: ''\n      },\n      tempRoute: {},\n      keyNum: 0,\n      isAttr: false,\n      showAll:false,\n      videoLink: \"\",\n    }\n  },\n  computed: {\n    attrValue() {\n      const obj = Object.assign({}, defaultObj.attrValue[0])\n      delete obj.image\n      return obj\n    },\n    oneFormBatch() {\n      const obj = [Object.assign({}, defaultObj.attrValue[0])]\n      delete obj[0].barCode\n      return obj\n    }\n  },\n  watch: {\n    'formValidate.attr': {\n      handler: function(val) {\n        if (this.formValidate.specType && this.isAttr) this.watCh(val) //重要！！！\n      },\n      immediate: false,\n      deep: true\n    }\n  },\n  created() {\n    this.tempRoute = Object.assign({}, this.$route)\n    if (this.$route.params.id && this.formValidate.specType) {\n      this.$watch('formValidate.attr', this.watCh)\n    }\n  },\n  mounted() {\n    this.formValidate.sliderImages = []\n    if ( this.$route.params.id ) {\n      this.setTagsViewTitle()\n      this.getInfo()\n    }\n    this.getCategorySelect()\n    this.getShippingList()\n    this.getGoodsType()\n  },\n  methods: {\n    // 校验输入框不能输入0，保留2位小数，库存为正整数\n    keyupEvent(key, val, index, num) {\n      if (key === 'barCode') return;\n      var re = /^\\D*([0-9]\\d*\\.?\\d{0,2})?.*$/;\n      switch (num) {\n        case 1:\n          if (val == 0) {\n            this.oneFormBatch[index][key] = key === 'stock' ? 0 : 0.01;\n          } else {\n            this.oneFormBatch[index][key] =\n              key === 'stock'\n                ? parseInt(val)\n                : this.$set(this.oneFormBatch[index], key, val.toString().replace(re, '$1'));\n          }\n          break;\n        case 2:\n          if (val == 0) {\n            this.OneattrValue[index][key] = key === 'stock' ? 0 : 0.01;\n          } else {\n            this.OneattrValue[index][key] =\n              key === 'stock'\n                ? parseInt(val)\n                : this.$set(this.OneattrValue[index], key, val.toString().replace(re, '$1'));\n          }\n          break;\n        default:\n          if (val == 0) {\n            this.ManyAttrValue[index][key] = key === 'stock' ? 0 : 0.01;\n          } else {\n            this.ManyAttrValue[index][key] =\n              key === 'stock'\n                ? parseInt(val)\n                : this.$set(this.ManyAttrValue[index], key, val.toString().replace(re, '$1'));\n          }\n          break;\n      }\n    },\n    handleCloseCoupon(tag) {\n      this.isAttr = true\n      this.formValidate.coupons.splice(this.formValidate.coupons.indexOf(tag), 1)\n      this.formValidate.couponIds.splice(this.formValidate.couponIds.indexOf(tag.id), 1)\n    },\n    addCoupon() {\n      const _this = this\n      this.$modalCoupon('wu', this.keyNum += 1, this.formValidate.coupons, function(row) {\n        _this.formValidate.couponIds = []\n        _this.formValidate.coupons = row\n        row.map((item) => {\n          _this.formValidate.couponIds.push(item.id)\n        })\n      }, '')\n    },\n    setTagsViewTitle() {\n      const title = this.isDisabled?'商品详情':'编辑商品'\n      const route = Object.assign({}, this.tempRoute, { title: `${title}-${this.$route.params.id}` })\n      this.$store.dispatch('tagsView/updateVisitedView', route)\n    },\n    onChangeGroup() {\n      this.checkboxGroup.includes('isGood') ? this.formValidate.isGood = true : this.formValidate.isGood = false\n      this.checkboxGroup.includes('isBenefit') ? this.formValidate.isBenefit = true : this.formValidate.isBenefit = false\n      this.checkboxGroup.includes('isBest') ? this.formValidate.isBest = true : this.formValidate.isBest = false\n      this.checkboxGroup.includes('isNew') ? this.formValidate.isNew = true : this.formValidate.isNew = false\n      this.checkboxGroup.includes('isHot') ? this.formValidate.isHot = true : this.formValidate.isHot = false\n    },\n    watCh(val) {\n      const tmp = {}\n      const tmpTab = {}\n      this.formValidate.attr.forEach((o, i) => {\n        // tmp['value' + i] = { title: o.attrName }\n        // tmpTab['value' + i] = ''\n        tmp[o.attrName] = { title: o.attrName };\n        tmpTab[o.attrName] = '';\n      });\n      this.ManyAttrValue = this.attrFormat(val);\n      this.ManyAttrValue.forEach((val, index) => {\n        const key = Object.values(val.attrValue).sort().join('/')\n        if (this.attrInfo[key]) this.ManyAttrValue[index] = this.attrInfo[key]\n      })\n      this.attrInfo = [];\n      this.ManyAttrValue.forEach((val) => {\n        this.attrInfo[Object.values(val.attrValue).sort().join('/')] = val\n      })\n      this.manyTabTit = tmp\n      this.manyTabDate = tmpTab\n      this.formThead = Object.assign({}, this.formThead, tmp)\n    },\n    attrFormat(arr) {\n      let data = []\n      const res = []\n      return format(arr)\n      function format(arr) {\n        if (arr.length > 1) {\n          arr.forEach((v, i) => {\n            if (i === 0) data = arr[i]['attrValue']\n            const tmp = []\n            if(!data) return;\n            data.forEach(function(vv) {\n              arr[i + 1] && arr[i + 1]['attrValue'] && arr[i + 1]['attrValue'].forEach(g => {\n                const rep2 = (i !== 0 ? '' : arr[i]['attrName'] + '_') + vv + '$&' + arr[i + 1]['attrName'] + '_' + g\n                tmp.push(rep2)\n                if (i === (arr.length - 2)) {\n                  const rep4 = {\n                    image: '',\n                    price: 0,\n                    cost: 0,\n                    otPrice: 0,\n                    stock: 0,\n                    barCode: '',\n                    weight: 0,\n                    volume: 0,\n                    brokerage: 0,\n                    brokerage_two: 0\n                  }\n                  rep2.split('$&').forEach((h, k) => {\n                    const rep3 = h.split('_');\n                    if (!rep4['attrValue']) rep4['attrValue'] = {}\n                    rep4['attrValue'][rep3[0]] = rep3.length > 1 ? rep3[1] : ''\n                  })\n                  for (let attrValueKey in rep4.attrValue) {\n                    rep4[attrValueKey] = rep4.attrValue[attrValueKey];\n                  }\n                  res.push(rep4)\n                }\n              })\n            })\n            data = tmp.length ? tmp : []\n          })\n        } else {\n          const dataArr = []\n          arr.forEach((v, k) => {\n            v['attrValue'].forEach((vv, kk) => {\n              dataArr[kk] = v['attrName'] + '_' + vv\n              res[kk] = {\n                image: '',\n                price: 0,\n                cost: 0,\n                otPrice: 0,\n                stock: 0,\n                barCode: '',\n                weight: 0,\n                volume: 0,\n                brokerage: 0,\n                brokerage_two: 0,\n                attrValue: { [v['attrName']]: vv }\n              }\n              // Object.values(res[kk].attrValue).forEach((v, i) => {\n              //   res[kk]['value' + i] = v\n              // })\n              for (let attrValueKey in res[kk].attrValue) {\n                res[kk][attrValueKey] = res[kk].attrValue[attrValueKey];\n              }\n            })\n          })\n          data.push(dataArr.join('$&'))\n        }\n        return res\n      }\n    },\n    // 运费模板\n    addTem() {\n      this.$refs.addTemplates.dialogVisible = true\n      this.$refs.addTemplates.getCityList()\n    },\n    // 添加规则；\n    addRule() {\n      const _this = this\n      this.$modalAttr(this.formDynamics, function() {\n        _this.productGetRule()\n      })\n    },\n    // 选择规格\n    onChangeSpec(num) {\n      this.isAttr = true;\n      if (num) this.productGetRule()\n    },\n    // 选择属性确认\n    confirm() {\n      this.isAttr = true\n      if (!this.formValidate.selectRule) {\n        return this.$message.warning('请选择属性')\n      }\n      const data = []\n      this.ruleList.forEach(item => {\n        if (item.id === this.formValidate.selectRule) {\n          item.ruleValue.forEach(i => {\n            data.push({\n              attrName: i.value,\n              attrValue: i.detail\n            })\n          })\n        }\n        this.formValidate.attr = data;\n      });\n    },\n    // 商品分类；\n    getCategorySelect() {\n      categoryApi({ status: -1, type: 1 }).then(res => {\n        this.merCateList = this.filerMerCateList(res)\n        let newArr = [];\n        res.forEach((value,index) => {\n          newArr[index] = value;\n          if(value.child) newArr[index].child = value.child.filter(item => item.status === true)\n        }) //过滤商品分类设置为隐藏的子分类不出现在树形列表里\n        this.merCateList = this.filerMerCateList(newArr)\n      })\n    },\n    filerMerCateList(treeData) {\n      return treeData.map((item) => {\n        if(!item.child){\n          item.disabled = true\n        }\n        item.label = item.name\n        return item\n      })\n    },\n    // 获取商品属性模板；\n    productGetRule() {\n      templateListApi(this.tableFrom).then(res => {\n        const list = res.list\n        for (var i = 0; i < list.length; i++) {\n          list[i].ruleValue = JSON.parse(list[i].ruleValue)\n        }\n        this.ruleList = list\n      })\n    },\n    // 运费模板；\n    getShippingList() {\n      shippingTemplatesList(this.tempData).then(res => {\n        this.shippingList = res.list\n      })\n    },\n    showInput(item) {\n      this.$set(item, 'inputVisible', true)\n    },\n    onChangetype(item) {\n      if (item === 1) {\n        this.OneattrValue.map(item => {\n          this.$set(item, 'brokerage', null)\n          this.$set(item, 'brokerageTwo', null)\n        })\n        this.ManyAttrValue.map(item => {\n          this.$set(item, 'brokerage', null)\n          this.$set(item, 'brokerageTwo', null)\n        })\n      } else {\n        this.OneattrValue.map(item => {\n          delete item.brokerage\n          delete item.brokerageTwo\n          this.$set(item, 'brokerage', null)\n          this.$set(item, 'brokerageTwo', null)\n        })\n        this.ManyAttrValue.map(item => {\n          delete item.brokerage\n          delete item.brokerageTwo\n        })\n      }\n    },\n    // 删除表格中的属性\n    delAttrTable(index) {\n      this.ManyAttrValue.splice(index, 1)\n    },\n    // 批量添加\n    batchAdd() {\n      // if (!this.oneFormBatch[0].pic || !this.oneFormBatch[0].price || !this.oneFormBatch[0].cost || !this.oneFormBatch[0].ot_price ||\n      //     !this.oneFormBatch[0].stock || !this.oneFormBatch[0].bar_code) return this.$Message.warning('请填写完整的批量设置内容！');\n      for (const val of this.ManyAttrValue) {\n        this.$set(val, 'image', this.oneFormBatch[0].image)\n        this.$set(val, 'price', this.oneFormBatch[0].price)\n        this.$set(val, 'cost', this.oneFormBatch[0].cost)\n        this.$set(val, 'otPrice', this.oneFormBatch[0].otPrice)\n        this.$set(val, 'stock', this.oneFormBatch[0].stock)\n        this.$set(val, 'barCode', this.oneFormBatch[0].barCode)\n        this.$set(val, 'weight', this.oneFormBatch[0].weight)\n        this.$set(val, 'volume', this.oneFormBatch[0].volume)\n        this.$set(val, 'brokerage', this.oneFormBatch[0].brokerage)\n        this.$set(val, 'brokerageTwo', this.oneFormBatch[0].brokerageTwo)\n      }\n    },\n    // 添加按钮\n    addBtn() {\n      this.clearAttr()\n      this.isBtn = true\n    },\n    // 取消\n    offAttrName() {\n      this.isBtn = false\n    },\n    clearAttr() {\n      this.isAttr = true\n      this.formDynamic.attrsName = ''\n      this.formDynamic.attrsVal = ''\n    },\n    // 删除规格\n    handleRemoveAttr(index) {\n      this.isAttr = true\n      this.formValidate.attr.splice(index, 1)\n      this.manyFormValidate.splice(index, 1)\n    },\n    // 删除属性\n    handleClose(item, index) {\n      item.splice(index, 1)\n    },\n    // 添加规则名称\n    createAttrName() {\n      this.isAttr = true\n      if (this.formDynamic.attrsName && this.formDynamic.attrsVal) {\n        const data = {\n          attrName: this.formDynamic.attrsName,\n          attrValue: [\n            this.formDynamic.attrsVal\n          ]\n        }\n        this.formValidate.attr.push(data)\n        var hash = {}\n        this.formValidate.attr = this.formValidate.attr.reduce(function(item, next) {\n          /* eslint-disable */\n          hash[next.attrName] ? '' : hash[next.attrName] = true && item.push(next)\n          return item\n        }, [])\n        this.clearAttr()\n        this.isBtn = false\n      } else {\n        this.$Message.warning('请添加完整的规格！');\n      }\n    },\n    // 添加属性\n    createAttr (num, idx) {\n      this.isAttr = true\n      if (num) {\n        this.formValidate.attr[idx].attrValue.push(num);\n        var hash = {};\n        this.formValidate.attr[idx].attrValue = this.formValidate.attr[idx].attrValue.reduce(function (item, next) {\n          /* eslint-disable */\n          hash[next] ? '' : hash[next] = true && item.push(next);\n          return item\n        }, []);\n        this.formValidate.attr[idx].inputVisible = false\n      } else {\n        this.$message.warning('请添加属性');\n      }\n    },\n    //点击展示所有多规格属性\n    showAllSku(){\n        if(this.isAttr == false){\n          this.isAttr = true;\n          if (this.formValidate.specType && this.isAttr) this.watCh(this.formValidate.attr) //重要！！！\n        }else if(this.isAttr == true){\n          this.isAttr = false;\n          this.getInfo();\n        }\n    },\n    // 详情\n    getInfo () {\n      this.fullscreenLoading = true\n      productDetailApi(this.$route.params.id).then(async res => {\n        // this.isAttr = true;\n        let info = res\n        this.formValidate = {\n          image: this.$selfUtil.setDomain(info.image),\n          sliderImage: info.sliderImage,\n          sliderImages: JSON.parse(info.sliderImage),\n          storeName: info.storeName,\n          storeInfo: info.storeInfo,\n          keyword: info.keyword,\n          cateIds: info.cateId.split(','), // 商品分类id\n          cateId: info.cateId,// 商品分类id传值\n          unitName: info.unitName,\n          sort: info.sort,\n          isShow: info.isShow,\n          isBenefit: info.isBenefit,\n          isNew: info.isNew,\n          isGood: info.isGood,\n          isHot: info.isHot,\n          isBest: info.isBest,\n          tempId: info.tempId,\n          attr: info.attr,\n          attrValue: info.attrValue,\n          selectRule: info.selectRule,\n          isSub: info.isSub,\n          content: this.$selfUtil.replaceImgSrcHttps(info.content),\n          specType: info.specType,\n          id: info.id,\n          giveIntegral: info.giveIntegral,\n          ficti: info.ficti,\n          coupons: info.coupons,\n          couponIds: info.couponIds,\n          activity: info.activityStr ? info.activityStr.split(',') : ['默认','秒杀','砍价','拼团']\n        }\n        marketingSendApi({type:3}).then(res=>{\n          if(this.formValidate.couponIds !== null){\n            let ids = this.formValidate.couponIds.toString();\n            let arr = res.list;\n            let obj = {};\n            for (let i in arr) {\n              obj[arr[i].id] = arr[i];\n            }\n            let strArr = ids.split(',');\n            let newArr = [];\n            for (let item of strArr) {\n              if (obj[item]) {\n                newArr.push(obj[item]);\n              }\n            }\n            this.$set(this.formValidate,'coupons',newArr); //在编辑回显时，让返回数据中的优惠券id，通过接口匹配显示,\n          }\n        })\n        let imgs = JSON.parse(info.sliderImage)\n        let imgss = []\n        Object.keys(imgs).map(i => {\n          imgss.push(this.$selfUtil.setDomain(imgs[i]))\n        })\n        this.formValidate.sliderImages = [ ...imgss ]\n        if(info.isHot) this.checkboxGroup.push('isHot')\n        if(info.isGood) this.checkboxGroup.push('isGood')\n        if(info.isBenefit) this.checkboxGroup.push('isBenefit')\n        if(info.isBest) this.checkboxGroup.push('isBest')\n        if(info.isNew) this.checkboxGroup.push('isNew')\n        this.productGetRule()\n        if(info.specType){\n          this.formValidate.attr = info.attr.map(item => {\n            return {\n              attrName : item.attrName,\n              attrValue: item.attrValues.split(',')\n            }\n          })\n          this.ManyAttrValue = info.attrValue;\n          this.ManyAttrValue.forEach((val) => {\n            val.image = this.$selfUtil.setDomain(val.image);\n            val.attrValue = JSON.parse(val.attrValue);\n            this.attrInfo[Object.values(val.attrValue).sort().join('/')] = val;\n          })\n          /***多规格商品如果被删除过sku，优先展示api返回的数据,否则会有没有删除的错觉***/\n          let manyAttr = this.attrFormat(this.formValidate.attr)\n          if(manyAttr.length !== this.ManyAttrValue.length){\n            this.$set(this,'showAll',true)\n            this.isAttr = false;\n          }else{\n            this.isAttr = true;\n          }\n          /*******/\n          const tmp = {}\n          const tmpTab = {}\n          this.formValidate.attr.forEach((o, i) => {\n            // tmp['value' + i] = { title: o.attrName }\n            // tmpTab['value' + i] = ''\n            tmp[o.attrName] = { title: o.attrName };\n            tmpTab[o.attrName] = '';\n          })\n\n          // 此处手动实现后台原本value0 value1的逻辑\n          this.formValidate.attrValue.forEach(item => {\n            for (let attrValueKey in item.attrValue) {\n             item[attrValueKey] = item.attrValue[attrValueKey];\n            }\n          });\n\n          this.manyTabTit = tmp\n          this.manyTabDate = tmpTab\n          this.formThead = Object.assign({}, this.formThead, tmp)\n        }else{\n          this.OneattrValue = info.attrValue\n          // this.formValidate.attr = [] //单规格商品规格设置为空\n        }\n        this.fullscreenLoading = false\n      }).catch(res => {\n        this.fullscreenLoading = false\n        this.$message.error(res.message);\n      });\n    },\n    handleRemove (i) {\n      this.formValidate.sliderImages.splice(i, 1)\n    },\n    // 点击商品图\n    modalPicTap (tit, num, i,status) {\n      const _this = this;\n      if(_this.isDisabled) return;\n      this.$modalUpload(function(img) {\n        if(tit==='1'&& !num){\n          _this.formValidate.image = img[0].sattDir\n          _this.OneattrValue[0].image = img[0].sattDir\n        }\n        if(tit==='2'&& !num){\n          if(img.length>10) return this.$message.warning(\"最多选择10张图片！\");\n          if(img.length + _this.formValidate.sliderImages.length > 10) return this.$message.warning(\"最多选择10张图片！\");\n          img.map((item) => {\n            _this.formValidate.sliderImages.push(item.sattDir)\n          });\n        }\n        if(tit==='1'&& num === 'dan' ){\n          _this.OneattrValue[0].image = img[0].sattDir\n        }\n        if(tit==='1'&& num === 'duo' ){\n          _this.ManyAttrValue[i].image = img[0].sattDir\n        }\n        if(tit==='1'&& num === 'pi' ){\n          _this.oneFormBatch[0].image = img[0].sattDir\n        }\n      },tit, 'content')\n    },\n    handleSubmitUp(){\n      // this.currentTab --\n      if (this.currentTab-- <0) this.currentTab = 0;\n    },\n    handleSubmitNest(name){\n      this.$refs[name].validate((valid) => {\n        if (valid) {\n          if (this.currentTab++ > 2) this.currentTab = 0;\n        } else {\n          if(!this.formValidate.store_name || !this.formValidate.cate_id || !this.formValidate.keyword\n            || !this.formValidate.unit_name || !this.formValidate.store_info\n            || !this.formValidate.image || !this.formValidate.slider_image){\n            this.$message.warning(\"请填写完整商品信息！\");\n          }\n        }\n      })\n    },\n    // 提交\n    handleSubmit:Debounce(function(name) {\n      this.onChangeGroup()\n      if( this.formValidate.specType && this.formValidate.attr.length < 1 ) return this.$message.warning(\"请填写多规格属性！\");\n      this.formValidate.cateId = this.formValidate.cateIds.join(',')\n      this.formValidate.sliderImage = JSON.stringify(this.formValidate.sliderImages)\n      if(this.formValidate.specType){\n        this.formValidate.attrValue=this.ManyAttrValue;\n        this.formValidate.attr = this.formValidate.attr.map((item) =>{\n          return {\n            attrName:item.attrName,\n            id:item.id,\n            attrValues:item.attrValue.join(','),\n          }\n        })\n        for (var i = 0; i < this.formValidate.attrValue.length; i++) {\n          this.$set(this.formValidate.attrValue[i],'id',0);\n          this.$set(this.formValidate.attrValue[i],'productId',0);\n          this.$set(this.formValidate.attrValue[i],'attrValue',JSON.stringify(this.formValidate.attrValue[i].attrValue)); //\n          delete this.formValidate.attrValue[i].value0\n        }\n      }else{\n        this.formValidate.attr = [{attrName:'规格',attrValues:'默认',id: this.$route.params.id? this.formValidate.attr[0].id : 0}]\n        this.OneattrValue.map(item => {\n          this.$set(item, 'attrValue', JSON.stringify({'规格':'默认'}))\n        })\n        this.formValidate.attrValue=this.OneattrValue\n      }\n      this.$refs[name].validate((valid) => {\n        if (valid) {\n          this.fullscreenLoading = true\n          this.$route.params.id?productUpdateApi(this.formValidate).then(async res => {\n            this.$message.success('编辑成功');\n            setTimeout(() => {\n              this.$router.push({ path: '/store/index' });\n            }, 500);\n            this.fullscreenLoading = false\n          }).catch(res => {\n            this.fullscreenLoading = false\n          }):productCreateApi(this.formValidate).then(async res => {\n            this.$message.success('新增成功');\n            setTimeout(() => {\n              this.$router.push({ path: '/store/index' });\n            }, 500);\n            this.fullscreenLoading = false\n          }).catch(res => {\n            this.fullscreenLoading = false\n          })\n        } else {\n          if(!this.formValidate.storeName || !this.formValidate.cateId  || !this.formValidate.keyword\n            || !this.formValidate.unitName || !this.formValidate.storeInfo || !this.formValidate.image || !this.formValidate.sliderImages){\n            this.$message.warning(\"请填写完整商品信息！\");\n          }\n        }\n      });\n    }),\n    // 表单验证\n    validate (prop, status, error) {\n      if (status === false) {\n        this.$message.warning(error);\n      }\n    },\n    // 移动\n    handleDragStart (e, item) {\n      if(!this.isDisabled) this.dragging = item;\n    },\n    handleDragEnd (e, item) {\n      if(!this.isDisabled) this.dragging = null\n    },\n    handleDragOver (e) {\n      if(!this.isDisabled) e.dataTransfer.dropEffect = 'move'\n    },\n    handleDragEnter (e, item) {\n      if(!this.isDisabled){\n        e.dataTransfer.effectAllowed = 'move'\n        if (item === this.dragging) {\n          return\n        }\n        const newItems = [...this.formValidate.sliderImages]\n        const src = newItems.indexOf(this.dragging)\n        const dst = newItems.indexOf(item)\n        newItems.splice(dst, 0, ...newItems.splice(src, 1))\n        this.formValidate.sliderImages = newItems;\n      }\n    },\n    handleDragEnterFont(e, item) {\n      if(!this.isDisabled){\n        e.dataTransfer.effectAllowed = 'move'\n        if (item === this.dragging) {\n          return\n        }\n        const newItems = [...this.formValidate.activity]\n        const src = newItems.indexOf(this.dragging)\n        const dst = newItems.indexOf(item)\n        newItems.splice(dst, 0, ...newItems.splice(src, 1))\n        this.formValidate.activity = newItems;\n      }\n    },\n    getGoodsType(){\n      /** 让商品推荐列表的name属性与页面设置tab的name匹配**/\n      goodDesignList({gid:70}).then((response)=>{\n        let list = response.list;\n        let arr = [],arr1 = [];\n        const listArr = [{ name: '是否热卖', value: 'isGood' }];\n        let typeLists = [ \n          { name: '', value: 'isHot',type:'2' },   //热门榜单 \n          { name: '', value: 'isBenefit' ,type:'4'}, //促销单品\n          { name: '', value: 'isBest',type:'1' }, //精品推荐\n          { name: '', value: 'isNew',type:'3' }]; //首发新品\n        list.forEach((item)=>{\n          let obj = {};\n          obj.value = JSON.parse(item.value);\n          obj.id = item.id;\n          obj.gid = item.gid;\n          obj.status = item.status;\n          arr.push(obj);\n        })\n        arr.forEach((item1)=>{\n          let obj1 = {};\n          obj1.name = item1.value.fields[1].value;\n          obj1.status = item1.status;\n          obj1.type = item1.value.fields[3].value;\n          arr1.push(obj1);\n        })\n        typeLists.forEach((item)=>{\n          arr1.forEach((item1)=>{\n            if(item.type == item1.type){\n              listArr.push({\n                name:item1.name,\n                value:item.value,\n                type:item.type\n              })\n            }\n          })\n        })\n        this.recommend = listArr\n      })\n    },\n  }\n}\n", null]}