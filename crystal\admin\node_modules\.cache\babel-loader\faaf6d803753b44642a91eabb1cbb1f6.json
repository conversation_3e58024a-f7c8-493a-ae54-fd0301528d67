{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\userprocess.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\userprocess.vue", "mtime": 1753666157945}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _userprocess = require(\"@/api/userprocess\");\nvar _userprocessAddAndUpdate = _interopRequireDefault(require(\"./userprocess-add-and-update\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  data: function data() {\n    return {\n      userId: '',\n      dataList: [],\n      dataListLoading: false,\n      dataListSelections: [],\n      addOrUpdateVisible: false\n    };\n  },\n  components: {\n    AddOrUpdate: _userprocessAddAndUpdate.default\n  },\n  mounted: function mounted() {\n    this.userId = this.$route.query.id;\n    this.getDataList();\n  },\n  methods: {\n    // 获取数据列表\n    getDataList: function getDataList() {\n      var _this = this;\n      this.dataListLoading = true;\n      (0, _userprocess.userprocessFindByUserId)({\n        userId: this.userId\n      }).then(function (res) {\n        _this.dataList = res || [];\n        _this.dataListLoading = false;\n      }).catch(function () {\n        _this.dataList = [];\n        _this.dataListLoading = false;\n      });\n    },\n    // 多选\n    selectionChangeHandle: function selectionChangeHandle(val) {\n      this.dataListSelections = val;\n    },\n    // 新增 / 修改\n    addOrUpdateHandle: function addOrUpdateHandle(id) {\n      var _this2 = this;\n      this.addOrUpdateVisible = true;\n      this.$nextTick(function () {\n        _this2.$refs.addOrUpdate.init(id, _this2.userId);\n      });\n    },\n    // 删除\n    deleteHandle: function deleteHandle(id) {\n      var _this3 = this;\n      // var ids = id ? [id] : this.dataListSelections.map(item => {\n      //   return item.id\n      // })\n      this.$confirm(\"\\u786E\\u5B9A\\u5220\\u9664\\u64CD\\u4F5C?\", '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(function () {\n        questionDeleteApi(id).then(function () {\n          _this3.$message.success(\"删除成功\");\n          _this3.getDataList();\n        }).catch(function (res) {\n          _this3.$message.error(res.message);\n        });\n      });\n    }\n  }\n};", null]}