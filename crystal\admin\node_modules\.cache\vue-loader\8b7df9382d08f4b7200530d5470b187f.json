{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\Breadcrumb\\index.vue?vue&type=template&id=b50ef614&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\Breadcrumb\\index.vue", "mtime": 1753666157755}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"el-breadcrumb\",\n    { staticClass: \"app-breadcrumb\", attrs: { separator: \"/\" } },\n    [\n      _c(\n        \"transition-group\",\n        { attrs: { name: \"breadcrumb\" } },\n        _vm._l(_vm.levelList, function (item, index) {\n          return _c(\"el-breadcrumb-item\", { key: item.path }, [\n            item.redirect === \"noRedirect\" || index == _vm.levelList.length - 1\n              ? _c(\"span\", { staticClass: \"no-redirect\" }, [\n                  _vm._v(_vm._s(item.meta.title)),\n                ])\n              : _c(\n                  \"a\",\n                  {\n                    on: {\n                      click: function ($event) {\n                        $event.preventDefault()\n                        return _vm.handleLink(item)\n                      },\n                    },\n                  },\n                  [_vm._v(_vm._s(item.meta.title))]\n                ),\n          ])\n        }),\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}