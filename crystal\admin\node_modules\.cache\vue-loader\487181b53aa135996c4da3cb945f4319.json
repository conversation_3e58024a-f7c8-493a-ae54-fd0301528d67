{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\group\\index.vue?vue&type=template&id=fdc6b3d0&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\group\\index.vue", "mtime": 1753666157940}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"divBox\" },\n    [\n      _c(\n        \"el-card\",\n        { staticClass: \"box-card\" },\n        [\n          _c(\n            \"div\",\n            {\n              staticClass: \"clearfix\",\n              attrs: { slot: \"header\" },\n              slot: \"header\",\n            },\n            [\n              _c(\n                \"el-button\",\n                {\n                  directives: [\n                    {\n                      name: \"hasPermi\",\n                      rawName: \"v-hasPermi\",\n                      value: [\"admin:user:group:save\", \"admin:user:tag:save\"],\n                      expression:\n                        \"['admin:user:group:save','admin:user:tag:save']\",\n                    },\n                  ],\n                  attrs: { size: \"small\", type: \"primary\" },\n                  on: {\n                    click: function ($event) {\n                      return _vm.onAdd(null)\n                    },\n                  },\n                },\n                [\n                  _vm._v(\n                    _vm._s(\n                      _vm.$route.path.indexOf(\"group\") !== -1\n                        ? \"添加用户分组\"\n                        : \"添加用户标签\"\n                    )\n                  ),\n                ]\n              ),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-table\",\n            {\n              directives: [\n                {\n                  name: \"loading\",\n                  rawName: \"v-loading\",\n                  value: _vm.listLoading,\n                  expression: \"listLoading\",\n                },\n              ],\n              staticStyle: { width: \"100%\" },\n              attrs: { data: _vm.tableData.data, size: \"small\" },\n            },\n            [\n              _c(\"el-table-column\", {\n                attrs: { label: \"ID\", \"min-width\": \"80\", prop: \"id\" },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label:\n                    _vm.$route.path.indexOf(\"group\") !== -1\n                      ? \"分组名称\"\n                      : \"标签名称\",\n                  \"min-width\": \"180\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (ref) {\n                      var row = ref.row\n                      return [\n                        _c(\"span\", {\n                          domProps: {\n                            textContent: _vm._s(\n                              _vm.$route.path.indexOf(\"group\") !== -1\n                                ? row.groupName\n                                : row.name\n                            ),\n                          },\n                        }),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: \"操作\",\n                  \"min-width\": \"120\",\n                  fixed: \"right\",\n                  align: \"center\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\n                          \"el-button\",\n                          {\n                            directives: [\n                              {\n                                name: \"hasPermi\",\n                                rawName: \"v-hasPermi\",\n                                value: [\n                                  \"admin:user:group:update\",\n                                  \"admin:user:tag:update\",\n                                ],\n                                expression:\n                                  \"['admin:user:group:update','admin:user:tag:update']\",\n                              },\n                            ],\n                            staticClass: \"mr10\",\n                            attrs: { type: \"text\", size: \"small\" },\n                            on: {\n                              click: function ($event) {\n                                return _vm.onAdd(scope.row)\n                              },\n                            },\n                          },\n                          [_vm._v(\"编辑\")]\n                        ),\n                        _vm._v(\" \"),\n                        _c(\n                          \"el-button\",\n                          {\n                            directives: [\n                              {\n                                name: \"hasPermi\",\n                                rawName: \"v-hasPermi\",\n                                value: [\n                                  \"admin:user:group:delete\",\n                                  \"admin:user:tag:delete\",\n                                ],\n                                expression:\n                                  \"['admin:user:group:delete','admin:user:tag:delete']\",\n                              },\n                            ],\n                            attrs: { type: \"text\", size: \"small\", disable: \"\" },\n                            on: {\n                              click: function ($event) {\n                                return _vm.handleDelete(\n                                  scope.row.id,\n                                  scope.$index\n                                )\n                              },\n                            },\n                          },\n                          [_vm._v(\"删除\")]\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"div\",\n            { staticClass: \"block\" },\n            [\n              _c(\"el-pagination\", {\n                attrs: {\n                  \"page-sizes\": [20, 40, 60, 80],\n                  \"page-size\": _vm.tableFrom.limit,\n                  \"current-page\": _vm.tableFrom.page,\n                  layout: \"total, sizes, prev, pager, next, jumper\",\n                  total: _vm.tableData.total,\n                },\n                on: {\n                  \"size-change\": _vm.handleSizeChange,\n                  \"current-change\": _vm.pageChange,\n                },\n              }),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}