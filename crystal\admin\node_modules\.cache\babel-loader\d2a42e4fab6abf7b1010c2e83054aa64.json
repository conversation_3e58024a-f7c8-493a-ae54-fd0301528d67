{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\mailun\\question-user-record-detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\mailun\\question-user-record-detail.vue", "mtime": 1753666157878}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\"use strict\";\n\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _questionuser = require(\"@/api/questionuser\");\nvar XLSX = _interopRequireWildcard(require(\"xlsx\"));\nfunction _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != _typeof(e) && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); }\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  data: function data() {\n    return {\n      visible: false,\n      loading: false,\n      userInfo: {},\n      questionOptions: [],\n      chakraData: [{\n        name: \"海底轮\",\n        enName: \"Root Chakra\",\n        value: 0,\n        color: \"#993734\"\n      }, {\n        name: \"脐轮\",\n        enName: \"Sacral Chakra\",\n        value: 0,\n        color: \"#be6f2a\"\n      }, {\n        name: \"太阳轮\",\n        enName: \"Solar Plexus\",\n        value: 0,\n        color: \"#d7c34a\"\n      }, {\n        name: \"心轮\",\n        enName: \"Heart Chakra\",\n        value: 0,\n        color: \"#5f9057\"\n      }, {\n        name: \"喉轮\",\n        enName: \"Throat Chakra\",\n        value: 0,\n        color: \"#5b8aa4\"\n      }, {\n        name: \"眉心轮\",\n        enName: \"Third Eye\",\n        value: 0,\n        color: \"#2c3485\"\n      }, {\n        name: \"顶轮\",\n        enName: \"Crown Chakra\",\n        value: 0,\n        color: \"#7e4997\"\n      }]\n    };\n  },\n  methods: {\n    init: function init(id) {\n      var _this = this;\n      this.visible = true;\n      this.loading = true;\n      this.userInfo = {};\n      this.questionOptions = [];\n      (0, _questionuser.questionuserDetailApi)(id).then(function (res) {\n        console.log(res);\n        _this.userInfo = res || {};\n        _this.questionOptions = res.questionUserOptionEntities || [];\n\n        // 更新脉轮数据\n        if (_this.userInfo.status == 1) {\n          _this.chakraData[0].value = _this.userInfo.root || 0;\n          _this.chakraData[1].value = _this.userInfo.sacral || 0;\n          _this.chakraData[2].value = _this.userInfo.navel || 0;\n          _this.chakraData[3].value = _this.userInfo.heart || 0;\n          _this.chakraData[4].value = _this.userInfo.throat || 0;\n          _this.chakraData[5].value = _this.userInfo.thirdEye || 0;\n          _this.chakraData[6].value = _this.userInfo.crown || 0;\n        }\n        _this.loading = false;\n      }).catch(function (e) {\n        _this.loading = false;\n        console.log(e);\n        _this.$message.error('获取详情失败');\n      });\n    },\n    // 计算进度条百分比（假设最大值为100）\n    getProgressPercentage: function getProgressPercentage(value) {\n      var maxValue = 100;\n      return Math.min(value / maxValue * 100, 100);\n    },\n    // 复制脉轮数据\n    copyChakraData: function copyChakraData() {\n      var text = this.chakraData.map(function (chakra) {\n        return \"\".concat(chakra.name, \": \").concat(chakra.value || '0');\n      }).join('\\n');\n\n      // 创建临时文本区域来复制文本\n      var textArea = document.createElement('textarea');\n      textArea.value = text;\n      document.body.appendChild(textArea);\n      textArea.select();\n      try {\n        document.execCommand('copy');\n        this.$message.success('脉轮数据已复制到剪贴板');\n      } catch (err) {\n        this.$message.error('复制失败，请手动复制');\n      }\n      document.body.removeChild(textArea);\n    },\n    // 导出详情\n    exportDetail: function exportDetail() {\n      if (!this.userInfo.id) {\n        this.$message.error('没有可导出的数据');\n        return;\n      }\n\n      // 准备用户基本信息\n      var userBasicInfo = {\n        '用户名': this.userInfo.username || '-',\n        '手机号': this.userInfo.mobile || '-',\n        '测试状态': this.userInfo.status == 1 ? '已提交' : '未提交',\n        '总分': this.userInfo.points || 0,\n        '创建时间': this.userInfo.addTime || '-',\n        '更新时间': this.userInfo.updateTime || '-'\n      };\n\n      // 准备脉轮数据\n      var chakraInfo = {};\n      this.chakraData.forEach(function (chakra) {\n        chakraInfo[chakra.name] = chakra.value || '0';\n      });\n\n      // 准备答题详情\n      var questionDetails = this.questionOptions.map(function (item, index) {\n        return {\n          '题目序号': index + 1,\n          '题目内容': item.questionName || '题目' + (index + 1),\n          '选择答案': item.optionName || '-'\n        };\n      });\n\n      // 创建工作簿\n      var wb = XLSX.utils.book_new();\n\n      // 添加用户基本信息工作表\n      var userWs = XLSX.utils.json_to_sheet([userBasicInfo]);\n      XLSX.utils.book_append_sheet(wb, userWs, '用户信息');\n\n      // 添加脉轮数据工作表\n      var chakraWs = XLSX.utils.json_to_sheet([chakraInfo]);\n      XLSX.utils.book_append_sheet(wb, chakraWs, '脉轮数据');\n\n      // 添加答题详情工作表\n      if (questionDetails.length > 0) {\n        var questionWs = XLSX.utils.json_to_sheet(questionDetails);\n        XLSX.utils.book_append_sheet(wb, questionWs, '答题详情');\n      }\n\n      // 下载文件\n      var fileName = \"\\u8109\\u8F6E\\u6D4B\\u8BD5\\u8BE6\\u60C5_\".concat(this.userInfo.username || this.userInfo.id, \"_\").concat(new Date().toISOString().slice(0, 10), \".xlsx\");\n      XLSX.writeFile(wb, fileName);\n      this.$message.success('导出成功');\n    }\n  }\n};", null]}