<view class="data-v-6951518e"><view class="bill-details data-v-6951518e"><view class="nav acea-row data-v-6951518e"><view data-event-opts="{{[['tap',[['changeType',['all']]]]]}}" class="{{['item','data-v-6951518e',type==='all'?'on':'']}}" bindtap="__e">全部</view><view data-event-opts="{{[['tap',[['changeType',['expenditure']]]]]}}" class="{{['item','data-v-6951518e',type==='expenditure'?'on':'']}}" bindtap="__e">消费</view><view data-event-opts="{{[['tap',[['changeType',['income']]]]]}}" class="{{['item','data-v-6951518e',type==='income'?'on':'']}}" bindtap="__e">充值</view></view><view class="sign-record data-v-6951518e"><block wx:for="{{userBillList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="list pad30 data-v-6951518e"><view class="item data-v-6951518e"><view class="data data-v-6951518e">{{item.date}}</view><view class="listn borRadius14 data-v-6951518e"><block wx:for="{{item.list}}" wx:for-item="vo" wx:for-index="indexn" wx:key="indexn"><view class="itemn acea-row row-between-wrapper data-v-6951518e"><view class="data-v-6951518e"><view class="name line1 data-v-6951518e">{{vo.title}}</view><view class="data-v-6951518e">{{vo.add_time}}</view></view><block wx:if="{{vo.pm}}"><view class="num data-v-6951518e">{{"+"+vo.number}}</view></block><block wx:else><view class="num font-color data-v-6951518e">{{"-"+vo.number}}</view></block></view></block></view></view></view></block><block wx:if="{{$root.g0>0}}"><view class="loadingicon acea-row row-center-wrapper data-v-6951518e"><text class="loading iconfont icon-jiazai data-v-6951518e" hidden="{{loading==false}}"></text>{{loadTitle+''}}</view></block><block wx:if="{{$root.g1==0}}"><view class="data-v-6951518e"><empty-page vue-id="7e4d3d90-1" title="暂无账单的记录哦～" class="data-v-6951518e" bind:__l="__l"></empty-page></view></block></view></view><home vue-id="7e4d3d90-2" class="data-v-6951518e" bind:__l="__l"></home></view>