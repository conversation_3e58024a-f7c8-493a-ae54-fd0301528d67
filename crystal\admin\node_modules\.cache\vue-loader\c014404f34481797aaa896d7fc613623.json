{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\distribution\\index.vue?vue&type=template&id=9a451cbe&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\distribution\\index.vue", "mtime": 1753666157868}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"divBox\" },\n    [\n      _c(\n        \"el-card\",\n        { staticClass: \"box-card\" },\n        [\n          _c(\n            \"div\",\n            {\n              staticClass: \"clearfix\",\n              attrs: { slot: \"header\" },\n              slot: \"header\",\n            },\n            [\n              _c(\n                \"div\",\n                { staticClass: \"container\" },\n                [\n                  _c(\n                    \"el-form\",\n                    { attrs: { size: \"small\", \"label-width\": \"100px\" } },\n                    [\n                      _c(\n                        \"el-form-item\",\n                        {\n                          staticClass: \"width100\",\n                          attrs: { label: \"时间选择：\" },\n                        },\n                        [\n                          _c(\n                            \"el-radio-group\",\n                            {\n                              staticClass: \"mr20\",\n                              attrs: { type: \"button\", size: \"small\" },\n                              on: {\n                                change: function ($event) {\n                                  return _vm.selectChange(\n                                    _vm.tableFrom.dateLimit\n                                  )\n                                },\n                              },\n                              model: {\n                                value: _vm.tableFrom.dateLimit,\n                                callback: function ($$v) {\n                                  _vm.$set(_vm.tableFrom, \"dateLimit\", $$v)\n                                },\n                                expression: \"tableFrom.dateLimit\",\n                              },\n                            },\n                            _vm._l(_vm.fromList.fromTxt, function (item, i) {\n                              return _c(\n                                \"el-radio-button\",\n                                { key: i, attrs: { label: item.val } },\n                                [_vm._v(_vm._s(item.text))]\n                              )\n                            }),\n                            1\n                          ),\n                          _vm._v(\" \"),\n                          _c(\"el-date-picker\", {\n                            staticStyle: { width: \"250px\" },\n                            attrs: {\n                              \"value-format\": \"yyyy-MM-dd\",\n                              format: \"yyyy-MM-dd\",\n                              size: \"small\",\n                              type: \"daterange\",\n                              placement: \"bottom-end\",\n                              placeholder: \"自定义时间\",\n                            },\n                            on: { change: _vm.onchangeTime },\n                            model: {\n                              value: _vm.timeVal,\n                              callback: function ($$v) {\n                                _vm.timeVal = $$v\n                              },\n                              expression: \"timeVal\",\n                            },\n                          }),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"关键字：\" } },\n                        [\n                          _c(\n                            \"el-input\",\n                            {\n                              staticClass: \"selWidth\",\n                              attrs: {\n                                placeholder: \"请输入姓名、电话、UID\",\n                                size: \"small\",\n                                clearable: \"\",\n                              },\n                              model: {\n                                value: _vm.tableFrom.keywords,\n                                callback: function ($$v) {\n                                  _vm.$set(_vm.tableFrom, \"keywords\", $$v)\n                                },\n                                expression: \"tableFrom.keywords\",\n                              },\n                            },\n                            [\n                              _c(\"el-button\", {\n                                attrs: {\n                                  slot: \"append\",\n                                  icon: \"el-icon-search\",\n                                  size: \"small\",\n                                },\n                                on: { click: _vm.seachList },\n                                slot: \"append\",\n                              }),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n            ]\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-table\",\n            {\n              directives: [\n                {\n                  name: \"loading\",\n                  rawName: \"v-loading\",\n                  value: _vm.listLoading,\n                  expression: \"listLoading\",\n                },\n              ],\n              staticClass: \"table\",\n              staticStyle: { width: \"100%\" },\n              attrs: {\n                data: _vm.tableData.data,\n                size: \"mini\",\n                \"highlight-current-row\": \"\",\n              },\n            },\n            [\n              _c(\"el-table-column\", {\n                attrs: { prop: \"uid\", label: \"ID\", width: \"60\" },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { label: \"头像\", \"min-width\": \"80\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\n                          \"div\",\n                          { staticClass: \"demo-image__preview\" },\n                          [\n                            _c(\"el-image\", {\n                              attrs: {\n                                src: scope.row.avatar,\n                                \"preview-src-list\": [scope.row.avatar],\n                              },\n                            }),\n                          ],\n                          1\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"nickname\",\n                  label: \"用户信息\",\n                  \"min-width\": \"130\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  sortable: \"\",\n                  prop: \"spreadCount\",\n                  label: \"推广用户(一级)数量\",\n                  \"sort-method\": function (a, b) {\n                    return a.spreadCount - b.spreadCount\n                  },\n                  \"min-width\": \"150\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  sortable: \"\",\n                  label: \"推广订单数量\",\n                  prop: \"spreadOrderNum\",\n                  \"sort-method\": function (a, b) {\n                    return a.spreadOrderNum - b.spreadOrderNum\n                  },\n                  \"min-width\": \"120\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  sortable: \"\",\n                  label: \"推广订单金额\",\n                  \"min-width\": \"120\",\n                  \"sort-method\": function (a, b) {\n                    return a.spreadOrderTotalPrice - b.spreadOrderTotalPrice\n                  },\n                  prop: \"spreadOrderTotalPrice\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  sortable: \"\",\n                  label: \"佣金总金额\",\n                  \"min-width\": \"120\",\n                  \"sort-method\": function (a, b) {\n                    return a.totalBrokeragePrice - b.totalBrokeragePrice\n                  },\n                  prop: \"totalBrokeragePrice\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  sortable: \"\",\n                  label: \"已提现金额\",\n                  \"min-width\": \"120\",\n                  \"sort-method\": function (a, b) {\n                    return a.extractCountPrice - b.extractCountPrice\n                  },\n                  prop: \"extractCountPrice\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  sortable: \"\",\n                  label: \"已提现次数\",\n                  \"min-width\": \"120\",\n                  \"sort-method\": function (a, b) {\n                    return a.extractCountNum - b.extractCountNum\n                  },\n                  prop: \"extractCountNum\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  sortable: \"\",\n                  label: \"未提现金额\",\n                  \"min-width\": \"120\",\n                  \"sort-method\": function (a, b) {\n                    return a.brokeragePrice - b.brokeragePrice\n                  },\n                  prop: \"brokeragePrice\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  sortable: \"\",\n                  label: \"冻结中佣金\",\n                  \"min-width\": \"120\",\n                  \"sort-method\": function (a, b) {\n                    return a.freezeBrokeragePrice - b.freezeBrokeragePrice\n                  },\n                  prop: \"freezeBrokeragePrice\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"promoterTime\",\n                  label: \"成为推广员时间\",\n                  \"min-width\": \"150\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"spreadNickname\",\n                  label: \"上级推广人\",\n                  \"min-width\": \"150\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: \"操作\",\n                  \"min-width\": \"150\",\n                  fixed: \"right\",\n                  align: \"center\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\n                          \"el-button\",\n                          {\n                            directives: [\n                              {\n                                name: \"hasPermi\",\n                                rawName: \"v-hasPermi\",\n                                value: [\"admin:retail:spread:list\"],\n                                expression: \"['admin:retail:spread:list']\",\n                              },\n                            ],\n                            staticClass: \"mr10\",\n                            attrs: { type: \"text\", size: \"small\" },\n                            on: {\n                              click: function ($event) {\n                                return _vm.onSpread(\n                                  scope.row.uid,\n                                  \"man\",\n                                  \"推广人\"\n                                )\n                              },\n                            },\n                          },\n                          [_vm._v(\"推广人\")]\n                        ),\n                        _vm._v(\" \"),\n                        _c(\n                          \"el-dropdown\",\n                          [\n                            _c(\"span\", { staticClass: \"el-dropdown-link\" }, [\n                              _vm._v(\"\\n              更多\"),\n                              _c(\"i\", {\n                                staticClass:\n                                  \"el-icon-arrow-down el-icon--right\",\n                              }),\n                            ]),\n                            _vm._v(\" \"),\n                            _c(\n                              \"el-dropdown-menu\",\n                              { attrs: { slot: \"dropdown\" }, slot: \"dropdown\" },\n                              [\n                                _vm.checkPermi([\n                                  \"admin:retail:spread:order:list\",\n                                ])\n                                  ? _c(\n                                      \"el-dropdown-item\",\n                                      {\n                                        nativeOn: {\n                                          click: function ($event) {\n                                            return _vm.onSpreadOrder(\n                                              scope.row.uid,\n                                              \"order\",\n                                              \"推广订单\"\n                                            )\n                                          },\n                                        },\n                                      },\n                                      [_vm._v(\"推广订单\")]\n                                    )\n                                  : _vm._e(),\n                                _vm._v(\" \"),\n                                scope.row.spreadNickname &&\n                                scope.row.spreadNickname !== \"无\"\n                                  ? _c(\n                                      \"el-dropdown-item\",\n                                      {\n                                        directives: [\n                                          {\n                                            name: \"hasPermi\",\n                                            rawName: \"v-hasPermi\",\n                                            value: [\n                                              \"admin:retail:spread:clean\",\n                                            ],\n                                            expression:\n                                              \"['admin:retail:spread:clean']\",\n                                          },\n                                        ],\n                                        nativeOn: {\n                                          click: function ($event) {\n                                            return _vm.clearSpread(scope.row)\n                                          },\n                                        },\n                                      },\n                                      [_vm._v(\"清除上级推广人\")]\n                                    )\n                                  : _vm._e(),\n                              ],\n                              1\n                            ),\n                          ],\n                          1\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"div\",\n            { staticClass: \"block\" },\n            [\n              _c(\"el-pagination\", {\n                attrs: {\n                  \"page-sizes\": [20, 40, 60, 80],\n                  \"page-size\": _vm.tableFrom.limit,\n                  \"current-page\": _vm.tableFrom.page,\n                  layout: \"total, sizes, prev, pager, next, jumper\",\n                  total: _vm.tableData.total,\n                },\n                on: {\n                  \"size-change\": _vm.handleSizeChange,\n                  \"current-change\": _vm.pageChange,\n                },\n              }),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"el-dialog\",\n        {\n          attrs: {\n            title: _vm.titleName + \"列表\",\n            visible: _vm.dialogVisible,\n            width: \"900px\",\n            \"before-close\": _vm.handleClose,\n          },\n          on: {\n            \"update:visible\": function ($event) {\n              _vm.dialogVisible = $event\n            },\n          },\n        },\n        [\n          _c(\n            \"div\",\n            { staticClass: \"container\" },\n            [\n              _c(\n                \"el-form\",\n                { attrs: { size: \"small\", \"label-width\": \"100px\" } },\n                [\n                  this.onName !== \"man\"\n                    ? _c(\n                        \"el-form-item\",\n                        {\n                          key: \"1\",\n                          staticClass: \"width100\",\n                          attrs: { label: \"时间选择：\" },\n                        },\n                        [\n                          _c(\n                            \"el-radio-group\",\n                            {\n                              staticClass: \"mr20\",\n                              attrs: { type: \"button\", size: \"small\" },\n                              on: {\n                                change: function ($event) {\n                                  return _vm.selectChangeSpread(\n                                    _vm.spreadFrom.dateLimit\n                                  )\n                                },\n                              },\n                              model: {\n                                value: _vm.spreadFrom.dateLimit,\n                                callback: function ($$v) {\n                                  _vm.$set(_vm.spreadFrom, \"dateLimit\", $$v)\n                                },\n                                expression: \"spreadFrom.dateLimit\",\n                              },\n                            },\n                            _vm._l(_vm.fromList.fromTxt, function (item, i) {\n                              return _c(\n                                \"el-radio-button\",\n                                { key: i, attrs: { label: item.val } },\n                                [_vm._v(_vm._s(item.text))]\n                              )\n                            }),\n                            1\n                          ),\n                          _vm._v(\" \"),\n                          _c(\"el-date-picker\", {\n                            staticStyle: { width: \"250px\" },\n                            attrs: {\n                              \"value-format\": \"yyyy-MM-dd\",\n                              format: \"yyyy-MM-dd\",\n                              size: \"small\",\n                              type: \"daterange\",\n                              placement: \"bottom-end\",\n                              placeholder: \"自定义时间\",\n                            },\n                            on: { change: _vm.onchangeTimeSpread },\n                            model: {\n                              value: _vm.timeValSpread,\n                              callback: function ($$v) {\n                                _vm.timeValSpread = $$v\n                              },\n                              expression: \"timeValSpread\",\n                            },\n                          }),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-form-item\",\n                    { attrs: { label: \"用户类型：\" } },\n                    [\n                      _c(\n                        \"el-radio-group\",\n                        {\n                          attrs: { size: \"small\" },\n                          on: { change: _vm.onChanges },\n                          model: {\n                            value: _vm.spreadFrom.type,\n                            callback: function ($$v) {\n                              _vm.$set(_vm.spreadFrom, \"type\", $$v)\n                            },\n                            expression: \"spreadFrom.type\",\n                          },\n                        },\n                        [\n                          _c(\"el-radio-button\", { attrs: { label: \"0\" } }, [\n                            _vm._v(\"全部\"),\n                          ]),\n                          _vm._v(\" \"),\n                          _c(\"el-radio-button\", { attrs: { label: \"1\" } }, [\n                            _vm._v(\"一级推广人\"),\n                          ]),\n                          _vm._v(\" \"),\n                          _c(\"el-radio-button\", { attrs: { label: \"2\" } }, [\n                            _vm._v(\"二级推广人\"),\n                          ]),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-form-item\",\n                    { staticClass: \"width100\", attrs: { label: \"关键字：\" } },\n                    [\n                      _c(\n                        \"el-input\",\n                        {\n                          staticClass: \"selWidth\",\n                          attrs: {\n                            placeholder:\n                              _vm.onName === \"order\"\n                                ? \"请输入订单号\"\n                                : \"请输入姓名、电话、UID\",\n                            size: \"small\",\n                            clearable: \"\",\n                          },\n                          model: {\n                            value: _vm.spreadFrom.nickName,\n                            callback: function ($$v) {\n                              _vm.$set(_vm.spreadFrom, \"nickName\", $$v)\n                            },\n                            expression: \"spreadFrom.nickName\",\n                          },\n                        },\n                        [\n                          _c(\"el-button\", {\n                            attrs: {\n                              slot: \"append\",\n                              icon: \"el-icon-search\",\n                              size: \"small\",\n                            },\n                            on: { click: _vm.onChanges },\n                            slot: \"append\",\n                          }),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _vm.onName === \"man\"\n            ? _c(\n                \"el-table\",\n                {\n                  directives: [\n                    {\n                      name: \"loading\",\n                      rawName: \"v-loading\",\n                      value: _vm.spreadLoading,\n                      expression: \"spreadLoading\",\n                    },\n                  ],\n                  key: \"men\",\n                  staticClass: \"table\",\n                  staticStyle: { width: \"100%\" },\n                  attrs: {\n                    data: _vm.spreadData.data,\n                    size: \"mini\",\n                    \"highlight-current-row\": \"\",\n                  },\n                },\n                [\n                  _c(\"el-table-column\", {\n                    attrs: { prop: \"uid\", label: \"ID\", width: \"60\" },\n                  }),\n                  _vm._v(\" \"),\n                  _c(\"el-table-column\", {\n                    attrs: { label: \"头像\", \"min-width\": \"80\" },\n                    scopedSlots: _vm._u(\n                      [\n                        {\n                          key: \"default\",\n                          fn: function (scope) {\n                            return [\n                              _c(\n                                \"div\",\n                                { staticClass: \"demo-image__preview\" },\n                                [\n                                  _c(\"el-image\", {\n                                    attrs: {\n                                      src: scope.row.avatar,\n                                      \"preview-src-list\": [scope.row.avatar],\n                                    },\n                                  }),\n                                ],\n                                1\n                              ),\n                            ]\n                          },\n                        },\n                      ],\n                      null,\n                      false,\n                      4159822182\n                    ),\n                  }),\n                  _vm._v(\" \"),\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      prop: \"nickname\",\n                      label: \"用户信息\",\n                      \"min-width\": \"130\",\n                    },\n                  }),\n                  _vm._v(\" \"),\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      prop: \"is_promoter\",\n                      label: \"是否推广员\",\n                      \"min-width\": \"120\",\n                    },\n                    scopedSlots: _vm._u(\n                      [\n                        {\n                          key: \"default\",\n                          fn: function (scope) {\n                            return [\n                              _c(\"span\", [\n                                _vm._v(\n                                  _vm._s(\n                                    _vm._f(\"filterYesOrNo\")(\n                                      scope.row.isPromoter\n                                    )\n                                  )\n                                ),\n                              ]),\n                            ]\n                          },\n                        },\n                      ],\n                      null,\n                      false,\n                      62589210\n                    ),\n                  }),\n                  _vm._v(\" \"),\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      sortable: \"\",\n                      label: \"推广人数\",\n                      \"min-width\": \"120\",\n                      prop: \"spreadCount\",\n                    },\n                  }),\n                  _vm._v(\" \"),\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      sortable: \"\",\n                      label: \"订单数\",\n                      \"min-width\": \"120\",\n                      prop: \"payCount\",\n                    },\n                  }),\n                ],\n                1\n              )\n            : _vm._e(),\n          _vm._v(\" \"),\n          _vm.onName === \"order\"\n            ? _c(\n                \"el-table\",\n                {\n                  directives: [\n                    {\n                      name: \"loading\",\n                      rawName: \"v-loading\",\n                      value: _vm.spreadLoading,\n                      expression: \"spreadLoading\",\n                    },\n                  ],\n                  key: \"order\",\n                  staticClass: \"table\",\n                  staticStyle: { width: \"100%\" },\n                  attrs: {\n                    data: _vm.spreadData.data,\n                    size: \"mini\",\n                    \"highlight-current-row\": \"\",\n                  },\n                },\n                [\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      prop: \"orderId\",\n                      label: \"订单ID\",\n                      \"min-width\": \"120\",\n                    },\n                  }),\n                  _vm._v(\" \"),\n                  _c(\"el-table-column\", {\n                    attrs: { label: \"用户信息\", \"min-width\": \"150\" },\n                    scopedSlots: _vm._u(\n                      [\n                        {\n                          key: \"default\",\n                          fn: function (scope) {\n                            return [\n                              _c(\n                                \"span\",\n                                [\n                                  _vm._v(_vm._s(scope.row.realName)),\n                                  _c(\"el-divider\", {\n                                    attrs: { direction: \"vertical\" },\n                                  }),\n                                  _vm._v(_vm._s(scope.row.userPhone)),\n                                ],\n                                1\n                              ),\n                            ]\n                          },\n                        },\n                      ],\n                      null,\n                      false,\n                      1595025101\n                    ),\n                  }),\n                  _vm._v(\" \"),\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      prop: \"updateTime\",\n                      label: \"时间\",\n                      \"min-width\": \"150\",\n                    },\n                  }),\n                  _vm._v(\" \"),\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      sortable: \"\",\n                      label: \"返佣金额\",\n                      \"min-width\": \"120\",\n                      prop: \"price\",\n                    },\n                  }),\n                ],\n                1\n              )\n            : _vm._e(),\n          _vm._v(\" \"),\n          _c(\n            \"div\",\n            { staticClass: \"block\" },\n            [\n              _c(\"el-pagination\", {\n                attrs: {\n                  \"page-sizes\": [10, 20, 30, 40],\n                  \"page-size\": _vm.spreadFrom.limit,\n                  \"current-page\": _vm.spreadFrom.page,\n                  layout: \"total, sizes, prev, pager, next, jumper\",\n                  total: _vm.spreadData.total,\n                },\n                on: {\n                  \"size-change\": _vm.handleSizeChangeSpread,\n                  \"current-change\": _vm.pageChangeSpread,\n                },\n              }),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}