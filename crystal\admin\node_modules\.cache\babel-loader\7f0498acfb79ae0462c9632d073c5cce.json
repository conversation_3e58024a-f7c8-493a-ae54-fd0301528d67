{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\FormGenerator\\index\\FormDrawer.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\FormGenerator\\index\\FormDrawer.vue", "mtime": 1753666157769}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _parser = require(\"@babel/parser\");\nvar _clipboard = _interopRequireDefault(require(\"clipboard\"));\nvar _fileSaver = require(\"file-saver\");\nvar _html = require(\"@/components/FormGenerator/components/generator/html\");\nvar _js = require(\"@/components/FormGenerator/components/generator/js\");\nvar _css = require(\"@/components/FormGenerator/components/generator/css\");\nvar _index = require(\"../utils/index\");\nvar _ResourceDialog = _interopRequireDefault(require(\"./ResourceDialog\"));\nvar _loadMonaco = _interopRequireDefault(require(\"../utils/loadMonaco\"));\nvar _loadBeautifier = _interopRequireDefault(require(\"../utils/loadBeautifier\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nvar editorObj = {\n  html: null,\n  js: null,\n  css: null\n};\nvar mode = {\n  html: 'html',\n  js: 'javascript',\n  css: 'css'\n};\nvar beautifier;\nvar monaco;\nvar _default = exports.default = {\n  components: {\n    ResourceDialog: _ResourceDialog.default\n  },\n  props: ['formData', 'generateConf'],\n  data: function data() {\n    return {\n      activeTab: 'html',\n      htmlCode: '',\n      jsCode: '',\n      cssCode: '',\n      codeFrame: '',\n      isIframeLoaded: false,\n      isInitcode: false,\n      // 保证open后两个异步只执行一次runcode\n      isRefreshCode: false,\n      // 每次打开都需要重新刷新代码\n      resourceVisible: false,\n      scripts: [],\n      links: [],\n      monaco: null\n    };\n  },\n  computed: {\n    resources: function resources() {\n      return this.scripts.concat(this.links);\n    }\n  },\n  watch: {},\n  created: function created() {},\n  mounted: function mounted() {\n    var _this = this;\n    window.addEventListener('keydown', this.preventDefaultSave);\n    var clipboard = new _clipboard.default('.copy-btn', {\n      text: function text(trigger) {\n        var codeStr = _this.generateCode();\n        _this.$notify({\n          title: '成功',\n          message: '代码已复制到剪切板，可粘贴。',\n          type: 'success'\n        });\n        return codeStr;\n      }\n    });\n    clipboard.on('error', function (e) {\n      _this.$message.error('代码复制失败');\n    });\n  },\n  beforeDestroy: function beforeDestroy() {\n    window.removeEventListener('keydown', this.preventDefaultSave);\n  },\n  methods: {\n    preventDefaultSave: function preventDefaultSave(e) {\n      if (e.key === 's' && (e.metaKey || e.ctrlKey)) {\n        e.preventDefault();\n      }\n    },\n    onOpen: function onOpen() {\n      var _this2 = this;\n      var type = this.generateConf.type;\n      this.htmlCode = (0, _html.makeUpHtml)(this.formData, type);\n      this.jsCode = (0, _js.makeUpJs)(this.formData, type);\n      this.cssCode = (0, _css.makeUpCss)(this.formData);\n      (0, _loadBeautifier.default)(function (btf) {\n        beautifier = btf;\n        _this2.htmlCode = beautifier.html(_this2.htmlCode, _index.beautifierConf.html);\n        _this2.jsCode = beautifier.js(_this2.jsCode, _index.beautifierConf.js);\n        _this2.cssCode = beautifier.css(_this2.cssCode, _index.beautifierConf.html);\n        (0, _loadMonaco.default)(function (val) {\n          monaco = val;\n          _this2.setEditorValue('editorHtml', 'html', _this2.htmlCode);\n          _this2.setEditorValue('editorJs', 'js', _this2.jsCode);\n          _this2.setEditorValue('editorCss', 'css', _this2.cssCode);\n          if (!_this2.isInitcode) {\n            _this2.isRefreshCode = true;\n            _this2.isIframeLoaded && (_this2.isInitcode = true) && _this2.runCode();\n          }\n        });\n      });\n    },\n    onClose: function onClose() {\n      this.isInitcode = false;\n      this.isRefreshCode = false;\n      this.isIframeLoaded = false;\n    },\n    iframeLoad: function iframeLoad() {\n      if (!this.isInitcode) {\n        this.isIframeLoaded = true;\n        this.isRefreshCode && (this.isInitcode = true) && this.runCode();\n      }\n    },\n    setEditorValue: function setEditorValue(id, type, codeStr) {\n      var _this3 = this;\n      if (editorObj[type]) {\n        editorObj[type].setValue(codeStr);\n      } else {\n        editorObj[type] = monaco.editor.create(document.getElementById(id), {\n          value: codeStr,\n          theme: 'vs-dark',\n          language: mode[type],\n          automaticLayout: true\n        });\n      }\n      // ctrl + s 刷新\n      editorObj[type].onKeyDown(function (e) {\n        if (e.keyCode === 49 && (e.metaKey || e.ctrlKey)) {\n          _this3.runCode();\n        }\n      });\n    },\n    runCode: function runCode() {\n      var jsCodeStr = editorObj.js.getValue();\n      try {\n        var ast = (0, _parser.parse)(jsCodeStr, {\n          sourceType: 'module'\n        });\n        var astBody = ast.program.body;\n        if (astBody.length > 1) {\n          this.$confirm('js格式不能识别，仅支持修改export default的对象内容', '提示', {\n            type: 'warning'\n          });\n          return;\n        }\n        if (astBody[0].type === 'ExportDefaultDeclaration') {\n          var postData = {\n            type: 'refreshFrame',\n            data: {\n              generateConf: this.generateConf,\n              html: editorObj.html.getValue(),\n              js: jsCodeStr.replace(_index.exportDefault, ''),\n              css: editorObj.css.getValue(),\n              scripts: this.scripts,\n              links: this.links\n            }\n          };\n          this.$refs.previewPage.contentWindow.postMessage(postData, location.origin);\n        } else {\n          this.$message.error('请使用export default');\n        }\n      } catch (err) {\n        this.$message.error(\"js\\u9519\\u8BEF\\uFF1A\".concat(err));\n      }\n    },\n    generateCode: function generateCode() {\n      var html = (0, _html.vueTemplate)(editorObj.html.getValue());\n      var script = (0, _html.vueScript)(editorObj.js.getValue());\n      var css = (0, _html.cssStyle)(editorObj.css.getValue());\n      return beautifier.html(html + script + css, _index.beautifierConf.html);\n    },\n    exportFile: function exportFile() {\n      var _this4 = this;\n      this.$prompt('文件名:', '导出文件', {\n        inputValue: \"\".concat(+new Date(), \".vue\"),\n        closeOnClickModal: false,\n        inputPlaceholder: '请输入文件名'\n      }).then(function (_ref) {\n        var value = _ref.value;\n        if (!value) value = \"\".concat(+new Date(), \".vue\");\n        var codeStr = _this4.generateCode();\n        var blob = new Blob([codeStr], {\n          type: 'text/plain;charset=utf-8'\n        });\n        (0, _fileSaver.saveAs)(blob, value);\n      });\n    },\n    showResource: function showResource() {\n      this.resourceVisible = true;\n    },\n    setResource: function setResource(arr) {\n      var scripts = [];\n      var links = [];\n      if (Array.isArray(arr)) {\n        arr.forEach(function (item) {\n          if (item.endsWith('.css')) {\n            links.push(item);\n          } else {\n            scripts.push(item);\n          }\n        });\n        this.scripts = scripts;\n        this.links = links;\n      } else {\n        this.scripts = [];\n        this.links = [];\n      }\n    }\n  }\n};", null]}