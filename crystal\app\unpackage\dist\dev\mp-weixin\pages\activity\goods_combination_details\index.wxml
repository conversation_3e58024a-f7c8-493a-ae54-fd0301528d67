<view class="data-v-26bb9225"><view class="navbar data-v-26bb9225" style="{{'height:'+(navH+'rpx')+';'+('opacity:'+(opacity)+';')}}"><view class="navbarH data-v-26bb9225" style="{{('height:'+navH+'rpx;')}}"><view class="navbarCon acea-row row-center-wrapper data-v-26bb9225"><view class="header acea-row row-center-wrapper data-v-26bb9225"><block wx:for="{{navList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['tap',['$0',index],[[['navList','',index]]]]]]]}}" class="{{['item','data-v-26bb9225',navActive===index?'on':'']}}" bindtap="__e">{{''+item+''}}</view></block></view></view></view></view><view class="product-con data-v-26bb9225"><scroll-view style="{{('height:'+height+'px;')}}" scroll-top="{{scrollTop}}" scroll-y="true" scroll-with-animation="true" data-event-opts="{{[['scroll',[['scroll',['$event']]]]]}}" bindscroll="__e" class="data-v-26bb9225"><view id="past0" class="data-v-26bb9225"><product-con-swiper class="mb30 data-v-26bb9225" vue-id="9e9198fe-1" imgUrls="{{imgUrls}}" bind:__l="__l"></product-con-swiper><view class="pad30 data-v-26bb9225"><view class="wrapper mb30 data-v-26bb9225"><view class="share acea-row row-between row-bottom data-v-26bb9225"><view class="money font-color data-v-26bb9225">￥<text class="num data-v-26bb9225">{{storeInfo.price||0}}</text><text class="y-money data-v-26bb9225">{{"￥"+(storeInfo.otPrice||0)}}</text></view><view data-event-opts="{{[['tap',[['listenerActionSheet',['$event']]]]]}}" class="iconfont icon-fenxiang data-v-26bb9225" bindtap="__e"></view></view><view class="introduce line2 data-v-26bb9225">{{storeInfo.storeName}}</view><view class="label acea-row row-between-wrapper data-v-26bb9225"><view class="stock data-v-26bb9225">{{"类型："+(storeInfo.people||0)+"人团"}}</view><view class="data-v-26bb9225">{{"累计销量："+($root.m0+$root.m1)+" "+(storeInfo.unitName||'')}}</view><view class="data-v-26bb9225">{{"限购: "+(storeInfo.quotaShow?storeInfo.quotaShow:0)+"\n\t\t\t\t\t\t\t\t"+(storeInfo.unitName||'')+''}}</view></view></view><block wx:if="{{$root.g0}}"><view data-event-opts="{{[['tap',[['selecAttr',['$event']]]]]}}" class="attribute acea-row row-between-wrapper mb30 borRadius14 data-v-26bb9225" bindtap="__e"><view class="line1 data-v-26bb9225">{{attr+"："}}<text class="atterTxt data-v-26bb9225">{{attrValue}}</text></view><view class="iconfont icon-jiantou data-v-26bb9225"></view></view></block><block wx:if="{{$root.m2>0}}"><view class="notice acea-row row-middle mb30 borRadius14 data-v-26bb9225"><view class="num font-color data-v-26bb9225"><text class="iconfont icon-laba data-v-26bb9225"></text>{{'已拼'+pinkOkSum+"件"}}<text class="line data-v-26bb9225">|</text></view><view class="swiper data-v-26bb9225"><swiper indicator-dots="{{indicatorDots}}" autoplay="{{autoplay}}" interval="2500" duration="500" vertical="true" circular="true" class="data-v-26bb9225"><block wx:for="{{itemNew}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block class="data-v-26bb9225"><swiper-item class="data-v-26bb9225"><view class="line1 data-v-26bb9225">{{item.nickname+"拼团成功"}}</view></swiper-item></block></block></swiper></view></view></block><block wx:if="{{attribute.productSelect.quota>0}}"><view class="assemble mb30 borRadius14 data-v-26bb9225"><block wx:for="{{pink}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{index<AllIndex}}"><view class="item acea-row row-between-wrapper data-v-26bb9225"><view class="pictxt acea-row row-between-wrapper data-v-26bb9225"><view class="pictrue data-v-26bb9225"><image src="{{item.avatar}}" class="data-v-26bb9225"></image></view><view class="text line1 data-v-26bb9225">{{item.nickname}}</view></view><view class="right acea-row row-middle data-v-26bb9225"><view class="data-v-26bb9225"><view class="lack data-v-26bb9225">还差<text class="font-color data-v-26bb9225">{{item.count}}</text>人成团</view><view class="time acea-row data-v-26bb9225"><count-down vue-id="{{'9e9198fe-2-'+index}}" is-day="{{false}}" tip-text="剩余 " day-text=" " hour-text=":" minute-text=":" second-text=" " datatime="{{item.stopTime/1000}}" bgColor="{{bgColor}}" class="data-v-26bb9225" bind:__l="__l"></count-down></view></view><navigator class="spellBnt data-v-26bb9225" hover-class="none" url="{{'/pages/activity/goods_combination_status/index?id='+item.id}}">去拼单<text class="iconfont icon-jiantou data-v-26bb9225"></text></navigator></view></view></block></block><block wx:if="{{$root.g1}}"><block wx:if="{{$root.g2>AllIndex}}"><view data-event-opts="{{[['tap',[['showAll',['$event']]]]]}}" class="more data-v-26bb9225" bindtap="__e">查看更多<text class="iconfont icon-xiangxia data-v-26bb9225"></text></view></block><block wx:else><block wx:if="{{$root.g3}}"><view data-event-opts="{{[['tap',[['hideAll',['$event']]]]]}}" class="more data-v-26bb9225" bindtap="__e">收起<text class="iconfont icon-xiangshang data-v-26bb9225"></text></view></block></block></block></view></block><view class="playWay mb30 borRadius14 data-v-26bb9225"><view class="title acea-row row-between row-middle data-v-26bb9225"><view class="data-v-26bb9225">拼团玩法</view></view><view class="way acea-row row-middle data-v-26bb9225"><view class="item acea-row row-middle data-v-26bb9225"><text class="num data-v-26bb9225">①</text>开团/参团</view><view class="iconfont icon-arrow data-v-26bb9225"></view><view class="item data-v-26bb9225"><text class="num data-v-26bb9225">②</text>邀请好友</view><view class="iconfont icon-arrow data-v-26bb9225"></view><view class="item data-v-26bb9225"><view class="data-v-26bb9225"><text class="num data-v-26bb9225">③</text>满员发货</view></view></view></view><view class="userEvaluation borRadius14 data-v-26bb9225" id="past1"><view class="title acea-row row-between-wrapper data-v-26bb9225" style="{{(replyCount==0?'border-bottom-left-radius:14rpx;border-bottom-right-radius:14rpx;':'')}}"><view class="data-v-26bb9225">用户评价<view class="_i data-v-26bb9225">{{"("+replyCount+")"}}</view></view><navigator class="praise data-v-26bb9225" hover-class="none" url="{{'/pages/users/goods_comment_list/index?productId='+storeInfo.productId}}"><view class="_i data-v-26bb9225">好评</view><text class="font-color data-v-26bb9225">{{(replyChance||0)+"%"}}</text><text class="iconfont icon-jiantou data-v-26bb9225"></text></navigator></view><block wx:if="{{$root.g4>0}}"><user-evaluation vue-id="9e9198fe-3" reply="{{reply}}" class="data-v-26bb9225" bind:__l="__l"></user-evaluation></block></view></view></view><view class="product-intro data-v-26bb9225" id="past2"><view class="title data-v-26bb9225"><image src="../../../static/images/xzuo.png" class="data-v-26bb9225"></image><label class="sp _span data-v-26bb9225">产品详情</label><image src="../../../static/images/xyou.png" class="data-v-26bb9225"></image></view><view class="conter data-v-26bb9225"><jyf-parser vue-id="9e9198fe-4" html="{{storeInfo.content}}" tag-style="{{tagStyle}}" data-ref="article" class="data-v-26bb9225 vue-ref" bind:__l="__l"></jyf-parser></view></view><view style="height:120rpx;" class="data-v-26bb9225"></view></scroll-view><view class="footer acea-row row-between-wrapper data-v-26bb9225"><button class="item data-v-26bb9225" open-type="contact" hover-class="none"><view class="iconfont icon-kefu data-v-26bb9225"></view><view class="data-v-26bb9225">客服</view></button><view data-event-opts="{{[['tap',[['setCollect',['$event']]]]]}}" class="item data-v-26bb9225" bindtap="__e"><block wx:if="{{userCollect}}"><view class="iconfont icon-shoucang1 data-v-26bb9225"></view></block><block wx:else><view class="iconfont icon-shoucang data-v-26bb9225"></view></block><view class="data-v-26bb9225">收藏</view></view><view class="bnt acea-row data-v-26bb9225"><view data-event-opts="{{[['tap',[['goProduct',['$event']]]]]}}" class="joinCart bnts data-v-26bb9225" bindtap="__e">单独购买</view><block wx:if="{{attribute.productSelect.quota>0}}"><view data-event-opts="{{[['tap',[['goCat',['$event']]]]]}}" class="buy bnts data-v-26bb9225" bindtap="__e">立即开团</view></block><block wx:if="{{!dataShow}}"><view class="buy bnts bg-color-hui data-v-26bb9225">立即开团</view></block><block wx:if="{{attribute.productSelect.quota<=0}}"><view class="buy bnts bg-color-hui data-v-26bb9225">已售罄</view></block></view></view></view><share-red-packets vue-id="9e9198fe-5" sharePacket="{{sharePacket}}" data-event-opts="{{[['^listenerActionSheet',[['listenerActionSheet']]],['^closeChange',[['closeChange']]]]}}" bind:listenerActionSheet="__e" bind:closeChange="__e" class="data-v-26bb9225" bind:__l="__l"></share-red-packets><view class="{{['generate-posters','acea-row','row-middle','data-v-26bb9225',posters?'on':'']}}"><button class="item data-v-26bb9225" open-type="share" hover-class="none" data-event-opts="{{[['tap',[['goFriend',['$event']]]]]}}" bindtap="__e"><view class="iconfont icon-weixin3 data-v-26bb9225"></view><view class="data-v-26bb9225">发送给朋友</view></button><button class="item data-v-26bb9225" hover-class="none" data-event-opts="{{[['tap',[['goPoster',['$event']]]]]}}" bindtap="__e"><view class="iconfont icon-haibao data-v-26bb9225"></view><view class="data-v-26bb9225">生成海报</view></button></view><block wx:if="{{posters}}"><view data-event-opts="{{[['tap',[['closePosters',['$event']]]]]}}" class="mask data-v-26bb9225" bindtap="__e"></view></block><block wx:if="{{canvasStatus}}"><view data-event-opts="{{[['tap',[['listenerActionClose',['$event']]]]]}}" class="mask data-v-26bb9225" bindtap="__e"></view></block><block wx:if="{{canvasStatus}}"><view class="poster-pop data-v-26bb9225"><image class="close data-v-26bb9225" src="/static/images/poster-close.png" data-event-opts="{{[['tap',[['posterImageClose',['$event']]]]]}}" bindtap="__e"></image><image src="{{posterImage}}" class="data-v-26bb9225"></image><view data-event-opts="{{[['tap',[['savePosterPath',['$event']]]]]}}" class="save-poster data-v-26bb9225" bindtap="__e">保存到手机</view></view></block><block wx:else><view class="canvas data-v-26bb9225"><canvas style="width:750px;height:1190px;" canvas-id="firstCanvas" class="data-v-26bb9225"></canvas><canvas style="{{'width:'+(qrcodeSize+'px')+';'+('height:'+(qrcodeSize+'px')+';')}}" canvas-id="qrcode" class="data-v-26bb9225"></canvas></view></block><block wx:if="{{H5ShareBox}}"><view class="share-box data-v-26bb9225"><image src="/static/images/share-info.png" data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" bindtap="__e" class="data-v-26bb9225"></image></view></block><home vue-id="9e9198fe-6" class="data-v-26bb9225" bind:__l="__l"></home><product-window vue-id="9e9198fe-7" attr="{{attribute}}" limitNum="{{1}}" data-event-opts="{{[['^myevent',[['onMyEvent']]],['^ChangeAttr',[['ChangeAttr']]],['^ChangeCartNum',[['ChangeCartNum']]],['^iptCartNum',[['iptCartNum']]],['^attrVal',[['attrVal']]]]}}" bind:myevent="__e" bind:ChangeAttr="__e" bind:ChangeCartNum="__e" bind:iptCartNum="__e" bind:attrVal="__e" class="data-v-26bb9225" bind:__l="__l"></product-window></view>