@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.invalidClas.data-v-a5aa7f30 {
  position: relative;
  z-index: 111;
  top: -120rpx;
}
.invalidClasNO.data-v-a5aa7f30 {
  position: static;
  margin-top: 15px;
}
.shoppingCart .labelNav.data-v-a5aa7f30 {
  position: -webkit-sticky;
  position: sticky;
  height: 178rpx;
  padding: 30rpx 30rpx 0 30rpx;
  font-size: 22rpx;
  color: #fff;
  position: fixed;
  left: 0;
  width: 100%;
  box-sizing: border-box;
  background-color: #c9ab79;
  z-index: 5;
  top: 0;
  height: calc(178rpx + 44px + 45rpx);
  padding-top: calc(44px + 50rpx);
}
.shoppingCart .labelNav .item .iconfont.data-v-a5aa7f30 {
  font-size: 25rpx;
  margin-right: 10rpx;
}
.shoppingCart .nav.data-v-a5aa7f30 {
  width: 92%;
  height: 90rpx;
  background-color: #fff;
  padding: 0 24rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  color: #282828;
  position: fixed;
  left: 30rpx;
  z-index: 6;
  top: 94rpx;
  border-top-left-radius: 14rpx;
  border-top-right-radius: 14rpx;
}
.shoppingCart .nav .num.data-v-a5aa7f30 {
  margin-left: 12rpx;
}
.shoppingCart .nav .administrate.data-v-a5aa7f30 {
  font-size: 28rpx;
  color: #333333;
}
.shoppingCart .noCart.data-v-a5aa7f30 {
  margin-top: 171rpx;
  background-color: #fff;
  padding-top: 0.1rpx;
}
.shoppingCart .noCart .pictrue.data-v-a5aa7f30 {
  width: 414rpx;
  height: 336rpx;
  margin: 78rpx auto 56rpx auto;
}
.shoppingCart .noCart .pictrue image.data-v-a5aa7f30 {
  width: 100%;
  height: 100%;
}
.shoppingCart .list.data-v-a5aa7f30 {
  width: 100%;
  margin-top: 178rpx;
  overflow: hidden;
  border-bottom-left-radius: 14rpx;
  border-bottom-right-radius: 14rpx;
}
.shoppingCart .list .item.data-v-a5aa7f30 {
  padding: 24rpx;
  background-color: #fff;
}
.shoppingCart .list .item .picTxt.data-v-a5aa7f30 {
  width: 582rpx;
  position: relative;
}
.shoppingCart .list .item .picTxt .pictrue.data-v-a5aa7f30 {
  width: 160rpx;
  height: 160rpx;
}
.shoppingCart .list .item .picTxt .pictrue image.data-v-a5aa7f30 {
  width: 100%;
  height: 100%;
  border-radius: 6rpx;
}
.shoppingCart .list .item .picTxt .text.data-v-a5aa7f30 {
  width: 396rpx;
  font-size: 28rpx;
  color: #282828;
}
.shoppingCart .list .item .picTxt .text .reColor.data-v-a5aa7f30 {
  color: #999;
}
.shoppingCart .list .item .picTxt .text .reElection.data-v-a5aa7f30 {
  margin-top: 20rpx;
}
.shoppingCart .list .item .picTxt .text .reElection .title.data-v-a5aa7f30 {
  font-size: 24rpx;
}
.shoppingCart .list .item .picTxt .text .reElection .reBnt.data-v-a5aa7f30 {
  width: 120rpx;
  height: 46rpx;
  border-radius: 23rpx;
  font-size: 26rpx;
}
.shoppingCart .list .item .picTxt .text .infor.data-v-a5aa7f30 {
  font-size: 24rpx;
  color: #999999;
  margin-top: 16rpx;
}
.shoppingCart .list .item .picTxt .text .money.data-v-a5aa7f30 {
  font-size: 32rpx;
  color: #c9ab79;
  margin-top: 28rpx;
  font-weight: 600;
}
.shoppingCart .list .item .picTxt .carnum.data-v-a5aa7f30 {
  height: 47rpx;
  position: absolute;
  bottom: 7rpx;
  right: 0;
}
.shoppingCart .list .item .picTxt .carnum view.data-v-a5aa7f30 {
  border: 1rpx solid #a4a4a4;
  width: 66rpx;
  text-align: center;
  height: 100%;
  line-height: 44rpx;
  font-size: 28rpx;
  color: #a4a4a4;
}
.shoppingCart .list .item .picTxt .carnum .reduce.data-v-a5aa7f30 {
  border-right: 0;
  border-radius: 3rpx 0 0 3rpx;
  border-radius: 22rpx 0rpx 0rpx 22rpx;
  font-size: 34rpx;
  line-height: 40rpx;
}
.shoppingCart .list .item .picTxt .carnum .reduce.on.data-v-a5aa7f30 {
  border-color: #e3e3e3;
  color: #dedede;
}
.shoppingCart .list .item .picTxt .carnum .plus.data-v-a5aa7f30 {
  border-left: 0;
  border-radius: 0 3rpx 3rpx 0;
  border-radius: 0rpx 22rpx 22rpx 0rpx;
  font-size: 34rpx;
  line-height: 40rpx;
}
.shoppingCart .list .item .picTxt .carnum .num.data-v-a5aa7f30 {
  color: #282828;
}
.shoppingCart .invalidGoods.data-v-a5aa7f30 {
  background-color: #fff;
  margin-top: 30rpx;
  margin-top: 140rpx;
}
.shoppingCart .invalidGoods .goodsNav.data-v-a5aa7f30 {
  width: 100%;
  height: 90rpx;
  padding: 0 24rpx;
  box-sizing: border-box;
  font-size: 28rpx;
  color: #333333;
}
.shoppingCart .invalidGoods .goodsNav .iconfont.data-v-a5aa7f30 {
  color: #424242;
  font-size: 28rpx;
  margin-right: 17rpx;
}
.shoppingCart .invalidGoods .goodsNav .del.data-v-a5aa7f30 {
  font-size: 26rpx;
  color: #333;
}
.shoppingCart .invalidGoods .goodsNav .del .icon-shanchu1.data-v-a5aa7f30 {
  color: #333;
  font-size: 33rpx;
  vertical-align: -2rpx;
  margin-right: 8rpx;
}
.shoppingCart .invalidGoods .goodsList .item.data-v-a5aa7f30 {
  padding: 24rpx;
}
.shoppingCart .invalidGoods .goodsList .picTxt.data-v-a5aa7f30 {
  width: 576rpx;
}
.shoppingCart .invalidGoods .goodsList .item .invalid.data-v-a5aa7f30 {
  font-size: 22rpx;
  color: #CCCCCC;
  height: 36rpx;
  border-radius: 3rpx;
  text-align: center;
  line-height: 36rpx;
}
.shoppingCart .invalidGoods .goodsList .item .pictrue.data-v-a5aa7f30 {
  width: 160rpx;
  height: 160rpx;
}
.shoppingCart .invalidGoods .goodsList .item .pictrue image.data-v-a5aa7f30 {
  width: 100%;
  height: 100%;
  border-radius: 6rpx;
}
.shoppingCart .invalidGoods .goodsList .item .text.data-v-a5aa7f30 {
  width: 396rpx;
  font-size: 28rpx;
  color: #999;
  height: 140rpx;
}
.shoppingCart .invalidGoods .goodsList .item .text .name.data-v-a5aa7f30 {
  width: 100%;
}
.shoppingCart .invalidGoods .goodsList .item .text .infor.data-v-a5aa7f30 {
  font-size: 24rpx;
}
.shoppingCart .invalidGoods .goodsList .item .text .end.data-v-a5aa7f30 {
  font-size: 26rpx;
  color: #bbb;
}
.footer.data-v-a5aa7f30 {
  z-index: 9;
  width: 100%;
  height: 100rpx;
  background-color: #fff;
  position: fixed;
  padding: 0 24rpx;
  box-sizing: border-box;
  border-top: 1rpx solid #eee;
  bottom: 0;
}
.footer .checkAll.data-v-a5aa7f30 {
  font-size: 28rpx;
  color: #282828;
  margin-left: 14rpx;
}
.footer .money.data-v-a5aa7f30 {
  font-size: 30rpx;
}
.footer .money .font-color.data-v-a5aa7f30 {
  font-weight: 600;
}
.footer .placeOrder.data-v-a5aa7f30 {
  color: #fff;
  font-size: 30rpx;
  width: 226rpx;
  height: 70rpx;
  border-radius: 50rpx;
  text-align: center;
  line-height: 70rpx;
  margin-left: 22rpx;
}
.footer .button .bnt.data-v-a5aa7f30 {
  font-size: 28rpx;
  color: #999;
  border-radius: 50rpx;
  border: 1px solid #999;
  width: 160rpx;
  height: 60rpx;
  text-align: center;
  line-height: 60rpx;
}
.footer .button form ~ form.data-v-a5aa7f30 {
  margin-left: 17rpx;
}
.uni-p-b-96.data-v-a5aa7f30 {
  height: 96rpx;
}
.my_nav.data-v-a5aa7f30 {
  top: calc(44px + 88rpx + 50rpx) !important;
}
.my_nav_top.data-v-a5aa7f30 {
  margin-top: calc(44px + 88rpx + 30rpx + 105rpx) !important;
}

