@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.goodList .item.data-v-11dfe3b9 {
  position: relative;
  padding-left: 30rpx;
}
.goodList .item .pictrue.data-v-11dfe3b9 {
  width: 180rpx;
  height: 180rpx;
  position: relative;
}
.goodList .item .pictrue image.data-v-11dfe3b9 {
  width: 100%;
  height: 100%;
  border-radius: 6rpx;
}
.goodList .item .pictrue .numPic.data-v-11dfe3b9 {
  position: absolute;
  left: 7rpx;
  top: 7rpx;
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
}
.goodList .item .underline.data-v-11dfe3b9 {
  padding: 30rpx 30rpx 30rpx 0;
  border-bottom: 1px solid #f5f5f5;
}
.goodList .item:nth-last-child(1) .underline.data-v-11dfe3b9 {
  border-bottom: 0;
}
.goodList .item .text.data-v-11dfe3b9 {
  font-size: 30rpx;
  color: #222;
  width: 489rpx;
}
.goodList .item .text .money.data-v-11dfe3b9 {
  font-size: 26rpx;
  font-weight: bold;
  margin-top: 50rpx;
}
.goodList .item .text .money .num.data-v-11dfe3b9 {
  font-size: 34rpx;
}
.goodList .item .text .vip-money.data-v-11dfe3b9 {
  font-size: 24rpx;
  color: #282828;
  font-weight: bold;
  margin-top: 15rpx;
}
.goodList .item .text .vip-money image.data-v-11dfe3b9 {
  width: 46rpx;
  height: 21rpx;
  margin: 0 22rpx 0 5rpx;
}
.goodList .item .text .vip-money .num.data-v-11dfe3b9 {
  font-size: 22rpx;
  color: #aaa;
  font-weight: normal;
  margin: -2rpx 0 0 0;
}
.goodList .item .iconfont.data-v-11dfe3b9 {
  position: absolute;
  right: 30rpx;
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  font-size: 30rpx;
  bottom: 38rpx;
}

