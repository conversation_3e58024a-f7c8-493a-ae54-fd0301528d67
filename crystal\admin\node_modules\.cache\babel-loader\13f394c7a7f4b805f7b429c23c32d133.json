{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\uploadPicture\\internal.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\uploadPicture\\internal.js", "mtime": 1753666157795}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\eslint-loader\\index.js", "mtime": 1753666298172}], "contextDependencies": [], "result": ["\"use strict\";\n\n(function () {\n  /* eslint-disable */\n  if (window.frameElement.id) {\n    var parent = window.parent,\n      dialog = parent.$EDITORUI[window.frameElement.id.replace(/_iframe$/, '')],\n      editor = dialog.editor,\n      UE = parent.UE,\n      domUtils = UE.dom.domUtils,\n      utils = UE.utils,\n      browser = UE.browser,\n      /* eslint-disable */\n      ajax = UE.ajax,\n      $G = function $G(id) {\n        return document.getElementById(id);\n      },\n      $focus = function $focus(node) {\n        setTimeout(function () {\n          if (browser.ie) {\n            var r = node.createTextRange();\n            r.collapse(false);\n            r.select();\n          } else {\n            node.focus();\n          }\n        }, 0);\n      };\n    window.nowEditor = {\n      editor: editor,\n      dialog: dialog\n    };\n    utils.loadFile(document, {\n      href: editor.options.themePath + editor.options.theme + '/dialogbase.css?cache=' + Math.random(),\n      tag: 'link',\n      type: 'text/css',\n      rel: 'stylesheet'\n    });\n    var lang = editor.getLang(dialog.className.split('-')[2]);\n    if (lang) {\n      domUtils.on(window, 'load', function () {\n        var langImgPath = editor.options.langPath + editor.options.lang + '/images/';\n        // 针对静态资源\n        for (var i in lang['static']) {\n          var dom = $G(i);\n          if (!dom) continue;\n          var tagName = dom.tagName,\n            content = lang['static'][i];\n          if (content.src) {\n            // clone\n            content = utils.extend({}, content, false);\n            content.src = langImgPath + content.src;\n          }\n          if (content.style) {\n            content = utils.extend({}, content, false);\n            content.style = content.style.replace(/url\\s*\\(/g, 'url(' + langImgPath);\n          }\n          switch (tagName.toLowerCase()) {\n            case 'var':\n              dom.parentNode.replaceChild(document.createTextNode(content), dom);\n              break;\n            case 'select':\n              var ops = dom.options;\n              for (var j = 0, oj; oj = ops[j];) {\n                oj.innerHTML = content.options[j++];\n              }\n              for (var p in content) {\n                p != 'options' && dom.setAttribute(p, content[p]);\n              }\n              break;\n            default:\n              domUtils.setAttributes(dom, content);\n          }\n        }\n      });\n    }\n  }\n})();", null]}