@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.nav-title.data-v-68961d5a {
  padding: 10px 40rpx;
  display: flex;
  align-items: center;
  height: 60rpx;
  line-height: 60rpx;
  background: #f6f6f6;
}
.nav-title .color.data-v-68961d5a {
  width: 10rpx;
  height: 30rpx;
  background: #c9ab79;
  border-radius: 6rpx;
  margin-right: 10rpx;
}
.botton_1.data-v-68961d5a {
  width: 45%;
  background-color: #c9ab79;
  color: #fff;
  font-size: 28rpx;
  font-weight: 600;
  height: 80rpx;
  border-radius: 50rpx;
  text-align: center;
  line-height: 80rpx;
}
.botton_2.data-v-68961d5a {
  width: 45%;
  background-color: #DD5C5F;
  color: #fff;
  font-size: 28rpx;
  font-weight: 600;
  height: 80rpx;
  border-radius: 50rpx;
  text-align: center;
  line-height: 80rpx;
}
.container.data-v-68961d5a {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.bottom.data-v-68961d5a {
  display: flex;
  padding: 20px;
  justify-content: space-around;
}

