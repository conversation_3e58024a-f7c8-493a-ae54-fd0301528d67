{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\list\\index.vue?vue&type=template&id=2f2cef3b&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\list\\index.vue", "mtime": 1753666157941}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"divBox relative\" },\n    [\n      _c(\n        \"el-card\",\n        { staticClass: \"box-card\" },\n        [\n          _c(\n            \"div\",\n            {\n              staticClass: \"clearfix\",\n              attrs: { slot: \"header\" },\n              slot: \"header\",\n            },\n            [\n              _c(\n                \"el-tabs\",\n                {\n                  on: {\n                    \"tab-click\": function ($event) {\n                      return _vm.getList(1)\n                    },\n                  },\n                  model: {\n                    value: _vm.loginType,\n                    callback: function ($$v) {\n                      _vm.loginType = $$v\n                    },\n                    expression: \"loginType\",\n                  },\n                },\n                _vm._l(_vm.headeNum, function (item, index) {\n                  return _c(\"el-tab-pane\", {\n                    key: index,\n                    attrs: { label: item.name, name: item.type.toString() },\n                  })\n                }),\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"div\",\n                { staticClass: \"container\" },\n                [\n                  _c(\n                    \"el-form\",\n                    {\n                      ref: \"userFrom\",\n                      attrs: {\n                        inline: \"\",\n                        size: \"small\",\n                        model: _vm.userFrom,\n                        \"label-position\": _vm.labelPosition,\n                        \"label-width\": \"100px\",\n                      },\n                    },\n                    [\n                      _c(\n                        \"el-row\",\n                        [\n                          _c(\n                            \"el-col\",\n                            {\n                              attrs: { xs: 24, sm: 24, md: 24, lg: 18, xl: 18 },\n                            },\n                            [\n                              _c(\n                                \"el-col\",\n                                _vm._b({}, \"el-col\", _vm.grid, false),\n                                [\n                                  _c(\n                                    \"el-form-item\",\n                                    { attrs: { label: \"用户搜索：\" } },\n                                    [\n                                      _c(\"el-input\", {\n                                        staticClass: \"selWidth\",\n                                        attrs: {\n                                          placeholder: \"请输入姓名或手机号\",\n                                          clearable: \"\",\n                                        },\n                                        model: {\n                                          value: _vm.userFrom.keywords,\n                                          callback: function ($$v) {\n                                            _vm.$set(\n                                              _vm.userFrom,\n                                              \"keywords\",\n                                              $$v\n                                            )\n                                          },\n                                          expression: \"userFrom.keywords\",\n                                        },\n                                      }),\n                                    ],\n                                    1\n                                  ),\n                                ],\n                                1\n                              ),\n                            ],\n                            1\n                          ),\n                          _vm._v(\" \"),\n                          _vm.collapse\n                            ? [\n                                _c(\n                                  \"el-col\",\n                                  {\n                                    attrs: {\n                                      xs: 24,\n                                      sm: 24,\n                                      md: 24,\n                                      lg: 18,\n                                      xl: 18,\n                                    },\n                                  },\n                                  [\n                                    _c(\n                                      \"el-col\",\n                                      _vm._b({}, \"el-col\", _vm.grid, false),\n                                      [\n                                        _c(\n                                          \"el-form-item\",\n                                          { attrs: { label: \"用户等级：\" } },\n                                          [\n                                            _c(\n                                              \"el-select\",\n                                              {\n                                                staticClass: \"selWidth\",\n                                                attrs: {\n                                                  placeholder: \"请选择\",\n                                                  clearable: \"\",\n                                                  filterable: \"\",\n                                                  multiple: \"\",\n                                                },\n                                                model: {\n                                                  value: _vm.levelData,\n                                                  callback: function ($$v) {\n                                                    _vm.levelData = $$v\n                                                  },\n                                                  expression: \"levelData\",\n                                                },\n                                              },\n                                              _vm._l(\n                                                _vm.levelList,\n                                                function (item, index) {\n                                                  return _c(\"el-option\", {\n                                                    key: index,\n                                                    attrs: {\n                                                      value: item.id,\n                                                      label: item.name,\n                                                    },\n                                                  })\n                                                }\n                                              ),\n                                              1\n                                            ),\n                                          ],\n                                          1\n                                        ),\n                                      ],\n                                      1\n                                    ),\n                                    _vm._v(\" \"),\n                                    _c(\n                                      \"el-col\",\n                                      _vm._b({}, \"el-col\", _vm.grid, false),\n                                      [\n                                        _c(\n                                          \"el-form-item\",\n                                          { attrs: { label: \"用户分组：\" } },\n                                          [\n                                            _c(\n                                              \"el-select\",\n                                              {\n                                                staticClass: \"selWidth\",\n                                                attrs: {\n                                                  placeholder: \"请选择\",\n                                                  clearable: \"\",\n                                                  filterable: \"\",\n                                                  multiple: \"\",\n                                                },\n                                                model: {\n                                                  value: _vm.groupData,\n                                                  callback: function ($$v) {\n                                                    _vm.groupData = $$v\n                                                  },\n                                                  expression: \"groupData\",\n                                                },\n                                              },\n                                              _vm._l(\n                                                _vm.groupList,\n                                                function (item, index) {\n                                                  return _c(\"el-option\", {\n                                                    key: index,\n                                                    attrs: {\n                                                      value: item.id,\n                                                      label: item.groupName,\n                                                    },\n                                                  })\n                                                }\n                                              ),\n                                              1\n                                            ),\n                                          ],\n                                          1\n                                        ),\n                                      ],\n                                      1\n                                    ),\n                                    _vm._v(\" \"),\n                                    _c(\n                                      \"el-col\",\n                                      _vm._b({}, \"el-col\", _vm.grid, false),\n                                      [\n                                        _c(\n                                          \"el-form-item\",\n                                          { attrs: { label: \"用户标签：\" } },\n                                          [\n                                            _c(\n                                              \"el-select\",\n                                              {\n                                                staticClass: \"selWidth\",\n                                                attrs: {\n                                                  placeholder: \"请选择\",\n                                                  clearable: \"\",\n                                                  filterable: \"\",\n                                                  multiple: \"\",\n                                                },\n                                                model: {\n                                                  value: _vm.labelData,\n                                                  callback: function ($$v) {\n                                                    _vm.labelData = $$v\n                                                  },\n                                                  expression: \"labelData\",\n                                                },\n                                              },\n                                              _vm._l(\n                                                _vm.labelLists,\n                                                function (item, index) {\n                                                  return _c(\"el-option\", {\n                                                    key: index,\n                                                    attrs: {\n                                                      value: item.id,\n                                                      label: item.name,\n                                                    },\n                                                  })\n                                                }\n                                              ),\n                                              1\n                                            ),\n                                          ],\n                                          1\n                                        ),\n                                      ],\n                                      1\n                                    ),\n                                  ],\n                                  1\n                                ),\n                                _vm._v(\" \"),\n                                _c(\n                                  \"el-col\",\n                                  {\n                                    attrs: {\n                                      xs: 24,\n                                      sm: 24,\n                                      md: 24,\n                                      lg: 18,\n                                      xl: 18,\n                                    },\n                                  },\n                                  [\n                                    _c(\n                                      \"el-col\",\n                                      _vm._b({}, \"el-col\", _vm.grid, false),\n                                      [\n                                        _c(\n                                          \"el-form-item\",\n                                          { attrs: { label: \"国家：\" } },\n                                          [\n                                            _c(\n                                              \"el-select\",\n                                              {\n                                                staticClass: \"selWidth\",\n                                                attrs: {\n                                                  placeholder: \"请选择\",\n                                                  clearable: \"\",\n                                                },\n                                                on: {\n                                                  \"on-change\":\n                                                    _vm.changeCountry,\n                                                },\n                                                model: {\n                                                  value: _vm.userFrom.country,\n                                                  callback: function ($$v) {\n                                                    _vm.$set(\n                                                      _vm.userFrom,\n                                                      \"country\",\n                                                      $$v\n                                                    )\n                                                  },\n                                                  expression:\n                                                    \"userFrom.country\",\n                                                },\n                                              },\n                                              [\n                                                _c(\"el-option\", {\n                                                  attrs: {\n                                                    value: \"\",\n                                                    label: \"全部\",\n                                                  },\n                                                }),\n                                                _vm._v(\" \"),\n                                                _c(\"el-option\", {\n                                                  attrs: {\n                                                    value: \"CN\",\n                                                    label: \"中国\",\n                                                  },\n                                                }),\n                                                _vm._v(\" \"),\n                                                _c(\"el-option\", {\n                                                  attrs: {\n                                                    value: \"OTHER\",\n                                                    label: \"国外\",\n                                                  },\n                                                }),\n                                              ],\n                                              1\n                                            ),\n                                          ],\n                                          1\n                                        ),\n                                      ],\n                                      1\n                                    ),\n                                    _vm._v(\" \"),\n                                    _vm.userFrom.country === \"CN\"\n                                      ? _c(\n                                          \"el-col\",\n                                          _vm._b({}, \"el-col\", _vm.grid, false),\n                                          [\n                                            _c(\n                                              \"el-form-item\",\n                                              { attrs: { label: \"省份：\" } },\n                                              [\n                                                _c(\"el-cascader\", {\n                                                  staticClass: \"selWidth\",\n                                                  attrs: {\n                                                    options: _vm.addresData,\n                                                    props: _vm.propsCity,\n                                                    filterable: \"\",\n                                                    clearable: \"\",\n                                                  },\n                                                  on: {\n                                                    change: _vm.handleChange,\n                                                  },\n                                                  model: {\n                                                    value: _vm.address,\n                                                    callback: function ($$v) {\n                                                      _vm.address = $$v\n                                                    },\n                                                    expression: \"address\",\n                                                  },\n                                                }),\n                                              ],\n                                              1\n                                            ),\n                                          ],\n                                          1\n                                        )\n                                      : _vm._e(),\n                                    _vm._v(\" \"),\n                                    _c(\n                                      \"el-col\",\n                                      _vm._b({}, \"el-col\", _vm.grid, false),\n                                      [\n                                        _c(\n                                          \"el-form-item\",\n                                          { attrs: { label: \"消费情况：\" } },\n                                          [\n                                            _c(\n                                              \"el-select\",\n                                              {\n                                                staticClass: \"selWidth\",\n                                                attrs: {\n                                                  placeholder: \"请选择\",\n                                                  clearable: \"\",\n                                                },\n                                                model: {\n                                                  value: _vm.userFrom.payCount,\n                                                  callback: function ($$v) {\n                                                    _vm.$set(\n                                                      _vm.userFrom,\n                                                      \"payCount\",\n                                                      $$v\n                                                    )\n                                                  },\n                                                  expression:\n                                                    \"userFrom.payCount\",\n                                                },\n                                              },\n                                              [\n                                                _c(\"el-option\", {\n                                                  attrs: {\n                                                    value: \"\",\n                                                    label: \"全部\",\n                                                  },\n                                                }),\n                                                _vm._v(\" \"),\n                                                _c(\"el-option\", {\n                                                  attrs: {\n                                                    value: \"0\",\n                                                    label: \"0\",\n                                                  },\n                                                }),\n                                                _vm._v(\" \"),\n                                                _c(\"el-option\", {\n                                                  attrs: {\n                                                    value: \"1\",\n                                                    label: \"1+\",\n                                                  },\n                                                }),\n                                                _vm._v(\" \"),\n                                                _c(\"el-option\", {\n                                                  attrs: {\n                                                    value: \"2\",\n                                                    label: \"2+\",\n                                                  },\n                                                }),\n                                                _vm._v(\" \"),\n                                                _c(\"el-option\", {\n                                                  attrs: {\n                                                    value: \"3\",\n                                                    label: \"3+\",\n                                                  },\n                                                }),\n                                                _vm._v(\" \"),\n                                                _c(\"el-option\", {\n                                                  attrs: {\n                                                    value: \"4\",\n                                                    label: \"4+\",\n                                                  },\n                                                }),\n                                                _vm._v(\" \"),\n                                                _c(\"el-option\", {\n                                                  attrs: {\n                                                    value: \"5\",\n                                                    label: \"5+\",\n                                                  },\n                                                }),\n                                              ],\n                                              1\n                                            ),\n                                          ],\n                                          1\n                                        ),\n                                      ],\n                                      1\n                                    ),\n                                    _vm._v(\" \"),\n                                    _c(\n                                      \"el-col\",\n                                      _vm._b({}, \"el-col\", _vm.grid, false),\n                                      [\n                                        _c(\n                                          \"el-form-item\",\n                                          {\n                                            staticClass: \"timeBox\",\n                                            attrs: { label: \"时间选择：\" },\n                                          },\n                                          [\n                                            _c(\"el-date-picker\", {\n                                              staticClass: \"selWidth\",\n                                              attrs: {\n                                                align: \"right\",\n                                                \"unlink-panels\": \"\",\n                                                \"value-format\": \"yyyy-MM-dd\",\n                                                format: \"yyyy-MM-dd\",\n                                                size: \"small\",\n                                                type: \"daterange\",\n                                                placement: \"bottom-end\",\n                                                placeholder: \"自定义时间\",\n                                                \"picker-options\":\n                                                  _vm.pickerOptions,\n                                              },\n                                              on: { change: _vm.onchangeTime },\n                                              model: {\n                                                value: _vm.timeVal,\n                                                callback: function ($$v) {\n                                                  _vm.timeVal = $$v\n                                                },\n                                                expression: \"timeVal\",\n                                              },\n                                            }),\n                                          ],\n                                          1\n                                        ),\n                                      ],\n                                      1\n                                    ),\n                                  ],\n                                  1\n                                ),\n                                _vm._v(\" \"),\n                                _c(\n                                  \"el-col\",\n                                  {\n                                    attrs: {\n                                      xs: 24,\n                                      sm: 24,\n                                      md: 24,\n                                      lg: 18,\n                                      xl: 18,\n                                    },\n                                  },\n                                  [\n                                    _c(\n                                      \"el-col\",\n                                      _vm._b({}, \"el-col\", _vm.grid, false),\n                                      [\n                                        _c(\n                                          \"el-form-item\",\n                                          { attrs: { label: \"访问情况：\" } },\n                                          [\n                                            _c(\n                                              \"el-select\",\n                                              {\n                                                staticClass: \"selWidth\",\n                                                attrs: {\n                                                  placeholder: \"请选择\",\n                                                  clearable: \"\",\n                                                },\n                                                model: {\n                                                  value:\n                                                    _vm.userFrom.accessType,\n                                                  callback: function ($$v) {\n                                                    _vm.$set(\n                                                      _vm.userFrom,\n                                                      \"accessType\",\n                                                      $$v\n                                                    )\n                                                  },\n                                                  expression:\n                                                    \"userFrom.accessType\",\n                                                },\n                                              },\n                                              [\n                                                _c(\"el-option\", {\n                                                  attrs: {\n                                                    value: 0,\n                                                    label: \"全部\",\n                                                  },\n                                                }),\n                                                _vm._v(\" \"),\n                                                _c(\"el-option\", {\n                                                  attrs: {\n                                                    value: 1,\n                                                    label: \"首次访问\",\n                                                  },\n                                                }),\n                                                _vm._v(\" \"),\n                                                _c(\"el-option\", {\n                                                  attrs: {\n                                                    value: 2,\n                                                    label: \"时间段访问过\",\n                                                  },\n                                                }),\n                                                _vm._v(\" \"),\n                                                _c(\"el-option\", {\n                                                  attrs: {\n                                                    value: 3,\n                                                    label: \"时间段未访问\",\n                                                  },\n                                                }),\n                                              ],\n                                              1\n                                            ),\n                                          ],\n                                          1\n                                        ),\n                                      ],\n                                      1\n                                    ),\n                                    _vm._v(\" \"),\n                                    _c(\n                                      \"el-col\",\n                                      _vm._b({}, \"el-col\", _vm.grid, false),\n                                      [\n                                        _c(\n                                          \"el-form-item\",\n                                          { attrs: { label: \"性别：\" } },\n                                          [\n                                            _c(\n                                              \"el-radio-group\",\n                                              {\n                                                staticClass: \"selWidth\",\n                                                attrs: { type: \"button\" },\n                                                model: {\n                                                  value: _vm.userFrom.sex,\n                                                  callback: function ($$v) {\n                                                    _vm.$set(\n                                                      _vm.userFrom,\n                                                      \"sex\",\n                                                      $$v\n                                                    )\n                                                  },\n                                                  expression: \"userFrom.sex\",\n                                                },\n                                              },\n                                              [\n                                                _c(\n                                                  \"el-radio-button\",\n                                                  { attrs: { label: \"\" } },\n                                                  [_c(\"span\", [_vm._v(\"全部\")])]\n                                                ),\n                                                _vm._v(\" \"),\n                                                _c(\n                                                  \"el-radio-button\",\n                                                  { attrs: { label: \"0\" } },\n                                                  [_c(\"span\", [_vm._v(\"未知\")])]\n                                                ),\n                                                _vm._v(\" \"),\n                                                _c(\n                                                  \"el-radio-button\",\n                                                  { attrs: { label: \"1\" } },\n                                                  [_c(\"span\", [_vm._v(\"男\")])]\n                                                ),\n                                                _vm._v(\" \"),\n                                                _c(\n                                                  \"el-radio-button\",\n                                                  { attrs: { label: \"2\" } },\n                                                  [_c(\"span\", [_vm._v(\"女\")])]\n                                                ),\n                                                _vm._v(\" \"),\n                                                _c(\n                                                  \"el-radio-button\",\n                                                  { attrs: { label: \"3\" } },\n                                                  [_c(\"span\", [_vm._v(\"保密\")])]\n                                                ),\n                                              ],\n                                              1\n                                            ),\n                                          ],\n                                          1\n                                        ),\n                                      ],\n                                      1\n                                    ),\n                                    _vm._v(\" \"),\n                                    _c(\n                                      \"el-col\",\n                                      _vm._b({}, \"el-col\", _vm.grid, false),\n                                      [\n                                        _c(\n                                          \"el-form-item\",\n                                          { attrs: { label: \"身份：\" } },\n                                          [\n                                            _c(\n                                              \"el-radio-group\",\n                                              {\n                                                staticClass: \"selWidth\",\n                                                attrs: { type: \"button\" },\n                                                model: {\n                                                  value:\n                                                    _vm.userFrom.isPromoter,\n                                                  callback: function ($$v) {\n                                                    _vm.$set(\n                                                      _vm.userFrom,\n                                                      \"isPromoter\",\n                                                      $$v\n                                                    )\n                                                  },\n                                                  expression:\n                                                    \"userFrom.isPromoter\",\n                                                },\n                                              },\n                                              [\n                                                _c(\n                                                  \"el-radio-button\",\n                                                  { attrs: { label: \"\" } },\n                                                  [_c(\"span\", [_vm._v(\"全部\")])]\n                                                ),\n                                                _vm._v(\" \"),\n                                                _c(\n                                                  \"el-radio-button\",\n                                                  { attrs: { label: \"1\" } },\n                                                  [\n                                                    _c(\"span\", [\n                                                      _vm._v(\"推广员\"),\n                                                    ]),\n                                                  ]\n                                                ),\n                                                _vm._v(\" \"),\n                                                _c(\n                                                  \"el-radio-button\",\n                                                  { attrs: { label: \"0\" } },\n                                                  [\n                                                    _c(\"span\", [\n                                                      _vm._v(\"普通用户\"),\n                                                    ]),\n                                                  ]\n                                                ),\n                                              ],\n                                              1\n                                            ),\n                                          ],\n                                          1\n                                        ),\n                                      ],\n                                      1\n                                    ),\n                                  ],\n                                  1\n                                ),\n                              ]\n                            : _vm._e(),\n                          _vm._v(\" \"),\n                          _c(\n                            \"el-col\",\n                            {\n                              staticClass: \"text-right userFrom\",\n                              attrs: { xs: 24, sm: 24, md: 24, lg: 6, xl: 6 },\n                            },\n                            [\n                              _c(\n                                \"el-form-item\",\n                                [\n                                  _c(\n                                    \"el-button\",\n                                    {\n                                      staticClass: \"mr15\",\n                                      attrs: {\n                                        type: \"primary\",\n                                        icon: \"ios-search\",\n                                        label: \"default\",\n                                        size: \"small\",\n                                      },\n                                      on: { click: _vm.userSearchs },\n                                    },\n                                    [_vm._v(\"搜索\")]\n                                  ),\n                                  _vm._v(\" \"),\n                                  _c(\n                                    \"el-button\",\n                                    {\n                                      staticClass: \"ResetSearch mr10\",\n                                      attrs: { size: \"small\" },\n                                      on: {\n                                        click: function ($event) {\n                                          return _vm.reset(\"userFrom\")\n                                        },\n                                      },\n                                    },\n                                    [_vm._v(\"重置\")]\n                                  ),\n                                  _vm._v(\" \"),\n                                  _c(\n                                    \"a\",\n                                    {\n                                      staticClass: \"ivu-ml-8\",\n                                      on: {\n                                        click: function ($event) {\n                                          _vm.collapse = !_vm.collapse\n                                        },\n                                      },\n                                    },\n                                    [\n                                      !_vm.collapse\n                                        ? [\n                                            _vm._v(\n                                              \"\\n                    展开 \"\n                                            ),\n                                            _c(\"i\", {\n                                              staticClass: \"el-icon-arrow-down\",\n                                            }),\n                                          ]\n                                        : [\n                                            _vm._v(\n                                              \"\\n                    收起 \"\n                                            ),\n                                            _c(\"i\", {\n                                              staticClass: \"el-icon-arrow-up\",\n                                            }),\n                                          ],\n                                    ],\n                                    2\n                                  ),\n                                ],\n                                1\n                              ),\n                            ],\n                            1\n                          ),\n                        ],\n                        2\n                      ),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"div\",\n                { staticClass: \"btn_bt\" },\n                [\n                  _c(\n                    \"el-button\",\n                    {\n                      directives: [\n                        {\n                          name: \"hasPermi\",\n                          rawName: \"v-hasPermi\",\n                          value: [\"admin:coupon:user:receive\"],\n                          expression: \"['admin:coupon:user:receive']\",\n                        },\n                      ],\n                      staticClass: \"mr10\",\n                      attrs: { size: \"small\", type: \"primary\" },\n                      on: { click: _vm.onSend },\n                    },\n                    [_vm._v(\"发送优惠券\")]\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-button\",\n                    {\n                      staticClass: \"mr10\",\n                      attrs: { size: \"small\" },\n                      on: {\n                        click: function ($event) {\n                          return _vm.setBatch(\"group\")\n                        },\n                      },\n                    },\n                    [_vm._v(\"批量设置分组\")]\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-button\",\n                    {\n                      staticClass: \"mr10\",\n                      attrs: { size: \"small\" },\n                      on: {\n                        click: function ($event) {\n                          return _vm.setBatch(\"label\")\n                        },\n                      },\n                    },\n                    [_vm._v(\"批量设置标签\")]\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-table\",\n            {\n              directives: [\n                {\n                  name: \"loading\",\n                  rawName: \"v-loading\",\n                  value: _vm.listLoading,\n                  expression: \"listLoading\",\n                },\n              ],\n              ref: \"table\",\n              staticStyle: { width: \"100%\" },\n              attrs: {\n                data: _vm.tableData.data,\n                size: \"mini\",\n                \"highlight-current-row\": \"\",\n              },\n              on: { \"selection-change\": _vm.onSelectTab },\n            },\n            [\n              _c(\"el-table-column\", {\n                attrs: { type: \"expand\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (props) {\n                      return [\n                        _c(\n                          \"el-form\",\n                          {\n                            staticClass: \"demo-table-expand\",\n                            attrs: { \"label-position\": \"left\", inline: \"\" },\n                          },\n                          [\n                            _c(\"el-form-item\", { attrs: { label: \"身份：\" } }, [\n                              _c(\"span\", [\n                                _vm._v(\n                                  _vm._s(\n                                    _vm._f(\"filterIsPromoter\")(\n                                      props.row.isPromoter\n                                    )\n                                  )\n                                ),\n                              ]),\n                            ]),\n                            _vm._v(\" \"),\n                            _c(\n                              \"el-form-item\",\n                              { attrs: { label: \"首次访问：\" } },\n                              [\n                                _c(\"span\", [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm._f(\"filterEmpty\")(\n                                        props.row.createTime\n                                      )\n                                    )\n                                  ),\n                                ]),\n                              ]\n                            ),\n                            _vm._v(\" \"),\n                            _c(\n                              \"el-form-item\",\n                              { attrs: { label: \"近次访问：\" } },\n                              [\n                                _c(\"span\", [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm._f(\"filterEmpty\")(\n                                        props.row.lastLoginTime\n                                      )\n                                    )\n                                  ),\n                                ]),\n                              ]\n                            ),\n                            _vm._v(\" \"),\n                            _c(\n                              \"el-form-item\",\n                              { attrs: { label: \"手机号：\" } },\n                              [\n                                _c(\"span\", [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm._f(\"filterEmpty\")(props.row.phone)\n                                    )\n                                  ),\n                                ]),\n                              ]\n                            ),\n                            _vm._v(\" \"),\n                            _c(\"el-form-item\", { attrs: { label: \"标签：\" } }, [\n                              _c(\"span\", [\n                                _vm._v(\n                                  _vm._s(\n                                    _vm._f(\"filterEmpty\")(props.row.tagName)\n                                  )\n                                ),\n                              ]),\n                            ]),\n                            _vm._v(\" \"),\n                            _c(\"el-form-item\", { attrs: { label: \"地址：\" } }, [\n                              _c(\"span\", [\n                                _vm._v(\n                                  _vm._s(\n                                    _vm._f(\"filterEmpty\")(props.row.addres)\n                                  )\n                                ),\n                              ]),\n                            ]),\n                            _vm._v(\" \"),\n                            _c(\n                              \"el-form-item\",\n                              {\n                                staticStyle: {\n                                  width: \"100%\",\n                                  display: \"flex\",\n                                  \"margin-right\": \"10px\",\n                                },\n                                attrs: { label: \"备注：\" },\n                              },\n                              [\n                                _c(\"span\", [\n                                  _vm._v(\n                                    _vm._s(\n                                      _vm._f(\"filterEmpty\")(props.row.mark)\n                                    )\n                                  ),\n                                ]),\n                              ]\n                            ),\n                          ],\n                          1\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { type: \"selection\", width: \"55\" },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { prop: \"uid\", label: \"ID\", \"min-width\": \"80\" },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { label: \"头像\", \"min-width\": \"80\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\n                          \"div\",\n                          { staticClass: \"demo-image__preview\" },\n                          [\n                            _c(\"el-image\", {\n                              staticStyle: { width: \"36px\", height: \"36px\" },\n                              attrs: {\n                                src: scope.row.avatar,\n                                \"preview-src-list\": [scope.row.avatar],\n                              },\n                            }),\n                          ],\n                          1\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { label: \"姓名\", \"min-width\": \"160\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\"span\", [\n                          _vm._v(\n                            _vm._s(_vm._f(\"filterEmpty\")(scope.row.nickname)) +\n                              \" | \" +\n                              _vm._s(_vm._f(\"sexFilter\")(scope.row.sex))\n                          ),\n                        ]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { label: \"用户等级\", \"min-width\": \"100\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\"span\", [\n                          _vm._v(\n                            _vm._s(\n                              _vm._f(\"filterEmpty\")(\n                                _vm._f(\"levelFilter\")(scope.row.level)\n                              )\n                            )\n                          ),\n                        ]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { prop: \"groupName\", label: \"分组\", \"min-width\": \"100\" },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  prop: \"spreadNickname\",\n                  label: \"推荐人\",\n                  \"min-width\": \"130\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { label: \"手机号\", \"min-width\": \"100\" },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\"span\", [\n                          _vm._v(\n                            _vm._s(_vm._f(\"filterEmpty\")(scope.row.phone))\n                          ),\n                        ]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { prop: \"nowMoney\", label: \"余额\", \"min-width\": \"100\" },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: { prop: \"integral\", label: \"积分\", \"min-width\": \"100\" },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: \"操作\",\n                  \"min-width\": \"130\",\n                  fixed: \"right\",\n                  align: \"center\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\n                          \"el-button\",\n                          {\n                            directives: [\n                              {\n                                name: \"hasPermi\",\n                                rawName: \"v-hasPermi\",\n                                value: [\"admin:user:infobycondition\"],\n                                expression: \"['admin:user:infobycondition']\",\n                              },\n                            ],\n                            attrs: { type: \"text\", size: \"small\" },\n                            on: {\n                              click: function ($event) {\n                                return _vm.editUser(scope.row.uid)\n                              },\n                            },\n                          },\n                          [_vm._v(\"编辑\")]\n                        ),\n                        _vm._v(\" \"),\n                        _c(\n                          \"el-button\",\n                          {\n                            directives: [\n                              {\n                                name: \"hasPermi\",\n                                rawName: \"v-hasPermi\",\n                                value: [\"admin:user:infobycondition\"],\n                                expression: \"['admin:user:infobycondition']\",\n                              },\n                            ],\n                            attrs: { type: \"text\", size: \"small\" },\n                            on: {\n                              click: function ($event) {\n                                return _vm.goprocess(scope.row.uid)\n                              },\n                            },\n                          },\n                          [_vm._v(\"档案\")]\n                        ),\n                        _vm._v(\" \"),\n                        _c(\n                          \"el-dropdown\",\n                          { attrs: { trigger: \"click\" } },\n                          [\n                            _c(\"span\", { staticClass: \"el-dropdown-link\" }, [\n                              _vm._v(\"\\n              更多\"),\n                              _c(\"i\", {\n                                staticClass:\n                                  \"el-icon-arrow-down el-icon--right\",\n                              }),\n                            ]),\n                            _vm._v(\" \"),\n                            _c(\n                              \"el-dropdown-menu\",\n                              { attrs: { slot: \"dropdown\" }, slot: \"dropdown\" },\n                              [\n                                _vm.checkPermi([\"admin:user:topdetail\"])\n                                  ? _c(\n                                      \"el-dropdown-item\",\n                                      {\n                                        nativeOn: {\n                                          click: function ($event) {\n                                            return _vm.onDetails(scope.row.uid)\n                                          },\n                                        },\n                                      },\n                                      [_vm._v(\"账户详情\")]\n                                    )\n                                  : _vm._e(),\n                                _vm._v(\" \"),\n                                _vm.checkPermi([\"admin:user:operate:founds\"])\n                                  ? _c(\n                                      \"el-dropdown-item\",\n                                      {\n                                        nativeOn: {\n                                          click: function ($event) {\n                                            return _vm.editPoint(scope.row.uid)\n                                          },\n                                        },\n                                      },\n                                      [_vm._v(\"积分余额\")]\n                                    )\n                                  : _vm._e(),\n                                _vm._v(\" \"),\n                                _vm.checkPermi([\"admin:user:group\"])\n                                  ? _c(\n                                      \"el-dropdown-item\",\n                                      {\n                                        nativeOn: {\n                                          click: function ($event) {\n                                            return _vm.setBatch(\n                                              \"group\",\n                                              scope.row\n                                            )\n                                          },\n                                        },\n                                      },\n                                      [_vm._v(\"设置分组\")]\n                                    )\n                                  : _vm._e(),\n                                _vm._v(\" \"),\n                                _vm.checkPermi([\"admin:user:tag\"])\n                                  ? _c(\n                                      \"el-dropdown-item\",\n                                      {\n                                        nativeOn: {\n                                          click: function ($event) {\n                                            return _vm.setBatch(\n                                              \"label\",\n                                              scope.row\n                                            )\n                                          },\n                                        },\n                                      },\n                                      [_vm._v(\"设置标签\")]\n                                    )\n                                  : _vm._e(),\n                                _vm._v(\" \"),\n                                _vm.checkPermi([\"admin:user:update:phone\"])\n                                  ? _c(\n                                      \"el-dropdown-item\",\n                                      {\n                                        nativeOn: {\n                                          click: function ($event) {\n                                            return _vm.setPhone(scope.row)\n                                          },\n                                        },\n                                      },\n                                      [_vm._v(\"修改手机号\")]\n                                    )\n                                  : _vm._e(),\n                                _vm._v(\" \"),\n                                _vm.checkPermi([\"admin:user:update:level\"])\n                                  ? _c(\n                                      \"el-dropdown-item\",\n                                      {\n                                        nativeOn: {\n                                          click: function ($event) {\n                                            return _vm.onLevel(\n                                              scope.row.uid,\n                                              scope.row.level\n                                            )\n                                          },\n                                        },\n                                      },\n                                      [_vm._v(\"修改用户等级\")]\n                                    )\n                                  : _vm._e(),\n                                _vm._v(\" \"),\n                                _vm.checkPermi([\"admin:user:update:spread\"])\n                                  ? _c(\n                                      \"el-dropdown-item\",\n                                      {\n                                        nativeOn: {\n                                          click: function ($event) {\n                                            return _vm.setExtension(scope.row)\n                                          },\n                                        },\n                                      },\n                                      [_vm._v(\"修改上级推广人\")]\n                                    )\n                                  : _vm._e(),\n                                _vm._v(\" \"),\n                                scope.row.spreadUid &&\n                                scope.row.spreadUid > 0 &&\n                                _vm.checkPermi([\"admin:retail:spread:clean\"])\n                                  ? _c(\n                                      \"el-dropdown-item\",\n                                      {\n                                        nativeOn: {\n                                          click: function ($event) {\n                                            return _vm.clearSpread(scope.row)\n                                          },\n                                        },\n                                      },\n                                      [_vm._v(\"清除上级推广人\")]\n                                    )\n                                  : _vm._e(),\n                              ],\n                              1\n                            ),\n                          ],\n                          1\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"div\",\n            { staticClass: \"block\" },\n            [\n              _c(\"el-pagination\", {\n                attrs: {\n                  \"page-sizes\": [15, 30, 45, 60],\n                  \"page-size\": _vm.userFrom.limit,\n                  \"current-page\": _vm.userFrom.page,\n                  layout: \"total, sizes, prev, pager, next, jumper\",\n                  total: _vm.tableData.total,\n                },\n                on: {\n                  \"size-change\": _vm.handleSizeChange,\n                  \"current-change\": _vm.pageChange,\n                },\n              }),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"el-dialog\",\n        {\n          attrs: {\n            title: \"修改推广人\",\n            visible: _vm.extensionVisible,\n            width: \"500px\",\n            \"before-close\": _vm.handleCloseExtension,\n          },\n          on: {\n            \"update:visible\": function ($event) {\n              _vm.extensionVisible = $event\n            },\n          },\n        },\n        [\n          _c(\n            \"el-form\",\n            {\n              directives: [\n                {\n                  name: \"loading\",\n                  rawName: \"v-loading\",\n                  value: _vm.loading,\n                  expression: \"loading\",\n                },\n              ],\n              ref: \"formExtension\",\n              staticClass: \"formExtension mt20\",\n              attrs: {\n                model: _vm.formExtension,\n                rules: _vm.ruleInline,\n                \"label-width\": \"120px\",\n              },\n              nativeOn: {\n                submit: function ($event) {\n                  $event.preventDefault()\n                },\n              },\n            },\n            [\n              _c(\n                \"el-form-item\",\n                { attrs: { label: \"用户头像：\", prop: \"image\" } },\n                [\n                  _c(\n                    \"div\",\n                    {\n                      staticClass: \"upLoadPicBox\",\n                      on: { click: _vm.modalPicTap },\n                    },\n                    [\n                      _vm.formExtension.image\n                        ? _c(\"div\", { staticClass: \"pictrue\" }, [\n                            _c(\"img\", {\n                              attrs: { src: _vm.formExtension.image },\n                            }),\n                          ])\n                        : _c(\"div\", { staticClass: \"upLoad\" }, [\n                            _c(\"i\", {\n                              staticClass: \"el-icon-camera cameraIconfont\",\n                            }),\n                          ]),\n                    ]\n                  ),\n                ]\n              ),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"span\",\n            {\n              staticClass: \"dialog-footer\",\n              attrs: { slot: \"footer\" },\n              slot: \"footer\",\n            },\n            [\n              _c(\n                \"el-button\",\n                {\n                  attrs: { type: \"primary\" },\n                  on: {\n                    click: function ($event) {\n                      return _vm.onSubExtension(\"formExtension\")\n                    },\n                  },\n                },\n                [_vm._v(\"确 定\")]\n              ),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"el-dialog\",\n        {\n          attrs: {\n            title: \"用户列表\",\n            visible: _vm.userVisible,\n            width: \"900px\",\n          },\n          on: {\n            \"update:visible\": function ($event) {\n              _vm.userVisible = $event\n            },\n          },\n        },\n        [\n          _vm.userVisible\n            ? _c(\"user-list\", { on: { getTemplateRow: _vm.getTemplateRow } })\n            : _vm._e(),\n          _vm._v(\" \"),\n          _c(\n            \"span\",\n            {\n              staticClass: \"dialog-footer\",\n              attrs: { slot: \"footer\" },\n              slot: \"footer\",\n            },\n            [\n              _c(\n                \"el-button\",\n                {\n                  on: {\n                    click: function ($event) {\n                      _vm.userVisible = false\n                    },\n                  },\n                },\n                [_vm._v(\"取 消\")]\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-button\",\n                {\n                  attrs: { type: \"primary\" },\n                  on: {\n                    click: function ($event) {\n                      _vm.userVisible = false\n                    },\n                  },\n                },\n                [_vm._v(\"确 定\")]\n              ),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"el-dialog\",\n        {\n          attrs: {\n            title: \"设置\",\n            visible: _vm.dialogVisible,\n            width: \"500px\",\n            \"before-close\": _vm.handleClose,\n          },\n          on: {\n            \"update:visible\": function ($event) {\n              _vm.dialogVisible = $event\n            },\n          },\n        },\n        [\n          _c(\n            \"el-form\",\n            {\n              directives: [\n                {\n                  name: \"loading\",\n                  rawName: \"v-loading\",\n                  value: _vm.loading,\n                  expression: \"loading\",\n                },\n              ],\n              ref: \"dynamicValidateForm\",\n              staticClass: \"demo-dynamic\",\n              attrs: { model: _vm.dynamicValidateForm, \"label-width\": \"100px\" },\n            },\n            [\n              _vm.batchName === \"group\"\n                ? _c(\n                    \"el-form-item\",\n                    {\n                      key: \"1\",\n                      attrs: {\n                        prop: \"groupId\",\n                        label: \"用户分组\",\n                        rules: [\n                          {\n                            required: true,\n                            message: \"请选择用户分组\",\n                            trigger: \"change\",\n                          },\n                        ],\n                      },\n                    },\n                    [\n                      _c(\n                        \"el-select\",\n                        {\n                          staticStyle: { width: \"80%\" },\n                          attrs: { placeholder: \"请选择分组\", filterable: \"\" },\n                          model: {\n                            value: _vm.dynamicValidateForm.groupId,\n                            callback: function ($$v) {\n                              _vm.$set(_vm.dynamicValidateForm, \"groupId\", $$v)\n                            },\n                            expression: \"dynamicValidateForm.groupId\",\n                          },\n                        },\n                        _vm._l(_vm.groupList, function (item, index) {\n                          return _c(\"el-option\", {\n                            key: index,\n                            attrs: { value: item.id, label: item.groupName },\n                          })\n                        }),\n                        1\n                      ),\n                    ],\n                    1\n                  )\n                : _c(\n                    \"el-form-item\",\n                    {\n                      attrs: {\n                        prop: \"groupId\",\n                        label: \"用户标签\",\n                        rules: [\n                          {\n                            required: true,\n                            message: \"请选择用户标签\",\n                            trigger: \"change\",\n                          },\n                        ],\n                      },\n                    },\n                    [\n                      _c(\n                        \"el-select\",\n                        {\n                          staticStyle: { width: \"80%\" },\n                          attrs: { placeholder: \"请选择标签\", filterable: \"\" },\n                          model: {\n                            value: _vm.dynamicValidateForm.groupId,\n                            callback: function ($$v) {\n                              _vm.$set(_vm.dynamicValidateForm, \"groupId\", $$v)\n                            },\n                            expression: \"dynamicValidateForm.groupId\",\n                          },\n                        },\n                        _vm._l(_vm.labelLists, function (item, index) {\n                          return _c(\"el-option\", {\n                            key: index,\n                            attrs: { value: item.id, label: item.name },\n                          })\n                        }),\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"span\",\n            {\n              staticClass: \"dialog-footer\",\n              attrs: { slot: \"footer\" },\n              slot: \"footer\",\n            },\n            [\n              _c(\"el-button\", { on: { click: _vm.handleClose } }, [\n                _vm._v(\"取 消\"),\n              ]),\n              _vm._v(\" \"),\n              _c(\n                \"el-button\",\n                {\n                  attrs: { type: \"primary\" },\n                  on: {\n                    click: function ($event) {\n                      return _vm.submitForm(\"dynamicValidateForm\")\n                    },\n                  },\n                },\n                [_vm._v(\"确 定\")]\n              ),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"el-dialog\",\n        {\n          attrs: { title: \"编辑\", visible: _vm.visible, width: \"600px\" },\n          on: {\n            \"update:visible\": function ($event) {\n              _vm.visible = $event\n            },\n          },\n        },\n        [\n          _vm.visible\n            ? _c(\"edit-from\", {\n                attrs: { uid: _vm.uid },\n                on: { resetForm: _vm.resetForm },\n              })\n            : _vm._e(),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"el-dialog\",\n        {\n          attrs: {\n            title: \"积分余额\",\n            visible: _vm.VisiblePoint,\n            width: \"500px\",\n            \"close-on-click-modal\": false,\n            \"before-close\": _vm.handlePointClose,\n          },\n          on: {\n            \"update:visible\": function ($event) {\n              _vm.VisiblePoint = $event\n            },\n          },\n        },\n        [\n          _c(\n            \"el-form\",\n            {\n              directives: [\n                {\n                  name: \"loading\",\n                  rawName: \"v-loading\",\n                  value: _vm.loadingPoint,\n                  expression: \"loadingPoint\",\n                },\n              ],\n              ref: \"PointValidateForm\",\n              staticClass: \"demo-dynamic\",\n              attrs: { model: _vm.PointValidateForm, \"label-width\": \"100px\" },\n            },\n            [\n              _c(\n                \"el-form-item\",\n                { attrs: { label: \"修改余额\", required: \"\" } },\n                [\n                  _c(\n                    \"el-radio-group\",\n                    {\n                      model: {\n                        value: _vm.PointValidateForm.moneyType,\n                        callback: function ($$v) {\n                          _vm.$set(_vm.PointValidateForm, \"moneyType\", $$v)\n                        },\n                        expression: \"PointValidateForm.moneyType\",\n                      },\n                    },\n                    [\n                      _c(\"el-radio\", { attrs: { label: 1 } }, [_vm._v(\"增加\")]),\n                      _vm._v(\" \"),\n                      _c(\"el-radio\", { attrs: { label: 2 } }, [_vm._v(\"减少\")]),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                { attrs: { label: \"余额\", required: \"\" } },\n                [\n                  _c(\"el-input-number\", {\n                    attrs: {\n                      type: \"text\",\n                      precision: 2,\n                      step: 0.1,\n                      min: 0,\n                      max: 999999,\n                    },\n                    model: {\n                      value: _vm.PointValidateForm.moneyValue,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.PointValidateForm, \"moneyValue\", $$v)\n                      },\n                      expression: \"PointValidateForm.moneyValue\",\n                    },\n                  }),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                { attrs: { label: \"修改积分\", required: \"\" } },\n                [\n                  _c(\n                    \"el-radio-group\",\n                    {\n                      model: {\n                        value: _vm.PointValidateForm.integralType,\n                        callback: function ($$v) {\n                          _vm.$set(_vm.PointValidateForm, \"integralType\", $$v)\n                        },\n                        expression: \"PointValidateForm.integralType\",\n                      },\n                    },\n                    [\n                      _c(\"el-radio\", { attrs: { label: 1 } }, [_vm._v(\"增加\")]),\n                      _vm._v(\" \"),\n                      _c(\"el-radio\", { attrs: { label: 2 } }, [_vm._v(\"减少\")]),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                { attrs: { label: \"积分\", required: \"\" } },\n                [\n                  _c(\"el-input-number\", {\n                    attrs: {\n                      type: \"text\",\n                      \"step-strictly\": \"\",\n                      min: 0,\n                      max: 999999,\n                    },\n                    model: {\n                      value: _vm.PointValidateForm.integralValue,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.PointValidateForm, \"integralValue\", $$v)\n                      },\n                      expression: \"PointValidateForm.integralValue\",\n                    },\n                  }),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"span\",\n            {\n              staticClass: \"dialog-footer\",\n              attrs: { slot: \"footer\" },\n              slot: \"footer\",\n            },\n            [\n              _c(\"el-button\", { on: { click: _vm.handlePointClose } }, [\n                _vm._v(\"取 消\"),\n              ]),\n              _vm._v(\" \"),\n              _c(\n                \"el-button\",\n                {\n                  attrs: { type: \"primary\", loading: _vm.loadingBtn },\n                  on: {\n                    click: function ($event) {\n                      return _vm.submitPointForm(\"PointValidateForm\")\n                    },\n                  },\n                },\n                [_vm._v(\"确 定\")]\n              ),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _vm.uid\n        ? _c(\n            \"el-dialog\",\n            {\n              attrs: {\n                title: \"用户详情\",\n                visible: _vm.Visible,\n                width: \"1100px\",\n                \"before-close\": _vm.Close,\n              },\n              on: {\n                \"update:visible\": function ($event) {\n                  _vm.Visible = $event\n                },\n              },\n            },\n            [\n              _vm.Visible\n                ? _c(\"user-details\", {\n                    ref: \"userDetails\",\n                    attrs: { uid: _vm.uid },\n                  })\n                : _vm._e(),\n            ],\n            1\n          )\n        : _vm._e(),\n      _vm._v(\" \"),\n      _c(\n        \"el-dialog\",\n        {\n          attrs: {\n            title: \"设置\",\n            visible: _vm.levelVisible,\n            width: \"600px\",\n            \"before-close\": _vm.Close,\n          },\n          on: {\n            \"update:visible\": function ($event) {\n              _vm.levelVisible = $event\n            },\n          },\n        },\n        [\n          _c(\"level-edit\", {\n            attrs: { levelInfo: _vm.levelInfo, levelList: _vm.levelList },\n          }),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}