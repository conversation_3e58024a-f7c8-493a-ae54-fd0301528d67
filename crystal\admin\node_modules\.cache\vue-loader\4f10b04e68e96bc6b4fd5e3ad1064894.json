{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\grade\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\grade\\index.vue", "mtime": 1753666157939}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { userListApi, groupListApi, levelListApi, levelUseApi, levelDeleteApi } from '@/api/user'\nimport creatGrade from './creatGrade'\nimport { checkPermi } from \"@/utils/permission\"; // 权限判断函数\nexport default {\n  name: 'Grade',\n  filters: {\n    typeFilter(status) {\n      const statusMap = {\n        'wechat': '微信用户',\n        'routine': '小程序你用户',\n        'h5': 'H5用户'\n      }\n      return statusMap[status]\n    }\n  },\n  components: {creatGrade},\n  data() {\n    return {\n      listLoading: true,\n      userInfo:{},\n      tableData: {\n        data: [],\n        total: 0,\n      }\n    }\n  },\n  mounted() {\n    this.getList()\n  },\n  methods: {\n    checkPermi,\n    seachList() {\n      this.getList()\n    },\n    add() {\n      this.$refs.grades.dialogVisible = true\n      this.userInfo = {};\n    },\n    edit(id) {\n      // this.$refs.grades.info(id)\n      this.userInfo = id;\n      this.$refs.grades.dialogVisible = true\n    },\n    // 列表\n    getList() {\n      this.listLoading = true\n      levelListApi().then(res => {\n        this.tableData.data = res\n        this.listLoading = false\n      }).catch(() => {\n        this.listLoading = false\n      })\n    },\n    // 删除\n    handleDelete(id, idx) {\n      this.$modalSure('删除吗？删除会导致对应用户等级数据清空，请谨慎操作！').then(() => {\n        levelDeleteApi(id).then(() => {\n          this.$message.success('删除成功')\n          this.tableData.data.splice(idx, 1)\n        })\n      })\n    },\n    onchangeIsShow(row) {\n      if(row.isShow == false){\n        row.isShow = !row.isShow\n        levelUseApi({id: row.id, isShow:row.isShow}).then(() => {\n          this.$message.success('修改成功')\n          this.getList()\n        }).catch(()=>{\n          row.isShow = !row.isShow\n        })\n      }else{\n        this.$modalSure('该操作会导致对应用户等级隐藏，请谨慎操作').then(() => {\n          row.isShow = !row.isShow\n          levelUseApi({id: row.id, isShow:row.isShow}).then(() => {\n            this.$message.success('修改成功')\n            this.getList()\n          }).catch(()=>{\n            row.isShow = !row.isShow\n          })\n        })\n      }\n    }\n  }\n}\n", null]}