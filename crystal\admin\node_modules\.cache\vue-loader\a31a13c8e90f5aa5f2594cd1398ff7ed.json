{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\FormGenerator\\index\\JsonDrawer.vue?vue&type=template&id=575e4f74&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\FormGenerator\\index\\JsonDrawer.vue", "mtime": 1753666157770}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    [\n      _c(\n        \"el-drawer\",\n        _vm._g(\n          _vm._b(\n            {\n              attrs: { \"append-to-body\": \"\" },\n              on: { opened: _vm.onOpen, close: _vm.onClose },\n            },\n            \"el-drawer\",\n            _vm.$attrs,\n            false\n          ),\n          _vm.$listeners\n        ),\n        [\n          _c(\n            \"div\",\n            { staticClass: \"action-bar\", style: { \"text-align\": \"left\" } },\n            [\n              _c(\n                \"span\",\n                { staticClass: \"bar-btn\", on: { click: _vm.refresh } },\n                [\n                  _c(\"i\", { staticClass: \"el-icon-refresh\" }),\n                  _vm._v(\"\\n        刷新\\n      \"),\n                ]\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"span\",\n                { ref: \"copyBtn\", staticClass: \"bar-btn copy-json-btn\" },\n                [\n                  _c(\"i\", { staticClass: \"el-icon-document-copy\" }),\n                  _vm._v(\"\\n        复制JSON\\n      \"),\n                ]\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"span\",\n                { staticClass: \"bar-btn\", on: { click: _vm.exportJsonFile } },\n                [\n                  _c(\"i\", { staticClass: \"el-icon-download\" }),\n                  _vm._v(\"\\n        导出JSON文件\\n      \"),\n                ]\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"span\",\n                {\n                  staticClass: \"bar-btn delete-btn\",\n                  on: {\n                    click: function ($event) {\n                      return _vm.$emit(\"update:visible\", false)\n                    },\n                  },\n                },\n                [\n                  _c(\"i\", { staticClass: \"el-icon-circle-close\" }),\n                  _vm._v(\"\\n        关闭\\n      \"),\n                ]\n              ),\n            ]\n          ),\n          _vm._v(\" \"),\n          _c(\"div\", {\n            staticClass: \"json-editor\",\n            attrs: { id: \"editorJson\" },\n          }),\n        ]\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}