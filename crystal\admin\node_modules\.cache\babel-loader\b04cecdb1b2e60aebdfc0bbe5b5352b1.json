{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\FormGenerator\\index\\IconsDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\FormGenerator\\index\\IconsDialog.vue", "mtime": 1753666157770}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _icon = _interopRequireDefault(require(\"../utils/icon.json\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nvar originList = _icon.default.map(function (name) {\n  return \"el-icon-\".concat(name);\n});\nvar _default = exports.default = {\n  inheritAttrs: false,\n  props: ['current'],\n  data: function data() {\n    return {\n      iconList: originList,\n      active: null,\n      key: ''\n    };\n  },\n  watch: {\n    key: function key(val) {\n      if (val) {\n        this.iconList = originList.filter(function (name) {\n          return name.indexOf(val) > -1;\n        });\n      } else {\n        this.iconList = originList;\n      }\n    }\n  },\n  methods: {\n    onOpen: function onOpen() {\n      this.active = this.current;\n      this.key = '';\n    },\n    onClose: function onClose() {},\n    onSelect: function onSelect(icon) {\n      this.active = icon;\n      this.$emit('select', icon);\n      this.$emit('update:visible', false);\n    }\n  }\n};", null]}