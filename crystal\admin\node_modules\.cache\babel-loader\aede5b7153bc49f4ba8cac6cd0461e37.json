{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\element-ui\\node_modules\\async-validator\\es\\util.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\element-ui\\node_modules\\async-validator\\es\\util.js", "mtime": 1753666300696}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.asyncMap = asyncMap;\nexports.complementError = complementError;\nexports.deepMerge = deepMerge;\nexports.format = format;\nexports.isEmptyObject = isEmptyObject;\nexports.isEmptyValue = isEmptyValue;\nexports.warning = void 0;\nvar _extends2 = _interopRequireDefault(require(\"babel-runtime/helpers/extends\"));\nvar _typeof2 = _interopRequireDefault(require(\"babel-runtime/helpers/typeof\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nvar formatRegExp = /%[sdj%]/g;\nvar warning = exports.warning = function warning() {};\n\n// don't print warning message when in production env or node runtime\nif (process.env.NODE_ENV !== 'production' && typeof window !== 'undefined' && typeof document !== 'undefined') {\n  exports.warning = warning = function warning(type, errors) {\n    if (typeof console !== 'undefined' && console.warn) {\n      if (errors.every(function (e) {\n        return typeof e === 'string';\n      })) {\n        console.warn(type, errors);\n      }\n    }\n  };\n}\nfunction format() {\n  for (var _len = arguments.length, args = Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n  var i = 1;\n  var f = args[0];\n  var len = args.length;\n  if (typeof f === 'function') {\n    return f.apply(null, args.slice(1));\n  }\n  if (typeof f === 'string') {\n    var str = String(f).replace(formatRegExp, function (x) {\n      if (x === '%%') {\n        return '%';\n      }\n      if (i >= len) {\n        return x;\n      }\n      switch (x) {\n        case '%s':\n          return String(args[i++]);\n        case '%d':\n          return Number(args[i++]);\n        case '%j':\n          try {\n            return JSON.stringify(args[i++]);\n          } catch (_) {\n            return '[Circular]';\n          }\n          break;\n        default:\n          return x;\n      }\n    });\n    for (var arg = args[i]; i < len; arg = args[++i]) {\n      str += ' ' + arg;\n    }\n    return str;\n  }\n  return f;\n}\nfunction isNativeStringType(type) {\n  return type === 'string' || type === 'url' || type === 'hex' || type === 'email' || type === 'pattern';\n}\nfunction isEmptyValue(value, type) {\n  if (value === undefined || value === null) {\n    return true;\n  }\n  if (type === 'array' && Array.isArray(value) && !value.length) {\n    return true;\n  }\n  if (isNativeStringType(type) && typeof value === 'string' && !value) {\n    return true;\n  }\n  return false;\n}\nfunction isEmptyObject(obj) {\n  return Object.keys(obj).length === 0;\n}\nfunction asyncParallelArray(arr, func, callback) {\n  var results = [];\n  var total = 0;\n  var arrLength = arr.length;\n  function count(errors) {\n    results.push.apply(results, errors);\n    total++;\n    if (total === arrLength) {\n      callback(results);\n    }\n  }\n  arr.forEach(function (a) {\n    func(a, count);\n  });\n}\nfunction asyncSerialArray(arr, func, callback) {\n  var index = 0;\n  var arrLength = arr.length;\n  function next(errors) {\n    if (errors && errors.length) {\n      callback(errors);\n      return;\n    }\n    var original = index;\n    index = index + 1;\n    if (original < arrLength) {\n      func(arr[original], next);\n    } else {\n      callback([]);\n    }\n  }\n  next([]);\n}\nfunction flattenObjArr(objArr) {\n  var ret = [];\n  Object.keys(objArr).forEach(function (k) {\n    ret.push.apply(ret, objArr[k]);\n  });\n  return ret;\n}\nfunction asyncMap(objArr, option, func, callback) {\n  if (option.first) {\n    var flattenArr = flattenObjArr(objArr);\n    return asyncSerialArray(flattenArr, func, callback);\n  }\n  var firstFields = option.firstFields || [];\n  if (firstFields === true) {\n    firstFields = Object.keys(objArr);\n  }\n  var objArrKeys = Object.keys(objArr);\n  var objArrLength = objArrKeys.length;\n  var total = 0;\n  var results = [];\n  var next = function next(errors) {\n    results.push.apply(results, errors);\n    total++;\n    if (total === objArrLength) {\n      callback(results);\n    }\n  };\n  objArrKeys.forEach(function (key) {\n    var arr = objArr[key];\n    if (firstFields.indexOf(key) !== -1) {\n      asyncSerialArray(arr, func, next);\n    } else {\n      asyncParallelArray(arr, func, next);\n    }\n  });\n}\nfunction complementError(rule) {\n  return function (oe) {\n    if (oe && oe.message) {\n      oe.field = oe.field || rule.fullField;\n      return oe;\n    }\n    return {\n      message: oe,\n      field: oe.field || rule.fullField\n    };\n  };\n}\nfunction deepMerge(target, source) {\n  if (source) {\n    for (var s in source) {\n      if (source.hasOwnProperty(s)) {\n        var value = source[s];\n        if ((typeof value === 'undefined' ? 'undefined' : (0, _typeof2.default)(value)) === 'object' && (0, _typeof2.default)(target[s]) === 'object') {\n          target[s] = (0, _extends2.default)({}, target[s], value);\n        } else {\n          target[s] = value;\n        }\n      }\n    }\n  }\n  return target;\n}", null]}