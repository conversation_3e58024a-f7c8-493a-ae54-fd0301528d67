{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\storeComment\\creatComment.vue?vue&type=template&id=24e2766f&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\storeComment\\creatComment.vue", "mtime": 1753666157925}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"el-form\",\n    {\n      directives: [\n        {\n          name: \"loading\",\n          rawName: \"v-loading\",\n          value: _vm.loading,\n          expression: \"loading\",\n        },\n      ],\n      ref: \"formValidate\",\n      staticClass: \"demo-formValidate\",\n      attrs: {\n        model: _vm.formValidate,\n        rules: _vm.rules,\n        \"label-width\": \"100px\",\n      },\n    },\n    [\n      _c(\"el-form-item\", { attrs: { label: \"商品：\", prop: \"productId\" } }, [\n        _c(\n          \"div\",\n          { staticClass: \"upLoadPicBox\", on: { click: _vm.changeGood } },\n          [\n            _vm.formValidate.productId\n              ? _c(\"div\", { staticClass: \"pictrue\" }, [\n                  _c(\"img\", { attrs: { src: _vm.image } }),\n                ])\n              : _c(\"div\", { staticClass: \"upLoad\" }, [\n                  _c(\"i\", { staticClass: \"el-icon-camera cameraIconfont\" }),\n                ]),\n          ]\n        ),\n      ]),\n      _vm._v(\" \"),\n      _c(\n        \"el-form-item\",\n        { attrs: { label: \"用户名称：\", prop: \"nickname\" } },\n        [\n          _c(\"el-input\", {\n            attrs: { type: \"text\" },\n            model: {\n              value: _vm.formValidate.nickname,\n              callback: function ($$v) {\n                _vm.$set(_vm.formValidate, \"nickname\", $$v)\n              },\n              expression: \"formValidate.nickname \",\n            },\n          }),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"el-form-item\",\n        { attrs: { label: \"评价文字：\", prop: \"comment\" } },\n        [\n          _c(\"el-input\", {\n            attrs: { type: \"textarea\" },\n            model: {\n              value: _vm.formValidate.comment,\n              callback: function ($$v) {\n                _vm.$set(_vm.formValidate, \"comment\", $$v)\n              },\n              expression: \"formValidate.comment \",\n            },\n          }),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"el-form-item\",\n        {\n          staticClass: \"productScore\",\n          attrs: { label: \"商品分数：\", prop: \"productScore\" },\n        },\n        [\n          _c(\"el-rate\", {\n            model: {\n              value: _vm.formValidate.productScore,\n              callback: function ($$v) {\n                _vm.$set(_vm.formValidate, \"productScore\", $$v)\n              },\n              expression: \"formValidate.productScore\",\n            },\n          }),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"el-form-item\",\n        {\n          staticClass: \"productScore\",\n          attrs: { label: \"服务分数：\", prop: \"serviceScore\" },\n        },\n        [\n          _c(\"el-rate\", {\n            model: {\n              value: _vm.formValidate.serviceScore,\n              callback: function ($$v) {\n                _vm.$set(_vm.formValidate, \"serviceScore\", $$v)\n              },\n              expression: \"formValidate.serviceScore\",\n            },\n          }),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\"el-form-item\", { attrs: { label: \"用户头像：\", prop: \"avatar\" } }, [\n        _c(\n          \"div\",\n          {\n            staticClass: \"upLoadPicBox\",\n            on: {\n              click: function ($event) {\n                return _vm.modalPicTap(\"1\")\n              },\n            },\n          },\n          [\n            _vm.formValidate.avatar\n              ? _c(\"div\", { staticClass: \"pictrue\" }, [\n                  _c(\"img\", { attrs: { src: _vm.formValidate.avatar } }),\n                ])\n              : _c(\"div\", { staticClass: \"upLoad\" }, [\n                  _c(\"i\", { staticClass: \"el-icon-camera cameraIconfont\" }),\n                ]),\n          ]\n        ),\n      ]),\n      _vm._v(\" \"),\n      _c(\"el-form-item\", { attrs: { label: \"评价图片：\" } }, [\n        _c(\n          \"div\",\n          { staticClass: \"acea-row\" },\n          [\n            _vm._l(_vm.pics, function (item, index) {\n              return _c(\n                \"div\",\n                {\n                  key: index,\n                  staticClass: \"pictrue\",\n                  attrs: { draggable: \"false\" },\n                  on: {\n                    dragstart: function ($event) {\n                      return _vm.handleDragStart($event, item)\n                    },\n                    dragover: function ($event) {\n                      $event.preventDefault()\n                      return _vm.handleDragOver($event, item)\n                    },\n                    dragenter: function ($event) {\n                      return _vm.handleDragEnter($event, item)\n                    },\n                    dragend: function ($event) {\n                      return _vm.handleDragEnd($event, item)\n                    },\n                  },\n                },\n                [\n                  _c(\"img\", { attrs: { src: item } }),\n                  _vm._v(\" \"),\n                  _c(\"i\", {\n                    staticClass: \"el-icon-error btndel\",\n                    on: {\n                      click: function ($event) {\n                        return _vm.handleRemove(index)\n                      },\n                    },\n                  }),\n                ]\n              )\n            }),\n            _vm._v(\" \"),\n            _vm.pics < 10\n              ? _c(\n                  \"div\",\n                  {\n                    staticClass: \"upLoadPicBox\",\n                    on: {\n                      click: function ($event) {\n                        return _vm.modalPicTap(\"2\")\n                      },\n                    },\n                  },\n                  [\n                    _c(\"div\", { staticClass: \"upLoad\" }, [\n                      _c(\"i\", { staticClass: \"el-icon-camera cameraIconfont\" }),\n                    ]),\n                  ]\n                )\n              : _vm._e(),\n          ],\n          2\n        ),\n      ]),\n      _vm._v(\" \"),\n      _c(\n        \"el-form-item\",\n        [\n          _c(\n            \"el-button\",\n            {\n              attrs: { size: \"mini\", type: \"primary\", loading: _vm.loadingbtn },\n              on: {\n                click: function ($event) {\n                  return _vm.submitForm(\"formValidate\")\n                },\n              },\n            },\n            [_vm._v(\"提交\")]\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-button\",\n            {\n              attrs: { size: \"mini\" },\n              on: {\n                click: function ($event) {\n                  return _vm.resetForm(\"formValidate\")\n                },\n              },\n            },\n            [_vm._v(\"重置\")]\n          ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}