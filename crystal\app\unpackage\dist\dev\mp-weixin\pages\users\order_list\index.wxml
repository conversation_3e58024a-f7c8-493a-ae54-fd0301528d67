<view class="data-v-09744212"><view class="my-order data-v-09744212"><view class="header bg-color data-v-09744212"><view class="picTxt acea-row row-between-wrapper data-v-09744212"><view class="text data-v-09744212"><view class="name data-v-09744212">订单信息</view><view class="data-v-09744212">{{"消费订单："+(orderData.orderCount||0)+" 总消费：￥"+$root.g0+''}}</view></view><view class="pictrue data-v-09744212"><image src="../../../static/images/orderTime.png" class="data-v-09744212"></image></view></view></view><view class="nav acea-row row-around data-v-09744212"><view data-event-opts="{{[['tap',[['statusClick',[0]]]]]}}" class="{{['item','data-v-09744212',orderStatus==0?'on':'']}}" bindtap="__e"><view class="data-v-09744212">待付款</view><view class="num data-v-09744212">{{orderData.unPaidCount||0}}</view></view><view data-event-opts="{{[['tap',[['statusClick',[1]]]]]}}" class="{{['item','data-v-09744212',orderStatus==1?'on':'']}}" bindtap="__e"><view class="data-v-09744212">待发货</view><view class="num data-v-09744212">{{orderData.unShippedCount||0}}</view></view><view data-event-opts="{{[['tap',[['statusClick',[2]]]]]}}" class="{{['item','data-v-09744212',orderStatus==2?'on':'']}}" bindtap="__e"><view class="data-v-09744212">待收货</view><view class="num data-v-09744212">{{orderData.receivedCount||0}}</view></view><view data-event-opts="{{[['tap',[['statusClick',[3]]]]]}}" class="{{['item','data-v-09744212',orderStatus==3?'on':'']}}" bindtap="__e"><view class="data-v-09744212">待评价</view><view class="num data-v-09744212">{{orderData.evaluatedCount||0}}</view></view><view data-event-opts="{{[['tap',[['statusClick',[4]]]]]}}" class="{{['item','data-v-09744212',orderStatus==4?'on':'']}}" bindtap="__e"><view class="data-v-09744212">已完成</view><view class="num data-v-09744212">{{orderData.completeCount||0}}</view></view></view><view class="list data-v-09744212"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item data-v-09744212"><view data-event-opts="{{[['tap',[['goOrderDetails',['$0'],[[['orderList','',index,'orderId']]]]]]]}}" bindtap="__e" class="data-v-09744212"><view class="title acea-row row-between-wrapper data-v-09744212"><view class="acea-row row-middle data-v-09744212"><block wx:if="{{item.$orig.activityType!=='普通'&&item.$orig.activityType!=='核销'}}"><text class="sign cart-color acea-row row-center-wrapper data-v-09744212">{{item.$orig.activityType}}</text></block><view class="data-v-09744212">{{item.$orig.createTime}}</view></view><view class="font-color data-v-09744212">{{item.$orig.orderStatus}}</view></view><block wx:if="{{item.$orig.type==2}}"><view class="data-v-09744212"><view style="{{('display: flex;justify-content: center;align-items: center;height: '+item.$orig.radius*3+'rpx;position: relative;')}}" class="_div data-v-09744212"><view class="circle-container _div data-v-09744212" style="{{'width:'+(item.$orig.radius*2+'rpx')+';'+('height:'+(item.$orig.radius*2+'rpx')+';')}}"><block wx:for="{{item.l0}}" wx:for-item="item1" wx:for-index="index1" wx:key="index1"><view class="marble _div data-v-09744212" style="{{item1.s0}}"></view></block></view></view></view></block><block wx:else><block wx:for="{{item.$orig.orderInfoList}}" wx:for-item="items" wx:for-index="index1" wx:key="index1"><view class="item-info acea-row row-between row-top data-v-09744212"><view class="pictrue data-v-09744212"><image src="{{items.image}}" class="data-v-09744212"></image></view><view class="text acea-row row-between data-v-09744212"><view class="name line2 data-v-09744212">{{items.storeName}}</view><view class="money data-v-09744212"><view class="data-v-09744212">{{"￥"+items.price}}</view><view class="data-v-09744212">{{"x"+items.cartNum}}</view></view></view></view></block></block><view class="totalPrice data-v-09744212">{{"共"+item.$orig.totalNum+'件商品，总金额'}}<text class="money font-color data-v-09744212">{{"￥"+item.$orig.payPrice}}</text></view></view><view class="bottom acea-row row-right row-middle data-v-09744212"><block wx:if="{{!item.$orig.paid}}"><view data-event-opts="{{[['tap',[['cancelOrder',[index,'$0'],[[['orderList','',index,'id']]]]]]]}}" class="bnt cancelBnt data-v-09744212" bindtap="__e">取消订单</view></block><block wx:if="{{!item.$orig.paid}}"><view data-event-opts="{{[['tap',[['goPay',['$0','$1'],[[['orderList','',index,'payPrice']],[['orderList','',index,'orderId']]]]]]]}}" class="bnt bg-color data-v-09744212" bindtap="__e">立即付款</view></block><block wx:else><block wx:if="{{item.$orig.status==0||item.$orig.status==1||item.$orig.status==3}}"><view data-event-opts="{{[['tap',[['goOrderDetails',['$0'],[[['orderList','',index,'orderId']]]]]]]}}" class="bnt bg-color data-v-09744212" bindtap="__e">查看详情</view></block><block wx:else><block wx:if="{{item.$orig.status==2}}"><view data-event-opts="{{[['tap',[['goOrderDetails',['$0'],[[['orderList','',index,'orderId']]]]]]]}}" class="bnt bg-color data-v-09744212" bindtap="__e">去评价</view></block></block></block><block wx:if="{{item.$orig.status==3}}"><view data-event-opts="{{[['tap',[['delOrder',['$0',index],[[['orderList','',index,'id']]]]]]]}}" class="bnt cancelBnt data-v-09744212" bindtap="__e">删除订单</view></block></view></view></block></view><block wx:if="{{$root.g1>0}}"><view class="loadingicon acea-row row-center-wrapper data-v-09744212"><text class="loading iconfont icon-jiazai data-v-09744212" hidden="{{loading==false}}"></text>{{loadTitle+''}}</view></block><block wx:if="{{$root.g2==0}}"><view class="data-v-09744212"><empty-page vue-id="25148134-1" title="暂无订单~" class="data-v-09744212" bind:__l="__l"></empty-page></view></block></view><block wx:if="{{$root.g3}}"><view class="noCart data-v-09744212"><view class="pictrue data-v-09744212"><image src="/images/noOrder.png" class="data-v-09744212"></image></view></view></block><home vue-id="25148134-2" class="data-v-09744212" bind:__l="__l"></home><payment vue-id="25148134-3" payMode="{{payMode}}" pay_close="{{pay_close}}" order_id="{{pay_order_id}}" totalPrice="{{totalPrice}}" data-event-opts="{{[['^onChangeFun',[['onChangeFun']]]]}}" bind:onChangeFun="__e" class="data-v-09744212" bind:__l="__l"></payment></view>