{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\Category\\edit.vue?vue&type=template&id=70f2820a&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\Category\\edit.vue", "mtime": 1753666157755}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    [\n      _c(\n        \"el-form\",\n        {\n          ref: \"editPram\",\n          attrs: { model: _vm.editPram, \"label-width\": \"130px\" },\n        },\n        [\n          _c(\n            \"el-form-item\",\n            {\n              attrs: {\n                label: \"分类名称\",\n                prop: \"name\",\n                rules: [\n                  {\n                    required: true,\n                    message: \"请输入分类名称\",\n                    trigger: [\"blur\", \"change\"],\n                  },\n                ],\n              },\n            },\n            [\n              _c(\"el-input\", {\n                attrs: {\n                  maxlength: _vm.biztype.value === 1 ? 8 : 20,\n                  placeholder: \"分类名称\",\n                },\n                model: {\n                  value: _vm.editPram.name,\n                  callback: function ($$v) {\n                    _vm.$set(_vm.editPram, \"name\", $$v)\n                  },\n                  expression: \"editPram.name\",\n                },\n              }),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _vm.biztype.value !== 1 &&\n          _vm.biztype.value !== 3 &&\n          _vm.biztype.value !== 8\n            ? _c(\n                \"el-form-item\",\n                { attrs: { label: \"URL\" } },\n                [\n                  _c(\"el-input\", {\n                    attrs: { placeholder: \"URL\" },\n                    model: {\n                      value: _vm.editPram.url,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.editPram, \"url\", $$v)\n                      },\n                      expression: \"editPram.url\",\n                    },\n                  }),\n                ],\n                1\n              )\n            : _vm._e(),\n          _vm._v(\" \"),\n          _vm.biztype.value !== 3\n            ? _c(\n                \"el-form-item\",\n                { attrs: { label: \"父级\" } },\n                [\n                  _c(\"el-cascader\", {\n                    staticStyle: { width: \"100%\" },\n                    attrs: {\n                      disabled: _vm.isCreate === 1 && _vm.editPram.pid === 0,\n                      options:\n                        _vm.biztype.value === 5\n                          ? _vm.allTreeList\n                          : _vm.parentOptions,\n                      props: _vm.categoryProps,\n                    },\n                    model: {\n                      value: _vm.editPram.pid,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.editPram, \"pid\", $$v)\n                      },\n                      expression: \"editPram.pid\",\n                    },\n                  }),\n                ],\n                1\n              )\n            : _vm._e(),\n          _vm._v(\" \"),\n          _vm.biztype.value === 5\n            ? _c(\n                \"el-form-item\",\n                { attrs: { label: \"菜单图标\" } },\n                [\n                  _c(\n                    \"el-input\",\n                    {\n                      attrs: { placeholder: \"请选择菜单图标\" },\n                      model: {\n                        value: _vm.editPram.extra,\n                        callback: function ($$v) {\n                          _vm.$set(_vm.editPram, \"extra\", $$v)\n                        },\n                        expression: \"editPram.extra\",\n                      },\n                    },\n                    [\n                      _c(\"el-button\", {\n                        attrs: {\n                          slot: \"append\",\n                          icon: \"el-icon-circle-plus-outline\",\n                        },\n                        on: { click: _vm.addIcon },\n                        slot: \"append\",\n                      }),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              )\n            : _vm._e(),\n          _vm._v(\" \"),\n          _vm.biztype.value === 1 || _vm.biztype.value === 3\n            ? _c(\"el-form-item\", { attrs: { label: \"分类图标(180*180)\" } }, [\n                _c(\n                  \"div\",\n                  {\n                    staticClass: \"upLoadPicBox\",\n                    on: {\n                      click: function ($event) {\n                        return _vm.modalPicTap(\"1\")\n                      },\n                    },\n                  },\n                  [\n                    _vm.editPram.extra\n                      ? _c(\"div\", { staticClass: \"pictrue\" }, [\n                          _c(\"img\", { attrs: { src: _vm.editPram.extra } }),\n                        ])\n                      : _c(\"div\", { staticClass: \"upLoad\" }, [\n                          _c(\"i\", {\n                            staticClass: \"el-icon-camera cameraIconfont\",\n                          }),\n                        ]),\n                  ]\n                ),\n              ])\n            : _vm._e(),\n          _vm._v(\" \"),\n          _c(\n            \"el-form-item\",\n            { attrs: { label: \"排序\" } },\n            [\n              _c(\"el-input-number\", {\n                attrs: { min: 0 },\n                model: {\n                  value: _vm.editPram.sort,\n                  callback: function ($$v) {\n                    _vm.$set(_vm.editPram, \"sort\", $$v)\n                  },\n                  expression: \"editPram.sort\",\n                },\n              }),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-form-item\",\n            { attrs: { label: \"状态\" } },\n            [\n              _c(\"el-switch\", {\n                attrs: {\n                  \"active-text\": \"显示\",\n                  \"inactive-text\": \"隐藏\",\n                  \"active-value\": true,\n                  \"inactive-value\": false,\n                },\n                model: {\n                  value: _vm.editPram.status,\n                  callback: function ($$v) {\n                    _vm.$set(_vm.editPram, \"status\", $$v)\n                  },\n                  expression: \"editPram.status\",\n                },\n              }),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _vm.biztype.value !== 1 &&\n          _vm.biztype.value !== 3 &&\n          _vm.biztype.value !== 5 &&\n          _vm.biztype.value !== 8\n            ? _c(\n                \"el-form-item\",\n                { attrs: { label: \"扩展字段\" } },\n                [\n                  _c(\"el-input\", {\n                    attrs: { type: \"textarea\", placeholder: \"扩展字段\" },\n                    model: {\n                      value: _vm.editPram.extra,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.editPram, \"extra\", $$v)\n                      },\n                      expression: \"editPram.extra\",\n                    },\n                  }),\n                ],\n                1\n              )\n            : _vm._e(),\n          _vm._v(\" \"),\n          _c(\n            \"el-form-item\",\n            [\n              _c(\n                \"el-button\",\n                {\n                  directives: [\n                    {\n                      name: \"hasPermi\",\n                      rawName: \"v-hasPermi\",\n                      value: [\"admin:category:update\"],\n                      expression: \"['admin:category:update']\",\n                    },\n                  ],\n                  attrs: { type: \"primary\", loading: _vm.loadingBtn },\n                  on: {\n                    click: function ($event) {\n                      return _vm.handlerSubmit(\"editPram\")\n                    },\n                  },\n                },\n                [_vm._v(\"确定\")]\n              ),\n              _vm._v(\" \"),\n              _c(\"el-button\", { on: { click: _vm.close } }, [_vm._v(\"取消\")]),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}