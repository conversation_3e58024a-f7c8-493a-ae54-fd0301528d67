<view class="data-v-251d72aa"><view class="order-details data-v-251d72aa"><view class="{{['header','bg-color','data-v-251d72aa',isGoodsReturn?'on':'']}}"><view class="picTxt acea-row row-middle data-v-251d72aa"><block wx:if="{{isGoodsReturn==false}}"><view class="pictrue data-v-251d72aa"><image src="{{orderInfo.statusPic}}" class="data-v-251d72aa"></image></view></block><view class="{{['data','data-v-251d72aa',isGoodsReturn?'on':'']}}"><view class="state data-v-251d72aa">{{orderInfo.orderStatusMsg}}</view><view class="data-v-251d72aa">{{orderInfo.createTime}}</view></view></view></view><block wx:if="{{isGoodsReturn==false}}"><view class="pad30 data-v-251d72aa"><view class="nav data-v-251d72aa"><view class="navCon acea-row row-between-wrapper data-v-251d72aa"><view class="{{['data-v-251d72aa',!orderInfo.paid?'on':'']}}">待付款</view><view class="{{['data-v-251d72aa',orderInfo.paid&&orderInfo.status==0?'on':'']}}">{{''+(orderInfo.shippingType==1?'待发货':'待核销')}}</view><block wx:if="{{orderInfo.shippingType==1}}"><view class="{{['data-v-251d72aa',orderInfo.status==1?'on':'']}}">待收货</view></block><view class="{{['data-v-251d72aa',orderInfo.status==2?'on':'']}}">待评价</view><view class="{{['data-v-251d72aa',orderInfo.status==3?'on':'']}}">已完成</view></view><view class="progress acea-row row-between-wrapper data-v-251d72aa"><view class="{{['iconfont','data-v-251d72aa',(!orderInfo.paid?'icon-webicon318':'icon-yuandianxiao')+' '+(orderInfo.paid?'font-color':'')]}}"></view><view class="{{['line','data-v-251d72aa',orderInfo.paid>0?'bg-color':'']}}"></view><view class="{{['iconfont','data-v-251d72aa',(orderInfo.status==0?'icon-webicon318':'icon-yuandianxiao')+' '+(orderInfo.status>=0?'font-color':'')]}}"></view><block wx:if="{{orderInfo.shippingType==1}}"><view class="{{['line','data-v-251d72aa',orderInfo.status>0?'bg-color':'']}}"></view></block><block wx:if="{{orderInfo.shippingType==1}}"><view class="{{['iconfont','data-v-251d72aa',(orderInfo.status==1?'icon-webicon318':'icon-yuandianxiao')+' '+(orderInfo.status>=1?'font-color':'')]}}"></view></block><view class="{{['line','data-v-251d72aa',orderInfo.status>1?'bg-color':'']}}"></view><view class="{{['iconfont','data-v-251d72aa',(orderInfo.status==2?'icon-webicon318':'icon-yuandianxiao')+' '+(orderInfo.status>=2?'font-color':'')]}}"></view><view class="{{['line','data-v-251d72aa',orderInfo.status>2?'bg-color':'']}}"></view><view class="{{['iconfont','data-v-251d72aa',(orderInfo.status==3?'icon-webicon318':'icon-yuandianxiao')+' '+(orderInfo.status>=3?'font-color':'')]}}"></view></view></view><block wx:if="{{orderInfo.shippingType==2&&orderInfo.paid}}"><view class="writeOff borRadius14 data-v-251d72aa"><view class="title data-v-251d72aa">核销信息</view><view class="grayBg data-v-251d72aa"><view class="pictrue data-v-251d72aa"><image src="{{codeImg}}" class="data-v-251d72aa"></image></view></view><view class="gear data-v-251d72aa"><image src="../../static/images/writeOff.jpg" class="data-v-251d72aa"></image></view><view class="num data-v-251d72aa">{{orderInfo.verifyCode}}</view><block wx:if="{{orderInfo.systemStore}}"><view class="rules data-v-251d72aa"><view class="item data-v-251d72aa"><view class="rulesTitle acea-row row-middle data-v-251d72aa"><text class="iconfont icon-shijian data-v-251d72aa"></text>核销时间</view><view class="info data-v-251d72aa">每日：<text class="time data-v-251d72aa">{{$root.g0}}</text></view></view><view class="item data-v-251d72aa"><view class="rulesTitle acea-row row-middle data-v-251d72aa"><text class="iconfont icon-shuoming1 data-v-251d72aa"></text>使用说明</view><view class="info data-v-251d72aa">可将二维码出示给店员扫描或提供数字核销码</view></view></view></block></view></block><block wx:if="{{orderInfo.shippingType==2}}"><view class="map acea-row row-between-wrapper borRadius14 data-v-251d72aa"><view class="data-v-251d72aa">自提地址信息</view><view data-event-opts="{{[['tap',[['showMaoLocation',['$event']]]]]}}" class="place cart-color acea-row row-center-wrapper data-v-251d72aa" bindtap="__e"><text class="iconfont icon-weizhi data-v-251d72aa"></text>查看位置</view></view></block><block wx:if="{{orderInfo.shippingType===1}}"><view class="address borRadius14 data-v-251d72aa"><view class="name data-v-251d72aa">{{orderInfo.realName}}<text class="phone data-v-251d72aa">{{orderInfo.userPhone}}</text></view><view class="data-v-251d72aa">{{orderInfo.userAddress}}</view></view></block><block wx:else><view class="address data-v-251d72aa" style="margin-top:15rpx;"><view data-event-opts="{{[['tap',[['makePhone',['$event']]]]]}}" class="name data-v-251d72aa" bindtap="__e">{{orderInfo.systemStore?orderInfo.systemStore.name:''}}<text class="phone data-v-251d72aa">{{orderInfo.systemStore?orderInfo.systemStore.phone:''}}</text><text class="iconfont icon-tonghua font-color data-v-251d72aa"></text></view><view class="data-v-251d72aa">{{orderInfo.systemStore?orderInfo.systemStore.detailedAddress:''}}</view></view></block><order-goods vue-id="7e8fe08a-1" evaluate="{{evaluate}}" productType="{{orderInfo.type}}" orderId="{{order_id}}" ids="{{id}}" uniId="{{uniId}}" cartInfo="{{cartInfo}}" jump="{{true}}" type="{{orderInfo.type}}" class="data-v-251d72aa" bind:__l="__l"></order-goods><view class="goodCall borRadius14 _div data-v-251d72aa"><button open-type="contact" hover-class="none" class="data-v-251d72aa"><label class="iconfont icon-kefu _span data-v-251d72aa"></label><label class="_span data-v-251d72aa">联系客服</label></button></view></view></block><view class="pad30 data-v-251d72aa"><block wx:if="{{orderInfo.refundStatus>0}}"><view class="nav refund data-v-251d72aa"><view class="title data-v-251d72aa"><image src="/static/images/shuoming.png" mode class="data-v-251d72aa"></image>{{''+(orderInfo.refundStatus==1?'商家审核中':orderInfo.refundStatus==2?'商家已退款':'商家拒绝退款')+''}}</view><view class="con pad30 data-v-251d72aa">{{orderInfo.refundStatus==1?"您已成功发起退款申请，请耐心等待商家处理；退款前请与商家协商一致，有助于更好的处理售后问题":orderInfo.refundStatus==2?"退款已成功受理，如商家已寄出商品请尽快退回；感谢您的支持":"拒绝原因："+orderInfo.refundReason}}</view></view></block><view class="wrapper borRadius14 data-v-251d72aa"><view class="item acea-row row-between data-v-251d72aa"><view class="data-v-251d72aa">订单编号：</view><view class="conter acea-row row-middle row-right data-v-251d72aa">{{orderInfo.orderId+''}}<text data-event-opts="{{[['tap',[['copy',['$event']]]]]}}" class="copy data-v-251d72aa" bindtap="__e">复制</text></view></view><view class="item acea-row row-between data-v-251d72aa"><view class="data-v-251d72aa">下单时间：</view><view class="conter data-v-251d72aa">{{orderInfo.createTime||0}}</view></view><view class="item acea-row row-between data-v-251d72aa"><view class="data-v-251d72aa">支付状态：</view><block wx:if="{{orderInfo.paid}}"><view class="conter data-v-251d72aa">已支付</view></block><block wx:else><view class="conter data-v-251d72aa">未支付</view></block></view><view class="item acea-row row-between data-v-251d72aa"><view class="data-v-251d72aa">支付方式：</view><view class="conter data-v-251d72aa">{{orderInfo.payTypeStr}}</view></view><block wx:if="{{orderInfo.mark}}"><view class="item acea-row row-between data-v-251d72aa"><view class="data-v-251d72aa">买家留言：</view><view class="conter data-v-251d72aa">{{orderInfo.mark}}</view></view></block></view><block wx:if="{{isGoodsReturn}}"><view class="wrapper borRadius14 data-v-251d72aa"><view class="item acea-row row-between data-v-251d72aa"><view class="data-v-251d72aa">收货人：</view><view class="conter data-v-251d72aa">{{orderInfo.realName}}</view></view><view class="item acea-row row-between data-v-251d72aa"><view class="data-v-251d72aa">联系电话：</view><view class="conter data-v-251d72aa">{{orderInfo.userPhone}}</view></view><view class="item acea-row row-between data-v-251d72aa"><view class="data-v-251d72aa">收货地址：</view><view class="conter data-v-251d72aa">{{orderInfo.userAddress}}</view></view></view></block><block wx:if="{{orderInfo.status>0}}"><view class="data-v-251d72aa"><block wx:if="{{orderInfo.deliveryType=='express'}}"><view class="wrapper borRadius14 data-v-251d72aa"><view class="item acea-row row-between data-v-251d72aa"><view class="data-v-251d72aa">配送方式：</view><view class="conter data-v-251d72aa">发货</view></view><view class="item acea-row row-between data-v-251d72aa"><view class="data-v-251d72aa">快递公司：</view><view class="conter data-v-251d72aa">{{orderInfo.deliveryName||''}}</view></view><view class="item acea-row row-between data-v-251d72aa"><view class="data-v-251d72aa">快递号：</view><view class="conter data-v-251d72aa">{{orderInfo.deliveryId||''}}</view></view></view></block><block wx:else><block wx:if="{{orderInfo.deliveryType=='send'}}"><view class="wrapper borRadius14 data-v-251d72aa"><view class="item acea-row row-between data-v-251d72aa"><view class="data-v-251d72aa">配送方式：</view><view class="conter data-v-251d72aa">送货</view></view><view class="item acea-row row-between data-v-251d72aa"><view class="data-v-251d72aa">配送人姓名：</view><view class="conter data-v-251d72aa">{{orderInfo.deliveryName||''}}</view></view><view class="item acea-row row-between data-v-251d72aa"><view class="data-v-251d72aa">联系电话：</view><view class="conter acea-row row-middle row-right data-v-251d72aa">{{orderInfo.deliveryId||''}}<text data-event-opts="{{[['tap',[['goTel',['$event']]]]]}}" class="copy data-v-251d72aa" bindtap="__e">拨打</text></view></view></view></block><block wx:else><block wx:if="{{orderInfo.deliveryType=='fictitious'}}"><view class="wrapper borRadius14 data-v-251d72aa"><view class="item acea-row row-between data-v-251d72aa"><view class="data-v-251d72aa">虚拟发货：</view><view class="conter data-v-251d72aa">已发货，请注意查收</view></view></view></block></block></block></view></block><view class="wrapper borRadius14 data-v-251d72aa"><view class="item acea-row row-between data-v-251d72aa"><view class="data-v-251d72aa">商品总价：</view><view class="conter data-v-251d72aa">{{"￥"+orderInfo.proTotalPrice}}</view></view><block wx:if="{{orderInfo.payPostage>0}}"><view class="item acea-row row-between data-v-251d72aa"><view class="data-v-251d72aa">运费：</view><view class="conter data-v-251d72aa">{{"￥"+orderInfo.payPostage}}</view></view></block><block wx:if="{{orderInfo.couponId}}"><view class="item acea-row row-between data-v-251d72aa"><view class="data-v-251d72aa">优惠券抵扣：</view><view class="conter data-v-251d72aa">{{"-￥"+orderInfo.couponPrice}}</view></view></block><block wx:if="{{orderInfo.useIntegral>0}}"><view class="item acea-row row-between data-v-251d72aa"><view class="data-v-251d72aa">积分抵扣：</view><view class="conter data-v-251d72aa">{{"-￥"+orderInfo.deductionPrice}}</view></view></block><view class="actualPay acea-row row-right data-v-251d72aa">实付款：<text class="money font-color data-v-251d72aa">{{"￥"+orderInfo.payPrice}}</text></view></view><view style="height:120rpx;" class="data-v-251d72aa"></view><block wx:if="{{isGoodsReturn==false}}"><view class="footer acea-row row-right row-middle data-v-251d72aa"><block wx:if="{{!orderInfo.paid}}"><view data-event-opts="{{[['tap',[['cancelOrder',['$event']]]]]}}" class="qs-btn data-v-251d72aa" catchtap="__e">取消订单</view></block><block wx:if="{{!orderInfo.paid}}"><view data-event-opts="{{[['tap',[['pay_open',['$0'],['orderInfo.orderId']]]]]}}" class="bnt bg-color data-v-251d72aa" bindtap="__e">立即付款</view></block><block wx:else><block wx:if="{{orderInfo.paid===true&&orderInfo.refundStatus===0&&orderInfo.type!==1&&type==='normal'}}"><view data-event-opts="{{[['tap',[['openSubcribe',['/pages/users/goods_return/index?orderId='+orderInfo.orderId]]]]]}}" class="bnt cancel data-v-251d72aa" bindtap="__e">申请退款</view></block></block><block wx:if="{{orderInfo.combinationId>0}}"><view data-event-opts="{{[['tap',[['goJoinPink',['$event']]]]]}}" class="bnt bg-color data-v-251d72aa" bindtap="__e">查看拼团</view></block><block wx:if="{{orderInfo.deliveryType=='express'&&orderInfo.status>0}}"><navigator class="bnt cancel data-v-251d72aa" hover-class="none" url="{{'/pages/users/goods_logistics/index?orderId='+orderInfo.orderId}}">查看物流</navigator></block><block wx:if="{{orderInfo.status==1}}"><view data-event-opts="{{[['tap',[['confirmOrder',['$event']]]]]}}" class="bnt bg-color data-v-251d72aa" bindtap="__e">确认收货</view></block><block wx:if="{{orderInfo.status==3}}"><view data-event-opts="{{[['tap',[['delOrder',['$event']]]]]}}" class="bnt cancel data-v-251d72aa" bindtap="__e">删除订单</view></block><block wx:if="{{orderInfo.status==3&&orderInfo.type!==1}}"><view data-event-opts="{{[['tap',[['goOrderConfirm',['$event']]]]]}}" class="bnt bg-color data-v-251d72aa" bindtap="__e">再次购买</view></block></view></block></view></view><home vue-id="7e8fe08a-2" class="data-v-251d72aa" bind:__l="__l"></home><payment vue-id="7e8fe08a-3" payMode="{{payMode}}" pay_close="{{pay_close}}" order_id="{{pay_order_id}}" totalPrice="{{totalPrice}}" data-event-opts="{{[['^onChangeFun',[['onChangeFun']]]]}}" bind:onChangeFun="__e" class="data-v-251d72aa" bind:__l="__l"></payment></view>