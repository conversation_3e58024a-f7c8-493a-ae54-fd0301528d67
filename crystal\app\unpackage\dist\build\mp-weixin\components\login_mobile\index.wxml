<block wx:if="{{isUp}}"><view class="data-v-fd9fc3a4"><block wx:if="{{isShow}}"><view data-event-opts="{{[['tap',[['close',['$event']]]]]}}" class="mobile-bg data-v-fd9fc3a4" bindtap="__e"></view></block><view class="{{['mobile-mask','animated','data-v-fd9fc3a4',(isUp)?'slideInUp':'']}}" style="{{'position:'+(isPos?'fixed':'static')+';'}}"><view class="input-item data-v-fd9fc3a4"><input type="text" placeholder="输入手机号" data-event-opts="{{[['input',[['__set_model',['','account','$event',[]]]]]]}}" value="{{account}}" bindinput="__e" class="data-v-fd9fc3a4"/></view><view class="input-item data-v-fd9fc3a4"><input type="text" placeholder="输入验证码" data-event-opts="{{[['input',[['__set_model',['','codeNum','$event',[]]]]]]}}" value="{{codeNum}}" bindinput="__e" class="data-v-fd9fc3a4"/><button class="code data-v-fd9fc3a4" disabled="{{disabled}}" data-event-opts="{{[['tap',[['code',['$event']]]]]}}" bindtap="__e">{{text}}</button></view><view data-event-opts="{{[['tap',[['loginBtn',['$event']]]]]}}" class="sub_btn data-v-fd9fc3a4" bindtap="__e">{{!userInfo.phone&&isLogin||userInfo.phone&&isLogin?'立即绑定':'立即登录'}}</view></view></view></block>