{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/poster-poster/index.vue?d91d", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/poster-poster/index.vue?b7f2", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/poster-poster/index.vue?8e70", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/poster-poster/index.vue?7e23", "uni-app:///pages/activity/poster-poster/index.vue", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/poster-poster/index.vue?4797", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/activity/poster-poster/index.vue?0287"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "parameter", "type", "id", "image", "from", "storeCombination", "qrcodeSize", "posterbackgd", "PromotionCode", "canvasStatus", "imgTop", "onLoad", "uni", "title", "icon", "tab", "url", "onShow", "methods", "getPosterInfo", "pinkId", "getCombinationPink", "then", "catch", "success", "fail", "getImageBase64", "that", "make", "uQRCode", "canvasId", "text", "size", "margin", "setTimeout", "complete", "Poster<PERSON><PERSON><PERSON>", "mask", "context", "src", "topText", "bottomText", "destWidth", "destHeight", "fileType", "showImage", "urls", "longPressActions", "itemList", "console"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACmM;AACnM,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAkwB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACgBtxB;AACA;AACA;;;;;;;;;;;;;;;;eACA;EACAC;IACA;MACAC;QACA;QACA;QACA;QACA;QACA;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;IACA;EACA;EACAC;IAEA;IAKA;IACA;MACA;MACA;MACA;QACAC;UACAC;QACA;MACA;QACAD;UACAC;QACA;MACA;IACA;MACA;QACAA;QACAC;MACA;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;QAAAH;MACA;QACAI;QACAhB;MACA;MACA,qBAEA;QACA;MACA;IACA;IACA;IACAiB;MAAA;MACA;MACA,2CACAC;QACA;QACA;MAIA,GACAC;QACA;UACAV;QACA;QACAD;UACAY;UACAC;YACAb;cACAI;YACA;UACA;QACA;MACA;IACA;IACAU;MACA;MACA;QAAAV;MAAA;QACAW;MACA;IACA;IACA;IACAC;MAAA;MACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAT;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACAU;YACA;UACA;QACA;QACAC,+BACA;QACAV;UACA;YACAZ;UACA;QACA;MACA;IACA;IACA;IACAuB;MACAxB;QACAC;QACAwB;MACA;MACA;MACAC;MACA;MACA1B;QACA2B;QACAf;UACAc;UACAA;UACAA;UACAA;UACA;UACA;UACA;UACA;UACA;UACA;YACAP;YACAS;YACAC;UACA;YACA;cACAD;cACAC;YACA;cACAD;cACAC;YACA;UACA;UACAH;UACAA;UAEAA;UACAA;UACAA;UACAA;UAEAA;UACAA;UACAA;UAEAA;UACAA;UACAA;UAGAA;UACAA;UACAA;UACAA;UAEAA;YACA1B;cACA8B;cACAC;cACAb;cACAc;cACApB;gBACA;gBACAZ;gBACAe;gBACAA;cACA;YACA;UACA;QACA;QACAF;UACAb;UACAe;YACAd;UACA;QACA;MACA;IACA;IACAgC;MACA;MACA;MACAjC;QACAkC;QACAC;UACAC;UACAxB;YACAyB;UACA;UACAxB;YACAwB;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnPA;AAAA;AAAA;AAAA;AAAqlC,CAAgB,s8BAAG,EAAC,C;;;;;;;;;;;ACAzmC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/activity/poster-poster/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/activity/poster-poster/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=315573ee&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/activity/poster-poster/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=315573ee&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view class='poster-poster'>\r\n\t\t\t<view class='tip'><text class='iconfont icon-shuoming'></text>提示：点击图片即可保存至手机相册 </view>\r\n\t\t\t<view class='pictrue' v-if=\"canvasStatus\">\r\n\t\t\t\t<image :src='imagePath'></image>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"canvas\">\r\n\t\t\t\t<canvas style=\"width:750px;height:1130px;\" canvas-id=\"firstCanvas\" id=\"firstCanvas\"></canvas>\r\n\t\t\t\t<canvas canvas-id=\"qrcode\" :style=\"{width: `${qrcodeSize}px`, height: `${qrcodeSize}px`}\" style=\"opacity: 0;\"/>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { getCombinationPink, getCombinationPoster } from '../../../api/activity.js';\r\n\timport uQRCode from '@/js_sdk/Sansnn-uQRCode/uqrcode.js';\r\n\timport { imageBase64 } from \"@/api/public\";\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tparameter: {\r\n\t\t\t\t\t'navbar': '1',\r\n\t\t\t\t\t'return': '1',\r\n\t\t\t\t\t'title': '拼团海报',\r\n\t\t\t\t\t'color': true,\r\n\t\t\t\t\t'class': '0'\r\n\t\t\t\t},\r\n\t\t\t\ttype: 0,\r\n\t\t\t\tid: 0,\r\n\t\t\t\timage: '',\r\n\t\t\t\tfrom:'',\r\n\t\t\t\tstoreCombination: {},\r\n\t\t\t\tqrcodeSize: 600,\r\n\t\t\t\tposterbackgd: '/static/images/canbj.png',\r\n\t\t\t\tPromotionCode: '',//二维码\r\n\t\t\t\tcanvasStatus: false,\r\n\t\t\t\timgTop: '' //商品图base64位\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\t// #ifdef MP\r\n\t\t\tthis.from = 'routine'\r\n\t\t\t// #endif\r\n\t\t\t// #ifdef H5\r\n\t\t\tthis.from = 'wechat'\r\n\t\t\t// #endif\r\n\t\t\tvar that = this;\r\n\t\t\tif (options.hasOwnProperty('type') && options.hasOwnProperty('id')) {\r\n\t\t\t\tthis.type = options.type\r\n\t\t\t\tthis.id = options.id\r\n\t\t\t\tif (options.type == 1) {\r\n\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\ttitle: '砍价海报'\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\ttitle: '拼团海报'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\treturn app.Tips({\r\n\t\t\t\t\ttitle: '参数错误',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t}, {\r\n\t\t\t\t\ttab: 3,\r\n\t\t\t\t\turl: 1\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tthis.getPosterInfo();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetPosterInfo: function() {\r\n\t\t\t\tvar that = this,url = '';\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\tpinkId: parseFloat(that.id),\r\n\t\t\t\t\tfrom: that.from\r\n\t\t\t\t};\r\n\t\t\t\tif (that.type == 1) {\r\n\t\t\t\t\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.getCombinationPink();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t//拼团信息\r\n\t\t\tgetCombinationPink: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tgetCombinationPink(this.id)\r\n\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t   this.storeCombination = res.data;\r\n\t\t\t\t\t   this.getImageBase64(res.data.storeCombination.image);\r\n\t\t\t\t\t   // #ifdef H5\r\n\t\t\t\t\t   that.make(res.data.userInfo.uid);\r\n\t\t\t\t\t   // #endif\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\tthis.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tuni.redirectTo({\r\n\t\t\t\t\t\t\tsuccess(){},\r\n\t\t\t\t\t\t\tfail() {\r\n\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\turl: '/pages/index/index',\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgetImageBase64:function(images){\r\n\t\t\t\tlet that = this;\r\n\t\t\t\timageBase64({url:images}).then(res=>{\r\n\t\t\t\t\tthat.imgTop = res.data.code\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 生成二维码；\r\n\t\t\tmake(uid) {\r\n\t\t\t\tlet href = location.protocol + '//' + window.location.host + '/pages/activity/goods_combination_status/index?id=' + this.id + \"&spread=\" + uid;\r\n\t\t\t\tuQRCode.make({\r\n\t\t\t\t\tcanvasId: 'qrcode',\r\n\t\t\t\t\ttext: href,\r\n\t\t\t\t\tsize: this.qrcodeSize,\r\n\t\t\t\t\tmargin: 10,\r\n\t\t\t\t\tsuccess: res => {\r\n\t\t\t\t\t\tthis.PromotionCode = res;\r\n\t\t\t\t\t\tlet arrImages = [this.posterbackgd, this.imgTop, this.PromotionCode];\r\n\t\t\t\t\t\tlet storeName = this.storeCombination.storeCombination.title;\r\n\t\t\t\t\t\tlet price = this.storeCombination.storeCombination.price;\r\n\t\t\t\t\t\tlet people = this.storeCombination.storeCombination.people;\r\n\t\t\t\t\t\tlet otPrice = this.storeCombination.storeCombination.otPrice;\r\n\t\t\t\t\t\tlet count = this.storeCombination.count;\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tthis.PosterCanvas(arrImages, storeName, price, people,otPrice,count);\r\n\t\t\t\t\t\t}, 300);\r\n\t\t\t\t\t},\r\n\t\t\t\t\tcomplete: () => {\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail:res=>{\r\n\t\t\t\t\t\tthis.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: '海报二维码生成失败！'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 生成海报\r\n\t\t\tPosterCanvas:function(arrImages, storeName, price, people,otPrice,count){\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '海报生成中',\r\n\t\t\t\t\tmask: true\r\n\t\t\t\t});\r\n\t\t\t\tlet context = uni.createCanvasContext('firstCanvas')\r\n\t\t\t\tcontext.clearRect(0, 0, 0, 0);\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tuni.getImageInfo({\r\n\t\t\t\t            src: arrImages[0],\r\n\t\t\t\t            success: function (image) {\r\n\t\t\t\t\t\t\t\tcontext.drawImage(arrImages[0], 0, 0, 750, 1190);\r\n\t\t\t\t\t\t\t\tcontext.setFontSize(36);\r\n\t\t\t\t\t\t\t\tcontext.setTextAlign('center');\r\n\t\t\t\t\t\t\t\tcontext.setFillStyle('#282828');\r\n\t\t\t\t\t\t\t\tlet maxText = 20;\r\n\t\t\t\t\t\t\t\tlet text = storeName;\r\n\t\t\t\t\t\t\t\tlet topText = '';\r\n\t\t\t\t\t\t\t\tlet bottomText = '';\r\n\t\t\t\t\t\t\t\tlet len = text.length;\r\n\t\t\t\t\t\t\t\tif(len>maxText*2){\r\n\t\t\t\t\t\t\t\t\ttext = text.slice(0,maxText*2-4)+'......';\r\n\t\t\t\t\t\t\t\t\ttopText = text.slice(0,maxText-1);\r\n\t\t\t\t\t\t\t\t\tbottomText = text.slice(maxText-1,len);\r\n\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\tif(len>maxText){\r\n\t\t\t\t\t\t\t\t\t\ttopText = text.slice(0,maxText-1);\r\n\t\t\t\t\t\t\t\t\t\tbottomText = text.slice(maxText-1,len);\r\n\t\t\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\t\t\ttopText = text;\r\n\t\t\t\t\t\t\t\t\t\tbottomText = '';\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\tcontext.fillText(topText, 750/2, 60);\r\n\t\t\t\t\t\t\t\tcontext.fillText(bottomText, 750/2, 100);\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\tcontext.drawImage(arrImages[1], 150, 350, 450, 450);\r\n\t\t\t\t\t\t\t\tcontext.save();\r\n\t\t\t\t\t\t\t\tcontext.drawImage(arrImages[2], 300, 950, 140, 140);\r\n\t\t\t\t\t\t\t\tcontext.restore();\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\tcontext.setFontSize(72);\r\n\t\t\t\t\t\t\t\tcontext.setFillStyle('#fc4141');\r\n\t\t\t\t\t\t\t\tcontext.fillText(price, 250, 210);\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\tcontext.setFontSize(32);\r\n\t\t\t\t\t\t\t\tcontext.setFillStyle('#FFFFFF');\r\n\t\t\t\t\t\t\t\tcontext.fillText( people+'人团', 538, 198);\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\tcontext.setFontSize(26);\r\n\t\t\t\t\t\t\t\tcontext.setFillStyle('#3F3F3F');\r\n\t\t\t\t\t\t\t\tcontext.setTextAlign('center');\r\n\t\t\t\t\t\t\t\tcontext.fillText( '原价：￥'+otPrice +'   还差 ' + count + '人 拼团成功', 750 / 2, 275);\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\tcontext.draw(true,function(){\r\n\t\t\t\t\t\t\t\t\tuni.canvasToTempFilePath({\r\n\t\t\t\t\t\t\t\t\t  destWidth: 750,\r\n\t\t\t\t\t\t\t\t\t  destHeight: 1190,\r\n\t\t\t\t\t\t\t\t\t  canvasId: 'firstCanvas',\r\n\t\t\t\t\t\t\t\t\t  fileType: 'jpg',\r\n\t\t\t\t\t\t\t\t\t  success: function(res) {\r\n\t\t\t\t\t\t\t\t\t    // 在H5平台下，tempFilePath 为 base64\r\n\t\t\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\t\t\tthat.imagePath = res.tempFilePath;\r\n\t\t\t\t\t\t\t\t\t\tthat.canvasStatus = true;\r\n\t\t\t\t\t\t\t\t\t  } \r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t            },\r\n\t\t\t\t\t\t\tfail: function(err) {\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\tthat.$util.Tips({\r\n\t\t\t\t\t\t\t\t\ttitle: '无法获取图片信息'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tshowImage: function() {\r\n\t\t\t\tvar that = this;\r\n\t\t\t\tlet imgArr = this.image.split(',')\r\n\t\t\t\tuni.previewImage({\r\n\t\t\t\t\t\turls: imgArr,\r\n\t\t\t\t\t\tlongPressActions: {\r\n\t\t\t\t\t\t\t\titemList: ['发送给朋友', '保存图片', '收藏'],\r\n\t\t\t\t\t\t\t\tsuccess: function(data) {\r\n\t\t\t\t\t\t\t\t\t\tconsole.log('选中了第' + (data.tapIndex + 1) + '个按钮,第' + (data.index + 1) + '张图片');\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tfail: function(err) {\r\n\t\t\t\t\t\t\t\t\t\tconsole.log(err.errMsg);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\tpage {\r\n\t\tbackground-color: #d22516 !important;\r\n\t}\r\n    .canvas {\r\n\t\tposition:fixed;\r\n\t\tz-index: -5;\r\n\t\topacity: 0;\r\n\t}\r\n\t.poster-poster .tip {\r\n\t\theight: 80rpx;\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #e8c787;\r\n\t\ttext-align: center;\r\n\t\tline-height: 80rpx;\r\n\t}\r\n\r\n\t.poster-poster .tip .iconfont {\r\n\t\tfont-size: 36rpx;\r\n\t\tvertical-align: -4rpx;\r\n\t\tmargin-right: 18rpx;\r\n\t}\r\n\r\n\t.poster-poster .pictrue {\r\n\t\twidth: 690rpx;\r\n\t\theight: 1130rpx;\r\n\t\tmargin: 0 auto 50rpx auto;\r\n\t}\r\n\r\n\t.poster-poster .pictrue image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363899438\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}