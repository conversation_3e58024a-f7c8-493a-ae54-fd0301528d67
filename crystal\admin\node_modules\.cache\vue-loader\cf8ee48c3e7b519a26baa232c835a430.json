{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\grade\\index.vue?vue&type=template&id=e55abd20&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\user\\grade\\index.vue", "mtime": 1753666157939}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\n<div class=\"divBox\">\n  <el-card class=\"box-card\">\n    <div slot=\"header\" class=\"clearfix\">\n      <el-button  type=\"primary\" class=\"mr10\" @click=\"add\" size=\"small\" v-hasPermi=\"['admin:system:user:level:save']\">添加用户等级</el-button>\n    </div>\n    <el-table\n      v-loading=\"listLoading\"\n      :data=\"tableData.data\"\n      style=\"width: 100%\"\n      size=\"mini\"\n    >\n      <el-table-column\n        prop=\"id\"\n        label=\"ID\"\n        min-width=\"50\"\n      />\n      <el-table-column label=\"等级图标\" min-width=\"80\">\n        <template slot-scope=\"scope\">\n          <div class=\"demo-image__preview\">\n            <el-image\n              style=\"width: 36px; height: 36px\"\n              :src=\"scope.row.icon\"\n              :preview-src-list=\"[scope.row.icon]\"\n            />\n          </div>\n        </template>\n      </el-table-column>\n      <el-table-column\n        prop=\"name\"\n        label=\"等级名称\"\n        min-width=\"100\"\n      />\n      <el-table-column\n        prop=\"experience\"\n        label=\"经验\"\n        min-width=\"100\"\n      />\n      <el-table-column\n        prop=\"discount\"\n        label=\"享受折扣(%)\"\n        min-width=\"100\"\n      />\n      <el-table-column\n        label=\"状态\"\n        min-width=\"100\"\n      >\n        <template slot-scope=\"scope\" v-if=\"checkPermi(['admin:system:user:level:use'])\">\n          <el-switch\n            v-model=\"scope.row.isShow\"\n            :active-value=\"true\"\n            :inactive-value=\"false\"\n            active-text=\"开启\"\n            inactive-text=\"关闭\"\n            disabled\n            @click.native=\"onchangeIsShow(scope.row)\"\n          />\n        </template>\n      </el-table-column>\n      <el-table-column label=\"操作\" min-width=\"120\" fixed=\"right\" align=\"center\">\n        <template slot-scope=\"scope\">\n          <el-button type=\"text\" size=\"small\" @click=\"edit(scope.row)\" class=\"mr10\" v-hasPermi=\"['admin:system:user:level:update']\">编辑</el-button>\n          <el-button type=\"text\" size=\"small\" @click=\"handleDelete(scope.row.id, scope.$index)\" v-hasPermi=\"['admin:system:user:level:delete']\">删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n  </el-card>\n  <creat-grade ref=\"grades\" :user=\"userInfo\"></creat-grade>\n</div>\n", null]}