(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/bracelets/child/edit"],{

/***/ 599:
/*!*********************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/main.js?{"page":"pages%2Fbracelets%2Fchild%2Fedit"} ***!
  \*********************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _edit = _interopRequireDefault(__webpack_require__(/*! ./pages/bracelets/child/edit.vue */ 600));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_edit.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 600:
/*!************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/bracelets/child/edit.vue ***!
  \************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _edit_vue_vue_type_template_id_098d4cba_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./edit.vue?vue&type=template&id=098d4cba&scoped=true& */ 601);
/* harmony import */ var _edit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./edit.vue?vue&type=script&lang=js& */ 603);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _edit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _edit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _edit_vue_vue_type_style_index_0_id_098d4cba_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./edit.vue?vue&type=style&index=0&id=098d4cba&scoped=true&lang=scss& */ 605);
/* harmony import */ var _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 51);

var renderjs





/* normalize component */

var component = Object(_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _edit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _edit_vue_vue_type_template_id_098d4cba_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _edit_vue_vue_type_template_id_098d4cba_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "098d4cba",
  null,
  false,
  _edit_vue_vue_type_template_id_098d4cba_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/bracelets/child/edit.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 601:
/*!*******************************************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/bracelets/child/edit.vue?vue&type=template&id=098d4cba&scoped=true& ***!
  \*******************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_template_id_098d4cba_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=template&id=098d4cba&scoped=true& */ 602);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_template_id_098d4cba_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_template_id_098d4cba_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_template_id_098d4cba_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_template_id_098d4cba_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 602:
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/bracelets/child/edit.vue?vue&type=template&id=098d4cba&scoped=true& ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    shmilyDragImage: function () {
      return __webpack_require__.e(/*! import() | uni_modules/shmily-drag-image/components/shmily-drag-image/shmily-drag-image */ "uni_modules/shmily-drag-image/components/shmily-drag-image/shmily-drag-image").then(__webpack_require__.bind(null, /*! @/uni_modules/shmily-drag-image/components/shmily-drag-image/shmily-drag-image.vue */ 762))
    },
    ljsDialog: function () {
      return Promise.all(/*! import() | uni_modules/ljs-dialog/components/ljs-dialog/ljs-dialog */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/ljs-dialog/components/ljs-dialog/ljs-dialog")]).then(__webpack_require__.bind(null, /*! @/uni_modules/ljs-dialog/components/ljs-dialog/ljs-dialog.vue */ 769))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var g0 = _vm.productList && _vm.productList.length > 0
  var g1 = _vm.productList && _vm.productList.length > 1
  var g2 = !!_vm.isLogin ? _vm.selectList.length : null
  var g3 = !!_vm.isLogin
    ? _vm.selectList.length > 5 && _vm.uid == _vm.createUserId
    : null
  var g4 = !!_vm.isLogin
    ? _vm.selectList.length > 5 && _vm.uid != _vm.createUserId
    : null
  var l0 = _vm.__map(_vm.selectList, function (item, index) {
    var $orig = _vm.__get_orig(item)
    var s0 = _vm.__get_style([
      _vm.selectList.length < 6
        ? _vm.getMarbleStyleSmall(index)
        : _vm.getMarbleStyle(index),
    ])
    return {
      $orig: $orig,
      s0: s0,
    }
  })
  var g5 = _vm.selectList.length
  if (!_vm._isMounted) {
    _vm.e0 = function ($event) {
      _vm.handVisible = true
    }
    _vm.e1 = function ($event) {
      _vm.handVisible = true
    }
    _vm.e2 = function ($event) {
      _vm.handVisible = true
    }
    _vm.e3 = function ($event) {
      _vm.handVisible = true
    }
    _vm.e4 = function ($event) {
      _vm.handVisible = false
    }
  }
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        g0: g0,
        g1: g1,
        g2: g2,
        g3: g3,
        g4: g4,
        l0: l0,
        g5: g5,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 603:
/*!*************************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/bracelets/child/edit.vue?vue&type=script&lang=js& ***!
  \*************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=script&lang=js& */ 604);
/* harmony import */ var _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 604:
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/bracelets/child/edit.vue?vue&type=script&lang=js& ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _user = __webpack_require__(/*! @/api/user */ 38);
var _user2 = __webpack_require__(/*! @/api/user.js */ 38);
var _store = __webpack_require__(/*! @/api/store.js */ 80);
var _vuex = __webpack_require__(/*! vuex */ 35);
var _login = __webpack_require__(/*! @/libs/login.js */ 33);
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var _default = {
  data: function data() {
    return {
      indexOneProduct: [],
      indexOneProductIndex: 0,
      indexTwoProduct: [],
      indexTwoProductIndex: 0,
      scrollIndex: 0,
      dropdownLeft: 0,
      specLeft: 0,
      scrollIntoViewId: '',
      // 用于 scroll-into-view 的 id
      showDropdown: false,
      // 控制下拉列表显示状态
      isDraggingMarble: false,
      // 新增：用于标识是否正在拖动珠子
      draggingMarbleIndex: null,
      // 新增：记录正在拖动的珠子索引
      isDragging: false,
      dragProduct: null,
      dragPosition: {
        x: 0,
        y: 0
      },
      circleRect: null,
      isInCircleArea: false,
      id: '',
      createUserId: '',
      hand: 0,
      changeFlag: false,
      handVisible: false,
      radius: 100,
      // 圆弧的半径
      realRadius: 0,
      // 圆弧的半径
      productList: [],
      selectList: [],
      selectListChange: [],
      price: 0
    };
  },
  computed: (0, _vuex.mapGetters)(['isLogin', 'chatUrl', 'userInfo', 'uid']),
  onLoad: function onLoad(options) {
    this.id = options.id;

    // 小程序链接进入获取绑定关系id
    setTimeout(function () {
      if (options.spread) {
        app.globalData.spread = options.spread;
        (0, _user.spread)(options.spread).then(function (res) {});
      }
    }, 2000);
    this.getAllCategory();
    this.getInfo();
    // this.generateMarbles();
  },
  onShow: function onShow() {
    this.hand = this.userInfo.hand;
  },
  /**
   * 用户点击右上角分享
   */

  onShareAppMessage: function onShareAppMessage() {
    var that = this;
    return {
      title: '快来看看我自定义的手串吧~',
      // imageUrl:  '',
      path: '/pages/bracelets/child/edit?id=' + that.id + '&spread=' + that.uid
    };
  },
  methods: {
    handleOneTabClick: function handleOneTabClick(index2) {
      this.indexOneProductIndex = index2;
      this.indexOneProduct = this.productList[0].child[index2].productVos;
    },
    handleTwoTabClick: function handleTwoTabClick(index2) {
      this.indexTwoProductIndex = index2;
      this.indexTwoProduct = this.productList[1].child[index2].productVos;
    },
    backOne: function backOne() {
      this.selectList = this.selectListChange.length > 1 ? this.selectListChange[this.selectListChange.length - 2].list : [];
      this.selectListChange.pop();
      this.generateMarbles();
      this.dualPrice();
    },
    reset: function reset() {
      this.selectList = [];
    },
    toBuy: function toBuy() {
      var productIds = this.selectList.map(function (e) {
        return e.productId;
      });
      var productAttrValueIds = this.selectList.map(function (e) {
        return e.productAttrValueId;
      });
      this.$Order.getPreOrder("braceletsBuy", [{
        "productNum": 1
      }], productIds, productAttrValueIds, null);
    },
    fromSubmit: function fromSubmit() {
      var _this = this;
      var that = this;
      if (!that.hand) {
        return that.$util.Tips({
          title: '手围不能为空'
        });
      }
      userEditHand({
        hand: that.hand
      }).then(function (res) {
        that.$util.Tips({
          title: '设置成功',
          icon: 'success'
        }, {
          tab: 3,
          url: 1
        });
        that.handVisible = false;
        _this.$store.dispatch('USERINFO');
      }).catch(function (msg) {
        return that.$util.Tips({
          title: msg || '修改失败'
        }, {
          tab: 3,
          url: 1
        });
      });
    },
    _previewImage: function _previewImage(image) {
      var imgArr = [];
      imgArr.push(image);
      //预览图片
      uni.previewImage({
        urls: imgArr,
        current: imgArr[0]
      });
    },
    dualPrice: function dualPrice() {
      this.price = this.selectList.reduce(function (accumulator, currentValue) {
        return accumulator + parseFloat(currentValue.price);
      }, 0).toFixed(2);
    },
    addProduct: function addProduct(e) {
      // 手围限制：当userInfo.hand <= 0时，珠子的宽度之和不能超过350，否则提示“请先设置手围”，并且不能再添加珠子，当userInfo.hand > 0时，珠子的宽度之和不能超过userInfo.hand的130%，否则提示“手围不够”，并且不能再添加珠子
      if (this.userInfo.hand <= 0) {
        if (this.selectList.map(function (gg) {
          return parseFloat(gg.width);
        }).reduce(function (accumulator, currentValue) {
          return accumulator + currentValue;
        }, 0) + parseFloat(e.width) > 350) {
          return this.$util.Tips({
            title: '珠子手串太大啦，请先设置手围'
          });
        }
      } else {
        if (this.selectList.map(function (gg) {
          return parseFloat(gg.width);
        }).reduce(function (accumulator, currentValue) {
          return accumulator + currentValue;
        }, 0) + parseFloat(e.width) > this.userInfo.hand * 1.5) {
          return this.$util.Tips({
            title: '珠子的手围不能超过您手围的50%'
          });
        }
      }
      // if (this.selectList.length >= 20) {
      // 	return this.$util.Tips({
      // 		title: '最多只能添加20个珠子'
      // 	});
      // }
      e.productId = e.id;
      this.selectList.push(e);
      this.selectListChange.push({
        list: this.deepCopy(this.selectList)
      });
      this.generateMarbles();
      this.dualPrice();
    },
    delProduct: function delProduct(index) {
      this.selectList.splice(index, 1);
      this.selectListChange.push({
        list: this.deepCopy(this.selectList)
      });
      this.generateMarbles();
      this.dualPrice();
    },
    getMarbleStyleSmall: function getMarbleStyleSmall(index) {
      var angle = index * 2 * Math.PI / this.selectList.length;
      var x = this.radius + this.radius * Math.cos(angle);
      var y = this.radius + this.radius * Math.sin(angle);
      var rotation = angle + Math.PI / 2; // 旋转角度，使珠子垂直于原点

      return {
        position: 'absolute',
        left: x - this.selectList[index].width * 3 / 2 + 'rpx',
        top: y - this.selectList[index].height * 3 / 2 + 'rpx',
        width: this.selectList[index].width * 3 + 'rpx',
        height: this.selectList[index].height * 3 + 'rpx',
        transform: "rotate(".concat(rotation, "rad)"),
        background: "url('".concat(this.selectList[index].image, "') no-repeat center"),
        backgroundSize: "cover"
      };
    },
    getMarbleStyle: function getMarbleStyle(index) {
      // Calculate total circumference based on bead sizes
      var totalBeadsWidth = this.selectList.reduce(function (sum, bead) {
        return sum + bead.width * 3;
      }, 0);

      // Add small gaps between beads (5% of average bead size)
      var spacing = totalBeadsWidth / this.selectList.length * 0.05;
      var circumference = totalBeadsWidth + spacing * this.selectList.length;

      // Calculate radius to match circle border
      var dynamicRadius = circumference / (2 * Math.PI);

      // Calculate position on circle for current bead
      var angleOffset = 0;
      for (var i = 0; i < index; i++) {
        var prevBeadWidth = this.selectList[i].width * 3;
        angleOffset += (prevBeadWidth + spacing) / dynamicRadius;
      }
      var currentBeadWidth = this.selectList[index].width * 3;
      var angle = angleOffset + currentBeadWidth / 2 / dynamicRadius;

      // Position bead exactly on circle circumference
      var x = dynamicRadius * (1 + Math.cos(angle));
      var y = dynamicRadius * (1 + Math.sin(angle));
      return {
        position: 'absolute',
        left: x - currentBeadWidth / 2 + 'rpx',
        top: y - this.selectList[index].height * 3 / 2 + 'rpx',
        width: currentBeadWidth + 'rpx',
        height: this.selectList[index].height * 3 + 'rpx',
        transform: "rotate(".concat(angle + Math.PI / 2, "rad)"),
        background: "url('".concat(this.selectList[index].image, "') no-repeat center"),
        backgroundSize: 'cover',
        transition: 'all 0.3s ease'
      };
    },
    generateMarbles: function generateMarbles() {
      var realRoundWidth = this.selectList.map(function (e) {
        return parseFloat(e.width);
      }).reduce(function (accumulator, currentValue) {
        return accumulator + currentValue;
      }, 0);
      this.realRadius = realRoundWidth.toFixed(2);
      if (this.selectList.length < 6) {
        this.radius = 100;
        return;
      }
      var totalBeadsWidth = this.selectList.reduce(function (sum, bead) {
        return sum + parseFloat(bead.width) * 3;
      }, 0);
      var spacing = totalBeadsWidth / this.selectList.length * 0.05;
      var totalCircumference = totalBeadsWidth + spacing * this.selectList.length;

      // Set radius to match circle border
      this.radius = totalCircumference / (2 * Math.PI);
      //   this.realRadius = (totalCircumference / Math.PI).toFixed(2);
    },
    getAllCategory: function getAllCategory() {
      var _this2 = this;
      (0, _store.categoryZhuzi)().then(function (res) {
        _this2.productList = res.data;
        if (_this2.productList && _this2.productList.length > 0 && _this2.productList[0].child) {
          _this2.indexOneProduct = _this2.productList[0].child[0].productVos;
        }
        if (_this2.productList && _this2.productList.length > 1 && _this2.productList[1].child) {
          _this2.indexTwoProduct = _this2.productList[1].child[0].productVos;
        }
      });
    },
    getInfo: function getInfo() {
      var _this3 = this;
      (0, _user2.userBraceletsInfo)({
        id: this.id
      }).then(function (res) {
        _this3.createUserId = res.data.userId;
        _this3.selectList = res.data.userBraceletsItemEntities;
        _this3.generateMarbles();
        _this3.dualPrice();
      });
    },
    userBraceletsSubmit: function userBraceletsSubmit() {
      var productIds = this.selectList.map(function (e) {
        return e.productId;
      });
      var productAttrValueIds = this.selectList.map(function (e) {
        return e.productAttrValueId;
      });
      var userId = this.uid;
      var createUserId = this.createUserId;
      var id = this.id;
      uni.showModal({
        title: '提示',
        content: '确认保存手串',
        success: function success(res) {
          if (res.confirm) {
            uni.showLoading({
              title: '保存中'
            });
            (0, _user2.userBraceletsUpdate)({
              id: id,
              userId: userId,
              createUserId: createUserId,
              productAttrValueIds: productAttrValueIds,
              productIds: productIds
            }).then(function (res) {
              uni.hideLoading();
              uni.showModal({
                title: '提示',
                content: '保存成功',
                showCancel: false,
                success: function success(res) {
                  if (res.confirm) {
                    console.log('用户点击确定');
                  } else if (res.cancel) {
                    console.log('用户点击取消');
                  }
                }
              });
            }).catch(function (res) {
              uni.hideLoading();
              uni.showModal({
                title: '错误信息',
                content: res,
                showCancel: false,
                success: function success(res) {
                  if (res.confirm) {
                    console.log('用户点击确定');
                  } else if (res.cancel) {
                    console.log('用户点击取消');
                  }
                }
              });
            });
          } else if (res.cancel) {
            console.log('用户点击取消');
          }
        }
      });
    },
    toLogin: function toLogin() {
      (0, _login.toLogin)();
    },
    onLongPress: function onLongPress(event, product) {
      var _this4 = this;
      var touch = event.touches[0];

      // 开始拖拽
      this.isDragging = true;
      this.dragProduct = product;
      this.dragPosition = {
        x: touch.clientX - product.width * 3 / 2,
        y: touch.clientY - product.height * 3 / 2
      };

      // 获取圆形容器位置
      var query = uni.createSelectorQuery();
      query.select('.circle-container').boundingClientRect(function (data) {
        _this4.circleRect = data;
      }).exec();
    },
    onTouchMove: function onTouchMove(event) {
      if (!this.isDragging) return;
      var touch = event.touches[0];
      this.dragPosition = {
        x: touch.clientX - this.dragProduct.width * 3 / 2,
        y: touch.clientY - this.dragProduct.height * 3 / 2
      };
    },
    onChange: function onChange(event) {
      if (!this.isDragging || !this.circleRect) return;
      var _event$detail = event.detail,
        x = _event$detail.x,
        y = _event$detail.y;
      var circle = {
        centerX: this.circleRect.left + this.circleRect.width / 2,
        centerY: this.circleRect.top + this.circleRect.height / 2,
        radius: this.radius
      };
      var distance = Math.sqrt(Math.pow(x + this.dragProduct.width * 3 / 2 - circle.centerX, 2) + Math.pow(y + this.dragProduct.height * 3 / 2 - circle.centerY, 2));

      // Update hit detection state
      this.isInCircleArea = Math.abs(distance - circle.radius) <= 50;
    },
    onChange2: function onChange2(event) {
      if (!this.isDragging) return;
      if (this.circleRect) {
        var _event$detail2 = event.detail,
          x = _event$detail2.x,
          y = _event$detail2.y;
        var centerX = this.circleRect.left + this.circleRect.width / 2;
        var centerY = this.circleRect.top + this.circleRect.height / 2;
        var distance = Math.sqrt(Math.pow(x + this.dragProduct.width * 3 / 2 - centerX, 2) + Math.pow(y + this.dragProduct.height * 3 / 2 - centerY, 2));
        this.isInCircleArea = distance <= this.radius; // 增加缓冲区
      }
    },
    onTouchEnd: function onTouchEnd(event) {
      if (!this.isDragging) return;
      if (this.userInfo.hand <= 0) {
        if (this.selectList.map(function (gg) {
          return parseFloat(gg.width);
        }).reduce(function (accumulator, currentValue) {
          return accumulator + currentValue;
        }, 0) + parseFloat(this.dragProduct.width) > 350) {
          // 重置状态
          this.isDragging = false;
          this.dragProduct = null;
          return this.$util.Tips({
            title: '珠子手串太大啦，请先设置手围'
          });
        }
      } else {
        if (this.selectList.map(function (gg) {
          return parseFloat(gg.width);
        }).reduce(function (accumulator, currentValue) {
          return accumulator + currentValue;
        }, 0) + parseFloat(this.dragProduct.width) > this.userInfo.hand * 1.5) {
          // 重置状态
          this.isDragging = false;
          this.dragProduct = null;
          return this.$util.Tips({
            title: '珠子的手围不能超过您手围的50%'
          });
        }
      }
      if (this.selectList.length < 6) {
        var touch = event.changedTouches[0];
        var dropX = touch.clientX - this.dragProduct.width * 3 / 2;
        var dropY = touch.clientY - this.dragProduct.height * 3 / 2;
        if (this.circleRect) {
          var centerX = this.circleRect.left + this.circleRect.width / 2;
          var centerY = this.circleRect.top + this.circleRect.height / 2;
          var distance = Math.sqrt(Math.pow(dropX - centerX, 2) + Math.pow(dropY - centerY, 2));

          // 检查是否在圆形区域内
          if (distance <= this.radius) {
            // 在圆形区域内的处理
            var angle = this.calculateAngle(dropX, dropY);
            var insertIndex = this.calculateInsertIndex(angle);
            this.selectList.splice(insertIndex, 0, this.dragProduct);
            this.selectListChange.push({
              list: this.deepCopy(this.selectList)
            });
            this.generateMarbles();
            this.dualPrice();
          }
        }

        // 重置状态
        this.isDragging = false;
        this.dragProduct = null;
      } else {
        var _touch = event.changedTouches[0];
        var circle = {
          x: this.circleRect.left + this.circleRect.width / 2,
          y: this.circleRect.top + this.circleRect.height / 2
        };

        // Calculate drop angle relative to circle center
        var dropAngle = Math.atan2(_touch.clientY - circle.y, _touch.clientX - circle.x);

        // Calculate optimal position based on bead sizes
        var beadCount = this.selectList.length;
        var totalArc = this.calculateTotalArc();
        var arcPerBead = totalArc / beadCount;

        // Find nearest valid position
        var _insertIndex = Math.round((dropAngle + 2 * Math.PI) % (2 * Math.PI) / arcPerBead);
        _insertIndex = (_insertIndex + beadCount) % beadCount;

        // Insert bead
        this.selectList.splice(_insertIndex, 0, this.dragProduct);
        this.selectListChange.push({
          list: this.deepCopy(this.selectList)
        });
        this.generateMarbles();
        this.dualPrice();
        this.isDragging = false;
        this.dragProduct = null;
      }
    },
    calculateAngle: function calculateAngle(x, y) {
      var centerX = this.circleRect.left + this.circleRect.width / 2;
      var centerY = this.circleRect.top + this.circleRect.height / 2;
      var deltaX = x + this.dragProduct.width * 3 / 2 - centerX;
      var deltaY = y + this.dragProduct.height * 3 / 2 - centerY;
      var angle = Math.atan2(deltaY, deltaX);
      if (angle < 0) angle += 2 * Math.PI;
      return angle;
    },
    calculateInsertIndex: function calculateInsertIndex(angle) {
      if (this.selectList.length === 0) return 0;
      var angleStep = 2 * Math.PI / this.selectList.length;
      return Math.floor(angle / angleStep);
    },
    onMarbleLongPress: function onMarbleLongPress(index, event) {
      var _this5 = this;
      this.isDraggingMarble = true; // 设置拖动状态
      this.draggingMarbleIndex = index; // 记录正在拖动的珠子索引
      this.dragProduct = this.selectList[index]; // 记录当前拖动的珠子

      // 获取圆形容器位置
      var query = uni.createSelectorQuery();
      query.select('.circle-container').boundingClientRect(function (data) {
        _this5.circleRect = data;
      }).exec();
    },
    onMarbleTouchMove: function onMarbleTouchMove(index, event) {
      if (!this.isDraggingMarble) return;
      var touch = event.touches[0];
      this.dragPosition = {
        x: touch.clientX - this.dragProduct.width * 3 / 2,
        y: touch.clientY - this.dragProduct.height * 3 / 2
      };
    },
    // Circle position adjustment handlers  
    onMarbleTouchEnd: function onMarbleTouchEnd(index, event) {
      if (!this.isDraggingMarble) return;
      if (this.selectList.length < 6) {
        var touch = event.changedTouches[0];
        var dropX = touch.clientX;
        var dropY = touch.clientY;

        // 确保珠子在圆环内
        if (this.circleRect) {
          var centerX = this.circleRect.left + this.circleRect.width / 2;
          var centerY = this.circleRect.top + this.circleRect.height / 2;
          var distance = Math.sqrt(Math.pow(dropX - centerX, 2) + Math.pow(dropY - centerY, 2));
          if (distance <= this.radius) {
            // 计算新的插入位置
            var angle = this.calculateAngle(dropX, dropY);
            var newIndex = this.calculateInsertIndex(angle);

            // 确保新索引不与当前索引相同
            if (newIndex !== this.draggingMarbleIndex) {
              // 移动珠子到新的位置
              var movingMarble = this.selectList.splice(this.draggingMarbleIndex, 1)[0];
              this.selectList.splice(newIndex, 0, movingMarble);
              this.selectListChange.push({
                list: this.deepCopy(this.selectList)
              });
            }
          }
        }

        // 重新计算珠子的排列和总金额
        this.generateMarbles(); // 重新生成珠子的排列
        // 重置状态
        this.isDraggingMarble = false;
        this.draggingMarbleIndex = null;
        this.dragProduct = null; // 清空拖动的珠子
      } else {
        var _touch2 = event.changedTouches[0];
        var circle = {
          x: this.circleRect.left + this.circleRect.width / 2,
          y: this.circleRect.top + this.circleRect.height / 2
        };

        // Calculate drop angle relative to circle center
        var dropAngle = Math.atan2(_touch2.clientY - circle.y, _touch2.clientX - circle.x);

        // Calculate optimal position based on bead sizes
        var beadCount = this.selectList.length;
        var totalArc = this.calculateTotalArc();
        var arcPerBead = totalArc / beadCount;

        // Find nearest valid position
        var targetIndex = Math.round((dropAngle + 2 * Math.PI) % (2 * Math.PI) / arcPerBead);
        targetIndex = (targetIndex + beadCount) % beadCount;
        if (targetIndex !== this.draggingMarbleIndex) {
          var bead = this.selectList.splice(this.draggingMarbleIndex, 1)[0];
          this.selectList.splice(targetIndex, 0, bead);
          this.selectListChange.push({
            list: this.deepCopy(this.selectList)
          });
        }
        this.isDraggingMarble = false;
        this.draggingMarbleIndex = null;
        this.dragProduct = null;
        this.generateMarbles();
      }
    },
    calculateTotalArc: function calculateTotalArc() {
      var totalWidth = this.selectList.reduce(function (sum, bead) {
        return sum + bead.width * 3;
      }, 0);
      var spacing = totalWidth / this.selectList.length * 0.05;
      return 2 * Math.PI * (1 + spacing / totalWidth);
    },
    calculateInsertIndexFromAngle: function calculateInsertIndexFromAngle(angle) {
      var normalizedAngle = (angle + 2 * Math.PI) % (2 * Math.PI);
      var beadCount = this.selectList.length;
      if (beadCount === 0) return 0;
      return Math.floor(normalizedAngle * beadCount / (2 * Math.PI));
    },
    deepCopy: function deepCopy(array) {
      return array.map(function (item) {
        return _objectSpread({}, item);
      }); // 创建对象的深拷贝
    },
    toggleDropdown: function toggleDropdown(event, index) {
      var _this6 = this;
      // 从事件对象中获取点击位置的left坐标
      var clickLeft = event.touches[0].clientX;
      this.dropdownLeft = clickLeft; // 存储点击位置的left值
      this.scrollIndex = index;
      this.showDropdown = !this.showDropdown; // 切换下拉列表显示状态
      this.productList.forEach(function (category) {
        if (category.child) {
          category.child.forEach(function (e) {
            if (e.productVos) {
              e.productVos.forEach(function (p) {
                _this6.$set(p, 'showSpecs', false);
              });
            }
          });
        }
      });
    },
    scrollToProduct: function scrollToProduct(index, index1) {
      var _this7 = this;
      // 设置 scrollIntoView 属性为目标 id
      this.scrollIntoViewId = "producttitle-".concat(index).concat(index1);
      this.productList.forEach(function (category) {
        if (category.child) {
          category.child.forEach(function (e) {
            if (e.productVos) {
              e.productVos.forEach(function (p) {
                _this7.$set(p, 'showSpecs', false);
              });
            }
          });
        }
      });
      this.showDropdown = false;
    },
    toggleSpecifications: function toggleSpecifications(product, event) {
      var _this8 = this;
      console.log(product);
      // 关闭其他产品的规格选择
      this.productList.forEach(function (category) {
        if (category.child) {
          category.child.forEach(function (e) {
            if (e.productVos) {
              e.productVos.forEach(function (p) {
                if (p.id !== e.id) {
                  _this8.$set(p, 'showSpecs', false);
                }
              });
            }
          });
        }
      });
      this.showDropdown = false;
      // 从事件对象中获取点击位置的left坐标
      var clickLeft = event.touches[0].clientX;
      this.specLeft = clickLeft; // 存储点击位置的left值
      // 切换当前产品的规格选择显示状态
      this.$set(product, 'showSpecs', !product.showSpecs);
    },
    selectSpecification: function selectSpecification(product, spec) {
      console.log(product);
      var result = JSON.parse(JSON.stringify(product));
      // 更新产品的规格信息
      result.width = spec.width;
      result.height = spec.height;
      result.price = spec.price;
      result.productAttrValueId = spec.id;

      // 关闭规格选择框
      this.$set(product, 'showSpecs', false);

      // 添加到选中列表
      this.addProduct(result);
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 605:
/*!**********************************************************************************************************************************************************!*\
  !*** C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/bracelets/child/edit.vue?vue&type=style&index=0&id=098d4cba&scoped=true&lang=scss& ***!
  \**********************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_style_index_0_id_098d4cba_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./edit.vue?vue&type=style&index=0&id=098d4cba&scoped=true&lang=scss& */ 606);
/* harmony import */ var _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_style_index_0_id_098d4cba_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_style_index_0_id_098d4cba_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_style_index_0_id_098d4cba_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_style_index_0_id_098d4cba_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Program_Files_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_edit_vue_vue_type_style_index_0_id_098d4cba_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 606:
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/bracelets/child/edit.vue?vue&type=style&index=0&id=098d4cba&scoped=true&lang=scss& ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[599,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/bracelets/child/edit.js.map