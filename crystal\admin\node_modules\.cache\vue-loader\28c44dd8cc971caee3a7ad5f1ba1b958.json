{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\storeComment\\creatComment.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\storeComment\\creatComment.vue", "mtime": 1753666157925}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\nimport { replyCreatApi, replyEditApi, replyInfoApi } from '@/api/store'\nimport {Debounce} from '@/utils/validate'\nconst defaultObj= {\n  avatar: '',\n  comment: '',\n  nickname: '',\n  pics: '',\n  productId: '',\n  productScore: null,\n  serviceScore: null,\n  sku: ''\n}\nexport default {\n  name: \"creatComment\",\n  props:{\n    num: {\n      type: Number,\n      required: 0\n    },\n  },\n  data() {\n    var checkProductScore = (rule, value, callback) => {\n      if (!value) {\n        return callback(new Error('商品分数不能为空'));\n      }else{\n        callback();\n      }\n    };\n    var checkServiceScore = (rule, value, callback) => {\n      if (!value) {\n        return callback(new Error('服务分数不能为空'));\n      }else{\n        callback();\n      }\n    };\n    return {\n      loadingbtn: false,\n      loading: false,\n      pics: [],\n      image: '',\n      formValidate: Object.assign({}, defaultObj),\n      rules: {\n        avatar: [\n          {required: true, message: '请选择用户头像', trigger: 'change'},\n        ],\n        productId: [\n          {required: true, message: '请选择商品', trigger: 'change'},\n        ],\n        comment: [\n          {required: true, message: '请填写评价内容', trigger: 'blur'},\n        ],\n        nickname: [\n          {required: true, message: '请填写用户名称', trigger: 'blur'},\n        ],\n        pics: [\n          {required: true, message: '请选择评价图片', trigger: 'change'},\n        ],\n        productScore: [\n          {required: true, validator: checkProductScore, trigger: 'blur'},\n        ],\n        serviceScore: [\n          {required: true, validator: checkServiceScore,  trigger: 'change'},\n        ],\n      }\n    }\n  },\n  watch: {\n    num: {\n      handler: function(val) {\n        this.resetForm('formValidate')\n      },\n      deep: true\n    }\n  },\n  methods: {\n    changeGood(){\n      const _this = this\n      this.$modalGoodList(function(row) {\n        _this.image = row.image\n        _this.formValidate.productId = row.id\n        _this.formValidate.sku = row.attrValue[0].suk\n      })\n    },\n    // 点击商品图\n    modalPicTap (tit) {\n      const _this = this\n      _this.$modalUpload(function(img) {\n        tit==='1' ? _this.formValidate.avatar = img[0].sattDir : img.map((item) => {\n          _this.pics.push( item.sattDir)\n        })\n      },tit, 'store')\n    },\n    handleRemove (i) {\n      this.pics.splice(i, 1)\n    },\n    submitForm:Debounce(function(formName) {\n      this.formValidate.pics = this.pics.length>0 ? JSON.stringify(this.pics) : ''\n      this.$refs[formName].validate((valid) => {\n        if (valid) {\n          this.loadingbtn = true;\n          replyCreatApi(this.formValidate).then(() => {\n            this.$message.success(\"新增成功\")\n            setTimeout(() => {\n              // this.clear();\n              this.$emit('getList');\n            }, 600);\n            this.loadingbtn = false;\n          }).catch(()=>{\n            this.loadingbtn = false;\n          })\n        } else {\n          return false;\n        }\n      });\n    }),\n    resetForm(formName) {\n      this.$refs[formName].resetFields()\n      this.pics=[]\n      this.formValidate.pics=''\n    },\n    info(){\n      this.loading = true\n      replyInfoApi(this.formValidate).then(() => {\n        this.formValidate = res\n        this.loading = false\n      }).catch(() => {\n        this.loading = false\n      })\n    },\n    // 移动\n    handleDragStart (e, item) {\n      this.dragging = item;\n    },\n    handleDragEnd (e, item) {\n      this.dragging = null\n    },\n    handleDragOver (e) {\n      e.dataTransfer.dropEffect = 'move'\n    },\n    handleDragEnter (e, item) {\n      e.dataTransfer.effectAllowed = 'move'\n      if (item === this.dragging) {\n        return\n      }\n      const newItems = [...this.pics]\n      const src = newItems.indexOf(this.dragging)\n      const dst = newItems.indexOf(item)\n      newItems.splice(dst, 0, ...newItems.splice(src, 1))\n      this.pics = newItems;\n    }\n  }\n}\n", null]}