@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.return-list .goodWrapper.data-v-1a17ef08 {
  background-color: #fff;
  margin-top: 20rpx;
  position: relative;
  padding: 0rpx 24rpx;
}
.return-list .goodWrapper .orderNum.data-v-1a17ef08 {
  border-bottom: 1px solid #eee;
  height: 87rpx;
  line-height: 87rpx;
  font-size: 30rpx;
  color: #333333;
}
.return-list .goodWrapper .item.data-v-1a17ef08 {
  border-bottom: 0;
}
.return-list .goodWrapper .totalSum.data-v-1a17ef08 {
  padding: 0 30rpx 32rpx 30rpx;
  text-align: right;
  font-size: 26rpx;
  color: #282828;
}
.return-list .goodWrapper .totalSum .price.data-v-1a17ef08 {
  font-size: 28rpx;
  font-weight: bold;
}
.return-list .goodWrapper .iconfont.data-v-1a17ef08 {
  position: absolute;
  font-size: 109rpx;
  top: 7rpx;
  right: 22rpx;
  color: #ccc;
}
.return-list .goodWrapper .iconfont.powder.data-v-1a17ef08 {
  color: #f8c1bd;
}

