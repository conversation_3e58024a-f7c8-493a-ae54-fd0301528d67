{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\creatStore\\braceletsIndex.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\creatStore\\braceletsIndex.vue", "mtime": 1753666157922}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport Tinymce from '@/components/Tinymce/index'\r\nimport { templateListApi, productCreateApi, categoryApi, productDetailApi, productUpdateApi } from '@/api/store'\r\nimport { marketingSendApi } from '@/api/marketing';\r\nimport { shippingTemplatesList } from '@/api/logistics'\r\nimport { goodDesignList } from \"@/api/systemGroup\";\r\nimport { clearTreeData } from '@/utils/ZBKJIutil'\r\nimport CreatTemplates from '@/views/systemSetting/logistics/shippingTemplates/creatTemplates'\r\nimport Templates from \"../../appSetting/wxAccount/wxTemplate/index\";\r\nimport { Debounce } from '@/utils/validate'\r\nconst defaultObj = {\r\n  image: '',\r\n  sliderImages: [],\r\n  videoLink: '',\r\n  sliderImage: '',\r\n  storeName: '',\r\n  storeInfo: '',\r\n  keyword: '',\r\n  cateIds: [], // 珠子分类id\r\n  cateId: null, // 珠子分类id传值\r\n  unitName: '个',\r\n  width: 0,\r\n  height: 0,\r\n  type: 1,\r\n  sort: 0,\r\n  giveIntegral: 0,\r\n  ficti: 0,\r\n  isShow: false,\r\n  isBenefit: false,\r\n  isNew: false,\r\n  isGood: false,\r\n  isHot: false,\r\n  isBest: false,\r\n  tempId: '',\r\n  attrValue: [{\r\n    image: '',\r\n    price: 0,\r\n    cost: 0,\r\n    otPrice: 0,\r\n    stock: 0,\r\n    // width: 0,\r\n    // height: 0,\r\n    // barCode: '',\r\n    // weight: 0,\r\n    // volume: 0\r\n  }],\r\n  attr: [],\r\n  selectRule: '',\r\n  isSub: false,\r\n  content: '',\r\n  specType: false,\r\n  id: 0,\r\n  couponIds: [],\r\n  coupons: [],\r\n  activity: ['默认', '秒杀', '砍价', '拼团']\r\n}\r\nconst objTitle = {\r\n  price: {\r\n    title: '售价'\r\n  },\r\n  cost: {\r\n    title: '成本价'\r\n  },\r\n  otPrice: {\r\n    title: '原价'\r\n  },\r\n  stock: {\r\n    title: '库存'\r\n  },\r\n  // width: {\r\n  //   title: '宽度'\r\n  // },\r\n  // height: {\r\n  //   title: '高度'\r\n  // },\r\n  // barCode: {\r\n  //   title: '珠子编号'\r\n  // },\r\n  // weight: {\r\n  //   title: '重量（KG）'\r\n  // },\r\n  // volume: {\r\n  //   title: '体积(m³)'\r\n  // }\r\n}\r\nexport default {\r\n  name: 'ProductProductAdd',\r\n  components: { Templates, CreatTemplates, Tinymce },\r\n  data() {\r\n    return {\r\n      isDisabled: this.$route.params.isDisabled === '1' ? true : false,\r\n      activity: { '默认': 'red', '秒杀': 'blue', '砍价': 'green', '拼团': 'yellow' },\r\n      props2: {\r\n        children: 'child',\r\n        label: 'name',\r\n        value: 'id',\r\n        multiple: true,\r\n        emitPath: false\r\n      },\r\n      checkboxGroup: [],\r\n      recommend: [],\r\n      tabs: [],\r\n      fullscreenLoading: false,\r\n      props: { multiple: true },\r\n      active: 0,\r\n      OneattrValue: [Object.assign({}, defaultObj.attrValue[0])], // 单规格\r\n      ManyAttrValue: [Object.assign({}, defaultObj.attrValue[0])], // 多规格\r\n      ruleList: [],\r\n      merCateList: [], // 商户分类筛选\r\n      shippingList: [], // 运费模板\r\n      formThead: Object.assign({}, objTitle),\r\n      formValidate: Object.assign({}, defaultObj),\r\n      formDynamics: {\r\n        ruleName: '',\r\n        ruleValue: []\r\n      },\r\n      tempData: {\r\n        page: 1,\r\n        limit: 9999\r\n      },\r\n      manyTabTit: {},\r\n      manyTabDate: {},\r\n      grid2: {\r\n        xl: 12,\r\n        lg: 12,\r\n        md: 12,\r\n        sm: 24,\r\n        xs: 24\r\n      },\r\n      // 规格数据\r\n      formDynamic: {\r\n        image: '',\r\n        attrsName: '',\r\n        attrsVal: ''\r\n      },\r\n      isBtn: false,\r\n      manyFormValidate: [],\r\n      currentTab: 0,\r\n      isChoice: '',\r\n      grid: {\r\n        xl: 8,\r\n        lg: 8,\r\n        md: 12,\r\n        sm: 24,\r\n        xs: 24\r\n      },\r\n      ruleValidate: {\r\n        storeName: [\r\n          { required: true, message: '请输入珠子名称', trigger: 'blur' }\r\n        ],\r\n        cateIds: [\r\n          { required: true, message: '请选择珠子分类', trigger: 'change', type: 'array', min: '1' }\r\n        ],\r\n        keyword: [\r\n          { required: true, message: '请输入珠子关键字', trigger: 'blur' }\r\n        ],\r\n        width: [\r\n          { required: true, message: '请输入宽度', trigger: 'blur' }\r\n        ],\r\n        height: [\r\n          { required: true, message: '请输入宽度', trigger: 'blur' }\r\n        ],\r\n        storeInfo: [\r\n          { required: true, message: '请输入珠子简介', trigger: 'blur' }\r\n        ],\r\n        tempId: [\r\n          { required: true, message: '请选择运费模板', trigger: 'change' }\r\n        ],\r\n        image: [\r\n          { required: true, message: '请上传珠子图', trigger: 'change' }\r\n        ],\r\n        sliderImages: [\r\n          { required: true, message: '请上传珠子轮播图', type: 'array', trigger: 'change' }\r\n        ],\r\n        specType: [\r\n          { required: true, message: '请选择珠子规格', trigger: 'change' }\r\n        ]\r\n      },\r\n      attrInfo: {},\r\n      tableFrom: {\r\n        page: 1,\r\n        limit: 9999,\r\n        keywords: ''\r\n      },\r\n      tempRoute: {},\r\n      keyNum: 0,\r\n      isAttr: false,\r\n      showAll: false,\r\n      videoLink: \"\",\r\n    }\r\n  },\r\n  computed: {\r\n    attrValue() {\r\n      const obj = Object.assign({}, defaultObj.attrValue[0])\r\n      delete obj.image\r\n      return obj\r\n    },\r\n    oneFormBatch() {\r\n      const obj = [Object.assign({}, defaultObj.attrValue[0])]\r\n      delete obj[0].barCode\r\n      return obj\r\n    }\r\n  },\r\n  watch: {\r\n    'formValidate.attr': {\r\n      handler: function (val) {\r\n        if (this.formValidate.specType && this.isAttr) this.watCh(val) //重要！！！\r\n      },\r\n      immediate: false,\r\n      deep: true\r\n    }\r\n  },\r\n  created() {\r\n    this.tempRoute = Object.assign({}, this.$route)\r\n    if (this.$route.params.id && this.formValidate.specType) {\r\n      this.$watch('formValidate.attr', this.watCh)\r\n    }\r\n  },\r\n  mounted() {\r\n    this.formValidate.sliderImages = []\r\n    if (this.$route.params.id) {\r\n      this.setTagsViewTitle()\r\n      this.getInfo()\r\n    }\r\n    this.getCategorySelect()\r\n    this.getShippingList()\r\n    this.getGoodsType()\r\n  },\r\n  methods: {\r\n    // 校验输入框不能输入0，保留2位小数，库存为正整数\r\n    keyupEvent(key, val, index, num) {\r\n      if (key === 'barCode') return;\r\n      var re = /^\\D*([0-9]\\d*\\.?\\d{0,2})?.*$/;\r\n      switch (num) {\r\n        case 1:\r\n          if (val == 0) {\r\n            this.oneFormBatch[index][key] = key === 'stock' ? 0 : 0.01;\r\n          } else {\r\n            this.oneFormBatch[index][key] =\r\n              key === 'stock'\r\n                ? parseInt(val)\r\n                : this.$set(this.oneFormBatch[index], key, val.toString().replace(re, '$1'));\r\n          }\r\n          break;\r\n        case 2:\r\n          if (val == 0) {\r\n            this.OneattrValue[index][key] = key === 'stock' ? 0 : 0.01;\r\n          } else {\r\n            this.OneattrValue[index][key] =\r\n              key === 'stock'\r\n                ? parseInt(val)\r\n                : this.$set(this.OneattrValue[index], key, val.toString().replace(re, '$1'));\r\n          }\r\n          break;\r\n        default:\r\n          if (val == 0) {\r\n            this.ManyAttrValue[index][key] = key === 'stock' ? 0 : 0.01;\r\n          } else {\r\n            this.ManyAttrValue[index][key] =\r\n              key === 'stock'\r\n                ? parseInt(val)\r\n                : this.$set(this.ManyAttrValue[index], key, val.toString().replace(re, '$1'));\r\n          }\r\n          break;\r\n      }\r\n    },\r\n    handleCloseCoupon(tag) {\r\n      this.isAttr = true\r\n      this.formValidate.coupons.splice(this.formValidate.coupons.indexOf(tag), 1)\r\n      this.formValidate.couponIds.splice(this.formValidate.couponIds.indexOf(tag.id), 1)\r\n    },\r\n    addCoupon() {\r\n      const _this = this\r\n      this.$modalCoupon('wu', this.keyNum += 1, this.formValidate.coupons, function (row) {\r\n        _this.formValidate.couponIds = []\r\n        _this.formValidate.coupons = row\r\n        row.map((item) => {\r\n          _this.formValidate.couponIds.push(item.id)\r\n        })\r\n      }, '')\r\n    },\r\n    setTagsViewTitle() {\r\n      const title = this.isDisabled ? '珠子详情' : '编辑珠子'\r\n      const route = Object.assign({}, this.tempRoute, { title: `${title}-${this.$route.params.id}` })\r\n      this.$store.dispatch('tagsView/updateVisitedView', route)\r\n    },\r\n    onChangeGroup() {\r\n      this.checkboxGroup.includes('isGood') ? this.formValidate.isGood = true : this.formValidate.isGood = false\r\n      this.checkboxGroup.includes('isBenefit') ? this.formValidate.isBenefit = true : this.formValidate.isBenefit = false\r\n      this.checkboxGroup.includes('isBest') ? this.formValidate.isBest = true : this.formValidate.isBest = false\r\n      this.checkboxGroup.includes('isNew') ? this.formValidate.isNew = true : this.formValidate.isNew = false\r\n      this.checkboxGroup.includes('isHot') ? this.formValidate.isHot = true : this.formValidate.isHot = false\r\n    },\r\n    watCh(val) {\r\n      const tmp = {}\r\n      const tmpTab = {}\r\n      this.formValidate.attr.forEach((o, i) => {\r\n        // tmp['value' + i] = { title: o.attrName }\r\n        // tmpTab['value' + i] = ''\r\n        tmp[o.attrName] = { title: o.attrName };\r\n        tmpTab[o.attrName] = '';\r\n      });\r\n      this.ManyAttrValue = this.attrFormat(val);\r\n      this.ManyAttrValue.forEach((val, index) => {\r\n        const key = Object.values(val.attrValue).sort().join('/')\r\n        if (this.attrInfo[key]) this.ManyAttrValue[index] = this.attrInfo[key]\r\n      })\r\n      this.attrInfo = [];\r\n      this.ManyAttrValue.forEach((val) => {\r\n        this.attrInfo[Object.values(val.attrValue).sort().join('/')] = val\r\n      })\r\n      this.manyTabTit = tmp\r\n      this.manyTabDate = tmpTab\r\n      this.formThead = Object.assign({}, this.formThead, tmp)\r\n    },\r\n    attrFormat(arr) {\r\n      let data = []\r\n      const res = []\r\n      var that = this;\r\n      return format(arr)\r\n      function format(arr) {\r\n        console.log(arr)\r\n        if (arr.length > 1) {\r\n          arr.forEach((v, i) => {\r\n            if (i === 0) data = arr[i]['attrValue']\r\n            const tmp = []\r\n            if (!data) return;\r\n            data.forEach(function (vv) {\r\n              arr[i + 1] && arr[i + 1]['attrValue'] && arr[i + 1]['attrValue'].forEach(g => {\r\n                const rep2 = (i !== 0 ? '' : arr[i]['attrName'] + '_') + vv + '$&' + arr[i + 1]['attrName'] + '_' + g\r\n                tmp.push(rep2)\r\n                if (i === (arr.length - 2)) {\r\n                  const rep4 = {\r\n                    image: that.formValidate.image,\r\n                    price: 0,\r\n                    cost: 0,\r\n                    otPrice: 0,\r\n                    stock: 0,\r\n                    // width: 0,\r\n                    // height: 0,\r\n                    // barCode: '',\r\n                    // weight: 0,\r\n                    // volume: 0,\r\n                    brokerage: 0,\r\n                    brokerage_two: 0\r\n                  }\r\n                  rep2.split('$&').forEach((h, k) => {\r\n                    const rep3 = h.split('_');\r\n                    if (!rep4['attrValue']) rep4['attrValue'] = {}\r\n                    rep4['attrValue'][rep3[0]] = rep3.length > 1 ? rep3[1] : ''\r\n                  })\r\n                  for (let attrValueKey in rep4.attrValue) {\r\n                    rep4[attrValueKey] = rep4.attrValue[attrValueKey];\r\n                  }\r\n                  res.push(rep4)\r\n                }\r\n              })\r\n            })\r\n            data = tmp.length ? tmp : []\r\n          })\r\n        } else {\r\n          const dataArr = []\r\n          arr.forEach((v, k) => {\r\n            v['attrValue'].forEach((vv, kk) => {\r\n              let kkvv = vv.length ==3 ? vv.substring(0,1) : vv.substring(0,2);\r\n              dataArr[kk] = v['attrName'] + '_' + vv\r\n              res[kk] = {\r\n                image: that.formValidate.image,\r\n                price: vv.length ==3 ? ('0.0' + kkvv) : ('0.' + kkvv),\r\n                cost: 0,\r\n                otPrice: vv.length ==3 ? ('0.0' + kkvv) : ('0.' + kkvv),\r\n                stock: 999,\r\n                // width: kkvv,\r\n                // height: kkvv,\r\n                // barCode: '',\r\n                // weight: 0,\r\n                // volume: 0,\r\n                brokerage: 0,\r\n                brokerage_two: 0,\r\n                attrValue: { [v['attrName']]: vv }\r\n              }\r\n              // Object.values(res[kk].attrValue).forEach((v, i) => {\r\n              //   res[kk]['value' + i] = v\r\n              // })\r\n              for (let attrValueKey in res[kk].attrValue) {\r\n                res[kk][attrValueKey] = res[kk].attrValue[attrValueKey];\r\n              }\r\n            })\r\n          })\r\n          data.push(dataArr.join('$&'))\r\n        }\r\n        return res\r\n      }\r\n    },\r\n    // 运费模板\r\n    addTem() {\r\n      this.$refs.addTemplates.dialogVisible = true\r\n      this.$refs.addTemplates.getCityList()\r\n    },\r\n    // 添加规则；\r\n    addRule() {\r\n      const _this = this\r\n      this.$modalAttr(this.formDynamics, function () {\r\n        _this.productGetRule()\r\n      })\r\n    },\r\n    // 选择规格\r\n    onChangeSpec(num) {\r\n      this.isAttr = true;\r\n      if (num) this.productGetRule()\r\n    },\r\n    // 选择属性确认\r\n    confirm() {\r\n      this.isAttr = true\r\n      if (!this.formValidate.selectRule) {\r\n        return this.$message.warning('请选择属性')\r\n      }\r\n      const data = []\r\n      this.ruleList.forEach(item => {\r\n        if (item.id === this.formValidate.selectRule) {\r\n          item.ruleValue.forEach(i => {\r\n            data.push({\r\n              attrName: i.value,\r\n              attrValue: i.detail\r\n            })\r\n          })\r\n        }\r\n        this.formValidate.attr = data;\r\n      });\r\n    },\r\n    // 珠子分类；\r\n    getCategorySelect() {\r\n      categoryApi({ status: -1, type: 8 }).then(res => {\r\n        this.merCateList = this.filerMerCateList(res)\r\n        let newArr = [];\r\n        res.forEach((value, index) => {\r\n          newArr[index] = value;\r\n          if (value.child) newArr[index].child = value.child.filter(item => item.status === true)\r\n        }) //过滤珠子分类设置为隐藏的子分类不出现在树形列表里\r\n        this.merCateList = this.filerMerCateList(newArr)\r\n      })\r\n    },\r\n    filerMerCateList(treeData) {\r\n      return treeData.map((item) => {\r\n        // if(!item.child){\r\n        //   item.disabled = true\r\n        // }\r\n        item.label = item.name\r\n        return item\r\n      })\r\n    },\r\n    // 获取珠子属性模板；\r\n    productGetRule() {\r\n      templateListApi(this.tableFrom).then(res => {\r\n        const list = res.list\r\n        for (var i = 0; i < list.length; i++) {\r\n          list[i].ruleValue = JSON.parse(list[i].ruleValue)\r\n        }\r\n        this.ruleList = list\r\n      })\r\n    },\r\n    // 运费模板；\r\n    getShippingList() {\r\n      shippingTemplatesList(this.tempData).then(res => {\r\n        this.shippingList = res.list\r\n        if(this.shippingList && this.shippingList.length > 0) {\r\n          this.formValidate.tempId = this.shippingList[0].id;\r\n        }\r\n      })\r\n    },\r\n    showInput(item) {\r\n      this.$set(item, 'inputVisible', true)\r\n    },\r\n    onChangetype(item) {\r\n      if (item === 1) {\r\n        this.OneattrValue.map(item => {\r\n          this.$set(item, 'brokerage', null)\r\n          this.$set(item, 'brokerageTwo', null)\r\n        })\r\n        this.ManyAttrValue.map(item => {\r\n          this.$set(item, 'brokerage', null)\r\n          this.$set(item, 'brokerageTwo', null)\r\n        })\r\n      } else {\r\n        this.OneattrValue.map(item => {\r\n          delete item.brokerage\r\n          delete item.brokerageTwo\r\n          this.$set(item, 'brokerage', null)\r\n          this.$set(item, 'brokerageTwo', null)\r\n        })\r\n        this.ManyAttrValue.map(item => {\r\n          delete item.brokerage\r\n          delete item.brokerageTwo\r\n        })\r\n      }\r\n    },\r\n    // 删除表格中的属性\r\n    delAttrTable(index) {\r\n      this.ManyAttrValue.splice(index, 1)\r\n    },\r\n    // 批量添加\r\n    batchAdd() {\r\n      // if (!this.oneFormBatch[0].pic || !this.oneFormBatch[0].price || !this.oneFormBatch[0].cost || !this.oneFormBatch[0].ot_price ||\r\n      //     !this.oneFormBatch[0].stock || !this.oneFormBatch[0].bar_code) return this.$Message.warning('请填写完整的批量设置内容！');\r\n      for (const val of this.ManyAttrValue) {\r\n        this.$set(val, 'image', this.oneFormBatch[0].image)\r\n        this.$set(val, 'price', this.oneFormBatch[0].price)\r\n        this.$set(val, 'cost', this.oneFormBatch[0].cost)\r\n        this.$set(val, 'otPrice', this.oneFormBatch[0].otPrice)\r\n        this.$set(val, 'stock', this.oneFormBatch[0].stock)\r\n        // this.$set(val, 'width', this.oneFormBatch[0].width)\r\n        // this.$set(val, 'height', this.oneFormBatch[0].height)\r\n        // this.$set(val, 'barCode', this.oneFormBatch[0].barCode)\r\n        // this.$set(val, 'weight', this.oneFormBatch[0].weight)\r\n        // this.$set(val, 'volume', this.oneFormBatch[0].volume)\r\n        this.$set(val, 'brokerage', this.oneFormBatch[0].brokerage)\r\n        this.$set(val, 'brokerageTwo', this.oneFormBatch[0].brokerageTwo)\r\n      }\r\n    },\r\n    // 添加按钮\r\n    addBtn() {\r\n      this.clearAttr()\r\n      this.isBtn = true\r\n    },\r\n    // 取消\r\n    offAttrName() {\r\n      this.isBtn = false\r\n    },\r\n    clearAttr() {\r\n      this.isAttr = true\r\n      this.formDynamic.attrsName = ''\r\n      this.formDynamic.attrsVal = ''\r\n    },\r\n    // 删除规格\r\n    handleRemoveAttr(index) {\r\n      this.isAttr = true\r\n      this.formValidate.attr.splice(index, 1)\r\n      this.manyFormValidate.splice(index, 1)\r\n    },\r\n    // 删除属性\r\n    handleClose(item, index) {\r\n      item.splice(index, 1)\r\n    },\r\n    // 添加规则名称\r\n    createAttrName() {\r\n      this.isAttr = true\r\n      if (this.formDynamic.attrsName && this.formDynamic.attrsVal) {\r\n        const data = {\r\n          attrName: this.formDynamic.attrsName,\r\n          attrValue: [\r\n            this.formDynamic.attrsVal\r\n          ]\r\n        }\r\n        this.formValidate.attr.push(data)\r\n        var hash = {}\r\n        this.formValidate.attr = this.formValidate.attr.reduce(function (item, next) {\r\n          /* eslint-disable */\r\n          hash[next.attrName] ? '' : hash[next.attrName] = true && item.push(next)\r\n          return item\r\n        }, [])\r\n        this.clearAttr()\r\n        this.isBtn = false\r\n      } else {\r\n        this.$Message.warning('请添加完整的规格！');\r\n      }\r\n    },\r\n    // 添加属性\r\n    createAttr(num, idx) {\r\n      this.isAttr = true\r\n      if (num) {\r\n        this.formValidate.attr[idx].attrValue.push(num);\r\n        var hash = {};\r\n        this.formValidate.attr[idx].attrValue = this.formValidate.attr[idx].attrValue.reduce(function (item, next) {\r\n          /* eslint-disable */\r\n          hash[next] ? '' : hash[next] = true && item.push(next);\r\n          return item\r\n        }, []);\r\n        this.formValidate.attr[idx].inputVisible = false\r\n      } else {\r\n        this.$message.warning('请添加属性');\r\n      }\r\n    },\r\n    //点击展示所有多规格属性\r\n    showAllSku() {\r\n      if (this.isAttr == false) {\r\n        this.isAttr = true;\r\n        if (this.formValidate.specType && this.isAttr) this.watCh(this.formValidate.attr) //重要！！！\r\n      } else if (this.isAttr == true) {\r\n        this.isAttr = false;\r\n        this.getInfo();\r\n      }\r\n    },\r\n    // 详情\r\n    getInfo() {\r\n      this.fullscreenLoading = true\r\n      productDetailApi(this.$route.params.id).then(async res => {\r\n        // this.isAttr = true;\r\n        let info = res\r\n        this.formValidate = {\r\n          image: this.$selfUtil.setDomain(info.image),\r\n          sliderImage: info.sliderImage,\r\n          sliderImages: JSON.parse(info.sliderImage),\r\n          storeName: info.storeName,\r\n          storeInfo: info.storeInfo,\r\n          keyword: info.keyword,\r\n          cateIds: info.cateId.split(','), // 珠子分类id\r\n          cateId: info.cateId,// 珠子分类id传值\r\n          unitName: info.unitName,\r\n          width: info.width,\r\n          height: info.height,\r\n          sort: info.sort,\r\n          isShow: info.isShow,\r\n          isBenefit: info.isBenefit,\r\n          isNew: info.isNew,\r\n          isGood: info.isGood,\r\n          isHot: info.isHot,\r\n          isBest: info.isBest,\r\n          tempId: info.tempId,\r\n          attr: info.attr,\r\n          attrValue: info.attrValue,\r\n          selectRule: info.selectRule,\r\n          isSub: info.isSub,\r\n          content: this.$selfUtil.replaceImgSrcHttps(info.content),\r\n          specType: info.specType,\r\n          id: info.id,\r\n          giveIntegral: info.giveIntegral,\r\n          ficti: info.ficti,\r\n          coupons: info.coupons,\r\n          couponIds: info.couponIds,\r\n          type: info.type,\r\n          activity: info.activityStr ? info.activityStr.split(',') : ['默认', '秒杀', '砍价', '拼团']\r\n        }\r\n        marketingSendApi({ type: 3 }).then(res => {\r\n          if (this.formValidate.couponIds !== null) {\r\n            let ids = this.formValidate.couponIds.toString();\r\n            let arr = res.list;\r\n            let obj = {};\r\n            for (let i in arr) {\r\n              obj[arr[i].id] = arr[i];\r\n            }\r\n            let strArr = ids.split(',');\r\n            let newArr = [];\r\n            for (let item of strArr) {\r\n              if (obj[item]) {\r\n                newArr.push(obj[item]);\r\n              }\r\n            }\r\n            this.$set(this.formValidate, 'coupons', newArr); //在编辑回显时，让返回数据中的优惠券id，通过接口匹配显示,\r\n          }\r\n        })\r\n        let imgs = JSON.parse(info.sliderImage)\r\n        let imgss = []\r\n        Object.keys(imgs).map(i => {\r\n          imgss.push(this.$selfUtil.setDomain(imgs[i]))\r\n        })\r\n        this.formValidate.sliderImages = [...imgss]\r\n        if (info.isHot) this.checkboxGroup.push('isHot')\r\n        if (info.isGood) this.checkboxGroup.push('isGood')\r\n        if (info.isBenefit) this.checkboxGroup.push('isBenefit')\r\n        if (info.isBest) this.checkboxGroup.push('isBest')\r\n        if (info.isNew) this.checkboxGroup.push('isNew')\r\n        this.productGetRule()\r\n        if (info.specType) {\r\n          this.formValidate.attr = info.attr.map(item => {\r\n            return {\r\n              attrName: item.attrName,\r\n              attrValue: item.attrValues.split(',')\r\n            }\r\n          })\r\n          this.ManyAttrValue = info.attrValue;\r\n          this.ManyAttrValue.forEach((val) => {\r\n            val.image = this.$selfUtil.setDomain(val.image);\r\n            val.attrValue = JSON.parse(val.attrValue);\r\n            this.attrInfo[Object.values(val.attrValue).sort().join('/')] = val;\r\n          })\r\n          /***多规格珠子如果被删除过sku，优先展示api返回的数据,否则会有没有删除的错觉***/\r\n          let manyAttr = this.attrFormat(this.formValidate.attr)\r\n          if (manyAttr.length !== this.ManyAttrValue.length) {\r\n            this.$set(this, 'showAll', true)\r\n            this.isAttr = false;\r\n          } else {\r\n            this.isAttr = true;\r\n          }\r\n          /*******/\r\n          const tmp = {}\r\n          const tmpTab = {}\r\n          this.formValidate.attr.forEach((o, i) => {\r\n            // tmp['value' + i] = { title: o.attrName }\r\n            // tmpTab['value' + i] = ''\r\n            tmp[o.attrName] = { title: o.attrName };\r\n            tmpTab[o.attrName] = '';\r\n          })\r\n\r\n          // 此处手动实现后台原本value0 value1的逻辑\r\n          this.formValidate.attrValue.forEach(item => {\r\n            for (let attrValueKey in item.attrValue) {\r\n              item[attrValueKey] = item.attrValue[attrValueKey];\r\n            }\r\n          });\r\n\r\n          this.manyTabTit = tmp\r\n          this.manyTabDate = tmpTab\r\n          this.formThead = Object.assign({}, this.formThead, tmp)\r\n        } else {\r\n          this.OneattrValue = info.attrValue\r\n          // this.formValidate.attr = [] //单规格珠子规格设置为空\r\n        }\r\n        this.fullscreenLoading = false\r\n      }).catch(res => {\r\n        this.fullscreenLoading = false\r\n        this.$message.error(res.message);\r\n      });\r\n    },\r\n    handleRemove(i) {\r\n      this.formValidate.sliderImages.splice(i, 1)\r\n    },\r\n    // 点击珠子图\r\n    modalPicTap(tit, num, i, status) {\r\n      const _this = this;\r\n      if (_this.isDisabled) return;\r\n      this.$modalUpload(function (img) {\r\n        if (tit === '1' && !num) {\r\n          _this.formValidate.image = img[0].sattDir\r\n          _this.OneattrValue[0].image = img[0].sattDir\r\n        }\r\n        if (tit === '2' && !num) {\r\n          if (img.length > 10) return this.$message.warning(\"最多选择10张图片！\");\r\n          if (img.length + _this.formValidate.sliderImages.length > 10) return this.$message.warning(\"最多选择10张图片！\");\r\n          img.map((item) => {\r\n            _this.formValidate.sliderImages.push(item.sattDir)\r\n          });\r\n        }\r\n        if (tit === '1' && num === 'dan') {\r\n          _this.OneattrValue[0].image = img[0].sattDir\r\n        }\r\n        if (tit === '1' && num === 'duo') {\r\n          _this.ManyAttrValue[i].image = img[0].sattDir\r\n        }\r\n        if (tit === '1' && num === 'pi') {\r\n          _this.oneFormBatch[0].image = img[0].sattDir\r\n        }\r\n      }, tit, 'content')\r\n    },\r\n    handleSubmitUp() {\r\n      // this.currentTab --\r\n      if (this.currentTab-- < 0) this.currentTab = 0;\r\n    },\r\n    handleSubmitNest(name) {\r\n      this.$refs[name].validate((valid) => {\r\n        if (valid) {\r\n          if (this.currentTab++ > 2) this.currentTab = 0;\r\n        } else {\r\n          if (!this.formValidate.store_name\r\n            || !this.formValidate.cate_id\r\n            // || !this.formValidate.keyword\r\n            || !this.formValidate.unit_name\r\n            // || !this.formValidate.store_info\r\n            || !this.formValidate.image\r\n            // || !this.formValidate.slider_image\r\n          ) {\r\n            this.$message.warning(\"请填写完整珠子信息！\");\r\n          }\r\n        }\r\n      })\r\n    },\r\n    // 提交\r\n    handleSubmit: Debounce(function (name) {\r\n      this.onChangeGroup()\r\n      if (this.formValidate.specType && this.formValidate.attr.length < 1) return this.$message.warning(\"请填写多规格属性！\");\r\n      this.formValidate.cateId = this.formValidate.cateIds.join(',')\r\n      this.formValidate.sliderImage = JSON.stringify(this.formValidate.sliderImages)\r\n      if (this.formValidate.specType) {\r\n        this.formValidate.attrValue = this.ManyAttrValue;\r\n        this.formValidate.attr = this.formValidate.attr.map((item) => {\r\n          return {\r\n            attrName: item.attrName,\r\n            id: item.id,\r\n            attrValues: item.attrValue.join(','),\r\n          }\r\n        })\r\n        for (var i = 0; i < this.formValidate.attrValue.length; i++) {\r\n          this.$set(this.formValidate.attrValue[i], 'id', 0);\r\n          this.$set(this.formValidate.attrValue[i], 'productId', 0);\r\n          this.$set(this.formValidate.attrValue[i], 'attrValue', JSON.stringify(this.formValidate.attrValue[i].attrValue)); //\r\n          delete this.formValidate.attrValue[i].value0\r\n        }\r\n      } else {\r\n        this.formValidate.attr = [{ attrName: '规格', attrValues: '默认', id: this.$route.params.id ? this.formValidate.attr[0].id : 0 }]\r\n        this.OneattrValue.map(item => {\r\n          this.$set(item, 'attrValue', JSON.stringify({ '规格': '默认' }))\r\n        })\r\n        this.formValidate.attrValue = this.OneattrValue\r\n      }\r\n      this.$refs[name].validate((valid) => {\r\n        if (valid) {\r\n          this.fullscreenLoading = true\r\n          this.$route.params.id ? productUpdateApi(this.formValidate).then(async res => {\r\n            this.$message.success('编辑成功');\r\n            setTimeout(() => {\r\n              this.$router.push({ path: '/store/braceletsIndex' });\r\n            }, 500);\r\n            this.fullscreenLoading = false\r\n          }).catch(res => {\r\n            this.fullscreenLoading = false\r\n          }) : productCreateApi(this.formValidate).then(async res => {\r\n            this.$message.success('新增成功');\r\n            setTimeout(() => {\r\n              this.$router.push({ path: '/store/braceletsIndex' });\r\n            }, 500);\r\n            this.fullscreenLoading = false\r\n          }).catch(res => {\r\n            this.fullscreenLoading = false\r\n          })\r\n        } else {\r\n          if (!this.formValidate.storeName \r\n          || !this.formValidate.cateId \r\n          // || !this.formValidate.keyword\r\n            || !this.formValidate.unitName \r\n            // || !this.formValidate.storeInfo \r\n            || !this.formValidate.image \r\n            // || !this.formValidate.sliderImages\r\n          ) {\r\n            this.$message.warning(\"请填写完整珠子信息！\");\r\n          }\r\n        }\r\n      });\r\n    }),\r\n    // 表单验证\r\n    validate(prop, status, error) {\r\n      if (status === false) {\r\n        this.$message.warning(error);\r\n      }\r\n    },\r\n    // 移动\r\n    handleDragStart(e, item) {\r\n      if (!this.isDisabled) this.dragging = item;\r\n    },\r\n    handleDragEnd(e, item) {\r\n      if (!this.isDisabled) this.dragging = null\r\n    },\r\n    handleDragOver(e) {\r\n      if (!this.isDisabled) e.dataTransfer.dropEffect = 'move'\r\n    },\r\n    handleDragEnter(e, item) {\r\n      if (!this.isDisabled) {\r\n        e.dataTransfer.effectAllowed = 'move'\r\n        if (item === this.dragging) {\r\n          return\r\n        }\r\n        const newItems = [...this.formValidate.sliderImages]\r\n        const src = newItems.indexOf(this.dragging)\r\n        const dst = newItems.indexOf(item)\r\n        newItems.splice(dst, 0, ...newItems.splice(src, 1))\r\n        this.formValidate.sliderImages = newItems;\r\n      }\r\n    },\r\n    handleDragEnterFont(e, item) {\r\n      if (!this.isDisabled) {\r\n        e.dataTransfer.effectAllowed = 'move'\r\n        if (item === this.dragging) {\r\n          return\r\n        }\r\n        const newItems = [...this.formValidate.activity]\r\n        const src = newItems.indexOf(this.dragging)\r\n        const dst = newItems.indexOf(item)\r\n        newItems.splice(dst, 0, ...newItems.splice(src, 1))\r\n        this.formValidate.activity = newItems;\r\n      }\r\n    },\r\n    getGoodsType() {\r\n      /** 让珠子推荐列表的name属性与页面设置tab的name匹配**/\r\n      goodDesignList({ gid: 70 }).then((response) => {\r\n        let list = response.list;\r\n        let arr = [], arr1 = [];\r\n        const listArr = [{ name: '是否热卖', value: 'isGood' }];\r\n        let typeLists = [\r\n          { name: '', value: 'isHot', type: '2' },   //热门榜单 \r\n          { name: '', value: 'isBenefit', type: '4' }, //促销单品\r\n          { name: '', value: 'isBest', type: '1' }, //精品推荐\r\n          { name: '', value: 'isNew', type: '3' }]; //首发新品\r\n        list.forEach((item) => {\r\n          let obj = {};\r\n          obj.value = JSON.parse(item.value);\r\n          obj.id = item.id;\r\n          obj.gid = item.gid;\r\n          obj.status = item.status;\r\n          arr.push(obj);\r\n        })\r\n        arr.forEach((item1) => {\r\n          let obj1 = {};\r\n          obj1.name = item1.value.fields[1].value;\r\n          obj1.status = item1.status;\r\n          obj1.type = item1.value.fields[3].value;\r\n          arr1.push(obj1);\r\n        })\r\n        typeLists.forEach((item) => {\r\n          arr1.forEach((item1) => {\r\n            if (item.type == item1.type) {\r\n              listArr.push({\r\n                name: item1.name,\r\n                value: item.value,\r\n                type: item.type\r\n              })\r\n            }\r\n          })\r\n        })\r\n        this.recommend = listArr\r\n      })\r\n    },\r\n  }\r\n}\r\n", null]}