<view class="data-v-33ff140c"><view class="{{['address-window','data-v-33ff140c',address.address==true?'on':'']}}"><view class="title data-v-33ff140c">选择地址<text data-event-opts="{{[['tap',[['close',['$event']]]]]}}" class="iconfont icon-guanbi data-v-33ff140c" bindtap="__e"></text></view><view class="list data-v-33ff140c"><block wx:for="{{addressList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['tapAddress',[index,'$0'],[[['addressList','',index,'id']]]]]]]}}" class="{{['item','acea-row','row-between-wrapper','data-v-33ff140c',active==index?'font-color':'']}}" bindtap="__e"><text class="{{['iconfont','icon-ditu','data-v-33ff140c',active==index?'font-color':'']}}"></text><view class="address data-v-33ff140c"><view class="{{['name','data-v-33ff140c',active==index?'font-color':'']}}">{{item.realName}}<text class="phone data-v-33ff140c">{{item.phone}}</text></view><view class="line1 data-v-33ff140c">{{item.province+item.city+item.district+item.detail}}</view></view><text class="{{['iconfont','icon-complete','data-v-33ff140c',active==index?'font-color':'']}}"></text></view></block></view><block wx:if="{{$root.g0}}"><view class="pictrue data-v-33ff140c"><image src="../../static/images/noAddress.png" class="data-v-33ff140c"></image></view></block><view data-event-opts="{{[['tap',[['goAddressPages',['$event']]]]]}}" class="addressBnt bg-color data-v-33ff140c" bindtap="__e">选择其地址</view></view><view class="mask data-v-33ff140c" catchtouchmove="true" hidden="{{address.address==false}}" data-event-opts="{{[['tap',[['close',['$event']]]]]}}" bindtap="__e"></view></view>