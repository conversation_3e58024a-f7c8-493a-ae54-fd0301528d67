<view class="data-v-e8d29546"><view class="ChangePassword data-v-e8d29546"><view class="list data-v-e8d29546"><block wx:if="{{isNew}}"><view class="item data-v-e8d29546"><input type="number" disabled="true" placeholder="填写手机号码1" placeholder-class="placeholder" data-event-opts="{{[['input',[['__set_model',['$0','phone','$event',[]],['userInfo']]]]]}}" value="{{userInfo.phone}}" bindinput="__e" class="data-v-e8d29546"/></view></block><block wx:if="{{!isNew}}"><view class="item data-v-e8d29546"><input type="number" placeholder="填写手机号码" placeholder-class="placeholder" data-event-opts="{{[['input',[['__set_model',['','phone','$event',[]]]]]]}}" value="{{phone}}" bindinput="__e" class="data-v-e8d29546"/></view></block><view class="item acea-row row-between-wrapper data-v-e8d29546"><input class="codeIput data-v-e8d29546" type="number" placeholder="填写验证码" placeholder-class="placeholder" data-event-opts="{{[['input',[['__set_model',['','captcha','$event',[]]]]]]}}" value="{{captcha}}" bindinput="__e"/><button class="{{['code','font-color','data-v-e8d29546',disabled===true?'on':'']}}" disabled="{{disabled}}" data-event-opts="{{[['tap',[['code',['$event']]]]]}}" bindtap="__e">{{''+text+''}}</button></view></view><block wx:if="{{isNew}}"><button class="confirmBnt bg-color data-v-e8d29546" form-type="submit" data-event-opts="{{[['tap',[['next',['$event']]]]]}}" bindtap="__e">下一步</button></block><block wx:if="{{!isNew}}"><button class="confirmBnt bg-color data-v-e8d29546" form-type="submit" data-event-opts="{{[['tap',[['editPwd',['$event']]]]]}}" bindtap="__e">保存</button></block></view></view>