{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\storeAttr\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\storeAttr\\index.vue", "mtime": 1753666157924}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _store = require(\"@/api/store\");\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  name: 'StoreAttr',\n  data: function data() {\n    return {\n      formDynamic: {\n        ruleName: '',\n        ruleValue: []\n      },\n      tableFrom: {\n        page: 1,\n        limit: 20,\n        keywords: ''\n      },\n      tableData: {\n        data: [],\n        loading: false,\n        total: 0\n      },\n      listLoading: true,\n      selectionList: [],\n      multipleSelectionAll: [],\n      idKey: 'id',\n      nextPageFlag: false,\n      keyNum: 0\n    };\n  },\n  mounted: function mounted() {\n    this.getList();\n  },\n  methods: {\n    seachList: function seachList() {\n      this.tableFrom.page = 1;\n      this.getList();\n    },\n    handleSelectionChange: function handleSelectionChange(val) {\n      var _this2 = this;\n      this.selectionList = val;\n      setTimeout(function () {\n        _this2.changePageCoreRecordData();\n      }, 50);\n    },\n    // 设置选中的方法\n    setSelectRow: function setSelectRow() {\n      if (!this.multipleSelectionAll || this.multipleSelectionAll.length <= 0) {\n        return;\n      }\n      // 标识当前行的唯一键的名称\n      var idKey = this.idKey;\n      var selectAllIds = [];\n      this.multipleSelectionAll.forEach(function (row) {\n        selectAllIds.push(row[idKey]);\n      });\n      this.$refs.table.clearSelection();\n      for (var i = 0; i < this.tableData.data.length; i++) {\n        if (selectAllIds.indexOf(this.tableData.data[i][idKey]) >= 0) {\n          // 设置选中，记住table组件需要使用ref=\"table\"\n          this.$refs.table.toggleRowSelection(this.tableData.data[i], true);\n        }\n      }\n    },\n    // 记忆选择核心方法\n    changePageCoreRecordData: function changePageCoreRecordData() {\n      // 标识当前行的唯一键的名称\n      var idKey = this.idKey;\n      var that = this;\n      // 如果总记忆中还没有选择的数据，那么就直接取当前页选中的数据，不需要后面一系列计算\n      if (this.multipleSelectionAll.length <= 0) {\n        this.multipleSelectionAll = this.selectionList;\n        return;\n      }\n      // 总选择里面的key集合\n      var selectAllIds = [];\n      this.multipleSelectionAll.forEach(function (row) {\n        selectAllIds.push(row[idKey]);\n      });\n      var selectIds = [];\n      // 获取当前页选中的id\n      this.selectionList.forEach(function (row) {\n        selectIds.push(row[idKey]);\n        // 如果总选择里面不包含当前页选中的数据，那么就加入到总选择集合里\n        if (selectAllIds.indexOf(row[idKey]) < 0) {\n          that.multipleSelectionAll.push(row);\n        }\n      });\n      var noSelectIds = [];\n      // 得到当前页没有选中的id\n      this.tableData.data.forEach(function (row) {\n        if (selectIds.indexOf(row[idKey]) < 0) {\n          noSelectIds.push(row[idKey]);\n        }\n      });\n      noSelectIds.forEach(function (id) {\n        if (selectAllIds.indexOf(id) >= 0) {\n          for (var i = 0; i < that.multipleSelectionAll.length; i++) {\n            if (that.multipleSelectionAll[i][idKey] == id) {\n              // 如果总选择中有未被选中的，那么就删除这条\n              that.multipleSelectionAll.splice(i, 1);\n              break;\n            }\n          }\n        }\n      });\n    },\n    add: function add() {\n      var _this = this;\n      this.$modalAttr(Object.assign({}, this.formDynamic), function () {\n        _this.getList();\n      }, this.keyNum += 1);\n    },\n    // 列表\n    getList: function getList() {\n      var _this3 = this;\n      this.listLoading = true;\n      (0, _store.templateListApi)(this.tableFrom).then(function (res) {\n        var list = res.list;\n        _this3.tableData.data = list;\n        _this3.tableData.total = res.total;\n        for (var i = 0; i < list.length; i++) {\n          list[i].ruleValue = JSON.parse(list[i].ruleValue);\n        }\n        _this3.$nextTick(function () {\n          this.setSelectRow(); // 调用跨页选中方法\n        });\n        _this3.listLoading = false;\n      }).catch(function () {\n        _this3.listLoading = false;\n      });\n    },\n    pageChange: function pageChange(page) {\n      this.changePageCoreRecordData();\n      this.tableFrom.page = page;\n      this.getList();\n    },\n    handleSizeChange: function handleSizeChange(val) {\n      this.changePageCoreRecordData();\n      this.tableFrom.limit = val;\n      this.getList();\n    },\n    // 删除\n    handleDelete: function handleDelete(id, idx) {\n      var _this4 = this;\n      this.$modalSure().then(function () {\n        (0, _store.attrDeleteApi)(id).then(function () {\n          _this4.$message.success('删除成功');\n          _this4.tableData.data.splice(idx, 1);\n        });\n      }).catch(function () {});\n    },\n    handleDeleteAll: function handleDeleteAll() {\n      var _this5 = this;\n      if (!this.multipleSelectionAll.length) return this.$message.warning('请选择商品规格');\n      var data = [];\n      this.multipleSelectionAll.map(function (item) {\n        data.push(item.id);\n      });\n      this.ids = data.join(',');\n      this.$modalSure().then(function () {\n        (0, _store.attrDeleteApi)(_this5.ids).then(function () {\n          _this5.$message.success('删除成功');\n          _this5.getList();\n        });\n      }).catch(function () {});\n    },\n    onEdit: function onEdit(val) {\n      var _this = this;\n      this.$modalAttr(JSON.parse(JSON.stringify(val)), function () {\n        _this.getList();\n      });\n    }\n  }\n};", null]}