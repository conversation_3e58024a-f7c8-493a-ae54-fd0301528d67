{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\order\\orderVideoSend.vue?vue&type=template&id=4b3177f0&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\order\\orderVideoSend.vue", "mtime": 1753666157911}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"el-dialog\",\n    {\n      staticClass: \"order_box\",\n      attrs: {\n        visible: _vm.modals,\n        title: \"发送货\",\n        \"before-close\": _vm.handleClose,\n        width: \"600px\",\n      },\n      on: {\n        \"update:visible\": function ($event) {\n          _vm.modals = $event\n        },\n      },\n    },\n    [\n      _c(\n        \"el-form\",\n        {\n          ref: \"formItem\",\n          attrs: {\n            model: _vm.formItem,\n            \"label-width\": \"110px\",\n            rules: _vm.rules,\n          },\n          nativeOn: {\n            submit: function ($event) {\n              $event.preventDefault()\n            },\n          },\n        },\n        [\n          _c(\n            \"el-form-item\",\n            { attrs: { label: \"快递公司：\", prop: \"expressCode\" } },\n            [\n              _c(\n                \"el-select\",\n                {\n                  staticStyle: { width: \"80%\" },\n                  attrs: { filterable: \"\" },\n                  model: {\n                    value: _vm.formItem.deliveryId,\n                    callback: function ($$v) {\n                      _vm.$set(_vm.formItem, \"deliveryId\", $$v)\n                    },\n                    expression: \"formItem.deliveryId\",\n                  },\n                },\n                _vm._l(_vm.express, function (item, i) {\n                  return _c(\"el-option\", {\n                    key: i,\n                    attrs: { value: item.deliveryId, label: item.deliveryName },\n                  })\n                }),\n                1\n              ),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-form-item\",\n            { attrs: { label: \"快递单号：\", prop: \"waybillId\" } },\n            [\n              _c(\"el-input\", {\n                staticStyle: { width: \"80%\" },\n                attrs: { placeholder: \"请输入快递单号\" },\n                model: {\n                  value: _vm.formItem.waybillId,\n                  callback: function ($$v) {\n                    _vm.$set(_vm.formItem, \"waybillId\", $$v)\n                  },\n                  expression: \"formItem.waybillId\",\n                },\n              }),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"div\",\n        { attrs: { slot: \"footer\" }, slot: \"footer\" },\n        [\n          _c(\n            \"el-button\",\n            {\n              directives: [\n                {\n                  name: \"hasPermi\",\n                  rawName: \"v-hasPermi\",\n                  value: [\"admin:order:video:send\"],\n                  expression: \"['admin:order:video:send']\",\n                },\n              ],\n              attrs: { size: \"mini\", type: \"primary\" },\n              on: {\n                click: function ($event) {\n                  return _vm.putSend(\"formItem\")\n                },\n              },\n            },\n            [_vm._v(\"提交\")]\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-button\",\n            {\n              attrs: { size: \"mini\" },\n              on: {\n                click: function ($event) {\n                  return _vm.cancel(\"formItem\")\n                },\n              },\n            },\n            [_vm._v(\"取消\")]\n          ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}