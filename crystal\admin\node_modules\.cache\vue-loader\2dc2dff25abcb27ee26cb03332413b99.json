{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\order\\orderDetail.vue?vue&type=template&id=4328fb58&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\order\\orderDetail.vue", "mtime": 1753666157910}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\n<div>\n  <el-dialog\n    title=\"订单信息\"\n    :visible.sync=\"dialogVisible\"\n    width=\"700px\"\n    v-if=\"orderDatalist\"\n  >\n    <div class=\"description\" v-loading=\"loading\">\n      <div class=\"title\">用户信息</div>\n      <div class=\"acea-row\">\n        <div class=\"description-term\">用户昵称：{{orderDatalist.nikeName}}</div>\n        <div class=\"description-term\">绑定电话：{{orderDatalist.phone ? orderDatalist.phone : '无'}}</div>\n      </div>\n      <el-divider></el-divider>\n      <div class=\"title\">{{orderDatalist.statusStr.key === 'toBeWrittenOff'?'提货信息': '收货信息'}}</div>\n      <div class=\"acea-row\">\n        <div class=\"description-term\">{{orderDatalist.statusStr.key === 'toBeWrittenOff'?'提货人': '收货人'}}：{{orderDatalist.realName}}</div>\n        <div class=\"description-term\">{{orderDatalist.statusStr.key === 'toBeWrittenOff'?'提货电话': '收货电话'}}：{{orderDatalist.userPhone}}</div>\n        <div class=\"description-term\" v-if=\"orderDatalist.statusStr.key !== 'toBeWrittenOff'\">{{orderDatalist.statusStr.key === 'toBeWrittenOff'?'提货地址': '收货地址'}}：{{orderDatalist.userAddress}}</div>\n      </div>\n      <el-divider></el-divider>\n      <div class=\"title\">订单信息</div>\n      <div class=\"acea-row\">\n        <div class=\"description-term\">订单编号：{{orderDatalist.orderId}}</div>\n        <div class=\"description-term\" style=\"color: red\">订单状态：{{orderDatalist.statusStr.value}}</div>\n        <div class=\"description-term\">商品总数：{{orderDatalist.totalNum}}</div>\n        <div class=\"description-term\">商品总价：{{orderDatalist.proTotalPrice}}</div>\n        <div class=\"description-term\">支付邮费：{{orderDatalist.payPostage}}</div>\n        <div class=\"description-term\">优惠券金额：{{orderDatalist.couponPrice}}</div>\n        <div class=\"description-term\">实际支付：{{orderDatalist.payPrice}}</div>\n        <div class=\"description-term\">抵扣金额：{{orderDatalist.deductionPrice}}</div>\n        <div class=\"description-term fontColor3\" v-if=\"orderDatalist.refundPrice\">退款金额：{{orderDatalist.refundPrice}}</div>\n        <div class=\"description-term\" v-if=\"orderDatalist.useIntegral\">使用积分：{{orderDatalist.useIntegral}}</div>\n        <div class=\"description-term\" v-if=\"orderDatalist.backIntegral\">退回积分：{{orderDatalist.backIntegral}}</div>\n        <div class=\"description-term\">创建时间：{{orderDatalist.createTime}}</div>\n        <div class=\"description-term\" v-if=\"orderDatalist.refundReasonTime\">退款时间：{{orderDatalist.refundReasonTime}}</div>\n        <div class=\"description-term\">支付方式：{{orderDatalist.payTypeStr}}</div>\n        <div class=\"description-term\">推广人：{{orderDatalist.spreadName | filterEmpty}}</div>\n        <div class=\"description-term\" v-if=\"orderDatalist.shippingType === 2 && orderDatalist.statusStr.key === 'notShipped'\">门店名称：{{orderDatalist.storeName}}</div>\n        <div class=\"description-term\" v-if=\"orderDatalist.shippingType === 2 && orderDatalist.statusStr.key === 'notShipped'\">核销码：{{orderDatalist.user_phone}}</div>\n        <div class=\"description-term\">商家备注：{{orderDatalist.remark}}</div>\n        <template v-if=\"orderDatalist.statusStr.key === 'toBeWrittenOff' && orderDatalist.systemStore\">\n          <div class=\"description-term\">提货码：{{orderDatalist.verifyCode}}</div>\n          <div class=\"description-term\">门店名称：{{orderDatalist.systemStore.name}}</div>\n          <div class=\"description-term\">门店电话：{{orderDatalist.systemStore.phone}}</div>\n          <div class=\"description-term\">门店地址：{{orderDatalist.systemStore.address + orderDatalist.systemStore.detailedAddress}}</div>\n        </template>\n\n      </div>\n      <template v-if=\"orderDatalist.deliveryType === 'express'\">\n        <el-divider></el-divider>\n        <div class=\"title\">物流信息</div>\n        <div class=\"acea-row\">\n          <div class=\"description-term\">快递公司：{{orderDatalist.deliveryName}}</div>\n          <div class=\"description-term\">快递单号：{{orderDatalist.deliveryId}}\n            <el-button type=\"primary\" size=\"mini\" @click=\"openLogistics\" style=\"margin-left: 5px\" v-hasPermi=\"['admin:order:logistics:info']\">物流查询</el-button>\n          </div>\n        </div>\n      </template>\n      <template v-if=\"orderDatalist.deliveryType === 'send'\">\n        <el-divider></el-divider>\n        <div class=\"title\">配送信息</div>\n        <div class=\"acea-row\">\n          <div class=\"description-term\">送货人姓名：{{orderDatalist.deliveryName}}</div>\n          <div class=\"description-term\">送货人电话：{{orderDatalist.deliveryId}}</div>\n        </div>\n      </template>\n      <template v-if=\"orderDatalist.mark\">\n        <el-divider></el-divider>\n        <div class=\"title\">用户备注</div>\n        <div class=\"acea-row\">\n          <div class=\"description-term\">{{orderDatalist.mark}}</div>\n        </div>\n      </template>\n    </div>\n  </el-dialog>\n  <el-dialog\n    v-if=\"orderDatalist\"\n    title=\"提示\"\n    :visible.sync=\"modal2\"\n    width=\"30%\">\n    <div class=\"logistics acea-row row-top\">\n      <div class=\"logistics_img\"><img src=\"@/assets/imgs/expressi.jpg\"></div>\n      <div class=\"logistics_cent\">\n        <span class=\"mb10\">物流公司：{{orderDatalist.deliveryName}}</span>\n        <span>物流单号：{{orderDatalist.deliveryId}}</span>\n      </div>\n    </div>\n    <div class=\"acea-row row-column-around trees-coadd\">\n      <div class=\"scollhide\">\n        <el-timeline :reverse=\"reverse\">\n          <el-timeline-item v-for=\"(item,i) in result\" :key=\"i\">\n            <p class=\"time\" v-text=\"item.time\"></p>\n            <p class=\"content\" v-text=\"item.status\"></p>\n          </el-timeline-item>\n        </el-timeline >\n      </div>\n    </div>\n    <span slot=\"footer\" class=\"dialog-footer\">\n    <el-button type=\"primary\" @click=\"modal2 = false\">关闭</el-button>\n</span>\n  </el-dialog>\n</div>\n", null]}