{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\marketing\\bargain\\bargainGoods\\creatBargain.vue", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\marketing\\bargain\\bargainGoods\\creatBargain.vue", "mtime": 1753666157888}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\eslint-loader\\index.js", "mtime": 1753666298172}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./creatBargain.vue?vue&type=template&id=3b5da284&scoped=true\"\nimport script from \"./creatBargain.vue?vue&type=script&lang=js\"\nexport * from \"./creatBargain.vue?vue&type=script&lang=js\"\nimport style0 from \"./creatBargain.vue?vue&type=style&index=0&id=3b5da284&scoped=true&lang=scss\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3b5da284\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\crystal-mall\\\\crystal\\\\admin\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('3b5da284')) {\n      api.createRecord('3b5da284', component.options)\n    } else {\n      api.reload('3b5da284', component.options)\n    }\n    module.hot.accept(\"./creatBargain.vue?vue&type=template&id=3b5da284&scoped=true\", function () {\n      api.rerender('3b5da284', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/views/marketing/bargain/bargainGoods/creatBargain.vue\"\nexport default component.exports"]}