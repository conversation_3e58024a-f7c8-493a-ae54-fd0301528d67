(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/users/order_confirm/index"],{"140e":function(e,t,i){},2374:function(e,t,i){"use strict";i.r(t);var n=i("caf6"),s=i.n(n);for(var r in n)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(r);t["default"]=s.a},"35b2":function(e,t,i){"use strict";var n=i("140e"),s=i.n(n);s.a},"3f21":function(e,t,i){"use strict";i.d(t,"b",(function(){return n})),i.d(t,"c",(function(){return s})),i.d(t,"a",(function(){}));var n=function(){var e=this.$createElement,t=(this._self._c,0!=this.shippingType?this.storeList.length:null),i=0==this.shippingType?parseFloat(this.orderInfoVo.freightFee):null;this.$mp.data=Object.assign({},{$root:{g0:t,m0:i}})},s=[]},"3f93":function(e,t,i){"use strict";i.r(t);var n=i("3f21"),s=i("2374");for(var r in s)["default"].indexOf(r)<0&&function(e){i.d(t,e,(function(){return s[e]}))}(r);i("35b2");var o=i("828b"),a=Object(o["a"])(s["default"],n["b"],n["c"],!1,null,"03b3f3f6",null,!1,n["a"],void 0);t["default"]=a.exports},a1e7:function(e,t,i){"use strict";(function(e,t){var n=i("47a9");i("5c2d");n(i("3240"));var s=n(i("3f93"));e.__webpack_require_UNI_MP_PLUGIN__=i,t(s.default)}).call(this,i("3223")["default"],i("df3c")["createPage"])},caf6:function(e,t,i){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=i("3988"),s=i("5904"),r=i("2ae0"),o=i("1932"),a=(i("4bbc"),i("cda4")),d=i("8f59"),u={components:{couponListWindow:function(){Promise.all([i.e("common/vendor"),i.e("components/couponListWindow/index")]).then(function(){return resolve(i("f35f"))}.bind(null,i)).catch(i.oe)},addressWindow:function(){i.e("components/addressWindow/index").then(function(){return resolve(i("62d7"))}.bind(null,i)).catch(i.oe)},orderGoods:function(){i.e("components/orderGoods/index").then(function(){return resolve(i("2d34"))}.bind(null,i)).catch(i.oe)},home:function(){i.e("components/home/<USER>").then(function(){return resolve(i("bc9e"))}.bind(null,i)).catch(i.oe)},authorize:function(){Promise.all([i.e("common/vendor"),i.e("components/Authorize")]).then(function(){return resolve(i("cf49"))}.bind(null,i)).catch(i.oe)}},data:function(){return{userBraceletId:"",type:0,orderShow:"orderShow",textareaStatus:!0,cartArr:[{name:"微信支付",icon:"icon-weixin2",value:"weixin",title:"微信快捷支付",payStatus:1},{name:"余额支付",icon:"icon-icon-test",value:"yue",title:"可用余额:",payStatus:1}],payType:"weixin",openType:1,active:0,coupon:{coupon:!1,list:[],statusTile:"立即使用"},address:{address:!1,addressId:0},addressInfo:{},addressId:0,couponId:0,cartId:"",userInfo:{},mark:"",couponTitle:"请选择",coupon_price:0,useIntegral:!1,integral_price:0,integral:0,ChangePrice:0,formIds:[],status:0,is_address:!1,toPay:!1,shippingType:0,system_store:{},storePostage:0,contacts:"",contactsTel:"",mydata:{},storeList:[],store_self_mention:0,cartInfo:[],priceGroup:{},animated:!1,totalPrice:0,integralRatio:"0",pagesUrl:"",orderKey:"",offlinePostage:"",isAuto:!1,isShowAuth:!1,payChannel:"",news:!0,again:!1,addAgain:!1,bargain:!1,combination:!1,secKill:!1,orderInfoVo:{},addressList:[],orderProNum:0,preOrderNo:""}},computed:(0,d.mapGetters)(["isLogin","systemPlatform","productType"]),watch:{isLogin:{handler:function(e,t){e&&this.getloadPreOrder()},deep:!0}},onLoad:function(e){this.payChannel="routine",this.preOrderNo=e.preOrderNo||0,this.addressId=e.addressId||0,this.is_address=!!e.is_address,this.isLogin?this.getloadPreOrder():(0,a.toLogin)()},onShow:function(){var t=this;this.textareaStatus=!0,this.isLogin&&this.toPay,e.$on("handClick",(function(i){i&&(t.system_store=i.address),e.$off("handClick")}))},methods:{getloadPreOrder:function(){var e=this;(0,n.loadPreOrderApi)(this.preOrderNo).then((function(t){var i=t.data.orderInfoVo;e.orderInfoVo=i,e.cartInfo=i.orderDetailList,e.type=i.type,e.userBraceletId=i.userBraceletId,e.orderProNum=i.orderProNum,e.address.addressId=e.addressId?e.addressId:i.addressId,e.cartArr[1].title="可用余额:"+i.userBalance,e.cartArr[1].payStatus=1===parseInt(t.data.yuePayStatus)?1:2,e.cartArr[0].payStatus=1===parseInt(t.data.payWeixinOpen)?1:0,e.store_self_mention="true"==t.data.storeSelfMention&&"normal"===e.productType,e.$nextTick((function(){this.$refs.addressWindow.getAddressList()}))})).catch((function(t){return e.$util.Tips({title:t})}))},onLoadFun:function(){},getList:function(){var t=this,i=e.getStorageSync("user_longitude"),n=e.getStorageSync("user_latitude"),s={latitude:n,longitude:i,page:1,limit:10};(0,o.storeListApi)(s).then((function(e){var i=e.data.list||[];t.$set(t,"storeList",i),t.$set(t,"system_store",i[0])})).catch((function(e){return t.$util.Tips({title:e})}))},changeClose:function(){this.$set(this.address,"address",!1)},showStoreList:function(){this.storeList.length>0&&e.navigateTo({url:"/pages/users/goods_details_store/index"})},computedPrice:function(){var e=this,t=this.shippingType;(0,n.postOrderComputed)({addressId:this.address.addressId,useIntegral:!!this.useIntegral,couponId:this.couponId,shippingType:parseInt(t)+1,preOrderNo:this.preOrderNo}).then((function(t){var i=t.data;e.orderInfoVo.couponFee=i.couponFee,e.orderInfoVo.userIntegral=i.surplusIntegral,e.orderInfoVo.deductionPrice=i.deductionPrice,e.orderInfoVo.freightFee=i.freightFee,e.orderInfoVo.payFee=i.payFee,e.orderInfoVo.proTotalFee=i.proTotalFee,e.orderInfoVo.useIntegral=i.useIntegral,e.orderInfoVo.usedIntegral=i.usedIntegral,e.orderInfoVo.surplusIntegral=i.surplusIntegral})).catch((function(t){return e.$util.Tips({title:t})}))},addressType:function(e){var t=e;this.shippingType=parseInt(t),this.computedPrice(),1==t&&this.getList()},bindPickerChange:function(e){var t=e.detail.value;this.shippingType=t,this.computedPrice()},ChangCouponsClone:function(){this.$set(this.coupon,"coupon",!1)},changeTextareaStatus:function(){for(var e=0,t=this.coupon.list.length;e<t;e++)this.coupon.list[e].use_title="",this.coupon.list[e].is_use=0;this.textareaStatus=!0,this.status=0,this.$set(this.coupon,"list",this.coupon.list)},ChangCoupons:function(e){for(var t=e,i=this.coupon.list,n="请选择",s=0,r=0,o=i.length;r<o;r++)r!=t&&(i[r].use_title="",i[r].isUse=0);i[t].isUse?(i[t].use_title="",i[t].isUse=0):(i[t].use_title="不使用",i[t].isUse=1,n=i[t].name,s=i[t].id),this.couponTitle=n,this.couponId=s,this.$set(this.coupon,"coupon",!1),this.$set(this.coupon,"list",i),this.computedPrice()},ChangeIntegral:function(){this.useIntegral=!this.useIntegral,this.computedPrice()},OnDefaultAddress:function(e){this.addressInfo=e,this.address.addressId=e.id},OnChangeAddress:function(e){this.addressInfo=e,this.address.addressId=e.id,this.textareaStatus=!0,this.address.address=!1,this.computedPrice()},bindHideKeyboard:function(e){this.mark=e.detail.value},getCouponList:function(){var e=this;(0,n.getCouponsOrderPrice)(this.preOrderNo).then((function(t){e.$set(e.coupon,"list",t.data),e.openType=1}))},getaddressInfo:function(){var e=this;e.addressId?(0,s.getAddressDetail)(e.addressId).then((function(t){t.data&&(t.data.isDefault=parseInt(t.data.isDefault),e.addressInfo=t.data||{},e.addressId=t.data.id||0,e.address.addressId=t.data.id||0)})):getAddressDefault().then((function(t){t.data&&(t.data.isDefault=parseInt(t.data.isDefault),e.addressInfo=t.data||{},e.addressId=t.data.id||0,e.address.addressId=t.data.id||0)}))},payItem:function(e){var t=this,i=e;t.active=i,t.animated=!0,t.payType=t.cartArr[i].value,t.computedPrice(),setTimeout((function(){t.car()}),500)},couponTap:function(){this.coupon.coupon=!0,this.coupon.list.length||this.getCouponList()},car:function(){this.animated=!1},onAddress:function(){this.textareaStatus=!1,this.address.address=!0,this.pagesUrl="/pages/users/user_address_list/index?preOrderNo="+this.preOrderNo},realName:function(e){this.contacts=e.detail.value},phone:function(e){this.contactsTel=e.detail.value},payment:function(t){var i=this;(0,n.orderCreate)(t).then((function(e){i.getOrderPay(e.data.orderNo,"支付成功")})).catch((function(t){return e.hideLoading(),i.$util.Tips({title:t})}))},getOrderPay:function(t,i){var s=this,r="/pages/order_pay_status/index?order_id="+t+"&msg="+i;(0,n.wechatOrderPay)({orderNo:t,payChannel:s.payChannel,payType:s.payType,scene:"normal"===s.productType?0:1177}).then((function(o){var a=o.data.jsConfig;switch(o.data.payType){case"weixin":e.requestPayment({timeStamp:a.timeStamp,nonceStr:a.nonceStr,package:a.packages,signType:a.signType,paySign:a.paySign,ticket:"normal"===s.productType?null:a.ticket,success:function(i){e.hideLoading(),(0,n.wechatQueryPayResult)(t).then((function(t){return e.hideLoading(),s.orderInfoVo.bargainId||s.orderInfoVo.combinationId||s.pinkId||s.orderInfoVo.seckillId?s.$util.Tips({title:"支付成功",icon:"success"},{tab:4,url:r}):s.$util.Tips({title:"支付成功",icon:"success"},{tab:5,url:r})})).cache((function(t){return e.hideLoading(),s.$util.Tips({title:t})}))},fail:function(t){return e.hideLoading(),s.$util.Tips({title:"取消支付"},{tab:5,url:r+"&status=2"})},complete:function(t){if(e.hideLoading(),"requestPayment:cancel"==t.errMsg)return s.$util.Tips({title:"取消支付"},{tab:5,url:r+"&status=2"})}});break;case"yue":return e.hideLoading(),s.$util.Tips({title:i},{tab:5,url:r+"&status=1"});case"weixinh5":e.hideLoading(),s.$util.Tips({title:"订单创建成功"},{tab:5,url:r+"&status=0"}),setTimeout((function(){location.href=a.mwebUrl+"&redirect_url="+window.location.protocol+"//"+window.location.host+r+"&status=1"}),100);break}})).catch((function(t){return e.hideLoading(),s.$util.Tips({title:t})}))},getPayType:function(t,i,n,s){var r=this,o="/pages/order_pay_status/index?order_id="+i+"&msg="+n;switch(t){case"ORDER_EXIST":case"EXTEND_ORDER":case"PAY_ERROR":return e.hideLoading(),r.$util.Tips({title:n},{tab:5,url:o});case"SUCCESS":return e.hideLoading(),r.orderInfoVo.bargainId||r.orderInfoVo.combinationId||r.pinkId||r.orderInfoVo.seckillId?r.$util.Tips({title:n,icon:"success"},{tab:4,url:o}):r.$util.Tips({title:n,icon:"success"},{tab:5,url:o});case"WECHAT_PAY":r.toPay=!0;var a="prepay_id="+s.prepayId;e.requestPayment({timeStamp:s.timeStamp.toString(),nonceStr:s.nonceStr,package:a,signType:s.signType,paySign:s.paySign,success:function(t){return e.hideLoading(),r.orderInfoVo.bargainId||r.orderInfoVo.combinationId||r.pinkId||r.orderInfoVo.seckillId?r.$util.Tips({title:"支付成功",icon:"success"},{tab:4,url:o}):r.$util.Tips({title:"支付成功",icon:"success"},{tab:5,url:o})},fail:function(t){return e.hideLoading(),r.$util.Tips({title:"取消支付"},{tab:5,url:o+"&status=0"})},complete:function(t){if(e.hideLoading(),"requestPayment:cancel"==res.errMsg)return r.$util.Tips({title:"取消支付"},{tab:5,url:o+"&status=0"})}});break;case"PAY_DEFICIENCY":return e.hideLoading(),r.$util.Tips({title:n},{tab:5,url:o+"&status=1"});case"WECHAT_H5_PAY":setTimeout((function(){var e=encodeURIComponent(location.href),t=jsConfigAgain.h5PayUrl+"&redirect_url="+e;return location.href=t,r.$util.Tips({title:"支付成功",icon:"success"},{tab:5,url:o})}),100);break}},SubOrder:function(t){var i,n=this;if(!n.payType)return n.$util.Tips({title:"请选择支付方式"});if(!n.address.addressId&&!n.shippingType)return n.$util.Tips({title:"请选择收货地址"});if(1==n.shippingType){if(""==n.contacts||""==n.contactsTel)return n.$util.Tips({title:"请填写联系人或联系人电话"});if(!/^1(3|4|5|7|8|9|6)\d{9}$/.test(n.contactsTel))return n.$util.Tips({title:"请填写正确的手机号"});if(!/^[\u4e00-\u9fa5\w]{2,16}$/.test(n.contacts))return n.$util.Tips({title:"请填写您的真实姓名"});if(0==n.storeList.length)return n.$util.Tips({title:"暂无门店,请选择其他方式"})}if(i={realName:n.contacts,phone:n.contactsTel,addressId:n.address.addressId,couponId:n.couponId,payType:n.payType,useIntegral:n.useIntegral,preOrderNo:n.preOrderNo,mark:n.mark,type:n.type,userBraceletId:n.userBraceletId,storeId:n.system_store.id||0,shippingType:n.$util.$h.Add(n.shippingType,1),payChannel:n.payChannel||"weixinh5"},"yue"==i.payType&&parseFloat(n.userInfo.nowMoney)<parseFloat(n.totalPrice))return n.$util.Tips({title:"余额不足！"});e.showLoading({title:"订单支付中"}),(0,r.openPaySubscribe)().then((function(){n.payment(i)}))}}};t.default=u}).call(this,i("df3c")["default"])}},[["a1e7","common/runtime","common/vendor"]]]);