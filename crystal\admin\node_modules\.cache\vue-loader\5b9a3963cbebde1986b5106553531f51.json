{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\creatStore\\braceletsIndex.vue?vue&type=template&id=c58d0934&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\creatStore\\braceletsIndex.vue", "mtime": 1753666157922}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"divBox\" },\n    [\n      _c(\n        \"el-card\",\n        { staticClass: \"box-card\" },\n        [\n          _c(\n            \"el-form\",\n            {\n              directives: [\n                {\n                  name: \"loading\",\n                  rawName: \"v-loading\",\n                  value: _vm.fullscreenLoading,\n                  expression: \"fullscreenLoading\",\n                },\n              ],\n              ref: \"formValidate\",\n              staticClass: \"formValidate mt20\",\n              attrs: {\n                rules: _vm.ruleValidate,\n                model: _vm.formValidate,\n                \"label-width\": \"120px\",\n              },\n              nativeOn: {\n                submit: function ($event) {\n                  $event.preventDefault()\n                },\n              },\n            },\n            [\n              _c(\n                \"el-row\",\n                {\n                  directives: [\n                    {\n                      name: \"show\",\n                      rawName: \"v-show\",\n                      value: _vm.currentTab === 0,\n                      expression: \"currentTab === 0\",\n                    },\n                  ],\n                  attrs: { gutter: 24 },\n                },\n                [\n                  _c(\n                    \"el-col\",\n                    _vm._b({}, \"el-col\", _vm.grid2, false),\n                    [\n                      _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"珠子名称：\", prop: \"storeName\" } },\n                        [\n                          _c(\"el-input\", {\n                            attrs: {\n                              maxlength: \"249\",\n                              placeholder: \"请输入珠子名称\",\n                              disabled: _vm.isDisabled,\n                            },\n                            model: {\n                              value: _vm.formValidate.storeName,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.formValidate, \"storeName\", $$v)\n                              },\n                              expression: \"formValidate.storeName\",\n                            },\n                          }),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-col\",\n                    _vm._b({}, \"el-col\", _vm.grid2, false),\n                    [\n                      _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"珠子分类：\", prop: \"cateIds\" } },\n                        [\n                          _c(\"el-cascader\", {\n                            staticClass: \"selWidth\",\n                            attrs: {\n                              options: _vm.merCateList,\n                              props: _vm.props2,\n                              clearable: \"\",\n                              \"show-all-levels\": false,\n                              disabled: _vm.isDisabled,\n                            },\n                            model: {\n                              value: _vm.formValidate.cateIds,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.formValidate, \"cateIds\", $$v)\n                              },\n                              expression: \"formValidate.cateIds\",\n                            },\n                          }),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-col\",\n                    _vm._b({}, \"el-col\", _vm.grid2, false),\n                    [\n                      _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"宽度：\", prop: \"width\" } },\n                        [\n                          _c(\"el-input\", {\n                            attrs: {\n                              placeholder: \"请输入宽度\",\n                              disabled: _vm.isDisabled,\n                            },\n                            model: {\n                              value: _vm.formValidate.width,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.formValidate, \"width\", $$v)\n                              },\n                              expression: \"formValidate.width\",\n                            },\n                          }),\n                          _vm._v(\" \"),\n                          _c(\"div\", { staticStyle: { color: \"red\" } }, [\n                            _vm._v(\"单位：mm\"),\n                          ]),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-col\",\n                    _vm._b({}, \"el-col\", _vm.grid2, false),\n                    [\n                      _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"高度：\", prop: \"height\" } },\n                        [\n                          _c(\"el-input\", {\n                            attrs: {\n                              placeholder: \"请输入高度\",\n                              disabled: _vm.isDisabled,\n                            },\n                            model: {\n                              value: _vm.formValidate.height,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.formValidate, \"height\", $$v)\n                              },\n                              expression: \"formValidate.height\",\n                            },\n                          }),\n                          _vm._v(\" \"),\n                          _c(\"div\", { staticStyle: { color: \"red\" } }, [\n                            _vm._v(\"单位：mm\"),\n                          ]),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-col\",\n                    _vm._b({}, \"el-col\", _vm.grid2, false),\n                    [\n                      _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"珠子简介：\", prop: \"storeInfo\" } },\n                        [\n                          _c(\"el-input\", {\n                            attrs: {\n                              type: \"textarea\",\n                              maxlength: \"250\",\n                              rows: 3,\n                              placeholder: \"请输入珠子简介\",\n                              disabled: _vm.isDisabled,\n                            },\n                            model: {\n                              value: _vm.formValidate.storeInfo,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.formValidate, \"storeInfo\", $$v)\n                              },\n                              expression: \"formValidate.storeInfo\",\n                            },\n                          }),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-col\",\n                    _vm._b({}, \"el-col\", _vm.grid2, false),\n                    [\n                      _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"珠子封面图：\", prop: \"image\" } },\n                        [\n                          _c(\n                            \"div\",\n                            {\n                              staticClass: \"upLoadPicBox\",\n                              attrs: { disabled: _vm.isDisabled },\n                              on: {\n                                click: function ($event) {\n                                  return _vm.modalPicTap(\"1\")\n                                },\n                              },\n                            },\n                            [\n                              _vm.formValidate.image\n                                ? _c(\"div\", { staticClass: \"pictrue\" }, [\n                                    _c(\"img\", {\n                                      attrs: { src: _vm.formValidate.image },\n                                    }),\n                                  ])\n                                : _c(\"div\", { staticClass: \"upLoad\" }, [\n                                    _c(\"i\", {\n                                      staticClass:\n                                        \"el-icon-camera cameraIconfont\",\n                                    }),\n                                  ]),\n                            ]\n                          ),\n                        ]\n                      ),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-col\",\n                    { attrs: { span: 24 } },\n                    [\n                      _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"佣金设置：\", props: \"isSub\" } },\n                        [\n                          _c(\n                            \"el-radio-group\",\n                            {\n                              attrs: { disabled: _vm.isDisabled },\n                              on: {\n                                change: function ($event) {\n                                  return _vm.onChangetype(\n                                    _vm.formValidate.isSub\n                                  )\n                                },\n                              },\n                              model: {\n                                value: _vm.formValidate.isSub,\n                                callback: function ($$v) {\n                                  _vm.$set(_vm.formValidate, \"isSub\", $$v)\n                                },\n                                expression: \"formValidate.isSub\",\n                              },\n                            },\n                            [\n                              _c(\n                                \"el-radio\",\n                                {\n                                  staticClass: \"radio\",\n                                  attrs: { label: true },\n                                },\n                                [_vm._v(\"单独设置\")]\n                              ),\n                              _vm._v(\" \"),\n                              _c(\"el-radio\", { attrs: { label: false } }, [\n                                _vm._v(\"默认设置\"),\n                              ]),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _vm.formValidate.specType && !_vm.isDisabled\n                    ? _c(\n                        \"el-col\",\n                        { staticClass: \"noForm\", attrs: { span: 24 } },\n                        [\n                          _c(\n                            \"el-form-item\",\n                            { attrs: { label: \"选择规格：\", prop: \"\" } },\n                            [\n                              _c(\n                                \"div\",\n                                { staticClass: \"acea-row\" },\n                                [\n                                  _c(\n                                    \"el-select\",\n                                    {\n                                      model: {\n                                        value: _vm.formValidate.selectRule,\n                                        callback: function ($$v) {\n                                          _vm.$set(\n                                            _vm.formValidate,\n                                            \"selectRule\",\n                                            $$v\n                                          )\n                                        },\n                                        expression: \"formValidate.selectRule\",\n                                      },\n                                    },\n                                    _vm._l(_vm.ruleList, function (item) {\n                                      return _c(\"el-option\", {\n                                        key: item.id,\n                                        attrs: {\n                                          label: item.ruleName,\n                                          value: item.id,\n                                        },\n                                      })\n                                    }),\n                                    1\n                                  ),\n                                  _vm._v(\" \"),\n                                  _c(\n                                    \"el-button\",\n                                    {\n                                      staticClass: \"mr20\",\n                                      attrs: { type: \"primary\" },\n                                      on: { click: _vm.confirm },\n                                    },\n                                    [_vm._v(\"确认\")]\n                                  ),\n                                  _vm._v(\" \"),\n                                  _c(\n                                    \"el-button\",\n                                    {\n                                      staticClass: \"mr15\",\n                                      on: { click: _vm.addRule },\n                                    },\n                                    [_vm._v(\"添加规格\")]\n                                  ),\n                                ],\n                                1\n                              ),\n                            ]\n                          ),\n                          _vm._v(\" \"),\n                          _c(\n                            \"el-form-item\",\n                            _vm._l(\n                              _vm.formValidate.attr,\n                              function (item, index) {\n                                return _c(\"div\", { key: index }, [\n                                  _c(\n                                    \"div\",\n                                    { staticClass: \"acea-row row-middle\" },\n                                    [\n                                      _c(\"span\", { staticClass: \"mr5\" }, [\n                                        _vm._v(_vm._s(item.attrName)),\n                                      ]),\n                                      _c(\"i\", {\n                                        staticClass: \"el-icon-circle-close\",\n                                        on: {\n                                          click: function ($event) {\n                                            return _vm.handleRemoveAttr(index)\n                                          },\n                                        },\n                                      }),\n                                    ]\n                                  ),\n                                  _vm._v(\" \"),\n                                  _c(\n                                    \"div\",\n                                    { staticClass: \"rulesBox\" },\n                                    [\n                                      _vm._l(\n                                        item.attrValue,\n                                        function (j, indexn) {\n                                          return _c(\n                                            \"el-tag\",\n                                            {\n                                              key: indexn,\n                                              staticClass: \"mb5 mr10\",\n                                              attrs: {\n                                                closable: \"\",\n                                                size: \"medium\",\n                                                \"disable-transitions\": false,\n                                              },\n                                              on: {\n                                                close: function ($event) {\n                                                  return _vm.handleClose(\n                                                    item.attrValue,\n                                                    indexn\n                                                  )\n                                                },\n                                              },\n                                            },\n                                            [\n                                              _vm._v(\n                                                \"\\n                  \" +\n                                                  _vm._s(j) +\n                                                  \"\\n                \"\n                                              ),\n                                            ]\n                                          )\n                                        }\n                                      ),\n                                      _vm._v(\" \"),\n                                      item.inputVisible\n                                        ? _c(\"el-input\", {\n                                            ref: \"saveTagInput\",\n                                            refInFor: true,\n                                            staticClass: \"input-new-tag\",\n                                            attrs: { size: \"small\" },\n                                            on: {\n                                              blur: function ($event) {\n                                                return _vm.createAttr(\n                                                  item.attrValue.attrsVal,\n                                                  index\n                                                )\n                                              },\n                                            },\n                                            nativeOn: {\n                                              keyup: function ($event) {\n                                                if (\n                                                  !$event.type.indexOf(\"key\") &&\n                                                  _vm._k(\n                                                    $event.keyCode,\n                                                    \"enter\",\n                                                    13,\n                                                    $event.key,\n                                                    \"Enter\"\n                                                  )\n                                                ) {\n                                                  return null\n                                                }\n                                                return _vm.createAttr(\n                                                  item.attrValue.attrsVal,\n                                                  index\n                                                )\n                                              },\n                                            },\n                                            model: {\n                                              value: item.attrValue.attrsVal,\n                                              callback: function ($$v) {\n                                                _vm.$set(\n                                                  item.attrValue,\n                                                  \"attrsVal\",\n                                                  $$v\n                                                )\n                                              },\n                                              expression:\n                                                \"item.attrValue.attrsVal\",\n                                            },\n                                          })\n                                        : _c(\n                                            \"el-button\",\n                                            {\n                                              staticClass: \"button-new-tag\",\n                                              attrs: { size: \"small\" },\n                                              on: {\n                                                click: function ($event) {\n                                                  return _vm.showInput(item)\n                                                },\n                                              },\n                                            },\n                                            [_vm._v(\"+ 添加\")]\n                                          ),\n                                    ],\n                                    2\n                                  ),\n                                ])\n                              }\n                            ),\n                            0\n                          ),\n                          _vm._v(\" \"),\n                          _vm.isBtn\n                            ? _c(\n                                \"el-col\",\n                                [\n                                  _c(\n                                    \"el-col\",\n                                    {\n                                      attrs: {\n                                        xl: 6,\n                                        lg: 9,\n                                        md: 9,\n                                        sm: 24,\n                                        xs: 24,\n                                      },\n                                    },\n                                    [\n                                      _c(\n                                        \"el-form-item\",\n                                        { attrs: { label: \"规格：\" } },\n                                        [\n                                          _c(\"el-input\", {\n                                            attrs: {\n                                              placeholder: \"请输入规格\",\n                                            },\n                                            model: {\n                                              value: _vm.formDynamic.attrsName,\n                                              callback: function ($$v) {\n                                                _vm.$set(\n                                                  _vm.formDynamic,\n                                                  \"attrsName\",\n                                                  $$v\n                                                )\n                                              },\n                                              expression:\n                                                \"formDynamic.attrsName\",\n                                            },\n                                          }),\n                                        ],\n                                        1\n                                      ),\n                                    ],\n                                    1\n                                  ),\n                                  _vm._v(\" \"),\n                                  _c(\n                                    \"el-col\",\n                                    {\n                                      attrs: {\n                                        xl: 6,\n                                        lg: 9,\n                                        md: 9,\n                                        sm: 24,\n                                        xs: 24,\n                                      },\n                                    },\n                                    [\n                                      _c(\n                                        \"el-form-item\",\n                                        { attrs: { label: \"规格值：\" } },\n                                        [\n                                          _c(\"el-input\", {\n                                            attrs: {\n                                              placeholder: \"请输入规格值\",\n                                            },\n                                            model: {\n                                              value: _vm.formDynamic.attrsVal,\n                                              callback: function ($$v) {\n                                                _vm.$set(\n                                                  _vm.formDynamic,\n                                                  \"attrsVal\",\n                                                  $$v\n                                                )\n                                              },\n                                              expression:\n                                                \"formDynamic.attrsVal\",\n                                            },\n                                          }),\n                                        ],\n                                        1\n                                      ),\n                                    ],\n                                    1\n                                  ),\n                                  _vm._v(\" \"),\n                                  _c(\n                                    \"el-col\",\n                                    {\n                                      attrs: {\n                                        xl: 12,\n                                        lg: 6,\n                                        md: 6,\n                                        sm: 24,\n                                        xs: 24,\n                                      },\n                                    },\n                                    [\n                                      _c(\n                                        \"el-form-item\",\n                                        { staticClass: \"noLeft\" },\n                                        [\n                                          _c(\n                                            \"el-button\",\n                                            {\n                                              staticClass: \"mr15\",\n                                              attrs: { type: \"primary\" },\n                                              on: { click: _vm.createAttrName },\n                                            },\n                                            [_vm._v(\"确定\")]\n                                          ),\n                                          _vm._v(\" \"),\n                                          _c(\n                                            \"el-button\",\n                                            { on: { click: _vm.offAttrName } },\n                                            [_vm._v(\"取消\")]\n                                          ),\n                                        ],\n                                        1\n                                      ),\n                                    ],\n                                    1\n                                  ),\n                                ],\n                                1\n                              )\n                            : _vm._e(),\n                          _vm._v(\" \"),\n                          !_vm.isBtn\n                            ? _c(\n                                \"el-form-item\",\n                                [\n                                  _c(\n                                    \"el-button\",\n                                    {\n                                      staticClass: \"mr15\",\n                                      attrs: {\n                                        type: \"primary\",\n                                        icon: \"md-add\",\n                                      },\n                                      on: { click: _vm.addBtn },\n                                    },\n                                    [_vm._v(\"添加新规格\")]\n                                  ),\n                                ],\n                                1\n                              )\n                            : _vm._e(),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.formValidate.attr.length > 0 &&\n                  _vm.formValidate.specType &&\n                  !_vm.isDisabled\n                    ? _c(\n                        \"el-col\",\n                        { staticClass: \"noForm\", attrs: { span: 24 } },\n                        [\n                          _c(\n                            \"el-form-item\",\n                            { attrs: { label: \"批量设置：\" } },\n                            [\n                              _c(\n                                \"el-table\",\n                                {\n                                  staticClass: \"tabNumWidth\",\n                                  attrs: {\n                                    data: _vm.oneFormBatch,\n                                    border: \"\",\n                                    size: \"mini\",\n                                  },\n                                },\n                                [\n                                  _c(\"el-table-column\", {\n                                    attrs: {\n                                      align: \"center\",\n                                      label: \"图片\",\n                                      \"min-width\": \"80\",\n                                    },\n                                    scopedSlots: _vm._u(\n                                      [\n                                        {\n                                          key: \"default\",\n                                          fn: function (scope) {\n                                            return [\n                                              _c(\n                                                \"div\",\n                                                {\n                                                  staticClass: \"upLoadPicBox\",\n                                                  on: {\n                                                    click: function ($event) {\n                                                      return _vm.modalPicTap(\n                                                        \"1\",\n                                                        \"pi\"\n                                                      )\n                                                    },\n                                                  },\n                                                },\n                                                [\n                                                  scope.row.image\n                                                    ? _c(\n                                                        \"div\",\n                                                        {\n                                                          staticClass:\n                                                            \"pictrue tabPic\",\n                                                        },\n                                                        [\n                                                          _c(\"img\", {\n                                                            attrs: {\n                                                              src: scope.row\n                                                                .image,\n                                                            },\n                                                          }),\n                                                        ]\n                                                      )\n                                                    : _c(\n                                                        \"div\",\n                                                        {\n                                                          staticClass:\n                                                            \"upLoad tabPic\",\n                                                        },\n                                                        [\n                                                          _c(\"i\", {\n                                                            staticClass:\n                                                              \"el-icon-camera cameraIconfont\",\n                                                          }),\n                                                        ]\n                                                      ),\n                                                ]\n                                              ),\n                                            ]\n                                          },\n                                        },\n                                      ],\n                                      null,\n                                      false,\n                                      3302088318\n                                    ),\n                                  }),\n                                  _vm._v(\" \"),\n                                  _vm._l(_vm.attrValue, function (item, iii) {\n                                    return _c(\"el-table-column\", {\n                                      key: iii,\n                                      attrs: {\n                                        label: _vm.formThead[iii].title,\n                                        align: \"center\",\n                                        \"min-width\": \"120\",\n                                      },\n                                      scopedSlots: _vm._u(\n                                        [\n                                          {\n                                            key: \"default\",\n                                            fn: function (scope) {\n                                              return [\n                                                _c(\"el-input\", {\n                                                  staticClass: \"priceBox\",\n                                                  attrs: {\n                                                    maxlength: \"9\",\n                                                    min: \"0.01\",\n                                                  },\n                                                  on: {\n                                                    blur: function ($event) {\n                                                      return _vm.keyupEvent(\n                                                        iii,\n                                                        scope.row[iii],\n                                                        scope.$index,\n                                                        1\n                                                      )\n                                                    },\n                                                  },\n                                                  model: {\n                                                    value: scope.row[iii],\n                                                    callback: function ($$v) {\n                                                      _vm.$set(\n                                                        scope.row,\n                                                        iii,\n                                                        $$v\n                                                      )\n                                                    },\n                                                    expression:\n                                                      \"scope.row[iii]\",\n                                                  },\n                                                }),\n                                              ]\n                                            },\n                                          },\n                                        ],\n                                        null,\n                                        true\n                                      ),\n                                    })\n                                  }),\n                                  _vm._v(\" \"),\n                                  _vm.formValidate.isSub\n                                    ? [\n                                        _c(\"el-table-column\", {\n                                          attrs: {\n                                            align: \"center\",\n                                            label: \"一级返佣(元)\",\n                                            \"min-width\": \"120\",\n                                          },\n                                          scopedSlots: _vm._u(\n                                            [\n                                              {\n                                                key: \"default\",\n                                                fn: function (scope) {\n                                                  return [\n                                                    _c(\"el-input\", {\n                                                      staticClass: \"priceBox\",\n                                                      attrs: {\n                                                        type: \"number\",\n                                                        min: 0,\n                                                        max: scope.row.price,\n                                                      },\n                                                      model: {\n                                                        value:\n                                                          scope.row.brokerage,\n                                                        callback: function (\n                                                          $$v\n                                                        ) {\n                                                          _vm.$set(\n                                                            scope.row,\n                                                            \"brokerage\",\n                                                            $$v\n                                                          )\n                                                        },\n                                                        expression:\n                                                          \"scope.row.brokerage\",\n                                                      },\n                                                    }),\n                                                  ]\n                                                },\n                                              },\n                                            ],\n                                            null,\n                                            false,\n                                            99765219\n                                          ),\n                                        }),\n                                        _vm._v(\" \"),\n                                        _c(\"el-table-column\", {\n                                          attrs: {\n                                            align: \"center\",\n                                            label: \"二级返佣(元)\",\n                                            \"min-width\": \"120\",\n                                          },\n                                          scopedSlots: _vm._u(\n                                            [\n                                              {\n                                                key: \"default\",\n                                                fn: function (scope) {\n                                                  return [\n                                                    _c(\"el-input\", {\n                                                      staticClass: \"priceBox\",\n                                                      attrs: {\n                                                        type: \"number\",\n                                                        min: 0,\n                                                        max: scope.row.price,\n                                                      },\n                                                      model: {\n                                                        value:\n                                                          scope.row\n                                                            .brokerageTwo,\n                                                        callback: function (\n                                                          $$v\n                                                        ) {\n                                                          _vm.$set(\n                                                            scope.row,\n                                                            \"brokerageTwo\",\n                                                            $$v\n                                                          )\n                                                        },\n                                                        expression:\n                                                          \"scope.row.brokerageTwo\",\n                                                      },\n                                                    }),\n                                                  ]\n                                                },\n                                              },\n                                            ],\n                                            null,\n                                            false,\n                                            1573915567\n                                          ),\n                                        }),\n                                      ]\n                                    : _vm._e(),\n                                  _vm._v(\" \"),\n                                  _c(\n                                    \"el-table-column\",\n                                    {\n                                      attrs: {\n                                        align: \"center\",\n                                        label: \"操作\",\n                                        \"min-width\": \"80\",\n                                      },\n                                    },\n                                    [\n                                      [\n                                        _c(\n                                          \"el-button\",\n                                          {\n                                            staticClass: \"submission\",\n                                            attrs: { type: \"text\" },\n                                            on: { click: _vm.batchAdd },\n                                          },\n                                          [_vm._v(\"批量添加\")]\n                                        ),\n                                      ],\n                                    ],\n                                    2\n                                  ),\n                                ],\n                                2\n                              ),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-col\",\n                    { attrs: { xl: 24, lg: 24, md: 24, sm: 24, xs: 24 } },\n                    [\n                      _vm.formValidate.specType === false\n                        ? _c(\n                            \"el-form-item\",\n                            [\n                              _c(\n                                \"el-table\",\n                                {\n                                  staticClass: \"tabNumWidth\",\n                                  attrs: {\n                                    data: _vm.OneattrValue,\n                                    border: \"\",\n                                    size: \"mini\",\n                                  },\n                                },\n                                [\n                                  _c(\"el-table-column\", {\n                                    attrs: {\n                                      align: \"center\",\n                                      label: \"图片\",\n                                      \"min-width\": \"80\",\n                                    },\n                                    scopedSlots: _vm._u(\n                                      [\n                                        {\n                                          key: \"default\",\n                                          fn: function (scope) {\n                                            return [\n                                              _c(\n                                                \"div\",\n                                                {\n                                                  staticClass: \"upLoadPicBox\",\n                                                  on: {\n                                                    click: function ($event) {\n                                                      return _vm.modalPicTap(\n                                                        \"1\",\n                                                        \"dan\",\n                                                        \"pi\"\n                                                      )\n                                                    },\n                                                  },\n                                                },\n                                                [\n                                                  _vm.formValidate.image\n                                                    ? _c(\n                                                        \"div\",\n                                                        {\n                                                          staticClass:\n                                                            \"pictrue tabPic\",\n                                                        },\n                                                        [\n                                                          _c(\"img\", {\n                                                            attrs: {\n                                                              src: scope.row\n                                                                .image,\n                                                            },\n                                                          }),\n                                                        ]\n                                                      )\n                                                    : _c(\n                                                        \"div\",\n                                                        {\n                                                          staticClass:\n                                                            \"upLoad tabPic\",\n                                                        },\n                                                        [\n                                                          _c(\"i\", {\n                                                            staticClass:\n                                                              \"el-icon-camera cameraIconfont\",\n                                                          }),\n                                                        ]\n                                                      ),\n                                                ]\n                                              ),\n                                            ]\n                                          },\n                                        },\n                                      ],\n                                      null,\n                                      false,\n                                      1357914119\n                                    ),\n                                  }),\n                                  _vm._v(\" \"),\n                                  _vm._l(_vm.attrValue, function (item, iii) {\n                                    return _c(\"el-table-column\", {\n                                      key: iii,\n                                      attrs: {\n                                        label: _vm.formThead[iii].title,\n                                        align: \"center\",\n                                        \"min-width\": \"120\",\n                                      },\n                                      scopedSlots: _vm._u(\n                                        [\n                                          {\n                                            key: \"default\",\n                                            fn: function (scope) {\n                                              return [\n                                                _c(\"el-input\", {\n                                                  staticClass: \"priceBox\",\n                                                  attrs: {\n                                                    disabled: _vm.isDisabled,\n                                                    maxlength: \"9\",\n                                                    min: \"0.01\",\n                                                  },\n                                                  on: {\n                                                    blur: function ($event) {\n                                                      return _vm.keyupEvent(\n                                                        iii,\n                                                        scope.row[iii],\n                                                        scope.$index,\n                                                        2\n                                                      )\n                                                    },\n                                                  },\n                                                  model: {\n                                                    value: scope.row[iii],\n                                                    callback: function ($$v) {\n                                                      _vm.$set(\n                                                        scope.row,\n                                                        iii,\n                                                        $$v\n                                                      )\n                                                    },\n                                                    expression:\n                                                      \"scope.row[iii]\",\n                                                  },\n                                                }),\n                                              ]\n                                            },\n                                          },\n                                        ],\n                                        null,\n                                        true\n                                      ),\n                                    })\n                                  }),\n                                  _vm._v(\" \"),\n                                  _vm.formValidate.isSub\n                                    ? [\n                                        _c(\"el-table-column\", {\n                                          attrs: {\n                                            align: \"center\",\n                                            label: \"一级返佣(元)\",\n                                            \"min-width\": \"120\",\n                                          },\n                                          scopedSlots: _vm._u(\n                                            [\n                                              {\n                                                key: \"default\",\n                                                fn: function (scope) {\n                                                  return [\n                                                    _c(\"el-input\", {\n                                                      staticClass: \"priceBox\",\n                                                      attrs: {\n                                                        disabled:\n                                                          _vm.isDisabled,\n                                                        type: \"number\",\n                                                        min: 0,\n                                                      },\n                                                      model: {\n                                                        value:\n                                                          scope.row.brokerage,\n                                                        callback: function (\n                                                          $$v\n                                                        ) {\n                                                          _vm.$set(\n                                                            scope.row,\n                                                            \"brokerage\",\n                                                            $$v\n                                                          )\n                                                        },\n                                                        expression:\n                                                          \"scope.row.brokerage\",\n                                                      },\n                                                    }),\n                                                  ]\n                                                },\n                                              },\n                                            ],\n                                            null,\n                                            false,\n                                            3549059968\n                                          ),\n                                        }),\n                                        _vm._v(\" \"),\n                                        _c(\"el-table-column\", {\n                                          attrs: {\n                                            align: \"center\",\n                                            label: \"二级返佣(元)\",\n                                            \"min-width\": \"120\",\n                                          },\n                                          scopedSlots: _vm._u(\n                                            [\n                                              {\n                                                key: \"default\",\n                                                fn: function (scope) {\n                                                  return [\n                                                    _c(\"el-input\", {\n                                                      staticClass: \"priceBox\",\n                                                      attrs: {\n                                                        disabled:\n                                                          _vm.isDisabled,\n                                                        type: \"number\",\n                                                        min: 0,\n                                                      },\n                                                      model: {\n                                                        value:\n                                                          scope.row\n                                                            .brokerageTwo,\n                                                        callback: function (\n                                                          $$v\n                                                        ) {\n                                                          _vm.$set(\n                                                            scope.row,\n                                                            \"brokerageTwo\",\n                                                            $$v\n                                                          )\n                                                        },\n                                                        expression:\n                                                          \"scope.row.brokerageTwo\",\n                                                      },\n                                                    }),\n                                                  ]\n                                                },\n                                              },\n                                            ],\n                                            null,\n                                            false,\n                                            342384460\n                                          ),\n                                        }),\n                                      ]\n                                    : _vm._e(),\n                                ],\n                                2\n                              ),\n                            ],\n                            1\n                          )\n                        : _vm._e(),\n                      _vm._v(\" \"),\n                      _vm.$route.params.id && _vm.showAll\n                        ? _c(\n                            \"el-form-item\",\n                            { attrs: { label: \"全部sku：\" } },\n                            [\n                              _c(\n                                \"el-button\",\n                                {\n                                  attrs: {\n                                    type: \"default\",\n                                    disabled: _vm.isDisabled,\n                                  },\n                                  on: {\n                                    click: function ($event) {\n                                      return _vm.showAllSku()\n                                    },\n                                  },\n                                },\n                                [_vm._v(\"展示\")]\n                              ),\n                            ],\n                            1\n                          )\n                        : _vm._e(),\n                      _vm._v(\" \"),\n                      _vm.formValidate.attr.length > 0 &&\n                      _vm.formValidate.specType\n                        ? _c(\n                            \"el-form-item\",\n                            {\n                              staticClass: \"labeltop\",\n                              class: _vm.isDisabled\n                                ? \"disLabel\"\n                                : \"disLabelmoren\",\n                              attrs: { label: \"珠子属性：\" },\n                            },\n                            [\n                              _c(\n                                \"el-table\",\n                                {\n                                  staticClass: \"tabNumWidth\",\n                                  attrs: {\n                                    data: _vm.ManyAttrValue,\n                                    border: \"\",\n                                    size: \"mini\",\n                                  },\n                                },\n                                [\n                                  _vm.manyTabDate\n                                    ? _vm._l(\n                                        _vm.manyTabDate,\n                                        function (item, iii) {\n                                          return _c(\"el-table-column\", {\n                                            key: iii,\n                                            attrs: {\n                                              align: \"center\",\n                                              label: _vm.manyTabTit[iii].title,\n                                              \"min-width\": \"80\",\n                                            },\n                                            scopedSlots: _vm._u(\n                                              [\n                                                {\n                                                  key: \"default\",\n                                                  fn: function (scope) {\n                                                    return [\n                                                      _c(\"span\", {\n                                                        staticClass: \"priceBox\",\n                                                        domProps: {\n                                                          textContent: _vm._s(\n                                                            scope.row[iii]\n                                                          ),\n                                                        },\n                                                      }),\n                                                    ]\n                                                  },\n                                                },\n                                              ],\n                                              null,\n                                              true\n                                            ),\n                                          })\n                                        }\n                                      )\n                                    : _vm._e(),\n                                  _vm._v(\" \"),\n                                  _c(\"el-table-column\", {\n                                    attrs: {\n                                      align: \"center\",\n                                      label: \"图片\",\n                                      \"min-width\": \"80\",\n                                    },\n                                    scopedSlots: _vm._u(\n                                      [\n                                        {\n                                          key: \"default\",\n                                          fn: function (scope) {\n                                            return [\n                                              _c(\n                                                \"div\",\n                                                {\n                                                  staticClass: \"upLoadPicBox\",\n                                                  on: {\n                                                    click: function ($event) {\n                                                      return _vm.modalPicTap(\n                                                        \"1\",\n                                                        \"duo\",\n                                                        scope.$index\n                                                      )\n                                                    },\n                                                  },\n                                                },\n                                                [\n                                                  scope.row.image\n                                                    ? _c(\n                                                        \"div\",\n                                                        {\n                                                          staticClass:\n                                                            \"pictrue tabPic\",\n                                                        },\n                                                        [\n                                                          _c(\"img\", {\n                                                            attrs: {\n                                                              src: scope.row\n                                                                .image,\n                                                            },\n                                                          }),\n                                                        ]\n                                                      )\n                                                    : _c(\n                                                        \"div\",\n                                                        {\n                                                          staticClass:\n                                                            \"upLoad tabPic\",\n                                                        },\n                                                        [\n                                                          _c(\"i\", {\n                                                            staticClass:\n                                                              \"el-icon-camera cameraIconfont\",\n                                                          }),\n                                                        ]\n                                                      ),\n                                                ]\n                                              ),\n                                            ]\n                                          },\n                                        },\n                                      ],\n                                      null,\n                                      false,\n                                      4035746379\n                                    ),\n                                  }),\n                                  _vm._v(\" \"),\n                                  _vm._l(_vm.attrValue, function (item, iii) {\n                                    return _c(\"el-table-column\", {\n                                      key: iii,\n                                      attrs: {\n                                        label: _vm.formThead[iii].title,\n                                        align: \"center\",\n                                        \"min-width\": \"120\",\n                                      },\n                                      scopedSlots: _vm._u(\n                                        [\n                                          {\n                                            key: \"default\",\n                                            fn: function (scope) {\n                                              return [\n                                                _c(\"el-input\", {\n                                                  staticClass: \"priceBox\",\n                                                  attrs: {\n                                                    disabled: _vm.isDisabled,\n                                                    maxlength: \"9\",\n                                                    min: \"0.01\",\n                                                  },\n                                                  on: {\n                                                    blur: function ($event) {\n                                                      return _vm.keyupEvent(\n                                                        iii,\n                                                        scope.row[iii],\n                                                        scope.$index,\n                                                        3\n                                                      )\n                                                    },\n                                                  },\n                                                  model: {\n                                                    value: scope.row[iii],\n                                                    callback: function ($$v) {\n                                                      _vm.$set(\n                                                        scope.row,\n                                                        iii,\n                                                        $$v\n                                                      )\n                                                    },\n                                                    expression:\n                                                      \"scope.row[iii]\",\n                                                  },\n                                                }),\n                                              ]\n                                            },\n                                          },\n                                        ],\n                                        null,\n                                        true\n                                      ),\n                                    })\n                                  }),\n                                  _vm._v(\" \"),\n                                  _vm.formValidate.isSub\n                                    ? _c(\"el-table-column\", {\n                                        attrs: {\n                                          align: \"center\",\n                                          label: \"一级返佣(元)\",\n                                          \"min-width\": \"120\",\n                                        },\n                                        scopedSlots: _vm._u(\n                                          [\n                                            {\n                                              key: \"default\",\n                                              fn: function (scope) {\n                                                return [\n                                                  _c(\"el-input\", {\n                                                    staticClass: \"priceBox\",\n                                                    attrs: {\n                                                      disabled: _vm.isDisabled,\n                                                      type: \"number\",\n                                                      min: 0,\n                                                      max: scope.row.price,\n                                                    },\n                                                    model: {\n                                                      value:\n                                                        scope.row.brokerage,\n                                                      callback: function ($$v) {\n                                                        _vm.$set(\n                                                          scope.row,\n                                                          \"brokerage\",\n                                                          $$v\n                                                        )\n                                                      },\n                                                      expression:\n                                                        \"scope.row.brokerage\",\n                                                    },\n                                                  }),\n                                                ]\n                                              },\n                                            },\n                                          ],\n                                          null,\n                                          false,\n                                          2857277871\n                                        ),\n                                      })\n                                    : _vm._e(),\n                                  _vm._v(\" \"),\n                                  _vm.formValidate.isSub\n                                    ? _c(\"el-table-column\", {\n                                        attrs: {\n                                          align: \"center\",\n                                          label: \"二级返佣(元)\",\n                                          \"min-width\": \"120\",\n                                        },\n                                        scopedSlots: _vm._u(\n                                          [\n                                            {\n                                              key: \"default\",\n                                              fn: function (scope) {\n                                                return [\n                                                  _c(\"el-input\", {\n                                                    staticClass: \"priceBox\",\n                                                    attrs: {\n                                                      disabled: _vm.isDisabled,\n                                                      type: \"number\",\n                                                      min: 0,\n                                                      max: scope.row.price,\n                                                    },\n                                                    model: {\n                                                      value:\n                                                        scope.row.brokerageTwo,\n                                                      callback: function ($$v) {\n                                                        _vm.$set(\n                                                          scope.row,\n                                                          \"brokerageTwo\",\n                                                          $$v\n                                                        )\n                                                      },\n                                                      expression:\n                                                        \"scope.row.brokerageTwo\",\n                                                    },\n                                                  }),\n                                                ]\n                                              },\n                                            },\n                                          ],\n                                          null,\n                                          false,\n                                          3609981283\n                                        ),\n                                      })\n                                    : _vm._e(),\n                                  _vm._v(\" \"),\n                                  !_vm.isDisabled\n                                    ? _c(\"el-table-column\", {\n                                        key: \"3\",\n                                        attrs: {\n                                          align: \"center\",\n                                          label: \"操作\",\n                                          \"min-width\": \"80\",\n                                        },\n                                        scopedSlots: _vm._u(\n                                          [\n                                            {\n                                              key: \"default\",\n                                              fn: function (scope) {\n                                                return [\n                                                  _c(\n                                                    \"el-button\",\n                                                    {\n                                                      staticClass: \"submission\",\n                                                      attrs: { type: \"text\" },\n                                                      on: {\n                                                        click: function (\n                                                          $event\n                                                        ) {\n                                                          return _vm.delAttrTable(\n                                                            scope.$index\n                                                          )\n                                                        },\n                                                      },\n                                                    },\n                                                    [_vm._v(\"删除\")]\n                                                  ),\n                                                ]\n                                              },\n                                            },\n                                          ],\n                                          null,\n                                          false,\n                                          2803824461\n                                        ),\n                                      })\n                                    : _vm._e(),\n                                ],\n                                2\n                              ),\n                            ],\n                            1\n                          )\n                        : _vm._e(),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-col\",\n                    _vm._b({}, \"el-col\", _vm.grid, false),\n                    [\n                      _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"排序：\" } },\n                        [\n                          _c(\"el-input-number\", {\n                            attrs: {\n                              min: 0,\n                              placeholder: \"请输入排序\",\n                              disabled: _vm.isDisabled,\n                            },\n                            model: {\n                              value: _vm.formValidate.sort,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.formValidate, \"sort\", $$v)\n                              },\n                              expression: \"formValidate.sort\",\n                            },\n                          }),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-col\",\n                    _vm._b({}, \"el-col\", _vm.grid, false),\n                    [\n                      _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"虚拟销量：\" } },\n                        [\n                          _c(\"el-input-number\", {\n                            attrs: {\n                              min: 0,\n                              placeholder: \"请输入虚拟销量\",\n                              disabled: _vm.isDisabled,\n                            },\n                            model: {\n                              value: _vm.formValidate.ficti,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.formValidate, \"ficti\", $$v)\n                              },\n                              expression: \"formValidate.ficti\",\n                            },\n                          }),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-col\",\n                    { attrs: { span: 24 } },\n                    [\n                      _c(\n                        \"el-form-item\",\n                        { attrs: { label: \"珠子详情：\" } },\n                        [\n                          _c(\"Tinymce\", {\n                            directives: [\n                              {\n                                name: \"show\",\n                                rawName: \"v-show\",\n                                value: !_vm.isDisabled,\n                                expression: \"!isDisabled\",\n                              },\n                            ],\n                            model: {\n                              value: _vm.formValidate.content,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.formValidate, \"content\", $$v)\n                              },\n                              expression: \"formValidate.content\",\n                            },\n                          }),\n                          _vm._v(\" \"),\n                          _c(\"span\", {\n                            directives: [\n                              {\n                                name: \"show\",\n                                rawName: \"v-show\",\n                                value: _vm.isDisabled,\n                                expression: \"isDisabled\",\n                              },\n                            ],\n                            domProps: {\n                              innerHTML: _vm._s(\n                                _vm.formValidate.content || \"无\"\n                              ),\n                            },\n                          }),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-row\",\n                {\n                  directives: [\n                    {\n                      name: \"show\",\n                      rawName: \"v-show\",\n                      value: _vm.currentTab === 2,\n                      expression: \"currentTab === 2\",\n                    },\n                  ],\n                },\n                [\n                  _c(\n                    \"el-col\",\n                    { attrs: { span: 24 } },\n                    [\n                      _c(\n                        \"el-col\",\n                        _vm._b({}, \"el-col\", _vm.grid, false),\n                        [\n                          _c(\n                            \"el-form-item\",\n                            { attrs: { label: \"排序：\" } },\n                            [\n                              _c(\"el-input-number\", {\n                                attrs: {\n                                  min: 0,\n                                  placeholder: \"请输入排序\",\n                                  disabled: _vm.isDisabled,\n                                },\n                                model: {\n                                  value: _vm.formValidate.sort,\n                                  callback: function ($$v) {\n                                    _vm.$set(_vm.formValidate, \"sort\", $$v)\n                                  },\n                                  expression: \"formValidate.sort\",\n                                },\n                              }),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-col\",\n                        _vm._b({}, \"el-col\", _vm.grid, false),\n                        [\n                          _c(\n                            \"el-form-item\",\n                            { attrs: { label: \"虚拟销量：\" } },\n                            [\n                              _c(\"el-input-number\", {\n                                attrs: {\n                                  min: 0,\n                                  placeholder: \"请输入排序\",\n                                  disabled: _vm.isDisabled,\n                                },\n                                model: {\n                                  value: _vm.formValidate.ficti,\n                                  callback: function ($$v) {\n                                    _vm.$set(_vm.formValidate, \"ficti\", $$v)\n                                  },\n                                  expression: \"formValidate.ficti\",\n                                },\n                              }),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                [\n                  _c(\n                    \"el-button\",\n                    {\n                      directives: [\n                        {\n                          name: \"show\",\n                          rawName: \"v-show\",\n                          value: !_vm.isDisabled,\n                          expression: \"!isDisabled\",\n                        },\n                      ],\n                      staticClass: \"submission\",\n                      attrs: { type: \"primary\" },\n                      on: {\n                        click: function ($event) {\n                          return _vm.handleSubmit(\"formValidate\")\n                        },\n                      },\n                    },\n                    [_vm._v(\"提交\")]\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\"CreatTemplates\", {\n        ref: \"addTemplates\",\n        on: { getList: _vm.getShippingList },\n      }),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}