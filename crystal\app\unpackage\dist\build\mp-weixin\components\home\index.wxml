<view style="touch-action:none;" class="data-v-840a3948"><view class="home data-v-840a3948" style="{{'position:fixed;'+('top:'+(top+'px')+';')}}" id="right-nav" data-event-opts="{{[['touchmove',[['setTouchMove',['$event']]]]]}}" catchtouchmove="__e"><block wx:if="{{homeActive}}"><view class="{{['homeCon','data-v-840a3948',homeActive===true?'on':'']}}"><navigator class="nav-item data-v-840a3948" hover-class="none" url="/pages/index/index" open-type="switchTab"><text class="iconfont icon-shouye-xianxing data-v-840a3948"></text><text class="nav-text data-v-840a3948">首页</text></navigator><navigator class="nav-item data-v-840a3948" hover-class="none" url="/pages/order_addcart/order_addcart" open-type="switchTab"><text class="iconfont icon-caigou-xianxing data-v-840a3948"></text><text class="nav-text data-v-840a3948">购物车</text></navigator><navigator class="nav-item data-v-840a3948" hover-class="none" url="/pages/user/index" open-type="switchTab"><text class="iconfont icon-yonghu1 data-v-840a3948"></text><text class="nav-text data-v-840a3948">我的</text></navigator></view></block><view data-event-opts="{{[['tap',[['open',['$event']]]]]}}" class="pictrueBox data-v-840a3948" bindtap="__e"><view class="{{['pictrue','data-v-840a3948',homeActive?'active':'']}}"><text class="{{['iconfont','data-v-840a3948',homeActive?'icon-guanbi':'icon-caidan']}}"></text></view></view></view></view>