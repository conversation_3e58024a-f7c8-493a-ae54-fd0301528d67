{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/user_payment/index.vue?deef", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/user_payment/index.vue?2e18", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/user_payment/index.vue?c680", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/user_payment/index.vue?32f4", "uni-app:///pages/users/user_payment/index.vue", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/user_payment/index.vue?e205", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/users/user_payment/index.vue?f8da"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "authorize", "home", "data", "now_money", "navRecharge", "active", "number", "placeholder", "from", "isAuto", "isShowAuth", "picList", "activePic", "money", "numberPic", "rechar_id", "rechargeAttention", "computed", "watch", "is<PERSON>ogin", "handler", "deep", "onLoad", "methods", "picCharge", "get<PERSON><PERSON><PERSON>ge", "then", "catch", "mes", "onLoadFun", "auth<PERSON><PERSON><PERSON>", "navRecharges", "submitSub", "title", "uni", "content", "success", "price", "that", "amount1", "amount2", "icon", "tab", "url", "type", "timeStamp", "nonceStr", "package", "signType", "paySign", "fail", "complete"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACmM;AACnM,gBAAgB,2LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvBA;AAAA;AAAA;AAAA;AAAkwB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACwDtxB;AAOA;AACA;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAOA;EACAC;IAEAC;IAEAC;EACA;EACAC;IACA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;EACAC;IACAC;MACAC;QACA;UACA;QACA;MACA;MACAC;IACA;EACA;EACAC;IAIA;MACA;IACA;MACA;IACA;EACA;EACAC;IAEA;AACA;AACA;IACAC;MACA;MACA;QACA;QACA;MACA;QACA;QACA;QACA;MACA;IACA;IAGA;AACA;AACA;IACAC;MAAA;MACA,4BACAC;QACA;QACA;UACA;UACA;QACA;QACA;MACA,GACAC;QACA;UACAC;QACA;MACA;IACA;IAGAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA;MACA;MACA;QACA;UACA;YACAC;UACA;QACA;QACAC;UACAD;UACAE;UACAC;YACA;cACA;gBACAC;cACA;gBACAC;kBACAC;kBACAC;gBACA;gBACA;kBACAP;kBACAQ;gBACA;kBACAC;kBACAC;gBACA;cACA;gBACA;kBACAV;gBACA;cACA;YACA;cACA;gBACAA;cACA;YACA;UACA;QACA;MACA;QACAC;UACAD;QACA;QACA;QACA;UACA;YACA;cACAA;YACA;UACA;UACA;YACA;cACAA;YACA;UACA;UACA;YACA;cACAA;YACA;UACA;QACA;UACApB;QACA;QAEA;UACAwB;UACAO;UACA7B;QACA;UACAmB;UACA;UACAA;YACAW;YACAC;YACAC;YACAC;YACAC;YACAb;cACAE;gBACAC;gBACAC;cACA;cACA;cACA;gBACAP;gBACAQ;cACA;gBACAC;gBACAC;cACA;YACA;YACAO;cACA;gBACAjB;cACA;YACA;YACAkB;cACA;gBACAlB;cACA;YACA;UACA;QACA;UACAC;UACA;YACAD;UACA;QACA;MAoEA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjWA;AAAA;AAAA;AAAA;AAA66C,CAAgB,4tCAAG,EAAC,C;;;;;;;;;;;ACAj8C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/users/user_payment/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/users/user_payment/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=6b0986f5&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/users/user_payment/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=6b0986f5&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = !_vm.active ? parseFloat(_vm.activePic) : null\n  var m1 = !_vm.active ? parseFloat(_vm.picList.length) : null\n  var m2 = !_vm.active ? parseFloat(_vm.activePic) : null\n  var m3 = !_vm.active ? parseFloat(_vm.picList.length) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n        m3: m3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<form @submit=\"submitSub\" report-submit='true'>\r\n\t\t\t<view class=\"payment-top acea-row row-column row-center-wrapper\">\r\n\t\t\t\t<span class=\"name\">我的余额</span>\r\n\t\t\t\t<view class=\"pic\">\r\n\t\t\t\t\t￥<span class=\"pic-font\">{{ userInfo.nowMoney || 0 }}</span>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"payment\">\r\n\t\t\t\t<view class=\"nav acea-row row-around row-middle\">\r\n\t\t\t\t\t<view class=\"item\" :class=\"active==index?'on':''\" v-for=\"(item,index) in navRecharge\" :key=\"index\" @click=\"navRecharges(index)\">{{item}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class='tip picList' v-if='!active'>\r\n\t\t\t\t\t<view class=\"pic-box pic-box-color acea-row row-center-wrapper row-column\" :class=\"activePic === index ? 'pic-box-color-active' : ''\"\r\n\t\t\t\t\t v-for=\"(item, index) in picList\" :key=\"index\" @click=\"picCharge(index, item)\">\r\n\t\t\t\t\t\t<view class=\"pic-number-pic\">\r\n\t\t\t\t\t\t\t{{ item.price }}<span class=\"pic-number\"> 元</span>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"pic-number\">赠送：{{ item.giveMoney }} 元</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"pic-box pic-box-color acea-row row-center-wrapper\" :class=\"parseFloat(activePic)===parseFloat(picList.length)?'pic-box-color-active':''\" @click=\"picCharge(picList.length)\">\r\n\t\t\t\t\t\t<input type=\"number\" placeholder=\"其他\" v-model=\"money\" :maxlength=\"50000\" class=\"pic-box-money pic-number-pic uni-input\" :class=\"parseFloat(activePic) === parseFloat(picList.length) ? 'pic-box-color-active' : ''\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"tips-box\">\r\n\t\t\t\t\t\t<view class=\"tips mt-30\">注意事项：</view>\r\n\t\t\t\t\t\t<view class=\"tips-samll\" v-for=\"item in rechargeAttention\" :key=\"item\">\r\n\t\t\t\t\t\t\t{{ item }}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"tip\" v-else>\r\n\t\t\t\t\t<view class='input'><text>￥</text><input placeholder=\"0.00\" type='number' placeholder-class='placeholder' :value=\"number\"\r\n\t\t\t\t\t\t name=\"number\"></input></view>\r\n\t\t\t\t\t<view class=\"tips-title\">\r\n\t\t\t\t\t\t<view style=\"font-weight: bold; font-size: 26rpx;\">提示：</view>\r\n\t\t\t\t\t\t<view style=\"margin-top: 10rpx;\">当前佣金为 <text class='font-color'>￥{{userInfo.brokeragePrice || 0}}</text></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"tips-box\">\r\n\t\t\t\t\t\t<view class=\"tips mt-30\">注意事项：</view>\r\n\t\t\t\t\t\t<view class=\"tips-samll\" v-for=\"item in rechargeAttention\" :key=\"item\">\r\n\t\t\t\t\t\t\t{{ item }}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<button class='but' formType=\"submit\"> {{active ? '立即转入': '立即充值' }}</button>\r\n\t\t\t</view>\r\n\t\t</form>\r\n\t\t<!-- #ifdef MP -->\r\n\t\t<!-- <authorize @onLoadFun=\"onLoadFun\" :isAuto=\"isAuto\" :isShowAuth=\"isShowAuth\" @authColse=\"authColse\"></authorize> -->\r\n\t\t<!-- #endif -->\r\n\t\t<home></home>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\trechargeRoutine,\r\n\t\trechargeWechat,\r\n\t\tgetRechargeApi,\r\n\t\ttransferIn,\r\n\t\tappWechat\r\n\t} from '@/api/user.js';\r\n\timport { wechatQueryPayResult } from '@/api/order.js';\r\n\timport {\r\n\t\ttoLogin\r\n\t} from '@/libs/login.js';\r\n\timport {\r\n\t\tmapGetters\r\n\t} from \"vuex\";\r\n\t// #ifdef MP\r\n\timport authorize from '@/components/Authorize';\r\n\t// #endif\r\n\timport home from '@/components/home';\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\t// #ifdef MP\r\n\t\t\tauthorize,\r\n\t\t\t// #endif\r\n\t\t\thome\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\tlet that = this;\r\n\t\t\treturn {\r\n\t\t\t\tnow_money: 0,\r\n\t\t\t\tnavRecharge: ['账户充值', '佣金转入'],\r\n\t\t\t\tactive: 0,\r\n\t\t\t\tnumber: '',\r\n\t\t\t\tplaceholder: \"0.00\",\r\n\t\t\t\tfrom: '',\r\n\t\t\t\tisAuto: false, //没有授权的不会自动授权\r\n\t\t\t\tisShowAuth: false, //是否隐藏授权\r\n\t\t\t\tpicList: [],\r\n\t\t\t\tactivePic: 0,\r\n\t\t\t\tmoney: \"\",\r\n\t\t\t\tnumberPic: '',\r\n\t\t\t\trechar_id: 0,\r\n\t\t\t\trechargeAttention: []\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: mapGetters(['isLogin', 'systemPlatform','userInfo']),\r\n\t\twatch:{\r\n\t\t\tisLogin:{\r\n\t\t\t\thandler:function(newV,oldV){\r\n\t\t\t\t\tif(newV){\r\n\t\t\t\t\t\tthis.getRecharge();\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tdeep:true\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\t// #ifdef H5\r\n\t\t\tthis.from = this.$wechat.isWeixin() ? \"public\" : \"weixinh5\";\r\n\t\t\t// #endif\r\n\t\t\tif (this.isLogin) {\r\n\t\t\t\tthis.getRecharge();\r\n\t\t\t} else {\r\n\t\t\t\ttoLogin();\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\r\n\t\t\t/**\r\n\t\t\t * 选择金额\r\n\t\t\t */\r\n\t\t\tpicCharge(idx, item) {\r\n\t\t\t\tthis.activePic = idx;\r\n\t\t\t\tif (item === undefined) {\r\n\t\t\t\t\tthis.rechar_id = 0;\r\n\t\t\t\t\tthis.numberPic = \"\";\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.money = \"\";\r\n\t\t\t\t\tthis.rechar_id = item.id;\r\n\t\t\t\t\tthis.numberPic = item.price;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\r\n\t\t\t/**\r\n\t\t\t * 充值额度选择\r\n\t\t\t */\r\n\t\t\tgetRecharge() {\r\n\t\t\t\tgetRechargeApi()\r\n\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\tthis.picList = res.data.rechargeQuota;\r\n\t\t\t\t\t\tif (this.picList[0]) {\r\n\t\t\t\t\t\t\tthis.rechar_id = this.picList[0].id;\r\n\t\t\t\t\t\t\tthis.numberPic = this.picList[0].price;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.rechargeAttention = res.data.rechargeAttention || [];\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch(res => {\r\n\t\t\t\t\t\tthis.$dialog.toast({\r\n\t\t\t\t\t\t\tmes: res\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\r\n\r\n\t\t\tonLoadFun: function() {\r\n\t\t\t\tthis.getRecharge();\r\n\t\t\t},\r\n\t\t\t// 授权关闭\r\n\t\t\tauthColse: function(e) {\r\n\t\t\t\tthis.isShowAuth = e\r\n\t\t\t},\r\n\t\t\tnavRecharges: function(index) {\r\n\t\t\t\tthis.active = index;\r\n\t\t\t},\r\n\t\t\t/*\r\n\t\t\t * 用户充值\r\n\t\t\t */\r\n\t\t\tsubmitSub: function(e) {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tlet value = e.detail.value.number;\r\n\t\t\t\t// 转入余额\r\n\t\t\t\tif (that.active) {\r\n\t\t\t\t\tif (parseFloat(value) < 0 || parseFloat(value) == NaN || value == undefined || value == \"\") {\r\n\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: '请输入金额'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '转入余额',\r\n\t\t\t\t\t\tcontent: '转入余额后无法再次转出，确认是否转入余额',\r\n\t\t\t\t\t\tsuccess(res) {\r\n\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\ttransferIn({\r\n\t\t\t\t\t\t\t\t\t\t\tprice: parseFloat(value)\r\n\t\t\t\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\t\t\t\tthat.$store.commit(\"changInfo\", {\r\n\t\t\t\t\t\t\t\t\t\tamount1: 'brokeragePrice',\r\n\t\t\t\t\t\t\t\t\t\tamount2: that.$util.$h.Sub(that.userInfo.brokeragePrice, parseFloat(value))\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '转入成功',\r\n\t\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\t\t\ttab: 5,\r\n\t\t\t\t\t\t\t\t\t\turl: '/pages/users/user_money/index'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}).catch(err=>{\r\n\t\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t} else if (res.cancel) {\r\n\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\ttitle: '已取消'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\ttitle: '正在支付',\r\n\t\t\t\t\t})\r\n\t\t\t\t\tlet money = parseFloat(this.money);\r\n\t\t\t\t\tif (this.rechar_id == 0) {\r\n\t\t\t\t\t\tif (Number.isNaN(money)) {\r\n\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: '充值金额必须为数字'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (money <= 0) {\r\n\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: '充值金额不能为0'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (money > 50000) {\r\n\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\ttitle: '充值金额最大值为50000'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tmoney = this.numberPic\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// #ifdef MP\r\n\t\t\t\t\trechargeRoutine({\r\n\t\t\t\t\t\tprice: money,\r\n\t\t\t\t\t\ttype: 0,\r\n\t\t\t\t\t\trechar_id: this.rechar_id\r\n\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tlet jsConfig = res.data.data.jsConfig;\r\n\t\t\t\t\t\tuni.requestPayment({\r\n\t\t\t\t\t\t\ttimeStamp: jsConfig.timeStamp,\r\n\t\t\t\t\t\t\tnonceStr: jsConfig.nonceStr,\r\n\t\t\t\t\t\t\tpackage: jsConfig.packages,\r\n\t\t\t\t\t\t\tsignType: jsConfig.signType,\r\n\t\t\t\t\t\t\tpaySign: jsConfig.paySign,\r\n\t\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\t\tthat.$store.commit(\"changInfo\", {\r\n\t\t\t\t\t\t\t\t\tamount1: 'nowMoney',\r\n\t\t\t\t\t\t\t\t\tamount2: that.$util.$h.Add(value, that.userinfo.nowMoney)\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t//that.$set(that, 'userinfo.nowMoney', that.$util.$h.Add(value, that.userinfo.nowMoney));\r\n\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\ttitle: '支付成功',\r\n\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\t\ttab: 5,\r\n\t\t\t\t\t\t\t\t\turl: '/pages/users/user_money/index'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tfail: function(err) {\r\n\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\ttitle: '支付失败'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tcomplete: function(res) {\r\n\t\t\t\t\t\t\t\tif (res.errMsg == 'requestPayment:cancel') return that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\ttitle: '取消支付'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}).catch(err => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t});\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifdef H5 \r\n\t\t\t\t\trechargeWechat({\r\n\t\t\t\t\t\tprice: money,\r\n\t\t\t\t\t\tfrom: that.from,\r\n\t\t\t\t\t\trechar_id: that.rechar_id,\r\n\t\t\t\t\t\tpayType: 0\r\n\t\t\t\t\t}).then(res => {\r\n\t\t\t\t\t\tlet jsConfig = res.data.jsConfig;\r\n\t\t\t\t\t\tlet orderNo = res.data.orderNo;\r\n\t\t\t\t\t\tlet data = {\r\n\t\t\t\t\t\t\ttimestamp:jsConfig.timeStamp,\r\n\t\t\t\t\t\t\tnonceStr:jsConfig.nonceStr,\r\n\t\t\t\t\t\t\tpackage:jsConfig.packages,\r\n\t\t\t\t\t\t\tsignType:jsConfig.signType,\r\n\t\t\t\t\t\t\tpaySign:jsConfig.paySign\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\tif (that.from == \"weixinh5\") {\r\n\t\t\t\t\t\t\tlet domain = encodeURIComponent(location.href.split('/pages')[0]);\r\n\t\t\t\t\t\t\tlet urls = jsConfig.mwebUrl + '&redirect_url='+ domain + '/pages/users/user_money/index';\r\n\t\t\t\t\t\t\tlocation.replace(urls);\r\n\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\ttab: 5,\r\n\t\t\t\t\t\t\t\turl: '/pages/users/user_money/index'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t// return that.$util.Tips({\r\n\t\t\t\t\t\t\t// \ttitle: '支付成功',\r\n\t\t\t\t\t\t\t// \ticon: 'success'\r\n\t\t\t\t\t\t\t// }, {\r\n\t\t\t\t\t\t\t// \ttab: 5,\r\n\t\t\t\t\t\t\t// \turl: '/pages/users/user_money/index'\r\n\t\t\t\t\t\t\t// });\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthat.$wechat.pay(data)\r\n\t\t\t\t\t\t\t\t.finally(() => {\r\n\t\t\t\t\t\t\t\t\tthat.$store.commit(\"changInfo\", {\r\n\t\t\t\t\t\t\t\t\t\tamount1: 'nowMoney',\r\n\t\t\t\t\t\t\t\t\t\tamount2: that.$util.$h.Add(value, that.userinfo.nowMoney)\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t// that.$set(that, 'userinfo.nowMoney', that.$util.$h.Add(value, that.userinfo.nowMoney));\r\n\t\t\t\t\t\t\t\t\twechatQueryPayResult(orderNo).then(res => {\r\n\t\t\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\t\ttitle: '支付成功',\r\n\t\t\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\t\t\t\t\ttab: 5,\r\n\t\t\t\t\t\t\t\t\t\t\turl: '/pages/users/user_money/index'\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t}).cache(err => {\r\n\t\t\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\t\ttitle: err\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t.catch(function(err) {\r\n\t\t\t\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '支付失败'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}).catch(res=>{\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\treturn that.$util.Tips({\r\n\t\t\t\t\t\t\ttitle: res\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t})\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\tpage {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tbackground-color: #fff;\r\n\t}\r\n\r\n\t.payment {\r\n\t\tposition: relative;\r\n\t\ttop: -60rpx;\r\n\t\twidth: 100%;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 10rpx;\r\n\t\tpadding-top: 25rpx;\r\n\t\tborder-top-right-radius: 14rpx;\r\n\t\tborder-top-left-radius: 14rpx;\r\n\t}\r\n\r\n\t.payment .nav {\r\n\t\theight: 75rpx;\r\n\t\tline-height: 75rpx;\r\n\t\tpadding: 0 100rpx;\r\n\t}\r\n\r\n\t.payment .nav .item {\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.payment .nav .item.on {\r\n\t\tfont-weight: bold;\r\n\t\tborder-bottom: 4rpx solid #e83323;\r\n\t}\r\n\r\n\t.payment .input {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tborder-bottom: 1px dashed #dddddd;\r\n\t\tmargin: 60rpx auto 0 auto;\r\n\t\tpadding-bottom: 20rpx;\r\n\t\tfont-size: 56rpx;\r\n\t\tcolor: #333333;\r\n\t\tflex-wrap: nowrap;\r\n\r\n\t}\r\n\r\n\t.payment .input text {\r\n\t\tpadding-left: 106rpx;\r\n\t}\r\n\r\n\t.payment .input input {\r\n\t\tpadding-right: 106rpx;\r\n\t\twidth: 300rpx;\r\n\t\theight: 94rpx;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 70rpx;\r\n\t}\r\n\r\n\t.payment .placeholder {\r\n\t\tcolor: #d0d0d0;\r\n\t\theight: 100%;\r\n\t\tline-height: 94rpx;\r\n\t}\r\n\r\n\t.payment .tip {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #888888;\r\n\t\tpadding: 0 30rpx;\r\n\t\tmargin-top: 25rpx;\r\n\t}\r\n\r\n\t.payment .but {\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 30rpx;\r\n\t\twidth: 700rpx;\r\n\t\theight: 86rpx;\r\n\t\tborder-radius: 43rpx;\r\n\t\tmargin: 50rpx auto 0 auto;\r\n\t\tbackground: linear-gradient(90deg, #FF7931 0%, #F11B09 100%);\r\n\t\tline-height: 86rpx;\r\n\t}\r\n\r\n\t.payment-top {\r\n\t\twidth: 100%;\r\n\t\theight: 350rpx;\r\n\t\tbackground-color: #e83323;\r\n\r\n\t\t.name {\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\tcolor: rgba(255, 255, 255, 0.8);\r\n\t\t\tmargin-top: -38rpx;\r\n\t\t\tmargin-bottom: 30rpx;\r\n\t\t}\r\n\r\n\t\t.pic {\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tcolor: #fff;\r\n\t\t}\r\n\r\n\t\t.pic-font {\r\n\t\t\tfont-size: 78rpx;\r\n\t\t\tcolor: #fff;\r\n\t\t}\r\n\t}\r\n\r\n\t.picList {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tmargin: 30rpx 0;\r\n\r\n\t\t.pic-box {\r\n\t\t\twidth: 32%;\r\n\t\t\theight: auto;\r\n\t\t\tborder-radius: 20rpx;\r\n\t\t\tmargin-top: 21rpx;\r\n\t\t\tpadding: 20rpx 0;\r\n\t\t\tmargin-right: 12rpx;\r\n\r\n\t\t\t&:nth-child(3n) {\r\n\t\t\t\tmargin-right: 0;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.pic-box-color {\r\n\t\t\tbackground-color: #f4f4f4;\r\n\t\t\tcolor: #656565;\r\n\t\t}\r\n\r\n\t\t.pic-number {\r\n\t\t\tfont-size: 22rpx;\r\n\t\t}\r\n\r\n\t\t.pic-number-pic {\r\n\t\t\tfont-size: 38rpx;\r\n\t\t\tmargin-right: 10rpx;\r\n\t\t\ttext-align: center;\r\n\t\t}\r\n\r\n\t}\r\n    .pic-box-color-active {\r\n\t\t\tbackground-color: #ec3323 !important;\r\n\t\t\tcolor: #fff !important;\r\n\t}\r\n\t.tips-box {\r\n\t\t.tips {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: #333333;\r\n\t\t\tfont-weight: 800;\r\n\t\t\tmargin-bottom: 14rpx;\r\n\t\t\tmargin-top: 20rpx;\r\n\t\t}\r\n\r\n\t\t.tips-samll {\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tcolor: #333333;\r\n\t\t\tmargin-bottom: 14rpx;\r\n\t\t}\r\n\r\n\t\t.tip-box {\r\n\t\t\tmargin-top: 30rpx;\r\n\t\t}\r\n\t}\r\n\r\n\t.tips-title {\r\n\t\tmargin-top: 20rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #333;\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363903565\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}