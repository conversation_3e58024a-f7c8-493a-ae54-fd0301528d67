(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/users/user_phone/index"],{"128d":function(t,e,i){"use strict";var n=i("2cfd"),s=i.n(n);s.a},"2cfd":function(t,e,i){},"5d25":function(t,e,i){"use strict";i.r(e);var n=i("c5ad"),s=i.n(n);for(var u in n)["default"].indexOf(u)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(u);e["default"]=s.a},"68ad":function(t,e,i){"use strict";i.r(e);var n=i("bd9d"),s=i("5d25");for(var u in s)["default"].indexOf(u)<0&&function(t){i.d(e,t,(function(){return s[t]}))}(u);i("128d");var a=i("828b"),r=Object(a["a"])(s["default"],n["b"],n["c"],!1,null,"3fe66bcc",null,!1,n["a"],void 0);e["default"]=r.exports},bd9d:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement;this._self._c},s=[]},c5ad:function(t,e,i){"use strict";(function(t){var n=i("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var s=n(i("7eb4")),u=n(i("ee10")),a=n(i("74cc")),r=i("fdf2"),c=i("cda4"),o=i("8f59"),d={mixins:[a.default],components:{authorize:function(){Promise.all([i.e("common/vendor"),i.e("components/Authorize")]).then(function(){return resolve(i("cf49"))}.bind(null,i)).catch(i.oe)}},data:function(){return{phone:"",captcha:"",isAuto:!1,isShowAuth:!1,key:"",isNew:!0,timer:"",text:"获取验证码",nums:60}},mounted:function(){},computed:(0,o.mapGetters)(["isLogin","userInfo"]),onLoad:function(){this.isLogin||(0,c.toLogin)()},methods:{getTimes:function(){this.nums=this.nums-1,this.text="剩余 "+this.nums+"s",this.nums<0&&clearInterval(this.timer),this.text="剩余 "+this.nums+"s",this.text<"剩余 0s"&&(this.disabled=!1,this.text="重新获取")},onLoadFun:function(){},authColse:function(t){this.isShowAuth=t},next:function(){var e=this;if(t.hideLoading(),this.isNew=!1,this.captcha="",clearInterval(this.timer),this.disabled=!1,this.text="获取验证码",t.showLoading({title:"加载中",mask:!0}),!this.captcha)return this.$util.Tips({title:"请填写验证码"});(0,r.bindingVerify)({phone:this.userInfo.phone,captcha:this.captcha}).then((function(i){t.hideLoading(),e.isNew=!1,e.captcha="",clearInterval(e.timer),e.disabled=!1,e.text="获取验证码"})).catch((function(t){return e.$util.Tips({title:t})}))},editPwd:function(){var e=this;return e.phone?/^1(3|4|5|7|8|9|6)\d{9}$/i.test(e.phone)?e.captcha?void t.showModal({title:"是否更换绑定账号",confirmText:"绑定",success:function(t){if(t.confirm)(0,r.bindingPhone)({phone:e.phone,captcha:e.captcha}).then((function(t){return e.$util.Tips({title:t.message,icon:"success"},{tab:5,url:"/pages/users/user_info/index"})})).catch((function(t){return e.$util.Tips({title:t})}));else if(t.cancel)return e.$util.Tips({title:"您已取消更换绑定！"},{tab:5,url:"/pages/users/user_info/index"})}}):e.$util.Tips({title:"请填写验证码"}):e.$util.Tips({title:"请输入正确的手机号码！"}):e.$util.Tips({title:"请填写手机号码！"})},code:function(){var e=this;return(0,u.default)(s.default.mark((function i(){var n;return s.default.wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(e.nums=60,t.showLoading({title:"加载中",mask:!0}),n=e,n.isNew){i.next=8;break}if(n.phone){i.next=6;break}return i.abrupt("return",n.$util.Tips({title:"请填写手机号码！"}));case 6:if(/^1(3|4|5|7|8|9|6)\d{9}$/i.test(n.phone)){i.next=8;break}return i.abrupt("return",n.$util.Tips({title:"请输入正确的手机号码！"}));case 8:return i.next=10,(0,r.registerVerify)(n.isNew?n.userInfo.phone:n.phone).then((function(e){n.$util.Tips({title:e.message}),n.timer=setInterval(n.getTimes,1e3),n.disabled=!0,t.hideLoading()})).catch((function(t){return n.$util.Tips({title:t})}));case 10:case"end":return i.stop()}}),i)})))()}}};e.default=d}).call(this,i("df3c")["default"])},d583:function(t,e,i){"use strict";(function(t,e){var n=i("47a9");i("5c2d");n(i("3240"));var s=n(i("68ad"));t.__webpack_require_UNI_MP_PLUGIN__=i,e(s.default)}).call(this,i("3223")["default"],i("df3c")["createPage"])}},[["d583","common/runtime","common/vendor"]]]);