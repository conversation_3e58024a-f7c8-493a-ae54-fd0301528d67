{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/mailun/child/detail.vue?726e", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/mailun/child/detail.vue?b808", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/mailun/child/detail.vue?8fb2", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/mailun/child/detail.vue?eb74", "uni-app:///pages/mailun/child/detail.vue", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/mailun/child/detail.vue?ff65", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/mailun/child/detail.vue?4663"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "data", "chartData", "questionEntities", "questionUserEntity", "questionUserId", "animationFrame", "<PERSON><PERSON><PERSON>", "dpr", "filters", "valueFilter", "onLoad", "onShow", "label", "en<PERSON><PERSON><PERSON>", "value", "color", "title", "onShareAppMessage", "methods", "mailundakai", "uni", "url", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ctx", "setTimeout", "animate", "handleTouch", "y", "x", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACmM;AACnM,gBAAgB,2LAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxBA;AAAA;AAAA;AAAA;AAAmwB,CAAgB,srBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACiCvxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACAC,aACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACAC;MACA;QACA;MACA;QACA;MACA;QACA;MAEA;IACA;EACA;EACA;AACA;AACA;EACAC;IACA;EACA;EACAC;IAAA;IAEA;MAAAP;IAAA;MACA;MACA;MACA,mBACA;QAAAQ;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAH;QAAAC;QAAAC;QAAAC;MAAA,EACA,EACA;IACA;MAEA;QACAC;MACA;IACA;EACA;EACA;AACA;AACA;;EAEAC;IACA;IACA;IACA;IACA;IACA;IACA;EAAA,CACA;EAEAC;IACAC;MAEAC;QAAAC;MAAA;IACA;IACAC;MAEAF;QACAC;MACA;IACA;IAEAE;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;QACA;QACA;QACAC;QACAA;QACAA;QACA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;;QAEA;QACA;UACA;;UAEA;UACA;UACA;UACA;;UAEA;UACAA;UACAA;;UAEA;UACA;UACA;UACA;UACA;;UAEA;UACAA;UACAA;UACAA;UACAA;QACA;QAEAA;QAEA;UACA;UACAC;QACA;MACA;MAEA;MACAC;IACA;IACAC;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;QAAAC;MAEA;QACA;QACA;QACA;;QAEA;QACA,yCACAA;UACA;YACAhB;YACAE;YACAe;YACAC;UACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/MA;AAAA;AAAA;AAAA;AAAs8C,CAAgB,qvCAAG,EAAC,C;;;;;;;;;;;ACA19C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/mailun/child/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/mailun/child/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=68961d5a&scoped=true&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&id=68961d5a&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"68961d5a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/mailun/child/detail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=68961d5a&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.chartData, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var f0 = _vm._f(\"valueFilter\")(item.value)\n    return {\n      $orig: $orig,\n      f0: f0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\r\n    <view>\r\n        <div style=\"margin: 8px 0\" class=\"nav-title\">\r\n            <div class=\"color\"></div>\r\n            <div class=\"text\">脉轮测试结果</div>\r\n        </div>\r\n        <view class=\"container\">\r\n\r\n\r\n            <!-- 绘图Canvas -->\r\n            <canvas canvas-id=\"barChartCanvas\" style=\"width: 370px; height: 300px;\"\r\n                bindtouchstart=\"handleTouch\"></canvas>\r\n            <view style=\"background-color: white;width: 370px;\">\r\n                <view v-for=\"(item, index) in chartData\" :key=\"index\"\r\n                    style=\"display: flex;align-items: center;height: 80rpx;padding: 0 20rpx;font-size: 28rpx;\">\r\n                    <view style=\"width: 300rpx;\">{{ item.label }}({{ item.enLabel }})</view>\r\n                    <view style=\"width: 300rpx;\">{{ item.value | valueFilter }}({{ item.value }})</view>\r\n                </view>\r\n            </view>\r\n        </view>\r\n        <div class=\"bottom\">\r\n            <view class=\"botton_1\" @click=\"mailunjianjie\">\r\n                脉轮简介\r\n            </view>\r\n            <view class=\"botton_2\" @click=\"mailundakai\">\r\n                平衡脉轮\r\n            </view>\r\n\r\n        </div>\r\n    </view>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n    questionFinishDetail,\r\n} from '@/api/question.js';\r\nexport default {\r\n    components: {\r\n    },\r\n    data() {\r\n        return {\r\n            chartData: [],\r\n            questionEntities: [],\r\n            questionUserEntity: {},\r\n            questionUserId: '',\r\n            animationFrame: 0, // 当前动画帧\r\n            selectedBar: null, // 被点击的柱子信息\r\n            dpr: 1, // 设备像素比\r\n        };\r\n    },\r\n    filters: {\r\n        valueFilter(v) {\r\n            if (v <= 0) {\r\n                return '不活跃'\r\n            } else if (v > 0 && v <= 50) {\r\n                return '已开启'\r\n            } else {\r\n                return '过分活跃'\r\n\r\n            }\r\n        }\r\n    },\r\n    /**\r\n      * 生命周期函数--监听页面加载\r\n      */\r\n    onLoad: function (options) {\r\n        this.questionUserId = options.questionUserId;\r\n    },\r\n    onShow: function () {\r\n\r\n        questionFinishDetail({ questionUserId: this.questionUserId }).then(res => {\r\n            this.$set(this, \"questionEntities\", res.data.questionEntities);\r\n            this.$set(this, \"questionUserEntity\", res.data.questionUserEntity);\r\n            this.chartData = [\r\n                { label: \"海底轮\", enLabel: 'Root', value: res.data.questionUserEntity.root, color: \"#993734\" },\r\n                { label: \"脐轮\", enLabel: 'Sacral', value: res.data.questionUserEntity.sacral, color: \"#be6f2a\" },\r\n                { label: \"太阳轮\", enLabel: 'Solar Plexus', value: res.data.questionUserEntity.navel, color: \"#d7c34a\" },\r\n                { label: \"心轮\", enLabel: 'Heart', value: res.data.questionUserEntity.heart, color: \"#5f9057\" },\r\n                { label: \"喉轮\", enLabel: 'Throat', value: res.data.questionUserEntity.throat, color: \"#5b8aa4\" },\r\n                { label: \"眉心轮\", enLabel: 'Third Eye', value: res.data.questionUserEntity.thirdEye, color: \"#2c3485\" },\r\n                { label: \"顶轮\", enLabel: 'Crown', value: res.data.questionUserEntity.crown, color: \"#7e4997\" },\r\n            ],\r\n                this.drawBarChart();\r\n        }).catch(res => {\r\n\r\n            this.$util.Tips({\r\n                title: res\r\n            });\r\n        })\r\n    },\r\n    /**\r\n     * 用户点击右上角分享\r\n     */\r\n    // #ifdef MP\r\n    onShareAppMessage: function () {\r\n        // return {\r\n        // \ttitle: this.articleInfo.title,\r\n        // \timageUrl: this.articleInfo.imageInput.length ? this.articleInfo.imageInput[0] : \"\",\r\n        // \tdesc: this.articleInfo.synopsis,\r\n        // \tpath: '/pages/news_details/index?id=' + this.id\r\n        // };\r\n    },\r\n    // #endif\r\n    methods: {\r\n        mailundakai() {\r\n\r\n            uni.navigateTo({ url: '/pages/mailun/child/mailundakai' })\r\n        },\r\n        mailunjianjie() {\r\n\r\n            uni.navigateTo({\r\n                url: '/pages/mailun/child/mailunjianjie'\r\n            })\r\n        },\r\n\r\n        drawBarChart() {\r\n            const ctx = uni.createCanvasContext(\"barChartCanvas\");\r\n            const canvasWidth = 370;\r\n            const canvasHeight = 300;\r\n            const chartHeight = 200;\r\n            const barWidth = 25;\r\n            const gap = 20;\r\n            const baseY = 250; // 图表底部位置\r\n            const maxFrame = 60;\r\n            const startX = (canvasWidth - (this.chartData.length * barWidth + (this.chartData.length - 1) * gap)) / 2;\r\n\r\n            const animate = () => {\r\n                // 清空画布\r\n                // 绘制中间线\r\n                ctx.clearRect(0, 0, canvasWidth, canvasHeight);\r\n                ctx.setFillStyle(\"#FFFFFF\");\r\n                ctx.fillRect(0, 0, canvasWidth, canvasHeight);\r\n                // 绘制x轴基线\r\n                ctx.beginPath();\r\n                ctx.setStrokeStyle(\"#000000\");\r\n                ctx.setLineWidth(1);\r\n                ctx.moveTo(startX - 10, baseY);  // 稍微向左延伸一点\r\n                ctx.lineTo(startX + (this.chartData.length * (barWidth + gap)), baseY);  // 覆盖整个图表宽度\r\n                ctx.stroke();\r\n\r\n                // 绘制柱子\r\n                this.chartData.forEach((item, index) => {\r\n                    const x = startX + index * (barWidth + gap);\r\n\r\n                    // 将值从[-100, 100]映射到[0, chartHeight]\r\n                    const normalizedValue = item.value + 100; // 将-100~100转换为0~200\r\n                    const height = (normalizedValue / 200) * chartHeight; // 将0~200映射到实际高度\r\n                    const currentHeight = (this.animationFrame / maxFrame) * height;\r\n\r\n                    // 所有柱子都从底部开始画\r\n                    ctx.setFillStyle(item.color);\r\n                    ctx.fillRect(x, baseY - currentHeight, barWidth, currentHeight);\r\n\r\n                    // 绘制数值\r\n                    // ctx.setFillStyle(\"#000000\");\r\n                    // ctx.setFontSize(12);\r\n                    // ctx.setTextAlign(\"center\");\r\n                    // ctx.fillText(item.value, x + barWidth / 2, baseY - currentHeight - 5);\r\n\r\n                    // 绘制标签\r\n                    ctx.setFillStyle(\"#000000\");\r\n                    ctx.setFontSize(12);\r\n                    ctx.setTextAlign(\"center\");\r\n                    ctx.fillText(item.label, x + barWidth / 2, baseY + 20);\r\n                });\r\n\r\n                ctx.draw();\r\n\r\n                if (this.animationFrame < maxFrame) {\r\n                    this.animationFrame++;\r\n                    setTimeout(animate, 16);\r\n                }\r\n            };\r\n\r\n            this.animationFrame = 0;\r\n            animate();\r\n        },\r\n        handleTouch(e) {\r\n            const touch = e.touches[0];\r\n            const canvasWidth = 370;\r\n            const chartHeight = 200;\r\n            const barWidth = 30;\r\n            const gap = 20;\r\n            const baseY = 250;\r\n            const startX = (canvasWidth - (this.chartData.length * barWidth + (this.chartData.length - 1) * gap)) / 2;\r\n\r\n            const { x, y } = touch;\r\n\r\n            this.chartData.forEach((item, index) => {\r\n                const barX = startX + index * (barWidth + gap);\r\n                const normalizedValue = item.value + 100;\r\n                const height = (normalizedValue / 200) * chartHeight;\r\n\r\n                // 检查点击是否在柱子范围内\r\n                if (x >= barX && x <= barX + barWidth &&\r\n                    y >= baseY - height && y <= baseY) {\r\n                    this.selectedBar = {\r\n                        label: item.label,\r\n                        value: item.value,\r\n                        x: barX,\r\n                        barWidth: barWidth\r\n                    };\r\n                    this.drawBarChart();\r\n                }\r\n            });\r\n        },\r\n    }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.nav-title {\r\n    padding: 10px 40rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    height: 60rpx;\r\n    line-height: 60rpx;\r\n    background: #f6f6f6;\r\n\r\n    .color {\r\n        width: 10rpx;\r\n        height: 30rpx;\r\n        background: #c9ab79;\r\n        border-radius: 6rpx;\r\n        margin-right: 10rpx;\r\n    }\r\n}\r\n\r\n.botton_1 {\r\n    width: 45%;\r\n    background-color: #c9ab79;\r\n    color: #fff;\r\n    font-size: 28rpx;\r\n    font-weight: 600;\r\n    height: 80rpx;\r\n    border-radius: 50rpx;\r\n    text-align: center;\r\n    line-height: 80rpx;\r\n}\r\n\r\n.botton_2 {\r\n    width: 45%;\r\n    background-color: #DD5C5F;\r\n    color: #fff;\r\n    font-size: 28rpx;\r\n    font-weight: 600;\r\n    height: 80rpx;\r\n    border-radius: 50rpx;\r\n    text-align: center;\r\n    line-height: 80rpx;\r\n}\r\n\r\n\r\n.container {\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    justify-content: center;\r\n}\r\n\r\n.bottom {\r\n    display: flex;\r\n    padding: 20px;\r\n    justify-content: space-around;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=68961d5a&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=68961d5a&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363902064\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}