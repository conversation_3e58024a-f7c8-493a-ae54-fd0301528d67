{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\api\\userprocess.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\api\\userprocess.js", "mtime": 1753666157724}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\eslint-loader\\index.js", "mtime": 1753666298172}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.userprocessCreateApi = userprocessCreateApi;\nexports.userprocessDeleteApi = userprocessDeleteApi;\nexports.userprocessDetailApi = userprocessDetailApi;\nexports.userprocessFindByUserId = userprocessFindByUserId;\nexports.userprocessListApi = userprocessListApi;\nexports.userprocessUpdateApi = userprocessUpdateApi;\nvar _request = _interopRequireDefault(require(\"@/utils/request\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\n/**\r\n * 新增userprocess\r\n * @param pram\r\n */\nfunction userprocessCreateApi(data) {\n  return (0, _request.default)({\n    url: 'userprocess/save',\n    method: 'POST',\n    data: data\n  });\n}\n\n/**\r\n * userprocess更新\r\n * @param pram\r\n */\nfunction userprocessUpdateApi(data) {\n  return (0, _request.default)({\n    url: 'userprocess/update',\n    method: 'POST',\n    data: data\n  });\n}\n\n/**\r\n * userprocess详情\r\n * @param pram\r\n */\nfunction userprocessDetailApi(id) {\n  return (0, _request.default)({\n    url: \"userprocess/info/\".concat(id),\n    method: 'GET'\n  });\n}\n\n/**\r\n * userprocess删除\r\n * @param pram\r\n */\nfunction userprocessDeleteApi(id) {\n  return (0, _request.default)({\n    url: \"userprocess/delete/\".concat(id),\n    method: 'get'\n  });\n}\n\n/**\r\n * userprocess列表\r\n * @param pram\r\n */\nfunction userprocessListApi(params) {\n  return (0, _request.default)({\n    url: 'userprocess/list',\n    method: 'GET',\n    params: params\n  });\n}\nfunction userprocessFindByUserId(params) {\n  return (0, _request.default)({\n    url: 'userprocess/findByUserId',\n    method: 'GET',\n    params: params\n  });\n}", null]}