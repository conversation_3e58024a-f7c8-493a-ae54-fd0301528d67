@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page.data-v-137d5072,
page.data-v-137d5072 {
  height: 100%;
}
.bg.data-v-137d5072 {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 420rpx;
  background-image: linear-gradient(180deg, #c9ab79 0%, #F5F5F5 100%);
}
.contenBox.data-v-137d5072 {
  padding: 0 30rpx;
}
.support.data-v-137d5072 {
  width: 219rpx;
  height: 74rpx;
  margin: 54rpx auto;
  display: block;
}
.new-users.data-v-137d5072 {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.new-users .sys-head.data-v-137d5072 {
  position: relative;
  width: 100%;
  background: linear-gradient(90deg, #F73730 0%, #F86429 100%);
}
.new-users .sys-head .sys-title.data-v-137d5072 {
  z-index: 10;
  position: relative;
  height: 43px;
  text-align: center;
  line-height: 43px;
  font-size: 36rpx;
  color: #FFFFFF;
}
.new-users .head .user-card.data-v-137d5072 {
  position: relative;
  width: 100%;
  margin: 0 auto;
  padding: 35rpx 0 30rpx 0;
}
.new-users .head .user-card .user-info.data-v-137d5072 {
  z-index: 20;
  position: relative;
  display: flex;
}
.new-users .head .user-card .user-info .avatar.data-v-137d5072 {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
}
.new-users .head .user-card .user-info .info.data-v-137d5072 {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-left: 20rpx;
  padding: 15rpx 0;
}
.new-users .head .user-card .user-info .info .name.data-v-137d5072 {
  display: flex;
  align-items: center;
  color: #30313b;
  font-size: 31rpx;
}
.new-users .head .user-card .user-info .info .name .vip.data-v-137d5072 {
  display: flex;
  align-items: center;
  padding: 6rpx 20rpx;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 18px;
  font-size: 20rpx;
  margin-left: 12rpx;
}
.new-users .head .user-card .user-info .info .name .vip image.data-v-137d5072 {
  width: 27rpx;
  height: 27rpx;
}
.new-users .head .user-card .user-info .info .num-txt.data-v-137d5072 {
  color: #30313b;
}
.new-users .head .user-card .user-info .info .num.data-v-137d5072 {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #30313b;
}
.new-users .head .user-card .user-info .info .num image.data-v-137d5072 {
  width: 22rpx;
  height: 23rpx;
  margin-left: 20rpx;
}
.new-users .head .user-card .num-wrapper.data-v-137d5072 {
  z-index: 30;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 30rpx;
  color: #30313b;
}
.new-users .head .user-card .num-wrapper .num-item.data-v-137d5072 {
  width: 33.33%;
  text-align: center;
}
.new-users .head .user-card .num-wrapper .num-item .num.data-v-137d5072 {
  font-size: 42rpx;
  font-weight: bold;
}
.new-users .head .user-card .num-wrapper .num-item .txt.data-v-137d5072 {
  margin-top: 10rpx;
  font-size: 26rpx;
  color: #30313b;
}
.new-users .head .user-card .sign.data-v-137d5072 {
  z-index: 200;
  position: absolute;
  right: -12rpx;
  top: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 60rpx;
  background: linear-gradient(90deg, #ffe157 0%, #eec10f 100%);
  border-radius: 29rpx 4rpx 4rpx 29rpx;
  color: #282828;
  font-size: 28rpx;
  font-weight: bold;
}
.new-users .head .order-wrapper.data-v-137d5072 {
  background-color: #ffffff;
  border-radius: 14rpx;
  padding: 30rpx 16rpx;
  position: relative;
  z-index: 11;
}
.new-users .head .order-wrapper .order-hd.data-v-137d5072 {
  justify-content: space-between;
  font-size: 30rpx;
  color: #282828;
  margin-bottom: 40rpx;
  padding: 0 16rpx;
}
.new-users .head .order-wrapper .order-hd .left.data-v-137d5072 {
  color: #282828;
  font-size: 30rpx;
  font-weight: 600;
}
.new-users .head .order-wrapper .order-hd .right.data-v-137d5072 {
  align-items: center;
  color: #666666;
  font-size: 26rpx;
}
.new-users .head .order-wrapper .order-hd .right .icon-xiangyou.data-v-137d5072 {
  margin-left: 5rpx;
  font-size: 24rpx;
}
.new-users .head .order-wrapper .order-bd.data-v-137d5072 {
  display: flex;
  justify-content: space-between;
  padding: 0;
}
.new-users .head .order-wrapper .order-bd .order-item.data-v-137d5072 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.new-users .head .order-wrapper .order-bd .order-item .pic.data-v-137d5072 {
  position: relative;
  text-align: center;
}
.new-users .head .order-wrapper .order-bd .order-item .pic image.data-v-137d5072 {
  width: 48rpx;
  height: 48rpx;
}
.new-users .head .order-wrapper .order-bd .order-item .txt.data-v-137d5072 {
  margin-top: 15rpx;
  font-size: 26rpx;
  color: #454545;
}
.new-users .slider-wrapper.data-v-137d5072 {
  margin: 20rpx 0;
  height: 138rpx;
}
.new-users .slider-wrapper swiper.data-v-137d5072,
.new-users .slider-wrapper swiper-item.data-v-137d5072 {
  height: 100%;
}
.new-users .slider-wrapper image.data-v-137d5072 {
  width: 100%;
  height: 100%;
}
.new-users .user-menus.data-v-137d5072 {
  background-color: #fff;
  border-radius: 14rpx;
}
.new-users .user-menus .menu-title.data-v-137d5072 {
  padding: 30rpx 30rpx 40rpx;
  font-size: 30rpx;
  color: #282828;
  font-weight: 600;
}
.new-users .user-menus .list-box.data-v-137d5072 {
  display: flex;
  flex-wrap: wrap;
  padding: 0;
}
.new-users .user-menus .item.data-v-137d5072 {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-direction: column;
  width: 25%;
  margin-bottom: 47rpx;
  font-size: 26rpx;
  color: #333333;
}
.new-users .user-menus .item image.data-v-137d5072 {
  width: 52rpx;
  height: 52rpx;
  margin-bottom: 18rpx;
}
.new-users .user-menus .item.data-v-137d5072:last-child::before {
  display: none;
}
.new-users .user-menus button.data-v-137d5072 {
  font-size: 28rpx;
}
.new-users .phone.data-v-137d5072 {
  color: #fff;
}
.new-users .order-status-num.data-v-137d5072 {
  min-width: 12rpx;
  background-color: #fff;
  color: #ee5a52;
  border-radius: 15px;
  position: absolute;
  right: -14rpx;
  top: -15rpx;
  font-size: 20rpx;
  padding: 0 8rpx;
  border: 1px solid #ee5a52;
}

