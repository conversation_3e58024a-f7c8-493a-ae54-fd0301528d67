(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/users/user_vip/index"],{2924:function(e,t,n){"use strict";n.r(t);var i=n("8d76"),a=n("7e1f");for(var l in a)["default"].indexOf(l)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(l);n("aa6b");var o=n("828b"),c=Object(o["a"])(a["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);t["default"]=c.exports},"2c68":function(e,t,n){"use strict";(function(e,t){var i=n("47a9");n("5c2d");i(n("3240"));var a=i(n("2924"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(a.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},"7e1f":function(e,t,n){"use strict";n.r(t);var i=n("cd6a"),a=n.n(i);for(var l in i)["default"].indexOf(l)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(l);t["default"]=a.a},"8d76":function(e,t,n){"use strict";n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){}));var i=function(){var e=this.$createElement,t=(this._self._c,this.expList.length),n=this.expList.length;this.$mp.data=Object.assign({},{$root:{g0:t,g1:n}})},a=[]},aa6b:function(e,t,n){"use strict";var i=n("d228"),a=n.n(i);a.a},cd6a:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=n("8f59"),a=n("5904"),l={components:{home:function(){n.e("components/home/<USER>").then(function(){return resolve(n("bc9e"))}.bind(null,n)).catch(n.oe)}},computed:(0,i.mapGetters)(["userInfo"]),data:function(){return{levelInfo:"",levelList:[],current:0,widthLen:0,loading:!1,loadend:!1,loadTitle:"加载更多",page:1,limit:20,expList:[]}},onLoad:function(){this.levelInfo=this.userInfo.experience,this.getInfo(),this.getlevelList()},methods:{getInfo:function(){var e=this;(0,a.getlevelInfo)().then((function(t){var n=t.data,i=[];e.levelList=n,n.map((function(t,n){t.experience<=e.levelInfo&&i.push(t.experience)}));var a=Math.max.apply(null,i);e.current=a;var l=n[i.length]?n[i.length]:n[i.length-1],o=l.experience-a?l.experience-a:1,c=(e.levelInfo-a)/o/n.length;e.widthLen=(i.length-.5)/n.length*100+100*c})).catch((function(t){return e.$util.Tips({title:t})}))},getlevelList:function(){var e=this;return!this.loadend&&(!this.loading&&void(0,a.getlevelExpList)({page:e.page,limit:e.limit}).then((function(t){var n=t.data.list,i=n.length<e.limit,a=e.$util.SplitArray(n,e.expList);e.$set(e,"expList",a),e.loadend=i,e.loadTitle=i?"我也是有底线的":"加载更多",e.page=e.page+1,e.loading=!1})).catch((function(t){e.loading=!1,e.loadTitle="加载更多"})))}},onReachBottom:function(){this.getlevelList()}};t.default=l},d228:function(e,t,n){}},[["2c68","common/runtime","common/vendor"]]]);