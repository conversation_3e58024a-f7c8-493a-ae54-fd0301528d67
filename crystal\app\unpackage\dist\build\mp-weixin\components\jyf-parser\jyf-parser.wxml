<view><block wx:if="{{!$root.g0}}"><slot></slot></block><view style="{{(showAm+(selectable?';user-select:text;-webkit-user-select:text':''))}}" id="top" animation="{{scaleAm}}" data-event-opts="{{[['tap',[['_tap',['$event']]]],['touchstart',[['_touchstart',['$event']]]],['touchmove',[['_touchmove',['$event']]]]]}}" bindtap="__e" bindtouchstart="__e" bindtouchmove="__e"><trees vue-id="6b79763c-1" nodes="{{nodes}}" lazy-load="{{lazyLoad}}" loadVideo="{{loadVideo}}" bind:__l="__l"></trees><block wx:for="{{imgs}}" wx:for-item="item" wx:for-index="index" wx:key="index"><image id="{{index}}" src="{{item}}" hidden="{{true}}" data-event-opts="{{[['load',[['_load',['$event']]]]]}}" bindload="__e"></image></block></view></view>