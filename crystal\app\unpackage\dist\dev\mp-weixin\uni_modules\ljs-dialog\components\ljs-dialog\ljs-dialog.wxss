@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* crmeb颜色变量 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.ljs-dialog.data-v-68330d90 {
  width: 100%;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.6);
  overflow-y: auto;
}
.ljs-dialog .ljs-dialog-box.data-v-68330d90 {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  background-color: #FFF;
  border-radius: 20rpx;
  overflow: hidden;
}
.ljs-dialog .ljs-dialog-box .ljs-dialog-title.data-v-68330d90 {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  font-weight: 600;
  padding: 0 30rpx;
  box-sizing: border-box;
  position: relative;
}
.ljs-dialog .ljs-dialog-box .ljs-dialog-title .ljs-dialog-close.data-v-68330d90 {
  width: 90rpx;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  position: absolute;
  top: 0;
  right: 0;
  padding: 30rpx;
  box-sizing: border-box;
}
.ljs-dialog .ljs-dialog-box > .ljs-dialog-content.data-v-68330d90 {
  width: 100%;
  overflow: hidden;
}
.ljs-dialog .ljs-dialog-box > .ljs-dialog-butBox.data-v-68330d90 {
  width: 100%;
  padding: 30rpx;
  box-sizing: border-box;
}

