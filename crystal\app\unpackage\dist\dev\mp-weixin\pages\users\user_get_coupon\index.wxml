<view class="data-v-67877894"><view class="acea-row row-around nav data-v-67877894"><block wx:for="{{navList}}" wx:for-item="item" wx:for-index="__i0__" wx:key="type"><view data-event-opts="{{[['tap',[['setType',['$0'],[[['navList','type',item.type,'type']]]]]]]}}" class="{{['data-v-67877894','acea-row','row-middle',type===item.type?'on':'']}}" bindtap="__e">{{item.name}}</view></block></view><view style="height:106rpx;" class="data-v-67877894"></view><block wx:if="{{$root.g0}}"><view class="coupon-list data-v-67877894"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item acea-row row-center-wrapper data-v-67877894"><view class="{{['money','data-v-67877894',item.$orig.isUse?'moneyGray':'']}}"><view class="data-v-67877894">￥<text class="num data-v-67877894">{{item.$orig.money?item.m0:''}}</text></view><view class="pic-num data-v-67877894">{{"满"+(item.$orig.minPrice?item.m1:'')+"元可用"}}</view></view><view class="text data-v-67877894"><view class="condition line2 data-v-67877894"><block wx:if="{{item.$orig.useType===1}}"><label class="{{['line-title','_span','data-v-67877894',item.$orig.isUse==true||item.$orig.isUse==2?'gray':'']}}">通用</label></block><block wx:else><block wx:if="{{item.$orig.useType===3}}"><label class="{{['line-title','_span','data-v-67877894',item.$orig.isUse==true||item.$orig.isUse==2?'gray':'']}}">品类</label></block><block wx:else><label class="{{['line-title','_span','data-v-67877894',item.$orig.isUse==true||item.$orig.isUse==2?'gray':'']}}">商品</label></block></block><label class="_span data-v-67877894">{{item.$orig.name}}</label></view><view class="data acea-row row-between-wrapper data-v-67877894"><block wx:if="{{item.$orig.day>0}}"><view class="data-v-67877894">{{"领取后"+item.$orig.day+"天内可用"}}</view></block><block wx:else><view class="data-v-67877894">{{''+(item.$orig.useStartTimeStr&&item.$orig.useEndTimeStr?item.$orig.useStartTimeStr+" - "+item.$orig.useEndTimeStr:"")+''}}</view></block><block wx:if="{{item.$orig.isUse==true}}"><view class="bnt gray data-v-67877894">已领取</view></block><block wx:else><view data-event-opts="{{[['tap',[['getCoupon',['$0',index],[[['couponsList','',index,'id']]]]]]]}}" class="bnt bg-color data-v-67877894" bindtap="__e">立即领取</view></block></view></view></view></block></view></block><view class="loadingicon acea-row row-center-wrapper data-v-67877894"><text class="loading iconfont icon-jiazai data-v-67877894" hidden="{{loading==false}}"></text>{{($root.g1?loadTitle:'')+''}}</view><block wx:if="{{$root.g2}}"><view class="noCommodity data-v-67877894"><view class="pictrue data-v-67877894"><image src="../../../static/images/noCoupon.png" class="data-v-67877894"></image></view></view></block></view>