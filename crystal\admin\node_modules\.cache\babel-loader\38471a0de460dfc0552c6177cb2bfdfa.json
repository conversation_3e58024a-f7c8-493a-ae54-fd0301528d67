{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\element-ui\\lib\\utils\\aria-utils.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\element-ui\\lib\\utils\\aria-utils.js", "mtime": 1753666304625}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}], "contextDependencies": [], "result": ["'use strict';\n\nexports.__esModule = true;\nvar aria = aria || {};\naria.Utils = aria.Utils || {};\n\n/**\n * @desc Set focus on descendant nodes until the first focusable element is\n *       found.\n * @param element\n *          DOM node for which to find the first focusable descendant.\n * @returns\n *  true if a focusable element is found and focus is set.\n */\naria.Utils.focusFirstDescendant = function (element) {\n  for (var i = 0; i < element.childNodes.length; i++) {\n    var child = element.childNodes[i];\n    if (aria.Utils.attemptFocus(child) || aria.Utils.focusFirstDescendant(child)) {\n      return true;\n    }\n  }\n  return false;\n};\n\n/**\n * @desc Find the last descendant node that is focusable.\n * @param element\n *          DOM node for which to find the last focusable descendant.\n * @returns\n *  true if a focusable element is found and focus is set.\n */\n\naria.Utils.focusLastDescendant = function (element) {\n  for (var i = element.childNodes.length - 1; i >= 0; i--) {\n    var child = element.childNodes[i];\n    if (aria.Utils.attemptFocus(child) || aria.Utils.focusLastDescendant(child)) {\n      return true;\n    }\n  }\n  return false;\n};\n\n/**\n * @desc Set Attempt to set focus on the current node.\n * @param element\n *          The node to attempt to focus on.\n * @returns\n *  true if element is focused.\n */\naria.Utils.attemptFocus = function (element) {\n  if (!aria.Utils.isFocusable(element)) {\n    return false;\n  }\n  aria.Utils.IgnoreUtilFocusChanges = true;\n  try {\n    element.focus();\n  } catch (e) {}\n  aria.Utils.IgnoreUtilFocusChanges = false;\n  return document.activeElement === element;\n};\naria.Utils.isFocusable = function (element) {\n  if (element.tabIndex > 0 || element.tabIndex === 0 && element.getAttribute('tabIndex') !== null) {\n    return true;\n  }\n  if (element.disabled) {\n    return false;\n  }\n  switch (element.nodeName) {\n    case 'A':\n      return !!element.href && element.rel !== 'ignore';\n    case 'INPUT':\n      return element.type !== 'hidden' && element.type !== 'file';\n    case 'BUTTON':\n    case 'SELECT':\n    case 'TEXTAREA':\n      return true;\n    default:\n      return false;\n  }\n};\n\n/**\n * 触发一个事件\n * mouseenter, mouseleave, mouseover, keyup, change, click 等\n * @param  {Element} elm\n * @param  {String} name\n * @param  {*} opts\n */\naria.Utils.triggerEvent = function (elm, name) {\n  var eventName = void 0;\n  if (/^mouse|click/.test(name)) {\n    eventName = 'MouseEvents';\n  } else if (/^key/.test(name)) {\n    eventName = 'KeyboardEvent';\n  } else {\n    eventName = 'HTMLEvents';\n  }\n  var evt = document.createEvent(eventName);\n  for (var _len = arguments.length, opts = Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {\n    opts[_key - 2] = arguments[_key];\n  }\n  evt.initEvent.apply(evt, [name].concat(opts));\n  elm.dispatchEvent ? elm.dispatchEvent(evt) : elm.fireEvent('on' + name, evt);\n  return elm;\n};\naria.Utils.keys = {\n  tab: 9,\n  enter: 13,\n  space: 32,\n  left: 37,\n  up: 38,\n  right: 39,\n  down: 40,\n  esc: 27\n};\nexports.default = aria.Utils;", null]}