{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/index/components/c_bargain.vue?1159", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/index/components/c_bargain.vue?1783", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/index/components/c_bargain.vue?3f7e", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/index/components/c_bargain.vue?4579", "uni-app:///pages/index/components/c_bargain.vue", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/index/components/c_bargain.vue?c196", "webpack:///C:/Users/<USER>/Desktop/code/crystal-mall/crystal/app/pages/index/components/c_bargain.vue?a5a2"], "names": ["name", "computed", "data", "bargList", "isBorader", "created", "mounted", "methods", "getBargainList", "bargDetail", "uni", "url"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACsC;;;AAG9F;AACmM;AACnM,gBAAgB,2LAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAswB,CAAgB,yrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;AC+B1xB;AAGA;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAPA;AAAA,eAQA;EACAA;EACAC;IACA;IACA;EACA;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC,6BACA;EACAC;IACA;IACAC;MAAA;MACA;QACA;MACA;IACA;IACAC;MACAC;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpEA;AAAA;AAAA;AAAA;AAAy8C,CAAgB,wvCAAG,EAAC,C;;;;;;;;;;;ACA79C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/components/c_bargain.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./c_bargain.vue?vue&type=template&id=0a3f79d3&scoped=true&\"\nvar renderjs\nimport script from \"./c_bargain.vue?vue&type=script&lang=js&\"\nexport * from \"./c_bargain.vue?vue&type=script&lang=js&\"\nimport style0 from \"./c_bargain.vue?vue&type=style&index=0&id=0a3f79d3&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0a3f79d3\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/components/c_bargain.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./c_bargain.vue?vue&type=template&id=0a3f79d3&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.bargList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./c_bargain.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./c_bargain.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view :class=\"{borderShow:isBorader}\">\r\n\t\t<view class=\"combination\" v-if=\"bargList.length\">\r\n\t\t\t<view class=\"title acea-row row-between\">\r\n\t\t\t\t<view class=\"acea-row row-column\">\r\n\t\t\t\t\t<image src=\"../../../static/images/kanjia.png\" class=\"pic\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<navigator url=\"/pages/activity/goods_bargain/index\" hover-class=\"none\" class=\"more acea-row row-center-wrapper\">GO<text class=\"iconfont icon-xiangyou\"></text></navigator>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"conter acea-row\">\r\n\t\t\t\t<scroll-view scroll-x=\"true\" style=\"white-space: nowrap; vertical-align: middle;\" show-scrollbar=\"false\">\r\n\t\t\t\t\t<view class=\"itemCon\" v-for=\"(item, index) in bargList\" :key=\"index\" @click=\"bargDetail(item)\">\r\n\t\t\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t\t\t<view class=\"pictrue\">\r\n\t\t\t\t\t\t\t\t<image :src=\"item.image\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"text lines1\">\r\n\t\t\t\t\t\t\t\t<view class=\"name line1\">{{item.title}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"money\">¥<text class=\"num\">{{item.minPrice}}</text></view>\r\n\t\t\t\t\t\t\t\t<view class=\"btn\">参与砍价</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</scroll-view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tlet app = getApp();\r\n\timport {\r\n\t\ttoLogin\r\n\t} from '@/libs/login.js';\r\n\timport {\r\n\t\tgetBargainIndexApi\r\n\t} from '@/api/activity.js';\r\n\timport { mapGetters } from 'vuex';\r\n\texport default {\r\n\t\tname: 'c_bargain',\r\n\t\tcomputed: mapGetters({\r\n\t\t\t'userData': 'userInfo',\r\n\t\t\t'uid': 'uid'\r\n\t\t}),\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tbargList: [],\r\n\t\t\t\tisBorader:false\r\n\t\t\t};\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.getBargainList();\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 砍价列表\r\n\t\t\tgetBargainList() {\r\n\t\t\t\tgetBargainIndexApi().then(res => {\r\n\t\t\t\t\tthis.bargList = res.data ? res.data.productList : [];\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tbargDetail(item){\r\n\t\t\t   uni.navigateTo({\r\n\t\t\t   \turl: `/pages/activity/goods_bargain_details/index?id=${item.id}&startBargainUid=${this.uid}`\r\n\t\t\t   });\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.pic{\r\n\t\twidth: 130rpx;\r\n\t\theight: 30rpx;\r\n\t}\r\n\t.default{\r\n\t\twidth: 690rpx;\r\n\t\theight: 300rpx;\r\n\t\tborder-radius: 14rpx;\r\n\t\tmargin: 26rpx auto 0 auto;\r\n\t\tbackground-color: #ccc;\r\n\t\ttext-align: center;\r\n\t\tline-height: 300rpx;\r\n\t\t.iconfont{\r\n\t\t\tfont-size: 80rpx;\r\n\t\t}\r\n\t}\r\n\t.combination{\r\n\t\twidth: auto;\r\n\t\tbackground-image: url(../../../static/images/kjbj.png);\r\n\t\tbackground-repeat: no-repeat;\r\n\t\tbackground-size: 100%;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 14rpx;\r\n\t\tmargin: 30rpx auto 0 auto;\r\n\t\tpadding: 25rpx 20rpx 25rpx 20rpx;\r\n\t\t\r\n\t\t.title {\r\n\t\t\t.sign {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tcolor: $theme-color;\r\n\t\t\t\tmargin-bottom: 2rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tmargin-bottom: 10rpx;\r\n\t\t\t}\r\n\t\t\r\n\t\t\t.name {\r\n\t\t\t\t\r\n\t\t\t\ttext {\r\n\t\t\t\t\tcolor: #333333;\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\tfont-weight: 400;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\r\n\t\t\t.more {\r\n\t\t\t\twidth: 86rpx;\r\n\t\t\t\theight: 40rpx;\r\n\t\t\t\tbackground: linear-gradient(142deg, #FFE9CE 0%, #FFD6A7 100%);\r\n\t\t\t\topacity: 1;\r\n\t\t\t\tborder-radius: 18px;\r\n\t\t\t\tfont-size: 22rpx;\r\n\t\t\t\tcolor: #FE960F;\r\n\t\t\t\tpadding-left: 8rpx;\r\n\t\t\t\tfont-weight: 800;\r\n\t\t\t\t.iconfont {\r\n\t\t\t\t\tfont-size: 21rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\t.conter{\r\n\t\t\tmargin-top: 28rpx;\r\n\t\t\t.itemCon {\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\twidth: 220rpx;\r\n\t\t\t\tmargin-right: 24rpx;\r\n\t\t\t}\r\n\t\t\t.item{\r\n\t\t\t\twidth:100%;\r\n\t\t\t\t.pictrue{\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\theight: 220rpx;\r\n\t\t\t\t\tborder-radius: 6rpx;\r\n\t\t\t\t\timage{\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\tborder-radius: 6rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t.text{\r\n\t\t\t\t\tmargin-top: 4rpx;\r\n\t\t\t\t\t.y_money {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: #999999;\r\n\t\t\t\t\t\ttext-decoration: line-through;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.name {\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tcolor: #000;\r\n\t\t\t\t\t\tmargin-top: 14rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.money {\r\n\t\t\t\t\t\tcolor: #FD502F;\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\theight: 100%;\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t    margin: 10rpx 0;\r\n\t\t\t\t\t\t.num {\r\n\t\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t.btn{\r\n\t\t\t\t\t\twidth: 220rpx;\r\n\t\t\t\t\t\theight: 48rpx;\r\n\t\t\t\t\t\tline-height: 48rpx;\r\n\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\tbackground: linear-gradient(129deg, #FF5555 0%, #FF0000 100%);\r\n\t\t\t\t\t\topacity: 1;\r\n\t\t\t\t\t\tborder-radius: 0px 0px 14rpx 14rpx;\r\n\t\t\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\t\tmargin-top: 6rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./c_bargain.vue?vue&type=style&index=0&id=0a3f79d3&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Program Files/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./c_bargain.vue?vue&type=style&index=0&id=0a3f79d3&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754363904132\n      var cssReload = require(\"C:/Program Files/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}