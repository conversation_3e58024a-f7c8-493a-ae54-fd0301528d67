{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\marketing\\coupon\\record\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\marketing\\coupon\\record\\index.vue", "mtime": 1753666157892}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _marketing = require(\"@/api/marketing\");\nvar _settings = require(\"@/settings\");\nvar _user = require(\"@/api/user\");\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  name: 'CouponUser',\n  filters: {\n    failFilter: function failFilter(status) {\n      var statusMap = {\n        'receive': '自己领取',\n        'send': '后台发送',\n        'give': '满赠',\n        'new': '新人',\n        'buy': '买赠送'\n      };\n      return statusMap[status];\n    },\n    statusFilter: function statusFilter(status) {\n      var statusMap = {\n        0: '未使用',\n        1: '已使用',\n        2: '已过期'\n      };\n      return statusMap[status];\n    }\n  },\n  data: function data() {\n    return {\n      Loading: false,\n      roterPre: _settings.roterPre,\n      imgList: [],\n      tableFromIssue: {\n        page: 1,\n        limit: 20,\n        uid: '',\n        name: '',\n        status: ''\n      },\n      issueData: {\n        data: [],\n        total: 0\n      },\n      loading: false,\n      options: []\n    };\n  },\n  mounted: function mounted() {\n    this.getIssueList();\n  },\n  methods: {\n    remoteMethod: function remoteMethod(query) {\n      var _this = this;\n      if (query !== '') {\n        this.loading = true;\n        setTimeout(function () {\n          _this.loading = false;\n          (0, _user.userListApi)({\n            keywords: query,\n            page: 1,\n            limit: 10\n          }).then(function (res) {\n            _this.options = res.list;\n          });\n        }, 200);\n      } else {\n        this.options = [];\n      }\n    },\n    seachList: function seachList() {\n      this.tableFromIssue.page = 1;\n      this.getIssueList();\n    },\n    // 列表\n    getIssueList: function getIssueList() {\n      var _this2 = this;\n      this.Loading = true;\n      (0, _marketing.couponUserListApi)(this.tableFromIssue).then(function (res) {\n        _this2.issueData.data = res.list;\n        _this2.issueData.total = res.total;\n        // this.issueData.data.map((item) => {\n        //   this.imgList.push(item.user.avatar)\n        // })\n        _this2.Loading = false;\n      }).catch(function (res) {\n        _this2.Loading = false;\n        _this2.$message.error(res.message);\n      });\n    },\n    pageChangeIssue: function pageChangeIssue(page) {\n      this.tableFromIssue.page = page;\n      this.getIssueList();\n    },\n    handleSizeChangeIssue: function handleSizeChangeIssue(val) {\n      this.tableFromIssue.limit = val;\n      this.getIssueList();\n    }\n  }\n};", null]}