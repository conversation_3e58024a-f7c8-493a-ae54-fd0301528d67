{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\Category\\info.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\Category\\info.vue", "mtime": 1753666157756}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n\r\nimport * as categoryApi from '@/api/categoryApi.js'\r\nexport default {\r\n  // name: \"info\"\r\n  props: {\r\n    id: {\r\n      type: Number,\r\n      required: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      defaultProps: {\r\n        children: 'children',\r\n        label: 'label'\r\n      },\r\n      ddd: [{\r\n        label: '一级 1',\r\n        children: [{\r\n          label: '二级 1-1',\r\n          children: [{\r\n            label: '三级 1-1-1'\r\n          }]\r\n        }]\r\n      }, {\r\n        label: '一级 2',\r\n        children: [{\r\n          label: '二级 2-1',\r\n          children: [{\r\n            label: '三级 2-1-1'\r\n          }]\r\n        }, {\r\n          label: '二级 2-2',\r\n          children: [{\r\n            label: '三级 2-2-1'\r\n          }]\r\n        }]\r\n      }, {\r\n        label: '一级 3',\r\n        children: [{\r\n          label: '二级 3-1',\r\n          children: [{\r\n            label: '三级 3-1-1'\r\n          }]\r\n        }, {\r\n          label: '二级 3-2',\r\n          children: [{\r\n            label: '三级 3-2-1'\r\n          }]\r\n        }]\r\n      }],\r\n      dataList: {// 数据结果\r\n        page: 0,\r\n        limit: 0,\r\n        totalPage: 0,\r\n        total: 0,\r\n        list: []\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.handlerGetTreeList(this.id)\r\n  },\r\n  methods: {\r\n    handlerGetTreeList(id) {\r\n      if (!id) {\r\n        this.$message.error('当前数据id不正确')\r\n        return\r\n      }\r\n      categoryApi.treeCategroy({ pid: id }).then(data => {\r\n        this.dataList = data\r\n      })\r\n    },\r\n    handleNodeClick(data) {\r\n      console.log('data:', data)\r\n    }\r\n  }\r\n}\r\n", null]}