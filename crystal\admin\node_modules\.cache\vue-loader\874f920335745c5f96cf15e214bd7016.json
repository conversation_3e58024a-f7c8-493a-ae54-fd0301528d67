{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\FormGenerator\\index\\FormDrawer.vue?vue&type=template&id=14fdd522&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\components\\FormGenerator\\index\\FormDrawer.vue", "mtime": 1753666157769}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\n<div>\n  <el-drawer v-bind=\"$attrs\" v-on=\"$listeners\" @opened=\"onOpen\" @close=\"onClose\">\n    <div style=\"height:100%\">\n      <el-row style=\"height:100%;overflow:auto\">\n        <el-col :md=\"24\" :lg=\"12\" class=\"left-editor\">\n          <div class=\"setting\" title=\"资源引用\" @click=\"showResource\">\n            <el-badge :is-dot=\"!!resources.length\" class=\"item\">\n              <i class=\"el-icon-setting\" />\n            </el-badge>\n          </div>\n          <el-tabs v-model=\"activeTab\" type=\"card\" class=\"editor-tabs\">\n            <el-tab-pane name=\"html\">\n              <span slot=\"label\">\n                <i v-if=\"activeTab==='html'\" class=\"el-icon-edit\" />\n                <i v-else class=\"el-icon-document\" />\n                template\n              </span>\n            </el-tab-pane>\n            <el-tab-pane name=\"js\">\n              <span slot=\"label\">\n                <i v-if=\"activeTab==='js'\" class=\"el-icon-edit\" />\n                <i v-else class=\"el-icon-document\" />\n                script\n              </span>\n            </el-tab-pane>\n            <el-tab-pane name=\"css\">\n              <span slot=\"label\">\n                <i v-if=\"activeTab==='css'\" class=\"el-icon-edit\" />\n                <i v-else class=\"el-icon-document\" />\n                css\n              </span>\n            </el-tab-pane>\n          </el-tabs>\n          <div v-show=\"activeTab==='html'\" id=\"editorHtml\" class=\"tab-editor\" />\n          <div v-show=\"activeTab==='js'\" id=\"editorJs\" class=\"tab-editor\" />\n          <div v-show=\"activeTab==='css'\" id=\"editorCss\" class=\"tab-editor\" />\n        </el-col>\n        <el-col :md=\"24\" :lg=\"12\" class=\"right-preview\">\n          <div class=\"action-bar\" :style=\"{'text-align': 'left'}\">\n            <span class=\"bar-btn\" @click=\"runCode\">\n              <i class=\"el-icon-refresh\" />\n              刷新\n            </span>\n            <span class=\"bar-btn\" @click=\"exportFile\">\n              <i class=\"el-icon-download\" />\n              导出vue文件\n            </span>\n            <span ref=\"copyBtn\" class=\"bar-btn copy-btn\">\n              <i class=\"el-icon-document-copy\" />\n              复制代码\n            </span>\n            <span class=\"bar-btn delete-btn\" @click=\"$emit('update:visible', false)\">\n              <i class=\"el-icon-circle-close\" />\n              关闭\n            </span>\n          </div>\n          <iframe\n            v-show=\"isIframeLoaded\"\n            ref=\"previewPage\"\n            class=\"result-wrapper\"\n            frameborder=\"0\"\n            src=\"preview.html\"\n            @load=\"iframeLoad\"\n          />\n          <div v-show=\"!isIframeLoaded\" v-loading=\"true\" class=\"result-wrapper\" />\n        </el-col>\n      </el-row>\n    </div>\n  </el-drawer>\n  <resource-dialog\n    :visible.sync=\"resourceVisible\"\n    :origin-resource=\"resources\"\n    @save=\"setResource\"\n  />\n</div>\n", null]}