{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\mailun\\question-user-record-detail.vue?vue&type=style&index=0&id=377e772d&lang=scss&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\mailun\\question-user-record-detail.vue", "mtime": 1753666157878}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\css-loader\\index.js", "mtime": 1753666298053}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753666301105}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753666299466}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1753666297707}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\r\n.info-item {\r\n  margin-bottom: 10px;\r\n  \r\n  .label {\r\n    color: #666;\r\n    font-weight: 500;\r\n  }\r\n  \r\n  .value {\r\n    color: #333;\r\n    margin-left: 5px;\r\n  }\r\n}\r\n\r\n.chakra-results {\r\n  .chakra-grid {\r\n    display: grid;\r\n    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\r\n    gap: 20px;\r\n  }\r\n  \r\n  .chakra-card {\r\n    border: 1px solid #e4e7ed;\r\n    border-radius: 8px;\r\n    padding: 16px;\r\n    text-align: center;\r\n    \r\n    .chakra-header {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-bottom: 12px;\r\n      \r\n      .chakra-dot {\r\n        width: 16px;\r\n        height: 16px;\r\n        border-radius: 50%;\r\n        margin-right: 8px;\r\n      }\r\n      \r\n      .chakra-info {\r\n        text-align: left;\r\n        \r\n        .chakra-name {\r\n          font-weight: 500;\r\n          color: #333;\r\n          font-size: 14px;\r\n        }\r\n        \r\n        .chakra-en-name {\r\n          font-size: 12px;\r\n          color: #999;\r\n        }\r\n      }\r\n    }\r\n    \r\n    .chakra-value {\r\n      font-size: 24px;\r\n      font-weight: bold;\r\n      color: #333;\r\n      margin-bottom: 8px;\r\n    }\r\n    \r\n    .chakra-progress {\r\n      margin-top: 8px;\r\n    }\r\n  }\r\n}\r\n\r\n.question-details {\r\n  height: 500px;\r\n  overflow-y: auto;\r\n  \r\n  .question-item {\r\n    border-bottom: 1px solid #f0f0f0;\r\n    padding: 15px 0;\r\n    \r\n    &:last-child {\r\n      border-bottom: none;\r\n    }\r\n    \r\n    .question-title {\r\n      margin-bottom: 8px;\r\n      \r\n      .question-number {\r\n        color: #409eff;\r\n        font-weight: 500;\r\n        margin-right: 5px;\r\n      }\r\n    }\r\n    \r\n    .question-answer {\r\n      .answer-label {\r\n        color: #666;\r\n        font-size: 14px;\r\n      }\r\n      \r\n      .answer-value {\r\n        color: #333;\r\n        font-weight: 500;\r\n        margin-left: 5px;\r\n      }\r\n    }\r\n  }\r\n}\r\n", null]}