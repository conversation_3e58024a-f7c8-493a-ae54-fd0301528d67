{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\storeAttr\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\store\\storeAttr\\index.vue", "mtime": 1753666157924}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport { templateListApi, attrDeleteApi } from '@/api/store'\r\nexport default {\r\n  name: 'StoreAttr',\r\n  data() {\r\n    return {\r\n      formDynamic: {\r\n        ruleName: '',\r\n        ruleValue: []\r\n      },\r\n      tableFrom: {\r\n        page: 1,\r\n        limit: 20,\r\n        keywords: ''\r\n      },\r\n      tableData: {\r\n        data: [],\r\n        loading: false,\r\n        total: 0\r\n      },\r\n      listLoading: true,\r\n      selectionList: [],\r\n      multipleSelectionAll: [],\r\n      idKey: 'id',\r\n      nextPageFlag: false,\r\n      keyNum: 0\r\n    }\r\n  },\r\n  mounted() {\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    seachList() {\r\n      this.tableFrom.page = 1\r\n      this.getList()\r\n    },\r\n    handleSelectionChange(val) {\r\n      this.selectionList = val\r\n      setTimeout(() => {\r\n        this.changePageCoreRecordData()\r\n      }, 50)\r\n    },\r\n    // 设置选中的方法\r\n    setSelectRow() {\r\n      if (!this.multipleSelectionAll || this.multipleSelectionAll.length <= 0) {\r\n        return\r\n      }\r\n      // 标识当前行的唯一键的名称\r\n      const idKey = this.idKey\r\n      const selectAllIds = []\r\n      this.multipleSelectionAll.forEach(row => {\r\n        selectAllIds.push(row[idKey])\r\n      })\r\n      this.$refs.table.clearSelection()\r\n      for (var i = 0; i < this.tableData.data.length; i++) {\r\n        if (selectAllIds.indexOf(this.tableData.data[i][idKey]) >= 0) {\r\n          // 设置选中，记住table组件需要使用ref=\"table\"\r\n          this.$refs.table.toggleRowSelection(this.tableData.data[i], true)\r\n        }\r\n      }\r\n    },\r\n    // 记忆选择核心方法\r\n    changePageCoreRecordData() {\r\n      // 标识当前行的唯一键的名称\r\n      const idKey = this.idKey\r\n      const that = this\r\n      // 如果总记忆中还没有选择的数据，那么就直接取当前页选中的数据，不需要后面一系列计算\r\n      if (this.multipleSelectionAll.length <= 0) {\r\n        this.multipleSelectionAll = this.selectionList\r\n        return\r\n      }\r\n      // 总选择里面的key集合\r\n      const selectAllIds = []\r\n      this.multipleSelectionAll.forEach(row => {\r\n        selectAllIds.push(row[idKey])\r\n      })\r\n      const selectIds = []\r\n      // 获取当前页选中的id\r\n      this.selectionList.forEach(row => {\r\n        selectIds.push(row[idKey])\r\n        // 如果总选择里面不包含当前页选中的数据，那么就加入到总选择集合里\r\n        if (selectAllIds.indexOf(row[idKey]) < 0) {\r\n          that.multipleSelectionAll.push(row)\r\n        }\r\n      })\r\n      const noSelectIds = []\r\n      // 得到当前页没有选中的id\r\n      this.tableData.data.forEach(row => {\r\n        if (selectIds.indexOf(row[idKey]) < 0) {\r\n          noSelectIds.push(row[idKey])\r\n        }\r\n      })\r\n      noSelectIds.forEach(id => {\r\n        if (selectAllIds.indexOf(id) >= 0) {\r\n          for (let i = 0; i < that.multipleSelectionAll.length; i++) {\r\n            if (that.multipleSelectionAll[i][idKey] == id) {\r\n              // 如果总选择中有未被选中的，那么就删除这条\r\n              that.multipleSelectionAll.splice(i, 1)\r\n              break\r\n            }\r\n          }\r\n        }\r\n      })\r\n    },\r\n    add() {\r\n      const _this = this\r\n      this.$modalAttr(Object.assign({}, this.formDynamic), function() {\r\n        _this.getList()\r\n      }, this.keyNum += 1)\r\n    },\r\n    // 列表\r\n    getList() {\r\n      this.listLoading = true\r\n      templateListApi(this.tableFrom).then(res => {\r\n        const list = res.list\r\n        this.tableData.data = list\r\n        this.tableData.total = res.total\r\n        for (var i = 0; i < list.length; i++) {\r\n          list[i].ruleValue = JSON.parse(list[i].ruleValue)\r\n        }\r\n        this.$nextTick(function() {\r\n          this.setSelectRow()// 调用跨页选中方法\r\n        })\r\n        this.listLoading = false\r\n      }).catch(() => {\r\n        this.listLoading = false\r\n      })\r\n    },\r\n    pageChange(page) {\r\n      this.changePageCoreRecordData()\r\n      this.tableFrom.page = page\r\n      this.getList()\r\n    },\r\n    handleSizeChange(val) {\r\n      this.changePageCoreRecordData()\r\n      this.tableFrom.limit = val\r\n      this.getList()\r\n    },\r\n    // 删除\r\n    handleDelete(id, idx) {\r\n      this.$modalSure().then(() => {\r\n        attrDeleteApi( id ).then(() => {\r\n          this.$message.success('删除成功')\r\n          this.tableData.data.splice(idx, 1)\r\n        })\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    handleDeleteAll(){\r\n      if(!this.multipleSelectionAll.length) return this.$message.warning('请选择商品规格')\r\n      const data = []\r\n      this.multipleSelectionAll.map((item) => {\r\n        data.push(item.id)\r\n      })\r\n      this.ids = data.join(',')\r\n      this.$modalSure().then(() => {\r\n        attrDeleteApi( this.ids ).then(() => {\r\n          this.$message.success('删除成功')\r\n          this.getList()\r\n        })\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    onEdit(val) {\r\n      const _this = this\r\n      this.$modalAttr(JSON.parse(JSON.stringify(val)), function() {\r\n        _this.getList()\r\n      })\r\n    }\r\n  }\r\n}\r\n", null]}