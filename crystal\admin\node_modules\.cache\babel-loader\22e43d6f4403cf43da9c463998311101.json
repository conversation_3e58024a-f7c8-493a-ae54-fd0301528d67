{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\element-ui\\node_modules\\async-validator\\es\\validator\\date.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\element-ui\\node_modules\\async-validator\\es\\validator\\date.js", "mtime": 1753666301025}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\babel.config.js", "mtime": 1753666157682}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753666299468}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _rule = _interopRequireDefault(require(\"../rule/\"));\nvar _util = require(\"../util\");\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction date(rule, value, callback, source, options) {\n  // console.log('integer rule called %j', rule);\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  // console.log('validate on %s value', value);\n  if (validate) {\n    if ((0, _util.isEmptyValue)(value) && !rule.required) {\n      return callback();\n    }\n    _rule.default.required(rule, value, source, errors, options);\n    if (!(0, _util.isEmptyValue)(value)) {\n      var dateObject = void 0;\n      if (typeof value === 'number') {\n        dateObject = new Date(value);\n      } else {\n        dateObject = value;\n      }\n      _rule.default.type(rule, dateObject, source, errors, options);\n      if (dateObject) {\n        _rule.default.range(rule, dateObject.getTime(), source, errors, options);\n      }\n    }\n  }\n  callback(errors);\n}\nvar _default = exports.default = date;", null]}