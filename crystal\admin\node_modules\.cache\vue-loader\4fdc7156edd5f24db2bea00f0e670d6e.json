{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\mailun\\question-user-record-detail.vue?vue&type=template&id=377e772d&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\src\\views\\mailun\\question-user-record-detail.vue", "mtime": 1753666157878}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753666301175}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753666299118}, {"path": "C:\\Users\\<USER>\\Desktop\\code\\crystal-mall\\crystal\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753666300283}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"el-dialog\",\n    {\n      attrs: {\n        title: \"用户测试记录详情\",\n        visible: _vm.visible,\n        width: \"80%\",\n        \"close-on-click-modal\": false,\n      },\n      on: {\n        \"update:visible\": function ($event) {\n          _vm.visible = $event\n        },\n      },\n    },\n    [\n      _c(\n        \"div\",\n        {\n          directives: [\n            {\n              name: \"loading\",\n              rawName: \"v-loading\",\n              value: _vm.loading,\n              expression: \"loading\",\n            },\n          ],\n        },\n        [\n          _c(\n            \"el-card\",\n            {\n              staticClass: \"box-card\",\n              staticStyle: { \"margin-bottom\": \"20px\" },\n            },\n            [\n              _c(\n                \"div\",\n                {\n                  staticClass: \"clearfix\",\n                  attrs: { slot: \"header\" },\n                  slot: \"header\",\n                },\n                [_c(\"span\", [_vm._v(\"用户信息\")])]\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-row\",\n                { attrs: { gutter: 20 } },\n                [\n                  _c(\"el-col\", { attrs: { span: 8 } }, [\n                    _c(\"div\", { staticClass: \"info-item\" }, [\n                      _c(\"span\", { staticClass: \"label\" }, [\n                        _vm._v(\"用户名：\"),\n                      ]),\n                      _vm._v(\" \"),\n                      _c(\"span\", { staticClass: \"value\" }, [\n                        _vm._v(_vm._s(_vm.userInfo.username || \"-\")),\n                      ]),\n                    ]),\n                  ]),\n                  _vm._v(\" \"),\n                  _c(\"el-col\", { attrs: { span: 8 } }, [\n                    _c(\"div\", { staticClass: \"info-item\" }, [\n                      _c(\"span\", { staticClass: \"label\" }, [\n                        _vm._v(\"手机号：\"),\n                      ]),\n                      _vm._v(\" \"),\n                      _c(\"span\", { staticClass: \"value\" }, [\n                        _vm._v(_vm._s(_vm.userInfo.mobile || \"-\")),\n                      ]),\n                    ]),\n                  ]),\n                  _vm._v(\" \"),\n                  _c(\"el-col\", { attrs: { span: 8 } }, [\n                    _c(\n                      \"div\",\n                      { staticClass: \"info-item\" },\n                      [\n                        _c(\"span\", { staticClass: \"label\" }, [\n                          _vm._v(\"测试状态：\"),\n                        ]),\n                        _vm._v(\" \"),\n                        _c(\n                          \"el-tag\",\n                          {\n                            attrs: {\n                              type:\n                                _vm.userInfo.status == 1\n                                  ? \"success\"\n                                  : \"warning\",\n                            },\n                          },\n                          [\n                            _vm._v(\n                              \"\\n              \" +\n                                _vm._s(\n                                  _vm.userInfo.status == 1 ? \"已提交\" : \"未提交\"\n                                ) +\n                                \"\\n            \"\n                            ),\n                          ]\n                        ),\n                      ],\n                      1\n                    ),\n                  ]),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-row\",\n                {\n                  staticStyle: { \"margin-top\": \"15px\" },\n                  attrs: { gutter: 20 },\n                },\n                [\n                  _c(\"el-col\", { attrs: { span: 8 } }, [\n                    _c(\"div\", { staticClass: \"info-item\" }, [\n                      _c(\"span\", { staticClass: \"label\" }, [_vm._v(\"总分：\")]),\n                      _vm._v(\" \"),\n                      _c(\"span\", { staticClass: \"value\" }, [\n                        _vm._v(_vm._s(_vm.userInfo.points || 0)),\n                      ]),\n                    ]),\n                  ]),\n                  _vm._v(\" \"),\n                  _c(\"el-col\", { attrs: { span: 8 } }, [\n                    _c(\"div\", { staticClass: \"info-item\" }, [\n                      _c(\"span\", { staticClass: \"label\" }, [\n                        _vm._v(\"创建时间：\"),\n                      ]),\n                      _vm._v(\" \"),\n                      _c(\"span\", { staticClass: \"value\" }, [\n                        _vm._v(_vm._s(_vm.userInfo.addTime || \"-\")),\n                      ]),\n                    ]),\n                  ]),\n                  _vm._v(\" \"),\n                  _c(\"el-col\", { attrs: { span: 8 } }, [\n                    _c(\"div\", { staticClass: \"info-item\" }, [\n                      _c(\"span\", { staticClass: \"label\" }, [\n                        _vm._v(\"更新时间：\"),\n                      ]),\n                      _vm._v(\" \"),\n                      _c(\"span\", { staticClass: \"value\" }, [\n                        _vm._v(_vm._s(_vm.userInfo.updateTime || \"-\")),\n                      ]),\n                    ]),\n                  ]),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _vm.userInfo.status == 1\n            ? _c(\n                \"el-card\",\n                {\n                  staticClass: \"box-card\",\n                  staticStyle: { \"margin-bottom\": \"20px\" },\n                },\n                [\n                  _c(\n                    \"div\",\n                    {\n                      staticClass: \"clearfix\",\n                      attrs: { slot: \"header\" },\n                      slot: \"header\",\n                    },\n                    [\n                      _c(\"span\", [_vm._v(\"脉轮测试结果\")]),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-button\",\n                        {\n                          staticStyle: { float: \"right\", padding: \"3px 0\" },\n                          attrs: { type: \"text\" },\n                          on: {\n                            click: function ($event) {\n                              return _vm.copyChakraData()\n                            },\n                          },\n                        },\n                        [\n                          _c(\"i\", { staticClass: \"el-icon-document-copy\" }),\n                          _vm._v(\" 复制脉轮数据\\n        \"),\n                        ]\n                      ),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\"div\", { staticClass: \"chakra-results\" }, [\n                    _c(\n                      \"div\",\n                      { staticClass: \"chakra-grid\" },\n                      _vm._l(_vm.chakraData, function (chakra, index) {\n                        return _c(\n                          \"div\",\n                          { key: index, staticClass: \"chakra-card\" },\n                          [\n                            _c(\"div\", { staticClass: \"chakra-header\" }, [\n                              _c(\"div\", {\n                                staticClass: \"chakra-dot\",\n                                style: { backgroundColor: chakra.color },\n                              }),\n                              _vm._v(\" \"),\n                              _c(\"div\", { staticClass: \"chakra-info\" }, [\n                                _c(\"div\", { staticClass: \"chakra-name\" }, [\n                                  _vm._v(_vm._s(chakra.name)),\n                                ]),\n                                _vm._v(\" \"),\n                                _c(\"div\", { staticClass: \"chakra-en-name\" }, [\n                                  _vm._v(_vm._s(chakra.enName)),\n                                ]),\n                              ]),\n                            ]),\n                            _vm._v(\" \"),\n                            _c(\"div\", { staticClass: \"chakra-value\" }, [\n                              _vm._v(_vm._s(chakra.value || 0)),\n                            ]),\n                            _vm._v(\" \"),\n                            _c(\n                              \"div\",\n                              { staticClass: \"chakra-progress\" },\n                              [\n                                _c(\"el-progress\", {\n                                  attrs: {\n                                    percentage: _vm.getProgressPercentage(\n                                      chakra.value\n                                    ),\n                                    color: chakra.color,\n                                    \"show-text\": false,\n                                  },\n                                }),\n                              ],\n                              1\n                            ),\n                          ]\n                        )\n                      }),\n                      0\n                    ),\n                  ]),\n                ]\n              )\n            : _vm._e(),\n          _vm._v(\" \"),\n          _vm.questionOptions.length > 0\n            ? _c(\"el-card\", { staticClass: \"box-card\" }, [\n                _c(\n                  \"div\",\n                  {\n                    staticClass: \"clearfix\",\n                    attrs: { slot: \"header\" },\n                    slot: \"header\",\n                  },\n                  [_c(\"span\", [_vm._v(\"答题详情\")])]\n                ),\n                _vm._v(\" \"),\n                _c(\n                  \"div\",\n                  { staticClass: \"question-details\" },\n                  _vm._l(_vm.questionOptions, function (item, index) {\n                    return _c(\n                      \"div\",\n                      { key: index, staticClass: \"question-item\" },\n                      [\n                        _c(\"div\", { staticClass: \"question-title\" }, [\n                          _c(\"span\", { staticClass: \"question-number\" }, [\n                            _vm._v(_vm._s(index + 1) + \".\"),\n                          ]),\n                          _vm._v(\" \"),\n                          _c(\"span\", [\n                            _vm._v(\n                              _vm._s(item.questionName || \"题目\" + (index + 1))\n                            ),\n                          ]),\n                        ]),\n                        _vm._v(\" \"),\n                        _c(\"div\", { staticClass: \"question-answer\" }, [\n                          _c(\"span\", { staticClass: \"answer-label\" }, [\n                            _vm._v(\"选择答案：\"),\n                          ]),\n                          _vm._v(\" \"),\n                          _c(\"span\", { staticClass: \"answer-value\" }, [\n                            _vm._v(_vm._s(item.optionName || \"-\")),\n                          ]),\n                        ]),\n                      ]\n                    )\n                  }),\n                  0\n                ),\n              ])\n            : _vm._e(),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"div\",\n        {\n          staticClass: \"dialog-footer\",\n          attrs: { slot: \"footer\" },\n          slot: \"footer\",\n        },\n        [\n          _c(\n            \"el-button\",\n            {\n              attrs: { type: \"primary\" },\n              on: {\n                click: function ($event) {\n                  return _vm.exportDetail()\n                },\n              },\n            },\n            [_vm._v(\"导出详情\")]\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-button\",\n            {\n              on: {\n                click: function ($event) {\n                  _vm.visible = false\n                },\n              },\n            },\n            [_vm._v(\"关闭\")]\n          ),\n        ],\n        1\n      ),\n    ]\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}